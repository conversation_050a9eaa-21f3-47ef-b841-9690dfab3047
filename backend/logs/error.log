[2025-05-05 00:30:52 +0800] [73553] [INFO] Starting gunicorn 20.1.0
[2025-05-05 00:30:52 +0800] [73553] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:30:52 +0800] [73553] [ERROR] Retrying in 1 second.
[2025-05-05 00:30:53 +0800] [73553] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:30:53 +0800] [73553] [ERROR] Retrying in 1 second.
[2025-05-05 00:30:54 +0800] [73553] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:30:54 +0800] [73553] [ERROR] Retrying in 1 second.
[2025-05-05 00:30:55 +0800] [73553] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:30:55 +0800] [73553] [ERROR] Retrying in 1 second.
[2025-05-05 00:30:56 +0800] [73553] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:30:56 +0800] [73553] [ERROR] Retrying in 1 second.
[2025-05-05 00:30:57 +0800] [73553] [ERROR] Can't connect to ('0.0.0.0', 5001)
[2025-05-05 00:32:27 +0800] [74295] [INFO] Starting gunicorn 20.1.0
[2025-05-05 00:32:27 +0800] [74295] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:32:27 +0800] [74295] [ERROR] Retrying in 1 second.
[2025-05-05 00:32:28 +0800] [74295] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:32:28 +0800] [74295] [ERROR] Retrying in 1 second.
[2025-05-05 00:32:29 +0800] [74295] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:32:29 +0800] [74295] [ERROR] Retrying in 1 second.
[2025-05-05 00:32:30 +0800] [74295] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:32:30 +0800] [74295] [ERROR] Retrying in 1 second.
[2025-05-05 00:32:31 +0800] [74295] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:32:31 +0800] [74295] [ERROR] Retrying in 1 second.
[2025-05-05 00:32:32 +0800] [74295] [ERROR] Can't connect to ('0.0.0.0', 5001)
[2025-05-05 00:34:52 +0800] [77402] [DEBUG] Current configuration:
  config: gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:5001']
  backlog: 2048
  workers: 29
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /dev/shm
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: ./logs/error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: run:app
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x102370430>
  on_reload: <function OnReload.on_reload at 0x102328670>
  when_ready: <function WhenReady.when_ready at 0x102328790>
  pre_fork: <function Prefork.pre_fork at 0x1023288b0>
  post_fork: <function post_fork at 0x10232a440>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x102328af0>
  worker_int: <function WorkerInt.worker_int at 0x102328c10>
  worker_abort: <function WorkerAbort.worker_abort at 0x102328d30>
  pre_exec: <function PreExec.pre_exec at 0x102328e50>
  pre_request: <function PreRequest.pre_request at 0x102328f70>
  post_request: <function PostRequest.post_request at 0x102329000>
  child_exit: <function ChildExit.child_exit at 0x102329120>
  worker_exit: <function WorkerExit.worker_exit at 0x102329240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x102329360>
  on_exit: <function on_exit at 0x10232a320>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
[2025-05-05 00:34:52 +0800] [77402] [INFO] Starting gunicorn 20.1.0
Gunicorn服务器正在启动...
[2025-05-05 00:34:52 +0800] [77402] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:34:52 +0800] [77402] [DEBUG] connection to ('0.0.0.0', 5001) failed: [Errno 48] Address already in use
[2025-05-05 00:34:52 +0800] [77402] [ERROR] Retrying in 1 second.
[2025-05-05 00:34:53 +0800] [77402] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:34:53 +0800] [77402] [DEBUG] connection to ('0.0.0.0', 5001) failed: [Errno 48] Address already in use
[2025-05-05 00:34:53 +0800] [77402] [ERROR] Retrying in 1 second.
[2025-05-05 00:34:54 +0800] [77402] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:34:54 +0800] [77402] [DEBUG] connection to ('0.0.0.0', 5001) failed: [Errno 48] Address already in use
[2025-05-05 00:34:54 +0800] [77402] [ERROR] Retrying in 1 second.
[2025-05-05 00:34:55 +0800] [77402] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:34:55 +0800] [77402] [DEBUG] connection to ('0.0.0.0', 5001) failed: [Errno 48] Address already in use
[2025-05-05 00:34:55 +0800] [77402] [ERROR] Retrying in 1 second.
[2025-05-05 00:34:56 +0800] [77402] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:34:56 +0800] [77402] [DEBUG] connection to ('0.0.0.0', 5001) failed: [Errno 48] Address already in use
[2025-05-05 00:34:56 +0800] [77402] [ERROR] Retrying in 1 second.
[2025-05-05 00:34:57 +0800] [77402] [ERROR] Can't connect to ('0.0.0.0', 5001)
[2025-05-05 00:37:19 +0800] [78466] [DEBUG] Current configuration:
  config: gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:5001']
  backlog: 2048
  workers: 29
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /dev/shm
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: ./logs/error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: run:appcd
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x10420e320>
  on_reload: <function OnReload.on_reload at 0x10420c670>
  when_ready: <function WhenReady.when_ready at 0x10420c790>
  pre_fork: <function Prefork.pre_fork at 0x10420c8b0>
  post_fork: <function post_fork at 0x10420e560>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x10420caf0>
  worker_int: <function WorkerInt.worker_int at 0x10420cc10>
  worker_abort: <function WorkerAbort.worker_abort at 0x10420cd30>
  pre_exec: <function PreExec.pre_exec at 0x10420ce50>
  pre_request: <function PreRequest.pre_request at 0x10420cf70>
  post_request: <function PostRequest.post_request at 0x10420d000>
  child_exit: <function ChildExit.child_exit at 0x10420d120>
  worker_exit: <function WorkerExit.worker_exit at 0x10420d240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x10420d360>
  on_exit: <function on_exit at 0x10420e440>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/util.py", line 402, in import_app
    app = getattr(mod, name)
AttributeError: module 'run' has no attribute 'appcd'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/bin/gunicorn", line 8, in <module>
    sys.exit(run())
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/wsgiapp.py", line 67, in run
    WSGIApplication("%(prog)s [OPTIONS] [APP_MODULE]").run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 231, in run
    super().run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 72, in run
    Arbiter(self).run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 58, in __init__
    self.setup(app)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 118, in setup
    self.app.wsgi()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 67, in wsgi
    self.callable = self.load()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/wsgiapp.py", line 58, in load
    return self.load_wsgiapp()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/wsgiapp.py", line 48, in load_wsgiapp
    return util.import_app(self.app_uri)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/util.py", line 406, in import_app
    raise AppImportError("Failed to find attribute %r in %r." % (name, module))
gunicorn.errors.AppImportError: Failed to find attribute 'appcd' in 'run'.
[2025-05-05 00:37:34 +0800] [78748] [DEBUG] Current configuration:
  config: gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:5001']
  backlog: 2048
  workers: 29
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /dev/shm
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: ./logs/error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: run:app
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x10177a320>
  on_reload: <function OnReload.on_reload at 0x101778670>
  when_ready: <function WhenReady.when_ready at 0x101778790>
  pre_fork: <function Prefork.pre_fork at 0x1017788b0>
  post_fork: <function post_fork at 0x10177a560>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x101778af0>
  worker_int: <function WorkerInt.worker_int at 0x101778c10>
  worker_abort: <function WorkerAbort.worker_abort at 0x101778d30>
  pre_exec: <function PreExec.pre_exec at 0x101778e50>
  pre_request: <function PreRequest.pre_request at 0x101778f70>
  post_request: <function PostRequest.post_request at 0x101779000>
  child_exit: <function ChildExit.child_exit at 0x101779120>
  worker_exit: <function WorkerExit.worker_exit at 0x101779240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x101779360>
  on_exit: <function on_exit at 0x10177a440>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
[2025-05-05 00:37:34 +0800] [78748] [INFO] Starting gunicorn 20.1.0
Gunicorn服务器正在启动...
[2025-05-05 00:37:34 +0800] [78748] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:37:34 +0800] [78748] [DEBUG] connection to ('0.0.0.0', 5001) failed: [Errno 48] Address already in use
[2025-05-05 00:37:34 +0800] [78748] [ERROR] Retrying in 1 second.
[2025-05-05 00:37:35 +0800] [78748] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:37:35 +0800] [78748] [DEBUG] connection to ('0.0.0.0', 5001) failed: [Errno 48] Address already in use
[2025-05-05 00:37:35 +0800] [78748] [ERROR] Retrying in 1 second.
[2025-05-05 00:37:36 +0800] [78748] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:37:36 +0800] [78748] [DEBUG] connection to ('0.0.0.0', 5001) failed: [Errno 48] Address already in use
[2025-05-05 00:37:36 +0800] [78748] [ERROR] Retrying in 1 second.
[2025-05-05 00:37:37 +0800] [78748] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:37:37 +0800] [78748] [DEBUG] connection to ('0.0.0.0', 5001) failed: [Errno 48] Address already in use
[2025-05-05 00:37:37 +0800] [78748] [ERROR] Retrying in 1 second.
[2025-05-05 00:37:38 +0800] [78748] [ERROR] Connection in use: ('0.0.0.0', 5001)
[2025-05-05 00:37:38 +0800] [78748] [DEBUG] connection to ('0.0.0.0', 5001) failed: [Errno 48] Address already in use
[2025-05-05 00:37:38 +0800] [78748] [ERROR] Retrying in 1 second.
[2025-05-05 00:37:39 +0800] [78748] [ERROR] Can't connect to ('0.0.0.0', 5001)
[2025-05-05 00:41:57 +0800] [81591] [DEBUG] Current configuration:
  config: gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:5001']
  backlog: 2048
  workers: 29
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /dev/shm
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: ./logs/error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: run:app
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x101f26320>
  on_reload: <function OnReload.on_reload at 0x101f24670>
  when_ready: <function WhenReady.when_ready at 0x101f24790>
  pre_fork: <function Prefork.pre_fork at 0x101f248b0>
  post_fork: <function post_fork at 0x101f26560>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x101f24af0>
  worker_int: <function WorkerInt.worker_int at 0x101f24c10>
  worker_abort: <function WorkerAbort.worker_abort at 0x101f24d30>
  pre_exec: <function PreExec.pre_exec at 0x101f24e50>
  pre_request: <function PreRequest.pre_request at 0x101f24f70>
  post_request: <function PostRequest.post_request at 0x101f25000>
  child_exit: <function ChildExit.child_exit at 0x101f25120>
  worker_exit: <function WorkerExit.worker_exit at 0x101f25240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x101f25360>
  on_exit: <function on_exit at 0x101f26440>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
[2025-05-05 00:41:57 +0800] [81591] [INFO] Starting gunicorn 20.1.0
Gunicorn服务器正在启动...
[2025-05-05 00:41:57 +0800] [81591] [DEBUG] Arbiter booted
[2025-05-05 00:41:57 +0800] [81591] [INFO] Listening at: http://0.0.0.0:5001 (81591)
[2025-05-05 00:41:57 +0800] [81591] [INFO] Using worker: gevent
[2025-05-05 00:41:57 +0800] [81591] [INFO] Unhandled exception in main loop
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 202, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 22, in __init__
    raise RuntimeError("%s doesn't exist. Can't create workertmp." % fdir)
RuntimeError: /dev/shm doesn't exist. Can't create workertmp.
[2025-05-05 00:42:33 +0800] [81933] [DEBUG] Current configuration:
  config: ./gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:5001']
  backlog: 2048
  workers: 4
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /dev/shm
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: ./logs/error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: run:app
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x105d9a320>
  on_reload: <function OnReload.on_reload at 0x105d98670>
  when_ready: <function WhenReady.when_ready at 0x105d98790>
  pre_fork: <function Prefork.pre_fork at 0x105d988b0>
  post_fork: <function post_fork at 0x105d9a560>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x105d98af0>
  worker_int: <function WorkerInt.worker_int at 0x105d98c10>
  worker_abort: <function WorkerAbort.worker_abort at 0x105d98d30>
  pre_exec: <function PreExec.pre_exec at 0x105d98e50>
  pre_request: <function PreRequest.pre_request at 0x105d98f70>
  post_request: <function PostRequest.post_request at 0x105d99000>
  child_exit: <function ChildExit.child_exit at 0x105d99120>
  worker_exit: <function WorkerExit.worker_exit at 0x105d99240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x105d99360>
  on_exit: <function on_exit at 0x105d9a440>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
[2025-05-05 00:42:33 +0800] [81933] [INFO] Starting gunicorn 20.1.0
Gunicorn服务器正在启动...
[2025-05-05 00:42:33 +0800] [81933] [DEBUG] Arbiter booted
[2025-05-05 00:42:33 +0800] [81933] [INFO] Listening at: http://0.0.0.0:5001 (81933)
[2025-05-05 00:42:33 +0800] [81933] [INFO] Using worker: gevent
[2025-05-05 00:42:33 +0800] [81933] [INFO] Unhandled exception in main loop
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 202, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 22, in __init__
    raise RuntimeError("%s doesn't exist. Can't create workertmp." % fdir)
RuntimeError: /dev/shm doesn't exist. Can't create workertmp.
[2025-05-05 00:44:19 +0800] [83371] [DEBUG] Current configuration:
  config: ./gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:5001']
  backlog: 2048
  workers: 29
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /dev/shm
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: ./logs/error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: run:app
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x105e6a320>
  on_reload: <function OnReload.on_reload at 0x105e68670>
  when_ready: <function WhenReady.when_ready at 0x105e68790>
  pre_fork: <function Prefork.pre_fork at 0x105e688b0>
  post_fork: <function post_fork at 0x105e6a560>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x105e68af0>
  worker_int: <function WorkerInt.worker_int at 0x105e68c10>
  worker_abort: <function WorkerAbort.worker_abort at 0x105e68d30>
  pre_exec: <function PreExec.pre_exec at 0x105e68e50>
  pre_request: <function PreRequest.pre_request at 0x105e68f70>
  post_request: <function PostRequest.post_request at 0x105e69000>
  child_exit: <function ChildExit.child_exit at 0x105e69120>
  worker_exit: <function WorkerExit.worker_exit at 0x105e69240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x105e69360>
  on_exit: <function on_exit at 0x105e6a440>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
[2025-05-05 00:44:20 +0800] [83371] [INFO] Starting gunicorn 20.1.0
Gunicorn服务器正在启动...
[2025-05-05 00:44:20 +0800] [83371] [DEBUG] Arbiter booted
[2025-05-05 00:44:20 +0800] [83371] [INFO] Listening at: http://0.0.0.0:5001 (83371)
[2025-05-05 00:44:20 +0800] [83371] [INFO] Using worker: gevent
[2025-05-05 00:44:20 +0800] [83371] [INFO] Unhandled exception in main loop
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 202, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 22, in __init__
    raise RuntimeError("%s doesn't exist. Can't create workertmp." % fdir)
RuntimeError: /dev/shm doesn't exist. Can't create workertmp.
[2025-05-05 01:10:13 +0800] [6308] [DEBUG] Current configuration:
  config: ./gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:5001']
  backlog: 2048
  workers: 29
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /dev/shm
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: ./logs/error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: run:app
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x104542320>
  on_reload: <function OnReload.on_reload at 0x104540670>
  when_ready: <function WhenReady.when_ready at 0x104540790>
  pre_fork: <function Prefork.pre_fork at 0x1045408b0>
  post_fork: <function post_fork at 0x104542560>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x104540af0>
  worker_int: <function WorkerInt.worker_int at 0x104540c10>
  worker_abort: <function WorkerAbort.worker_abort at 0x104540d30>
  pre_exec: <function PreExec.pre_exec at 0x104540e50>
  pre_request: <function PreRequest.pre_request at 0x104540f70>
  post_request: <function PostRequest.post_request at 0x104541000>
  child_exit: <function ChildExit.child_exit at 0x104541120>
  worker_exit: <function WorkerExit.worker_exit at 0x104541240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x104541360>
  on_exit: <function on_exit at 0x104542440>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
[2025-05-05 01:10:13 +0800] [6308] [INFO] Starting gunicorn 20.1.0
Gunicorn服务器正在启动...
[2025-05-05 01:10:13 +0800] [6308] [DEBUG] Arbiter booted
[2025-05-05 01:10:13 +0800] [6308] [INFO] Listening at: http://0.0.0.0:5001 (6308)
[2025-05-05 01:10:13 +0800] [6308] [INFO] Using worker: gevent
[2025-05-05 01:10:13 +0800] [6308] [INFO] Unhandled exception in main loop
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 202, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 22, in __init__
    raise RuntimeError("%s doesn't exist. Can't create workertmp." % fdir)
RuntimeError: /dev/shm doesn't exist. Can't create workertmp.
[2025-05-05 01:12:06 +0800] [7532] [DEBUG] Current configuration:
  config: ./gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:5001']
  backlog: 2048
  workers: 29
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /dev/shm
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: ./logs/error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: run:app
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x10574a320>
  on_reload: <function OnReload.on_reload at 0x105748670>
  when_ready: <function WhenReady.when_ready at 0x105748790>
  pre_fork: <function Prefork.pre_fork at 0x1057488b0>
  post_fork: <function post_fork at 0x10574a560>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x105748af0>
  worker_int: <function WorkerInt.worker_int at 0x105748c10>
  worker_abort: <function WorkerAbort.worker_abort at 0x105748d30>
  pre_exec: <function PreExec.pre_exec at 0x105748e50>
  pre_request: <function PreRequest.pre_request at 0x105748f70>
  post_request: <function PostRequest.post_request at 0x105749000>
  child_exit: <function ChildExit.child_exit at 0x105749120>
  worker_exit: <function WorkerExit.worker_exit at 0x105749240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x105749360>
  on_exit: <function on_exit at 0x10574a440>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
[2025-05-05 01:12:06 +0800] [7532] [INFO] Starting gunicorn 20.1.0
Gunicorn服务器正在启动...
[2025-05-05 01:12:06 +0800] [7532] [DEBUG] Arbiter booted
[2025-05-05 01:12:06 +0800] [7532] [INFO] Listening at: http://0.0.0.0:5001 (7532)
[2025-05-05 01:12:06 +0800] [7532] [INFO] Using worker: gevent
[2025-05-05 01:12:06 +0800] [7532] [INFO] Unhandled exception in main loop
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 202, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 22, in __init__
    raise RuntimeError("%s doesn't exist. Can't create workertmp." % fdir)
RuntimeError: /dev/shm doesn't exist. Can't create workertmp.
[2025-05-05 01:12:20 +0800] [8126] [DEBUG] Current configuration:
  config: ./gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:5001']
  backlog: 2048
  workers: 29
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /dev/shm
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: ./logs/error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: run:app
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x102590430>
  on_reload: <function OnReload.on_reload at 0x102548670>
  when_ready: <function WhenReady.when_ready at 0x102548790>
  pre_fork: <function Prefork.pre_fork at 0x1025488b0>
  post_fork: <function post_fork at 0x10254a440>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x102548af0>
  worker_int: <function WorkerInt.worker_int at 0x102548c10>
  worker_abort: <function WorkerAbort.worker_abort at 0x102548d30>
  pre_exec: <function PreExec.pre_exec at 0x102548e50>
  pre_request: <function PreRequest.pre_request at 0x102548f70>
  post_request: <function PostRequest.post_request at 0x102549000>
  child_exit: <function ChildExit.child_exit at 0x102549120>
  worker_exit: <function WorkerExit.worker_exit at 0x102549240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x102549360>
  on_exit: <function on_exit at 0x10254a320>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
[2025-05-05 01:12:20 +0800] [8126] [INFO] Starting gunicorn 20.1.0
Gunicorn服务器正在启动...
[2025-05-05 01:12:20 +0800] [8126] [DEBUG] Arbiter booted
[2025-05-05 01:12:20 +0800] [8126] [INFO] Listening at: http://0.0.0.0:5001 (8126)
[2025-05-05 01:12:20 +0800] [8126] [INFO] Using worker: gevent
[2025-05-05 01:12:20 +0800] [8126] [INFO] Unhandled exception in main loop
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 202, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 22, in __init__
    raise RuntimeError("%s doesn't exist. Can't create workertmp." % fdir)
RuntimeError: /dev/shm doesn't exist. Can't create workertmp.
[2025-05-05 01:12:39 +0800] [10843] [DEBUG] Current configuration:
  config: ./gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:8000']
  backlog: 2048
  workers: 29
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /dev/shm
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: ./logs/error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: test_app:app
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x10702a320>
  on_reload: <function OnReload.on_reload at 0x107028670>
  when_ready: <function WhenReady.when_ready at 0x107028790>
  pre_fork: <function Prefork.pre_fork at 0x1070288b0>
  post_fork: <function post_fork at 0x10702a560>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x107028af0>
  worker_int: <function WorkerInt.worker_int at 0x107028c10>
  worker_abort: <function WorkerAbort.worker_abort at 0x107028d30>
  pre_exec: <function PreExec.pre_exec at 0x107028e50>
  pre_request: <function PreRequest.pre_request at 0x107028f70>
  post_request: <function PostRequest.post_request at 0x107029000>
  child_exit: <function ChildExit.child_exit at 0x107029120>
  worker_exit: <function WorkerExit.worker_exit at 0x107029240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x107029360>
  on_exit: <function on_exit at 0x10702a440>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
[2025-05-05 01:12:39 +0800] [10843] [INFO] Starting gunicorn 20.1.0
Gunicorn服务器正在启动...
[2025-05-05 01:12:39 +0800] [10843] [ERROR] Connection in use: ('0.0.0.0', 8000)
[2025-05-05 01:12:39 +0800] [10843] [DEBUG] connection to ('0.0.0.0', 8000) failed: [Errno 48] Address already in use
[2025-05-05 01:12:39 +0800] [10843] [ERROR] Retrying in 1 second.
[2025-05-05 01:12:40 +0800] [10843] [ERROR] Connection in use: ('0.0.0.0', 8000)
[2025-05-05 01:12:40 +0800] [10843] [DEBUG] connection to ('0.0.0.0', 8000) failed: [Errno 48] Address already in use
[2025-05-05 01:12:40 +0800] [10843] [ERROR] Retrying in 1 second.
[2025-05-05 01:12:41 +0800] [10843] [ERROR] Connection in use: ('0.0.0.0', 8000)
[2025-05-05 01:12:41 +0800] [10843] [DEBUG] connection to ('0.0.0.0', 8000) failed: [Errno 48] Address already in use
[2025-05-05 01:12:41 +0800] [10843] [ERROR] Retrying in 1 second.
[2025-05-05 01:12:42 +0800] [10843] [ERROR] Connection in use: ('0.0.0.0', 8000)
[2025-05-05 01:12:42 +0800] [10843] [DEBUG] connection to ('0.0.0.0', 8000) failed: [Errno 48] Address already in use
[2025-05-05 01:12:42 +0800] [10843] [ERROR] Retrying in 1 second.
[2025-05-05 01:12:43 +0800] [10843] [ERROR] Connection in use: ('0.0.0.0', 8000)
[2025-05-05 01:12:43 +0800] [10843] [DEBUG] connection to ('0.0.0.0', 8000) failed: [Errno 48] Address already in use
[2025-05-05 01:12:43 +0800] [10843] [ERROR] Retrying in 1 second.
[2025-05-05 01:12:44 +0800] [10843] [ERROR] Can't connect to ('0.0.0.0', 8000)
[2025-05-05 01:14:56 +0800] [13451] [DEBUG] Current configuration:
  config: ./gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:5001']
  backlog: 2048
  workers: 29
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /dev/shm
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: ./logs/error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: run:app
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x103e7c430>
  on_reload: <function OnReload.on_reload at 0x103e34670>
  when_ready: <function WhenReady.when_ready at 0x103e34790>
  pre_fork: <function Prefork.pre_fork at 0x103e348b0>
  post_fork: <function post_fork at 0x103e36440>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x103e34af0>
  worker_int: <function WorkerInt.worker_int at 0x103e34c10>
  worker_abort: <function WorkerAbort.worker_abort at 0x103e34d30>
  pre_exec: <function PreExec.pre_exec at 0x103e34e50>
  pre_request: <function PreRequest.pre_request at 0x103e34f70>
  post_request: <function PostRequest.post_request at 0x103e35000>
  child_exit: <function ChildExit.child_exit at 0x103e35120>
  worker_exit: <function WorkerExit.worker_exit at 0x103e35240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x103e35360>
  on_exit: <function on_exit at 0x103e36320>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
[2025-05-05 01:14:56 +0800] [13451] [INFO] Starting gunicorn 20.1.0
Gunicorn服务器正在启动...
[2025-05-05 01:14:56 +0800] [13451] [DEBUG] Arbiter booted
[2025-05-05 01:14:56 +0800] [13451] [INFO] Listening at: http://0.0.0.0:5001 (13451)
[2025-05-05 01:14:56 +0800] [13451] [INFO] Using worker: gevent
[2025-05-05 01:14:56 +0800] [13451] [INFO] Unhandled exception in main loop
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 202, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 22, in __init__
    raise RuntimeError("%s doesn't exist. Can't create workertmp." % fdir)
RuntimeError: /dev/shm doesn't exist. Can't create workertmp.
[2025-05-05 01:16:03 +0800] [15341] [DEBUG] Current configuration:
  config: gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:5001']
  backlog: 2048
  workers: 29
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /tmp
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: ./logs/error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: run:app
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x103f9c430>
  on_reload: <function OnReload.on_reload at 0x103f54670>
  when_ready: <function WhenReady.when_ready at 0x103f54790>
  pre_fork: <function Prefork.pre_fork at 0x103f548b0>
  post_fork: <function post_fork at 0x103f56440>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x103f54af0>
  worker_int: <function WorkerInt.worker_int at 0x103f54c10>
  worker_abort: <function WorkerAbort.worker_abort at 0x103f54d30>
  pre_exec: <function PreExec.pre_exec at 0x103f54e50>
  pre_request: <function PreRequest.pre_request at 0x103f54f70>
  post_request: <function PostRequest.post_request at 0x103f55000>
  child_exit: <function ChildExit.child_exit at 0x103f55120>
  worker_exit: <function WorkerExit.worker_exit at 0x103f55240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x103f55360>
  on_exit: <function on_exit at 0x103f56320>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
[2025-05-05 01:16:03 +0800] [15341] [INFO] Starting gunicorn 20.1.0
Gunicorn服务器正在启动...
[2025-05-05 01:16:03 +0800] [15341] [DEBUG] Arbiter booted
[2025-05-05 01:16:03 +0800] [15341] [INFO] Listening at: http://0.0.0.0:5001 (15341)
[2025-05-05 01:16:03 +0800] [15341] [INFO] Using worker: gevent
[2025-05-05 01:16:03 +0800] [15352] [INFO] Booting worker with pid: 15352
[2025-05-05 01:16:03 +0800] [15352] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15353] [INFO] Booting worker with pid: 15353
[2025-05-05 01:16:03 +0800] [15353] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15354] [INFO] Booting worker with pid: 15354
[2025-05-05 01:16:03 +0800] [15354] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15355] [INFO] Booting worker with pid: 15355
[2025-05-05 01:16:03 +0800] [15355] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15356] [INFO] Booting worker with pid: 15356
[2025-05-05 01:16:03 +0800] [15356] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15357] [INFO] Booting worker with pid: 15357
[2025-05-05 01:16:03 +0800] [15357] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15358] [INFO] Booting worker with pid: 15358
[2025-05-05 01:16:03 +0800] [15358] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15359] [INFO] Booting worker with pid: 15359
[2025-05-05 01:16:03 +0800] [15359] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15360] [INFO] Booting worker with pid: 15360
[2025-05-05 01:16:03 +0800] [15360] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15361] [INFO] Booting worker with pid: 15361
[2025-05-05 01:16:03 +0800] [15361] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15362] [INFO] Booting worker with pid: 15362
[2025-05-05 01:16:03 +0800] [15362] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15363] [INFO] Booting worker with pid: 15363
[2025-05-05 01:16:03 +0800] [15363] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15364] [INFO] Booting worker with pid: 15364
[2025-05-05 01:16:03 +0800] [15364] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15366] [INFO] Booting worker with pid: 15366
[2025-05-05 01:16:03 +0800] [15366] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15368] [INFO] Booting worker with pid: 15368
[2025-05-05 01:16:03 +0800] [15368] [INFO] 工作进程已启动
[2025-05-05 01:16:03 +0800] [15369] [INFO] Booting worker with pid: 15369
[2025-05-05 01:16:03 +0800] [15369] [INFO] 工作进程已启动
[2025-05-05 01:16:04 +0800] [15370] [INFO] Booting worker with pid: 15370
[2025-05-05 01:16:04 +0800] [15370] [INFO] 工作进程已启动
[2025-05-05 01:16:04 +0800] [15371] [INFO] Booting worker with pid: 15371
[2025-05-05 01:16:04 +0800] [15371] [INFO] 工作进程已启动
[2025-05-05 01:16:04 +0800] [15377] [INFO] Booting worker with pid: 15377
[2025-05-05 01:16:04 +0800] [15377] [INFO] 工作进程已启动
[2025-05-05 01:16:04 +0800] [15378] [INFO] Booting worker with pid: 15378
[2025-05-05 01:16:04 +0800] [15378] [INFO] 工作进程已启动
[2025-05-05 01:16:04 +0800] [15379] [INFO] Booting worker with pid: 15379
[2025-05-05 01:16:04 +0800] [15379] [INFO] 工作进程已启动
[2025-05-05 01:16:04 +0800] [15380] [INFO] Booting worker with pid: 15380
[2025-05-05 01:16:04 +0800] [15380] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:16:04 +0800] [15381] [INFO] Booting worker with pid: 15381
[2025-05-05 01:16:04 +0800] [15381] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:16:04 +0800] [15382] [INFO] Booting worker with pid: 15382
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:16:04 +0800] [15382] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:16:04 +0800] [15383] [INFO] Booting worker with pid: 15383
[2025-05-05 01:16:04 +0800] [15383] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:16:04 +0800] [15384] [INFO] Booting worker with pid: 15384
[2025-05-05 01:16:04 +0800] [15384] [INFO] 工作进程已启动
[2025-05-05 01:16:04 +0800] [15385] [INFO] Booting worker with pid: 15385
[2025-05-05 01:16:04 +0800] [15385] [INFO] 工作进程已启动
[2025-05-05 01:16:04 +0800] [15386] [INFO] Booting worker with pid: 15386
[2025-05-05 01:16:04 +0800] [15386] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:16:04 +0800] [15387] [INFO] Booting worker with pid: 15387
[2025-05-05 01:16:04 +0800] [15387] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:16:04 +0800] [15341] [DEBUG] 29 workers
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15384 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15383 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15382 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15381 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15380 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15379 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15378 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15377 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15370 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15369 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15368 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15386 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15385 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15387 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15356 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15364 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15360 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15362 was terminated due to signal 1
--- Logging error ---
--- Logging error ---
--- Logging error ---
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1100, in emit
    msg = self.format(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 943, in format
    return fmt.format(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 680, in format
    record.asctime = self.formatTime(record, self.datefmt)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 617, in formatTime
    s = time.strftime(datefmt, ct)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1021, in handleError
    sys.stderr.write('--- Logging error ---\n')
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1021, in handleError
    sys.stderr.write('--- Logging error ---\n')
RuntimeError: reentrant call inside <_io.BufferedWriter name='<stderr>'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>
Call stack:
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/bin/gunicorn", line 8, in <module>
    sys.exit(run())
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/wsgiapp.py", line 67, in run
    WSGIApplication("%(prog)s [OPTIONS] [APP_MODULE]").run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 231, in run
    super().run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 72, in run
    Arbiter(self).run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 211, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 23, in __init__
    fd, name = tempfile.mkstemp(prefix="wgunicorn-", dir=fdir)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 357, in mkstemp
    return _mkstemp_inner(dir, prefix, suffix, flags, output_type)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 253, in _mkstemp_inner
    file = _os.path.join(dir, pre + name + suf)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/posixpath.py", line 71, in join
    def join(a, *p):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1622, in _log
    record = self.makeRecord(self.name, level, fn, lno, msg, args,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1591, in makeRecord
    rv = _logRecordFactory(name, level, fn, lno, msg, args, exc_info, func,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 340, in __init__
    mp = sys.modules.get('multiprocessing')
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1019, in handleError
    t, v, tb = sys.exc_info()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1633, in handle
    if (not self.disabled) and self.filter(record):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1022, in handleError
    traceback.print_exception(t, v, tb, None, sys.stderr)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 119, in print_exception
    te = TracebackException(type(value), value, tb, limit=limit, compact=True)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 502, in __init__
    self.stack = StackSummary.extract(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 369, in extract
    fnames.add(filename)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
Message: 'Worker with pid %s was terminated due to signal %s'
Arguments: (15363, 1)
--- Logging error ---
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1100, in emit
    msg = self.format(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 943, in format
    return fmt.format(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 680, in format
    record.asctime = self.formatTime(record, self.datefmt)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 617, in formatTime
    s = time.strftime(datefmt, ct)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1021, in handleError
    sys.stderr.write('--- Logging error ---\n')
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1021, in handleError
    sys.stderr.write('--- Logging error ---\n')
RuntimeError: reentrant call inside <_io.BufferedWriter name='<stderr>'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>
Call stack:
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/bin/gunicorn", line 8, in <module>
    sys.exit(run())
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/wsgiapp.py", line 67, in run
    WSGIApplication("%(prog)s [OPTIONS] [APP_MODULE]").run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 231, in run
    super().run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 72, in run
    Arbiter(self).run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 211, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 23, in __init__
    fd, name = tempfile.mkstemp(prefix="wgunicorn-", dir=fdir)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 357, in mkstemp
    return _mkstemp_inner(dir, prefix, suffix, flags, output_type)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 253, in _mkstemp_inner
    file = _os.path.join(dir, pre + name + suf)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/posixpath.py", line 71, in join
    def join(a, *p):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1622, in _log
    record = self.makeRecord(self.name, level, fn, lno, msg, args,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1591, in makeRecord
    rv = _logRecordFactory(name, level, fn, lno, msg, args, exc_info, func,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 340, in __init__
    mp = sys.modules.get('multiprocessing')
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1019, in handleError
    t, v, tb = sys.exc_info()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1633, in handle
    if (not self.disabled) and self.filter(record):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1022, in handleError
    traceback.print_exception(t, v, tb, None, sys.stderr)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 119, in print_exception
    te = TracebackException(type(value), value, tb, limit=limit, compact=True)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 502, in __init__
    self.stack = StackSummary.extract(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 369, in extract
    fnames.add(filename)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
Message: 'Worker with pid %s was terminated due to signal %s'
Arguments: (15355, 1)
--- Logging error ---
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1100, in emit
    msg = self.format(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 943, in format
    return fmt.format(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 680, in format
    record.asctime = self.formatTime(record, self.datefmt)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 617, in formatTime
    s = time.strftime(datefmt, ct)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1021, in handleError
    sys.stderr.write('--- Logging error ---\n')
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1021, in handleError
    sys.stderr.write('--- Logging error ---\n')
RuntimeError: reentrant call inside <_io.BufferedWriter name='<stderr>'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>
Call stack:
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/bin/gunicorn", line 8, in <module>
    sys.exit(run())
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/wsgiapp.py", line 67, in run
    WSGIApplication("%(prog)s [OPTIONS] [APP_MODULE]").run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 231, in run
    super().run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 72, in run
    Arbiter(self).run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 211, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 23, in __init__
    fd, name = tempfile.mkstemp(prefix="wgunicorn-", dir=fdir)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 357, in mkstemp
    return _mkstemp_inner(dir, prefix, suffix, flags, output_type)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 253, in _mkstemp_inner
    file = _os.path.join(dir, pre + name + suf)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/posixpath.py", line 71, in join
    def join(a, *p):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1622, in _log
    record = self.makeRecord(self.name, level, fn, lno, msg, args,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1591, in makeRecord
    rv = _logRecordFactory(name, level, fn, lno, msg, args, exc_info, func,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 340, in __init__
    mp = sys.modules.get('multiprocessing')
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1019, in handleError
    t, v, tb = sys.exc_info()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1633, in handle
    if (not self.disabled) and self.filter(record):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1022, in handleError
    traceback.print_exception(t, v, tb, None, sys.stderr)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 119, in print_exception
    te = TracebackException(type(value), value, tb, limit=limit, compact=True)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 502, in __init__
    self.stack = StackSummary.extract(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 369, in extract
    fnames.add(filename)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
Message: 'Worker with pid %s was terminated due to signal %s'
Arguments: (15354, 1)
--- Logging error ---
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1100, in emit
    msg = self.format(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 943, in format
    return fmt.format(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 680, in format
    record.asctime = self.formatTime(record, self.datefmt)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 617, in formatTime
    s = time.strftime(datefmt, ct)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1021, in handleError
    sys.stderr.write('--- Logging error ---\n')
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1021, in handleError
    sys.stderr.write('--- Logging error ---\n')
RuntimeError: reentrant call inside <_io.BufferedWriter name='<stderr>'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>
Call stack:
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/bin/gunicorn", line 8, in <module>
    sys.exit(run())
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/wsgiapp.py", line 67, in run
    WSGIApplication("%(prog)s [OPTIONS] [APP_MODULE]").run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 231, in run
    super().run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 72, in run
    Arbiter(self).run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 211, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 23, in __init__
    fd, name = tempfile.mkstemp(prefix="wgunicorn-", dir=fdir)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 357, in mkstemp
    return _mkstemp_inner(dir, prefix, suffix, flags, output_type)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 253, in _mkstemp_inner
    file = _os.path.join(dir, pre + name + suf)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/posixpath.py", line 71, in join
    def join(a, *p):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1622, in _log
    record = self.makeRecord(self.name, level, fn, lno, msg, args,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1591, in makeRecord
    rv = _logRecordFactory(name, level, fn, lno, msg, args, exc_info, func,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 340, in __init__
    mp = sys.modules.get('multiprocessing')
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1019, in handleError
    t, v, tb = sys.exc_info()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1633, in handle
    if (not self.disabled) and self.filter(record):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1022, in handleError
    traceback.print_exception(t, v, tb, None, sys.stderr)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 119, in print_exception
    te = TracebackException(type(value), value, tb, limit=limit, compact=True)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 502, in __init__
    self.stack = StackSummary.extract(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 369, in extract
    fnames.add(filename)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
Message: 'Worker with pid %s was terminated due to signal %s'
Arguments: (15353, 1)
--- Logging error ---
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1100, in emit
    msg = self.format(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 943, in format
    return fmt.format(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 680, in format
    record.asctime = self.formatTime(record, self.datefmt)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 617, in formatTime
    s = time.strftime(datefmt, ct)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1021, in handleError
    sys.stderr.write('--- Logging error ---\n')
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1021, in handleError
    sys.stderr.write('--- Logging error ---\n')
RuntimeError: reentrant call inside <_io.BufferedWriter name='<stderr>'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>
Call stack:
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/bin/gunicorn", line 8, in <module>
    sys.exit(run())
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/wsgiapp.py", line 67, in run
    WSGIApplication("%(prog)s [OPTIONS] [APP_MODULE]").run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 231, in run
    super().run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 72, in run
    Arbiter(self).run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 211, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 23, in __init__
    fd, name = tempfile.mkstemp(prefix="wgunicorn-", dir=fdir)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 357, in mkstemp
    return _mkstemp_inner(dir, prefix, suffix, flags, output_type)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 253, in _mkstemp_inner
    file = _os.path.join(dir, pre + name + suf)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/posixpath.py", line 71, in join
    def join(a, *p):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1622, in _log
    record = self.makeRecord(self.name, level, fn, lno, msg, args,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1591, in makeRecord
    rv = _logRecordFactory(name, level, fn, lno, msg, args, exc_info, func,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 340, in __init__
    mp = sys.modules.get('multiprocessing')
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1019, in handleError
    t, v, tb = sys.exc_info()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1633, in handle
    if (not self.disabled) and self.filter(record):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1022, in handleError
    traceback.print_exception(t, v, tb, None, sys.stderr)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 119, in print_exception
    te = TracebackException(type(value), value, tb, limit=limit, compact=True)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 502, in __init__
    self.stack = StackSummary.extract(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/traceback.py", line 369, in extract
    fnames.add(filename)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
Message: 'Worker with pid %s was terminated due to signal %s'
Arguments: (15352, 1)
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1100, in emit
    msg = self.format(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 943, in format
    return fmt.format(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 680, in format
    record.asctime = self.formatTime(record, self.datefmt)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 617, in formatTime
    s = time.strftime(datefmt, ct)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1021, in handleError
    sys.stderr.write('--- Logging error ---\n')
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1021, in handleError
    sys.stderr.write('--- Logging error ---\n')
RuntimeError: reentrant call inside <_io.BufferedWriter name='<stderr>'>
Call stack:
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/bin/gunicorn", line 8, in <module>
    sys.exit(run())
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/wsgiapp.py", line 67, in run
    WSGIApplication("%(prog)s [OPTIONS] [APP_MODULE]").run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 231, in run
    super().run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 72, in run
    Arbiter(self).run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 211, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 23, in __init__
    fd, name = tempfile.mkstemp(prefix="wgunicorn-", dir=fdir)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 357, in mkstemp
    return _mkstemp_inner(dir, prefix, suffix, flags, output_type)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 253, in _mkstemp_inner
    file = _os.path.join(dir, pre + name + suf)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/posixpath.py", line 71, in join
    def join(a, *p):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1622, in _log
    record = self.makeRecord(self.name, level, fn, lno, msg, args,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1591, in makeRecord
    rv = _logRecordFactory(name, level, fn, lno, msg, args, exc_info, func,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 340, in __init__
    mp = sys.modules.get('multiprocessing')
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1019, in handleError
    t, v, tb = sys.exc_info()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1633, in handle
    if (not self.disabled) and self.filter(record):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
Message: 'Worker with pid %s was terminated due to signal %s'
Arguments: (15359, 1)
--- Logging error ---
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>
Call stack:
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/bin/gunicorn", line 8, in <module>
    sys.exit(run())
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/wsgiapp.py", line 67, in run
    WSGIApplication("%(prog)s [OPTIONS] [APP_MODULE]").run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 231, in run
    super().run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 72, in run
    Arbiter(self).run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 211, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 23, in __init__
    fd, name = tempfile.mkstemp(prefix="wgunicorn-", dir=fdir)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 357, in mkstemp
    return _mkstemp_inner(dir, prefix, suffix, flags, output_type)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 253, in _mkstemp_inner
    file = _os.path.join(dir, pre + name + suf)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/posixpath.py", line 71, in join
    def join(a, *p):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1622, in _log
    record = self.makeRecord(self.name, level, fn, lno, msg, args,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1591, in makeRecord
    rv = _logRecordFactory(name, level, fn, lno, msg, args, exc_info, func,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 340, in __init__
    mp = sys.modules.get('multiprocessing')
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1108, in emit
    self.handleError(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1019, in handleError
    t, v, tb = sys.exc_info()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
Message: 'Worker with pid %s was terminated due to signal %s'
Arguments: (15361, 1)
--- Logging error ---
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>
Call stack:
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/bin/gunicorn", line 8, in <module>
    sys.exit(run())
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/wsgiapp.py", line 67, in run
    WSGIApplication("%(prog)s [OPTIONS] [APP_MODULE]").run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 231, in run
    super().run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 72, in run
    Arbiter(self).run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 211, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 23, in __init__
    fd, name = tempfile.mkstemp(prefix="wgunicorn-", dir=fdir)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 357, in mkstemp
    return _mkstemp_inner(dir, prefix, suffix, flags, output_type)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/tempfile.py", line 253, in _mkstemp_inner
    file = _os.path.join(dir, pre + name + suf)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/posixpath.py", line 71, in join
    def join(a, *p):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1622, in _log
    record = self.makeRecord(self.name, level, fn, lno, msg, args,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1591, in makeRecord
    rv = _logRecordFactory(name, level, fn, lno, msg, args, exc_info, func,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 340, in __init__
    mp = sys.modules.get('multiprocessing')
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
Message: 'Worker with pid %s was terminated due to signal %s'
Arguments: (15358, 1)
[2025-05-05 01:21:33 +0800] [15341] [WARNING] Worker with pid 15366 was terminated due to signal 1
[2025-05-05 01:21:33 +0800] [17311] [INFO] Booting worker with pid: 17311
[2025-05-05 01:21:33 +0800] [17311] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:33 +0800] [17312] [INFO] Booting worker with pid: 17312
[2025-05-05 01:21:33 +0800] [17312] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:33 +0800] [17313] [INFO] Booting worker with pid: 17313
[2025-05-05 01:21:33 +0800] [17313] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:33 +0800] [17314] [INFO] Booting worker with pid: 17314
[2025-05-05 01:21:33 +0800] [17314] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:33 +0800] [17315] [INFO] Booting worker with pid: 17315
[2025-05-05 01:21:33 +0800] [17315] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:33 +0800] [17316] [INFO] Booting worker with pid: 17316
[2025-05-05 01:21:33 +0800] [17316] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:33 +0800] [17317] [INFO] Booting worker with pid: 17317
[2025-05-05 01:21:33 +0800] [17317] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:33 +0800] [17318] [INFO] Booting worker with pid: 17318
[2025-05-05 01:21:33 +0800] [17318] [INFO] 工作进程已启动
[2025-05-05 01:21:33 +0800] [17319] [INFO] Booting worker with pid: 17319
[2025-05-05 01:21:33 +0800] [17319] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:33 +0800] [17320] [INFO] Booting worker with pid: 17320
[2025-05-05 01:21:33 +0800] [17320] [INFO] 工作进程已启动
[2025-05-05 01:21:33 +0800] [17321] [INFO] Booting worker with pid: 17321
[2025-05-05 01:21:33 +0800] [17321] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17322] [INFO] Booting worker with pid: 17322
[2025-05-05 01:21:34 +0800] [17322] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17323] [INFO] Booting worker with pid: 17323
[2025-05-05 01:21:34 +0800] [17323] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17324] [INFO] Booting worker with pid: 17324
[2025-05-05 01:21:34 +0800] [17324] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17325] [INFO] Booting worker with pid: 17325
[2025-05-05 01:21:34 +0800] [17325] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17326] [INFO] Booting worker with pid: 17326
[2025-05-05 01:21:34 +0800] [17326] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [15341] [DEBUG] 18 workers
[2025-05-05 01:21:34 +0800] [15341] [INFO] Handling signal: hup
[2025-05-05 01:21:34 +0800] [15341] [INFO] Hang up: Master
[2025-05-05 01:21:34 +0800] [15341] [DEBUG] Current configuration:
  config: gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:5001']
  backlog: 2048
  workers: 29
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /tmp
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: ./logs/error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: run:app
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x107ea5510>
  on_reload: <function OnReload.on_reload at 0x103f54670>
  when_ready: <function WhenReady.when_ready at 0x103f54790>
  pre_fork: <function Prefork.pre_fork at 0x103f548b0>
  post_fork: <function post_fork at 0x107ea5630>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x103f54af0>
  worker_int: <function WorkerInt.worker_int at 0x103f54c10>
  worker_abort: <function WorkerAbort.worker_abort at 0x103f54d30>
  pre_exec: <function PreExec.pre_exec at 0x103f54e50>
  pre_request: <function PreRequest.pre_request at 0x103f54f70>
  post_request: <function PostRequest.post_request at 0x103f55000>
  child_exit: <function ChildExit.child_exit at 0x103f55120>
  worker_exit: <function WorkerExit.worker_exit at 0x103f55240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x103f55360>
  on_exit: <function on_exit at 0x107ea55a0>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
[2025-05-05 01:21:34 +0800] [17327] [INFO] Booting worker with pid: 17327
[2025-05-05 01:21:34 +0800] [17327] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17328] [INFO] Booting worker with pid: 17328
[2025-05-05 01:21:34 +0800] [17328] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17329] [INFO] Booting worker with pid: 17329
[2025-05-05 01:21:34 +0800] [17329] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17330] [INFO] Booting worker with pid: 17330
[2025-05-05 01:21:34 +0800] [17330] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17331] [INFO] Booting worker with pid: 17331
[2025-05-05 01:21:34 +0800] [17331] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17332] [INFO] Booting worker with pid: 17332
[2025-05-05 01:21:34 +0800] [17332] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17333] [INFO] Booting worker with pid: 17333
[2025-05-05 01:21:34 +0800] [17333] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17334] [INFO] Booting worker with pid: 17334
[2025-05-05 01:21:34 +0800] [17334] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17335] [INFO] Booting worker with pid: 17335
[2025-05-05 01:21:34 +0800] [17335] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17336] [INFO] Booting worker with pid: 17336
[2025-05-05 01:21:34 +0800] [17336] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17337] [INFO] Booting worker with pid: 17337
[2025-05-05 01:21:34 +0800] [17337] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17338] [INFO] Booting worker with pid: 17338
[2025-05-05 01:21:34 +0800] [17338] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17339] [INFO] Booting worker with pid: 17339
[2025-05-05 01:21:34 +0800] [17339] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17340] [INFO] Booting worker with pid: 17340
[2025-05-05 01:21:34 +0800] [17340] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17341] [INFO] Booting worker with pid: 17341
[2025-05-05 01:21:34 +0800] [17341] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17342] [INFO] Booting worker with pid: 17342
[2025-05-05 01:21:34 +0800] [17342] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17343] [INFO] Booting worker with pid: 17343
[2025-05-05 01:21:34 +0800] [17343] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17344] [INFO] Booting worker with pid: 17344
[2025-05-05 01:21:34 +0800] [17344] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17346] [INFO] Booting worker with pid: 17346
[2025-05-05 01:21:34 +0800] [17346] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17345] [INFO] Booting worker with pid: 17345
[2025-05-05 01:21:34 +0800] [17345] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17347] [INFO] Booting worker with pid: 17347
[2025-05-05 01:21:34 +0800] [17347] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17348] [INFO] Booting worker with pid: 17348
[2025-05-05 01:21:34 +0800] [17348] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17350] [INFO] Booting worker with pid: 17350
[2025-05-05 01:21:34 +0800] [17350] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17349] [INFO] Booting worker with pid: 17349
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17349] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17351] [INFO] Booting worker with pid: 17351
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17351] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17352] [INFO] Booting worker with pid: 17352
[2025-05-05 01:21:34 +0800] [17352] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17353] [INFO] Booting worker with pid: 17353
[2025-05-05 01:21:34 +0800] [17354] [INFO] Booting worker with pid: 17354
[2025-05-05 01:21:34 +0800] [17354] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17353] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [15341] [DEBUG] 29 workers
[2025-05-05 01:21:34 +0800] [15341] [INFO] Handling signal: hup
[2025-05-05 01:21:34 +0800] [15341] [INFO] Hang up: Master
[2025-05-05 01:21:34 +0800] [17355] [INFO] Booting worker with pid: 17355
[2025-05-05 01:21:34 +0800] [17355] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [15341] [DEBUG] Current configuration:
  config: gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:5001']
  backlog: 2048
  workers: 29
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /tmp
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: ./logs/error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: run:app
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x107ea5ab0>
  on_reload: <function OnReload.on_reload at 0x103f54670>
  when_ready: <function WhenReady.when_ready at 0x103f54790>
  pre_fork: <function Prefork.pre_fork at 0x103f548b0>
  post_fork: <function post_fork at 0x107ea5bd0>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x103f54af0>
  worker_int: <function WorkerInt.worker_int at 0x103f54c10>
  worker_abort: <function WorkerAbort.worker_abort at 0x103f54d30>
  pre_exec: <function PreExec.pre_exec at 0x103f54e50>
  pre_request: <function PreRequest.pre_request at 0x103f54f70>
  post_request: <function PostRequest.post_request at 0x103f55000>
  child_exit: <function ChildExit.child_exit at 0x103f55120>
  worker_exit: <function WorkerExit.worker_exit at 0x103f55240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x103f55360>
  on_exit: <function on_exit at 0x107ea5b40>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17356] [INFO] Booting worker with pid: 17356
[2025-05-05 01:21:34 +0800] [17356] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17357] [INFO] Booting worker with pid: 17357
[2025-05-05 01:21:34 +0800] [17357] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17358] [INFO] Booting worker with pid: 17358
[2025-05-05 01:21:34 +0800] [17358] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17312] [INFO] Worker exiting (pid: 17312)
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17360] [INFO] Booting worker with pid: 17360
[2025-05-05 01:21:34 +0800] [17360] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17359] [INFO] Booting worker with pid: 17359
[2025-05-05 01:21:34 +0800] [17359] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17362] [INFO] Booting worker with pid: 17362
[2025-05-05 01:21:34 +0800] [17362] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17361] [INFO] Booting worker with pid: 17361
[2025-05-05 01:21:34 +0800] [17361] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17363] [INFO] Booting worker with pid: 17363
[2025-05-05 01:21:34 +0800] [17363] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17364] [INFO] Booting worker with pid: 17364
[2025-05-05 01:21:34 +0800] [17364] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17366] [INFO] Booting worker with pid: 17366
[2025-05-05 01:21:34 +0800] [17366] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17365] [INFO] Booting worker with pid: 17365
[2025-05-05 01:21:34 +0800] [17365] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17367] [INFO] Booting worker with pid: 17367
[2025-05-05 01:21:34 +0800] [17367] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17368] [INFO] Booting worker with pid: 17368
[2025-05-05 01:21:34 +0800] [17368] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17313] [INFO] Worker exiting (pid: 17313)
[2025-05-05 01:21:34 +0800] [17369] [INFO] Booting worker with pid: 17369
[2025-05-05 01:21:34 +0800] [17369] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17370] [INFO] Booting worker with pid: 17370
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17370] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17371] [INFO] Booting worker with pid: 17371
[2025-05-05 01:21:34 +0800] [17371] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17372] [INFO] Booting worker with pid: 17372
[2025-05-05 01:21:34 +0800] [17373] [INFO] Booting worker with pid: 17373
[2025-05-05 01:21:34 +0800] [17372] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17373] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17374] [INFO] Booting worker with pid: 17374
[2025-05-05 01:21:34 +0800] [17374] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17375] [INFO] Booting worker with pid: 17375
[2025-05-05 01:21:34 +0800] [17375] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17376] [INFO] Booting worker with pid: 17376
[2025-05-05 01:21:34 +0800] [17376] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17377] [INFO] Booting worker with pid: 17377
[2025-05-05 01:21:34 +0800] [17377] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17378] [INFO] Booting worker with pid: 17378
[2025-05-05 01:21:34 +0800] [17378] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17379] [INFO] Booting worker with pid: 17379
[2025-05-05 01:21:34 +0800] [17379] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17380] [INFO] Booting worker with pid: 17380
[2025-05-05 01:21:34 +0800] [17380] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17314] [INFO] Worker exiting (pid: 17314)
[2025-05-05 01:21:34 +0800] [17381] [INFO] Booting worker with pid: 17381
[2025-05-05 01:21:34 +0800] [17381] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17382] [INFO] Booting worker with pid: 17382
[2025-05-05 01:21:34 +0800] [17382] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17384] [INFO] Booting worker with pid: 17384
[2025-05-05 01:21:34 +0800] [17384] [INFO] 工作进程已启动
[2025-05-05 01:21:34 +0800] [17383] [INFO] Booting worker with pid: 17383
[2025-05-05 01:21:34 +0800] [17383] [INFO] 工作进程已启动
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [15341] [WARNING] Worker with pid 17313 was terminated due to signal 15
[2025-05-05 01:21:34 +0800] [15341] [WARNING] Worker with pid 17312 was terminated due to signal 15
[2025-05-05 01:21:34 +0800] [15341] [WARNING] Worker with pid 17314 was terminated due to signal 15
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17315] [INFO] Worker exiting (pid: 17315)
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/ggevent.py:38: MonkeyPatchWarning: Monkey-patching ssl after ssl has already been imported may lead to errors, including RecursionError on Python 3.6. It may also silently lead to incorrect behaviour on Python 3.7. Please monkey-patch earlier. See https://github.com/gevent/gevent/issues/1016. Modules that had direct imports (NOT patched): ['jwt.jwks_client (/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/jwt/jwks_client.py)']. 
  monkey.patch_all()
[2025-05-05 01:21:34 +0800] [17316] [INFO] Worker exiting (pid: 17316)
[2025-05-05 01:21:34 +0800] [17317] [INFO] Worker exiting (pid: 17317)
[2025-05-05 01:21:34 +0800] [17318] [INFO] Worker exiting (pid: 17318)
[2025-05-05 01:21:34 +0800] [17319] [INFO] Worker exiting (pid: 17319)
[2025-05-05 01:21:34 +0800] [15341] [WARNING] Worker with pid 17319 was terminated due to signal 15
[2025-05-05 01:21:34 +0800] [17320] [INFO] Worker exiting (pid: 17320)
[2025-05-05 01:21:34 +0800] [17321] [INFO] Worker exiting (pid: 17321)
[2025-05-05 01:21:34 +0800] [15341] [WARNING] Worker with pid 17321 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [17322] [INFO] Worker exiting (pid: 17322)
[2025-05-05 01:21:35 +0800] [17323] [INFO] Worker exiting (pid: 17323)
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17323 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [17324] [INFO] Worker exiting (pid: 17324)
[2025-05-05 01:21:35 +0800] [17325] [INFO] Worker exiting (pid: 17325)
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17325 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [17326] [INFO] Worker exiting (pid: 17326)
[2025-05-05 01:21:35 +0800] [17311] [INFO] Worker exiting (pid: 17311)
[2025-05-05 01:21:35 +0800] [17328] [INFO] Worker exiting (pid: 17328)
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17328 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [17331] [INFO] Worker exiting (pid: 17331)
[2025-05-05 01:21:35 +0800] [17338] [INFO] Worker exiting (pid: 17338)
[2025-05-05 01:21:35 +0800] [17332] [INFO] Worker exiting (pid: 17332)
[2025-05-05 01:21:35 +0800] [17329] [INFO] Worker exiting (pid: 17329)
[2025-05-05 01:21:35 +0800] [17330] [INFO] Worker exiting (pid: 17330)
[2025-05-05 01:21:35 +0800] [17339] [INFO] Worker exiting (pid: 17339)
[2025-05-05 01:21:35 +0800] [17327] [INFO] Worker exiting (pid: 17327)
[2025-05-05 01:21:35 +0800] [17333] [INFO] Worker exiting (pid: 17333)
[2025-05-05 01:21:35 +0800] [17335] [INFO] Worker exiting (pid: 17335)
[2025-05-05 01:21:35 +0800] [17334] [INFO] Worker exiting (pid: 17334)
[2025-05-05 01:21:35 +0800] [17342] [INFO] Worker exiting (pid: 17342)
[2025-05-05 01:21:35 +0800] [17340] [INFO] Worker exiting (pid: 17340)
[2025-05-05 01:21:35 +0800] [17343] [INFO] Worker exiting (pid: 17343)
[2025-05-05 01:21:35 +0800] [17337] [INFO] Worker exiting (pid: 17337)
[2025-05-05 01:21:35 +0800] [17346] [INFO] Worker exiting (pid: 17346)
[2025-05-05 01:21:35 +0800] [17351] [INFO] Worker exiting (pid: 17351)
[2025-05-05 01:21:35 +0800] [17350] [INFO] Worker exiting (pid: 17350)
[2025-05-05 01:21:35 +0800] [17355] [INFO] Worker exiting (pid: 17355)
[2025-05-05 01:21:35 +0800] [17341] [INFO] Worker exiting (pid: 17341)
[2025-05-05 01:21:35 +0800] [17348] [INFO] Worker exiting (pid: 17348)
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17350 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17346 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17339 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17337 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17334 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17351 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17340 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17343 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17342 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17333 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17330 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17329 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17327 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17335 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [17354] [INFO] Worker exiting (pid: 17354)
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17338 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [17347] [INFO] Worker exiting (pid: 17347)
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17341 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17348 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17354 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [17353] [INFO] Worker exiting (pid: 17353)
--- Logging error ---
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
RuntimeError: reentrant call inside <_io.BufferedWriter name='/Volumes/acasis/memorial/backend/logs/error.log'>
Call stack:
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/bin/gunicorn", line 8, in <module>
    sys.exit(run())
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/wsgiapp.py", line 67, in run
    WSGIApplication("%(prog)s [OPTIONS] [APP_MODULE]").run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 231, in run
    super().run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/app/base.py", line 72, in run
    Arbiter(self).run()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 209, in run
    self.sleep()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 357, in sleep
    ready = select.select([self.PIPE[0]], [], [], 1.0)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1489, in warning
    self._log(WARNING, msg, args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1624, in _log
    self.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1634, in handle
    self.callHandlers(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1696, in callHandlers
    hdlr.handle(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 968, in handle
    self.emit(record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1218, in emit
    StreamHandler.emit(self, record)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1104, in emit
    self.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/logging/__init__.py", line 1084, in flush
    self.stream.flush()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 242, in handle_chld
    self.reap_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 530, in reap_workers
    self.log.warning(
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/glogging.py", line 261, in warning
    self.error_log.warning(msg, *args, **kwargs)
Message: 'Worker with pid %s was terminated due to signal %s'
Arguments: (17355, 15)
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17347 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17353 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [17352] [INFO] Worker exiting (pid: 17352)
[2025-05-05 01:21:35 +0800] [17336] [INFO] Worker exiting (pid: 17336)
[2025-05-05 01:21:35 +0800] [17345] [INFO] Worker exiting (pid: 17345)
[2025-05-05 01:21:35 +0800] [17349] [INFO] Worker exiting (pid: 17349)
[2025-05-05 01:21:35 +0800] [17344] [INFO] Worker exiting (pid: 17344)
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17336 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17349 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17345 was terminated due to signal 15
[2025-05-05 01:21:35 +0800] [15341] [WARNING] Worker with pid 17344 was terminated due to signal 15
[2025-05-05 01:25:39 +0800] [15341] [INFO] Handling signal: term
[2025-05-05 01:25:39 +0800] [17357] [INFO] Worker exiting (pid: 17357)
[2025-05-05 01:25:39 +0800] [17356] [INFO] Worker exiting (pid: 17356)
[2025-05-05 01:25:39 +0800] [17358] [INFO] Worker exiting (pid: 17358)
[2025-05-05 01:25:39 +0800] [17361] [INFO] Worker exiting (pid: 17361)
[2025-05-05 01:25:39 +0800] [17366] [INFO] Worker exiting (pid: 17366)
[2025-05-05 01:25:39 +0800] [17364] [INFO] Worker exiting (pid: 17364)
[2025-05-05 01:25:39 +0800] [17362] [INFO] Worker exiting (pid: 17362)
[2025-05-05 01:25:39 +0800] [17359] [INFO] Worker exiting (pid: 17359)
[2025-05-05 01:25:39 +0800] [17372] [INFO] Worker exiting (pid: 17372)
[2025-05-05 01:25:39 +0800] [17360] [INFO] Worker exiting (pid: 17360)
[2025-05-05 01:25:39 +0800] [17368] [INFO] Worker exiting (pid: 17368)
[2025-05-05 01:25:39 +0800] [17371] [INFO] Worker exiting (pid: 17371)
[2025-05-05 01:25:39 +0800] [17363] [INFO] Worker exiting (pid: 17363)
[2025-05-05 01:25:39 +0800] [17365] [INFO] Worker exiting (pid: 17365)
[2025-05-05 01:25:39 +0800] [17375] [INFO] Worker exiting (pid: 17375)
[2025-05-05 01:25:39 +0800] [17374] [INFO] Worker exiting (pid: 17374)
[2025-05-05 01:25:39 +0800] [17376] [INFO] Worker exiting (pid: 17376)
[2025-05-05 01:25:39 +0800] [17378] [INFO] Worker exiting (pid: 17378)
[2025-05-05 01:25:39 +0800] [17367] [INFO] Worker exiting (pid: 17367)
[2025-05-05 01:25:39 +0800] [17370] [INFO] Worker exiting (pid: 17370)
[2025-05-05 01:25:39 +0800] [17377] [INFO] Worker exiting (pid: 17377)
[2025-05-05 01:25:39 +0800] [17379] [INFO] Worker exiting (pid: 17379)
[2025-05-05 01:25:39 +0800] [17369] [INFO] Worker exiting (pid: 17369)
[2025-05-05 01:25:39 +0800] [17373] [INFO] Worker exiting (pid: 17373)
[2025-05-05 01:25:39 +0800] [17380] [INFO] Worker exiting (pid: 17380)
[2025-05-05 01:25:39 +0800] [17381] [INFO] Worker exiting (pid: 17381)
[2025-05-05 01:25:39 +0800] [17384] [INFO] Worker exiting (pid: 17384)
[2025-05-05 01:25:39 +0800] [17383] [INFO] Worker exiting (pid: 17383)
[2025-05-05 01:25:39 +0800] [17382] [INFO] Worker exiting (pid: 17382)
[2025-05-05 01:25:40 +0800] [15341] [INFO] Shutting down: Master
Gunicorn服务器正在关闭...
2025-05-06 01:42:13,567 - ERROR - 服务器错误: GET /api/auth/me (api_docs.auth_current_user) - 状态码: 500
