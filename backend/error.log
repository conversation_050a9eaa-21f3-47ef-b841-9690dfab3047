[2025-05-05 01:15:14 +0800] [14099] [DEBUG] Current configuration:
  config: ./gunicorn.conf.py
  wsgi_app: None
  bind: ['0.0.0.0:5001']
  backlog: 2048
  workers: 29
  worker_class: gevent
  threads: 1
  worker_connections: 1000
  max_requests: 1000
  max_requests_jitter: 50
  timeout: 30
  graceful_timeout: 30
  keepalive: 2
  limit_request_line: 4096
  limit_request_fields: 100
  limit_request_field_size: 8190
  reload: False
  reload_engine: auto
  reload_extra_files: []
  spew: False
  check_config: False
  print_config: False
  preload_app: True
  sendfile: None
  reuse_port: False
  chdir: /Volumes/acasis/memorial/backend
  daemon: False
  raw_env: ['APP_ENV=production']
  pidfile: None
  worker_tmp_dir: /dev/shm
  user: 501
  group: 20
  umask: 0
  initgroups: False
  tmp_upload_dir: None
  secure_scheme_headers: {'X-FORWARDED-PROTOCOL': 'ssl', 'X-FORWARDED-PROTO': 'https', 'X-FORWARDED-SSL': 'on'}
  forwarded_allow_ips: ['127.0.0.1']
  accesslog: ./logs/access.log
  disable_redirect_access_to_syslog: False
  access_log_format: %({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"
  errorlog: error.log
  loglevel: debug
  capture_output: True
  logger_class: gunicorn.glogging.Logger
  logconfig: None
  logconfig_dict: {}
  syslog_addr: unix:///var/run/syslog
  syslog: False
  syslog_prefix: None
  syslog_facility: user
  enable_stdio_inheritance: False
  statsd_host: None
  dogstatsd_tags: 
  statsd_prefix: 
  proc_name: memorial_api
  default_proc_name: run:app
  pythonpath: None
  paste: None
  on_starting: <function on_starting at 0x103a32320>
  on_reload: <function OnReload.on_reload at 0x103a30670>
  when_ready: <function WhenReady.when_ready at 0x103a30790>
  pre_fork: <function Prefork.pre_fork at 0x103a308b0>
  post_fork: <function post_fork at 0x103a32560>
  post_worker_init: <function PostWorkerInit.post_worker_init at 0x103a30af0>
  worker_int: <function WorkerInt.worker_int at 0x103a30c10>
  worker_abort: <function WorkerAbort.worker_abort at 0x103a30d30>
  pre_exec: <function PreExec.pre_exec at 0x103a30e50>
  pre_request: <function PreRequest.pre_request at 0x103a30f70>
  post_request: <function PostRequest.post_request at 0x103a31000>
  child_exit: <function ChildExit.child_exit at 0x103a31120>
  worker_exit: <function WorkerExit.worker_exit at 0x103a31240>
  nworkers_changed: <function NumWorkersChanged.nworkers_changed at 0x103a31360>
  on_exit: <function on_exit at 0x103a32440>
  proxy_protocol: False
  proxy_allow_ips: ['127.0.0.1']
  keyfile: None
  certfile: None
  ssl_version: 2
  cert_reqs: 0
  ca_certs: None
  suppress_ragged_eofs: True
  do_handshake_on_connect: False
  ciphers: None
  raw_paste_global_conf: []
  strip_header_spaces: False
[2025-05-05 01:15:15 +0800] [14099] [INFO] Starting gunicorn 20.1.0
Gunicorn服务器正在启动...
[2025-05-05 01:15:15 +0800] [14099] [DEBUG] Arbiter booted
[2025-05-05 01:15:15 +0800] [14099] [INFO] Listening at: http://0.0.0.0:5001 (14099)
[2025-05-05 01:15:15 +0800] [14099] [INFO] Using worker: gevent
[2025-05-05 01:15:15 +0800] [14099] [INFO] Unhandled exception in main loop
Traceback (most recent call last):
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 202, in run
    self.manage_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 551, in manage_workers
    self.spawn_workers()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 622, in spawn_workers
    self.spawn_worker()
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/arbiter.py", line 569, in spawn_worker
    worker = self.worker_class(self.worker_age, self.pid, self.LISTENERS,
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base_async.py", line 23, in __init__
    super().__init__(*args, **kwargs)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/base.py", line 64, in __init__
    self.tmp = WorkerTmp(cfg)
  File "/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.10/site-packages/gunicorn/workers/workertmp.py", line 22, in __init__
    raise RuntimeError("%s doesn't exist. Can't create workertmp." % fdir)
RuntimeError: /dev/shm doesn't exist. Can't create workertmp.
