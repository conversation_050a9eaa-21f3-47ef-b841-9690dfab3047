from enum import Enum

from pydantic import BaseModel, Field


class RenderStatus(BaseModel):
    available: bool = Field(..., description="渲染服务是否可用")
    version: str = Field(..., description="渲染服务版本")
    maxClients: int = Field(..., description="最大客户端数量")
    activeClients: int = Field(..., description="当前活跃客户端数量")
    gpuRendering: bool = Field(..., description="是否支持GPU渲染")
    rendererStatus: str = Field(..., description="渲染器状态")


class RenderInitRequest(BaseModel):
    modelPath: str = Field(..., description="模型路径")
    width: int | None = Field(800, description="渲染宽度")
    height: int | None = Field(600, description="渲染高度")


class RenderControlType(str, Enum):
    rotate = "rotate"
    zoom = "zoom"
    reset = "reset"


class RenderControlRequest(BaseModel):
    type: RenderControlType = Field(..., description="控制类型")
    x: float | None = Field(None, description="X轴旋转值")
    y: float | None = Field(None, description="Y轴旋转值")
    delta: float | None = Field(None, description="缩放增量")


class RenderStatusResponse(BaseModel):
    success: bool = Field(..., description="操作是否成功")
    status: RenderStatus = Field(..., description="渲染服务状态")


class RenderInitResponse(BaseModel):
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
    sessionId: str = Field(..., description="会话ID")
    modelPath: str = Field(..., description="模型路径")
    width: int = Field(..., description="渲染宽度")
    height: int = Field(..., description="渲染高度")


class RenderControlResponse(BaseModel):
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="响应消息")
