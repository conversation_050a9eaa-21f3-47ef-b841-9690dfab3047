from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime


class FeatureAccessCheck(BaseModel):
    """功能访问检查结果"""

    allowed: bool = Field(..., description="是否允许访问")
    remaining: Optional[Union[int, float]] = Field(None, description="剩余可用量")
    limit: Union[int, float, bool] = Field(..., description="功能限制")
    current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
    tier: str = Field(..., description="用户会员等级")


class OperationValidation(BaseModel):
    """操作验证结果"""

    allowed: bool = Field(..., description="是否允许操作")
    remaining: Optional[Union[int, float]] = Field(None, description="剩余可用量")
    limit: Optional[Union[int, float, bool]] = Field(None, description="功能限制")
    current_usage: Optional[Union[int, float]] = Field(None, description="当前使用量")
    tier: Optional[str] = Field(None, description="用户会员等级")
    error: Optional[str] = Field(None, description="错误信息")


class UsageUpdateRequest(BaseModel):
    """使用量更新请求"""

    feature_type: str = Field(..., description="功能类型")
    amount: Union[int, float] = Field(1, description="使用量", gt=0)


class UsageInfo(BaseModel):
    """使用情况信息"""

    memorial_spaces_used: int = Field(0, description="已使用的纪念空间数量")
    storage_used_gb: float = Field(0, description="已使用的存储空间(GB)")
    ai_minutes_used: int = Field(0, description="已使用的AI服务分钟数")
    api_calls_used: int = Field(0, description="已使用的API调用次数")
    last_reset_date: str = Field(..., description="上次重置日期")
    period_start: str = Field(..., description="当前计费周期开始")
    period_end: str = Field(..., description="当前计费周期结束")


class TierLimits(BaseModel):
    """等级限制"""

    memorial_spaces: Union[int, str] = Field(..., description="纪念空间数量限制")
    storage_gb: Union[int, str] = Field(..., description="存储空间限制(GB)")
    ai_minutes: Union[int, str] = Field(..., description="AI服务分钟数限制")
    custom_domains: Union[int, str] = Field(..., description="自定义域名数量限制")
    api_calls: Union[int, str] = Field(..., description="API调用次数限制")
    priority_support: bool = Field(..., description="是否有优先支持")
    advanced_analytics: bool = Field(..., description="是否有高级分析")
    white_label: bool = Field(..., description="是否有白标服务")
    custom_branding: bool = Field(..., description="是否有自定义品牌")
    backup_retention: Union[int, str] = Field(..., description="备份保留天数")


class UpgradeRecommendation(BaseModel):
    """升级建议"""

    feature: str = Field(..., description="功能名称")
    current_usage: Union[int, float] = Field(..., description="当前使用量")
    current_limit: Union[int, float] = Field(..., description="当前限制")
    usage_percentage: float = Field(..., description="使用百分比")
    recommended_tier: str = Field(..., description="推荐的等级")
    reason: str = Field(..., description="推荐原因")


class MembershipFeatures(BaseModel):
    """会员功能状态"""

    memorial_spaces: FeatureAccessCheck
    storage: FeatureAccessCheck
    ai_minutes: FeatureAccessCheck
    priority_support: bool
    advanced_analytics: bool


class MembershipSummaryResponse(BaseModel):
    """会员权益概览响应"""

    user_id: str = Field(..., description="用户ID")
    current_tier: str = Field(..., description="当前会员等级")
    tier_display_name: str = Field(..., description="等级显示名称")
    limits: Dict[str, Any] = Field(..., description="功能限制")
    usage: Dict[str, Any] = Field(..., description="使用情况")
    usage_percentages: Dict[str, Optional[float]] = Field(..., description="使用百分比")
    upgrade_recommendations: List[UpgradeRecommendation] = Field(
        ..., description="升级建议"
    )
    features: MembershipFeatures = Field(..., description="功能权限状态")


class TierInfo(BaseModel):
    """等级信息"""

    tier: str = Field(..., description="等级代码")
    name: str = Field(..., description="等级名称")
    description: str = Field(..., description="等级描述")
    limits: Dict[str, Any] = Field(..., description="功能限制")


class TiersResponse(BaseModel):
    """所有等级信息响应"""

    tiers: List[TierInfo] = Field(..., description="所有等级信息")


# 权益验证装饰器相关
class MembershipRequirement(BaseModel):
    """会员权益要求"""

    required_tier: Optional[str] = Field(None, description="要求的最低等级")
    required_features: Optional[List[str]] = Field(None, description="要求的功能")
    feature_usage: Optional[Dict[str, Union[int, float]]] = Field(
        None, description="功能使用量要求"
    )


class MembershipValidationResult(BaseModel):
    """会员权益验证结果"""

    valid: bool = Field(..., description="是否通过验证")
    user_tier: str = Field(..., description="用户当前等级")
    required_tier: Optional[str] = Field(None, description="要求的等级")
    missing_features: Optional[List[str]] = Field(None, description="缺少的功能")
    insufficient_usage: Optional[Dict[str, Any]] = Field(None, description="使用量不足的功能")
    error_message: Optional[str] = Field(None, description="错误信息")
    upgrade_suggestion: Optional[str] = Field(None, description="升级建议")


# 使用量监控相关
class UsageAlert(BaseModel):
    """使用量警告"""

    feature_type: str = Field(..., description="功能类型")
    current_usage: Union[int, float] = Field(..., description="当前使用量")
    limit: Union[int, float] = Field(..., description="限制")
    usage_percentage: float = Field(..., description="使用百分比")
    alert_level: str = Field(..., description="警告级别: warning/critical")
    message: str = Field(..., description="警告信息")


class UsageMonitoringResponse(BaseModel):
    """使用量监控响应"""

    user_id: str = Field(..., description="用户ID")
    tier: str = Field(..., description="用户等级")
    alerts: List[UsageAlert] = Field(..., description="使用量警告")
    recommendations: List[UpgradeRecommendation] = Field(..., description="升级建议")
    overall_status: str = Field(..., description="整体状态: healthy/warning/critical")


# 功能使用历史
class FeatureUsageHistory(BaseModel):
    """功能使用历史"""

    feature_type: str = Field(..., description="功能类型")
    date: str = Field(..., description="日期")
    usage_amount: Union[int, float] = Field(..., description="使用量")
    cumulative_usage: Union[int, float] = Field(..., description="累计使用量")


class UsageHistoryResponse(BaseModel):
    """使用历史响应"""

    user_id: str = Field(..., description="用户ID")
    period_start: str = Field(..., description="统计开始日期")
    period_end: str = Field(..., description="统计结束日期")
    history: List[FeatureUsageHistory] = Field(..., description="使用历史")


# 权益比较
class TierComparison(BaseModel):
    """等级比较"""

    feature_name: str = Field(..., description="功能名称")
    free: Union[int, float, bool, str] = Field(..., description="免费版")
    premium: Union[int, float, bool, str] = Field(..., description="高级版")
    family: Union[int, float, bool, str] = Field(..., description="家族版")
    enterprise: Union[int, float, bool, str] = Field(..., description="企业版")


class TierComparisonResponse(BaseModel):
    """等级比较响应"""

    comparisons: List[TierComparison] = Field(..., description="功能比较")
    recommendations: Dict[str, str] = Field(..., description="推荐理由")
