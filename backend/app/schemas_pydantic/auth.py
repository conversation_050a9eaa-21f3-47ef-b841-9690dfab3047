from pydantic import BaseModel, EmailStr, Field


class PasswordResetRequest(BaseModel):
    """密码重置请求模式"""

    email: EmailStr = Field(..., description="用户邮箱")


class PasswordResetConfirm(BaseModel):
    """密码重置确认模式"""

    token: str = Field(..., description="密码重置令牌")
    new_password: str = Field(
        ..., min_length=8, max_length=100, description="新密码，长度8-100位"
    )


class EmailVerificationRequest(BaseModel):
    """邮箱验证请求模式"""

    token: str = Field(..., description="邮箱验证令牌")


class MessageResponse(BaseModel):
    """通用消息响应模式"""

    message: str = Field(..., description="响应消息")
    success: bool = Field(default=True, description="操作是否成功")
