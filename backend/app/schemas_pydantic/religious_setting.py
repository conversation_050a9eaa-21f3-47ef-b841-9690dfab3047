import uuid  # Added uuid import
from datetime import datetime
from typing import Any

from pydantic import BaseModel, ConfigDict, Field  # Added ConfigDict


class ReligiousCulturalSettingBase(BaseModel):
    name: str = Field(..., max_length=64)
    description: str | None = None
    ritual_elements: list[Any] = Field(default_factory=list)  # 可以是任意类型，根据实际数据调整
    special_dates: list[Any] = Field(default_factory=list)  # 可以是任意类型，根据实际数据调整
    default_prayers: str | None = None
    icon_path: str | None = Field(None, max_length=256)


class ReligiousCulturalSettingCreate(ReligiousCulturalSettingBase):
    pass


class ReligiousCulturalSettingUpdate(BaseModel):
    name: str | None = None
    description: str | None = None
    ritual_elements: list[Any] | None = None
    special_dates: list[Any] | None = None
    default_prayers: str | None = None
    icon_path: str | None = Field(None, max_length=256)


class ReligiousCulturalSettingResponse(ReligiousCulturalSettingBase):
    id: uuid.UUID  # Changed from int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)
