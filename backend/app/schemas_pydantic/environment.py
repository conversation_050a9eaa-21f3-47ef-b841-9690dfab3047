import uuid  # Add uuid import
from datetime import datetime
from typing import Any

from pydantic import BaseModel, ConfigDict, Field  # Added ConfigDict


class EnvironmentBase(BaseModel):
    name: str = Field(..., max_length=64)
    description: str | None = None
    style_type: str = Field(..., max_length=64)
    religious_affiliation: list[str] = Field(default_factory=list)
    cultural_elements: dict[str, Any] = Field(default_factory=dict)
    season_support: bool = False
    weather_support: bool = False
    model_path: str = Field(..., max_length=256)
    low_poly_path: str | None = Field(None, max_length=256)
    thumbnail: str | None = Field(None, max_length=256)


class EnvironmentCreate(EnvironmentBase):
    pass


class EnvironmentUpdate(BaseModel):
    name: str | None = None
    description: str | None = None
    style_type: str | None = None
    religious_affiliation: list[str] | None = None
    cultural_elements: dict[str, Any] | None = None
    season_support: bool | None = None
    weather_support: bool | None = None
    model_path: str | None = None
    low_poly_path: str | None = None
    thumbnail: str | None = None


class EnvironmentResponse(EnvironmentBase):
    id: uuid.UUID  # Changed from int to uuid.UUID
    creation_date: datetime

    model_config = ConfigDict(from_attributes=True)
