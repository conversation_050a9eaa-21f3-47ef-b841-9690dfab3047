import uuid
from datetime import datetime
from enum import Enum
from typing import Any

from pydantic import BaseModel, ConfigDict, Field, HttpUrl  # Added ConfigDict


class AssetTypeEnum(str, Enum):
    image = "image"
    video = "video"
    audio = "audio"
    document = "document"
    other = "other"
    cover_image = "cover_image"  # Specific type for cover image
    life_photo = "life_photo"  # Specific type for life photos


class MemorialAssetBase(BaseModel):
    asset_type: AssetTypeEnum = Field(..., description="资产类型")
    title: str | None = Field(None, max_length=255, description="标题")
    description: str | None = Field(None, description="描述")
    # file_url is usually set by the system after upload, not directly in create schema from client
    # thumbnail_url is also usually system-generated
    original_filename: str | None = Field(None, max_length=255, description="原始文件名")
    file_size: int | None = Field(None, description="文件大小 (bytes)")
    asset_metadata: dict[str, Any] | None = Field(
        None, description="资产元数据 (JSON), e.g., image dimensions, video duration"
    )
    display_order: int | None = Field(0, description="显示顺序")
    is_ai_enhanced: bool | None = Field(False, description="是否经过AI增强")


class MemorialAssetCreate(MemorialAssetBase):
    # space_id and uploader_id are usually set by the system/context, not part of create schema directly from client
    # file_url will be provided by the upload logic
    file_url: HttpUrl  # This must be provided during creation by the backend logic after upload
    pass


class MemorialAssetUpdate(BaseModel):
    asset_type: AssetTypeEnum | None = Field(None, description="资产类型")
    title: str | None = Field(None, max_length=255, description="标题")
    description: str | None = Field(None, description="描述")
    thumbnail_url: HttpUrl | None = Field(None, description="缩略图URL (可更新)")
    asset_metadata: dict[str, Any] | None = Field(None, description="资产元数据 (JSON)")
    display_order: int | None = Field(None, description="显示顺序")
    is_ai_enhanced: bool | None = Field(None, description="是否经过AI增强")


class MemorialAssetResponse(MemorialAssetBase):
    id: uuid.UUID
    space_id: uuid.UUID
    uploader_id: uuid.UUID
    file_url: HttpUrl
    thumbnail_url: HttpUrl | None = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class MemorialAssetListResponse(BaseModel):
    assets: list[MemorialAssetResponse]
    total_count: int

    model_config = ConfigDict(from_attributes=True)
