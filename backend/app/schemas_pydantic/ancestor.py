import uuid
from datetime import date, datetime

from pydantic import BaseModel, ConfigDict, Field  # Added ConfigDict


class AncestorBase(BaseModel):
    user_id: uuid.UUID  # Changed from int
    name: str = Field(..., max_length=64)
    birth_date: date | None = None
    death_date: date | None = None
    religious_preference: str | None = Field(None, max_length=64)
    cultural_background: str | None = Field(None, max_length=64)
    biography: str | None = None
    photo_path: str | None = Field(None, max_length=256)
    model_path: str | None = Field(None, max_length=256)


class AncestorCreate(AncestorBase):
    pass


class AncestorUpdate(BaseModel):
    user_id: uuid.UUID | None = None
    name: str | None = Field(None, max_length=64)
    birth_date: date | None = None
    death_date: date | None = None
    religious_preference: str | None = Field(None, max_length=64)
    cultural_background: str | None = Field(None, max_length=64)
    biography: str | None = None
    photo_path: str | None = Field(None, max_length=256)
    model_path: str | None = Field(None, max_length=256)


class AncestorResponse(AncestorBase):
    id: uuid.UUID  # Changed from int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)
