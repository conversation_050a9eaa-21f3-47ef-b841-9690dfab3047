# 权限控制相关的 Pydantic 模式
from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field


class PermissionType(str, Enum):
    """权限类型枚举"""

    view = "view"  # 查看权限
    edit = "edit"  # 编辑权限
    tribute = "tribute"  # 祭拜权限
    manage = "manage"  # 管理权限
    moderate = "moderate"  # 审核权限


class AccessType(str, Enum):
    """访问类型枚举"""

    view = "view"
    tribute = "tribute"
    edit = "edit"
    manage = "manage"


class PermissionBase(BaseModel):
    """权限基础模式"""

    permission_type: PermissionType
    expires_at: Optional[datetime] = Field(None, description="权限过期时间")
    can_view_private_info: bool = Field(default=False, description="能否查看私密信息")
    can_moderate: bool = Field(default=False, description="能否审核内容")
    can_invite_others: bool = Field(default=False, description="能否邀请其他人")


class PermissionCreate(PermissionBase):
    """创建权限请求"""

    user_id: UUID
    memorial_space_id: UUID


class PermissionUpdate(BaseModel):
    """更新权限请求"""

    permission_type: Optional[PermissionType] = None
    expires_at: Optional[datetime] = None
    can_view_private_info: Optional[bool] = None
    can_moderate: Optional[bool] = None
    can_invite_others: Optional[bool] = None
    is_active: Optional[bool] = None


class PermissionResponse(PermissionBase):
    """权限响应模式"""

    id: UUID
    memorial_space_id: UUID
    user_id: UUID
    granted_by: UUID
    granted_at: datetime
    is_active: bool
    user_name: Optional[str] = None
    grantor_name: Optional[str] = None

    class Config:
        from_attributes = True


class AccessCheckRequest(BaseModel):
    """权限检查请求"""

    memorial_space_id: UUID
    access_type: AccessType
    access_password: Optional[str] = Field(None, description="访问密码")


class AccessCheckResponse(BaseModel):
    """权限检查响应"""

    access_granted: bool
    permission_level: Optional[PermissionType] = None
    denial_reason: Optional[str] = None
    requires_password: bool = False
    requires_family_verification: bool = False


class AccessLogEntry(BaseModel):
    """访问日志条目"""

    id: UUID
    memorial_space_id: UUID
    user_id: Optional[UUID] = None
    access_type: AccessType
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    access_granted: bool
    denial_reason: Optional[str] = None
    accessed_at: datetime
    session_duration: Optional[int] = None
    user_name: Optional[str] = None

    class Config:
        from_attributes = True


class AccessLogListResponse(BaseModel):
    """访问日志列表响应"""

    items: list[AccessLogEntry]
    total: int
    page: int
    size: int


class PermissionBatchOperation(BaseModel):
    """批量权限操作"""

    user_ids: list[UUID]
    permission_type: PermissionType
    expires_at: Optional[datetime] = None
    can_view_private_info: bool = False
    can_moderate: bool = False
    can_invite_others: bool = False


class FamilyAccessRequest(BaseModel):
    """家族访问请求"""

    memorial_space_id: UUID
    relationship_to_deceased: str = Field(..., description="与逝者的关系")
    verification_message: str = Field(..., description="验证信息")
    contact_email: Optional[str] = None


class PrivacySettings(BaseModel):
    """隐私设置配置"""

    allow_public_tributes: bool = Field(default=True, description="允许公开祭拜")
    allow_anonymous_tributes: bool = Field(default=True, description="允许匿名祭拜")
    require_approval_for_tributes: bool = Field(default=False, description="祭拜需要审核")
    allow_public_messages: bool = Field(default=True, description="允许公开留言")
    show_visitor_count: bool = Field(default=True, description="显示访问者数量")
    show_tribute_count: bool = Field(default=True, description="显示祭拜次数")
    enable_access_logging: bool = Field(default=True, description="启用访问日志")
    max_daily_tributes_per_user: Optional[int] = Field(None, description="每用户每日最大祭拜次数")
