# 祭拜功能相关的 Pydantic 模式
from datetime import datetime
from typing import Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class TributeItem(BaseModel):
    """祭拜物品配置"""

    item_id: str = Field(..., description="物品ID")
    name: str = Field(..., description="物品名称")
    quantity: int = Field(default=1, description="数量")
    position: Optional[Dict] = Field(None, description="3D位置坐标 {x, y, z}")


class ClientInfo(BaseModel):
    """客户端信息"""

    device_type: Optional[str] = Field(None, description="设备类型")
    browser: Optional[str] = Field(None, description="浏览器信息")
    screen_resolution: Optional[str] = Field(None, description="屏幕分辨率")
    user_agent: Optional[str] = Field(None, description="用户代理")


class TributeBase(BaseModel):
    """祭拜记录基础模式"""

    tribute_type: str = Field(
        ..., description="祭拜类型：candle, flower, incense, food, bow, offering"
    )
    message: Optional[str] = Field(None, description="祭拜留言或寄语")
    is_anonymous: bool = Field(default=False, description="是否匿名祭拜")
    tribute_items: Optional[List[TributeItem]] = Field(None, description="祭拜物品列表")
    duration_seconds: Optional[int] = Field(default=0, description="祭拜持续时间（秒）")
    coordinates: Optional[Dict] = Field(None, description="3D场景中的坐标位置 {x, y, z}")
    client_info: Optional[ClientInfo] = Field(None, description="客户端信息")


class TributeCreateRequest(TributeBase):
    """创建祭拜记录请求模式"""

    pass


class TributeResponse(TributeBase):
    """祭拜记录响应模式"""

    id: UUID
    memorial_space_id: UUID
    user_id: UUID
    user_name: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True


class TributeListResponse(BaseModel):
    """祭拜记录列表响应模式"""

    items: List[TributeResponse]
    total: int
    skip: int = 0
    limit: int = 50


class TributeStatsResponse(BaseModel):
    """祭拜统计响应模式"""

    memorial_space_id: UUID
    total_tributes: int
    tribute_types: Dict[str, int] = Field(description="各类型祭拜次数统计")
    recent_tributes: List[TributeResponse] = Field(description="最近的祭拜记录")


class TributeItemBase(BaseModel):
    """祭品基础模式"""

    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = None
    category: str = Field(
        ..., description="祭品类别：flower, candle, incense, food, drink, fruit"
    )
    image_url: Optional[str] = None
    model_url: Optional[str] = Field(None, description="3D模型URL")
    price: float = Field(default=0, ge=0, description="价格")


class TributeItemCreate(TributeItemBase):
    """创建祭品模式"""

    pass


class TributeItemResponse(TributeItemBase):
    """祭品响应模式"""

    id: UUID
    is_premium: bool
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TributeItemListResponse(BaseModel):
    """祭品列表响应模式"""

    items: List[TributeItemResponse]
    total: int
    categories: List[str] = Field(description="可用的祭品类别")


class WorshipSessionBase(BaseModel):
    """祭拜会话基础模式"""

    session_type: str = Field(
        default="individual", description="会话类型：individual, group, family"
    )
    duration_minutes: Optional[int] = Field(None, description="预计祭拜时长（分钟）")
    participant_count: int = Field(default=1, description="参与人数")


class WorshipSessionCreate(WorshipSessionBase):
    """创建祭拜会话模式"""

    memorial_space_id: UUID


class WorshipSessionResponse(WorshipSessionBase):
    """祭拜会话响应模式"""

    id: UUID
    memorial_space_id: UUID
    creator_id: UUID
    status: str = Field(description="会话状态：active, completed, cancelled")
    started_at: datetime
    ended_at: Optional[datetime] = None
    tribute_count: int = Field(default=0, description="本次会话的祭拜次数")

    class Config:
        from_attributes = True


# 预定义祭品配置
TRIBUTE_ITEMS_TEMPLATES = {
    "flowers": [
        {
            "name": "白菊花",
            "description": "寓意哀思和纪念",
            "category": "flower",
            "image_url": "/images/tributes/white_chrysanthemum.jpg",
            "price": 0,
        },
        {
            "name": "百合花",
            "description": "象征纯洁和重生",
            "category": "flower",
            "image_url": "/images/tributes/lily.jpg",
            "price": 0,
        },
        {
            "name": "玫瑰花束",
            "description": "表达深深的思念",
            "category": "flower",
            "image_url": "/images/tributes/roses.jpg",
            "price": 5.00,
        },
    ],
    "candles": [
        {
            "name": "祈福蜡烛",
            "description": "为逝者点亮心灯",
            "category": "candle",
            "image_url": "/images/tributes/prayer_candle.jpg",
            "price": 0,
        },
        {
            "name": "莲花蜡烛",
            "description": "佛教祈福专用",
            "category": "candle",
            "image_url": "/images/tributes/lotus_candle.jpg",
            "price": 3.00,
        },
    ],
    "incense": [
        {
            "name": "檀香",
            "description": "清香淡雅，净化心灵",
            "category": "incense",
            "image_url": "/images/tributes/sandalwood.jpg",
            "price": 0,
        },
        {
            "name": "沉香",
            "description": "珍贵香材，表达敬意",
            "category": "incense",
            "image_url": "/images/tributes/agarwood.jpg",
            "price": 10.00,
        },
    ],
    "food": [
        {
            "name": "水果拼盘",
            "description": "新鲜时令水果",
            "category": "food",
            "image_url": "/images/tributes/fruit_platter.jpg",
            "price": 0,
        },
        {
            "name": "糕点",
            "description": "精美糕点供品",
            "category": "food",
            "image_url": "/images/tributes/pastries.jpg",
            "price": 8.00,
        },
    ],
}
