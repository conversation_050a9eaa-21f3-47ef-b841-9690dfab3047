# 3D场景Pydantic模式
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, validator


class SceneBase(BaseModel):
    """场景基础模式"""

    name: str = Field(..., min_length=1, max_length=100, description="场景名称")
    description: Optional[str] = Field(None, description="场景描述")
    category: str = Field(
        ..., description="场景类别：buddhist, christian, traditional, modern, nature"
    )
    model_url: str = Field(..., description="3D模型文件URL")
    texture_urls: Optional[List[str]] = Field(default=None, description="贴图文件URLs")
    thumbnail_url: Optional[str] = Field(None, description="场景缩略图URL")


class SceneConfig(BaseModel):
    """场景配置模式"""

    lighting_config: Optional[Dict[str, Any]] = Field(default=None, description="光照配置")
    camera_config: Optional[Dict[str, Any]] = Field(default=None, description="相机默认配置")
    interaction_config: Optional[Dict[str, Any]] = Field(
        default=None, description="交互配置"
    )
    audio_config: Optional[Dict[str, Any]] = Field(default=None, description="音频配置")


class SceneCreate(SceneBase, SceneConfig):
    """创建场景模式"""

    is_premium: bool = Field(default=False, description="是否为付费场景")
    supports_mobile: bool = Field(default=True, description="是否支持移动端")
    min_performance_level: str = Field(default="low", description="最低性能要求")

    @validator("category")
    def validate_category(cls, v):
        valid_categories = [
            "buddhist",
            "christian",
            "traditional",
            "modern",
            "nature",
            "cosmos",
        ]
        if v not in valid_categories:
            raise ValueError(f'场景类别必须是以下值之一: {", ".join(valid_categories)}')
        return v

    @validator("min_performance_level")
    def validate_performance_level(cls, v):
        valid_levels = ["low", "medium", "high"]
        if v not in valid_levels:
            raise ValueError(f'性能要求必须是以下值之一: {", ".join(valid_levels)}')
        return v

    @validator("model_url")
    def validate_model_url(cls, v):
        if not v or not v.strip():
            raise ValueError("3D模型URL不能为空")
        if not (
            v.startswith("http://") or v.startswith("https://") or v.startswith("/")
        ):
            raise ValueError("3D模型URL格式无效")
        return v

    @validator("texture_urls")
    def validate_texture_urls(cls, v):
        if v:
            for url in v:
                if not (
                    url.startswith("http://")
                    or url.startswith("https://")
                    or url.startswith("/")
                ):
                    raise ValueError(f"贴图URL格式无效: {url}")
        return v


class SceneUpdate(BaseModel):
    """更新场景模式"""

    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    model_url: Optional[str] = None
    texture_urls: Optional[List[str]] = None
    thumbnail_url: Optional[str] = None
    lighting_config: Optional[Dict[str, Any]] = None
    camera_config: Optional[Dict[str, Any]] = None
    interaction_config: Optional[Dict[str, Any]] = None
    audio_config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    is_premium: Optional[bool] = None
    supports_mobile: Optional[bool] = None
    min_performance_level: Optional[str] = None


class SceneResponse(SceneBase, SceneConfig):
    """场景响应模式"""

    id: UUID
    is_active: bool
    is_premium: bool
    supports_mobile: bool
    min_performance_level: str
    usage_count: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class SceneListResponse(BaseModel):
    """场景列表响应模式"""

    id: UUID
    name: str
    description: Optional[str]
    category: str
    thumbnail_url: Optional[str]
    is_premium: bool
    supports_mobile: bool
    min_performance_level: str
    usage_count: int

    class Config:
        from_attributes = True


class InteractionPointBase(BaseModel):
    """交互点基础模式"""

    name: str = Field(..., min_length=1, max_length=100)
    interaction_type: str = Field(
        ..., description="交互类型：offering, candle, photo_display, message_board"
    )
    position: Dict[str, float] = Field(..., description="3D位置坐标")
    rotation: Optional[Dict[str, float]] = Field(default=None, description="旋转角度")
    scale: Optional[Dict[str, float]] = Field(default=None, description="缩放比例")
    config: Optional[Dict[str, Any]] = Field(default=None, description="交互配置")


class InteractionPointCreate(InteractionPointBase):
    """创建交互点模式"""

    scene_id: UUID


class InteractionPointResponse(InteractionPointBase):
    """交互点响应模式"""

    id: UUID
    scene_id: UUID
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True


# 预定义场景配置
SCENE_TEMPLATES = {
    "buddhist_temple": {
        "name": "佛教寺庙",
        "description": "传统佛教寺庙环境，庄严肃穆，适合佛教信仰的纪念",
        "category": "buddhist",
        "lighting_config": {
            "ambient": {"color": "#FFF8DC", "intensity": 0.3},
            "directional": {
                "color": "#FFE4B5",
                "intensity": 0.7,
                "position": {"x": 5, "y": 10, "z": 5},
            },
            "candle_lights": {"color": "#FF4500", "intensity": 0.4},
        },
        "camera_config": {
            "position": {"x": 0, "y": 3, "z": 8},
            "target": {"x": 0, "y": 1, "z": 0},
            "fov": 50,
        },
        "interaction_config": {
            "offering_table": {"x": 0, "y": 0.8, "z": -2},
            "incense_burner": {"x": 0, "y": 1, "z": -1},
            "photo_altar": {"x": 0, "y": 1.2, "z": -2.5},
        },
    },
    "christian_church": {
        "name": "基督教堂",
        "description": "神圣的教堂环境，彩色玻璃窗和十字架装饰",
        "category": "christian",
        "lighting_config": {
            "ambient": {"color": "#F5F5DC", "intensity": 0.4},
            "directional": {
                "color": "#E6E6FA",
                "intensity": 0.8,
                "position": {"x": 0, "y": 15, "z": 5},
            },
            "stained_glass": {"color": "#9370DB", "intensity": 0.3},
        },
        "camera_config": {
            "position": {"x": 0, "y": 2.5, "z": 10},
            "target": {"x": 0, "y": 1.5, "z": 0},
            "fov": 55,
        },
    },
    "traditional_shrine": {
        "name": "传统祠堂",
        "description": "中式传统祠堂，红木装饰，古朴典雅",
        "category": "traditional",
        "lighting_config": {
            "ambient": {"color": "#FDF5E6", "intensity": 0.3},
            "directional": {
                "color": "#DEB887",
                "intensity": 0.6,
                "position": {"x": 3, "y": 8, "z": 3},
            },
            "lanterns": {"color": "#FF6347", "intensity": 0.5},
        },
        "camera_config": {
            "position": {"x": 0, "y": 2, "z": 6},
            "target": {"x": 0, "y": 1, "z": 0},
            "fov": 60,
        },
    },
}
