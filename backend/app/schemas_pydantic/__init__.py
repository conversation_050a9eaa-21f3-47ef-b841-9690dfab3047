from pydantic import BaseModel  # Import BaseModel directly

from .ai import (
    AIResultResponse,
    PhotoProcessRequest,
    VoiceCloneRequest,
)
from .ancestor import (
    AncestorBase,
    AncestorCreate,
    AncestorResponse,
    AncestorUpdate,
)
from .environment import (
    EnvironmentBase,
    EnvironmentCreate,
    EnvironmentResponse,
    EnvironmentUpdate,
)
from .family import (
    FamilyCreateRequest,
    FamilyInvitationCreateRequest,
    FamilyInvitationResponse,
    FamilyJoinRequest,
    FamilyMemberListResponse,
    FamilyMemberResponse,
    FamilyResponse,
    FamilyUpdateRequest,
    GenealogyNodeCreateRequest,
    GenealogyNodeResponse,
    GenealogyNodeUpdateRequest,
    GenealogyRelationshipCreateRequest,
    GenealogyRelationshipResponse,
    GenealogyResponse,
    MemorialMessageCreateRequest,
    MemorialMessageListResponse,
    MemorialMessageResponse,
    MemorialMessageUpdateRequest,
)
from .memorial_asset import (
    Asset<PERSON>ype<PERSON><PERSON>,
    MemorialAssetBase,
    MemorialAssetCreate,
    MemorialAssetListResponse,  # Added import
    MemorialAssetResponse,
    MemorialAssetUpdate,
)
from .memorial_event import (
    MemorialEventBase,
    MemorialEventCreate,
    MemorialEventListResponse,  # Added import
    MemorialEventResponse,
    MemorialEventUpdate,
)
from .memorial_space import (
    DeceasedGenderEnum,
    MemorialCustomSettings,
    MemorialSpaceBase,
    MemorialSpaceCreate,
    MemorialSpaceListResponse,  # Added import
    MemorialSpaceResponse,
    SceneCustomization,
    MemorialSpaceUpdate,
    PrivacyLevelEnum,
)
from .permission import (
    AccessCheckRequest,
    AccessCheckResponse,
    AccessLogEntry,
    AccessLogListResponse,
    AccessType,
    FamilyAccessRequest,
    PermissionBase,
    PermissionBatchOperation,
    PermissionCreate,
    PermissionResponse,
    PermissionType,
    PermissionUpdate,
    PrivacySettings,
)
from .religious_setting import (
    ReligiousCulturalSettingBase,
    ReligiousCulturalSettingCreate,
    ReligiousCulturalSettingResponse,
    ReligiousCulturalSettingUpdate,
)
from .render import (
    RenderControlRequest,
    RenderControlResponse,
    RenderControlType,
    RenderInitRequest,
    RenderInitResponse,
    RenderStatus,
    RenderStatusResponse,
)
from .token import (
    RefreshTokenRequest,
    Token,
    TokenPayload,
    TokenResponse,
)
from .tribute import (
    ClientInfo,
    TributeBase,
    TributeCreateRequest,
    TributeItem,
    TributeListResponse,
    TributeResponse,
    TributeStatsResponse,
)
from .user import (
    UserBase,
    UserCreate,
    UserInDBBase,
    UserResponse,
    UserUpdate,
    UserUpdateBase,
)

# Re-export BaseModel so it's available as schemas.BaseModel
__all__ = [
    "BaseModel",
    # User
    "UserBase",
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserInDBBase",
    "UserUpdateBase",
    # Ancestor
    "AncestorBase",
    "AncestorCreate",
    "AncestorUpdate",
    "AncestorResponse",
    # Environment
    "EnvironmentBase",
    "EnvironmentCreate",
    "EnvironmentUpdate",
    "EnvironmentResponse",
    # Render
    "RenderStatus",
    "RenderInitRequest",
    "RenderControlType",
    "RenderControlRequest",
    "RenderStatusResponse",
    "RenderInitResponse",
    "RenderControlResponse",
    # AI
    "AIResultResponse",
    "PhotoProcessRequest",
    "VoiceCloneRequest",
    # Family/Tribute
    "FamilyBase",
    "FamilyCreateRequest",
    "FamilyResponse",
    "FamilyUpdateRequest",
    "FamilyInvitationBase",
    "FamilyInvitationCreateRequest",
    "FamilyInvitationResponse",
    "FamilyJoinRequest",
    "FamilyMemberBase",
    "FamilyMemberCreateRequest",
    "FamilyMemberResponse",
    "FamilyMemberListResponse",
    "TributeBase",
    "TributeCreateRequest",
    "TributeResponse",
    "TributeListResponse",
    "TributeStatsResponse",
    # Memorial Message
    "MemorialMessageBase",
    "MemorialMessageCreateRequest",
    "MemorialMessageResponse",
    "MemorialMessageListResponse",
    "MemorialMessageUpdateRequest",
    # Genealogy
    "GenealogyBase",
    "GenealogyCreateRequest",
    "GenealogyResponse",
    "GenealogyNodeBase",
    "GenealogyNodeCreateRequest",
    "GenealogyNodeResponse",
    "GenealogyNodeUpdateRequest",
    "GenealogyRelationshipBase",
    "GenealogyRelationshipCreateRequest",
    "GenealogyRelationshipResponse",
    # Religious Setting
    "ReligiousCulturalSettingBase",
    "ReligiousCulturalSettingCreate",
    "ReligiousCulturalSettingUpdate",
    "ReligiousCulturalSettingResponse",
    # Memorial Space
    "DeceasedGenderEnum",
    "PrivacyLevelEnum",
    "MemorialSpaceBase",
    "MemorialSpaceCreate",
    "MemorialSpaceUpdate",
    "MemorialSpaceResponse",
    "MemorialSpaceListResponse",  # Added to __all__
    # Memorial Asset
    "AssetTypeEnum",
    "MemorialAssetBase",
    "MemorialAssetCreate",
    "MemorialAssetUpdate",
    "MemorialAssetResponse",
    "MemorialAssetListResponse",  # Added to __all__
    # Memorial Event
    "MemorialEventBase",
    "MemorialEventCreate",
    "MemorialEventUpdate",
    "MemorialEventResponse",
    "MemorialEventListResponse",  # Added to __all__
    # Token
    "Token",
    "TokenPayload",
    "TokenResponse",
    "RefreshTokenRequest",
]
