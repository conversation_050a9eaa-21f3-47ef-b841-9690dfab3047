import uuid

from pydantic import BaseModel


class Token(BaseModel):
    access_token: str
    token_type: str


class TokenPayload(BaseModel):
    sub: uuid.UUID | None = None  # Subject (user ID)
    type: str | None = None  # Could be 'access' or 'refresh'


class UserInfo(BaseModel):
    """用户基本信息"""

    id: str
    username: str
    email: str
    full_name: str | None = None
    is_email_verified: bool = False
    is_active: bool = True
    role: str = "user"


class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str | None = None
    token_type: str = "bearer"
    expires_in: int  # Expiration time in seconds for the access_token
    user: UserInfo | None = None  # 包含用户信息


class RefreshTokenRequest(BaseModel):
    refresh_token: str
