import uuid
from datetime import datetime

from pydantic import BaseModel, EmailStr, Field, HttpUrl


# --- User Schemas ---
class UserBase(BaseModel):
    email: EmailStr = Field(..., example="<EMAIL>")
    username: str = Field(..., min_length=3, max_length=50, example="john_doe")
    full_name: str | None = Field(None, max_length=100, example="John Doe")
    # phone: str | None = Field(None, max_length=20, example="+1234567890")  # 数据库中不存在此字段
    is_active: bool | None = True
    is_verified: bool | None = False
    role: str = "user"  # 将类型从 str | None 改为 str，因为我们总是设置默认值


class UserCreate(UserBase):
    password: str = Field(..., min_length=8, example="strongpassword123")
    is_superuser: bool = False  # Added for superuser creation


# 创建一个新的基类，不继承 UserBase，避免类型冲突
class UserUpdateBase(BaseModel):
    email: EmailStr | None = Field(None, example="<EMAIL>")
    username: str | None = Field(None, min_length=3, max_length=50, example="john_doe")
    password: str | None = Field(None, min_length=8, example="newstrongpassword123")
    full_name: str | None = Field(None, max_length=100, example="John Doe")
    # phone: str | None = Field(None, max_length=20, example="+1234567890")  # 数据库中不存在此字段
    avatar_url: HttpUrl | None = Field(None, example="http://example.com/avatar.png")
    bio: str | None = Field(None, example="I am a software engineer.")
    is_active: bool | None = None
    is_verified: bool | None = None
    role: str | None = Field(None, example="user")  # e.g., 'user', 'admin', 'editor'


class UserUpdate(UserUpdateBase):
    """User update schema"""

    pass


class UserResponse(UserBase):
    id: uuid.UUID
    avatar_url: HttpUrl | None = None
    bio: str | None = None
    is_active: bool = True
    is_verified: bool = False
    is_superuser: bool = False  # Added for superuser info
    role: str = "user"
    created_at: datetime
    updated_at: datetime
    last_login_at: datetime | None = None

    class Config:
        from_attributes = True


# --- Authentication Schemas ---
class LoginRequest(BaseModel):
    login_identifier: str = Field(
        ..., example="<EMAIL>"
    )  # Can be email or username
    password: str = Field(..., min_length=1, example="password123")


# Alias for backward compatibility
UserRead = UserResponse


class PasswordChange(BaseModel):
    """密码修改请求"""

    current_password: str = Field(..., min_length=1, example="oldpassword123")
    new_password: str = Field(..., min_length=8, example="newstrongpassword123")


class UserInDBBase(UserBase):
    id: uuid.UUID
    password_hash: str
    is_active: bool = True
    is_verified: bool = False
    is_superuser: bool = False
    role: str = "user"
    created_at: datetime
    updated_at: datetime
    last_login_at: datetime | None = None

    class Config:
        from_attributes = True
