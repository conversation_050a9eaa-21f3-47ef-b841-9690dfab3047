from datetime import date, datetime
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field


# --- Family Schemas ---
class FamilyBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="家族名称")
    description: str | None = Field(None, description="家族描述")
    family_motto: str | None = Field(None, max_length=500, description="家族座右铭")
    origin_location: str | None = Field(None, max_length=255, description="家族起源地")
    established_date: date | None = Field(None, description="家族成立日期")
    privacy_level: str = Field("private", description="隐私级别: public, private")


class FamilyCreateRequest(FamilyBase):
    pass


class FamilyUpdateRequest(BaseModel):
    name: str | None = Field(None, min_length=1, max_length=255, description="家族名称")
    description: str | None = Field(None, description="家族描述")
    family_motto: str | None = Field(None, max_length=500, description="家族座右铭")
    origin_location: str | None = Field(None, max_length=255, description="家族起源地")
    established_date: date | None = Field(None, description="家族成立日期")
    privacy_level: str | None = Field(None, description="隐私级别: public, private")


class FamilyResponse(FamilyBase):
    id: UUID
    creator_id: UUID
    member_count: int = Field(..., description="家族成员数量")
    created_at: datetime
    updated_at: datetime
    creator_name: str = Field(..., description="创建者姓名")

    class Config:
        from_attributes = True


# --- Family Member Schemas ---
class FamilyMemberBase(BaseModel):
    role: str = Field("member", description="角色: member, admin, moderator")


class FamilyJoinRequest(BaseModel):
    invitation_code: str | None = Field(None, description="邀请码")
    role: str | None = Field("member", description="申请的角色")
    message: str | None = Field(None, description="申请留言")


class FamilyMemberResponse(BaseModel):
    id: UUID
    family_id: UUID
    user_id: UUID
    role: str
    joined_at: datetime
    user_name: str = Field(..., description="用户姓名")
    user_avatar: str | None = Field(None, description="用户头像")

    class Config:
        from_attributes = True


class FamilyMemberListResponse(BaseModel):
    items: list[FamilyMemberResponse]
    total: int
    skip: int
    limit: int


# --- Family Invitation Schemas ---
class FamilyInvitationCreateRequest(BaseModel):
    invitee_email: EmailStr = Field(..., description="被邀请人邮箱")
    message: str | None = Field(None, description="邀请留言")
    expires_in_days: int = Field(7, ge=1, le=30, description="邀请有效期（天数）")


class FamilyInvitationResponse(BaseModel):
    id: UUID
    family_id: UUID
    inviter_id: UUID
    invitee_email: str
    message: str | None
    status: str = Field(..., description="状态: pending, accepted, rejected, expired")
    created_at: datetime
    expires_at: datetime
    family_name: str = Field(..., description="家族名称")
    inviter_name: str = Field(..., description="邀请人姓名")

    class Config:
        from_attributes = True


# --- Genealogy Schemas ---
class GenealogyNodeBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=255, description="姓名")
    gender: str | None = Field(None, description="性别: male, female, other")
    birth_date: date | None = Field(None, description="出生日期")
    death_date: date | None = Field(None, description="逝世日期")
    biography: str | None = Field(None, description="生平简介")
    photo_url: str | None = Field(None, description="照片URL")
    generation: int | None = Field(None, description="世代数")
    position_x: float | None = Field(None, description="族谱图中的X坐标")
    position_y: float | None = Field(None, description="族谱图中的Y坐标")


class GenealogyNodeCreateRequest(GenealogyNodeBase):
    pass


class GenealogyNodeUpdateRequest(BaseModel):
    name: str | None = Field(None, min_length=1, max_length=255, description="姓名")
    gender: str | None = Field(None, description="性别: male, female, other")
    birth_date: date | None = Field(None, description="出生日期")
    death_date: date | None = Field(None, description="逝世日期")
    biography: str | None = Field(None, description="生平简介")
    photo_url: str | None = Field(None, description="照片URL")
    generation: int | None = Field(None, description="世代数")
    position_x: float | None = Field(None, description="族谱图中的X坐标")
    position_y: float | None = Field(None, description="族谱图中的Y坐标")


class GenealogyNodeResponse(GenealogyNodeBase):
    id: UUID
    family_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class GenealogyRelationshipBase(BaseModel):
    from_node_id: UUID = Field(..., description="关系起始节点ID")
    to_node_id: UUID = Field(..., description="关系目标节点ID")
    relationship_type: str = Field(
        ..., description="关系类型: parent, child, spouse, sibling"
    )


class GenealogyRelationshipCreateRequest(GenealogyRelationshipBase):
    pass


class GenealogyRelationshipResponse(GenealogyRelationshipBase):
    id: UUID
    family_id: UUID
    created_at: datetime

    class Config:
        from_attributes = True


class GenealogyResponse(BaseModel):
    family_id: UUID
    nodes: list[GenealogyNodeResponse]
    relationships: list[GenealogyRelationshipResponse]


# --- Tribute Schemas ---
class TributeBase(BaseModel):
    tribute_type: str = Field(..., description="祭拜类型: incense, flower, candle, bow")
    message: str | None = Field(None, description="祭拜留言")
    is_anonymous: bool = Field(False, description="是否匿名")


class TributeCreateRequest(TributeBase):
    pass


class TributeResponse(TributeBase):
    id: UUID
    memorial_space_id: UUID
    user_id: UUID
    created_at: datetime
    user_name: str | None = Field(None, description="用户姓名（非匿名时显示）")

    class Config:
        from_attributes = True


class TributeListResponse(BaseModel):
    items: list[TributeResponse]
    total: int
    skip: int
    limit: int


class TributeStatsResponse(BaseModel):
    memorial_space_id: UUID
    total_tributes: int = Field(..., description="总祭拜次数")
    tribute_types: dict = Field(..., description="各类型祭拜统计")
    recent_tributes: list[TributeResponse] = Field(..., description="最近的祭拜记录")


# --- Memorial Message Schemas ---
class MemorialMessageBase(BaseModel):
    content: str = Field(..., min_length=1, description="留言内容")
    is_anonymous: bool = Field(False, description="是否匿名")


class MemorialMessageCreateRequest(MemorialMessageBase):
    pass


class MemorialMessageUpdateRequest(BaseModel):
    content: str | None = Field(None, min_length=1, description="留言内容")


class MemorialMessageResponse(MemorialMessageBase):
    id: UUID
    memorial_space_id: UUID
    author_id: UUID
    created_at: datetime
    updated_at: datetime
    author_name: str | None = Field(None, description="作者姓名（非匿名时显示）")
    author_avatar: str | None = Field(None, description="作者头像（非匿名时显示）")

    class Config:
        from_attributes = True


class MemorialMessageListResponse(BaseModel):
    items: list[MemorialMessageResponse]
    total: int
    skip: int
    limit: int
