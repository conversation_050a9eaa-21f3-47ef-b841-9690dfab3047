from typing import Any

from pydantic import BaseModel, Field, HttpUrl


class AIResultResponse(BaseModel):
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="消息")
    original_url: HttpUrl | None = Field(None, description="原始文件 URL")
    result_url: HttpUrl | None = Field(None, description="结果文件 URL")
    result_data: Any | None = Field(
        None, description="额外结果数据，例如3D模型信息"
    )  # For create-3d-model


class PhotoProcessRequest(BaseModel):
    description: str | None = Field(None, description="描述（可选）")


class VoiceCloneRequest(BaseModel):
    text: str = Field(..., description="要生成的文本")
    language: str = Field("zh", description="语言代码")
