import uuid
from datetime import date, datetime  # Added datetime
from enum import Enum
from typing import Any

from pydantic import BaseModel, ConfigDict, Field, HttpUrl  # Added ConfigDict


# 自定义设置结构化定义
class SceneCustomization(BaseModel):
    """场景个性化配置"""

    lighting_intensity: float | None = Field(None, ge=0, le=2, description="光照强度 (0-2)")
    ambient_color: str | None = Field(
        None, pattern=r"^#[0-9A-Fa-f]{6}$", description="环境光颜色 (Hex)"
    )
    background_music_volume: float | None = Field(
        None, ge=0, le=1, description="背景音乐音量 (0-1)"
    )
    interaction_hints_enabled: bool | None = Field(None, description="是否显示交互提示")
    camera_auto_rotate: bool | None = Field(None, description="是否启用相机自动旋转")
    texture_quality: str | None = Field(
        None, pattern=r"^(low|medium|high)$", description="贴图质量"
    )


class MemorialCustomSettings(BaseModel):
    """纪念空间自定义设置"""

    scene_customization: SceneCustomization | None = Field(None, description="场景个性化配置")
    display_preferences: dict[str, Any] | None = Field(None, description="显示偏好设置")
    notification_settings: dict[str, Any] | None = Field(None, description="通知设置")
    accessibility_options: dict[str, Any] | None = Field(None, description="无障碍选项")
    memorial_theme: str | None = Field(None, description="纪念主题风格")
    custom_css: str | None = Field(None, max_length=10000, description="自定义CSS样式")


class DeceasedGenderEnum(str, Enum):
    male = "male"
    female = "female"
    other = "other"
    unknown = "unknown"


class PrivacyLevelEnum(str, Enum):
    public = "public"
    private = "private"
    password = "password"
    family = "family"


class MemorialSpaceBase(BaseModel):
    deceased_name: str = Field(..., max_length=100, description="逝者姓名")
    deceased_gender: DeceasedGenderEnum | None = Field(None, description="逝者性别")
    birth_date: date | None = Field(None, description="出生日期")
    death_date: date | None = Field(None, description="逝世日期")
    creator_relationship_to_deceased: str | None = Field(
        None, max_length=50, description="创建者与逝者的关系"
    )
    bio: str | None = Field(None, description="逝者生平")
    scene_id: uuid.UUID | None = Field(None, description="场景ID (可选)")
    music_url: HttpUrl | None = Field(None, description="背景音乐URL")
    privacy_level: PrivacyLevelEnum = Field(
        PrivacyLevelEnum.private, description="隐私级别"
    )
    access_password: str | None = Field(
        None, max_length=255, description="访问密码 (当隐私级别为password时)"
    )
    custom_settings: MemorialCustomSettings | None = Field(None, description="自定义设置")
    cover_image_url: HttpUrl | None = Field(None, description="封面图片URL")


class MemorialSpaceCreate(MemorialSpaceBase):
    # creator_id is usually set by the system/logged-in user, not part of create schema directly from client
    pass


class MemorialSpaceUpdate(BaseModel):
    deceased_name: str | None = Field(None, max_length=100, description="逝者姓名")
    deceased_gender: DeceasedGenderEnum | None = Field(None, description="逝者性别")
    birth_date: date | None = Field(None, description="出生日期")
    death_date: date | None = Field(None, description="逝世日期")
    creator_relationship_to_deceased: str | None = Field(
        None, max_length=50, description="创建者与逝者的关系"
    )
    bio: str | None = Field(None, description="逝者生平")
    scene_id: uuid.UUID | None = Field(None, description="场景ID (可选)")
    music_url: HttpUrl | None = Field(None, description="背景音乐URL")
    privacy_level: PrivacyLevelEnum | None = Field(None, description="隐私级别")
    access_password: str | None = Field(
        None, max_length=255, description="访问密码 (当隐私级别为password时, 清空密码请传空字符串)"
    )
    custom_settings: MemorialCustomSettings | None = Field(None, description="自定义设置")
    cover_image_url: HttpUrl | None = Field(None, description="封面图片URL")
    is_active: bool | None = Field(None, description="是否激活")


class MemorialSpaceResponse(MemorialSpaceBase):
    id: uuid.UUID
    creator_id: uuid.UUID
    visit_count: int
    created_at: datetime  # Changed from date to datetime
    updated_at: datetime  # Changed from date to datetime
    is_active: bool

    model_config = ConfigDict(from_attributes=True)


class MemorialSpaceListResponse(BaseModel):
    spaces: list[MemorialSpaceResponse]
    total_count: int

    model_config = ConfigDict(from_attributes=True)
