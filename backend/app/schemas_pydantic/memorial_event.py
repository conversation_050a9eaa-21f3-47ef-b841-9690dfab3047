import uuid
from datetime import datetime  # Changed from date to datetime for consistency

from pydantic import BaseModel, ConfigDict, Field  # Added ConfigDict


class MemorialEventBase(BaseModel):
    event_date: str = Field(
        ..., description="事件日期 (YYYY-MM-DD 或其他可描述格式)"
    )  # Kept as string as per DB model
    title: str = Field(..., max_length=255, description="事件标题")
    description: str | None = Field(None, description="事件描述")


class MemorialEventCreate(MemorialEventBase):
    # memorial_space_id is usually set by the system/context
    pass


class MemorialEventUpdate(BaseModel):
    event_date: str | None = Field(None, description="事件日期")
    title: str | None = Field(None, max_length=255, description="事件标题")
    description: str | None = Field(None, description="事件描述")


class MemorialEventResponse(MemorialEventBase):
    id: uuid.UUID
    memorial_space_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class MemorialEventListResponse(BaseModel):
    events: list[MemorialEventResponse]
    total_count: int

    model_config = ConfigDict(from_attributes=True)
