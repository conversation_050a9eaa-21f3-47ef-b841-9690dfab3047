import uuid
from datetime import datetime

from sqlalchemy import and_, func, or_
from sqlalchemy.orm import Session

from . import models_sqlalchemy as models
from . import schemas_pydantic as schemas
from .crud import CRUDBase


# --- Family CRUD ---
class CRUDFamily(
    CRUDBase[models.Family, schemas.FamilyCreateRequest, schemas.FamilyUpdateRequest]
):
    def create_with_creator(
        self, db: Session, *, obj_in: schemas.FamilyCreateRequest, creator_id: uuid.UUID
    ) -> models.Family:
        obj_in_data = obj_in.dict()
        db_obj = self.model(**obj_in_data, creator_id=creator_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)

        # Create family member record for creator
        family_member = models.FamilyMember(
            family_id=db_obj.id, user_id=creator_id, role="admin", status="active"
        )
        db.add(family_member)
        db.commit()

        return db_obj

    def get_by_creator(
        self, db: Session, *, creator_id: uuid.UUID
    ) -> list[models.Family]:
        return db.query(self.model).filter(models.Family.creator_id == creator_id).all()

    def get_user_families(
        self, db: Session, *, user_id: uuid.UUID
    ) -> list[models.Family]:
        return (
            db.query(self.model)
            .join(models.FamilyMember)
            .filter(
                and_(
                    models.FamilyMember.user_id == user_id,
                    models.FamilyMember.status == "active",
                )
            )
            .all()
        )


# --- FamilyMember CRUD ---
class CRUDFamilyMember(
    CRUDBase[
        models.FamilyMember, schemas.FamilyMemberResponse, schemas.FamilyMemberResponse
    ]
):
    def get_by_family(
        self, db: Session, *, family_id: uuid.UUID
    ) -> list[models.FamilyMember]:
        return (
            db.query(self.model)
            .filter(models.FamilyMember.family_id == family_id)
            .all()
        )

    def get_by_user_and_family(
        self, db: Session, *, user_id: uuid.UUID, family_id: uuid.UUID
    ) -> models.FamilyMember | None:
        return (
            db.query(self.model)
            .filter(
                and_(
                    models.FamilyMember.user_id == user_id,
                    models.FamilyMember.family_id == family_id,
                )
            )
            .first()
        )

    def is_family_admin(
        self, db: Session, *, user_id: uuid.UUID, family_id: uuid.UUID
    ) -> bool:
        member = self.get_by_user_and_family(db, user_id=user_id, family_id=family_id)
        if member is None:
            return False
        return bool(member.role == "admin" and member.status == "active")

    def is_family_member(
        self, db: Session, *, user_id: uuid.UUID, family_id: uuid.UUID
    ) -> bool:
        member = self.get_by_user_and_family(db, user_id=user_id, family_id=family_id)
        return member is not None and member.status == "active"


# --- FamilyInvitation CRUD ---
class CRUDFamilyInvitation(
    CRUDBase[
        models.FamilyInvitation,
        schemas.FamilyInvitationCreateRequest,
        schemas.FamilyInvitationResponse,
    ]
):
    def create_invitation(
        self,
        db: Session,
        *,
        obj_in: schemas.FamilyInvitationCreateRequest,
        family_id: uuid.UUID,
        inviter_id: uuid.UUID,
    ) -> models.FamilyInvitation:
        obj_in_data = obj_in.dict()
        db_obj = self.model(
            **obj_in_data, family_id=family_id, inviter_id=inviter_id, status="pending"
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_code(
        self, db: Session, *, invitation_code: str
    ) -> models.FamilyInvitation | None:
        return (
            db.query(self.model)
            .filter(models.FamilyInvitation.invitation_code == invitation_code)
            .first()
        )

    def get_by_family(
        self, db: Session, *, family_id: uuid.UUID
    ) -> list[models.FamilyInvitation]:
        return (
            db.query(self.model)
            .filter(models.FamilyInvitation.family_id == family_id)
            .order_by(models.FamilyInvitation.created_at.desc())
            .all()
        )


# --- GenealogyNode CRUD ---
class CRUDGenealogyNode(
    CRUDBase[
        models.GenealogyNode,
        schemas.GenealogyNodeCreateRequest,
        schemas.GenealogyNodeUpdateRequest,
    ]
):
    def create_for_family(
        self,
        db: Session,
        *,
        obj_in: schemas.GenealogyNodeCreateRequest,
        family_id: uuid.UUID,
    ) -> models.GenealogyNode:
        obj_in_data = obj_in.dict()
        db_obj = self.model(**obj_in_data, family_id=family_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_family(
        self, db: Session, *, family_id: uuid.UUID
    ) -> list[models.GenealogyNode]:
        return (
            db.query(self.model)
            .filter(models.GenealogyNode.family_id == family_id)
            .all()
        )


# --- GenealogyRelationship CRUD ---
class CRUDGenealogyRelationship(
    CRUDBase[
        models.GenealogyRelationship,
        schemas.GenealogyRelationshipCreateRequest,
        schemas.GenealogyRelationshipResponse,
    ]
):
    def create_relationship(
        self, db: Session, *, obj_in: schemas.GenealogyRelationshipCreateRequest
    ) -> models.GenealogyRelationship:
        obj_in_data = obj_in.dict()
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_node(
        self, db: Session, *, node_id: uuid.UUID
    ) -> list[models.GenealogyRelationship]:
        return (
            db.query(self.model)
            .filter(
                or_(
                    models.GenealogyRelationship.parent_node_id == node_id,
                    models.GenealogyRelationship.child_node_id == node_id,
                )
            )
            .all()
        )


# --- TributeRecord CRUD ---
class CRUDTributeRecord(
    CRUDBase[
        models.TributeRecord, schemas.TributeCreateRequest, schemas.TributeResponse
    ]
):
    def create_tribute(
        self,
        db: Session,
        *,
        obj_in: schemas.TributeCreateRequest,
        user_id: uuid.UUID,
        memorial_space_id: uuid.UUID,
    ) -> models.TributeRecord:
        obj_in_data = obj_in.dict()
        db_obj = self.model(
            **obj_in_data, user_id=user_id, memorial_space_id=memorial_space_id
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_memorial_space(
        self,
        db: Session,
        *,
        memorial_space_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
    ) -> list[models.TributeRecord]:
        return (
            db.query(self.model)
            .filter(models.TributeRecord.memorial_space_id == memorial_space_id)
            .order_by(models.TributeRecord.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_stats_by_memorial_space(
        self, db: Session, *, memorial_space_id: uuid.UUID
    ) -> dict:
        total_tributes = (
            db.query(func.count(self.model.id))
            .filter(models.TributeRecord.memorial_space_id == memorial_space_id)
            .scalar()
            or 0
        )

        unique_visitors = (
            db.query(func.count(func.distinct(self.model.user_id)))
            .filter(models.TributeRecord.memorial_space_id == memorial_space_id)
            .scalar()
            or 0
        )

        recent_tributes = (
            db.query(func.count(self.model.id))
            .filter(
                and_(
                    models.TributeRecord.memorial_space_id == memorial_space_id,
                    models.TributeRecord.created_at >= datetime.now().replace(day=1),
                )
            )
            .scalar()
            or 0
        )

        return {
            "total_tributes": total_tributes,
            "unique_visitors": unique_visitors,
            "recent_tributes": recent_tributes,
        }


# --- MemorialMessage CRUD ---
class CRUDMemorialMessage(
    CRUDBase[
        models.MemorialMessage,
        schemas.MemorialMessageCreateRequest,
        schemas.MemorialMessageUpdateRequest,
    ]
):
    def create_message(
        self,
        db: Session,
        *,
        obj_in: schemas.MemorialMessageCreateRequest,
        author_id: uuid.UUID,
        memorial_space_id: uuid.UUID,
    ) -> models.MemorialMessage:
        obj_in_data = obj_in.dict()
        db_obj = self.model(
            **obj_in_data, author_id=author_id, memorial_space_id=memorial_space_id
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_memorial_space(
        self,
        db: Session,
        *,
        memorial_space_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
    ) -> list[models.MemorialMessage]:
        return (
            db.query(self.model)
            .filter(models.MemorialMessage.memorial_space_id == memorial_space_id)
            .order_by(models.MemorialMessage.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_author(
        self, db: Session, *, author_id: uuid.UUID, skip: int = 0, limit: int = 100
    ) -> list[models.MemorialMessage]:
        return (
            db.query(self.model)
            .filter(models.MemorialMessage.author_id == author_id)
            .order_by(models.MemorialMessage.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def count_by_memorial_space(
        self, db: Session, *, memorial_space_id: uuid.UUID
    ) -> int:
        return (
            db.query(func.count(self.model.id))
            .filter(models.MemorialMessage.memorial_space_id == memorial_space_id)
            .scalar()
            or 0
        )


# Create instances
family = CRUDFamily(models.Family)
family_member = CRUDFamilyMember(models.FamilyMember)
family_invitation = CRUDFamilyInvitation(models.FamilyInvitation)
genealogy_node = CRUDGenealogyNode(models.GenealogyNode)
genealogy_relationship = CRUDGenealogyRelationship(models.GenealogyRelationship)
tribute_record = CRUDTributeRecord(models.TributeRecord)
memorial_message = CRUDMemorialMessage(models.MemorialMessage)
