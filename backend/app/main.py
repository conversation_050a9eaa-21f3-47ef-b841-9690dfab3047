from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api_v1_routers import (
    ai,
    ai_enhanced,
    analytics,
    auth,
    db_admin,
    families,
    memorial_assets,
    memorial_events,
    memorial_spaces,
    membership,
    messages,
    permissions,
    render,
    scenes,
    tributes,
    users,
)
from app.core.config import settings

# Import other routers as they are created
# from app.api_v1_routers import users as users_router # Example for a user management router

app = FastAPI(
    title=settings.PROJECT_NAME, openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# Set all CORS enabled origins
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Include routers
app.include_router(auth.router, prefix="/api/v1/auth", tags=["auth"])
app.include_router(users.router, prefix="/api/v1/users", tags=["users"])
app.include_router(families.router, prefix="/api/v1/families", tags=["families"])
app.include_router(
    memorial_spaces.router, prefix="/api/v1/memorial-spaces", tags=["memorial-spaces"]
)
app.include_router(
    memorial_events.router,
    prefix="/api/v1/memorial-spaces/{space_id}/events",
    tags=["memorial-events"],
)
app.include_router(
    memorial_assets.router,
    prefix="/api/v1/memorial-spaces/{space_id}/assets",
    tags=["memorial-assets"],
)
app.include_router(
    tributes.router,
    prefix="/api/v1/memorial-spaces/{space_id}/tributes",
    tags=["tributes"],
)
app.include_router(
    messages.router,
    prefix="/api/v1/memorial-spaces/{space_id}/messages",
    tags=["messages"],
)
app.include_router(scenes.router, prefix="/api/v1/scenes", tags=["scenes"])
app.include_router(ai.router, prefix="/api/v1/ai", tags=["ai"])
app.include_router(
    ai_enhanced.router, prefix="/api/v1/ai-enhanced", tags=["ai-enhanced"]
)
app.include_router(
    permissions.router, prefix="/api/v1/permissions", tags=["permissions"]
)
app.include_router(render.router, prefix="/api/v1/render", tags=["render"])
app.include_router(membership.router, prefix="/api/v1/membership", tags=["membership"])
app.include_router(analytics.router, prefix="/api/v1/analytics", tags=["analytics"])
app.include_router(db_admin.router, prefix="/api/v1/db-admin", tags=["database-admin"])

# Add other top-level routers here if needed, e.g., for users, admin, etc.
# app.include_router(users_router.router, prefix=f"{settings.API_V1_STR}/users", tags=["users"])


@app.get("/", tags=["root"])
async def read_root():
    return {"message": "Welcome to the Memorial API"}


@app.get("/health", tags=["health"])
def health_check():
    """简单的健康检查端点"""
    return {"status": "ok", "message": "Memorial API is running"}


@app.get(f"{settings.API_V1_STR}/healthcheck", tags=["healthcheck"])
def healthcheck():
    return {"status": "ok"}


# For development with uvicorn, you might run this file directly (though typically not for production)
# if __name__ == "__main__":
#     import uvicorn
#     uvicorn.run(app, host="0.0.0.0", port=8000)
