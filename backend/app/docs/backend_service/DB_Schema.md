# 数据库设计说明

本文档描述了归处忆后端服务的数据库设计。

## 1. 数据库类型

*   **数据库**: PostgreSQL

## 2. 核心数据表结构

以下是核心数据表的结构定义。

### 2.1. `users` 表

存储用户信息。

| 列名             | 数据类型        | 约束                                   | 描述                     |
| ---------------- | --------------- | -------------------------------------- | ------------------------ |
| `id`             | UUID            | Primary Key, Default: uuid_generate_v4() | 用户唯一标识符           |
| `username`       | VARCHAR(50)     | Unique, Not Null, Index                | 用户名                   |
| `email`          | VARCHAR(100)    | Unique, Not Null, Index                | 用户邮箱                 |
| `password_hash`  | VARCHAR(255)    | Not Null                               | 哈希后的用户密码         |
| `full_name`      | VARCHAR(100)    | Nullable                               | 用户全名                 |
| `avatar_url`     | VARCHAR(255)    | Nullable                               | 用户头像 URL             |
| `bio`            | TEXT            | Nullable                               | 用户简介                 |
| `is_active`      | BOOLEAN         | Not Null, Default: True                | 用户是否激活             |
| `is_verified`    | BOOLEAN         | Not Null, Default: False               | 用户邮箱是否已验证       |
| `created_at`     | TIMESTAMP       | Not Null, Default: CURRENT_TIMESTAMP   | 创建时间                 |
| `updated_at`     | TIMESTAMP       | Not Null, Default: CURRENT_TIMESTAMP   | 更新时间 (自动更新)      |
| `last_login_at`  | TIMESTAMP       | Nullable                               | 最后登录时间             |
| `role`           | VARCHAR(20)     | Not Null, Default: 'user'              | 用户角色 (e.g., user, admin) |

### 2.2. `memorial_spaces` 表

存储纪念空间信息。

| 列名                | 数据类型        | 约束                                     | 描述                         |
| ------------------- | --------------- | ---------------------------------------- | ---------------------------- |
| `id`                | UUID            | Primary Key, Default: uuid_generate_v4(), Index | 纪念空间唯一标识符           |
| `creator_id`        | UUID            | Foreign Key (users.id), Not Null, Index  | 创建者用户 ID                |
| `deceased_name`     | VARCHAR(100)    | Not Null, Index                          | 逝者姓名                     |
| `deceased_gender`   | EnumSQL         | Nullable (male, female, other, unknown)  | 逝者性别                     |
| `birth_date`        | DATE            | Nullable                                 | 逝者出生日期                 |
| `death_date`        | DATE            | Nullable                                 | 逝者逝世日期                 |
| `relationship`      | VARCHAR(50)     | Nullable                                 | 与创建者的关系               |
| `bio`               | TEXT            | Nullable                                 | 逝者生平简介                 |
| `scene_id`          | UUID            | Nullable, Index                          | 场景 ID (关联场景表)         |
| `music_url`         | VARCHAR(255)    | Nullable                                 | 背景音乐 URL                 |
| `privacy_level`     | EnumSQL         | Not Null, Default: 'private', Index      | 隐私设置 (public, private, password, family) |
| `access_password`   | VARCHAR(255)    | Nullable                                 | 访问密码 (如果 privacy_level 为 'password') |
| `visit_count`       | INTEGER         | Not Null, Default: 0                     | 访问次数                     |
| `created_at`        | TIMESTAMP       | Not Null, Default: CURRENT_TIMESTAMP, Index | 创建时间                     |
| `updated_at`        | TIMESTAMP       | Not Null, Default: CURRENT_TIMESTAMP     | 更新时间 (自动更新)          |
| `is_active`         | BOOLEAN         | Not Null, Default: True                  | 是否激活                     |
| `custom_settings`   | JSON            | Nullable                                 | 自定义设置 (JSON格式)        |
| `cover_image_url`   | VARCHAR(255)    | Nullable                                 | 封面图片 URL                 |

### 2.3. `memorial_assets` 表

存储纪念空间中的资源文件（图片、视频、音频等）。

| 列名                | 数据类型        | 约束                                     | 描述                         |
| ------------------- | --------------- | ---------------------------------------- | ---------------------------- |
| `id`                | UUID            | Primary Key, Default: uuid_generate_v4(), Index | 资源唯一标识符               |
| `space_id`          | UUID            | Foreign Key (memorial_spaces.id), Not Null, Index | 所属纪念空间 ID              |
| `uploader_id`       | UUID            | Foreign Key (users.id), Not Null, Index  | 上传者用户 ID                |
| `asset_type`        | EnumSQL         | Not Null, Index                          | 资源类型 (image, video, audio, document, other, cover_image, life_photo) |
| `title`             | VARCHAR(255)    | Nullable                                 | 资源标题                     |
| `description`       | TEXT            | Nullable                                 | 资源描述                     |
| `file_url`          | VARCHAR(255)    | Not Null                                 | 文件存储 URL                 |
| `thumbnail_url`     | VARCHAR(255)    | Nullable                                 | 缩略图 URL                   |
| `original_filename` | VARCHAR(255)    | Nullable                                 | 原始文件名                   |
| `file_size`         | INTEGER         | Nullable                                 | 文件大小 (bytes)             |
| `metadata`          | JSON            | Nullable                                 | 元数据 (如图片尺寸、视频时长) |
| `display_order`     | INTEGER         | Default: 0                               | 显示顺序                     |
| `created_at`        | TIMESTAMP       | Not Null, Default: CURRENT_TIMESTAMP, Index | 创建时间                     |
| `updated_at`        | TIMESTAMP       | Not Null, Default: CURRENT_TIMESTAMP     | 更新时间 (自动更新)          |
| `is_ai_enhanced`    | BOOLEAN         | Default: False                           | 是否经过 AI 增强处理         |

### 2.4. `memorial_events` 表

存储纪念空间中的生平事件（时间轴）。

| 列名                | 数据类型        | 约束                                     | 描述                         |
| ------------------- | --------------- | ---------------------------------------- | ---------------------------- |
| `id`                | UUID            | Primary Key, Default: uuid_generate_v4(), Index | 事件唯一标识符               |
| `memorial_space_id` | UUID            | Foreign Key (memorial_spaces.id), Not Null, Index | 所属纪念空间 ID              |
| `event_date`        | VARCHAR(50)     | Not Null                                 | 事件发生日期/年份 (字符串格式) |
| `title`             | VARCHAR(255)    | Not Null                                 | 事件标题                     |
| `description`       | TEXT            | Nullable                                 | 事件描述                     |
| `created_at`        | TIMESTAMP       | Not Null, Default: CURRENT_TIMESTAMP     | 创建时间                     |
| `updated_at`        | TIMESTAMP       | Not Null, Default: CURRENT_TIMESTAMP     | 更新时间 (自动更新)          |

## 3. (可选) 实体关系图 (ERD)

```mermaid
erDiagram
    users {
        UUID id PK
        VARCHAR username
        VARCHAR email
        VARCHAR password_hash
        VARCHAR full_name
        VARCHAR avatar_url
        TEXT bio
        BOOLEAN is_active
        BOOLEAN is_verified
        TIMESTAMP created_at
        TIMESTAMP updated_at
        TIMESTAMP last_login_at
        VARCHAR role
    }

    memorial_spaces {
        UUID id PK
        UUID creator_id FK
        VARCHAR deceased_name
        EnumSQL deceased_gender
        DATE birth_date
        DATE death_date
        VARCHAR relationship
        TEXT bio
        UUID scene_id
        VARCHAR music_url
        EnumSQL privacy_level
        VARCHAR access_password
        INTEGER visit_count
        TIMESTAMP created_at
        TIMESTAMP updated_at
        BOOLEAN is_active
        JSON custom_settings
        VARCHAR cover_image_url
    }

    memorial_assets {
        UUID id PK
        UUID space_id FK
        UUID uploader_id FK
        EnumSQL asset_type
        VARCHAR title
        TEXT description
        VARCHAR file_url
        VARCHAR thumbnail_url
        VARCHAR original_filename
        INTEGER file_size
        JSON metadata
        INTEGER display_order
        TIMESTAMP created_at
        TIMESTAMP updated_at
        BOOLEAN is_ai_enhanced
    }

    memorial_events {
        UUID id PK
        UUID memorial_space_id FK
        VARCHAR event_date
        VARCHAR title
        TEXT description
        TIMESTAMP created_at
        TIMESTAMP updated_at
    }

    users ||--o{ memorial_spaces : creates
    users ||--o{ memorial_assets : uploads
    memorial_spaces ||--o{ memorial_assets : contains
    memorial_spaces ||--o{ memorial_events : has
```