# 技术选型与架构说明

本文档概述了归处忆后端服务采用的主要技术栈和架构考虑。

## 1. 主要编程语言与框架

*   **编程语言**: Python (版本 3.8+)
*   **Web 框架**: FastAPI
    *   FastAPI 以其高性能、易用性、基于 OpenAPI 的自动文档生成以及对异步编程的良好支持而被选中。

## 2. 数据库

*   **数据库类型**: PostgreSQL
*   **ORM**: SQLAlchemy (与 FastAPI 异步集成)
*   **数据库迁移**: Alembic (推荐，用于管理数据库 schema 变更)

## 3. 核心依赖库

*   **`uvicorn`**: ASGI 服务器，用于运行 FastAPI 应用。
*   **`pydantic`**: 用于数据验证和设置管理。
*   **`python-jose[cryptography]`**: 用于 JWT 令牌的创建和验证。
*   **`passlib[bcrypt]`**: 用于密码哈希。
*   **`python-multipart`**: 用于处理文件上传 (表单数据)。
*   **`psycopg2-binary`**: PostgreSQL 驱动程序。
*   **`python-dotenv`**: 用于管理环境变量。

## 4. 架构考虑

*   **当前架构**: 单体应用 (Monolith)
    *   对于项目初期和中等规模，单体架构更易于开发、部署和管理。
*   **模块化设计**: 应用内部按功能模块划分 (如认证、纪念空间管理、资源管理等)，每个模块包含自己的路由、CRUD 操作、模型和 schemas。
*   **API 设计**: 遵循 RESTful 原则，提供清晰、一致的 API 接口。
*   **异步处理**: FastAPI 的异步特性被充分利用，以提高 I/O 密集型操作的性能。

## 5. 部署与运维 (建议)

*   **容器化**: Docker (推荐，用于打包应用及其依赖，确保环境一致性)
*   **编排**: Docker Compose (用于本地开发和简单部署), Kubernetes (用于生产环境大规模部署和管理)
*   **CI/CD**: Jenkins, GitLab CI, GitHub Actions (推荐，用于自动化测试和部署流程)

## 6. 安全性

*   **认证**: JWT (JSON Web Tokens) 用于 API 认证。
*   **授权**: 基于用户角色和资源所有权进行权限控制。
*   **密码存储**: 使用 bcrypt 哈希算法存储用户密码。
*   **输入验证**: Pydantic 用于严格验证所有输入数据。
*   **CORS**: 配置跨域资源共享策略，限制允许的来源。
*   **HTTPS**: 在生产环境中强制使用 HTTPS。

## 7. 文件存储

*   **本地存储**: 当前实现将上传的文件存储在服务器本地文件系统 (`./uploads` 目录)。
*   **云存储 (推荐用于生产)**: 考虑使用 AWS S3, Google Cloud Storage, Azure Blob Storage 等云存储服务，以获得更好的可伸缩性、持久性和可用性。