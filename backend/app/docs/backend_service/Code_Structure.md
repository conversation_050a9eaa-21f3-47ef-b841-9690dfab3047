# 后端代码结构方案

本文档描述了归处忆后端服务的代码目录结构和主要模块功能。

## 1. 顶层结构 (`backend/`)

```
backend/
├── app/                  # FastAPI 应用核心代码
│   ├── __init__.py
│   ├── api_v1_routers/   # API 版本1的路由模块
│   │   ├── __init__.py
│   │   ├── auth.py         # 认证相关路由 (登录, 注册, Token刷新)
│   │   ├── deps.py         # FastAPI 依赖项 (如 get_db, get_current_user)
│   │   ├── memorial_spaces.py # 纪念空间 CRUD 路由
│   │   ├── memorial_events.py # 纪念事件 CRUD 路由 (嵌套于空间下)
│   │   └── memorial_assets.py # 纪念资源 CRUD 路由 (嵌套于空间下)
│   ├── core/             # 核心配置和工具
│   │   ├── __init__.py
│   │   ├── config.py       # 应用配置 (环境变量, 密钥等)
│   │   └── security.py     # 安全相关工具 (密码哈希, JWT令牌创建)
│   ├── crud.py           # CRUD (Create, Read, Update, Delete) 操作的基类和具体实现
│   ├── db/               # 数据库相关模块
│   │   ├── __init__.py
│   │   ├── session.py      # SQLAlchemy 数据库会话和引擎设置
│   │   └── init_db.py      # 数据库初始化脚本 (创建表, 创建超级用户)
│   ├── docs/             # 项目文档
│   │   └── backend_service/
│   │       ├── API_Spec.md       # OpenAPI 规范文档
│   │       ├── DB_Schema.md      # 数据库表结构文档
│   │       └── Tech_Stack.md     # 技术选型文档
│   ├── main.py           # FastAPI 应用主入口 (应用实例化, 中间件配置, 路由包含)
│   ├── models_sqlalchemy.py # SQLAlchemy 数据模型定义 (数据库表结构)
│   ├── schemas_pydantic.py  # Pydantic 主模式定义文件 (部分核心 Schema)
│   └── schemas_pydantic/   # Pydantic 模式定义 (按模块组织)
│       ├── __init__.py
│       ├── token.py        # Token 相关 Pydantic Schemas
│       └── user.py         # User 相关 Pydantic Schemas
├── run.py                # 应用启动脚本 (使用 uvicorn)
└── README.md             # 后端服务说明文档 (如何运行、配置等)
```

## 2. 模块说明

*   **`app/`**: 包含所有 FastAPI 应用的核心逻辑。
    *   **`api_v1_routers/`**: 定义了所有 API V1 版本的端点。每个文件通常对应一个资源或一组相关功能。
        *   `deps.py`: 存放 FastAPI 的依赖注入函数，如数据库会话获取、当前用户认证等。
    *   **`core/`**: 包含应用的核心配置 (`config.py`) 和安全相关的工具函数 (`security.py`)。
    *   **`crud.py`**: 实现了与数据库交互的 CRUD (Create, Read, Update, Delete) 操作。包含一个通用的 `CRUDBase` 类和针对每个数据模型的具体 CRUD 类。
    *   **`db/`**: 管理数据库连接 (`session.py`) 和初始化 (`init_db.py`)。
    *   **`docs/backend_service/`**: 存放后端相关的设计和规范文档。
    *   **`main.py`**: FastAPI 应用的入口点。在这里创建 FastAPI 实例，配置中间件 (如 CORS)，并包含所有 API 路由器。
    *   **`models_sqlalchemy.py`**: 定义了所有 SQLAlchemy 模型，这些模型映射到数据库中的表。
    *   **`schemas_pydantic.py` / `schemas_pydantic/`**: 定义了所有 Pydantic 模型，用于数据验证、序列化以及 API 请求和响应的类型提示。
*   **`run.py`**: 用于启动后端 FastAPI 应用的脚本，通常使用 `uvicorn`。在启动前会执行数据库初始化逻辑。
*   **`README.md`**: (待创建) 提供后端项目的设置、运行和基本使用指南。

## 3. 设计原则

*   **模块化**: 代码按功能组织，易于理解和维护。
*   **关注点分离**: 路由、业务逻辑 (CRUD)、数据模型、配置等分离到不同的模块/文件。
*   **依赖注入**: FastAPI 的依赖注入系统被广泛使用，以简化代码和提高可测试性。
*   **配置集中管理**: 应用配置通过 `core/config.py` 使用 Pydantic 和环境变量进行管理。
*   **类型提示**: 广泛使用 Python 类型提示，结合 Pydantic 模型，以提高代码健壮性和可维护性。