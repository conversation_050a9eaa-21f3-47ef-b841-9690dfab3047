# API 定义文档 (OpenAPI 3.0)

本文档遵循 OpenAPI 3.0 规范，描述了归处忆后端服务的 API 接口。

```yaml
openapi: 3.0.3
info:
  title: YunNian Memorial Platform API
  version: v1
  description: API for the YunNian Memorial Platform, enabling creation and management of memorial spaces, events, and assets.
  contact:
    name: YunNian Support
    email: <EMAIL> # Placeholder
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html

servers:
  - url: http://localhost:8000/api/v1 # From settings.SERVER_HOST and settings.API_V1_STR
    description: Local development server
  # Add production server URL when available
  # - url: https://api.yunnian.com/api/v1
  #   description: Production server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    # --- General <PERSON> ---
    ErrorResponse:
      type: object
      properties:
        detail:
          type: string
          example: "Error message describing the issue."
        code:
          type: integer
          nullable: true
          example: 40001

    # --- Token Schemas (from schemas_pydantic/token.py) ---
    TokenResponse:
      type: object
      properties:
        access_token:
          type: string
        refresh_token:
          type: string
          nullable: true
        token_type:
          type: string
          default: bearer
        expires_in:
          type: integer
          description: Expiration time in seconds for the access_token.
          nullable: true

    RefreshTokenRequest:
      type: object
      required:
        - refresh_token
      properties:
        refresh_token:
          type: string

    # --- User Schemas (from schemas_pydantic/user.py) ---
    UserBase:
      type: object
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        username:
          type: string
          minLength: 3
          maxLength: 50
          example: "john_doe"
        full_name:
          type: string
          maxLength: 100
          nullable: true
          example: "John Doe"
        phone:
          type: string
          maxLength: 20
          nullable: true
          example: "+1234567890"

    UserCreate:
      allOf:
        - $ref: '#/components/schemas/UserBase'
        - type: object
          required:
            - password
          properties:
            password:
              type: string
              format: password
              minLength: 8
              example: "strongpassword123"
            is_superuser:
              type: boolean
              default: false

    UserResponse:
      allOf:
        - $ref: '#/components/schemas/UserBase'
        - type: object
          properties:
            id:
              type: string
              format: uuid
            avatar_url:
              type: string
              format: url
              nullable: true
            bio:
              type: string
              nullable: true
            is_active:
              type: boolean
              default: true
            is_verified:
              type: boolean
              default: false
            is_superuser:
              type: boolean
              default: false
            role:
              type: string
              default: "user"
            created_at:
              type: string
              format: date-time
            updated_at:
              type: string
              format: date-time
            last_login_at:
              type: string
              format: date-time
              nullable: true

    # --- Memorial Space Schemas (from schemas_pydantic.py) ---
    DeceasedGenderEnum:
      type: string
      enum: ["male", "female", "other", "unknown"]

    PrivacyLevelEnum:
      type: string
      enum: ["public", "private", "password", "family"]

    MemorialSpaceBase:
      type: object
      properties:
        deceased_name:
          type: string
          example: "先父李明"
        deceased_gender:
          $ref: '#/components/schemas/DeceasedGenderEnum'
          nullable: true
        birth_date:
          type: string
          format: date
          nullable: true
          example: "1950-01-15"
        death_date:
          type: string
          format: date
          nullable: true
          example: "2020-03-10"
        relationship:
          type: string
          nullable: true
          example: "父亲"
        bio:
          type: string
          nullable: true
          example: "一位慈祥的父亲，一位敬业的工程师。"
        scene_id:
          type: string
          format: uuid
          nullable: true
        music_url:
          type: string
          format: url
          nullable: true
        privacy_level:
          $ref: '#/components/schemas/PrivacyLevelEnum'
          default: "private"
        access_password:
          type: string
          nullable: true
          example: "securepassword123"
        custom_settings:
          type: object
          nullable: true
          example: { "theme_color": "blue" }

    MemorialSpaceCreate:
      allOf:
        - $ref: '#/components/schemas/MemorialSpaceBase'
      # Note: Actual creation uses FormData, this schema represents the data fields.

    MemorialSpaceUpdate:
      type: object # All fields optional for update
      properties:
        deceased_name:
          type: string
          nullable: true
        deceased_gender:
          $ref: '#/components/schemas/DeceasedGenderEnum'
          nullable: true
        birth_date:
          type: string
          format: date
          nullable: true
        death_date:
          type: string
          format: date
          nullable: true
        relationship:
          type: string
          nullable: true
        bio:
          type: string
          nullable: true
        scene_id:
          type: string
          format: uuid
          nullable: true
        music_url:
          type: string
          format: url
          nullable: true
        privacy_level:
          $ref: '#/components/schemas/PrivacyLevelEnum'
          nullable: true
        access_password:
          type: string
          nullable: true
        custom_settings:
          type: object
          nullable: true

    MemorialSpaceResponse:
      allOf:
        - $ref: '#/components/schemas/MemorialSpaceBase'
        - type: object
          properties:
            id:
              type: string
              format: uuid
            creator_id:
              type: string
              format: uuid
            visit_count:
              type: integer
              default: 0
            created_at:
              type: string
              format: date-time
            updated_at:
              type: string
              format: date-time
            is_active:
              type: boolean
              default: true
            cover_image_url:
              type: string
              format: url
              nullable: true

    MemorialSpaceListResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/MemorialSpaceResponse'
        total:
          type: integer
        page:
          type: integer
        size:
          type: integer

    # --- Memorial Asset Schemas (from schemas_pydantic.py) ---
    AssetTypeEnum:
      type: string
      enum: ["image", "video", "audio", "document", "other", "cover_image", "life_photo"]

    MemorialAssetBase:
      type: object
      properties:
        asset_type:
          $ref: '#/components/schemas/AssetTypeEnum'
        title:
          type: string
          nullable: true
          example: "珍贵合影"
        description:
          type: string
          nullable: true
          example: "2005年家庭聚会照片"
        file_url: # Renamed to 'url' in Pydantic response, but base might be file_url
          type: string
          format: url
          nullable: true
        thumbnail_url:
          type: string
          format: url
          nullable: true
        display_order:
          type: integer
          default: 0

    MemorialAssetCreate:
      type: object
      required:
        - asset_type
      properties:
        asset_type:
          $ref: '#/components/schemas/AssetTypeEnum'
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        display_order:
          type: integer
          nullable: true
          default: 0
      # Note: Actual creation uses FormData with a 'file' field.

    MemorialAssetUpdate:
      type: object
      properties:
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        display_order:
          type: integer
          nullable: true

    MemorialAssetResponse:
      allOf:
        - $ref: '#/components/schemas/MemorialAssetBase'
        - type: object
          properties:
            id:
              type: string
              format: uuid
            space_id:
              type: string
              format: uuid
            uploader_id:
              type: string
              format: uuid
            original_filename:
              type: string
              nullable: true
            file_size:
              type: integer
              nullable: true
            metadata:
              type: object
              nullable: true
            is_ai_enhanced:
              type: boolean
              default: false
            created_at:
              type: string
              format: date-time
            updated_at:
              type: string
              format: date-time
            url: # Consolidated field name
              type: string
              format: url

    MemorialAssetListResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/MemorialAssetResponse'
        total:
          type: integer
        page:
          type: integer
        size:
          type: integer

    # --- Memorial Event Schemas (from schemas_pydantic.py) ---
    MemorialEventBase:
      type: object
      required:
        - event_date
        - title
      properties:
        event_date:
          type: string
          example: "2003" # Or a full date "2003-05-10"
        title:
          type: string
        description:
          type: string
          nullable: true

    MemorialEventCreate:
      allOf:
        - $ref: '#/components/schemas/MemorialEventBase'

    MemorialEventUpdate:
      type: object
      properties:
        event_date:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true

    MemorialEventResponse:
      allOf:
        - $ref: '#/components/schemas/MemorialEventBase'
        - type: object
          properties:
            id:
              type: string
              format: uuid
            memorial_space_id:
              type: string
              format: uuid
            created_at:
              type: string
              format: date-time
            updated_at:
              type: string
              format: date-time
            year: # Derived field for frontend compatibility
              type: string
              nullable: true

    MemorialEventListResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/MemorialEventResponse'
        total:
          type: integer
        page:
          type: integer
        size:
          type: integer

security:
  - bearerAuth: []

paths:
  # --- Auth Routes (from api_v1_routers/auth.py) ---
  /auth/login/access-token:
    post:
      tags:
        - auth
      summary: Login for Access Token
      description: OAuth2 compatible token login, get an access token for future requests.
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required:
                - username
                - password
              properties:
                username:
                  type: string
                  description: Can be email or username.
                password:
                  type: string
                grant_type:
                  type: string
                  description: OAuth2 grant type, typically 'password'. Optional here as FastAPI handles it.
      responses:
        '200':
          description: Successful login, returns access and refresh tokens.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenResponse'
        '400':
          description: Incorrect email/username or password, or inactive user.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/register:
    post:
      tags:
        - auth
      summary: Register New User
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreate'
      responses:
        '200': # FastAPI default is 200 for POST if not specified, but 201 is more common for creation
          description: User registered successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '400':
          description: User with this email or username already exists.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/login/refresh-token:
    post:
      tags:
        - auth
      summary: Refresh Access Token
      security: [] # Refresh token itself is the auth for this endpoint
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        '200':
          description: Access token refreshed successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenResponse'
        '401':
          description: Invalid refresh token or inactive user.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/logout:
    post:
      tags:
        - auth
      summary: Logout User
      security:
        - bearerAuth: []
      responses:
        '204':
          description: User logged out successfully (client-side token removal).
        '401':
          description: Unauthorized.

  # --- Memorial Space Routes (from api_v1_routers/memorial_spaces.py) ---
  /memorial-spaces/:
    post:
      tags:
        - memorial-spaces
      summary: Create Memorial Space
      security:
        - bearerAuth: []
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - deceased_name
              properties:
                deceased_name:
                  type: string
                deceased_gender:
                  $ref: '#/components/schemas/DeceasedGenderEnum'
                birth_date:
                  type: string
                  format: date
                  nullable: true
                death_date:
                  type: string
                  format: date
                  nullable: true
                relationship:
                  type: string
                  nullable: true
                bio:
                  type: string
                  nullable: true
                privacy_level:
                  $ref: '#/components/schemas/PrivacyLevelEnum'
                  default: "private"
                access_password:
                  type: string
                  nullable: true
                # scene_id, music_url, custom_settings can be added if supported via form
      responses:
        '200': # Or 201
          description: Memorial space created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialSpaceResponse'
        '400':
          description: Invalid input (e.g., password required for password-protected space).
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized.

    get:
      tags:
        - memorial-spaces
      summary: List Memorial Spaces (for current user or public)
      security:
        - bearerAuth: [] # Or make it optional for public spaces
      parameters:
        - name: skip
          in: query
          schema:
            type: integer
            default: 0
        - name: limit
          in: query
          schema:
            type: integer
            default: 100
        - name: filter_type
          in: query
          description: "Filter spaces: 'my' (created by user), 'public', 'family' (if implemented)"
          schema:
            type: string
            enum: ["my", "public", "family"]
            default: "my"
      responses:
        '200':
          description: A list of memorial spaces.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialSpaceListResponse'
        '401':
          description: Unauthorized.

  /memorial-spaces/{space_id}:
    get:
      tags:
        - memorial-spaces
      summary: Get Memorial Space by ID
      security:
        - bearerAuth: [] # Or make it optional for public spaces, with password check if needed
      parameters:
        - name: space_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: password # For password-protected spaces if not authenticated as owner
          in: query
          schema:
            type: string
            nullable: true
      responses:
        '200':
          description: Details of the memorial space.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialSpaceResponse'
        '401':
          description: Unauthorized (e.g. wrong password for protected space).
        '403':
          description: Forbidden (e.g. trying to access private space without permission).
        '404':
          description: Memorial space not found.

    put:
      tags:
        - memorial-spaces
      summary: Update Memorial Space
      security:
        - bearerAuth: []
      parameters:
        - name: space_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json: # Assuming JSON for update, though form data could also be used
            schema:
              $ref: '#/components/schemas/MemorialSpaceUpdate'
      responses:
        '200':
          description: Memorial space updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialSpaceResponse'
        '401':
          description: Unauthorized.
        '403':
          description: Forbidden (not owner).
        '404':
          description: Memorial space not found.

    delete:
      tags:
        - memorial-spaces
      summary: Delete Memorial Space
      security:
        - bearerAuth: []
      parameters:
        - name: space_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200': # Or 204 No Content
          description: Memorial space deleted successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Memorial space deleted successfully."
        '401':
          description: Unauthorized.
        '403':
          description: Forbidden (not owner).
        '404':
          description: Memorial space not found.

  # --- Memorial Event Routes (nested under memorial-spaces) ---
  /memorial-spaces/{space_id}/events/:
    post:
      tags:
        - memorial-events
      summary: Create Memorial Event for a Space
      security:
        - bearerAuth: []
      parameters:
        - name: space_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MemorialEventCreate'
      responses:
        '200': # Or 201
          description: Memorial event created successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialEventResponse'
        '401':
          description: Unauthorized.
        '403':
          description: Forbidden (not owner of the space).
        '404':
          description: Memorial space not found.

    get:
      tags:
        - memorial-events
      summary: List Memorial Events for a Space
      security:
        - bearerAuth: [] # Or public access depending on space privacy
      parameters:
        - name: space_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: skip
          in: query
          schema:
            type: integer
            default: 0
        - name: limit
          in: query
          schema:
            type: integer
            default: 100
      responses:
        '200':
          description: A list of memorial events for the space.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialEventListResponse'
        '401':
          description: Unauthorized.
        '404':
          description: Memorial space not found.

  /memorial-spaces/{space_id}/events/{event_id}:
    get:
      tags:
        - memorial-events
      summary: Get Memorial Event by ID
      security:
        - bearerAuth: [] # Or public access
      parameters:
        - name: space_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: event_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Details of the memorial event.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialEventResponse'
        '401':
          description: Unauthorized.
        '404':
          description: Memorial space or event not found.

    put:
      tags:
        - memorial-events
      summary: Update Memorial Event
      security:
        - bearerAuth: []
      parameters:
        - name: space_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: event_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MemorialEventUpdate'
      responses:
        '200':
          description: Memorial event updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialEventResponse'
        '401':
          description: Unauthorized.
        '403':
          description: Forbidden (not owner).
        '404':
          description: Memorial space or event not found.

    delete:
      tags:
        - memorial-events
      summary: Delete Memorial Event
      security:
        - bearerAuth: []
      parameters:
        - name: space_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: event_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200': # Or 204
          description: Memorial event deleted successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Memorial event deleted successfully."
        '401':
          description: Unauthorized.
        '403':
          description: Forbidden (not owner).
        '404':
          description: Memorial space or event not found.

  # --- Memorial Asset Routes (nested under memorial-spaces) ---
  /memorial-spaces/{space_id}/assets/:
    post:
      tags:
        - memorial-assets
      summary: Upload Memorial Asset to a Space
      security:
        - bearerAuth: []
      parameters:
        - name: space_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - file
                - asset_type
              properties:
                file:
                  type: string
                  format: binary
                asset_type:
                  $ref: '#/components/schemas/AssetTypeEnum'
                title:
                  type: string
                  nullable: true
                description:
                  type: string
                  nullable: true
                display_order:
                  type: integer
                  nullable: true
                  default: 0
      responses:
        '200': # Or 201
          description: Memorial asset uploaded successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialAssetResponse'
        '400':
          description: Invalid input.
        '401':
          description: Unauthorized.
        '403':
          description: Forbidden (not owner of the space).
        '404':
          description: Memorial space not found.
        '500':
          description: Could not save file.

    get:
      tags:
        - memorial-assets
      summary: List Memorial Assets for a Space
      security:
        - bearerAuth: [] # Or public access
      parameters:
        - name: space_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: asset_type
          in: query
          schema:
            $ref: '#/components/schemas/AssetTypeEnum'
          nullable: true
        - name: skip
          in: query
          schema:
            type: integer
            default: 0
        - name: limit
          in: query
          schema:
            type: integer
            default: 100
      responses:
        '200':
          description: A list of memorial assets for the space.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialAssetListResponse'
        '401':
          description: Unauthorized.
        '404':
          description: Memorial space not found.

  /memorial-spaces/{space_id}/assets/{asset_id}:
    get:
      tags:
        - memorial-assets
      summary: Get Memorial Asset by ID
      security:
        - bearerAuth: [] # Or public access
      parameters:
        - name: space_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: asset_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Details of the memorial asset.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialAssetResponse'
        '401':
          description: Unauthorized.
        '404':
          description: Memorial space or asset not found.

    put:
      tags:
        - memorial-assets
      summary: Update Memorial Asset metadata
      security:
        - bearerAuth: []
      parameters:
        - name: space_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: asset_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MemorialAssetUpdate'
      responses:
        '200':
          description: Memorial asset updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialAssetResponse'
        '401':
          description: Unauthorized.
        '403':
          description: Forbidden (not owner).
        '404':
          description: Memorial space or asset not found.

    delete:
      tags:
        - memorial-assets
      summary: Delete Memorial Asset
      security:
        - bearerAuth: []
      parameters:
        - name: space_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
        - name: asset_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200': # Or 204
          description: Memorial asset deleted successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Memorial asset deleted successfully."
        '401':
          description: Unauthorized.
        '403':
          description: Forbidden (not owner).
        '404':
          description: Memorial space or asset not found.

  # --- Health Check --- 
  /healthcheck:
    get:
      tags:
        - health
      summary: Health Check
      description: Returns the status of the API.
      security: [] # No auth needed for health check
      responses:
        '200':
          description: API is healthy.
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "ok"

```