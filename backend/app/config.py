"""
应用配置
"""

import os
from datetime import timedelta


class Config:
    """基础配置"""

    SECRET_KEY = os.environ.get("SECRET_KEY") or "dev-key-please-change-in-production"
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    JWT_SECRET_KEY = os.environ.get("JWT_SECRET_KEY") or "jwt-dev-key-please-change"
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=1)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)

    # 上传文件配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(__file__)), "uploads")
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

    # Replicate API 配置
    REPLICATE_API_KEY = (
        os.environ.get("REPLICATE_API_KEY")
        or "****************************************"
    )

    @staticmethod
    def init_app(app):
        """初始化应用"""
        # 确保上传目录存在
        os.makedirs(os.path.join(app.instance_path, "uploads"), exist_ok=True)
        os.makedirs(
            os.path.join(app.instance_path, "uploads", "ai_results"), exist_ok=True
        )


class DevelopmentConfig(Config):
    """开发环境配置"""

    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get(
        "DEV_DATABASE_URL"
    ) or "sqlite:///" + os.path.join(
        os.path.dirname(os.path.dirname(__file__)), "dev.db"
    )


class TestingConfig(Config):
    """测试环境配置"""

    TESTING = True
    SQLALCHEMY_DATABASE_URI = os.environ.get("TEST_DATABASE_URL") or "sqlite://"


class ProductionConfig(Config):
    """生产环境配置"""

    SQLALCHEMY_DATABASE_URI = os.environ.get(
        "DATABASE_URL"
    ) or "sqlite:///" + os.path.join(
        os.path.dirname(os.path.dirname(__file__)), "data.db"
    )

    @classmethod
    def init_app(cls, app):
        Config.init_app(app)

        # 生产环境特定配置
        import logging
        from logging.handlers import RotatingFileHandler

        # 文件日志
        file_handler = RotatingFileHandler(
            "logs/memorial.log", maxBytes=10 * 1024 * 1024, backupCount=10
        )
        file_handler.setFormatter(
            logging.Formatter(
                "%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]"
            )
        )
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)

        app.logger.setLevel(logging.INFO)
        app.logger.info("祭祀网站启动")


config = {
    "development": DevelopmentConfig,
    "testing": TestingConfig,
    "production": ProductionConfig,
    "default": DevelopmentConfig,
}
