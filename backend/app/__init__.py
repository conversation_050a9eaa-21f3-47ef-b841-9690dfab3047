# Memorial Backend Application
# FastAPI-only implementation

import os

from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 应用配置
class Config:
    SECRET_KEY = os.environ.get("APP_SECRET_KEY", "dev-key")
    DATABASE_URL = os.environ.get("DATABASE_URL", "sqlite:///app.db")
    JWT_SECRET_KEY = os.environ.get("JWT_SECRET_KEY", "dev-jwt-key")
    DEBUG = os.environ.get("DEBUG", "False").lower() == "true"

    # CORS 配置
    CORS_ORIGINS = [
        "http://localhost:3000",
        "http://localhost:4001",
        "http://localhost:5173",
        "https://memorial.example.com",
    ]


config = Config()
