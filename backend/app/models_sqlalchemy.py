import uuid
from datetime import datetime
from typing import Any  # 移除未使用的 Type 导入

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    Date,
    DateTime,
    Float,
    ForeignKey,
    Integer,
    Numeric,
    String,
    Text,
    UniqueConstraint,
)
from sqlalchemy import (
    Enum as SAEnum,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import DeclarativeBase, relationship


# 为 SQLAlchemy 模型创建基类，并添加类型注解
class Base(DeclarativeBase):
    # 允许传统的类型注解，避免SQLAlchemy 2.0的严格类型检查
    __allow_unmapped__ = True


# Enum definitions from Pydantic schemas to be used in SQLAlchemy models
# These should ideally be kept in sync or defined in a shared location
class DeceasedGenderEnumSQL(SAEnum):
    male = "male"
    female = "female"
    other = "other"
    unknown = "unknown"


class PrivacyLevelEnumSQL(SAEnum):
    public = "public"
    private = "private"
    password = "password"
    family = "family"


class AssetTypeEnumSQL(SAEnum):
    image = "image"
    video = "video"
    audio = "audio"
    document = "document"
    other = "other"
    cover_image = "cover_image"
    life_photo = "life_photo"


class ProductTypeEnumSQL(SAEnum):
    virtual_item = "virtual_item"
    service = "service"
    subscription = "subscription"


class OrderStatusEnumSQL(SAEnum):
    pending = "pending"
    paid = "paid"
    cancelled = "cancelled"
    refunded = "refunded"


# --- User Model (Simplified, assuming it exists elsewhere or is defined as needed) ---
class User(Base):
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    password_hash: Column[str] = Column(String(255), nullable=False)
    full_name = Column(String(100))
    # phone = Column(String(20))  # 数据库中不存在此字段
    avatar_url = Column(String(255))
    bio = Column(Text)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    # is_email_verified: Column[bool] = Column(Boolean, default=False, nullable=False)  # 数据库中不存在此字段
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    last_login_at = Column(DateTime)
    role = Column(String(20), default="user", nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)

    # 密码重置和邮箱验证相关字段
    password_reset_token = Column(String(255), nullable=True)
    password_reset_expires_at = Column(DateTime, nullable=True)
    email_verification_token = Column(String(255), nullable=True)
    email_verification_expires_at = Column(DateTime, nullable=True)

    # 刷新Token存储 (可选，用于安全性要求更高的场景)
    refresh_token_hash = Column(String(255), nullable=True)
    refresh_token_expires_at = Column(DateTime, nullable=True)

    # Relationships
    memorial_spaces_created: Any = relationship(
        "MemorialSpace", back_populates="creator", cascade="all, delete-orphan"
    )
    assets_uploaded: Any = relationship(
        "MemorialAsset", back_populates="uploader", cascade="all, delete-orphan"
    )
    families_created: Any = relationship(
        "Family", back_populates="creator", cascade="all, delete-orphan"
    )
    family_memberships: Any = relationship(
        "FamilyMember", back_populates="user", cascade="all, delete-orphan"
    )
    family_invitations_sent: Any = relationship(
        "FamilyInvitation", back_populates="inviter", cascade="all, delete-orphan"
    )
    tributes: Any = relationship(
        "TributeRecord", back_populates="user", cascade="all, delete-orphan"
    )
    messages: Any = relationship(
        "MemorialMessage", back_populates="author", cascade="all, delete-orphan"
    )
    orders: Any = relationship("Order", back_populates="user")
    ai_tasks: Any = relationship("AITask", back_populates="user")


# --- Memorial Space Model ---
class MemorialSpace(Base):
    __tablename__ = "memorial_spaces"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    creator_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    deceased_name = Column(String(100), nullable=False, index=True)
    deceased_gender: Column[str] = Column(
        SAEnum("male", "female", "other", "unknown", name="deceased_gender_enum")
    )
    birth_date = Column(Date)
    death_date = Column(Date)
    creator_relationship_to_deceased = Column(
        String(50)
    )  # Renamed from 'relationship' to avoid conflict
    bio = Column(Text)
    scene_id = Column(
        UUID(as_uuid=True), ForeignKey("scenes.id"), index=True, nullable=True
    )
    music_url = Column(String(255))
    privacy_level: Column[str] = Column(
        SAEnum("public", "private", "password", "family", name="privacy_level_enum"),
        nullable=False,
        default="private",
        index=True,
    )
    access_password = Column(String(255))  # Should be hashed if stored
    visit_count = Column(Integer, nullable=False, default=0)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    is_active = Column(Boolean, default=True, nullable=False)
    custom_settings = Column(JSON)
    cover_image_url: Column[str | None] = Column(
        String(255), nullable=True
    )  # Added based on frontend usage and API_Spec implication

    # Relationships
    creator: Any = relationship("User", back_populates="memorial_spaces_created")
    assets: Any = relationship(
        "MemorialAsset", back_populates="memorial_space", cascade="all, delete-orphan"
    )
    events: Any = relationship(
        "MemorialEvent", back_populates="memorial_space", cascade="all, delete-orphan"
    )
    tributes: Any = relationship(
        "TributeRecord", back_populates="memorial_space", cascade="all, delete-orphan"
    )
    messages: Any = relationship(
        "MemorialMessage", back_populates="memorial_space", cascade="all, delete-orphan"
    )
    scene: Any = relationship("Scene", back_populates="memorial_spaces")


# --- Memorial Asset Model ---
class MemorialAsset(Base):
    __tablename__ = "memorial_assets"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    space_id = Column(
        UUID(as_uuid=True), ForeignKey("memorial_spaces.id"), nullable=False, index=True
    )
    uploader_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    asset_type: Column[str] = Column(
        SAEnum(
            "image",
            "video",
            "audio",
            "document",
            "other",
            "cover_image",
            "life_photo",
            name="asset_type_enum",
        ),
        nullable=False,
        index=True,
    )
    title = Column(String(255))
    description = Column(Text)
    file_url = Column(
        String(255), nullable=False
    )  # Renamed to 'url' in Pydantic, but DB might keep 'file_url'
    thumbnail_url = Column(String(255))
    original_filename = Column(String(255))
    file_size = Column(Integer)  # In bytes
    asset_metadata = Column(
        JSON
    )  # e.g., image dimensions, video duration. Renamed from 'metadata'
    display_order = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    is_ai_enhanced = Column(Boolean, default=False)

    # Relationships
    memorial_space: Any = relationship("MemorialSpace", back_populates="assets")
    uploader: Any = relationship("User", back_populates="assets_uploaded")


# --- Memorial Event Model ---
class MemorialEvent(Base):
    __tablename__ = "memorial_events"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    memorial_space_id = Column(
        UUID(as_uuid=True), ForeignKey("memorial_spaces.id"), nullable=False, index=True
    )
    event_date = Column(
        String(50), nullable=False
    )  # Storing as string as per Pydantic, could be Date or String with specific format
    title = Column(String(255), nullable=False)
    description = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # Relationships
    memorial_space: Any = relationship("MemorialSpace", back_populates="events")


# Scene model is now in app/models/scene.py to avoid duplication
# Import it here for convenience
from app.models.scene import Scene, SceneInteractionPoint

# Example for database connection and table creation (typically in main app setup)
# DATABASE_URL = "postgresql://user:password@host:port/database"
# engine = create_engine(DATABASE_URL)
# def create_db_and_tables():
#     Base.metadata.create_all(bind=engine)

# If you run this file directly, it can create tables (for testing/dev)
# if __name__ == "__main__":
#     # Replace with your actual database URL
#     # SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db" # Example for SQLite
#     SQLALCHEMY_DATABASE_URL = "postgresql://your_user:your_password@localhost/your_database_name"
#     engine = create_engine(SQLALCHEMY_DATABASE_URL)
#     Base.metadata.create_all(bind=engine)
#     print("Database tables created (if they didn't exist).")


# --- Family Models ---
class Family(Base):
    __tablename__ = "families"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    creator_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    family_motto = Column(String(500))
    origin_location = Column(String(255))
    established_date = Column(Date)
    privacy_level: Column[str] = Column(
        SAEnum("public", "private", "password", "family", name="privacy_level_enum"),
        nullable=False,
        default="private",
        index=True,
    )
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    is_active = Column(Boolean, default=True, nullable=False)

    # Relationships
    creator: Any = relationship("User", back_populates="families_created")
    members: Any = relationship(
        "FamilyMember", back_populates="family", cascade="all, delete-orphan"
    )
    invitations: Any = relationship(
        "FamilyInvitation", back_populates="family", cascade="all, delete-orphan"
    )
    genealogy_nodes: Any = relationship(
        "GenealogyNode", back_populates="family", cascade="all, delete-orphan"
    )
    genealogy_relationships: Any = relationship(
        "GenealogyRelationship", back_populates="family", cascade="all, delete-orphan"
    )


class FamilyMember(Base):
    __tablename__ = "family_members"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    family_id = Column(
        UUID(as_uuid=True), ForeignKey("families.id"), nullable=False, index=True
    )
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    role = Column(
        String(50), nullable=False, default="member"
    )  # member, admin, moderator
    joined_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)

    # Unique constraint to prevent duplicate memberships
    __table_args__ = (
        UniqueConstraint("family_id", "user_id", name="unique_family_member"),
    )

    # Relationships
    family: Any = relationship("Family", back_populates="members")
    user: Any = relationship("User", back_populates="family_memberships")


class FamilyInvitation(Base):
    __tablename__ = "family_invitations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    family_id = Column(
        UUID(as_uuid=True), ForeignKey("families.id"), nullable=False, index=True
    )
    inviter_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    invitee_email = Column(String(255), nullable=False)
    invitation_code = Column(String(100), unique=True, nullable=False, index=True)
    message = Column(Text)
    status = Column(
        String(50), nullable=False, default="pending"
    )  # pending, accepted, rejected, expired
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    expires_at = Column(DateTime, nullable=False)
    responded_at = Column(DateTime)

    # Relationships
    family: Any = relationship("Family", back_populates="invitations")
    inviter: Any = relationship("User", back_populates="family_invitations_sent")


# --- Genealogy Models ---
class GenealogyNode(Base):
    __tablename__ = "genealogy_nodes"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    family_id = Column(
        UUID(as_uuid=True), ForeignKey("families.id"), nullable=False, index=True
    )
    name = Column(String(255), nullable=False)
    gender = Column(String(20))  # male, female, other
    birth_date = Column(Date)
    death_date = Column(Date)
    biography = Column(Text)
    photo_url = Column(String(255))
    generation = Column(Integer)  # 世代数，用于族谱布局
    position_x = Column(Float)  # 族谱图中的X坐标
    position_y = Column(Float)  # 族谱图中的Y坐标
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    is_active = Column(Boolean, default=True, nullable=False)

    # Relationships
    family: Any = relationship("Family", back_populates="genealogy_nodes")
    relationships_from: Any = relationship(
        "GenealogyRelationship",
        foreign_keys="GenealogyRelationship.from_node_id",
        back_populates="from_node",
        cascade="all, delete-orphan",
    )
    relationships_to: Any = relationship(
        "GenealogyRelationship",
        foreign_keys="GenealogyRelationship.to_node_id",
        back_populates="to_node",
        cascade="all, delete-orphan",
    )


class GenealogyRelationship(Base):
    __tablename__ = "genealogy_relationships"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    family_id = Column(
        UUID(as_uuid=True), ForeignKey("families.id"), nullable=False, index=True
    )
    from_node_id = Column(
        UUID(as_uuid=True), ForeignKey("genealogy_nodes.id"), nullable=False, index=True
    )
    to_node_id = Column(
        UUID(as_uuid=True), ForeignKey("genealogy_nodes.id"), nullable=False, index=True
    )
    relationship_type = Column(
        String(50), nullable=False
    )  # parent, child, spouse, sibling
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Unique constraint to prevent duplicate relationships
    __table_args__ = (
        UniqueConstraint(
            "from_node_id",
            "to_node_id",
            "relationship_type",
            name="unique_genealogy_relationship",
        ),
    )

    # Relationships
    family: Any = relationship("Family", back_populates="genealogy_relationships")
    from_node: Any = relationship(
        "GenealogyNode",
        foreign_keys=[from_node_id],
        back_populates="relationships_from",
    )
    to_node: Any = relationship(
        "GenealogyNode", foreign_keys=[to_node_id], back_populates="relationships_to"
    )


# --- Tribute and Message Models ---
# --- Permission and Access Control Models ---
class MemorialSpacePermission(Base):
    """纪念空间权限管理"""

    __tablename__ = "memorial_space_permissions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    memorial_space_id = Column(
        UUID(as_uuid=True), ForeignKey("memorial_spaces.id"), nullable=False, index=True
    )
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    permission_type = Column(
        String(50), nullable=False
    )  # 'view', 'edit', 'tribute', 'manage'
    granted_by = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False
    )  # 授权者
    granted_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime)  # 权限过期时间
    is_active = Column(Boolean, default=True, nullable=False)

    # 权限配置
    can_view_private_info = Column(Boolean, default=False)  # 能否查看私密信息
    can_moderate = Column(Boolean, default=False)  # 能否审核内容
    can_invite_others = Column(Boolean, default=False)  # 能否邀请其他人

    # Relationships
    memorial_space: Any = relationship("MemorialSpace")
    user: Any = relationship("User", foreign_keys=[user_id])
    grantor: Any = relationship("User", foreign_keys=[granted_by])


class AccessLog(Base):
    """访问日志"""

    __tablename__ = "access_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    memorial_space_id = Column(
        UUID(as_uuid=True), ForeignKey("memorial_spaces.id"), nullable=False, index=True
    )
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, index=True
    )  # null for anonymous access
    access_type = Column(String(50), nullable=False)  # 'view', 'tribute', 'edit'
    ip_address = Column(String(45))  # IPv4/IPv6
    user_agent = Column(String(500))
    access_granted = Column(Boolean, nullable=False)
    denial_reason = Column(String(100))  # 拒绝访问的原因
    accessed_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    session_duration = Column(Integer)  # 会话持续时间（秒）

    # Relationships
    memorial_space: Any = relationship("MemorialSpace")
    user: Any = relationship("User")


class TributeRecord(Base):
    __tablename__ = "tribute_records"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    memorial_space_id = Column(
        UUID(as_uuid=True), ForeignKey("memorial_spaces.id"), nullable=False, index=True
    )
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    tribute_type = Column(
        String(50), nullable=False
    )  # incense, flower, candle, bow, food, offering
    message = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    is_anonymous = Column(Boolean, default=False, nullable=False)

    # 增强字段
    tribute_items = Column(JSON)  # 祭拜物品的详细配置 [{item_id, name, quantity, position}]
    duration_seconds = Column(Integer, default=0)  # 祭拜持续时间（秒）
    client_info = Column(JSON)  # 客户端信息（设备类型、浏览器等，用于统计）
    coordinates = Column(JSON)  # 3D场景中的坐标位置 {x, y, z}

    # Relationships
    memorial_space: Any = relationship("MemorialSpace", back_populates="tributes")
    user: Any = relationship("User", back_populates="tributes")


class MemorialMessage(Base):
    __tablename__ = "memorial_messages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    memorial_space_id = Column(
        UUID(as_uuid=True), ForeignKey("memorial_spaces.id"), nullable=False, index=True
    )
    author_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    content = Column(Text, nullable=False)
    is_anonymous = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    is_active = Column(Boolean, default=True, nullable=False)

    # Relationships
    memorial_space: Any = relationship("MemorialSpace", back_populates="messages")
    author: Any = relationship("User", back_populates="messages")


class Product(Base):
    """商品表"""

    __tablename__ = "products"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    product_type: Column[str] = Column(
        SAEnum("virtual_item", "service", "subscription", name="product_type_enum"),
        nullable=False,
        index=True,
    )
    category = Column(String(50), index=True)
    price = Column(Numeric(10, 2), nullable=False, index=True)
    discount_price = Column(Numeric(10, 2))
    image_url = Column(String(255))
    reference_id = Column(UUID(as_uuid=True))  # 关联ID(如tribute_item_id, scene_id等)
    is_active = Column(Boolean, nullable=False, default=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    meta_data = Column(JSON)  # 元数据(JSON格式)

    # Relationships
    order_items: Any = relationship("OrderItem", back_populates="product")


class Order(Base):
    """订单表"""

    __tablename__ = "orders"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    order_number = Column(String(50), unique=True, nullable=False, index=True)
    total_amount = Column(Numeric(10, 2), nullable=False)
    status: Column[str] = Column(
        SAEnum("pending", "paid", "cancelled", "refunded", name="order_status_enum"),
        nullable=False,
        default="pending",
        index=True,
    )
    payment_method = Column(String(50))
    payment_id = Column(String(100))  # 支付平台交易ID
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    paid_at = Column(DateTime, index=True)
    notes = Column(Text)

    # Relationships
    user: Any = relationship("User", back_populates="orders")
    order_items: Any = relationship(
        "OrderItem", back_populates="order", cascade="all, delete-orphan"
    )


class OrderItem(Base):
    """订单项表"""

    __tablename__ = "order_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    order_id = Column(
        UUID(as_uuid=True), ForeignKey("orders.id"), nullable=False, index=True
    )
    product_id = Column(
        UUID(as_uuid=True), ForeignKey("products.id"), nullable=False, index=True
    )
    quantity = Column(Integer, nullable=False, default=1)
    unit_price = Column(Numeric(10, 2), nullable=False)
    total_price = Column(Numeric(10, 2), nullable=False)
    meta_data = Column(JSON)  # 元数据(JSON格式)

    # Relationships
    order: Any = relationship("Order", back_populates="order_items")
    product: Any = relationship("Product", back_populates="order_items")


# Duplicate Scene class removed - using the one from app/models/scene.py


class TributeItem(Base):
    """祭品表"""

    __tablename__ = "tribute_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    category = Column(
        String(50), nullable=False, index=True
    )  # flower/candle/incense/food/etc
    image_url = Column(String(255))
    model_url = Column(String(255))  # 3D模型URL(如适用)
    is_premium = Column(Boolean, nullable=False, default=False, index=True)
    price = Column(Numeric(10, 2), default=0)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    is_active = Column(Boolean, nullable=False, default=True, index=True)


class AITask(Base):
    """AI任务表"""

    __tablename__ = "ai_tasks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    task_type = Column(
        String(50), nullable=False, index=True
    )  # photo_repair/voice_clone/etc
    status = Column(
        String(20), nullable=False, default="pending", index=True
    )  # pending/processing/completed/failed
    input_data = Column(JSON)  # 输入数据(JSON格式)
    output_data = Column(JSON)  # 输出数据(JSON格式)
    error_message = Column(Text)
    priority = Column(Integer, default=0, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    completed_at = Column(DateTime)

    # Relationships
    user: Any = relationship("User", back_populates="ai_tasks")


class AIModel(Base):
    """AI模型表"""

    __tablename__ = "ai_models"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    model_type = Column(
        String(50), nullable=False, index=True
    )  # photo_repair/voice_clone/etc
    version = Column(String(20), nullable=False, index=True)
    config = Column(JSON)  # 配置参数(JSON格式)
    is_active = Column(Boolean, nullable=False, default=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )


# ... existing code ...
