import uuid
from datetime import date, datetime
from enum import Enum

from pydantic import BaseModel, Field, HttpUrl


# --- Generic <PERSON> ---
class ErrorResponse(BaseModel):
    detail: str
    code: int | None = None


class PaginationLinks(BaseModel):
    first: HttpUrl | None = None
    last: HttpUrl | None = None
    next: HttpUrl | None = None
    prev: HttpUrl | None = None


# --- Memorial Space Schemas ---
class DeceasedGenderEnum(str, Enum):
    male = "male"
    female = "female"
    other = "other"
    unknown = "unknown"


class PrivacyLevelEnum(str, Enum):
    public = "public"
    private = "private"
    password = "password"
    family = "family"


class MemorialSpaceBase(BaseModel):
    deceased_name: str = Field(..., example="先父李明")
    deceased_gender: DeceasedGenderEnum | None = Field(None, example="male")
    birth_date: date | None = Field(None, example="1950-01-15")
    death_date: date | None = Field(None, example="2020-03-10")
    relationship: str | None = Field(None, example="父亲")
    bio: str | None = Field(None, example="一位慈祥的父亲，一位敬业的工程师。")
    scene_id: uuid.UUID | None = None
    music_url: HttpUrl | None = None
    privacy_level: PrivacyLevelEnum = Field(PrivacyLevelEnum.private, example="private")
    access_password: str | None = Field(
        None, example="securepassword123"
    )  # Only if privacy_level is 'password'
    custom_settings: dict | None = Field(None, example={"theme_color": "blue"})


class MemorialSpaceCreate(MemorialSpaceBase):
    # Frontend sends FormData, so fields will be individual. FastAPI can handle this with Form fields.
    # If cover_image is uploaded during creation, it would be a UploadFile type here.
    # For now, aligning with API_Spec which implies JSON, but acknowledging frontend behavior.
    pass


class MemorialSpaceUpdate(MemorialSpaceBase):
    # Fields are optional for update
    deceased_name: str | None = Field(None, example="先父李明")
    deceased_gender: DeceasedGenderEnum | None = Field(None, example="male")
    relationship: str | None = Field(None, example="父亲")
    bio: str | None = Field(None, example="一位慈祥的父亲，一位敬业的工程师。")
    scene_id: uuid.UUID | None = None
    music_url: HttpUrl | None = None
    privacy_level: PrivacyLevelEnum | None = Field(None, example="private")
    access_password: str | None = Field(None, example="securepassword123")
    custom_settings: dict | None = Field(None, example={"theme_color": "blue"})


class MemorialSpaceResponse(MemorialSpaceBase):
    id: uuid.UUID
    creator_id: uuid.UUID
    visit_count: int = 0
    created_at: datetime
    updated_at: datetime
    is_active: bool = True
    cover_image_url: HttpUrl | None = None  # Added based on frontend usage
    # creator: Optional[UserResponse] = None # Embedding creator info if needed
    # scene: Optional[SceneResponse] = None # Embedding scene info if needed

    model_config = {"from_attributes": True}


class MemorialSpaceListResponse(BaseModel):
    items: list[MemorialSpaceResponse]
    total: int
    page: int
    size: int
    links: PaginationLinks | None = None


# --- Memorial Asset Schemas ---
class AssetTypeEnum(str, Enum):
    image = "image"
    video = "video"
    audio = "audio"
    document = "document"
    other = "other"
    cover_image = "cover_image"  # As used in frontend
    life_photo = "life_photo"  # As used in frontend


class MemorialAssetBase(BaseModel):
    asset_type: AssetTypeEnum = Field(..., example="image")
    title: str | None = Field(None, example="珍贵合影")
    description: str | None = Field(None, example="2005年家庭聚会照片")
    file_url: HttpUrl | None = None  # URL is set after upload
    thumbnail_url: HttpUrl | None = None
    display_order: int = 0


class MemorialAssetCreate(BaseModel):
    # Actual file is UploadFile, other metadata comes from form fields or query params
    asset_type: AssetTypeEnum
    title: str | None = None
    description: str | None = None
    display_order: int | None = 0


class MemorialAssetUpdate(BaseModel):
    title: str | None = None
    description: str | None = None
    display_order: int | None = None


class MemorialAssetResponse(MemorialAssetBase):
    id: uuid.UUID
    space_id: uuid.UUID
    uploader_id: uuid.UUID
    original_filename: str | None = None
    file_size: int | None = None  # in bytes
    metadata: dict | None = None  # e.g., image dimensions, video duration
    is_ai_enhanced: bool = False
    created_at: datetime
    updated_at: datetime
    file_url: HttpUrl  # Changed from 'url' to 'file_url' for consistency with API_Spec and Base

    model_config = {"from_attributes": True}


class MemorialAssetListResponse(BaseModel):
    items: list[MemorialAssetResponse]
    total: int
    page: int
    size: int
    links: PaginationLinks | None = None


# --- Memorial Event Schemas (TimelineEvent in frontend) ---
class MemorialEventBase(BaseModel):
    event_date: str  # Frontend uses 'year', API spec implies 'event_date'
    title: str
    description: str | None = None


class MemorialEventCreate(MemorialEventBase):
    pass


class MemorialEventUpdate(MemorialEventBase):
    event_date: str | None = None
    title: str | None = None


class MemorialEventResponse(MemorialEventBase):
    id: uuid.UUID
    memorial_space_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    # Frontend expects 'year', so we might need a computed property or transform
    year: str | None = (
        None  # To match frontend TimelineEvent, can be derived from event_date
    )

    model_config = {"from_attributes": True}


class MemorialEventListResponse(BaseModel):
    items: list[MemorialEventResponse]
    total: int
    page: int
    size: int
    links: PaginationLinks | None = None


# --- Token Schemas (from API Spec) ---
class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str | None = None
    token_type: str = "bearer"
    expires_in: int | None = None


class RefreshTokenRequest(BaseModel):
    refresh_token: str


# --- Scene Schemas (Placeholder if needed by MemorialSpaceResponse) ---
class SceneResponse(BaseModel):
    id: uuid.UUID
    name: str
    preview_url: HttpUrl | None
    # ... other scene fields

    model_config = {"from_attributes": True}
