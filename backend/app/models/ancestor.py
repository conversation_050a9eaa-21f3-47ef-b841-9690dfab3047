import uuid
from datetime import datetime

from sqlalchemy import Column, Date, DateTime, ForeignKey, String, Text
from sqlalchemy.dialects.postgresql import UUID

from app.models_sqlalchemy import Base


class Ancestor(Base):
    __tablename__ = "ancestors"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    name = Column(String(64))
    birth_date = Column(Date)
    death_date = Column(Date)
    religious_preference = Column(String(64))
    cultural_background = Column(String(64))
    biography = Column(Text)
    photo_path = Column(String(256))
    model_path = Column(String(256))
    created_at = Column(DateTime, default=datetime.utcnow)

    def to_dict(self):
        return {
            "id": self.id,
            "user_id": self.user_id,
            "name": self.name,
            "birth_date": self.birth_date.isoformat() if self.birth_date else None,
            "death_date": self.death_date.isoformat() if self.death_date else None,
            "religious_preference": self.religious_preference,
            "cultural_background": self.cultural_background,
            "biography": self.biography,
            "photo_path": self.photo_path,
            "model_path": self.model_path,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }
