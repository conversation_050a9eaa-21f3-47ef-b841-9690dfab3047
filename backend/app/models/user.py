import uuid
from datetime import datetime

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Column,
    Date,
    DateTime,
    Foreign<PERSON>ey,
    Integer,
    String,
    Text,
)
from sqlalchemy import (
    Enum as SAEnum,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import DeclarativeBase, Mapped, relationship

from app.models_sqlalchemy import MemorialAsset, MemorialEvent


# 为 SQLAlchemy 模型创建基类，并添加类型注解
class Base(DeclarativeBase):
    pass


# Enum definitions from Pydantic schemas to be used in SQLAlchemy models
# These should ideally be kept in sync or defined in a shared location
class DeceasedGenderEnumSQL(SAEnum):
    male = "male"
    female = "female"
    other = "other"
    unknown = "unknown"


class PrivacyLevelEnumSQL(SAEnum):
    public = "public"
    private = "private"
    password = "password"
    family = "family"


class AssetTypeEnumSQL(SAEnum):
    image = "image"
    video = "video"
    audio = "audio"
    document = "document"
    other = "other"
    cover_image = "cover_image"
    life_photo = "life_photo"


# --- Memorial Space Model ---
class MemorialSpace(Base):
    __tablename__ = "memorial_spaces"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    creator_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    deceased_name = Column(String(100), nullable=False, index=True)
    deceased_gender: Column[str] = Column(
        SAEnum("male", "female", "other", "unknown", name="deceased_gender_enum")
    )
    birth_date = Column(Date)
    death_date = Column(Date)
    creator_relationship_to_deceased = Column(
        String(50)
    )  # Renamed from 'relationship' to avoid conflict
    bio = Column(Text)
    # scene_id = Column(UUID(as_uuid=True), ForeignKey("scenes.id"), index=True) # Assuming a 'scenes' table
    scene_id = Column(
        UUID(as_uuid=True), index=True, nullable=True
    )  # Simplified for now
    music_url = Column(String(255))
    privacy_level = Column(
        PrivacyLevelEnumSQL,
        nullable=False,
        default=PrivacyLevelEnumSQL.private,
        index=True,
    )
    access_password = Column(String(255))  # Should be hashed if stored
    visit_count = Column(Integer, nullable=False, default=0)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    is_active = Column(Boolean, default=True, nullable=False)
    custom_settings = Column(JSON)
    cover_image_url = Column(
        String(255)
    )  # Added based on frontend usage and API_Spec implication

    # Relationships
    creator: Mapped["User"] = relationship(back_populates="memorial_spaces_created")
    assets: Mapped[list["MemorialAsset"]] = relationship(
        back_populates="memorial_space", cascade="all, delete-orphan"
    )
    events: Mapped[list["MemorialEvent"]] = relationship(
        back_populates="memorial_space", cascade="all, delete-orphan"
    )
    # scene = relationship("Scene", back_populates="memorial_spaces") # If Scene model is defined


# --- User Model ---
class User(Base):
    __tablename__ = "users"
    __allow_unmapped__ = True

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(100), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(100))
    avatar_url = Column(String(255))
    bio = Column(Text)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )
    last_login_at = Column(DateTime)
    role = Column(String(20), default="user", nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)

    # Relationships
    memorial_spaces_created: Mapped[list["MemorialSpace"]] = relationship(
        back_populates="creator"
    )
    assets_uploaded: Mapped[list["MemorialAsset"]] = relationship(
        back_populates="uploader"
    )
    # Add other relationships like tributes, messages, family_memberships, etc.


# MemorialAsset 和 MemorialEvent 类定义已移至 models_sqlalchemy.py


# --- Scene Model (Placeholder, define if used) ---
# class Scene(Base):
#     __tablename__ = "scenes"
#     id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
#     name = Column(String(100), nullable=False)
#     description = Column(Text)
#     # Add other scene-related fields as needed
#     # Relationships
#     # memorial_spaces = relationship("MemorialSpace", back_populates="scene")

# Example for database connection and table creation (typically in main app setup)
# DATABASE_URL = "postgresql://user:password@host:port/database"
# engine = create_engine(DATABASE_URL)
# def create_db_and_tables():
#     Base.metadata.create_all(bind=engine)

# If you run this file directly, it can create tables (for testing/dev)
# if __name__ == "__main__":
#     # Replace with your actual database URL
#     # SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db" # Example for SQLite
#     SQLALCHEMY_DATABASE_URL = "postgresql://your_user:your_password@localhost/your_database_name"
#     engine = create_engine(SQLALCHEMY_DATABASE_URL)
#     Base.metadata.create_all(bind=engine)
#     print("Database tables created (if they didn't exist).")
