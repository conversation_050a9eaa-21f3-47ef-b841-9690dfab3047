# 3D场景数据模型
import uuid
from datetime import datetime
from typing import Any

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    DateTime,
    String,
    Text,
    Integer,
    ForeignKey,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.models_sqlalchemy import Base


class Scene(Base):
    """3D场景模型 - 定义纪念空间的3D环境"""

    __tablename__ = "scenes"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text)
    category = Column(
        String(50), nullable=False, index=True
    )  # 'buddhist', 'christian', 'traditional', 'modern', 'nature'

    # 3D资源配置
    model_url = Column(String(255), nullable=False)  # 3D模型文件URL
    texture_urls = Column(JSON)  # 贴图文件URLs的JSON数组
    thumbnail_url = Column(String(255))  # 场景缩略图

    # 场景配置
    lighting_config = Column(JSON)  # 光照配置
    camera_config = Column(JSON)  # 相机默认配置
    interaction_config = Column(JSON)  # 交互点配置
    audio_config = Column(JSON)  # 音频配置

    # 可用性和特性
    is_active = Column(Boolean, default=True, nullable=False)
    is_premium = Column(Boolean, default=False, nullable=False)  # 是否为付费场景
    supports_mobile = Column(Boolean, default=True, nullable=False)
    min_performance_level = Column(String(20), default="low")  # 'low', 'medium', 'high'

    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    # 使用统计
    usage_count = Column(Integer, nullable=False, default=0)

    # 关系
    memorial_spaces: Any = relationship("MemorialSpace", back_populates="scene")


class SceneInteractionPoint(Base):
    """场景交互点模型 - 定义场景中的可交互区域"""

    __tablename__ = "scene_interaction_points"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    scene_id = Column(
        UUID(as_uuid=True), ForeignKey("scenes.id"), nullable=False, index=True
    )
    name = Column(String(100), nullable=False)
    interaction_type = Column(
        String(50), nullable=False
    )  # 'offering', 'candle', 'photo_display', 'message_board'

    # 3D位置和方向
    position = Column(JSON, nullable=False)  # {x, y, z}
    rotation = Column(JSON)  # {x, y, z}
    scale = Column(JSON, default={"x": 1, "y": 1, "z": 1})  # {x, y, z}

    # 交互配置
    config = Column(JSON)  # 特定交互类型的配置
    is_active = Column(Boolean, default=True, nullable=False)

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # 关系
    scene: Any = relationship("Scene")
