import json
import uuid
from datetime import datetime

from sqlalchemy import Column, DateTime, String, Text
from sqlalchemy.dialects.postgresql import UUID

from app.models_sqlalchemy import Base


class ReligiousCulturalSetting(Base):
    __tablename__ = "religious_cultural_settings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(64))
    description = Column(Text)
    ritual_elements = Column(Text)  # 存储为JSON字符串
    special_dates = Column(Text)  # 存储为JSON字符串
    default_prayers = Column(Text)
    icon_path = Column(String(256))
    created_at = Column(DateTime, default=datetime.utcnow)

    def get_ritual_elements(self):
        if self.ritual_elements:
            return json.loads(self.ritual_elements)
        return []

    def set_ritual_elements(self, elements_list):
        if elements_list:
            self.ritual_elements = json.dumps(elements_list)
        else:
            self.ritual_elements = "[]"

    def get_special_dates(self):
        if self.special_dates:
            return json.loads(self.special_dates)
        return []

    def set_special_dates(self, dates_list):
        if dates_list:
            self.special_dates = json.dumps(dates_list)
        else:
            self.special_dates = "[]"

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "ritual_elements": self.get_ritual_elements(),
            "special_dates": self.get_special_dates(),
            "default_prayers": self.default_prayers,
            "icon_path": self.icon_path,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }
