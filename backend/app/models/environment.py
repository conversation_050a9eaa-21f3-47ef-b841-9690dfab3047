import json
import uuid
from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, String, Text
from sqlalchemy.dialects.postgresql import UUID

from app.models_sqlalchemy import Base


class Environment(Base):
    __tablename__ = "environments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(64))
    description = Column(Text)
    style_type = Column(String(64))
    religious_affiliation = Column(String(128))  # 存储为逗号分隔的字符串
    cultural_elements = Column(Text)  # 存储为JSON字符串
    season_support = Column(Boolean, default=False)
    weather_support = Column(Boolean, default=False)
    model_path = Column(String(256))
    low_poly_path = Column(String(256))
    thumbnail = Column(String(256))
    creation_date = Column(DateTime, default=datetime.utcnow)

    def get_religious_affiliation_list(self):
        if self.religious_affiliation:
            return self.religious_affiliation.split(",")
        return []

    def set_religious_affiliation_list(self, affiliation_list):
        if affiliation_list:
            self.religious_affiliation = ",".join(affiliation_list)
        else:
            self.religious_affiliation = ""

    def get_cultural_elements(self):
        if self.cultural_elements:
            return json.loads(self.cultural_elements)
        return {}

    def set_cultural_elements(self, elements_dict):
        if elements_dict:
            self.cultural_elements = json.dumps(elements_dict)
        else:
            self.cultural_elements = "{}"

    def to_dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "style_type": self.style_type,
            "religious_affiliation": self.get_religious_affiliation_list(),
            "cultural_elements": self.get_cultural_elements(),
            "season_support": self.season_support,
            "weather_support": self.weather_support,
            "model_path": self.model_path,
            "low_poly_path": self.low_poly_path,
            "thumbnail": self.thumbnail,
            "creation_date": (
                self.creation_date.isoformat() if self.creation_date else None
            ),
        }
