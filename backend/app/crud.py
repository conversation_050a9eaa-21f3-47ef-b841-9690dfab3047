import uuid
from datetime import datetime
from typing import Any, Generic, TypeVar

from sqlalchemy import func
from sqlalchemy.orm import Session

from . import models_sqlalchemy as models
from . import schemas_pydantic as schemas

ModelType = TypeVar("ModelType", bound=models.Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=schemas.BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=schemas.BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: type[ModelType]):
        """
        CRUD object with default methods to Create, Read, Update, Delete (CRUD).

        **Parameters**

        * `model`: A SQLAlchemy model class
        """
        self.model = model

    def get(self, db: Session, id: Any) -> ModelType | None:
        result = db.query(self.model).filter(self.model.id == id).first()
        return result  # 返回类型已经正确，不需要类型转换

    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> list[ModelType]:
        result = db.query(self.model).offset(skip).limit(limit).all()
        return list(result)  # 确保返回类型为 list

    def get_multi_by_owner(
        self, db: Session, *, owner_id: uuid.UUID, skip: int = 0, limit: int = 100
    ) -> list[ModelType]:
        # Assuming the model has an 'owner_id' or 'creator_id' field
        if hasattr(self.model, "creator_id"):
            result = (
                db.query(self.model)
                .filter(self.model.creator_id == owner_id)
                .offset(skip)
                .limit(limit)
                .all()
            )
            return list(result)  # 确保返回类型为 list
        elif hasattr(self.model, "owner_id"):
            result = (
                db.query(self.model)
                .filter(self.model.owner_id == owner_id)
                .offset(skip)
                .limit(limit)
                .all()
            )
            return list(result)  # 确保返回类型为 list
        else:
            # Fallback if no owner field is found
            return []

    def create(self, db: Session, *, obj_in: CreateSchemaType, **kwargs) -> ModelType:
        obj_in_data = obj_in.dict()
        obj_in_data.update(kwargs)  # Allow passing additional fields like creator_id
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj  # 返回类型已经正确

    def update(
        self, db: Session, *, db_obj: ModelType, obj_in: UpdateSchemaType
    ) -> ModelType:
        obj_data = db_obj.__dict__  # or use a to_dict method if available
        update_data = obj_in.dict(exclude_unset=True)
        for field in obj_data:
            if field in update_data:
                setattr(db_obj, field, update_data[field])
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, id: Any) -> ModelType | None:
        obj = db.query(self.model).get(id)
        if obj:
            db.delete(obj)
            db.commit()
        return obj  # 返回类型已经正确


# --- MemorialSpace CRUD ---
class CRUDMemorialSpace(
    CRUDBase[
        models.MemorialSpace, schemas.MemorialSpaceCreate, schemas.MemorialSpaceUpdate
    ]
):
    def create_with_creator(
        self, db: Session, *, obj_in: schemas.MemorialSpaceCreate, creator_id: uuid.UUID
    ) -> models.MemorialSpace:
        from app.core.security import get_password_hash

        # Convert Pydantic schema to dict, ensuring enums are converted to their values if necessary
        obj_in_data = obj_in.dict()
        # Handle enums explicitly if they are not automatically converted to string values by .dict()
        if isinstance(obj_in_data.get("deceased_gender"), schemas.DeceasedGenderEnum):
            obj_in_data["deceased_gender"] = obj_in_data["deceased_gender"].value
        if isinstance(obj_in_data.get("privacy_level"), schemas.PrivacyLevelEnum):
            obj_in_data["privacy_level"] = obj_in_data["privacy_level"].value

        # 处理访问密码：如果设置了密码，则进行哈希
        if obj_in_data.get("access_password"):
            obj_in_data["access_password"] = get_password_hash(
                obj_in_data["access_password"]
            )

        # 处理自定义设置：确保JSON序列化
        if obj_in_data.get("custom_settings"):
            # Pydantic对象转换为字典以便JSON存储
            if hasattr(obj_in_data["custom_settings"], "dict"):
                obj_in_data["custom_settings"] = obj_in_data["custom_settings"].dict()

        db_obj = self.model(**obj_in_data, creator_id=creator_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_multi_by_creator(
        self, db: Session, *, creator_id: uuid.UUID, skip: int = 0, limit: int = 100
    ) -> list[models.MemorialSpace]:
        return (
            db.query(self.model)
            .filter(models.MemorialSpace.creator_id == creator_id)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def count_by_space(self, db: Session, *, space_id: uuid.UUID) -> int:
        return (
            db.query(func.count(self.model.id))
            .filter(models.MemorialEvent.memorial_space_id == space_id)
            .scalar()
            or 0
        )

    def verify_access_password(
        self, db: Session, *, space: models.MemorialSpace, password: str
    ) -> bool:
        """验证纪念空间访问密码"""
        from app.core.security import verify_password

        if not space.access_password:
            return True  # 无密码保护

        return verify_password(password, space.access_password)

    def update_custom_settings(
        self,
        db: Session,
        *,
        space: models.MemorialSpace,
        settings: schemas.MemorialCustomSettings,
    ) -> models.MemorialSpace:
        """更新纪念空间自定义设置"""
        # 转换Pydantic对象为字典
        settings_dict = settings.dict(exclude_unset=True) if settings else {}

        # 合并现有设置
        current_settings = space.custom_settings or {}
        if isinstance(current_settings, dict):
            current_settings.update(settings_dict)
        else:
            current_settings = settings_dict

        space.custom_settings = current_settings
        db.add(space)
        db.commit()
        db.refresh(space)
        return space

    def increment_visit_count(
        self, db: Session, *, space: models.MemorialSpace
    ) -> models.MemorialSpace:
        """增加访问次数"""
        space.visit_count = (space.visit_count or 0) + 1
        db.add(space)
        db.commit()
        db.refresh(space)
        return space

    def get_public_spaces(
        self, db: Session, *, skip: int = 0, limit: int = 100, search: str | None = None
    ) -> list[models.MemorialSpace]:
        """获取公开的纪念空间"""
        query = db.query(self.model).filter(
            models.MemorialSpace.privacy_level == "public",
            models.MemorialSpace.is_active == True,
        )

        if search:
            query = query.filter(
                models.MemorialSpace.deceased_name.ilike(f"%{search}%")
            )

        return (
            query.order_by(models.MemorialSpace.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_space_with_access_check(
        self,
        db: Session,
        *,
        space_id: uuid.UUID,
        user: models.User,
        access_password: str | None = None,
    ) -> models.MemorialSpace | None:
        """获取纪念空间并检查访问权限"""
        space = self.get(db, space_id)
        if not space or not space.is_active:
            return None

        # 创建者总是有权限
        if space.creator_id == user.id:
            return space

        # 根据隐私级别检查权限
        if space.privacy_level == "public":
            return space
        elif space.privacy_level == "password":
            if access_password and self.verify_access_password(
                db, space=space, password=access_password
            ):
                return space
        elif space.privacy_level == "family":
            # TODO: 实现家族成员检查
            # 暂时允许所有认证用户
            return space

        return None  # 无权限访问


memorial_space = CRUDMemorialSpace(models.MemorialSpace)


# --- MemorialAsset CRUD ---
class CRUDMemorialAsset(
    CRUDBase[
        models.MemorialAsset, schemas.MemorialAssetCreate, schemas.MemorialAssetUpdate
    ]
):
    def create_with_uploader_and_space(
        self,
        db: Session,
        *,
        obj_in: schemas.MemorialAssetCreate,
        uploader_id: uuid.UUID,
        space_id: uuid.UUID,
        file_url: str,
        original_filename: str | None = None,
        file_size: int | None = None,
        thumbnail_url: str | None = None,
    ) -> models.MemorialAsset:
        obj_in_data = obj_in.dict(exclude_unset=True)
        if isinstance(obj_in_data.get("asset_type"), schemas.AssetTypeEnum):
            obj_in_data["asset_type"] = obj_in_data["asset_type"].value

        db_obj = self.model(
            **obj_in_data,
            uploader_id=uploader_id,
            space_id=space_id,
            file_url=file_url,
            original_filename=original_filename,
            file_size=file_size,
            thumbnail_url=thumbnail_url,
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_multi_by_space(
        self,
        db: Session,
        *,
        space_id: uuid.UUID,
        asset_type: schemas.AssetTypeEnum | None = None,
        skip: int = 0,
        limit: int = 100,
    ) -> list[models.MemorialAsset]:
        query = db.query(self.model).filter(models.MemorialAsset.space_id == space_id)
        if asset_type:
            query = query.filter(
                models.MemorialAsset.asset_type == asset_type.value
                if isinstance(asset_type, schemas.AssetTypeEnum)
                else asset_type
            )
        return (
            query.order_by(
                models.MemorialAsset.display_order, models.MemorialAsset.created_at
            )
            .offset(skip)
            .limit(limit)
            .all()
        )

    def count_by_space(
        self,
        db: Session,
        *,
        space_id: uuid.UUID,
        asset_type: schemas.AssetTypeEnum | None = None,
    ) -> int:
        query = db.query(func.count(self.model.id)).filter(
            models.MemorialAsset.space_id == space_id
        )
        if asset_type:
            query = query.filter(
                models.MemorialAsset.asset_type == asset_type.value
                if isinstance(asset_type, schemas.AssetTypeEnum)
                else asset_type
            )
        return query.scalar() or 0

    def update_cover_image_for_space(
        self, db: Session, *, space_id: uuid.UUID, cover_image_url: str | None
    ) -> models.MemorialSpace | None:
        space = (
            db.query(models.MemorialSpace)
            .filter(models.MemorialSpace.id == space_id)
            .first()
        )
        if space:
            space.cover_image_url = cover_image_url
            db.add(space)
            db.commit()
            db.refresh(space)
        return space


memorial_asset = CRUDMemorialAsset(models.MemorialAsset)


# --- MemorialEvent CRUD ---
class CRUDMemorialEvent(
    CRUDBase[
        models.MemorialEvent, schemas.MemorialEventCreate, schemas.MemorialEventUpdate
    ]
):
    def create_for_space(
        self, db: Session, *, obj_in: schemas.MemorialEventCreate, space_id: uuid.UUID
    ) -> models.MemorialEvent:
        obj_in_data = obj_in.dict()
        db_obj = self.model(**obj_in_data, memorial_space_id=space_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_multi_by_space(
        self, db: Session, *, space_id: uuid.UUID, skip: int = 0, limit: int = 100
    ) -> list[models.MemorialEvent]:
        return (
            db.query(self.model)
            .filter(models.MemorialEvent.memorial_space_id == space_id)
            .order_by(models.MemorialEvent.event_date)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def count_by_space(self, db: Session, *, space_id: uuid.UUID) -> int:
        return (
            db.query(func.count(self.model.id))
            .filter(models.MemorialEvent.memorial_space_id == space_id)
            .scalar()
            or 0
        )


memorial_event = CRUDMemorialEvent(models.MemorialEvent)

# --- User CRUD (Example, assuming User model and schemas are defined) ---
# class CRUDUser(CRUDBase[models.User, schemas.UserCreate, schemas.UserUpdate]):
#     def get_by_email(self, db: Session, *, email: str) -> Optional[models.User]:
#         return db.query(models.User).filter(models.User.email == email).first()

#     def get_by_username(self, db: Session, *, username: str) -> Optional[models.User]:
#         return db.query(models.User).filter(models.User.username == username).first()

#     def create(self, db: Session, *, obj_in: schemas.UserCreate) -> models.User:
#         # Hash password before saving, etc.
#         # create_data = obj_in.dict()
#         # create_data.pop('password') # Remove plain password
#         # db_obj = models.User(**create_data, hashed_password=get_password_hash(obj_in.password))
#         # ... (actual user creation logic)
#         pass


class CRUDUser(CRUDBase[models.User, schemas.UserCreate, schemas.UserUpdate]):
    def get_by_email(self, db: Session, *, email: str) -> models.User | None:
        result = db.query(models.User).filter(models.User.email == email).first()
        return result  # 返回类型已经正确

    def get_by_username(self, db: Session, *, username: str) -> models.User | None:
        result = db.query(models.User).filter(models.User.username == username).first()
        return result  # 返回类型已经正确

    def create(
        self, db: Session, *, obj_in: schemas.UserCreate, **kwargs
    ) -> models.User:
        from app.core.security import get_password_hash

        # 添加**kwargs参数，使签名与父类兼容
        db_obj = models.User(
            email=obj_in.email,
            username=obj_in.username,
            password_hash=get_password_hash(obj_in.password),
            full_name=obj_in.full_name,
            is_active=obj_in.is_active if hasattr(obj_in, "is_active") else True,
            is_verified=obj_in.is_verified if hasattr(obj_in, "is_verified") else False,
            role=obj_in.role if hasattr(obj_in, "role") else "user",
            is_superuser=(
                obj_in.is_superuser if hasattr(obj_in, "is_superuser") else False
            ),
            # 移除数据库中不存在的字段
            # phone=getattr(obj_in, 'phone', None),  # 数据库中没有此字段
            avatar_url=getattr(obj_in, 'avatar_url', None),
            bio=getattr(obj_in, 'bio', None),
            **kwargs,  # 允许传入额外的参数
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def authenticate(
        self, db: Session, *, email_or_username: str, password: str
    ) -> models.User | None:
        from app.core.security import verify_password

        user = self.get_by_email(db, email=email_or_username)
        if not user:
            user = self.get_by_username(db, username=email_or_username)
        if not user:
            return None
        if not verify_password(password, str(user.password_hash)):
            return None
        return user

    def is_active(self, user: models.User) -> bool:
        result = user.is_active
        return bool(result)  # 确保返回类型为 bool

    def is_superuser(self, user: models.User) -> bool:
        result = user.is_superuser
        return bool(result)  # 确保返回类型为 bool

    def update_last_login(self, db: Session, *, user: models.User) -> models.User:
        """更新用户最后登录时间"""
        from datetime import datetime

        user.last_login_at = datetime.utcnow()
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def set_password_reset_token(
        self, db: Session, *, user: models.User, token: str, expires_at
    ) -> models.User:
        """设置密码重置Token"""
        user.password_reset_token = token
        user.password_reset_expires_at = expires_at
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def clear_password_reset_token(
        self, db: Session, *, user: models.User
    ) -> models.User:
        """清除密码重置Token"""
        user.password_reset_token = None
        user.password_reset_expires_at = None
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def set_email_verification_token(
        self, db: Session, *, user: models.User, token: str, expires_at
    ) -> models.User:
        """设置邮箱验证Token"""
        user.email_verification_token = token
        user.email_verification_expires_at = expires_at
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def verify_email(self, db: Session, *, user: models.User) -> models.User:
        """验证邮箱"""
        user.is_email_verified = True
        user.email_verification_token = None
        user.email_verification_expires_at = None
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def set_refresh_token(
        self, db: Session, *, user: models.User, token_hash: str, expires_at
    ) -> models.User:
        """设置刷新Token"""
        user.refresh_token_hash = token_hash
        user.refresh_token_expires_at = expires_at
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def clear_refresh_token(self, db: Session, *, user: models.User) -> models.User:
        """清除刷新Token"""
        user.refresh_token_hash = None
        user.refresh_token_expires_at = None
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def update_password(
        self, db: Session, *, user: models.User, new_password: str
    ) -> models.User:
        """更新用户密码"""
        from app.core.security import get_password_hash

        user.password_hash = get_password_hash(new_password)
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    def get_by_password_reset_token(
        self, db: Session, *, token: str
    ) -> models.User | None:
        """通过密码重置Token查找用户"""
        from datetime import datetime

        result = (
            db.query(models.User)
            .filter(
                models.User.password_reset_token == token,
                models.User.password_reset_expires_at > datetime.utcnow(),
            )
            .first()
        )
        return result

    def get_by_email_verification_token(
        self, db: Session, *, token: str
    ) -> models.User | None:
        """通过邮箱验证Token查找用户"""
        from datetime import datetime

        result = (
            db.query(models.User)
            .filter(
                models.User.email_verification_token == token,
                models.User.email_verification_expires_at > datetime.utcnow(),
            )
            .first()
        )
        return result


user = CRUDUser(models.User)


# --- Tribute CRUD Functions ---
def get_memorial_space(db: Session, space_id: uuid.UUID) -> models.MemorialSpace | None:
    """获取纪念空间"""
    return (
        db.query(models.MemorialSpace)
        .filter(models.MemorialSpace.id == space_id)
        .first()
    )


def can_access_memorial_space(
    db: Session, space: models.MemorialSpace, user: models.User
) -> bool:
    """检查用户是否有权限访问纪念空间（简化版本，保持向后兼容）"""
    return check_memorial_space_access(db, space, user).access_granted


def check_memorial_space_access(
    db: Session,
    space: models.MemorialSpace,
    user: models.User,
    access_type: str = "view",
    access_password: str | None = None,
    ip_address: str | None = None,
    user_agent: str | None = None,
) -> schemas.AccessCheckResponse:
    """增强的纪念空间访问权限检查"""
    from app.schemas_pydantic.permission import AccessCheckResponse, PermissionType

    # 创建者总是有完全权限
    if space.creator_id == user.id:
        log_access_attempt(db, space, user, access_type, True, ip_address, user_agent)
        return AccessCheckResponse(
            access_granted=True, permission_level=PermissionType.manage
        )

    # 检查空间是否激活
    if not space.is_active:
        log_access_attempt(
            db,
            space,
            user,
            access_type,
            False,
            ip_address,
            user_agent,
            "Space deactivated",
        )
        return AccessCheckResponse(access_granted=False, denial_reason="纪念空间已被停用")

    # 公开空间的权限检查
    if space.privacy_level == "public":
        # 检查是否有特定权限限制
        permission = get_user_permission(db, space.id, user.id)
        if permission and not permission.is_active:
            log_access_attempt(
                db,
                space,
                user,
                access_type,
                False,
                ip_address,
                user_agent,
                "Permission revoked",
            )
            return AccessCheckResponse(access_granted=False, denial_reason="权限已被撤销")

        log_access_attempt(db, space, user, access_type, True, ip_address, user_agent)
        return AccessCheckResponse(
            access_granted=True, permission_level=PermissionType.view
        )

    # 密码保护的空间
    if space.privacy_level == "password":
        if access_password:
            password_valid = memorial_space.verify_access_password(
                db, space=space, password=access_password
            )
            if password_valid:
                log_access_attempt(
                    db, space, user, access_type, True, ip_address, user_agent
                )
                return AccessCheckResponse(
                    access_granted=True, permission_level=PermissionType.view
                )
            else:
                log_access_attempt(
                    db,
                    space,
                    user,
                    access_type,
                    False,
                    ip_address,
                    user_agent,
                    "Invalid password",
                )
                return AccessCheckResponse(access_granted=False, denial_reason="密码错误")
        else:
            return AccessCheckResponse(
                access_granted=False, requires_password=True, denial_reason="需要访问密码"
            )

    # 家族成员访问
    if space.privacy_level == "family":
        # 检查是否有明确的权限授权
        permission = get_user_permission(db, space.id, user.id)
        if permission and permission.is_active:
            # 检查权限是否过期
            if permission.expires_at and permission.expires_at < datetime.utcnow():
                log_access_attempt(
                    db,
                    space,
                    user,
                    access_type,
                    False,
                    ip_address,
                    user_agent,
                    "Permission expired",
                )
                return AccessCheckResponse(access_granted=False, denial_reason="权限已过期")

            log_access_attempt(
                db, space, user, access_type, True, ip_address, user_agent
            )
            return AccessCheckResponse(
                access_granted=True,
                permission_level=PermissionType(permission.permission_type),
            )
        else:
            return AccessCheckResponse(
                access_granted=False,
                requires_family_verification=True,
                denial_reason="需要家族成员验证",
            )

    # 私密空间 - 只有明确授权的用户可以访问
    if space.privacy_level == "private":
        permission = get_user_permission(db, space.id, user.id)
        if permission and permission.is_active:
            # 检查权限是否过期
            if permission.expires_at and permission.expires_at < datetime.utcnow():
                log_access_attempt(
                    db,
                    space,
                    user,
                    access_type,
                    False,
                    ip_address,
                    user_agent,
                    "Permission expired",
                )
                return AccessCheckResponse(access_granted=False, denial_reason="权限已过期")

            # 检查访问类型是否被允许
            if access_type in ["edit", "manage"] and permission.permission_type not in [
                "edit",
                "manage",
            ]:
                log_access_attempt(
                    db,
                    space,
                    user,
                    access_type,
                    False,
                    ip_address,
                    user_agent,
                    "Insufficient permission level",
                )
                return AccessCheckResponse(access_granted=False, denial_reason="权限级别不足")

            log_access_attempt(
                db, space, user, access_type, True, ip_address, user_agent
            )
            return AccessCheckResponse(
                access_granted=True,
                permission_level=PermissionType(permission.permission_type),
            )

    # 默认拒绝访问
    log_access_attempt(
        db, space, user, access_type, False, ip_address, user_agent, "Access denied"
    )
    return AccessCheckResponse(access_granted=False, denial_reason="无权限访问该纪念空间")


def get_user_permission(
    db: Session, space_id: uuid.UUID, user_id: uuid.UUID
) -> models.MemorialSpacePermission | None:
    """获取用户对特定纪念空间的权限"""
    return (
        db.query(models.MemorialSpacePermission)
        .filter(
            models.MemorialSpacePermission.memorial_space_id == space_id,
            models.MemorialSpacePermission.user_id == user_id,
            models.MemorialSpacePermission.is_active == True,
        )
        .order_by(models.MemorialSpacePermission.granted_at.desc())
        .first()
    )


def grant_permission(
    db: Session,
    space_id: uuid.UUID,
    user_id: uuid.UUID,
    permission_data: schemas.PermissionCreate,
    granted_by: uuid.UUID,
) -> models.MemorialSpacePermission:
    """授予用户权限"""
    # 检查是否已有权限，如有则更新
    existing_permission = get_user_permission(db, space_id, user_id)
    if existing_permission:
        # 更新现有权限
        existing_permission.permission_type = permission_data.permission_type
        existing_permission.expires_at = permission_data.expires_at
        existing_permission.can_view_private_info = (
            permission_data.can_view_private_info
        )
        existing_permission.can_moderate = permission_data.can_moderate
        existing_permission.can_invite_others = permission_data.can_invite_others
        existing_permission.granted_by = granted_by
        existing_permission.granted_at = datetime.utcnow()
        existing_permission.is_active = True

        db.add(existing_permission)
        db.commit()
        db.refresh(existing_permission)
        return existing_permission
    else:
        # 创建新权限
        permission = models.MemorialSpacePermission(
            memorial_space_id=space_id,
            user_id=user_id,
            permission_type=permission_data.permission_type,
            granted_by=granted_by,
            expires_at=permission_data.expires_at,
            can_view_private_info=permission_data.can_view_private_info,
            can_moderate=permission_data.can_moderate,
            can_invite_others=permission_data.can_invite_others,
        )
        db.add(permission)
        db.commit()
        db.refresh(permission)
        return permission


def revoke_permission(db: Session, space_id: uuid.UUID, user_id: uuid.UUID) -> bool:
    """撤销用户权限"""
    permission = get_user_permission(db, space_id, user_id)
    if permission:
        permission.is_active = False
        db.add(permission)
        db.commit()
        return True
    return False


def log_access_attempt(
    db: Session,
    space: models.MemorialSpace,
    user: models.User | None,
    access_type: str,
    access_granted: bool,
    ip_address: str | None = None,
    user_agent: str | None = None,
    denial_reason: str | None = None,
):
    """记录访问尝试"""
    log_entry = models.AccessLog(
        memorial_space_id=space.id,
        user_id=user.id if user else None,
        access_type=access_type,
        ip_address=ip_address,
        user_agent=user_agent,
        access_granted=access_granted,
        denial_reason=denial_reason,
    )
    db.add(log_entry)
    db.commit()


def get_access_logs(
    db: Session, space_id: uuid.UUID, skip: int = 0, limit: int = 100
) -> list[models.AccessLog]:
    """获取访问日志"""
    return (
        db.query(models.AccessLog)
        .filter(models.AccessLog.memorial_space_id == space_id)
        .order_by(models.AccessLog.accessed_at.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )


def create_tribute(
    db: Session,
    space_id: uuid.UUID,
    user_id: uuid.UUID,
    tribute_data: schemas.TributeCreateRequest,
) -> models.TributeRecord:
    """创建祭拜记录"""
    # 处理祭拜物品数据
    tribute_items_data = None
    if tribute_data.tribute_items:
        tribute_items_data = [item.dict() for item in tribute_data.tribute_items]

    # 处理客户端信息
    client_info_data = None
    if tribute_data.client_info:
        client_info_data = tribute_data.client_info.dict()

    db_obj = models.TributeRecord(
        memorial_space_id=space_id,
        user_id=user_id,
        tribute_type=tribute_data.tribute_type,
        message=tribute_data.message,
        is_anonymous=tribute_data.is_anonymous,
        tribute_items=tribute_items_data,
        duration_seconds=tribute_data.duration_seconds or 0,
        coordinates=tribute_data.coordinates,
        client_info=client_info_data,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def get_tributes_by_space(
    db: Session, space_id: uuid.UUID, skip: int = 0, limit: int = 50
) -> list[models.TributeRecord]:
    """获取指定纪念空间的祭拜记录"""
    return (
        db.query(models.TributeRecord)
        .filter(models.TributeRecord.memorial_space_id == space_id)
        .order_by(models.TributeRecord.created_at.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )


def count_tributes_by_space(db: Session, space_id: uuid.UUID) -> int:
    """统计指定纪念空间的祭拜记录数量"""
    return (
        db.query(func.count(models.TributeRecord.id))
        .filter(models.TributeRecord.memorial_space_id == space_id)
        .scalar()
        or 0
    )


def get_tribute_stats(db: Session, space_id: uuid.UUID) -> dict:
    """获取祭拜统计信息"""
    # 总祭拜次数
    total_tributes = count_tributes_by_space(db, space_id)

    # 按类型统计
    tribute_types = (
        db.query(
            models.TributeRecord.tribute_type,
            func.count(models.TributeRecord.id).label("count"),
        )
        .filter(models.TributeRecord.memorial_space_id == space_id)
        .group_by(models.TributeRecord.tribute_type)
        .all()
    )

    # 最近的祭拜记录
    recent_tributes = get_tributes_by_space(db, space_id, skip=0, limit=5)

    # 平均祭拜持续时间
    avg_duration = (
        db.query(func.avg(models.TributeRecord.duration_seconds))
        .filter(
            models.TributeRecord.memorial_space_id == space_id,
            models.TributeRecord.duration_seconds > 0,
        )
        .scalar()
        or 0
    )

    # 设备类型统计
    device_stats = (
        db.query(
            func.json_extract(models.TributeRecord.client_info, "$.device_type").label(
                "device_type"
            ),
            func.count(models.TributeRecord.id).label("count"),
        )
        .filter(models.TributeRecord.memorial_space_id == space_id)
        .group_by(func.json_extract(models.TributeRecord.client_info, "$.device_type"))
        .all()
    )

    return {
        "total_tributes": total_tributes,
        "tribute_types": {tribute_type: count for tribute_type, count in tribute_types},
        "recent_tributes": recent_tributes,
        "average_duration_seconds": float(avg_duration),
        "device_statistics": {
            device or "unknown": count for device, count in device_stats
        },
    }


def get_tribute_analytics(db: Session, space_id: uuid.UUID, days: int = 30) -> dict:
    """获取祭拜分析数据"""
    from datetime import timedelta

    start_date = datetime.utcnow() - timedelta(days=days)

    # 每日祭拜次数统计
    daily_stats = (
        db.query(
            func.date(models.TributeRecord.created_at).label("date"),
            func.count(models.TributeRecord.id).label("count"),
        )
        .filter(
            models.TributeRecord.memorial_space_id == space_id,
            models.TributeRecord.created_at >= start_date,
        )
        .group_by(func.date(models.TributeRecord.created_at))
        .order_by(func.date(models.TributeRecord.created_at))
        .all()
    )

    # 时段统计（按小时）
    hourly_stats = (
        db.query(
            func.extract("hour", models.TributeRecord.created_at).label("hour"),
            func.count(models.TributeRecord.id).label("count"),
        )
        .filter(
            models.TributeRecord.memorial_space_id == space_id,
            models.TributeRecord.created_at >= start_date,
        )
        .group_by(func.extract("hour", models.TributeRecord.created_at))
        .order_by(func.extract("hour", models.TributeRecord.created_at))
        .all()
    )

    return {
        "daily_tributes": [
            {"date": str(date), "count": count} for date, count in daily_stats
        ],
        "hourly_tributes": [
            {"hour": int(hour), "count": count} for hour, count in hourly_stats
        ],
        "period_days": days,
    }


# =============================================================================
# Message CRUD Functions
# =============================================================================


def create_message(
    db: Session,
    space_id: uuid.UUID,
    user_id: uuid.UUID | None,
    message_data: schemas.MemorialMessageCreateRequest,
) -> models.MemorialMessage:
    """创建纪念留言"""
    db_obj = models.MemorialMessage(
        memorial_space_id=space_id,
        author_id=user_id,
        content=message_data.content,
        is_anonymous=message_data.is_anonymous,
        is_active=True,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def get_message(db: Session, message_id: uuid.UUID) -> models.MemorialMessage | None:
    """获取单个留言"""
    return (
        db.query(models.MemorialMessage)
        .filter(
            models.MemorialMessage.id == message_id,
            models.MemorialMessage.is_active == True,
        )
        .first()
    )


def get_messages_by_space(
    db: Session, space_id: uuid.UUID, skip: int = 0, limit: int = 50
) -> list[models.MemorialMessage]:
    """获取指定纪念空间的留言列表"""
    return (
        db.query(models.MemorialMessage)
        .filter(
            models.MemorialMessage.memorial_space_id == space_id,
            models.MemorialMessage.is_active == True,
        )
        .order_by(models.MemorialMessage.created_at.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )


def count_messages_by_space(db: Session, space_id: uuid.UUID) -> int:
    """统计指定纪念空间的留言数量"""
    return (
        db.query(func.count(models.MemorialMessage.id))
        .filter(
            models.MemorialMessage.memorial_space_id == space_id,
            models.MemorialMessage.is_active == True,
        )
        .scalar()
        or 0
    )


def update_message(
    db: Session,
    message: models.MemorialMessage,
    message_update: schemas.MemorialMessageUpdateRequest,
) -> models.MemorialMessage:
    """更新留言内容"""
    update_data = message_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(message, field, value)

    # 更新时间戳
    message.updated_at = datetime.utcnow()

    db.commit()
    db.refresh(message)
    return message


def delete_message(db: Session, message: models.MemorialMessage) -> None:
    """软删除留言（标记为非激活状态）"""
    message.is_active = False
    message.updated_at = datetime.utcnow()
    db.commit()


def can_access_memorial_space(
    db: Session, space: models.MemorialSpace, user: models.User | None
) -> bool:
    """检查用户是否可以访问纪念空间"""
    # 公开空间任何人都可以访问
    if space.is_public:
        return True

    # 私人空间需要用户认证
    if not user:
        return False

    # 创建者可以访问
    if space.creator_id == user.id:
        return True

    # 检查是否有访问权限（通过家族关系等）
    # 这里可以根据业务需求添加更复杂的权限逻辑
    if hasattr(space, "family_id") and space.family_id:
        # 检查用户是否是家族成员
        family_member = (
            db.query(models.FamilyMember)
            .filter(
                models.FamilyMember.family_id == space.family_id,
                models.FamilyMember.user_id == user.id,
                models.FamilyMember.status == "active",
            )
            .first()
        )
        return family_member is not None

    return False
