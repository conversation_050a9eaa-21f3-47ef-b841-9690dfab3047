# Replicate AI 服务
import os
import logging
from typing import Dict, Any, Optional
import requests
import asyncio
import aiohttp
from pathlib import Path

logger = logging.getLogger(__name__)


class ReplicateAIService:
    """Replicate AI 服务类"""

    def __init__(self):
        self.api_key = os.environ.get(
            "REPLICATE_API_TOKEN", "****************************************"
        )  # 优先使用环境变量
        self.base_url = "https://api.replicate.com/v1"
        self.headers = {
            "Authorization": f"Token {self.api_key}",
            "Content-Type": "application/json",
        }

    async def restore_photo(self, image_url: str) -> Dict[str, Any]:
        """
        使用AI修复老照片
        """
        try:
            # 使用 Real-ESRGAN 模型进行照片修复
            model_version = "nightmareai/real-esrgan:42fed1c4974146d4d2414e2be2c5277c7fcf05fcc972f6b188a581c8b768ce6e"

            data = {
                "version": model_version,
                "input": {"image": image_url, "scale": 4, "face_enhance": True},
            }

            async with aiohttp.ClientSession() as session:
                # 创建预测
                async with session.post(
                    f"{self.base_url}/predictions", headers=self.headers, json=data
                ) as response:
                    if response.status != 201:
                        error_text = await response.text()
                        logger.error(
                            f"Replicate API error: {response.status} - {error_text}"
                        )
                        raise Exception(f"Replicate API error: {response.status}")

                    prediction = await response.json()
                    prediction_id = prediction["id"]

                # 轮询结果
                max_attempts = 60  # 最多等待5分钟
                for attempt in range(max_attempts):
                    async with session.get(
                        f"{self.base_url}/predictions/{prediction_id}",
                        headers=self.headers,
                    ) as status_response:
                        if status_response.status != 200:
                            continue

                        result = await status_response.json()
                        status = result.get("status")

                        if status == "succeeded":
                            return {
                                "success": True,
                                "original_url": image_url,
                                "result_url": result.get("output"),
                                "model_used": "Real-ESRGAN",
                                "options_used": data["input"],
                            }
                        elif status == "failed":
                            error_msg = result.get("error", "Unknown error")
                            logger.error(f"Replicate prediction failed: {error_msg}")
                            return {"success": False, "error": error_msg}
                        elif status in ["starting", "processing"]:
                            # 继续等待
                            await asyncio.sleep(5)
                        else:
                            logger.warning(f"Unknown status: {status}")
                            await asyncio.sleep(5)

                # 超时
                return {"success": False, "error": "请求超时，请稍后重试"}

        except Exception as e:
            logger.error(f"Photo restoration error: {str(e)}")
            return {"success": False, "error": f"照片修复失败: {str(e)}"}

    async def enhance_photo(
        self, image_url: str, scale: int = 4, face_enhance: bool = True
    ) -> Dict[str, Any]:
        """
        使用AI增强照片分辨率
        """
        try:
            # 使用 Real-ESRGAN 模型进行照片增强
            model_version = "nightmareai/real-esrgan:42fed1c4974146d4d2414e2be2c5277c7fcf05fcc972f6b188a581c8b768ce6e"

            data = {
                "version": model_version,
                "input": {
                    "image": image_url,
                    "scale": min(scale, 8),  # 限制最大放大倍数
                    "face_enhance": face_enhance,
                },
            }

            async with aiohttp.ClientSession() as session:
                # 创建预测
                async with session.post(
                    f"{self.base_url}/predictions", headers=self.headers, json=data
                ) as response:
                    if response.status != 201:
                        error_text = await response.text()
                        logger.error(
                            f"Replicate API error: {response.status} - {error_text}"
                        )
                        raise Exception(f"Replicate API error: {response.status}")

                    prediction = await response.json()
                    prediction_id = prediction["id"]

                # 轮询结果
                max_attempts = 60
                for attempt in range(max_attempts):
                    async with session.get(
                        f"{self.base_url}/predictions/{prediction_id}",
                        headers=self.headers,
                    ) as status_response:
                        if status_response.status != 200:
                            continue

                        result = await status_response.json()
                        status = result.get("status")

                        if status == "succeeded":
                            return {
                                "success": True,
                                "original_url": image_url,
                                "result_url": result.get("output"),
                                "model_used": "Real-ESRGAN",
                                "scale_used": scale,
                                "options_used": data["input"],
                            }
                        elif status == "failed":
                            error_msg = result.get("error", "Unknown error")
                            logger.error(f"Replicate prediction failed: {error_msg}")
                            return {"success": False, "error": error_msg}
                        elif status in ["starting", "processing"]:
                            await asyncio.sleep(5)
                        else:
                            await asyncio.sleep(5)

                return {"success": False, "error": "请求超时，请稍后重试"}

        except Exception as e:
            logger.error(f"Photo enhancement error: {str(e)}")
            return {"success": False, "error": f"照片增强失败: {str(e)}"}

    async def remove_background(
        self, image_url: str, model: str = "u2net"
    ) -> Dict[str, Any]:
        """
        使用AI移除照片背景
        """
        try:
            # 使用 REMBG 模型移除背景
            model_version = "cjwbw/rembg:fb8af171cfa1616ddcf1242c093f9c46bcada5ad4cf6f2fbe8b81b330ec5c003"

            data = {
                "version": model_version,
                "input": {"image": image_url, "model": model},
            }

            async with aiohttp.ClientSession() as session:
                # 创建预测
                async with session.post(
                    f"{self.base_url}/predictions", headers=self.headers, json=data
                ) as response:
                    if response.status != 201:
                        error_text = await response.text()
                        logger.error(
                            f"Replicate API error: {response.status} - {error_text}"
                        )
                        raise Exception(f"Replicate API error: {response.status}")

                    prediction = await response.json()
                    prediction_id = prediction["id"]

                # 轮询结果
                max_attempts = 30  # 背景移除通常较快
                for attempt in range(max_attempts):
                    async with session.get(
                        f"{self.base_url}/predictions/{prediction_id}",
                        headers=self.headers,
                    ) as status_response:
                        if status_response.status != 200:
                            continue

                        result = await status_response.json()
                        status = result.get("status")

                        if status == "succeeded":
                            return {
                                "success": True,
                                "original_url": image_url,
                                "result_url": result.get("output"),
                                "model_used": f"REMBG-{model}",
                                "options_used": data["input"],
                            }
                        elif status == "failed":
                            error_msg = result.get("error", "Unknown error")
                            logger.error(f"Replicate prediction failed: {error_msg}")
                            return {"success": False, "error": error_msg}
                        elif status in ["starting", "processing"]:
                            await asyncio.sleep(3)
                        else:
                            await asyncio.sleep(3)

                return {"success": False, "error": "请求超时，请稍后重试"}

        except Exception as e:
            logger.error(f"Background removal error: {str(e)}")
            return {"success": False, "error": f"背景移除失败: {str(e)}"}

    async def colorize_photo(self, image_url: str) -> Dict[str, Any]:
        """
        使用AI为黑白照片上色
        """
        try:
            # 使用 DeOldify 模型为照片上色
            model_version = "microsoft/bringing-old-photos-back-to-life:c75db81db6cbd809d93cc3b7e7a088a351a3349c9fa02b6d393e35e0d51ba799"

            data = {
                "version": model_version,
                "input": {"image": image_url, "HR": True},  # 高分辨率
            }

            async with aiohttp.ClientSession() as session:
                # 创建预测
                async with session.post(
                    f"{self.base_url}/predictions", headers=self.headers, json=data
                ) as response:
                    if response.status != 201:
                        error_text = await response.text()
                        logger.error(
                            f"Replicate API error: {response.status} - {error_text}"
                        )
                        raise Exception(f"Replicate API error: {response.status}")

                    prediction = await response.json()
                    prediction_id = prediction["id"]

                # 轮询结果
                max_attempts = 60
                for attempt in range(max_attempts):
                    async with session.get(
                        f"{self.base_url}/predictions/{prediction_id}",
                        headers=self.headers,
                    ) as status_response:
                        if status_response.status != 200:
                            continue

                        result = await status_response.json()
                        status = result.get("status")

                        if status == "succeeded":
                            return {
                                "success": True,
                                "original_url": image_url,
                                "result_url": result.get("output"),
                                "model_used": "Bringing Old Photos Back to Life",
                                "options_used": data["input"],
                            }
                        elif status == "failed":
                            error_msg = result.get("error", "Unknown error")
                            logger.error(f"Replicate prediction failed: {error_msg}")
                            return {"success": False, "error": error_msg}
                        elif status in ["starting", "processing"]:
                            await asyncio.sleep(5)
                        else:
                            await asyncio.sleep(5)

                return {"success": False, "error": "请求超时，请稍后重试"}

        except Exception as e:
            logger.error(f"Photo colorization error: {str(e)}")
            return {"success": False, "error": f"照片上色失败: {str(e)}"}


# 全局实例
replicate_ai_service = ReplicateAIService()
