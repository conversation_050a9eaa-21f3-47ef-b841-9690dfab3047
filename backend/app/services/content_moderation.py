# -*- coding: utf-8 -*-
"""
内容审核服务模块
提供基础的内容过滤、敏感词检测和内容质量评估功能
"""
import re
import hashlib
from typing import List, Tuple, Optional
from datetime import datetime, timedelta
from enum import Enum

from sqlalchemy.orm import Session
from sqlalchemy import func

from app import models_sqlalchemy as models


class ModerationAction(Enum):
    """审核动作枚举"""

    ALLOW = "allow"  # 允许发布
    REVIEW = "review"  # 需要人工审核
    BLOCK = "block"  # 直接拒绝


class ModerationResult:
    """内容审核结果"""

    def __init__(
        self,
        action: ModerationAction,
        confidence: float,
        reasons: Optional[List[str]] = None,
        filtered_content: Optional[str] = None,
    ):
        self.action = action
        self.confidence = confidence  # 0.0-1.0，置信度
        self.reasons = reasons or []
        self.filtered_content = filtered_content
        self.timestamp = datetime.utcnow()


class ContentModerationService:
    """内容审核服务类"""

    def __init__(self):
        # 基础敏感词库（中文）
        self.profanity_words = {
            # 脏话/骂人话
            "傻逼",
            "傻B",
            "sb",
            "SB",
            "草你",
            "操你",
            "艹你",
            "日你",
            "去死",
            "死全家",
            "白痴",
            "智障",
            "弱智",
            "脑残",
            "神经病",
            "有病",
            "滚蛋",
            "滚开",
            "fuck",
            "shit",
            "bitch",
            "damn",
            "hell",
            "ass",
            "fucking",
            "asshole",
            "bastard",
            # 政治敏感词
            "共产党",
            "中国共产党",
            "政府",
            "习近平",
            "毛泽东",
            "邓小平",
            "江泽民",
            "胡锦涛",
            "六四",
            "89",
            "天安门",
            "法轮功",
            "台独",
            "港独",
            "西藏独立",
            "新疆独立",
            # 宗教极端词汇
            "邪教",
            "异教徒",
            "魔鬼",
            "撒旦",
            "反基督",
            "亵渎",
            "诅咒",
            "下地狱",
            # 歧视性词汇
            "黑鬼",
            "小日本",
            "鬼子",
            "棒子",
            "阿三",
            "猴子",
            "野蛮人",
            # 暴力威胁词汇
            "杀死",
            "谋杀",
            "自杀",
            "炸弹",
            "爆炸",
            "恐怖分子",
            "枪击",
            "刀刺",
            "报复",
            "仇杀",
            "血洗",
            "灭门",
            "屠杀",
        }

        # 可疑词汇（需要进一步审核）
        self.suspicious_words = {
            "钱",
            "转账",
            "付款",
            "收费",
            "金钱",
            "贷款",
            "借钱",
            "欠债",
            "还钱",
            "广告",
            "推广",
            "宣传",
            "营销",
            "销售",
            "购买",
            "商品",
            "产品",
            "联系方式",
            "电话",
            "微信",
            "QQ",
            "邮箱",
            "地址",
            "见面",
            "约会",
            "私聊",
            "加我",
            "找我",
            "联系我",
            "扫码",
            "二维码",
            "链接",
            "网址",
        }

        # 垃圾信息模式
        self.spam_patterns = [
            # 重复字符模式
            r"(.)\1{4,}",  # 同一字符重复5次以上
            # 数字+字母混合（可能是推广码）
            r"[A-Za-z0-9]{10,}",
            # 多个感叹号或问号
            r"[!！]{3,}|[?？]{3,}",
            # URL模式
            r"https?://\S+|www\.\S+|\S+\.com|\S+\.cn|\S+\.net",
            # 电话号码模式
            r"1[3-9]\d{9}|0\d{2,3}-?\d{7,8}",
            # QQ号模式
            r"[Qq][Qq]:?\s*\d{5,}",
            # 微信号模式
            r"[Ww][Xx]:?\s*[A-Za-z0-9_-]{5,}|微信:?\s*[A-Za-z0-9_-]{5,}",
        ]

        # 最大内容长度限制
        self.max_content_length = 500

        # 最小内容长度限制
        self.min_content_length = 2

    def moderate_content(
        self, content: str, user_id: Optional[str] = None
    ) -> ModerationResult:
        """
        对内容进行综合审核

        Args:
            content: 待审核的内容
            user_id: 用户ID（可选，用于用户行为分析）

        Returns:
            ModerationResult: 审核结果
        """
        reasons = []
        confidence = 1.0
        action = ModerationAction.ALLOW
        filtered_content = content

        # 1. 基础内容检查
        if not content or not content.strip():
            return ModerationResult(
                action=ModerationAction.BLOCK, confidence=1.0, reasons=["空内容"]
            )

        content = content.strip()

        # 2. 长度检查
        if len(content) < self.min_content_length:
            return ModerationResult(
                action=ModerationAction.BLOCK, confidence=1.0, reasons=["内容过短"]
            )

        if len(content) > self.max_content_length:
            return ModerationResult(
                action=ModerationAction.BLOCK, confidence=1.0, reasons=["内容过长"]
            )

        # 3. 敏感词检查
        profanity_score, profanity_reasons = self._check_profanity(content)
        if profanity_score > 0:
            reasons.extend(profanity_reasons)
            confidence = min(confidence, 1.0 - profanity_score)

            if profanity_score >= 0.8:  # 高敏感度
                action = ModerationAction.BLOCK
            elif profanity_score >= 0.4:  # 中等敏感度
                action = ModerationAction.REVIEW

        # 4. 垃圾信息检查
        spam_score, spam_reasons = self._check_spam(content)
        if spam_score > 0:
            reasons.extend(spam_reasons)
            confidence = min(confidence, 1.0 - spam_score)

            if spam_score >= 0.6:
                action = ModerationAction.BLOCK
            elif spam_score >= 0.3:
                action = max(action, ModerationAction.REVIEW, key=lambda x: x.value)

        # 5. 可疑内容检查
        suspicious_score, suspicious_reasons = self._check_suspicious(content)
        if suspicious_score > 0:
            reasons.extend(suspicious_reasons)
            confidence = min(confidence, 1.0 - suspicious_score * 0.3)  # 可疑内容影响较小

            if suspicious_score >= 0.7:
                action = max(action, ModerationAction.REVIEW, key=lambda x: x.value)

        # 6. 过滤敏感词
        if action != ModerationAction.BLOCK:
            filtered_content = self._filter_content(content)

        # 7. 最终决策
        if action == ModerationAction.ALLOW and confidence < 0.6:
            action = ModerationAction.REVIEW
            reasons.append("置信度较低，需要人工审核")

        return ModerationResult(
            action=action,
            confidence=confidence,
            reasons=reasons,
            filtered_content=filtered_content,
        )

    def _check_profanity(self, content: str) -> Tuple[float, List[str]]:
        """检查敏感词汇"""
        content_lower = content.lower()
        found_words = []

        for word in self.profanity_words:
            if word.lower() in content_lower:
                found_words.append(word)

        if not found_words:
            return 0.0, []

        # 计算敏感度分数
        score = min(len(found_words) * 0.3, 1.0)
        reasons = [
            f"包含敏感词汇: {', '.join(found_words[:3])}{'...' if len(found_words) > 3 else ''}"
        ]

        return score, reasons

    def _check_spam(self, content: str) -> Tuple[float, List[str]]:
        """检查垃圾信息模式"""
        reasons: List[str] = []
        score = 0.0

        for pattern in self.spam_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                score += 0.2
                if "重复字符" not in str(reasons):
                    reasons.append("包含重复字符或可疑模式")

        return min(score, 1.0), reasons

    def _check_suspicious(self, content: str) -> Tuple[float, List[str]]:
        """检查可疑内容"""
        content_lower = content.lower()
        found_words = []

        for word in self.suspicious_words:
            if word.lower() in content_lower:
                found_words.append(word)

        if not found_words:
            return 0.0, []

        score = min(len(found_words) * 0.15, 1.0)
        reasons = [
            f"包含可疑词汇: {', '.join(found_words[:3])}{'...' if len(found_words) > 3 else ''}"
        ]

        return score, reasons

    def _filter_content(self, content: str) -> str:
        """过滤敏感内容，将敏感词替换为星号"""
        filtered = content

        for word in self.profanity_words:
            if word.lower() in content.lower():
                # 替换为同等长度的星号
                stars = "*" * len(word)
                filtered = re.sub(re.escape(word), stars, filtered, flags=re.IGNORECASE)

        return filtered

    def check_user_behavior(self, db: Session, user_id: str, content: str) -> dict:
        """检查用户行为模式"""
        if not user_id:
            return {"suspicious": False, "reasons": []}

        # 检查用户最近的留言频率
        recent_time = datetime.utcnow() - timedelta(hours=1)
        recent_messages = (
            db.query(func.count(models.MemorialMessage.id))
            .filter(
                models.MemorialMessage.author_id == user_id,
                models.MemorialMessage.created_at >= recent_time,
            )
            .scalar()
            or 0
        )

        # 检查内容相似度（简单的哈希比较）
        content_hash = hashlib.md5(content.encode()).hexdigest()
        similar_messages = (
            db.query(func.count(models.MemorialMessage.id))
            .filter(
                models.MemorialMessage.author_id == user_id,
                models.MemorialMessage.created_at >= recent_time,
                func.length(models.MemorialMessage.content) == len(content),
            )
            .scalar()
            or 0
        )

        suspicious_reasons = []

        # 频率过高
        if recent_messages > 10:
            suspicious_reasons.append("发言频率过高")

        # 内容重复
        if similar_messages > 3:
            suspicious_reasons.append("发布重复内容")

        return {
            "suspicious": len(suspicious_reasons) > 0,
            "reasons": suspicious_reasons,
            "recent_count": recent_messages,
            "similar_count": similar_messages,
        }

    def get_moderation_stats(self, db: Session, days: int = 7) -> dict:
        """获取审核统计信息"""
        start_date = datetime.utcnow() - timedelta(days=days)

        # 这里可以添加审核日志统计
        # 目前返回基础统计
        total_messages = (
            db.query(func.count(models.MemorialMessage.id))
            .filter(models.MemorialMessage.created_at >= start_date)
            .scalar()
            or 0
        )

        active_messages = (
            db.query(func.count(models.MemorialMessage.id))
            .filter(
                models.MemorialMessage.created_at >= start_date,
                models.MemorialMessage.is_active == True,
            )
            .scalar()
            or 0
        )

        return {
            "total_messages": total_messages,
            "active_messages": active_messages,
            "blocked_messages": total_messages - active_messages,
            "block_rate": (total_messages - active_messages)
            / max(total_messages, 1)
            * 100,
            "period_days": days,
        }


# 全局实例
content_moderator = ContentModerationService()


def moderate_message_content(
    content: str, user_id: Optional[str] = None
) -> ModerationResult:
    """
    便捷函数：审核留言内容

    Args:
        content: 留言内容
        user_id: 用户ID（可选）

    Returns:
        ModerationResult: 审核结果
    """
    return content_moderator.moderate_content(content, user_id)


def filter_sensitive_content(content: str) -> str:
    """
    便捷函数：过滤敏感内容

    Args:
        content: 原始内容

    Returns:
        str: 过滤后的内容
    """
    return content_moderator._filter_content(content)
