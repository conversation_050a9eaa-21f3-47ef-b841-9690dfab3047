import logging
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText

from app.core.config import settings

logger = logging.getLogger(__name__)


class EmailService:
    """邮件服务类，用于发送各种类型的邮件"""

    def __init__(self):
        self.smtp_host = settings.SMTP_HOST
        self.smtp_port = settings.SMTP_PORT
        self.smtp_user = settings.SMTP_USER
        self.smtp_password = settings.SMTP_PASSWORD
        self.smtp_tls = settings.SMTP_TLS
        self.from_email = settings.EMAILS_FROM_EMAIL
        self.from_name = settings.EMAILS_FROM_NAME

    def _create_smtp_connection(self) -> smtplib.SMTP:
        """创建SMTP连接"""
        try:
            server = smtplib.SMTP(self.smtp_host, self.smtp_port)
            if self.smtp_tls:
                server.starttls()
            if self.smtp_user and self.smtp_password:
                server.login(self.smtp_user, self.smtp_password)
            return server
        except Exception as e:
            logger.error(f"Failed to create SMTP connection: {e}")
            raise

    def send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        text_content: str | None = None,
    ) -> bool:
        """发送邮件

        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            html_content: HTML格式邮件内容
            text_content: 纯文本格式邮件内容（可选）

        Returns:
            bool: 发送成功返回True，失败返回False
        """
        if not settings.EMAILS_ENABLED:
            logger.warning("Email service is disabled")
            return False

        try:
            # 创建邮件消息
            msg = MIMEMultipart("alternative")
            msg["Subject"] = subject
            msg["From"] = f"{self.from_name} <{self.from_email}>"
            msg["To"] = to_email

            # 添加纯文本内容
            if text_content:
                text_part = MIMEText(text_content, "plain", "utf-8")
                msg.attach(text_part)

            # 添加HTML内容
            html_part = MIMEText(html_content, "html", "utf-8")
            msg.attach(html_part)

            # 发送邮件
            with self._create_smtp_connection() as server:
                server.send_message(msg)

            logger.info(f"Email sent successfully to {to_email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {e}")
            return False

    def send_password_reset_email(
        self, to_email: str, reset_token: str, username: str
    ) -> bool:
        """发送密码重置邮件

        Args:
            to_email: 收件人邮箱
            reset_token: 密码重置令牌
            username: 用户名

        Returns:
            bool: 发送成功返回True，失败返回False
        """
        # 构建重置链接
        reset_url = f"{settings.FRONTEND_HOST}/reset-password?token={reset_token}"

        # HTML邮件模板
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>密码重置 - 归处</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #4F46E5; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 30px; background-color: #f9f9f9; }}
                .button {{ 
                    display: inline-block; 
                    background-color: #4F46E5; 
                    color: white; 
                    padding: 12px 30px; 
                    text-decoration: none; 
                    border-radius: 5px; 
                    margin: 20px 0;
                }}
                .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>归处 - 密码重置</h1>
                </div>
                <div class="content">
                    <h2>您好，{username}！</h2>
                    <p>我们收到了您的密码重置请求。请点击下面的按钮来重置您的密码：</p>
                    <p style="text-align: center;">
                        <a href="{reset_url}" class="button">重置密码</a>
                    </p>
                    <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                    <p style="word-break: break-all; background-color: #f0f0f0; padding: 10px; border-radius: 3px;">
                        {reset_url}
                    </p>
                    <p><strong>注意：</strong></p>
                    <ul>
                        <li>此链接将在 {settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS} 小时后失效</li>
                        <li>如果您没有请求密码重置，请忽略此邮件</li>
                        <li>为了您的账户安全，请不要将此链接分享给他人</li>
                    </ul>
                </div>
                <div class="footer">
                    <p>此邮件由系统自动发送，请勿回复。</p>
                    <p>© 2024 归处 - 永恒的记忆，温暖的怀念</p>
                </div>
            </div>
        </body>
        </html>
        """

        # 纯文本版本
        text_content = f"""
        归处 - 密码重置
        
        您好，{username}！
        
        我们收到了您的密码重置请求。请访问以下链接来重置您的密码：
        
        {reset_url}
        
        注意：
        - 此链接将在 {settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS} 小时后失效
        - 如果您没有请求密码重置，请忽略此邮件
        - 为了您的账户安全，请不要将此链接分享给他人
        
        此邮件由系统自动发送，请勿回复。
        © 2024 归处 - 永恒的记忆，温暖的怀念
        """

        return self.send_email(
            to_email=to_email,
            subject="归处 - 密码重置请求",
            html_content=html_content,
            text_content=text_content,
        )

    def send_verification_email(
        self, to_email: str, verification_token: str, username: str
    ) -> bool:
        """发送邮箱验证邮件

        Args:
            to_email: 收件人邮箱
            verification_token: 验证令牌
            username: 用户名

        Returns:
            bool: 发送成功返回True，失败返回False
        """
        # 构建验证链接
        verify_url = f"{settings.FRONTEND_HOST}/verify-email?token={verification_token}"

        # HTML邮件模板
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>邮箱验证 - 归处</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #10B981; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 30px; background-color: #f9f9f9; }}
                .button {{ 
                    display: inline-block; 
                    background-color: #10B981; 
                    color: white; 
                    padding: 12px 30px; 
                    text-decoration: none; 
                    border-radius: 5px; 
                    margin: 20px 0;
                }}
                .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>归处 - 邮箱验证</h1>
                </div>
                <div class="content">
                    <h2>欢迎加入归处，{username}！</h2>
                    <p>感谢您注册归处账户。为了确保您的账户安全，请点击下面的按钮验证您的邮箱：</p>
                    <p style="text-align: center;">
                        <a href="{verify_url}" class="button">验证邮箱</a>
                    </p>
                    <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                    <p style="word-break: break-all; background-color: #f0f0f0; padding: 10px; border-radius: 3px;">
                        {verify_url}
                    </p>
                    <p><strong>注意：</strong></p>
                    <ul>
                        <li>此验证链接将在24小时后失效</li>
                        <li>验证成功后，您就可以正常使用归处的所有功能了</li>
                        <li>如果您没有注册归处账户，请忽略此邮件</li>
                    </ul>
                </div>
                <div class="footer">
                    <p>此邮件由系统自动发送，请勿回复。</p>
                    <p>© 2024 归处 - 永恒的记忆，温暖的怀念</p>
                </div>
            </div>
        </body>
        </html>
        """

        # 纯文本版本
        text_content = f"""
        归处 - 邮箱验证
        
        欢迎加入归处，{username}！
        
        感谢您注册归处账户。为了确保您的账户安全，请访问以下链接验证您的邮箱：
        
        {verify_url}
        
        注意：
        - 此验证链接将在24小时后失效
        - 验证成功后，您就可以正常使用归处的所有功能了
        - 如果您没有注册归处账户，请忽略此邮件
        
        此邮件由系统自动发送，请勿回复。
        © 2024 归处 - 永恒的记忆，温暖的怀念
        """

        return self.send_email(
            to_email=to_email,
            subject="归处 - 请验证您的邮箱",
            html_content=html_content,
            text_content=text_content,
        )


# 创建全局邮件服务实例
email_service = EmailService()
