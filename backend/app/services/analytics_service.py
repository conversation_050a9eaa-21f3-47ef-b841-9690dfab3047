# 商业化数据分析服务
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json
from sqlalchemy.orm import Session
from sqlalchemy import func, text
from app.models_sqlalchemy import User
from app.services.membership_service import MembershipService, MembershipTier


class AnalyticsService:
    """商业化数据分析服务"""

    def __init__(self, db: Session):
        self.db = db
        self.membership_service = MembershipService(db)

    def get_business_analytics(self, date_range: str = "30d") -> Dict[str, Any]:
        """获取商业化分析数据"""
        end_date = datetime.now()

        # 解析日期范围
        if date_range == "7d":
            start_date = end_date - timedelta(days=7)
        elif date_range == "30d":
            start_date = end_date - timedelta(days=30)
        elif date_range == "90d":
            start_date = end_date - timedelta(days=90)
        elif date_range == "1y":
            start_date = end_date - timedelta(days=365)
        else:
            start_date = end_date - timedelta(days=30)

        return {
            "revenue": self._get_revenue_metrics(start_date, end_date),
            "users": self._get_user_metrics(start_date, end_date),
            "conversion": self._get_conversion_metrics(start_date, end_date),
            "retention": self._get_retention_metrics(start_date, end_date),
            "products": self._get_product_metrics(start_date, end_date),
            "growth": self._get_growth_metrics(start_date, end_date),
            "membership": self._get_membership_analytics(start_date, end_date),
        }

    def _get_revenue_metrics(
        self, start_date: datetime, end_date: datetime
    ) -> Dict[str, Any]:
        """获取收入指标"""
        # 模拟数据 - 在实际实现中应该从数据库查询
        return {
            "mrr": 125000,  # 月度经常性收入
            "arr": 1500000,  # 年度经常性收入
            "total_revenue": 2150000,
            "revenue_growth": 28.5,
            "avg_revenue_per_user": 89.50,
            "by_source": [
                {
                    "source": "subscription",
                    "amount": 875000,
                    "percentage": 40.7,
                    "growth": 25.3,
                },
                {
                    "source": "virtual_products",
                    "amount": 645000,
                    "percentage": 30.0,
                    "growth": 35.8,
                },
                {
                    "source": "premium_services",
                    "amount": 430000,
                    "percentage": 20.0,
                    "growth": 18.2,
                },
                {
                    "source": "customization",
                    "amount": 200000,
                    "percentage": 9.3,
                    "growth": 42.1,
                },
            ],
            "by_plan": [
                {"plan": "free", "amount": 0, "users": 8000, "arpu": 0.00},
                {"plan": "premium", "amount": 350000, "users": 5000, "arpu": 70.00},
                {"plan": "family", "amount": 720000, "users": 4000, "arpu": 180.00},
                {"plan": "enterprise", "amount": 960000, "users": 800, "arpu": 1200.00},
            ],
            "trends": self._generate_revenue_trends(start_date, end_date),
        }

    def _get_user_metrics(
        self, start_date: datetime, end_date: datetime
    ) -> Dict[str, Any]:
        """获取用户指标"""
        # 在实际实现中，这些应该从数据库查询
        total_users = self.db.query(User).count()

        return {
            "total_users": total_users or 24500,
            "active_users": {"daily": 8420, "weekly": 15680, "monthly": 21200},
            "new_users": 1250,
            "user_growth_rate": 5.8,
            "churn_rate": 3.2,
            "ltv": 450.00,  # 用户生命周期价值
            "cac": 85.50,  # 客户获取成本
            "paying_users": 12800,
            "conversion_rate": 52.2,
        }

    def _get_conversion_metrics(
        self, start_date: datetime, end_date: datetime
    ) -> Dict[str, Any]:
        """获取转化指标"""
        return {
            "funnel_steps": [
                {"step": "访问网站", "users": 50000, "conversion_rate": 100, "dropoff": 0},
                {
                    "step": "注册账号",
                    "users": 8500,
                    "conversion_rate": 17.0,
                    "dropoff": 83.0,
                },
                {
                    "step": "创建纪念空间",
                    "users": 6200,
                    "conversion_rate": 72.9,
                    "dropoff": 27.1,
                },
                {
                    "step": "购买服务",
                    "users": 3100,
                    "conversion_rate": 50.0,
                    "dropoff": 50.0,
                },
                {
                    "step": "成为付费用户",
                    "users": 2800,
                    "conversion_rate": 90.3,
                    "dropoff": 9.7,
                },
            ],
            "by_source": [
                {
                    "source": "搜索引擎",
                    "visitors": 25000,
                    "conversions": 4500,
                    "rate": 18.0,
                },
                {
                    "source": "社交媒体",
                    "visitors": 15000,
                    "conversions": 2100,
                    "rate": 14.0,
                },
                {"source": "直接访问", "visitors": 8000, "conversions": 1600, "rate": 20.0},
                {"source": "推荐链接", "visitors": 2000, "conversions": 300, "rate": 15.0},
            ],
            "time_to_convert": 7.5,
            "dropoff_points": [
                {"point": "注册流程", "users": 41500, "reason": "注册步骤过多"},
                {"point": "首次使用", "users": 2300, "reason": "功能复杂度高"},
                {"point": "付费转化", "users": 300, "reason": "价格敏感"},
            ],
        }

    def _get_retention_metrics(
        self, start_date: datetime, end_date: datetime
    ) -> Dict[str, Any]:
        """获取留存指标"""
        return {
            "cohort_analysis": [
                {
                    "month": "2024-01",
                    "users": 1200,
                    "retention": [100, 85, 72, 68, 65, 62],
                },
                {"month": "2024-02", "users": 1350, "retention": [100, 88, 75, 71, 68]},
                {"month": "2024-03", "users": 1500, "retention": [100, 90, 78, 74]},
                {"month": "2024-04", "users": 1680, "retention": [100, 92, 80]},
                {"month": "2024-05", "users": 1820, "retention": [100, 94]},
                {"month": "2024-06", "users": 2000, "retention": [100]},
            ],
            "retention_rates": {"day1": 94, "day7": 78, "day30": 65, "day90": 52},
            "engagement_score": 8.2,
            "stickiness": 0.68,
        }

    def _get_product_metrics(
        self, start_date: datetime, end_date: datetime
    ) -> Dict[str, Any]:
        """获取产品指标"""
        return {
            "virtual_products": [
                {
                    "product_id": "incense_premium",
                    "name": "高级檀香",
                    "sales": 1250,
                    "revenue": 12475,
                    "popularity": 89,
                    "rating": 4.8,
                },
                {
                    "product_id": "lotus_flower",
                    "name": "莲花祭品",
                    "sales": 980,
                    "revenue": 15682,
                    "popularity": 76,
                    "rating": 4.9,
                },
                {
                    "product_id": "candle_set",
                    "name": "祈福蜡烛套装",
                    "sales": 750,
                    "revenue": 9742,
                    "popularity": 65,
                    "rating": 4.6,
                },
                {
                    "product_id": "cherry_blossom_effect",
                    "name": "樱花飘落特效",
                    "sales": 420,
                    "revenue": 12598,
                    "popularity": 92,
                    "rating": 4.9,
                },
            ],
            "subscription_plans": [
                {
                    "plan": "免费版",
                    "subscribers": 8000,
                    "churn_rate": 5.2,
                    "avg_lifetime": 18,
                    "upgrade_rate": 15.5,
                },
                {
                    "plan": "高级版",
                    "subscribers": 5000,
                    "churn_rate": 3.1,
                    "avg_lifetime": 28,
                    "upgrade_rate": 8.2,
                },
                {
                    "plan": "家族版",
                    "subscribers": 4000,
                    "churn_rate": 2.1,
                    "avg_lifetime": 36,
                    "upgrade_rate": 4.1,
                },
                {
                    "plan": "企业版",
                    "subscribers": 800,
                    "churn_rate": 1.2,
                    "avg_lifetime": 48,
                    "upgrade_rate": 0,
                },
            ],
            "premium_features": [
                {
                    "feature": "AI照片修复",
                    "usage": 85,
                    "conversion_impact": 32,
                    "satisfaction": 9.1,
                },
                {
                    "feature": "3D纪念空间",
                    "usage": 78,
                    "conversion_impact": 28,
                    "satisfaction": 8.8,
                },
                {
                    "feature": "语音克隆",
                    "usage": 45,
                    "conversion_impact": 45,
                    "satisfaction": 9.3,
                },
                {
                    "feature": "AR体验",
                    "usage": 23,
                    "conversion_impact": 52,
                    "satisfaction": 9.5,
                },
            ],
            "customer_satisfaction": 8.7,
        }

    def _get_growth_metrics(
        self, start_date: datetime, end_date: datetime
    ) -> Dict[str, Any]:
        """获取增长指标"""
        return {
            "nps": 68,  # 净推荐值
            "referral_rate": 12.5,
            "viral_coefficient": 1.35,
            "organic_growth": 65,
            "paid_growth": 35,
            "marketing_roi": 4.2,
        }

    def _get_membership_analytics(
        self, start_date: datetime, end_date: datetime
    ) -> Dict[str, Any]:
        """获取会员权益分析数据"""
        # 统计各等级用户数量
        tier_distribution = {
            MembershipTier.FREE: 8000,
            MembershipTier.PREMIUM: 5000,
            MembershipTier.FAMILY: 4000,
            MembershipTier.ENTERPRISE: 800,
        }

        # 计算会员转化率
        total_users = sum(tier_distribution.values())
        conversion_rates = {}
        for tier, count in tier_distribution.items():
            if tier != MembershipTier.FREE:
                conversion_rates[tier] = (count / total_users) * 100

        # 功能使用统计
        feature_usage = {
            "memorial_spaces": {
                "avg_usage_by_tier": {
                    MembershipTier.FREE: 1.2,
                    MembershipTier.PREMIUM: 4.8,
                    MembershipTier.FAMILY: 12.5,
                    MembershipTier.ENTERPRISE: 28.3,
                },
                "limit_hit_rate": {
                    MembershipTier.FREE: 78.5,
                    MembershipTier.PREMIUM: 32.1,
                    MembershipTier.FAMILY: 8.7,
                    MembershipTier.ENTERPRISE: 2.1,
                },
            },
            "ai_minutes": {
                "avg_usage_by_tier": {
                    MembershipTier.FREE: 8.2,
                    MembershipTier.PREMIUM: 45.6,
                    MembershipTier.FAMILY: 185.4,
                    MembershipTier.ENTERPRISE: 487.9,
                },
                "limit_hit_rate": {
                    MembershipTier.FREE: 85.2,
                    MembershipTier.PREMIUM: 68.4,
                    MembershipTier.FAMILY: 42.1,
                    MembershipTier.ENTERPRISE: 15.8,
                },
            },
            "storage_gb": {
                "avg_usage_by_tier": {
                    MembershipTier.FREE: 0.8,
                    MembershipTier.PREMIUM: 6.2,
                    MembershipTier.FAMILY: 28.5,
                    MembershipTier.ENTERPRISE: 156.8,
                },
                "limit_hit_rate": {
                    MembershipTier.FREE: 65.3,
                    MembershipTier.PREMIUM: 45.7,
                    MembershipTier.FAMILY: 28.9,
                    MembershipTier.ENTERPRISE: 12.4,
                },
            },
        }

        # 升级路径分析
        upgrade_paths = [
            {
                "from_tier": MembershipTier.FREE,
                "to_tier": MembershipTier.PREMIUM,
                "conversion_rate": 15.5,
                "avg_time_to_upgrade": 23.5,
                "primary_trigger": "纪念空间限制",
            },
            {
                "from_tier": MembershipTier.PREMIUM,
                "to_tier": MembershipTier.FAMILY,
                "conversion_rate": 8.2,
                "avg_time_to_upgrade": 45.8,
                "primary_trigger": "AI服务使用量",
            },
            {
                "from_tier": MembershipTier.FAMILY,
                "to_tier": MembershipTier.ENTERPRISE,
                "conversion_rate": 4.1,
                "avg_time_to_upgrade": 89.2,
                "primary_trigger": "存储空间需求",
            },
        ]

        # 流失分析
        churn_analysis = {
            "churn_rate_by_tier": {
                MembershipTier.FREE: 5.2,
                MembershipTier.PREMIUM: 3.1,
                MembershipTier.FAMILY: 2.1,
                MembershipTier.ENTERPRISE: 1.2,
            },
            "churn_reasons": [
                {
                    "reason": "功能限制过严",
                    "percentage": 35.2,
                    "mainly_affects": [MembershipTier.FREE],
                },
                {
                    "reason": "价格敏感",
                    "percentage": 28.5,
                    "mainly_affects": [MembershipTier.FREE, MembershipTier.PREMIUM],
                },
                {
                    "reason": "使用频率低",
                    "percentage": 18.7,
                    "mainly_affects": [MembershipTier.PREMIUM, MembershipTier.FAMILY],
                },
                {
                    "reason": "功能需求变化",
                    "percentage": 12.4,
                    "mainly_affects": [
                        MembershipTier.FAMILY,
                        MembershipTier.ENTERPRISE,
                    ],
                },
                {
                    "reason": "竞品选择",
                    "percentage": 5.2,
                    "mainly_affects": [MembershipTier.ENTERPRISE],
                },
            ],
        }

        # 收入贡献分析
        revenue_contribution = {
            tier: {
                "monthly_revenue": tier_distribution[tier]
                * self._get_tier_monthly_price(tier),
                "user_count": tier_distribution[tier],
                "arpu": self._get_tier_monthly_price(tier),
                "ltv": self._get_tier_ltv(tier),
            }
            for tier in tier_distribution.keys()
        }

        return {
            "tier_distribution": {
                tier: {"user_count": count, "percentage": (count / total_users) * 100}
                for tier, count in tier_distribution.items()
            },
            "conversion_rates": conversion_rates,
            "feature_usage": feature_usage,
            "upgrade_paths": upgrade_paths,
            "churn_analysis": churn_analysis,
            "revenue_contribution": revenue_contribution,
            "membership_health_score": self._calculate_membership_health_score(
                tier_distribution, feature_usage, churn_analysis
            ),
        }

    def _get_tier_monthly_price(self, tier: str) -> float:
        """获取等级月费"""
        prices = {
            MembershipTier.FREE: 0.0,
            MembershipTier.PREMIUM: 19.9,
            MembershipTier.FAMILY: 49.9,
            MembershipTier.ENTERPRISE: 199.0,
        }
        return prices.get(tier, 0.0)

    def _get_tier_ltv(self, tier: str) -> float:
        """获取等级用户生命周期价值"""
        avg_lifetime = {
            MembershipTier.FREE: 6,  # 月
            MembershipTier.PREMIUM: 18,
            MembershipTier.FAMILY: 36,
            MembershipTier.ENTERPRISE: 48,
        }
        monthly_price = self._get_tier_monthly_price(tier)
        return monthly_price * avg_lifetime.get(tier, 6)

    def _calculate_membership_health_score(
        self,
        tier_distribution: Dict[str, int],
        feature_usage: Dict[str, Any],
        churn_analysis: Dict[str, Any],
    ) -> float:
        """计算会员健康度评分"""
        total_users = sum(tier_distribution.values())

        # 付费用户比例得分 (40%)
        paying_users = sum(
            count
            for tier, count in tier_distribution.items()
            if tier != MembershipTier.FREE
        )
        paying_ratio_score = (paying_users / total_users) * 40

        # 升级转化率得分 (30%)
        # 基于免费用户升级率
        free_users = tier_distribution.get(MembershipTier.FREE, 0)
        if free_users > 0:
            upgrade_rate = (paying_users / (free_users + paying_users)) * 100
            upgrade_score = min(upgrade_rate / 20 * 30, 30)  # 20%为满分
        else:
            upgrade_score = 30

        # 用户留存得分 (30%)
        avg_churn_rate = sum(churn_analysis["churn_rate_by_tier"].values()) / len(
            churn_analysis["churn_rate_by_tier"]
        )
        retention_score = max(0, (10 - avg_churn_rate) / 10 * 30)  # 10%以下流失率为满分

        total_score = paying_ratio_score + upgrade_score + retention_score
        return min(total_score, 100)

    def _generate_revenue_trends(
        self, start_date: datetime, end_date: datetime
    ) -> List[Dict[str, Any]]:
        """生成收入趋势数据"""
        trends = []
        current_date = start_date

        while current_date <= end_date:
            # 模拟月度收入增长
            base_amount = 180000
            growth_factor = 1 + (current_date - start_date).days / 365 * 0.3  # 年增长30%

            month_str = current_date.strftime("%Y-%m")
            total_amount = int(base_amount * growth_factor)

            trends.append(
                {
                    "date": month_str,
                    "amount": total_amount,
                    "subscriptions": int(total_amount * 0.65),
                    "virtual_products": int(total_amount * 0.25),
                    "services": int(total_amount * 0.10),
                }
            )

            # 移动到下个月
            if current_date.month == 12:
                current_date = current_date.replace(year=current_date.year + 1, month=1)
            else:
                current_date = current_date.replace(month=current_date.month + 1)

        return trends

    def get_membership_recommendations(self) -> List[Dict[str, Any]]:
        """获取会员系统优化建议"""
        recommendations = []

        # 分析当前会员数据
        analytics = self._get_membership_analytics(
            datetime.now() - timedelta(days=30), datetime.now()
        )

        # 基于数据生成建议
        health_score = analytics["membership_health_score"]

        if health_score < 70:
            recommendations.append(
                {
                    "type": "urgent",
                    "title": "会员健康度偏低",
                    "description": "当前会员健康度评分为{:.1f}分，建议重点关注用户转化和留存".format(
                        health_score
                    ),
                    "actions": ["优化免费版功能体验", "调整定价策略", "加强用户引导和教育"],
                }
            )

        # 检查功能使用情况
        feature_usage = analytics["feature_usage"]
        for feature, data in feature_usage.items():
            free_hit_rate = data["limit_hit_rate"].get(MembershipTier.FREE, 0)
            if free_hit_rate > 80:
                recommendations.append(
                    {
                        "type": "opportunity",
                        "title": f"{feature}升级机会",
                        "description": f"免费用户{feature}限制触达率{free_hit_rate:.1f}%，升级转化潜力较大",
                        "actions": ["在用户接近限制时推送升级提醒", "提供限时升级优惠", "优化升级流程体验"],
                    }
                )

        return recommendations
