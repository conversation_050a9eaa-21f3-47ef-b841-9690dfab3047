# 会员权益管理服务
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json
from sqlalchemy.orm import Session
from app.models_sqlalchemy import User
from app.schemas_pydantic.user import UserBase


class MembershipTier:
    """会员等级定义"""

    FREE = "free"
    PREMIUM = "premium"
    FAMILY = "family"
    ENTERPRISE = "enterprise"


class FeatureType:
    """功能类型定义"""

    MEMORIAL_SPACES = "memorial_spaces"
    STORAGE_GB = "storage_gb"
    AI_MINUTES = "ai_minutes"
    CUSTOM_DOMAINS = "custom_domains"
    API_CALLS = "api_calls"
    PRIORITY_SUPPORT = "priority_support"
    ADVANCED_ANALYTICS = "advanced_analytics"
    WHITE_LABEL = "white_label"
    CUSTOM_BRANDING = "custom_branding"
    BACKUP_RETENTION = "backup_retention"


class MembershipFeatureConfig:
    """会员功能配置"""

    TIER_CONFIGS = {
        MembershipTier.FREE: {
            FeatureType.MEMORIAL_SPACES: 2,
            FeatureType.STORAGE_GB: 1,
            FeatureType.AI_MINUTES: 10,
            FeatureType.CUSTOM_DOMAINS: 0,
            FeatureType.API_CALLS: 100,
            FeatureType.PRIORITY_SUPPORT: False,
            FeatureType.ADVANCED_ANALYTICS: False,
            FeatureType.WHITE_LABEL: False,
            FeatureType.CUSTOM_BRANDING: False,
            FeatureType.BACKUP_RETENTION: 7,
        },
        MembershipTier.PREMIUM: {
            FeatureType.MEMORIAL_SPACES: 10,
            FeatureType.STORAGE_GB: 10,
            FeatureType.AI_MINUTES: 60,
            FeatureType.CUSTOM_DOMAINS: 1,
            FeatureType.API_CALLS: 1000,
            FeatureType.PRIORITY_SUPPORT: True,
            FeatureType.ADVANCED_ANALYTICS: True,
            FeatureType.WHITE_LABEL: False,
            FeatureType.CUSTOM_BRANDING: False,
            FeatureType.BACKUP_RETENTION: 30,
        },
        MembershipTier.FAMILY: {
            FeatureType.MEMORIAL_SPACES: -1,  # 无限制
            FeatureType.STORAGE_GB: 50,
            FeatureType.AI_MINUTES: 300,
            FeatureType.CUSTOM_DOMAINS: 3,
            FeatureType.API_CALLS: 5000,
            FeatureType.PRIORITY_SUPPORT: True,
            FeatureType.ADVANCED_ANALYTICS: True,
            FeatureType.WHITE_LABEL: True,
            FeatureType.CUSTOM_BRANDING: True,
            FeatureType.BACKUP_RETENTION: 90,
        },
        MembershipTier.ENTERPRISE: {
            FeatureType.MEMORIAL_SPACES: -1,  # 无限制
            FeatureType.STORAGE_GB: 500,
            FeatureType.AI_MINUTES: 1000,
            FeatureType.CUSTOM_DOMAINS: -1,  # 无限制
            FeatureType.API_CALLS: 50000,
            FeatureType.PRIORITY_SUPPORT: True,
            FeatureType.ADVANCED_ANALYTICS: True,
            FeatureType.WHITE_LABEL: True,
            FeatureType.CUSTOM_BRANDING: True,
            FeatureType.BACKUP_RETENTION: 365,
        },
    }


class MembershipUsageTracker:
    """会员使用情况追踪"""

    def __init__(self, db: Session):
        self.db = db

    def get_user_usage(self, user_id: str) -> Dict[str, Any]:
        """获取用户当前使用情况"""
        # 这里应该查询实际的使用数据
        # 为示例目的，返回模拟数据
        return {
            "memorial_spaces_used": 3,
            "storage_used_gb": 2.5,
            "ai_minutes_used": 25,
            "api_calls_used": 150,
            "last_reset_date": datetime.now().replace(day=1).isoformat(),
            "period_start": datetime.now().replace(day=1).isoformat(),
            "period_end": (datetime.now().replace(day=1) + timedelta(days=32))
            .replace(day=1)
            .isoformat(),
        }

    def increment_usage(self, user_id: str, feature_type: str, amount: int = 1) -> bool:
        """增加使用量"""
        # 实际实现中应该更新数据库中的使用记录
        print(f"增加用户 {user_id} 的 {feature_type} 使用量: {amount}")
        return True

    def reset_monthly_usage(self, user_id: str) -> bool:
        """重置月度使用量"""
        # 实际实现中应该重置所有月度限制的使用量
        print(f"重置用户 {user_id} 的月度使用量")
        return True


class MembershipService:
    """会员权益管理服务"""

    def __init__(self, db: Session):
        self.db = db
        self.usage_tracker = MembershipUsageTracker(db)
        self.config = MembershipFeatureConfig()

    def get_user_tier(self, user_id: str) -> str:
        """获取用户会员等级"""
        user = self.db.query(User).filter(User.id == user_id).first()
        if not user:
            return MembershipTier.FREE

        # 检查用户订阅状态
        # 这里应该检查实际的订阅数据
        # 为示例目的，返回免费版
        return getattr(user, "membership_tier", MembershipTier.FREE)

    def get_tier_limits(self, tier: str) -> Dict[str, Any]:
        """获取等级限制"""
        return self.config.TIER_CONFIGS.get(
            tier, self.config.TIER_CONFIGS[MembershipTier.FREE]
        )

    def get_user_limits(self, user_id: str) -> Dict[str, Any]:
        """获取用户的功能限制"""
        tier = self.get_user_tier(user_id)
        return self.get_tier_limits(tier)

    def check_feature_access(
        self, user_id: str, feature_type: str, requested_amount: int = 1
    ) -> Dict[str, Any]:
        """检查功能访问权限"""
        tier = self.get_user_tier(user_id)
        limits = self.get_tier_limits(tier)
        usage = self.usage_tracker.get_user_usage(user_id)

        # 获取功能限制
        feature_limit = limits.get(feature_type)
        current_usage = usage.get(f"{feature_type}_used", 0)

        # 检查访问权限
        if feature_limit == -1:  # 无限制
            return {
                "allowed": True,
                "remaining": -1,
                "limit": -1,
                "current_usage": current_usage,
                "tier": tier,
            }
        elif isinstance(feature_limit, bool):  # 布尔类型功能
            return {
                "allowed": feature_limit,
                "remaining": None,
                "limit": feature_limit,
                "current_usage": None,
                "tier": tier,
            }
        else:  # 数量限制
            remaining = feature_limit - current_usage
            allowed = remaining >= requested_amount

            return {
                "allowed": allowed,
                "remaining": remaining,
                "limit": feature_limit,
                "current_usage": current_usage,
                "tier": tier,
            }

    def can_create_memorial_space(self, user_id: str) -> Dict[str, Any]:
        """检查是否可以创建纪念空间"""
        return self.check_feature_access(user_id, FeatureType.MEMORIAL_SPACES)

    def can_use_ai_service(
        self, user_id: str, minutes_needed: int = 1
    ) -> Dict[str, Any]:
        """检查是否可以使用AI服务"""
        return self.check_feature_access(
            user_id, FeatureType.AI_MINUTES, minutes_needed
        )

    def can_upload_file(self, user_id: str, file_size_gb: float) -> Dict[str, Any]:
        """检查是否可以上传文件"""
        return self.check_feature_access(
            user_id, FeatureType.STORAGE_GB, int(file_size_gb)
        )

    def can_make_api_call(self, user_id: str) -> Dict[str, Any]:
        """检查是否可以进行API调用"""
        return self.check_feature_access(user_id, FeatureType.API_CALLS)

    def has_priority_support(self, user_id: str) -> bool:
        """检查是否有优先支持"""
        result = self.check_feature_access(user_id, FeatureType.PRIORITY_SUPPORT)
        return result.get("allowed", False)

    def has_advanced_analytics(self, user_id: str) -> bool:
        """检查是否有高级分析功能"""
        result = self.check_feature_access(user_id, FeatureType.ADVANCED_ANALYTICS)
        return result.get("allowed", False)

    def consume_feature_usage(
        self, user_id: str, feature_type: str, amount: int = 1
    ) -> bool:
        """消费功能使用量"""
        # 先检查是否有权限
        access_check = self.check_feature_access(user_id, feature_type, amount)

        if not access_check["allowed"]:
            return False

        # 增加使用量
        return self.usage_tracker.increment_usage(user_id, feature_type, amount)

    def get_upgrade_recommendations(self, user_id: str) -> List[Dict[str, Any]]:
        """获取升级建议"""
        current_tier = self.get_user_tier(user_id)
        usage = self.usage_tracker.get_user_usage(user_id)
        current_limits = self.get_tier_limits(current_tier)

        recommendations = []

        # 检查各项使用情况，给出升级建议
        for feature_type, used in usage.items():
            if not feature_type.endswith("_used"):
                continue

            base_feature = feature_type.replace("_used", "")
            limit = current_limits.get(base_feature, 0)

            if limit != -1 and isinstance(limit, (int, float)) and used >= limit * 0.8:
                # 使用量超过80%，建议升级
                recommendations.append(
                    {
                        "feature": base_feature,
                        "current_usage": used,
                        "current_limit": limit,
                        "usage_percentage": (used / limit) * 100 if limit > 0 else 0,
                        "recommended_tier": self._get_next_tier(current_tier),
                        "reason": f"当前{base_feature}使用量已达到{(used/limit)*100:.0f}%",
                    }
                )

        return recommendations

    def _get_next_tier(self, current_tier: str) -> str:
        """获取下一个等级"""
        tier_hierarchy = [
            MembershipTier.FREE,
            MembershipTier.PREMIUM,
            MembershipTier.FAMILY,
            MembershipTier.ENTERPRISE,
        ]

        try:
            current_index = tier_hierarchy.index(current_tier)
            if current_index < len(tier_hierarchy) - 1:
                return tier_hierarchy[current_index + 1]
        except ValueError:
            pass

        return MembershipTier.PREMIUM

    def get_membership_summary(self, user_id: str) -> Dict[str, Any]:
        """获取会员权益概览"""
        tier = self.get_user_tier(user_id)
        limits = self.get_tier_limits(tier)
        usage = self.usage_tracker.get_user_usage(user_id)
        recommendations = self.get_upgrade_recommendations(user_id)

        # 计算使用率
        usage_percentages = {}
        for feature_type, limit in limits.items():
            if isinstance(limit, (int, float)) and limit > 0:
                used = usage.get(f"{feature_type}_used", 0)
                usage_percentages[feature_type] = (used / limit) * 100
            elif limit == -1:
                usage_percentages[feature_type] = 0  # 无限制
            else:
                usage_percentages[feature_type] = None  # 布尔类型功能

        return {
            "user_id": user_id,
            "current_tier": tier,
            "tier_display_name": self._get_tier_display_name(tier),
            "limits": limits,
            "usage": usage,
            "usage_percentages": usage_percentages,
            "upgrade_recommendations": recommendations,
            "features": {
                "memorial_spaces": self.check_feature_access(
                    user_id, FeatureType.MEMORIAL_SPACES
                ),
                "storage": self.check_feature_access(user_id, FeatureType.STORAGE_GB),
                "ai_minutes": self.check_feature_access(
                    user_id, FeatureType.AI_MINUTES
                ),
                "priority_support": self.has_priority_support(user_id),
                "advanced_analytics": self.has_advanced_analytics(user_id),
            },
        }

    def _get_tier_display_name(self, tier: str) -> str:
        """获取等级显示名称"""
        display_names = {
            MembershipTier.FREE: "免费版",
            MembershipTier.PREMIUM: "高级版",
            MembershipTier.FAMILY: "家族版",
            MembershipTier.ENTERPRISE: "企业版",
        }
        return display_names.get(tier, "未知")

    def validate_operation(
        self, user_id: str, operation_type: str, **kwargs
    ) -> Dict[str, Any]:
        """验证操作权限"""
        """
        operation_type可以是:
        - create_memorial: 创建纪念空间
        - upload_file: 上传文件 (需要file_size_gb参数)
        - use_ai: 使用AI服务 (需要minutes参数)
        - api_call: API调用
        """

        if operation_type == "create_memorial":
            return self.can_create_memorial_space(user_id)
        elif operation_type == "upload_file":
            file_size_gb = kwargs.get("file_size_gb", 0)
            return self.can_upload_file(user_id, file_size_gb)
        elif operation_type == "use_ai":
            minutes = kwargs.get("minutes", 1)
            return self.can_use_ai_service(user_id, minutes)
        elif operation_type == "api_call":
            return self.can_make_api_call(user_id)
        else:
            return {"allowed": False, "error": f"未知的操作类型: {operation_type}"}
