import os
from typing import Any

from dotenv import load_dotenv
from pydantic import EmailStr, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

# Load .env file if it exists
load_dotenv()


class Settings(BaseSettings):
    model_config = SettingsConfigDict(
        case_sensitive=True, env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )

    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = os.getenv(
        "SECRET_KEY", "a_very_secret_key_that_should_be_changed"
    )
    # 60 minutes * 24 hours * 8 days = 8 days
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 30  # 30 days

    SERVER_NAME: str | None = os.getenv("SERVER_NAME")
    SERVER_HOST: str | None = os.getenv("SERVER_HOST", "http://localhost:8008")
    # BACKEND_CORS_ORIGINS is a JSON-formatted list of origins
    # e.g. '["http://localhost", "http://localhost:4200", "http://localhost:3000", "http://localhost:5173"]'
    BACKEND_CORS_ORIGINS: list[str] = [
        "http://localhost",
        "http://localhost:3000",  # Common React dev port
        "http://localhost:4001",  # Memorial frontend port
        "http://localhost:4200",  # Common Angular dev port
        "http://localhost:5173",  # Common Vite dev port
        "http://localhost:8080",  # Common Vue dev port
        "https://your-production-domain.com",  # Example production domain
    ]

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: str | list[str]) -> list[str] | str:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, list | str):
            return v
        raise ValueError(v)

    PROJECT_NAME: str = "YunNian Memorial Platform API"

    POSTGRES_SERVER: str = os.getenv("POSTGRES_SERVER", "localhost")
    POSTGRES_PORT: str = os.getenv("POSTGRES_PORT", "5432")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "memorial")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "password")
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "memorial")
    SQLALCHEMY_DATABASE_URI: str | None = None

    @field_validator("SQLALCHEMY_DATABASE_URI", mode="before")
    @classmethod
    def assemble_db_connection(cls, v: str | None, info) -> Any:
        if isinstance(v, str):
            return v

        # If not a string, construct from environment variables
        values = info.data  # type: ignore[unreachable]
        return f"postgresql://{values.get('POSTGRES_USER')}:{values.get('POSTGRES_PASSWORD')}@{values.get('POSTGRES_SERVER')}:{values.get('POSTGRES_PORT')}/{values.get('POSTGRES_DB')}"

    # Directory for file uploads
    UPLOAD_DIRECTORY: str = os.getenv(
        "UPLOAD_DIRECTORY", "./uploads"
    )  # Relative to backend app root
    # Base URL for serving static files (uploads)
    STATIC_FILES_BASE_URL: str = os.getenv("STATIC_FILES_BASE_URL", "/static/uploads")

    # Email settings for user verification, password reset, etc.
    SMTP_TLS: bool = True
    SMTP_PORT: int | None = None
    SMTP_HOST: str | None = None
    SMTP_USER: str | None = None
    SMTP_PASSWORD: str | None = None
    EMAILS_FROM_EMAIL: EmailStr = os.getenv("EMAILS_FROM_EMAIL", "<EMAIL>")
    EMAILS_FROM_NAME: str | None = None

    @field_validator("EMAILS_FROM_EMAIL", mode="before")
    @classmethod
    def validate_emails_from_email(cls, v: str) -> str:
        if not v or v.strip() == "":
            return "<EMAIL>"
        return v

    @field_validator("EMAILS_FROM_NAME")
    @classmethod
    def get_project_name_as_emails_from_name(cls, v: str | None, info) -> str:
        if not v:
            return str(info.data["PROJECT_NAME"])  # 确保返回类型为str
        return v

    EMAIL_RESET_TOKEN_EXPIRE_HOURS: int = 48
    EMAIL_TEMPLATES_DIR: str = "/app/email-templates/build"
    EMAILS_ENABLED: bool = False

    @field_validator("EMAILS_ENABLED", mode="before")
    @classmethod
    def get_emails_enabled(cls, v: bool, info) -> bool:
        values = info.data
        return bool(
            values.get("SMTP_HOST")
            and values.get("SMTP_PORT")
            and values.get("EMAILS_FROM_EMAIL")
        )

    FIRST_SUPERUSER_EMAIL: EmailStr = os.getenv(
        "FIRST_SUPERUSER_EMAIL", "<EMAIL>"
    )
    FIRST_SUPERUSER_USERNAME: str = os.getenv("FIRST_SUPERUSER_USERNAME", "admin")
    FIRST_SUPERUSER_PASSWORD: str = os.getenv("FIRST_SUPERUSER_PASSWORD", "changethis")

    # Frontend URL for email links
    FRONTEND_HOST: str = os.getenv("FRONTEND_HOST", "http://localhost:3000")

    # Replicate API configuration
    REPLICATE_API_KEY: str = os.getenv(
        "REPLICATE_API_KEY", "****************************************"
    )

    # Security configuration
    SECURITY_ALGORITHM: str = "HS256"


settings = Settings()
