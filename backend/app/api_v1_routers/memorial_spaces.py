import uuid
from datetime import datetime
from typing import Any
from uuid import UUID

from fastapi import APIRouter, Depends, Form, HTTPException, status
from sqlalchemy import func
from sqlalchemy.orm import Session

from app import crud
from app import models_sqlalchemy as models
from app import schemas_pydantic as schemas
from app.api_v1_routers import (
    deps,
)  # Assuming deps.py for dependencies like get_db, get_current_user

router = APIRouter()


@router.post("/", response_model=schemas.MemorialSpaceResponse)
def create_memorial_space(
    *,  # Enforces keyword-only arguments for subsequent parameters
    db: Session = Depends(deps.get_db),
    deceased_name: str = Form(...),
    deceased_gender: schemas.DeceasedGenderEnum | None = Form(None),
    birth_date: str | None = Form(None),  # Assuming date as string from form
    death_date: str | None = Form(None),
    relationship: str | None = Form(None),
    bio: str | None = Form(None),
    privacy_level: schemas.PrivacyLevelEnum = Form(schemas.PrivacyLevelEnum.private),
    access_password: str | None = Form(None),
    scene_id: UUID | None = Form(None),  # 3D场景选择
    music_url: str | None = Form(None),  # 背景音乐URL
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new memorial space.
    Frontend sends FormData, so we use Form fields.
    """
    if privacy_level == schemas.PrivacyLevelEnum.password and not access_password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Access password is required for password-protected spaces.",
        )

    # 验证场景ID是否有效
    if scene_id:
        from app.models.scene import Scene

        scene = (
            db.query(Scene)
            .filter(Scene.id == scene_id, Scene.is_active == True)
            .first()
        )
        if not scene:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Selected scene not found or inactive.",
            )

    memorial_space_in = schemas.MemorialSpaceCreate(
        deceased_name=deceased_name,
        deceased_gender=deceased_gender,
        birth_date=(
            datetime.strptime(birth_date, "%Y-%m-%d").date() if birth_date else None
        ),  # Convert string to date for Pydantic
        death_date=datetime.strptime(death_date, "%Y-%m-%d").date()
        if death_date
        else None,
        creator_relationship_to_deceased=relationship,
        bio=bio,
        privacy_level=privacy_level,
        access_password=(
            access_password
            if privacy_level == schemas.PrivacyLevelEnum.password
            else None
        ),
        scene_id=scene_id,
        music_url=music_url,
        custom_settings=None,
        cover_image_url=None,
    )
    memorial_space = crud.memorial_space.create_with_creator(
        db=db, obj_in=memorial_space_in, creator_id=UUID(str(current_user.id))
    )
    return memorial_space


@router.get("/", response_model=schemas.MemorialSpaceListResponse)
def read_memorial_spaces(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve memorial spaces created by the current user.
    """
    spaces = crud.memorial_space.get_multi_by_creator(
        db=db, creator_id=UUID(str(current_user.id)), skip=skip, limit=limit
    )
    total = (
        db.query(func.count(models.MemorialSpace.id))
        .filter(models.MemorialSpace.creator_id == current_user.id)
        .scalar()
        or 0
    )
    # This is a simplified total count. For complex filters, count might need to reflect those.
    return {
        "items": spaces,
        "total": total,
        "page": (skip // limit) + 1 if limit > 0 else 1,
        "size": limit,
        # "links": {} # Add pagination links if needed
    }


@router.get("/{space_id}", response_model=schemas.MemorialSpaceResponse)
def read_memorial_space(
    space_id: uuid.UUID,
    access_password: str | None = None,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(
        deps.get_current_active_user
    ),  # Or a more permissive dependency for public spaces
) -> Any:
    """
    Get memorial space by ID.
    Implement access control based on privacy_level and user.
    """
    # 使用增强的权限检查方法
    space = crud.memorial_space.get_space_with_access_check(
        db=db, space_id=space_id, user=current_user, access_password=access_password
    )

    if not space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Memorial space not found or access denied",
        )

    # 增加访问次数（仅当非创建者访问时）
    if space.creator_id != current_user.id:
        crud.memorial_space.increment_visit_count(db, space=space)

    return space


@router.put("/{space_id}", response_model=schemas.MemorialSpaceResponse)
def update_memorial_space(
    space_id: uuid.UUID,
    *,  # Enforces keyword-only arguments
    db: Session = Depends(deps.get_db),
    space_in: schemas.MemorialSpaceUpdate,  # Frontend sends JSON for PUT
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a memorial space.
    Only the creator can update.
    """
    space = crud.memorial_space.get(db=db, id=space_id)
    if not space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )
    if space.creator_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
        )

    # Ensure password is handled correctly if privacy level changes
    if (
        space_in.privacy_level == schemas.PrivacyLevelEnum.password
        and not space_in.access_password
    ):
        # If updating to password protected and no password is provided, keep existing or require one.
        # For simplicity, we'll assume frontend sends it or it's an error not to.
        # Or, if password is not in payload, don't update it.
        pass  # Current Pydantic model makes access_password optional.
    elif (
        space_in.privacy_level
        and space_in.privacy_level != schemas.PrivacyLevelEnum.password
    ):
        space_in.access_password = None  # Clear password if not password-protected

    updated_space = crud.memorial_space.update(db=db, db_obj=space, obj_in=space_in)
    return updated_space


@router.delete("/{space_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_memorial_space(
    space_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> None:
    """
    Delete a memorial space.
    Only the creator can delete.
    """
    space = crud.memorial_space.get(db=db, id=space_id)
    if not space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )
    if space.creator_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
        )

    crud.memorial_space.remove(db=db, id=space_id)
    return None


@router.put("/{space_id}/settings", response_model=schemas.MemorialSpaceResponse)
def update_memorial_space_settings(
    space_id: uuid.UUID,
    *,
    db: Session = Depends(deps.get_db),
    settings: schemas.MemorialCustomSettings,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update memorial space custom settings.
    Only the creator can update settings.
    """
    space = crud.memorial_space.get(db=db, id=space_id)
    if not space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )
    if space.creator_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
        )

    updated_space = crud.memorial_space.update_custom_settings(
        db=db, space=space, settings=settings
    )
    return updated_space


@router.get("/public", response_model=schemas.MemorialSpaceListResponse)
def get_public_memorial_spaces(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    search: str | None = None,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get public memorial spaces.
    """
    spaces = crud.memorial_space.get_public_spaces(
        db=db, skip=skip, limit=limit, search=search
    )
    total = (
        db.query(func.count(models.MemorialSpace.id))
        .filter(
            models.MemorialSpace.privacy_level == "public",
            models.MemorialSpace.is_active == True,
        )
        .scalar()
        or 0
    )

    if search:
        total = (
            db.query(func.count(models.MemorialSpace.id))
            .filter(
                models.MemorialSpace.privacy_level == "public",
                models.MemorialSpace.is_active == True,
                models.MemorialSpace.deceased_name.ilike(f"%{search}%"),
            )
            .scalar()
            or 0
        )

    return {
        "items": spaces,
        "total": total,
        "page": (skip // limit) + 1 if limit > 0 else 1,
        "size": limit,
    }
