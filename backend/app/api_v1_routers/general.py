from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.dependencies import get_db  # 导入 get_db 依赖
from app.models.environment import Environment
from app.models.religious_setting import ReligiousCulturalSetting
from app.schemas_pydantic.environment import EnvironmentResponse
from app.schemas_pydantic.religious_setting import ReligiousCulturalSettingResponse

router = APIRouter()


@router.get("/environments", response_model=list[EnvironmentResponse], summary="获取所有环境")
def get_all_environments(db: Session = Depends(get_db)):
    """
    获取所有可用的环境信息。
    """
    environments = db.query(Environment).all()
    return environments


@router.get(
    "/environments/{environment_id}",
    response_model=EnvironmentResponse,
    summary="获取特定环境",
)
def get_single_environment(environment_id: int, db: Session = Depends(get_db)):
    """
    根据ID获取特定环境的详细信息。
    """
    environment = db.query(Environment).filter(Environment.id == environment_id).first()
    if not environment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Environment not found"
        )
    return environment


@router.get(
    "/religious-settings",
    response_model=list[ReligiousCulturalSettingResponse],
    summary="获取所有宗教/文化设置",
)
def get_all_religious_settings(db: Session = Depends(get_db)):
    """
    获取所有可用的宗教/文化设置信息。
    """
    settings = db.query(ReligiousCulturalSetting).all()
    return settings
