"""Authentication routes for the Memorial API."""
import logging
from datetime import <PERSON><PERSON><PERSON>

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session

from app import crud
from app import models_sqlalchemy as models
from app import schemas_pydantic as schemas
from app.api_v1_routers import deps
from app.core import security
from app.core.config import settings
from app.core.security import (
    create_email_verification_token,
    create_password_reset_token,
    get_password_hash,
    verify_email_verification_token,
    verify_password_reset_token,
)
from app.schemas_pydantic.auth import (
    EmailVerificationRequest,
    MessageResponse,
    PasswordResetConfirm,
    PasswordResetRequest,
)
from app.schemas_pydantic.token import TokenResponse
from app.schemas_pydantic.user import LoginRequest, PasswordChange
from app.services.email_service import email_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/login/access-token", response_model=TokenResponse)
def login_access_token(
    db: Session = Depends(deps.get_db), form_data: OAuth2PasswordRequestForm = Depends()
):
    """
    OAuth2 compatible token login, get an access token for future requests.
    """
    user = crud.user.authenticate(
        db,
        email_or_username=form_data.username,  # form_data.username can be email or username
        password=form_data.password,
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect email/username or password",
        )
    elif not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user"
        )

    # 更新最后登录时间
    crud.user.update_last_login(db, user=user)

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    refresh_token_expires = timedelta(minutes=settings.REFRESH_TOKEN_EXPIRE_MINUTES)

    access_token = security.create_access_token(
        user.id, expires_delta=access_token_expires
    )
    refresh_token = security.create_refresh_token(
        user.id, expires_delta=refresh_token_expires
    )

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": int(access_token_expires.total_seconds()),
        "user": {
            "id": str(user.id),
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "is_email_verified": user.is_email_verified,
            "is_active": user.is_active,
            "role": user.role,
        },
    }


@router.post("/login", response_model=TokenResponse)
def login_with_json(*, db: Session = Depends(deps.get_db), login_data: LoginRequest):
    """
    JSON-based login endpoint for frontend compatibility.
    """
    user = crud.user.authenticate(
        db,
        email_or_username=login_data.login_identifier,
        password=login_data.password,
    )
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect email/username or password",
        )
    elif not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user"
        )

    # 更新最后登录时间
    crud.user.update_last_login(db, user=user)

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    refresh_token_expires = timedelta(minutes=settings.REFRESH_TOKEN_EXPIRE_MINUTES)

    access_token = security.create_access_token(
        user.id, expires_delta=access_token_expires
    )
    refresh_token = security.create_refresh_token(
        user.id, expires_delta=refresh_token_expires
    )

    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": int(access_token_expires.total_seconds()),
        "user": {
            "id": str(user.id),
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "is_email_verified": user.is_email_verified,
            "is_active": user.is_active,
            "role": user.role,
        },
    }


@router.post("/register", response_model=schemas.UserResponse)
def register_user(
    *,  # Enforces keyword-only arguments
    db: Session = Depends(deps.get_db),
    user_in: schemas.UserCreate,  # Pydantic model for request body
):
    """
    Create new user.
    """
    user = crud.user.get_by_email(db, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The user with this email already exists in the system.",
        )
    user_by_username = crud.user.get_by_username(db, username=user_in.username)
    if user_by_username:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The user with this username already exists in the system.",
        )

    created_user = crud.user.create(db, obj_in=user_in)

    # 发送邮箱验证邮件
    if settings.EMAILS_ENABLED:
        verification_token = create_email_verification_token(str(created_user.email))

        # 将验证令牌存储到数据库
        from datetime import datetime, timedelta

        expires_at = datetime.utcnow() + timedelta(hours=24)  # 24小时过期
        crud.user.set_email_verification_token(
            db, user=created_user, token=verification_token, expires_at=expires_at
        )

        email_sent = email_service.send_verification_email(
            to_email=str(created_user.email),
            verification_token=verification_token,
            username=str(created_user.username),
        )
        if not email_sent:
            logger.warning(f"Failed to send verification email to {created_user.email}")
    else:
        # 如果邮件服务未启用，直接设置为已验证
        crud.user.verify_email(db, user=created_user)

    return created_user


@router.post("/login/refresh-token", response_model=TokenResponse)
def refresh_access_token(
    db: Session = Depends(deps.get_db),
    user: models.User = Depends(
        deps.get_current_user_from_refresh_token
    ),  # Custom dependency
):
    """
    Refresh access token using a refresh token.
    `user` will contain the user object if the refresh token is valid.
    """
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token or inactive user",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    new_access_token = security.create_access_token(
        user.id, expires_delta=access_token_expires
    )

    # Optionally, issue a new refresh token as well (good practice for security)
    # new_refresh_token_expires = timedelta(minutes=settings.REFRESH_TOKEN_EXPIRE_MINUTES)
    # new_refresh_token = security.create_refresh_token(
    #     user.id, expires_delta=new_refresh_token_expires
    # )

    return {
        "access_token": new_access_token,
        "refresh_token": None,  # Or return the new_refresh_token if implemented
        "token_type": "bearer",
        "expires_in": int(access_token_expires.total_seconds()),
    }


@router.post("/logout", status_code=status.HTTP_204_NO_CONTENT)
def logout(
    # This endpoint might not do much if using stateless JWTs.
    # Client should discard the token.
    # If using a token blacklist or server-side sessions, implement revocation here.
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    Logout user (client-side token removal is primary).
    """
    # Example: crud.user_token.revoke_refresh_tokens_for_user(db, user_id=current_user.id)
    # For now, it's a no-op on the server for stateless JWTs.
    return None


@router.post("/forgot-password", response_model=MessageResponse)
def request_password_reset(
    password_reset_request: PasswordResetRequest, db: Session = Depends(deps.get_db)
) -> MessageResponse:
    """
    请求密码重置

    发送密码重置邮件到用户邮箱
    """
    user = crud.user.get_by_email(db, email=password_reset_request.email)

    # 无论用户是否存在，都返回成功消息（安全考虑）
    if user and user.is_active:
        # 生成密码重置令牌
        reset_token = create_password_reset_token(str(user.email))

        # 将重置令牌存储到数据库
        from datetime import datetime, timedelta

        expires_at = datetime.utcnow() + timedelta(hours=1)  # 1小时过期
        crud.user.set_password_reset_token(
            db, user=user, token=reset_token, expires_at=expires_at
        )

        # 发送密码重置邮件
        if settings.EMAILS_ENABLED:
            email_sent = email_service.send_password_reset_email(
                to_email=str(user.email),
                reset_token=reset_token,
                username=str(user.username),
            )

            if not email_sent:
                logger.error(f"Failed to send password reset email to {user.email}")
        else:
            logger.info(
                f"Email service disabled. Reset token for {user.email}: {reset_token}"
            )

    return MessageResponse(message="如果该邮箱已注册，您将收到密码重置邮件", success=True)


@router.post("/reset-password", response_model=MessageResponse)
def reset_password(
    password_reset_confirm: PasswordResetConfirm, db: Session = Depends(deps.get_db)
) -> MessageResponse:
    """
    重置密码

    使用密码重置令牌重置用户密码
    """
    # 首先通过数据库查找有效的重置令牌
    user = crud.user.get_by_password_reset_token(db, token=password_reset_confirm.token)
    if not user:
        raise HTTPException(status_code=400, detail="无效或已过期的密码重置令牌")

    # 验证重置令牌（双重验证）
    email = verify_password_reset_token(password_reset_confirm.token)
    if not email or email != user.email:
        raise HTTPException(status_code=400, detail="无效或已过期的密码重置令牌")

    # 更新密码并清除重置令牌
    crud.user.update_password(
        db, user=user, new_password=password_reset_confirm.new_password
    )
    crud.user.clear_password_reset_token(db, user=user)

    logger.info(f"Password reset successful for user: {user.email}")

    return MessageResponse(message="密码重置成功，请使用新密码登录", success=True)


@router.post("/verify-email", response_model=MessageResponse)
def verify_email(
    verification_request: EmailVerificationRequest, db: Session = Depends(deps.get_db)
) -> MessageResponse:
    """
    验证邮箱

    使用邮箱验证令牌验证用户邮箱
    """
    # 首先通过数据库查找有效的验证令牌
    user = crud.user.get_by_email_verification_token(
        db, token=verification_request.token
    )
    if not user:
        raise HTTPException(status_code=400, detail="无效或已过期的邮箱验证令牌")

    # 验证邮箱验证令牌（双重验证）
    email = verify_email_verification_token(verification_request.token)
    if not email or email != user.email:
        raise HTTPException(status_code=400, detail="无效或已过期的邮箱验证令牌")

    # 更新邮箱验证状态并清除验证令牌
    crud.user.verify_email(db, user=user)

    logger.info(f"Email verification successful for user: {user.email}")

    return MessageResponse(message="邮箱验证成功", success=True)


@router.post("/resend-verification", response_model=MessageResponse)
def resend_verification_email(
    email_request: PasswordResetRequest, db: Session = Depends(deps.get_db)  # 复用相同的模式
) -> MessageResponse:
    """
    重新发送验证邮件

    为未验证的用户重新发送邮箱验证邮件
    """
    user = crud.user.get_by_email(db, email=email_request.email)

    if not user:
        # 安全考虑：不透露用户是否存在
        return MessageResponse(message="如果该邮箱已注册且未验证，您将收到验证邮件", success=True)

    if user.is_email_verified:
        return MessageResponse(message="该邮箱已经验证过了", success=True)

    # 生成邮箱验证令牌
    verification_token = create_email_verification_token(str(user.email))

    # 发送验证邮件
    email_sent = email_service.send_verification_email(
        to_email=str(user.email),
        verification_token=verification_token,
        username=str(user.username),
    )

    if not email_sent:
        logger.error(f"Failed to send verification email to {user.email}")
        raise HTTPException(status_code=500, detail="发送验证邮件失败，请稍后重试")

    return MessageResponse(message="验证邮件已发送，请检查您的邮箱", success=True)


@router.get("/me", response_model=schemas.UserResponse)
def get_current_user_info(
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    获取当前登录用户信息
    """
    return current_user


@router.put("/me", response_model=schemas.UserResponse)
def update_current_user(
    *,
    db: Session = Depends(deps.get_db),
    user_update: schemas.UserUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    更新当前登录用户信息
    """
    # 如果要更新邮箱，需要检查邮箱是否已存在
    if user_update.email and user_update.email != current_user.email:
        existing_user = crud.user.get_by_email(db, email=user_update.email)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该邮箱已被其他用户使用",
            )
        # 邮箱更新后需要重新验证
        user_update.is_email_verified = False

    # 如果要更新用户名，需要检查用户名是否已存在
    if user_update.username and user_update.username != current_user.username:
        existing_user = crud.user.get_by_username(db, username=user_update.username)
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该用户名已被其他用户使用",
            )

    updated_user = crud.user.update(db, db_obj=current_user, obj_in=user_update)
    return updated_user


@router.post("/change-password", response_model=MessageResponse)
def change_password(
    *,
    db: Session = Depends(deps.get_db),
    password_change: PasswordChange,
    current_user: models.User = Depends(deps.get_current_active_user),
):
    """
    修改当前用户密码
    """
    from app.core.security import verify_password

    # 验证当前密码
    if not verify_password(
        password_change.current_password, str(current_user.password_hash)
    ):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码不正确",
        )

    # 更新密码
    crud.user.update_password(
        db, user=current_user, new_password=password_change.new_password
    )

    logger.info(f"Password changed for user: {current_user.email}")

    return MessageResponse(message="密码修改成功", success=True)
