import logging
import os
import uuid
from pathlib import Path

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status

from app.ai_services.replicate_service import ReplicateService
from app.core.config import settings
from app.api_v1_routers.deps import get_current_active_user
from app.models_sqlalchemy import User  # 导入 User 模型
from app.schemas_pydantic.ai import AIResultResponse

logger = logging.getLogger(__name__)

# 配置上传目录
UPLOAD_FOLDER = Path("uploads")
AI_RESULTS_FOLDER = UPLOAD_FOLDER / "ai_results"
UPLOAD_FOLDER.mkdir(exist_ok=True)
AI_RESULTS_FOLDER.mkdir(exist_ok=True)

router = APIRouter()


def get_replicate_service() -> ReplicateService:
    """依赖注入 ReplicateService 实例"""
    return ReplicateService(api_key=settings.REPLICATE_API_KEY)


async def save_upload_file(upload_file: UploadFile, destination_folder: Path) -> Path:
    """保存上传的文件到指定目录"""
    try:
        # 生成唯一文件名
        filename = f"{uuid.uuid4()}_{upload_file.filename}"
        file_path = destination_folder / filename

        # 确保目录存在
        destination_folder.mkdir(parents=True, exist_ok=True)

        # 异步写入文件
        with open(file_path, "wb") as buffer:
            while contents := await upload_file.read(1024 * 1024):  # Read in 1MB chunks
                buffer.write(contents)
        return file_path
    except Exception as e:
        logger.error(f"保存文件失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"文件保存失败: {e}"
        ) from e


@router.post("/photo-restore", response_model=AIResultResponse, summary="修复照片")
async def photo_restore(
    file: UploadFile = File(..., description="图片文件"),
    description: str | None = Form(None, description="描述（可选）"),
    replicate_service: ReplicateService = Depends(get_replicate_service),
    current_user: User | None = Depends(get_current_active_user),  # 可选认证
):
    """
    上传老照片，AI 将自动修复和增强照片质量。
    """
    try:
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件")

        # 保存原始文件
        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)

        # 调用 Replicate 服务
        result = replicate_service.restore_photo(
            open(original_file_path, "rb"), original_file_path.name
        )

        # 构建 URL
        base_url = (
            str(settings.SERVER_HOST).rstrip("/")
            if settings.SERVER_HOST
            else "http://localhost:8000"
        )  # 从 settings 获取基础 URL
        original_url = f"{base_url}/uploads/{os.path.basename(result['original'])}"
        result_url = (
            f"{base_url}/uploads/ai_results/{os.path.basename(result['result'])}"
        )

        return {
            "success": True,
            "message": "照片修复成功",
            "original_url": original_url,
            "result_url": result_url,
        }

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"照片修复失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e


@router.post("/photo-colorize", response_model=AIResultResponse, summary="照片上色")
async def photo_colorize(
    file: UploadFile = File(..., description="图片文件"),
    description: str | None = Form(None, description="描述（可选）"),
    replicate_service: ReplicateService = Depends(get_replicate_service),
    current_user: User | None = Depends(get_current_active_user),
):
    """
    上传黑白照片，AI 将自动为照片上色。
    """
    try:
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件")

        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)

        result = replicate_service.colorize_photo(
            open(original_file_path, "rb"), original_file_path.name
        )

        base_url = (
            str(settings.SERVER_HOST).rstrip("/")
            if settings.SERVER_HOST
            else "http://localhost:8000"
        )
        original_url = f"{base_url}/uploads/{os.path.basename(result['original'])}"
        result_url = (
            f"{base_url}/uploads/ai_results/{os.path.basename(result['result'])}"
        )

        return {
            "success": True,
            "message": "照片上色成功",
            "original_url": original_url,
            "result_url": result_url,
        }

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"照片上色失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e


@router.post("/photo-to-3d", response_model=AIResultResponse, summary="创建照片 3D 效果")
async def photo_to_3d(
    file: UploadFile = File(..., description="图片文件"),
    description: str | None = Form(None, description="描述（可选）"),
    replicate_service: ReplicateService = Depends(get_replicate_service),
    current_user: User | None = Depends(get_current_active_user),
):
    """
    上传照片，AI 将创建 3D 视差效果。
    """
    try:
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件")

        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)

        result = replicate_service.create_3d_effect(
            open(original_file_path, "rb"), original_file_path.name
        )

        base_url = (
            str(settings.SERVER_HOST).rstrip("/")
            if settings.SERVER_HOST
            else "http://localhost:8000"
        )
        original_url = f"{base_url}/uploads/{os.path.basename(result['original'])}"

        return {
            "success": True,
            "message": "3D 效果创建成功",
            "original_url": original_url,
            "result_url": result["result"],
        }

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"创建 3D 效果失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e


@router.post("/create-3d-model", response_model=AIResultResponse, summary="创建 3D 模型")
async def create_3d_model(
    file: UploadFile = File(..., description="图片文件"),
    description: str | None = Form(None, description="描述（可选）"),
    replicate_service: ReplicateService = Depends(get_replicate_service),
    current_user: User | None = Depends(get_current_active_user),
):
    """
    上传照片，AI 将创建 3D 模型。
    """
    try:
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件")

        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)

        result = replicate_service.create_3d_model(
            open(original_file_path, "rb"), original_file_path.name, description or ""
        )

        base_url = (
            str(settings.SERVER_HOST).rstrip("/")
            if settings.SERVER_HOST
            else "http://localhost:8000"
        )
        original_url = f"{base_url}/uploads/{os.path.basename(result['original'])}"

        result_data = result["result"]
        result_url = None
        if isinstance(result_data, dict):
            if "mesh" in result_data:
                result_url = result_data["mesh"]
            elif "output" in result_data:
                result_url = result_data["output"]
        elif isinstance(result_data, str):
            result_url = result_data

        return {
            "success": True,
            "message": "3D 模型创建成功",
            "original_url": original_url,
            "result_url": result_url,
            "result_data": result_data,
        }

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"创建 3D 模型失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e


@router.post("/clone-voice", response_model=AIResultResponse, summary="克隆声音")
async def clone_voice(
    file: UploadFile = File(..., description="声音样本文件"),
    text: str = Form(..., description="要生成的文本"),
    language: str = Form("zh", description="语言代码"),
    replicate_service: ReplicateService = Depends(get_replicate_service),
    current_user: User | None = Depends(get_current_active_user),
):
    """
    上传声音样本，AI 将克隆声音并生成新的语音。
    """
    try:
        if not file or not text:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件或文本"
            )  # Changed from BAD_BAD_REQUEST

        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)

        result = replicate_service.clone_voice(
            open(original_file_path, "rb"), original_file_path.name, text, language
        )

        base_url = (
            str(settings.SERVER_HOST).rstrip("/")
            if settings.SERVER_HOST
            else "http://localhost:8000"
        )
        original_url = f"{base_url}/uploads/{os.path.basename(result['original'])}"

        return {
            "success": True,
            "message": "声音克隆成功",
            "original_url": original_url,
            "result_url": result["result"],
        }

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"声音克隆失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e


@router.post("/photo-enhance", response_model=AIResultResponse, summary="增强照片分辨率")
async def photo_enhance(
    file: UploadFile = File(..., description="图片文件"),
    scale: int = Form(4, description="放大倍数 (2-8)"),
    face_enhance: bool = Form(True, description="是否增强面部"),
    replicate_service: ReplicateService = Depends(get_replicate_service),
    current_user: User | None = Depends(get_current_active_user),
):
    """
    上传照片，AI 将增强照片分辨率和质量。
    """
    try:
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件")

        # 验证放大倍数
        if scale < 2 or scale > 8:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="放大倍数必须在2-8之间"
            )

        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)

        result = replicate_service.enhance_photo_resolution(
            open(original_file_path, "rb"),
            original_file_path.name,
            scale=scale,
            face_enhance=face_enhance,
        )

        base_url = (
            str(settings.SERVER_HOST).rstrip("/")
            if settings.SERVER_HOST
            else "http://localhost:8000"
        )
        original_url = f"{base_url}/uploads/{os.path.basename(result['original'])}"
        result_url = (
            f"{base_url}/uploads/ai_results/{os.path.basename(result['result'])}"
        )

        return {
            "success": True,
            "message": "照片增强成功",
            "original_url": original_url,
            "result_url": result_url,
            "options_used": result.get("options_used", {}),
        }

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"照片增强失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e


@router.post("/photo-remove-bg", response_model=AIResultResponse, summary="移除照片背景")
async def photo_remove_bg(
    file: UploadFile = File(..., description="图片文件"),
    model: str = Form("u2net", description="模型类型 (u2net 或 silueta)"),
    replicate_service: ReplicateService = Depends(get_replicate_service),
    current_user: User | None = Depends(get_current_active_user),
):
    """
    上传照片，AI 将移除照片背景。
    """
    try:
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件")

        # 验证模型类型
        if model not in ["u2net", "silueta"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="模型类型必须是 u2net 或 silueta",
            )

        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)

        result = replicate_service.remove_background(
            open(original_file_path, "rb"), original_file_path.name, model=model
        )

        base_url = (
            str(settings.SERVER_HOST).rstrip("/")
            if settings.SERVER_HOST
            else "http://localhost:8000"
        )
        original_url = f"{base_url}/uploads/{os.path.basename(result['original'])}"
        result_url = (
            f"{base_url}/uploads/ai_results/{os.path.basename(result['result'])}"
        )

        return {
            "success": True,
            "message": "背景移除成功",
            "original_url": original_url,
            "result_url": result_url,
            "options_used": result.get("options_used", {}),
        }

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"背景移除失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e
