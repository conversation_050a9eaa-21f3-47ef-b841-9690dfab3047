"""
数据库管理 API 路由
提供数据库表结构查看、数据浏览、统计信息等功能
"""

from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import inspect, text
from sqlalchemy.orm import Session

from app.core.config import settings
from app.db.session import get_db
from app.dependencies import get_current_user
from app.models_sqlalchemy import User

router = APIRouter()


def check_admin_permission(current_user: User = Depends(get_current_user)) -> User:
    """检查当前用户是否有管理员权限"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限才能访问数据库管理功能")
    return current_user


@router.get("/tables", summary="获取数据库表列表")
def get_database_tables(
    db: Session = Depends(get_db), current_user: User = Depends(check_admin_permission)
) -> dict[str, Any]:
    """获取数据库中所有表的列表和基本信息"""
    try:
        inspector = inspect(db.bind)
        tables = inspector.get_table_names()

        table_list = []
        for table_name in tables:
            try:
                # 获取表的列信息
                columns = inspector.get_columns(table_name)
                primary_keys = inspector.get_pk_constraint(table_name)
                foreign_keys = inspector.get_foreign_keys(table_name)

                # 获取行数
                count_result = db.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                row_count = count_result.scalar() or 0

                table_list.append(
                    {
                        "name": table_name,
                        "column_count": len(columns),
                        "row_count": row_count,
                        "primary_keys": primary_keys.get("constrained_columns", []),
                        "foreign_keys_count": len(foreign_keys),
                    }
                )
            except Exception:
                # 如果单个表出错，跳过但不影响整体
                continue

        return {"success": True, "data": {"tables": table_list}}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库表信息失败: {str(e)}") from e


@router.get("/tables/{table_name}/schema", summary="获取表结构")
def get_table_schema(
    table_name: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission),
) -> dict[str, Any]:
    """获取指定表的详细结构信息"""
    try:
        inspector = inspect(db.bind)

        # 检查表是否存在
        if table_name not in inspector.get_table_names():
            raise HTTPException(status_code=404, detail=f"表 '{table_name}' 不存在")

        # 获取表结构信息
        columns = inspector.get_columns(table_name)
        primary_keys = inspector.get_pk_constraint(table_name)
        foreign_keys = inspector.get_foreign_keys(table_name)
        indexes = inspector.get_indexes(table_name)
        unique_constraints = inspector.get_unique_constraints(table_name)
        check_constraints = inspector.get_check_constraints(table_name)

        # 格式化列信息
        formatted_columns = []
        for col in columns:
            formatted_columns.append(
                {
                    "name": col["name"],
                    "type": str(col["type"]),
                    "nullable": col["nullable"],
                    "default": col["default"],
                    "autoincrement": col.get("autoincrement", False),
                    "comment": col.get("comment", ""),
                }
            )

        return {
            "success": True,
            "data": {
                "table_name": table_name,
                "columns": formatted_columns,
                "primary_keys": primary_keys,
                "foreign_keys": foreign_keys,
                "indexes": indexes,
                "unique_constraints": unique_constraints,
                "check_constraints": check_constraints,
            },
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取表结构失败: {str(e)}") from e


def _build_search_query(table_name: str, search: str | None, columns: list[str]) -> str:
    """构建搜索查询条件"""
    if not search:
        return ""

    search_conditions = []
    for col in columns:
        search_conditions.append(f"CAST({col} AS TEXT) ILIKE '%{search}%'")

    return f" WHERE ({' OR '.join(search_conditions)})"


def _build_order_query(
    order_by: str | None, order_direction: str, columns: list[str]
) -> str:
    """构建排序查询条件"""
    if not order_by or order_by not in columns:
        return ""

    return f" ORDER BY {order_by} {order_direction.upper()}"


@router.get("/tables/{table_name}/data", summary="获取表数据")
def get_table_data(
    table_name: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页记录数"),
    search: str | None = Query(None, description="搜索关键词"),
    order_by: str | None = Query(None, description="排序字段"),
    order_direction: str = Query("asc", regex="^(asc|desc)$", description="排序方向"),
    db: Session = Depends(get_db),
    current_user: User = Depends(check_admin_permission),
) -> dict[str, Any]:
    """获取表数据（支持分页、搜索、排序）"""
    try:
        inspector = inspect(db.bind)

        # 检查表是否存在
        if table_name not in inspector.get_table_names():
            raise HTTPException(status_code=404, detail=f"表 '{table_name}' 不存在")

        # 获取表列名
        columns = inspector.get_columns(table_name)
        column_names = [col["name"] for col in columns]

        # 构建查询
        base_query = f"SELECT * FROM {table_name}"
        search_clause = _build_search_query(table_name, search, column_names)
        order_clause = _build_order_query(order_by, order_direction, column_names)

        # 获取总记录数
        count_query = f"SELECT COUNT(*) FROM {table_name}{search_clause}"
        total_count = db.execute(text(count_query)).scalar() or 0

        # 计算分页
        total_pages = (total_count + page_size - 1) // page_size
        offset = (page - 1) * page_size

        # 获取数据
        data_query = f"{base_query}{search_clause}{order_clause} LIMIT {page_size} OFFSET {offset}"
        result = db.execute(text(data_query))
        rows = result.fetchall()

        # 格式化数据
        formatted_rows = []
        for row in rows:
            row_dict = {}
            for i, value in enumerate(row):
                if i < len(column_names):
                    row_dict[column_names[i]] = value
            formatted_rows.append(row_dict)

        return {
            "success": True,
            "data": {
                "table_name": table_name,
                "columns": column_names,
                "rows": formatted_rows,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages,
                    "has_next": page < total_pages,
                    "has_prev": page > 1,
                },
                "filters": {
                    "search": search,
                    "order_by": order_by,
                    "order_direction": order_direction,
                },
            },
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取表数据失败: {str(e)}") from e


@router.get("/statistics", summary="获取数据库统计信息")
def get_database_statistics(
    db: Session = Depends(get_db), current_user: User = Depends(check_admin_permission)
) -> dict[str, Any]:
    """获取数据库整体统计信息"""
    try:
        inspector = inspect(db.bind)
        tables = inspector.get_table_names()

        total_tables = len(tables)
        total_rows = 0
        table_stats = []
        largest_table = None
        max_rows = 0

        for table_name in tables:
            try:
                # 获取行数
                count_result = db.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                row_count = count_result.scalar() or 0
                total_rows += row_count

                # 获取表大小 (PostgreSQL)
                try:
                    size_result = db.execute(
                        text(f"SELECT pg_total_relation_size('{table_name}')")
                    )
                    table_size = size_result.scalar() or 0
                except Exception:
                    table_size = 0

                # 获取列数
                columns = inspector.get_columns(table_name)
                column_count = len(columns)

                table_info = {
                    "name": table_name,
                    "row_count": row_count,
                    "column_count": column_count,
                    "size_bytes": table_size,
                    "size_mb": round(table_size / (1024 * 1024), 2)
                    if table_size
                    else 0,
                }
                table_stats.append(table_info)

                if row_count > max_rows:
                    max_rows = row_count
                    largest_table = table_info
            except Exception:
                continue

        # 获取数据库版本
        try:
            version_result = db.execute(text("SELECT version()"))
            db_version = version_result.scalar()
        except Exception:
            db_version = "Unknown"

        # 获取数据库大小
        try:
            db_size_result = db.execute(
                text("SELECT pg_database_size(current_database())")
            )
            db_size_bytes = db_size_result.scalar() or 0
            db_size_mb = round(db_size_bytes / (1024 * 1024), 2)
        except Exception:
            db_size_bytes = 0
            db_size_mb = 0

        avg_rows = round(total_rows / total_tables, 2) if total_tables > 0 else 0

        return {
            "success": True,
            "data": {
                "database_info": {
                    "name": settings.POSTGRES_DB,
                    "version": db_version,
                    "total_tables": total_tables,
                    "total_rows": total_rows,
                    "size_bytes": db_size_bytes,
                    "size_mb": db_size_mb,
                },
                "table_statistics": table_stats,
                "summary": {
                    "largest_table": largest_table,
                    "total_size_mb": db_size_mb,
                    "avg_rows_per_table": avg_rows,
                },
            },
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库统计信息失败: {str(e)}") from e


@router.get("/health", summary="数据库健康检查")
def check_database_health(
    db: Session = Depends(get_db), current_user: User = Depends(check_admin_permission)
) -> dict[str, Any]:
    """检查数据库连接状态和基本健康信息"""
    try:
        # 基本连接测试
        db.execute(text("SELECT 1"))

        # 获取活跃连接数
        try:
            connections_result = db.execute(
                text("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'")
            )
            active_connections = connections_result.scalar()
        except Exception:
            active_connections = None

        # 获取数据库启动时间
        try:
            uptime_result = db.execute(text("SELECT pg_postmaster_start_time()"))
            start_time = uptime_result.scalar()
        except Exception:
            start_time = None

        return {
            "success": True,
            "data": {
                "status": "healthy",
                "connection": "connected",
                "active_connections": active_connections,
                "database_start_time": str(start_time) if start_time else None,
                "timestamp": str(db.execute(text("SELECT NOW()")).scalar()),
            },
        }
    except Exception as e:
        return {
            "success": False,
            "data": {
                "status": "unhealthy",
                "connection": "failed",
                "error": str(e),
                "timestamp": "unknown",
            },
        }
