from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app import crud
from app import schemas_pydantic as schemas
from app.api_v1_routers.deps import get_current_user, get_db
from app.models_sqlalchemy import User

router = APIRouter()


@router.get("/profile", response_model=schemas.UserResponse)
async def get_user_profile(
    current_user: User = Depends(get_current_user),
) -> schemas.UserResponse:
    """
    获取当前用户的个人信息
    """
    return schemas.UserResponse.model_validate(current_user)


@router.put("/profile", response_model=schemas.UserResponse)
async def update_user_profile(
    user_update: schemas.UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
) -> schemas.UserResponse:
    """
    更新当前用户的个人信息
    """
    # 更新用户信息
    update_data = user_update.model_dump(exclude_unset=True)

    for field, value in update_data.items():
        if hasattr(current_user, field):
            setattr(current_user, field, value)

    db.commit()
    db.refresh(current_user)

    return schemas.UserResponse.model_validate(current_user)


@router.get("/memorial-spaces", response_model=schemas.MemorialSpaceListResponse)
async def get_user_memorial_spaces(
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> schemas.MemorialSpaceListResponse:
    """
    获取当前用户创建的纪念空间列表
    """
    memorial_spaces = crud.get_memorial_spaces_by_creator(
        db=db, creator_id=current_user.id, skip=skip, limit=limit
    )

    total = crud.count_memorial_spaces_by_creator(db=db, creator_id=current_user.id)

    return schemas.MemorialSpaceListResponse(
        spaces=[
            schemas.MemorialSpaceResponse.model_validate(space)
            for space in memorial_spaces
        ],
        total_count=total,
    )


@router.get("/families", response_model=list[schemas.FamilyResponse])
async def get_user_families(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> list[schemas.FamilyResponse]:
    """
    获取当前用户所属的家族列表
    """
    # TODO: 实现家族功能后完善此接口
    # families = crud.get_families_by_user(db=db, user_id=current_user.id)
    # return families

    # 暂时返回空列表
    return []
