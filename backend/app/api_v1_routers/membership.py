from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.db.session import get_db
from app.dependencies import get_current_user
from app.models_sqlalchemy import User
from app.services.membership_service import MembershipService, FeatureType
from app.schemas_pydantic.membership import (
    MembershipSummaryResponse,
    FeatureAccessCheck,
    OperationValidation,
    UsageUpdateRequest,
)

router = APIRouter()


def get_membership_service(db: Session = Depends(get_db)) -> MembershipService:
    """获取会员服务实例"""
    return MembershipService(db)


@router.get("/summary", response_model=MembershipSummaryResponse)
async def get_membership_summary(
    current_user: User = Depends(get_current_user),
    membership_service: MembershipService = Depends(get_membership_service),
):
    """获取当前用户的会员权益概览"""
    try:
        summary = membership_service.get_membership_summary(str(current_user.id))
        return MembershipSummaryResponse(**summary)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会员信息失败: {str(e)}",
        )


@router.get("/check-feature/{feature_type}", response_model=FeatureAccessCheck)
async def check_feature_access(
    feature_type: str,
    amount: int = 1,
    current_user: User = Depends(get_current_user),
    membership_service: MembershipService = Depends(get_membership_service),
):
    """检查特定功能的访问权限"""
    try:
        # 验证功能类型
        valid_features = [
            FeatureType.MEMORIAL_SPACES,
            FeatureType.STORAGE_GB,
            FeatureType.AI_MINUTES,
            FeatureType.CUSTOM_DOMAINS,
            FeatureType.API_CALLS,
            FeatureType.PRIORITY_SUPPORT,
            FeatureType.ADVANCED_ANALYTICS,
            FeatureType.WHITE_LABEL,
            FeatureType.CUSTOM_BRANDING,
            FeatureType.BACKUP_RETENTION,
        ]

        if feature_type not in valid_features:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的功能类型: {feature_type}",
            )

        access_info = membership_service.check_feature_access(
            str(current_user.id), feature_type, amount
        )

        return FeatureAccessCheck(**access_info)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检查功能权限失败: {str(e)}",
        )


@router.post("/validate-operation", response_model=OperationValidation)
async def validate_operation(
    operation_type: str,
    file_size_gb: Optional[float] = None,
    minutes: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    membership_service: MembershipService = Depends(get_membership_service),
):
    """验证操作权限"""
    try:
        # 构建参数
        kwargs = {}
        if file_size_gb is not None:
            kwargs["file_size_gb"] = file_size_gb
        if minutes is not None:
            kwargs["minutes"] = minutes

        validation_result = membership_service.validate_operation(
            str(current_user.id), operation_type, **kwargs
        )

        return OperationValidation(**validation_result)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证操作权限失败: {str(e)}",
        )


@router.post("/consume-usage")
async def consume_feature_usage(
    request: UsageUpdateRequest,
    current_user: User = Depends(get_current_user),
    membership_service: MembershipService = Depends(get_membership_service),
):
    """消费功能使用量"""
    try:
        success = membership_service.consume_feature_usage(
            str(current_user.id), request.feature_type, request.amount
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="功能使用量已达上限或权限不足"
            )

        return {"success": True, "message": "使用量更新成功"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新使用量失败: {str(e)}",
        )


@router.get("/usage")
async def get_current_usage(
    current_user: User = Depends(get_current_user),
    membership_service: MembershipService = Depends(get_membership_service),
):
    """获取当前使用情况"""
    try:
        usage = membership_service.usage_tracker.get_user_usage(str(current_user.id))
        return {"usage": usage}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取使用情况失败: {str(e)}",
        )


@router.get("/upgrade-recommendations")
async def get_upgrade_recommendations(
    current_user: User = Depends(get_current_user),
    membership_service: MembershipService = Depends(get_membership_service),
):
    """获取升级建议"""
    try:
        recommendations = membership_service.get_upgrade_recommendations(
            str(current_user.id)
        )
        return {"recommendations": recommendations}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取升级建议失败: {str(e)}",
        )


@router.get("/tiers")
async def get_all_tiers():
    """获取所有会员等级信息"""
    try:
        from app.services.membership_service import (
            MembershipFeatureConfig,
            MembershipTier,
        )

        config = MembershipFeatureConfig()
        tiers_info = []

        tier_names = {
            MembershipTier.FREE: "免费版",
            MembershipTier.PREMIUM: "高级版",
            MembershipTier.FAMILY: "家族版",
            MembershipTier.ENTERPRISE: "企业版",
        }

        tier_descriptions = {
            MembershipTier.FREE: "基础功能，适合个人用户",
            MembershipTier.PREMIUM: "增强功能，适合活跃用户",
            MembershipTier.FAMILY: "家族功能，适合家庭用户",
            MembershipTier.ENTERPRISE: "企业功能，适合组织机构",
        }

        for tier, limits in config.TIER_CONFIGS.items():
            tiers_info.append(
                {
                    "tier": tier,
                    "name": tier_names.get(tier, tier),
                    "description": tier_descriptions.get(tier, ""),
                    "limits": limits,
                }
            )

        return {"tiers": tiers_info}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取等级信息失败: {str(e)}",
        )


# 功能快捷检查端点
@router.get("/can-create-memorial")
async def can_create_memorial(
    current_user: User = Depends(get_current_user),
    membership_service: MembershipService = Depends(get_membership_service),
):
    """检查是否可以创建纪念空间"""
    result = membership_service.can_create_memorial_space(str(current_user.id))
    return result


@router.get("/can-use-ai")
async def can_use_ai(
    minutes: int = 1,
    current_user: User = Depends(get_current_user),
    membership_service: MembershipService = Depends(get_membership_service),
):
    """检查是否可以使用AI服务"""
    result = membership_service.can_use_ai_service(str(current_user.id), minutes)
    return result


@router.get("/has-priority-support")
async def has_priority_support(
    current_user: User = Depends(get_current_user),
    membership_service: MembershipService = Depends(get_membership_service),
):
    """检查是否有优先支持"""
    result = membership_service.has_priority_support(str(current_user.id))
    return {"has_priority_support": result}


@router.get("/has-advanced-analytics")
async def has_advanced_analytics(
    current_user: User = Depends(get_current_user),
    membership_service: MembershipService = Depends(get_membership_service),
):
    """检查是否有高级分析功能"""
    result = membership_service.has_advanced_analytics(str(current_user.id))
    return {"has_advanced_analytics": result}
