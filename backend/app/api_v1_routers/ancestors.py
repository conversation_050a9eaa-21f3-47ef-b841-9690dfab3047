from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.dependencies import get_current_user, get_db  # 导入 get_current_user 依赖
from app.models.ancestor import Ancestor
from app.models.user import User
from app.schemas_pydantic.ancestor import AncestorCreate, AncestorResponse

router = APIRouter()


@router.get("/ancestors", response_model=list[AncestorResponse], summary="获取当前用户的所有先人")
async def get_all_ancestors(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """
    获取当前用户创建的所有先人信息。
    """
    ancestors = db.query(Ancestor).filter(Ancestor.user_id == current_user.id).all()
    return ancestors


@router.get(
    "/ancestors/{ancestor_id}", response_model=AncestorResponse, summary="获取特定先人"
)
async def get_single_ancestor(
    ancestor_id: int,  # 确保 ancestor_id 类型与数据库模型一致
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    根据ID获取特定先人的详细信息。
    """
    ancestor = (
        db.query(Ancestor)
        .filter(Ancestor.id == ancestor_id, Ancestor.user_id == current_user.id)
        .first()
    )
    if not ancestor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ancestor not found or you do not have permission to access it",
        )
    return ancestor


@router.post(
    "/ancestors",
    response_model=AncestorResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建新先人",
)
async def create_new_ancestor(
    ancestor_in: AncestorCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    为当前用户创建一个新的先人记录。
    """
    # 确保创建的先人属于当前用户
    ancestor_data = ancestor_in.model_dump()
    ancestor_data["user_id"] = current_user.id

    db_ancestor = Ancestor(**ancestor_data)
    db.add(db_ancestor)
    db.commit()
    db.refresh(db_ancestor)
    return db_ancestor
