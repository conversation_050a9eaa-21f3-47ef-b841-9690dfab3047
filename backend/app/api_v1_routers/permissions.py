from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session

from app import crud
from app import schemas_pydantic as schemas
from app.api_v1_routers.deps import get_current_active_user, get_db
from app.models_sqlalchemy import User

router = APIRouter()


@router.post("/check-access", response_model=schemas.AccessCheckResponse)
async def check_access(
    request: Request,
    access_request: schemas.AccessCheckRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> schemas.AccessCheckResponse:
    """
    检查用户对纪念空间的访问权限
    """
    # 获取纪念空间
    memorial_space = crud.get_memorial_space(
        db=db, space_id=access_request.memorial_space_id
    )
    if not memorial_space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # 获取客户端信息
    client_ip = request.client.host if request.client else None
    user_agent = request.headers.get("User-Agent")

    # 执行访问权限检查
    access_result = crud.check_memorial_space_access(
        db=db,
        space=memorial_space,
        user=current_user,
        access_type=access_request.access_type.value,
        access_password=access_request.access_password,
        ip_address=client_ip,
        user_agent=user_agent,
    )

    return access_result


@router.post("/{space_id}/grant", response_model=schemas.PermissionResponse)
async def grant_permission(
    space_id: UUID,
    permission_data: schemas.PermissionCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> schemas.PermissionResponse:
    """
    授予用户访问权限（仅创建者可用）
    """
    # 验证纪念空间是否存在
    memorial_space = crud.get_memorial_space(db=db, space_id=space_id)
    if not memorial_space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # 检查是否为创建者
    if memorial_space.creator_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only the creator can grant permissions",
        )

    # 验证被授权用户是否存在
    target_user = crud.user.get(db, permission_data.user_id)
    if not target_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Target user not found"
        )

    # 创建权限
    permission = crud.grant_permission(
        db=db,
        space_id=space_id,
        user_id=permission_data.user_id,
        permission_data=permission_data,
        granted_by=current_user.id,
    )

    return schemas.PermissionResponse(
        id=permission.id,
        memorial_space_id=permission.memorial_space_id,
        user_id=permission.user_id,
        permission_type=permission.permission_type,
        granted_by=permission.granted_by,
        granted_at=permission.granted_at,
        expires_at=permission.expires_at,
        is_active=permission.is_active,
        can_view_private_info=permission.can_view_private_info,
        can_moderate=permission.can_moderate,
        can_invite_others=permission.can_invite_others,
        user_name=target_user.full_name or target_user.username,
        grantor_name=current_user.full_name or current_user.username,
    )


@router.delete("/{space_id}/revoke/{user_id}")
async def revoke_permission(
    space_id: UUID,
    user_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
):
    """
    撤销用户权限（仅创建者可用）
    """
    # 验证纪念空间是否存在
    memorial_space = crud.get_memorial_space(db=db, space_id=space_id)
    if not memorial_space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # 检查是否为创建者
    if memorial_space.creator_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only the creator can revoke permissions",
        )

    # 撤销权限
    success = crud.revoke_permission(db=db, space_id=space_id, user_id=user_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Permission not found"
        )

    return {"message": "Permission revoked successfully"}


@router.get("/{space_id}/access-logs", response_model=schemas.AccessLogListResponse)
async def get_access_logs(
    space_id: UUID,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> schemas.AccessLogListResponse:
    """
    获取访问日志（仅创建者可用）
    """
    # 验证纪念空间是否存在
    memorial_space = crud.get_memorial_space(db=db, space_id=space_id)
    if not memorial_space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # 检查是否为创建者
    if memorial_space.creator_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only the creator can view access logs",
        )

    # 获取访问日志
    logs = crud.get_access_logs(db=db, space_id=space_id, skip=skip, limit=limit)

    # 统计总数
    from sqlalchemy import func

    total = (
        db.query(func.count(crud.models.AccessLog.id))
        .filter(crud.models.AccessLog.memorial_space_id == space_id)
        .scalar()
        or 0
    )

    return schemas.AccessLogListResponse(
        items=[
            schemas.AccessLogEntry(
                id=log.id,
                memorial_space_id=log.memorial_space_id,
                user_id=log.user_id,
                access_type=log.access_type,
                ip_address=log.ip_address,
                user_agent=log.user_agent,
                access_granted=log.access_granted,
                denial_reason=log.denial_reason,
                accessed_at=log.accessed_at,
                session_duration=log.session_duration,
                user_name=log.user.full_name or log.user.username
                if log.user
                else "匿名用户",
            )
            for log in logs
        ],
        total=total,
        page=(skip // limit) + 1 if limit > 0 else 1,
        size=limit,
    )


@router.get("/{space_id}/permissions", response_model=list[schemas.PermissionResponse])
async def get_space_permissions(
    space_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> list[schemas.PermissionResponse]:
    """
    获取纪念空间的所有权限（仅创建者可用）
    """
    # 验证纪念空间是否存在
    memorial_space = crud.get_memorial_space(db=db, space_id=space_id)
    if not memorial_space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # 检查是否为创建者
    if memorial_space.creator_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only the creator can view permissions",
        )

    # 获取权限列表
    permissions = (
        db.query(crud.models.MemorialSpacePermission)
        .filter(
            crud.models.MemorialSpacePermission.memorial_space_id == space_id,
            crud.models.MemorialSpacePermission.is_active == True,
        )
        .all()
    )

    return [
        schemas.PermissionResponse(
            id=permission.id,
            memorial_space_id=permission.memorial_space_id,
            user_id=permission.user_id,
            permission_type=permission.permission_type,
            granted_by=permission.granted_by,
            granted_at=permission.granted_at,
            expires_at=permission.expires_at,
            is_active=permission.is_active,
            can_view_private_info=permission.can_view_private_info,
            can_moderate=permission.can_moderate,
            can_invite_others=permission.can_invite_others,
            user_name=permission.user.full_name or permission.user.username,
            grantor_name=permission.grantor.full_name or permission.grantor.username,
        )
        for permission in permissions
    ]


@router.post("/batch-grant", response_model=list[schemas.PermissionResponse])
async def batch_grant_permissions(
    space_id: UUID,
    batch_operation: schemas.PermissionBatchOperation,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> list[schemas.PermissionResponse]:
    """
    批量授予权限（仅创建者可用）
    """
    # 验证纪念空间是否存在
    memorial_space = crud.get_memorial_space(db=db, space_id=space_id)
    if not memorial_space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # 检查是否为创建者
    if memorial_space.creator_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only the creator can grant permissions",
        )

    results = []
    for user_id in batch_operation.user_ids:
        # 验证用户是否存在
        target_user = crud.user.get(db, user_id)
        if not target_user:
            continue  # 跳过不存在的用户

        permission_data = schemas.PermissionCreate(
            user_id=user_id,
            memorial_space_id=space_id,
            permission_type=batch_operation.permission_type,
            expires_at=batch_operation.expires_at,
            can_view_private_info=batch_operation.can_view_private_info,
            can_moderate=batch_operation.can_moderate,
            can_invite_others=batch_operation.can_invite_others,
        )

        permission = crud.grant_permission(
            db=db,
            space_id=space_id,
            user_id=user_id,
            permission_data=permission_data,
            granted_by=current_user.id,
        )

        results.append(
            schemas.PermissionResponse(
                id=permission.id,
                memorial_space_id=permission.memorial_space_id,
                user_id=permission.user_id,
                permission_type=permission.permission_type,
                granted_by=permission.granted_by,
                granted_at=permission.granted_at,
                expires_at=permission.expires_at,
                is_active=permission.is_active,
                can_view_private_info=permission.can_view_private_info,
                can_moderate=permission.can_moderate,
                can_invite_others=permission.can_invite_others,
                user_name=target_user.full_name or target_user.username,
                grantor_name=current_user.full_name or current_user.username,
            )
        )

    return results
