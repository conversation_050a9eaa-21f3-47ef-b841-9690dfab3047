# 3D场景管理API
import logging
from typing import Any, List
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from app import crud
from app import models_sqlalchemy as models
from app import schemas_pydantic as schemas
from app.api_v1_routers import deps
from app.models.scene import Scene, SceneInteractionPoint
from app.schemas_pydantic.scene import (
    SceneCreate,
    SceneListResponse,
    SceneResponse,
    SceneUpdate,
    InteractionPointCreate,
    InteractionPointResponse,
    SCENE_TEMPLATES,
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=List[SceneListResponse])
def get_scenes(
    *,
    db: Session = Depends(deps.get_db),
    category: str = Query(None, description="按类别筛选场景"),
    supports_mobile: bool = Query(None, description="筛选支持移动端的场景"),
    is_premium: bool = Query(None, description="筛选付费/免费场景"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=100, description="限制返回的记录数"),
) -> Any:
    """
    获取可用场景列表
    支持按类别、移动端支持、付费状态筛选
    """
    try:
        logger.info(
            f"获取场景列表: category={category}, supports_mobile={supports_mobile}, is_premium={is_premium}"
        )

        query = db.query(Scene).filter(Scene.is_active == True)

        # 参数验证
        if category:
            valid_categories = [
                "buddhist",
                "christian",
                "traditional",
                "modern",
                "nature",
                "cosmos",
            ]
            if category not in valid_categories:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的场景类别。支持的类别: {', '.join(valid_categories)}",
                )
            query = query.filter(Scene.category == category)

        if supports_mobile is not None:
            query = query.filter(Scene.supports_mobile == supports_mobile)
        if is_premium is not None:
            query = query.filter(Scene.is_premium == is_premium)

        scenes = query.offset(skip).limit(limit).all()
        logger.info(f"成功获取 {len(scenes)} 个场景")
        return scenes

    except SQLAlchemyError as e:
        logger.error(f"数据库查询场景失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取场景列表失败"
        )
    except Exception as e:
        logger.error(f"获取场景列表时发生未知错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="服务器内部错误"
        )


@router.get("/{scene_id}", response_model=SceneResponse)
def get_scene(
    *,
    db: Session = Depends(deps.get_db),
    scene_id: UUID,
) -> Any:
    """
    获取场景详细信息
    """
    try:
        logger.info(f"获取场景详细信息: scene_id={scene_id}")

        scene = (
            db.query(Scene)
            .filter(Scene.id == scene_id, Scene.is_active == True)
            .first()
        )
        if not scene:
            logger.warning(f"场景未找到: {scene_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="场景不存在或已被删除"
            )

        logger.info(f"成功获取场景: {scene.name}")
        return scene

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        logger.error(f"数据库查询场景失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取场景信息失败"
        )
    except Exception as e:
        logger.error(f"获取场景信息时发生未知错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="服务器内部错误"
        )


@router.post("/", response_model=SceneResponse)
def create_scene(
    *,
    db: Session = Depends(deps.get_db),
    scene_in: SceneCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新场景（管理员功能）
    """
    try:
        logger.info(f"用户 {current_user.id} 尝试创建场景: {scene_in.name}")

        # 权限检查
        if not current_user.is_superuser:
            logger.warning(f"用户 {current_user.id} 无权限创建场景")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN, detail="权限不足，只有管理员可以创建场景"
            )

        # 验证场景名称唯一性
        existing_scene = db.query(Scene).filter(Scene.name == scene_in.name).first()
        if existing_scene:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="场景名称已存在，请使用其他名称"
            )

        # 验证类别
        valid_categories = [
            "buddhist",
            "christian",
            "traditional",
            "modern",
            "nature",
            "cosmos",
        ]
        if scene_in.category not in valid_categories:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的场景类别。支持的类别: {', '.join(valid_categories)}",
            )

        # 创建场景
        scene = Scene(
            name=scene_in.name,
            description=scene_in.description,
            category=scene_in.category,
            model_url=scene_in.model_url,
            texture_urls=scene_in.texture_urls,
            thumbnail_url=scene_in.thumbnail_url,
            lighting_config=scene_in.lighting_config,
            camera_config=scene_in.camera_config,
            interaction_config=scene_in.interaction_config,
            audio_config=scene_in.audio_config,
            is_premium=scene_in.is_premium,
            supports_mobile=scene_in.supports_mobile,
            min_performance_level=scene_in.min_performance_level,
        )

        db.add(scene)
        db.commit()
        db.refresh(scene)

        logger.info(f"成功创建场景: {scene.name} (ID: {scene.id})")
        return scene

    except HTTPException:
        raise
    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"数据库创建场景失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="创建场景失败"
        )
    except Exception as e:
        db.rollback()
        logger.error(f"创建场景时发生未知错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="服务器内部错误"
        )


@router.put("/{scene_id}", response_model=SceneResponse)
def update_scene(
    *,
    db: Session = Depends(deps.get_db),
    scene_id: UUID,
    scene_in: SceneUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新场景信息（管理员功能）
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
        )

    scene = db.query(Scene).filter(Scene.id == scene_id).first()
    if not scene:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Scene not found"
        )

    update_data = scene_in.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(scene, field, value)

    db.add(scene)
    db.commit()
    db.refresh(scene)
    return scene


@router.delete("/{scene_id}")
def delete_scene(
    *,
    db: Session = Depends(deps.get_db),
    scene_id: UUID,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除场景（软删除，管理员功能）
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
        )

    scene = db.query(Scene).filter(Scene.id == scene_id).first()
    if not scene:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Scene not found"
        )

    scene.is_active = False
    db.add(scene)
    db.commit()
    return {"detail": "Scene deleted successfully"}


@router.get("/categories/", response_model=List[str])
def get_scene_categories(
    *,
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    获取所有可用的场景类别
    """
    categories = (
        db.query(Scene.category).filter(Scene.is_active == True).distinct().all()
    )
    return [category[0] for category in categories]


@router.post("/{scene_id}/interaction-points/", response_model=InteractionPointResponse)
def create_interaction_point(
    *,
    db: Session = Depends(deps.get_db),
    scene_id: UUID,
    point_in: InteractionPointCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    为场景创建交互点（管理员功能）
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Not enough permissions"
        )

    # 验证场景存在
    scene = (
        db.query(Scene).filter(Scene.id == scene_id, Scene.is_active == True).first()
    )
    if not scene:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Scene not found"
        )

    interaction_point = SceneInteractionPoint(
        scene_id=scene_id,
        name=point_in.name,
        interaction_type=point_in.interaction_type,
        position=point_in.position,
        rotation=point_in.rotation,
        scale=point_in.scale,
        config=point_in.config,
    )

    db.add(interaction_point)
    db.commit()
    db.refresh(interaction_point)
    return interaction_point


@router.get(
    "/{scene_id}/interaction-points/", response_model=List[InteractionPointResponse]
)
def get_scene_interaction_points(
    *,
    db: Session = Depends(deps.get_db),
    scene_id: UUID,
) -> Any:
    """
    获取场景的交互点列表
    """
    # 验证场景存在
    scene = (
        db.query(Scene).filter(Scene.id == scene_id, Scene.is_active == True).first()
    )
    if not scene:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Scene not found"
        )

    points = (
        db.query(SceneInteractionPoint)
        .filter(
            SceneInteractionPoint.scene_id == scene_id,
            SceneInteractionPoint.is_active == True,
        )
        .all()
    )

    return points


@router.get("/templates/", response_model=dict)
def get_scene_templates() -> Any:
    """
    获取预定义场景模板
    """
    return SCENE_TEMPLATES


@router.post("/{scene_id}/usage/")
def track_scene_usage(
    *,
    db: Session = Depends(deps.get_db),
    scene_id: UUID,
) -> Any:
    """
    记录场景使用次数
    """
    scene = (
        db.query(Scene).filter(Scene.id == scene_id, Scene.is_active == True).first()
    )
    if not scene:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Scene not found"
        )

    scene.usage_count += 1
    db.add(scene)
    db.commit()
    return {"detail": "Usage tracked successfully"}
