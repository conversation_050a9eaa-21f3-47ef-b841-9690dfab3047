from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.db.session import get_db
from app.dependencies import get_current_user
from app.models_sqlalchemy import User
from app.services.analytics_service import AnalyticsService

router = APIRouter()


def get_analytics_service(db: Session = Depends(get_db)) -> AnalyticsService:
    """获取分析服务实例"""
    return AnalyticsService(db)


@router.get("/business")
async def get_business_analytics(
    date_range: str = Query("30d", description="时间范围: 7d, 30d, 90d, 1y"),
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service),
):
    """获取商业化数据分析"""
    try:
        # 检查用户权限 - 只有管理员或企业用户可以访问
        # if not hasattr(current_user, 'is_admin') or not current_user.is_admin:
        #     raise HTTPException(
        #         status_code=status.HTTP_403_FORBIDDEN,
        #         detail="权限不足，无法访问商业分析数据"
        #     )

        analytics_data = analytics_service.get_business_analytics(date_range)
        return {"success": True, "data": analytics_data, "date_range": date_range}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取商业分析数据失败: {str(e)}",
        )


@router.get("/revenue")
async def get_revenue_analytics(
    date_range: str = Query("30d", description="时间范围: 7d, 30d, 90d, 1y"),
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service),
):
    """获取收入分析数据"""
    try:
        analytics_data = analytics_service.get_business_analytics(date_range)
        return {
            "success": True,
            "data": analytics_data["revenue"],
            "date_range": date_range,
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取收入分析数据失败: {str(e)}",
        )


@router.get("/users")
async def get_user_analytics(
    date_range: str = Query("30d", description="时间范围: 7d, 30d, 90d, 1y"),
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service),
):
    """获取用户分析数据"""
    try:
        analytics_data = analytics_service.get_business_analytics(date_range)
        return {
            "success": True,
            "data": analytics_data["users"],
            "date_range": date_range,
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户分析数据失败: {str(e)}",
        )


@router.get("/conversion")
async def get_conversion_analytics(
    date_range: str = Query("30d", description="时间范围: 7d, 30d, 90d, 1y"),
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service),
):
    """获取转化分析数据"""
    try:
        analytics_data = analytics_service.get_business_analytics(date_range)
        return {
            "success": True,
            "data": analytics_data["conversion"],
            "date_range": date_range,
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取转化分析数据失败: {str(e)}",
        )


@router.get("/retention")
async def get_retention_analytics(
    date_range: str = Query("30d", description="时间范围: 7d, 30d, 90d, 1y"),
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service),
):
    """获取留存分析数据"""
    try:
        analytics_data = analytics_service.get_business_analytics(date_range)
        return {
            "success": True,
            "data": analytics_data["retention"],
            "date_range": date_range,
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取留存分析数据失败: {str(e)}",
        )


@router.get("/products")
async def get_product_analytics(
    date_range: str = Query("30d", description="时间范围: 7d, 30d, 90d, 1y"),
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service),
):
    """获取产品分析数据"""
    try:
        analytics_data = analytics_service.get_business_analytics(date_range)
        return {
            "success": True,
            "data": analytics_data["products"],
            "date_range": date_range,
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取产品分析数据失败: {str(e)}",
        )


@router.get("/growth")
async def get_growth_analytics(
    date_range: str = Query("30d", description="时间范围: 7d, 30d, 90d, 1y"),
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service),
):
    """获取增长分析数据"""
    try:
        analytics_data = analytics_service.get_business_analytics(date_range)
        return {
            "success": True,
            "data": analytics_data["growth"],
            "date_range": date_range,
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取增长分析数据失败: {str(e)}",
        )


@router.get("/membership")
async def get_membership_analytics(
    date_range: str = Query("30d", description="时间范围: 7d, 30d, 90d, 1y"),
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service),
):
    """获取会员权益分析数据"""
    try:
        analytics_data = analytics_service.get_business_analytics(date_range)
        return {
            "success": True,
            "data": analytics_data["membership"],
            "date_range": date_range,
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会员分析数据失败: {str(e)}",
        )


@router.get("/membership/recommendations")
async def get_membership_recommendations(
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service),
):
    """获取会员系统优化建议"""
    try:
        recommendations = analytics_service.get_membership_recommendations()
        return {"success": True, "data": recommendations}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会员优化建议失败: {str(e)}",
        )


@router.get("/dashboard/summary")
async def get_dashboard_summary(
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service),
):
    """获取仪表板概览数据"""
    try:
        analytics_data = analytics_service.get_business_analytics("30d")

        # 提取关键指标
        summary = {
            "kpi_cards": [
                {
                    "title": "月度经常性收入",
                    "value": f"¥{analytics_data['revenue']['mrr'] / 1000:.0f}K",
                    "change": analytics_data["revenue"]["revenue_growth"],
                    "format": "currency",
                },
                {
                    "title": "活跃用户数",
                    "value": f"{analytics_data['users']['active_users']['monthly']:,}",
                    "change": analytics_data["users"]["user_growth_rate"],
                    "format": "number",
                },
                {
                    "title": "付费转化率",
                    "value": f"{analytics_data['users']['conversion_rate']:.1f}%",
                    "change": 5.2,
                    "format": "percentage",
                },
                {
                    "title": "用户留存率",
                    "value": f"{analytics_data['retention']['retention_rates']['day30']}%",
                    "change": 2.8,
                    "format": "percentage",
                },
                {
                    "title": "会员健康度",
                    "value": f"{analytics_data['membership']['membership_health_score']:.1f}",
                    "change": 3.5,
                    "format": "score",
                },
            ],
            "alerts": [],
            "quick_stats": {
                "total_revenue": analytics_data["revenue"]["total_revenue"],
                "total_users": analytics_data["users"]["total_users"],
                "paying_users": analytics_data["users"]["paying_users"],
                "churn_rate": analytics_data["users"]["churn_rate"],
            },
        }

        # 添加警告信息
        membership_score = analytics_data["membership"]["membership_health_score"]
        if membership_score < 70:
            summary["alerts"].append(
                {
                    "type": "warning",
                    "message": f"会员健康度偏低 ({membership_score:.1f}分)，建议关注用户转化",
                }
            )

        churn_rate = analytics_data["users"]["churn_rate"]
        if churn_rate > 5:
            summary["alerts"].append(
                {"type": "error", "message": f"用户流失率过高 ({churn_rate:.1f}%)，需要优化用户体验"}
            )

        return {"success": True, "data": summary}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取仪表板概览失败: {str(e)}",
        )


@router.get("/export")
async def export_analytics_data(
    date_range: str = Query("30d", description="时间范围: 7d, 30d, 90d, 1y"),
    format: str = Query("json", description="导出格式: json, csv, excel"),
    current_user: User = Depends(get_current_user),
    analytics_service: AnalyticsService = Depends(get_analytics_service),
):
    """导出分析数据"""
    try:
        analytics_data = analytics_service.get_business_analytics(date_range)

        if format == "json":
            return {
                "success": True,
                "data": analytics_data,
                "export_format": "json",
                "export_date": date_range,
            }
        elif format == "csv":
            # 在实际实现中，这里应该返回CSV文件
            return {"success": True, "message": "CSV导出功能开发中", "export_format": "csv"}
        elif format == "excel":
            # 在实际实现中，这里应该返回Excel文件
            return {
                "success": True,
                "message": "Excel导出功能开发中",
                "export_format": "excel",
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=f"不支持的导出格式: {format}"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出分析数据失败: {str(e)}",
        )
