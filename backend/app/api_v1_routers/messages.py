from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import crud
from app import schemas_pydantic as schemas
from app.api_v1_routers.deps import get_current_user, get_db
from app.models_sqlalchemy import User
from app.services.content_moderation import moderate_message_content, ModerationAction

router = APIRouter()


@router.post("/", response_model=schemas.MemorialMessageResponse)
async def create_message(
    space_id: UUID,
    message_data: schemas.MemorialMessageCreateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> schemas.MemorialMessageResponse:
    """
    在指定纪念空间添加留言
    """
    # 验证纪念空间是否存在且用户有权限访问
    memorial_space = crud.get_memorial_space(db=db, space_id=space_id)
    if not memorial_space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # 检查访问权限
    if not crud.can_access_memorial_space(
        db=db, space=memorial_space, user=current_user
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this memorial space",
        )

    # 内容审核
    moderation_result = moderate_message_content(
        content=message_data.content, user_id=str(current_user.id)
    )

    # 根据审核结果决定处理方式
    if moderation_result.action == ModerationAction.BLOCK:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"消息被拒绝：{', '.join(moderation_result.reasons)}",
        )
    elif moderation_result.action == ModerationAction.REVIEW:
        # 需要人工审核的内容暂时不显示
        # 在实际应用中，这里可以创建一个待审核状态的消息
        raise HTTPException(
            status_code=status.HTTP_202_ACCEPTED,
            detail=f"消息已提交审核：{', '.join(moderation_result.reasons)}",
        )

    # 使用过滤后的内容创建留言
    filtered_message_data = schemas.MemorialMessageCreateRequest(
        content=moderation_result.filtered_content or message_data.content,
        is_anonymous=message_data.is_anonymous,
    )

    # 创建留言
    message = crud.create_message(
        db=db,
        space_id=space_id,
        user_id=current_user.id,
        message_data=filtered_message_data,
    )

    return schemas.MemorialMessageResponse(
        id=message.id,
        memorial_space_id=message.memorial_space_id,
        author_id=message.author_id,
        content=message.content,
        is_anonymous=message.is_anonymous,
        created_at=message.created_at,
        updated_at=message.updated_at,
        author_name=current_user.full_name or current_user.username
        if not message.is_anonymous
        else "匿名用户",
        author_avatar=current_user.avatar_url if not message.is_anonymous else None,
    )


@router.get("/", response_model=schemas.MemorialMessageListResponse)
async def get_messages(
    space_id: UUID,
    skip: int = 0,
    limit: int = 50,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> schemas.MemorialMessageListResponse:
    """
    获取指定纪念空间的留言列表
    """
    # 验证纪念空间是否存在且用户有权限访问
    memorial_space = crud.get_memorial_space(db=db, space_id=space_id)
    if not memorial_space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # 检查访问权限
    if not crud.can_access_memorial_space(
        db=db, space=memorial_space, user=current_user
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this memorial space",
        )

    # 获取留言列表
    messages = crud.get_messages_by_space(
        db=db, space_id=space_id, skip=skip, limit=limit
    )

    total = crud.count_messages_by_space(db=db, space_id=space_id)

    return schemas.MemorialMessageListResponse(
        items=[
            schemas.MemorialMessageResponse(
                id=message.id,
                memorial_space_id=message.memorial_space_id,
                author_id=message.author_id,
                content=message.content,
                is_anonymous=message.is_anonymous,
                created_at=message.created_at,
                updated_at=message.updated_at,
                author_name=message.author.full_name or message.author.username
                if not message.is_anonymous and message.author
                else "匿名用户",
                author_avatar=message.author.avatar_url
                if not message.is_anonymous and message.author
                else None,
            )
            for message in messages
        ],
        total=total,
        skip=skip,
        limit=limit,
    )


@router.put("/{message_id}", response_model=schemas.MemorialMessageResponse)
async def update_message(
    space_id: UUID,
    message_id: UUID,
    message_update: schemas.MemorialMessageUpdateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> schemas.MemorialMessageResponse:
    """
    更新留言内容（仅限留言作者）
    """
    # 获取留言
    message = crud.get_message(db=db, message_id=message_id)
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Message not found"
        )

    # 验证留言是否属于指定的纪念空间
    if message.space_id != space_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Message does not belong to this memorial space",
        )

    # 验证用户权限（只有留言作者可以修改）
    if message.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only edit your own messages",
        )

    # 如果更新内容，需要进行内容审核
    if hasattr(message_update, "content") and message_update.content:
        moderation_result = moderate_message_content(
            content=message_update.content, user_id=str(current_user.id)
        )

        # 根据审核结果决定处理方式
        if moderation_result.action == ModerationAction.BLOCK:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"消息被拒绝：{', '.join(moderation_result.reasons)}",
            )
        elif moderation_result.action == ModerationAction.REVIEW:
            raise HTTPException(
                status_code=status.HTTP_202_ACCEPTED,
                detail=f"消息已提交审核：{', '.join(moderation_result.reasons)}",
            )

        # 使用过滤后的内容
        if moderation_result.filtered_content:
            message_update.content = moderation_result.filtered_content

    # 更新留言
    updated_message = crud.update_message(
        db=db, message=message, message_update=message_update
    )

    return schemas.MemorialMessageResponse(
        id=updated_message.id,
        memorial_space_id=updated_message.memorial_space_id,
        author_id=updated_message.author_id,
        content=updated_message.content,
        is_anonymous=updated_message.is_anonymous,
        created_at=updated_message.created_at,
        updated_at=updated_message.updated_at,
        author_name=current_user.full_name or current_user.username
        if not updated_message.is_anonymous
        else "匿名用户",
        author_avatar=current_user.avatar_url
        if not updated_message.is_anonymous
        else None,
    )


@router.delete("/{message_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_message(
    space_id: UUID,
    message_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> None:
    """
    删除留言（仅限留言作者或纪念空间创建者）
    """
    # 获取留言
    message = crud.get_message(db=db, message_id=message_id)
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Message not found"
        )

    # 验证留言是否属于指定的纪念空间
    if message.space_id != space_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Message does not belong to this memorial space",
        )

    # 获取纪念空间信息
    memorial_space = crud.get_memorial_space(db=db, space_id=space_id)
    if not memorial_space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # 验证用户权限（留言作者或纪念空间创建者可以删除）
    if (
        message.user_id != current_user.id
        and memorial_space.creator_id != current_user.id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only delete your own messages or messages in your memorial spaces",
        )

    # 删除留言
    crud.delete_message(db=db, message=message)
