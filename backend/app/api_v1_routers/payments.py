"""
Memorial 支付系统 API路由
支持微信支付、支付宝、国际支付等多种支付方式
与前端Web和Flutter移动端完全同步
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from datetime import datetime, timedelta
import uuid
import hashlib
import hmac
import json

from app.db.session import get_db
from app.dependencies import get_current_user
from app.models_sqlalchemy import User
from app.services.payment_processor import PaymentProcessorService
from app.services.payment_webhook import PaymentWebhookService

router = APIRouter()

# ==================== 请求和响应模型 ====================


class PaymentProviderResponse(BaseModel):
    id: str
    name: str
    type: str  # 'domestic' | 'international'
    supported_methods: List[Dict[str, Any]]
    enabled: bool


class PaymentMethodResponse(BaseModel):
    id: str
    name: str
    icon: str
    description: str
    minimum_amount: float
    maximum_amount: float
    currency: List[str]


class CreatePaymentRequest(BaseModel):
    order_id: str = Field(..., description="订单ID")
    amount: float = Field(..., gt=0, description="支付金额")
    currency: str = Field(default="CNY", description="货币类型")
    description: str = Field(..., description="支付描述")
    provider_id: str = Field(..., description="支付提供商ID")
    method_id: str = Field(..., description="支付方式ID")
    return_url: Optional[str] = Field(None, description="支付成功返回URL")
    cancel_url: Optional[str] = Field(None, description="支付取消返回URL")
    metadata: Optional[Dict[str, Any]] = Field(None, description="附加数据")


class PaymentResponse(BaseModel):
    payment_id: str
    status: str  # 'pending' | 'completed' | 'failed' | 'cancelled'
    redirect_url: Optional[str] = None
    message: Optional[str] = None
    transaction_id: Optional[str] = None
    created_at: datetime


class PaymentStatusResponse(BaseModel):
    payment_id: str
    status: str
    message: Optional[str] = None
    transaction_id: Optional[str] = None
    completed_at: Optional[datetime] = None


class PaymentHistoryItem(BaseModel):
    id: str
    order_id: str
    amount: float
    currency: str
    status: str
    payment_method: str
    created_at: datetime
    completed_at: Optional[datetime]
    description: str
    metadata: Optional[Dict[str, Any]]


class PaymentHistoryResponse(BaseModel):
    payments: List[PaymentHistoryItem]
    total: int
    page: int
    limit: int


# ==================== 支付提供商管理 ====================


@router.get("/providers", response_model=Dict[str, List[PaymentProviderResponse]])
async def get_payment_providers(
    db: Session = Depends(get_db), current_user: User = Depends(get_current_user)
):
    """获取可用的支付提供商列表"""
    try:
        # 模拟支付提供商配置（实际应从数据库或配置文件读取）
        providers = [
            {
                "id": "wechat",
                "name": "微信支付",
                "type": "domestic",
                "supported_methods": [
                    {
                        "id": "wechat_h5",
                        "name": "微信H5支付",
                        "icon": "💚",
                        "description": "适用于手机浏览器",
                        "minimum_amount": 0.01,
                        "maximum_amount": 50000.0,
                        "currency": ["CNY"],
                    },
                    {
                        "id": "wechat_app",
                        "name": "微信APP支付",
                        "icon": "💚",
                        "description": "适用于移动应用",
                        "minimum_amount": 0.01,
                        "maximum_amount": 50000.0,
                        "currency": ["CNY"],
                    },
                ],
                "enabled": True,
            },
            {
                "id": "alipay",
                "name": "支付宝",
                "type": "domestic",
                "supported_methods": [
                    {
                        "id": "alipay_web",
                        "name": "支付宝网页支付",
                        "icon": "🔵",
                        "description": "适用于电脑浏览器",
                        "minimum_amount": 0.01,
                        "maximum_amount": 50000.0,
                        "currency": ["CNY"],
                    },
                    {
                        "id": "alipay_app",
                        "name": "支付宝APP支付",
                        "icon": "🔵",
                        "description": "适用于移动应用",
                        "minimum_amount": 0.01,
                        "maximum_amount": 50000.0,
                        "currency": ["CNY"],
                    },
                ],
                "enabled": True,
            },
            {
                "id": "unionpay",
                "name": "银联支付",
                "type": "domestic",
                "supported_methods": [
                    {
                        "id": "unionpay_web",
                        "name": "银联网页支付",
                        "icon": "💳",
                        "description": "支持各大银行卡",
                        "minimum_amount": 0.01,
                        "maximum_amount": 100000.0,
                        "currency": ["CNY"],
                    }
                ],
                "enabled": True,
            },
            {
                "id": "paypal",
                "name": "PayPal",
                "type": "international",
                "supported_methods": [
                    {
                        "id": "paypal_web",
                        "name": "PayPal支付",
                        "icon": "🟦",
                        "description": "国际主流支付方式",
                        "minimum_amount": 0.01,
                        "maximum_amount": 10000.0,
                        "currency": ["USD", "EUR", "GBP", "JPY"],
                    }
                ],
                "enabled": True,
            },
            {
                "id": "stripe",
                "name": "Stripe",
                "type": "international",
                "supported_methods": [
                    {
                        "id": "stripe_card",
                        "name": "信用卡支付",
                        "icon": "🟣",
                        "description": "Visa、MasterCard等",
                        "minimum_amount": 0.50,
                        "maximum_amount": 99999.0,
                        "currency": ["USD", "EUR", "GBP", "CNY"],
                    }
                ],
                "enabled": True,
            },
        ]

        return {"providers": providers}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取支付提供商失败: {str(e)}")


@router.get(
    "/providers/{provider_id}/methods",
    response_model=Dict[str, List[PaymentMethodResponse]],
)
async def get_payment_methods(
    provider_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取特定支付提供商的支付方式"""
    try:
        # 根据provider_id返回对应的支付方式
        providers_data = {
            "wechat": [
                {
                    "id": "wechat_h5",
                    "name": "微信H5支付",
                    "icon": "💚",
                    "description": "适用于手机浏览器",
                    "minimum_amount": 0.01,
                    "maximum_amount": 50000.0,
                    "currency": ["CNY"],
                },
                {
                    "id": "wechat_app",
                    "name": "微信APP支付",
                    "icon": "💚",
                    "description": "适用于移动应用",
                    "minimum_amount": 0.01,
                    "maximum_amount": 50000.0,
                    "currency": ["CNY"],
                },
            ],
            "alipay": [
                {
                    "id": "alipay_web",
                    "name": "支付宝网页支付",
                    "icon": "🔵",
                    "description": "适用于电脑浏览器",
                    "minimum_amount": 0.01,
                    "maximum_amount": 50000.0,
                    "currency": ["CNY"],
                },
                {
                    "id": "alipay_app",
                    "name": "支付宝APP支付",
                    "icon": "🔵",
                    "description": "适用于移动应用",
                    "minimum_amount": 0.01,
                    "maximum_amount": 50000.0,
                    "currency": ["CNY"],
                },
            ],
        }

        methods = providers_data.get(provider_id, [])
        return {"methods": methods}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取支付方式失败: {str(e)}")


# ==================== 支付处理 ====================


@router.post("/create", response_model=PaymentResponse)
async def create_payment(
    request: CreatePaymentRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """创建支付订单"""
    try:
        # 生成支付ID
        payment_id = str(uuid.uuid4())

        # 验证支付金额
        if request.amount <= 0:
            raise HTTPException(status_code=400, detail="支付金额必须大于0")

        # 验证支付提供商和方式
        if request.provider_id not in [
            "wechat",
            "alipay",
            "unionpay",
            "paypal",
            "stripe",
        ]:
            raise HTTPException(status_code=400, detail="不支持的支付提供商")

        # 创建支付记录（这里应该保存到数据库）
        payment_data = {
            "payment_id": payment_id,
            "user_id": current_user.id,
            "order_id": request.order_id,
            "amount": request.amount,
            "currency": request.currency,
            "description": request.description,
            "provider_id": request.provider_id,
            "method_id": request.method_id,
            "status": "pending",
            "created_at": datetime.utcnow(),
            "metadata": request.metadata or {},
        }

        # 根据支付方式生成支付URL或参数
        redirect_url = None
        if request.provider_id in ["wechat", "alipay"]:
            # 对于微信和支付宝，生成支付页面URL
            redirect_url = f"https://payment.memorial.com/pay/{payment_id}?provider={request.provider_id}&method={request.method_id}"
        elif request.provider_id == "paypal":
            redirect_url = f"https://www.paypal.com/checkoutnow?token={payment_id}"
        elif request.provider_id == "stripe":
            redirect_url = f"https://checkout.stripe.com/pay/{payment_id}"

        # 这里应该调用实际的支付处理服务
        # payment_processor = PaymentProcessorService()
        # result = await payment_processor.create_payment(payment_data)

        return PaymentResponse(
            payment_id=payment_id,
            status="pending",
            redirect_url=redirect_url,
            message="支付订单创建成功",
            transaction_id=f"TXN_{payment_id[:8]}",
            created_at=datetime.utcnow(),
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建支付失败: {str(e)}")


@router.get("/{payment_id}/status", response_model=PaymentStatusResponse)
async def get_payment_status(
    payment_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """查询支付状态"""
    try:
        # 这里应该从数据库查询实际的支付状态
        # 模拟支付状态查询
        import random

        statuses = ["pending", "completed", "failed"]
        # 模拟随机状态，实际应该查询真实状态
        status = random.choice(statuses)

        response = PaymentStatusResponse(
            payment_id=payment_id,
            status=status,
            transaction_id=f"TXN_{payment_id[:8]}" if status == "completed" else None,
            completed_at=datetime.utcnow() if status == "completed" else None,
        )

        if status == "completed":
            response.message = "支付成功"
        elif status == "failed":
            response.message = "支付失败，请重试"
        else:
            response.message = "支付处理中"

        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询支付状态失败: {str(e)}")


@router.post("/{payment_id}/cancel", response_model=PaymentStatusResponse)
async def cancel_payment(
    payment_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """取消支付"""
    try:
        # 这里应该调用支付处理服务取消支付
        # payment_processor = PaymentProcessorService()
        # result = await payment_processor.cancel_payment(payment_id)

        return PaymentStatusResponse(
            payment_id=payment_id, status="cancelled", message="支付已取消"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"取消支付失败: {str(e)}")


# ==================== 支付历史 ====================


@router.get("/history", response_model=PaymentHistoryResponse)
async def get_payment_history(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    method: Optional[str] = Query(None),
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取用户支付历史记录"""
    try:
        # 这里应该从数据库查询用户的支付历史
        # 模拟支付历史数据
        mock_payments = [
            {
                "id": str(uuid.uuid4()),
                "order_id": f"ORD_{i:06d}",
                "amount": round(19.9 + i * 10.5, 2),
                "currency": "CNY",
                "status": "completed" if i % 3 == 0 else "pending",
                "payment_method": "微信支付" if i % 2 == 0 else "支付宝",
                "created_at": datetime.utcnow() - timedelta(days=i),
                "completed_at": datetime.utcnow() - timedelta(days=i, hours=1)
                if i % 3 == 0
                else None,
                "description": f"Memorial 订阅服务 - 订单{i}",
                "metadata": {"platform": "web" if i % 2 == 0 else "mobile"},
            }
            for i in range(min(limit, 50))  # 限制模拟数据数量
        ]

        # 应用筛选条件
        filtered_payments = mock_payments
        if status:
            filtered_payments = [p for p in filtered_payments if p["status"] == status]
        if method:
            filtered_payments = [
                p for p in filtered_payments if method in p["payment_method"]
            ]

        # 分页处理
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_payments = filtered_payments[start_idx:end_idx]

        return PaymentHistoryResponse(
            payments=paginated_payments,
            total=len(filtered_payments),
            page=page,
            limit=limit,
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取支付历史失败: {str(e)}")


# ==================== 支付回调处理 ====================


@router.post("/{payment_id}/callback")
async def handle_payment_callback(
    payment_id: str, callback_data: Dict[str, Any], db: Session = Depends(get_db)
):
    """处理支付回调（供支付提供商调用）"""
    try:
        # 验证回调签名（实际项目中必须验证）
        # webhook_service = PaymentWebhookService()
        # if not webhook_service.verify_signature(callback_data):
        #     raise HTTPException(status_code=403, detail="回调签名验证失败")

        # 更新支付状态
        payment_status = callback_data.get("status", "unknown")
        transaction_id = callback_data.get("transaction_id")

        # 这里应该更新数据库中的支付记录
        # payment_processor = PaymentProcessorService()
        # await payment_processor.update_payment_status(payment_id, payment_status, transaction_id)

        return {
            "success": True,
            "payment_id": payment_id,
            "status": payment_status,
            "message": "回调处理成功",
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理支付回调失败: {str(e)}")


# ==================== 支付配置和工具 ====================


@router.get("/config")
async def get_payment_config(current_user: User = Depends(get_current_user)):
    """获取支付配置信息"""
    return {
        "supported_currencies": ["CNY", "USD", "EUR", "GBP", "JPY"],
        "default_currency": "CNY",
        "minimum_amount": 0.01,
        "maximum_amount": 100000.0,
        "webhook_url": "https://api.memorial.com/api/v1/payments/{payment_id}/callback",
        "payment_timeout": 900,  # 15分钟
        "retry_attempts": 3,
    }


@router.post("/webhook/test")
async def test_webhook(webhook_data: Dict[str, Any], db: Session = Depends(get_db)):
    """测试支付回调（开发环境使用）"""
    try:
        # 这是一个测试端点，用于模拟支付回调
        payment_id = webhook_data.get("payment_id")
        status = webhook_data.get("status", "completed")

        if not payment_id:
            raise HTTPException(status_code=400, detail="缺少payment_id参数")

        return {
            "success": True,
            "payment_id": payment_id,
            "status": status,
            "message": "测试回调处理成功",
            "timestamp": datetime.utcnow().isoformat(),
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试回调失败: {str(e)}")
