from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import crud
from app import schemas_pydantic as schemas
from app.api_v1_routers.deps import get_current_user, get_db
from app.models_sqlalchemy import User

router = APIRouter()


@router.post("/", response_model=schemas.FamilyResponse)
async def create_family(
    family_data: schemas.FamilyCreateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> schemas.FamilyResponse:
    """
    创建新的家族
    """
    # 创建家族
    family = crud.create_family(
        db=db, creator_id=current_user.id, family_data=family_data
    )

    return schemas.FamilyResponse(
        id=family.id,
        name=family.name,
        description=family.description,
        creator_id=family.creator_id,
        family_motto=family.family_motto,
        origin_location=family.origin_location,
        established_date=family.established_date,
        privacy_level=family.privacy_level,
        member_count=1,  # 创建者自动成为成员
        created_at=family.created_at,
        updated_at=family.updated_at,
        creator_name=str(current_user.full_name or current_user.username),
    )


@router.get("/{family_id}", response_model=schemas.FamilyResponse)
async def get_family(
    family_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> schemas.FamilyResponse:
    """
    获取家族详情
    """
    # 获取家族信息
    family = crud.get_family(db=db, family_id=family_id)
    if not family:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Family not found"
        )

    # 检查用户是否有权限访问该家族
    if not crud.is_family_member(db=db, family_id=family_id, user_id=current_user.id):
        if family.privacy_level != "public":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this family",
            )

    # 获取成员数量
    member_count = crud.count_family_members(db=db, family_id=family_id)

    return schemas.FamilyResponse(
        id=family.id,
        name=family.name,
        description=family.description,
        creator_id=family.creator_id,
        family_motto=family.family_motto,
        origin_location=family.origin_location,
        established_date=family.established_date,
        privacy_level=family.privacy_level,
        member_count=member_count,
        created_at=family.created_at,
        updated_at=family.updated_at,
        creator_name=family.creator.full_name or family.creator.username,
    )


@router.put("/{family_id}", response_model=schemas.FamilyResponse)
async def update_family(
    family_id: UUID,
    family_update: schemas.FamilyUpdateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> schemas.FamilyResponse:
    """
    更新家族信息（仅限创建者和管理员）
    """
    # 获取家族信息
    family = crud.get_family(db=db, family_id=family_id)
    if not family:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Family not found"
        )

    # 检查用户权限
    if not crud.can_manage_family(db=db, family_id=family_id, user_id=current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to manage this family",
        )

    # 更新家族信息
    updated_family = crud.update_family(
        db=db, family=family, family_update=family_update
    )

    # 获取成员数量
    member_count = crud.count_family_members(db=db, family_id=family_id)

    return schemas.FamilyResponse(
        id=updated_family.id,
        name=updated_family.name,
        description=updated_family.description,
        creator_id=updated_family.creator_id,
        family_motto=updated_family.family_motto,
        origin_location=updated_family.origin_location,
        established_date=updated_family.established_date,
        privacy_level=updated_family.privacy_level,
        member_count=member_count,
        created_at=updated_family.created_at,
        updated_at=updated_family.updated_at,
        creator_name=updated_family.creator.full_name
        or updated_family.creator.username,
    )


@router.post(
    "/{family_id}/invitations", response_model=schemas.FamilyInvitationResponse
)
async def create_family_invitation(
    family_id: UUID,
    invitation_data: schemas.FamilyInvitationCreateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> schemas.FamilyInvitationResponse:
    """
    邀请用户加入家族
    """
    # 验证家族是否存在
    family = crud.get_family(db=db, family_id=family_id)
    if not family:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Family not found"
        )

    # 检查用户权限
    if not crud.can_invite_to_family(
        db=db, family_id=family_id, user_id=current_user.id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to invite members to this family",
        )

    # 创建邀请
    invitation = crud.create_family_invitation(
        db=db,
        family_id=family_id,
        inviter_id=current_user.id,
        invitation_data=invitation_data,
    )

    return schemas.FamilyInvitationResponse(
        id=invitation.id,
        family_id=invitation.family_id,
        inviter_id=invitation.inviter_id,
        invitee_email=invitation.invitee_email,
        message=invitation.message,
        status=invitation.status,
        created_at=invitation.created_at,
        expires_at=invitation.expires_at,
        family_name=family.name,
        inviter_name=str(current_user.full_name or current_user.username),
    )


@router.post("/{family_id}/members", response_model=schemas.FamilyMemberResponse)
async def join_family(
    family_id: UUID,
    join_data: schemas.FamilyJoinRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> schemas.FamilyMemberResponse:
    """
    加入家族（通过邀请码或申请）
    """
    # 验证家族是否存在
    family = crud.get_family(db=db, family_id=family_id)
    if not family:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Family not found"
        )

    # 检查用户是否已经是家族成员
    if crud.is_family_member(db=db, family_id=family_id, user_id=current_user.id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You are already a member of this family",
        )

    # 验证邀请码或处理申请
    if join_data.invitation_code:
        # 通过邀请码加入
        invitation = crud.validate_invitation_code(
            db=db, family_id=family_id, invitation_code=join_data.invitation_code
        )
        if not invitation:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired invitation code",
            )
    else:
        # 申请加入（需要审核）
        if family.privacy_level == "private":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="This family requires an invitation to join",
            )

    # 添加家族成员
    member = crud.add_family_member(
        db=db,
        family_id=family_id,
        user_id=current_user.id,
        role=join_data.role or "member",
    )

    return schemas.FamilyMemberResponse(
        id=member.id,
        family_id=member.family_id,
        user_id=member.user_id,
        role=member.role,
        joined_at=member.joined_at,
        user_name=str(current_user.full_name or current_user.username),
        user_avatar=str(current_user.avatar_url),
    )


@router.get("/{family_id}/members", response_model=schemas.FamilyMemberListResponse)
async def get_family_members(
    family_id: UUID,
    skip: int = 0,
    limit: int = 50,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> schemas.FamilyMemberListResponse:
    """
    获取家族成员列表
    """
    # 验证家族是否存在
    family = crud.get_family(db=db, family_id=family_id)
    if not family:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Family not found"
        )

    # 检查用户是否有权限查看成员列表
    if not crud.can_view_family_members(
        db=db, family_id=family_id, user_id=current_user.id
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to view family members",
        )

    # 获取成员列表
    members = crud.get_family_members(
        db=db, family_id=family_id, skip=skip, limit=limit
    )

    total = crud.count_family_members(db=db, family_id=family_id)

    return schemas.FamilyMemberListResponse(
        items=[
            schemas.FamilyMemberResponse(
                id=member.id,
                family_id=member.family_id,
                user_id=member.user_id,
                role=member.role,
                joined_at=member.joined_at,
                user_name=member.user.full_name or member.user.username,
                user_avatar=member.user.avatar_url,
            )
            for member in members
        ],
        total=total,
        skip=skip,
        limit=limit,
    )


# 族谱功能路由
@router.post(
    "/{family_id}/genealogy/nodes", response_model=schemas.GenealogyNodeResponse
)
async def create_genealogy_node(
    family_id: UUID,
    node_data: schemas.GenealogyNodeCreateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> schemas.GenealogyNodeResponse:
    """
    创建族谱节点
    """
    # 验证家族是否存在且用户有权限
    family = crud.get_family(db=db, family_id=family_id)
    if not family:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Family not found"
        )

    if not crud.can_edit_genealogy(db=db, family_id=family_id, user_id=current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to edit this family's genealogy",
        )

    # 创建族谱节点
    node = crud.create_genealogy_node(
        db=db, family_id=family_id, creator_id=current_user.id, node_data=node_data
    )

    return schemas.GenealogyNodeResponse(
        id=node.id,
        family_id=node.family_id,
        name=node.name,
        gender=node.gender,
        birth_date=node.birth_date,
        death_date=node.death_date,
        biography=node.biography,
        photo_url=node.photo_url,
        generation=node.generation,
        position_x=node.position_x,
        position_y=node.position_y,
        created_at=node.created_at,
        updated_at=node.updated_at,
    )


@router.get("/{family_id}/genealogy", response_model=schemas.GenealogyResponse)
async def get_genealogy(
    family_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> schemas.GenealogyResponse:
    """
    获取家族族谱数据
    """
    # 验证家族是否存在且用户有权限
    family = crud.get_family(db=db, family_id=family_id)
    if not family:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Family not found"
        )

    if not crud.can_view_genealogy(db=db, family_id=family_id, user_id=current_user.id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to view this family's genealogy",
        )

    # 获取族谱数据
    nodes = crud.get_genealogy_nodes(db=db, family_id=family_id)
    relationships = crud.get_genealogy_relationships(db=db, family_id=family_id)

    return schemas.GenealogyResponse(
        family_id=family_id,
        nodes=[
            schemas.GenealogyNodeResponse(
                id=node.id,
                family_id=node.family_id,
                name=node.name,
                gender=node.gender,
                birth_date=node.birth_date,
                death_date=node.death_date,
                biography=node.biography,
                photo_url=node.photo_url,
                generation=node.generation,
                position_x=node.position_x,
                position_y=node.position_y,
                created_at=node.created_at,
                updated_at=node.updated_at,
            )
            for node in nodes
        ],
        relationships=[
            schemas.GenealogyRelationshipResponse(
                id=rel.id,
                family_id=rel.family_id,
                from_node_id=rel.from_node_id,
                to_node_id=rel.to_node_id,
                relationship_type=rel.relationship_type,
                created_at=rel.created_at,
            )
            for rel in relationships
        ],
    )
