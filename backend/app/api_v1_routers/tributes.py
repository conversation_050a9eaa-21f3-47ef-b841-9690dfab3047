from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import crud
from app import schemas_pydantic as schemas
from app.api_v1_routers.deps import get_current_active_user, get_db
from app.models_sqlalchemy import User

router = APIRouter()


@router.post("/", response_model=schemas.TributeResponse)
async def create_tribute(
    space_id: UUID,
    tribute_data: schemas.TributeCreateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> schemas.TributeResponse:
    """
    在指定纪念空间执行祭拜动作
    """
    # 验证纪念空间是否存在且用户有权限访问
    memorial_space = crud.get_memorial_space(db=db, space_id=space_id)
    if not memorial_space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # 检查访问权限
    if not crud.can_access_memorial_space(
        db=db, space=memorial_space, user=current_user
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this memorial space",
        )

    # 创建祭拜记录
    tribute = crud.create_tribute(
        db=db, space_id=space_id, user_id=current_user.id, tribute_data=tribute_data
    )

    return schemas.TributeResponse(
        id=tribute.id,
        memorial_space_id=tribute.memorial_space_id,
        user_id=tribute.user_id,
        tribute_type=tribute.tribute_type,
        message=tribute.message,
        is_anonymous=tribute.is_anonymous,
        tribute_items=tribute.tribute_items or [],
        duration_seconds=tribute.duration_seconds or 0,
        coordinates=tribute.coordinates,
        client_info=tribute.client_info,
        created_at=tribute.created_at,
        user_name=str(current_user.full_name or current_user.username),
    )


@router.get("/", response_model=schemas.TributeListResponse)
async def get_tributes(
    space_id: UUID,
    skip: int = 0,
    limit: int = 50,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> schemas.TributeListResponse:
    """
    获取指定纪念空间的祭拜记录列表
    """
    # 验证纪念空间是否存在且用户有权限访问
    memorial_space = crud.get_memorial_space(db=db, space_id=space_id)
    if not memorial_space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # 检查访问权限
    if not crud.can_access_memorial_space(
        db=db, space=memorial_space, user=current_user
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this memorial space",
        )

    # 获取祭拜记录
    tributes = crud.get_tributes_by_space(
        db=db, space_id=space_id, skip=skip, limit=limit
    )

    total = crud.count_tributes_by_space(db=db, space_id=space_id)

    return schemas.TributeListResponse(
        items=[
            schemas.TributeResponse(
                id=tribute.id,
                memorial_space_id=tribute.memorial_space_id,
                user_id=tribute.user_id,
                tribute_type=tribute.tribute_type,
                message=tribute.message,
                is_anonymous=tribute.is_anonymous,
                tribute_items=tribute.tribute_items or [],
                duration_seconds=tribute.duration_seconds or 0,
                coordinates=tribute.coordinates,
                client_info=tribute.client_info,
                created_at=tribute.created_at,
                user_name=str(tribute.user.full_name or tribute.user.username)
                if not tribute.is_anonymous
                else "匿名用户",
            )
            for tribute in tributes
        ],
        total=total,
        skip=skip,
        limit=limit,
    )


@router.get("/stats", response_model=schemas.TributeStatsResponse)
async def get_tribute_stats(
    space_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> schemas.TributeStatsResponse:
    """
    获取指定纪念空间的祭拜统计信息
    """
    # 验证纪念空间是否存在且用户有权限访问
    memorial_space = crud.get_memorial_space(db=db, space_id=space_id)
    if not memorial_space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # 检查访问权限
    if not crud.can_access_memorial_space(
        db=db, space=memorial_space, user=current_user
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this memorial space",
        )

    # 获取统计信息
    stats = crud.get_tribute_stats(db=db, space_id=space_id)

    return schemas.TributeStatsResponse(
        memorial_space_id=space_id,
        total_tributes=stats.get("total_tributes", 0),
        tribute_types=stats.get("tribute_types", {}),
        recent_tributes=[
            schemas.TributeResponse(
                id=tribute.id,
                memorial_space_id=tribute.memorial_space_id,
                user_id=tribute.user_id,
                tribute_type=tribute.tribute_type,
                message=tribute.message,
                is_anonymous=tribute.is_anonymous,
                tribute_items=tribute.tribute_items or [],
                duration_seconds=tribute.duration_seconds or 0,
                coordinates=tribute.coordinates,
                client_info=tribute.client_info,
                created_at=tribute.created_at,
                user_name=str(tribute.user.full_name or tribute.user.username)
                if not tribute.is_anonymous
                else "匿名用户",
            )
            for tribute in stats.get("recent_tributes", [])
        ],
    )


@router.get("/analytics", response_model=dict)
async def get_tribute_analytics(
    space_id: UUID,
    days: int = 30,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
) -> dict:
    """
    获取指定纪念空间的祭拜分析数据
    """
    # 验证纪念空间是否存在且用户有权限访问
    memorial_space = crud.get_memorial_space(db=db, space_id=space_id)
    if not memorial_space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # 只有创建者可以查看详细分析数据
    if memorial_space.creator_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only the creator can view detailed analytics",
        )

    # 获取分析数据
    analytics = crud.get_tribute_analytics(db=db, space_id=space_id, days=days)

    return analytics
