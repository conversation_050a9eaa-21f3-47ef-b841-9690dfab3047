import uuid
from pathlib import Path
from typing import Any
from uuid import UUID

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status
from sqlalchemy.orm import Session

from app import crud
from app import models_sqlalchemy as models
from app import schemas_pydantic as schemas
from app.api_v1_routers import deps  # Assuming deps.py for dependencies
from app.core.config import settings  # For UPLOAD_DIRECTORY

router = APIRouter()

# Ensure UPLOAD_DIRECTORY exists
UPLOAD_DIR = Path(settings.UPLOAD_DIRECTORY)
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)


@router.post("/", response_model=schemas.MemorialAssetResponse)
async def create_memorial_asset(
    space_id: uuid.UUID,
    *,
    db: Session = Depends(deps.get_db),
    file: UploadFile = File(...),
    asset_type: schemas.AssetTypeEnum = Form(...),
    title: str | None = Form(None),
    description: str | None = Form(None),
    display_order: int | None = Form(0),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Upload a new asset (image, video, etc.) to a memorial space.
    The space_id will be part of the path, e.g., /memorial-spaces/{space_id}/assets
    """
    space = crud.memorial_space.get(db=db, id=space_id)
    if not space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )
    if (
        space.creator_id != current_user.id
    ):  # Or other permission check (e.g., collaborators)
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to upload to this space",
        )

    # Generate a unique filename to prevent collisions
    file_extension = Path(file.filename).suffix
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_location = UPLOAD_DIR / unique_filename

    # Read file content to get size
    file_content = await file.read()
    file_size = len(file_content)

    try:
        with open(file_location, "wb+") as file_object:
            file_object.write(file_content)
    except Exception as e:
        # Log the error e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Could not save file: {e}",
        ) from e

    # Construct file URL (this depends on how files are served)
    # For local dev, it might be http://localhost:8000/static/uploads/filename
    # For production, it would be a CDN URL or similar.
    # This needs to be configured based on your static file serving setup.
    # A more robust way is to have a settings for STATIC_FILES_BASE_URL
    # file_url = f"{settings.SERVER_HOST}{file_url_path}" if settings.SERVER_HOST else file_url_path
    file_url_str = f"/static/uploads/{unique_filename}"  # Simplification for now, assuming relative path or nginx handles prefix
    # Convert to HttpUrl for schema validation
    from pydantic import HttpUrl

    file_url = HttpUrl(
        f"http://localhost:8000{file_url_str}"
    )  # Use full URL for HttpUrl validation

    asset_in = schemas.MemorialAssetCreate(
        asset_type=asset_type,
        title=title,
        description=description,
        display_order=display_order,
        file_url=file_url,
        original_filename=file.filename,
        file_size=file_size,
        asset_metadata=None,
        is_ai_enhanced=False,
    )

    db_asset = crud.memorial_asset.create_with_uploader_and_space(
        db=db,
        obj_in=asset_in,
        uploader_id=UUID(str(current_user.id)),
        space_id=space_id,
        file_url=str(file_url),
    )

    # If the uploaded asset is a cover image, update the memorial space's cover_image_url
    if asset_type == schemas.AssetTypeEnum.cover_image:
        crud.memorial_asset.update_cover_image_for_space(
            db=db, space_id=space_id, cover_image_url=str(file_url)
        )
        # Refresh space object if needed by frontend immediately, or rely on next GET

    # Convert SQLAlchemy model to Pydantic schema for response
    # model_validate will map db_asset.file_url to response_asset.file_url
    response_asset = schemas.MemorialAssetResponse.model_validate(db_asset)

    return response_asset


@router.get(
    "/", response_model=schemas.MemorialAssetListResponse
)  # API Spec: MemorialAssetListResponse
def list_memorial_assets(
    space_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    asset_type: schemas.AssetTypeEnum | None = None,  # Query parameter
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(
        deps.get_current_active_user
    ),  # Or a more permissive check for public spaces
) -> Any:
    """
    Retrieve assets for a memorial space.
    Optionally filter by asset_type (e.g., 'life_photo', 'video').
    """
    space = crud.memorial_space.get(db=db, id=space_id)
    if not space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # Access control: Simplified - only creator can list all assets for now.
    # Public spaces might allow viewing certain asset types.
    if (
        space.creator_id != current_user.id
        and space.privacy_level != schemas.PrivacyLevelEnum.public
    ):
        # Add more granular checks if public spaces can show some assets
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to view assets for this space",
        )

    assets = crud.memorial_asset.get_multi_by_space(
        db=db, space_id=space_id, asset_type=asset_type, skip=skip, limit=limit
    )

    # Convert to Pydantic response models, ensuring 'url' is correctly formatted
    response_assets = []
    for asset in assets:
        pydantic_asset = schemas.MemorialAssetResponse.model_validate(asset)
        # model_validate should map asset.file_url to pydantic_asset.file_url
        response_assets.append(pydantic_asset)

    total = crud.memorial_asset.count_by_space(
        db=db, space_id=space_id, asset_type=asset_type
    )

    return schemas.MemorialAssetListResponse(
        assets=response_assets,
        total_count=total,
        # links can be added here if needed
    )


@router.delete("/{asset_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_memorial_asset(
    space_id: uuid.UUID,
    asset_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> None:
    """
    Delete an asset from a memorial space.
    """
    asset = crud.memorial_asset.get(db=db, id=asset_id)
    if not asset:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Asset not found"
        )
    if asset.space_id != space_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Asset does not belong to this space",
        )

    space = crud.memorial_space.get(db=db, id=asset.space_id)
    if (
        not space or space.creator_id != current_user.id
    ):  # Only creator can delete assets
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to delete this asset",
        )

    # Delete the physical file
    try:
        file_path_to_delete = (
            UPLOAD_DIR / Path(asset.file_url).name
        )  # Assumes file_url's last part is filename
        if file_path_to_delete.exists():
            file_path_to_delete.unlink()
        # Also delete thumbnail if it exists
        if asset.thumbnail_url:
            thumb_path_to_delete = UPLOAD_DIR / Path(asset.thumbnail_url).name
            if thumb_path_to_delete.exists():
                thumb_path_to_delete.unlink()
    except Exception as e:
        # Log error, but proceed to delete DB record
        print(f"Error deleting asset file {asset.file_url}: {e}")
        # Note: We don't re-raise here as we want to continue with DB deletion

    # If this was the cover image, clear it from the memorial space
    if space.cover_image_url == asset.file_url:
        crud.memorial_asset.update_cover_image_for_space(
            db=db, space_id=UUID(str(space.id)), cover_image_url=None
        )

    crud.memorial_asset.remove(db=db, id=asset_id)
    return None


# TODO: Add PUT endpoint for updating asset metadata (title, description, display_order)
# @router.put("/{asset_id}", response_model=schemas.MemorialAssetResponse)
# def update_memorial_asset_metadata(...): ...
