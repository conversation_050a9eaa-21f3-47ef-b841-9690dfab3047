import uuid
from typing import Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app import crud
from app import models_sqlalchemy as models
from app import schemas_pydantic as schemas
from app.api_v1_routers import deps  # Assuming deps.py for dependencies

router = APIRouter()


@router.post("/", response_model=schemas.MemorialEventResponse)
def create_memorial_event(
    space_id: uuid.UUID,  # Path parameter from parent router
    *,
    db: Session = Depends(deps.get_db),
    event_in: schemas.MemorialEventCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create a new event for a memorial space.
    The space_id will be part of the path, e.g., /memorial-spaces/{space_id}/events
    """
    space = crud.memorial_space.get(db=db, id=space_id)
    if not space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )
    if space.creator_id != current_user.id:  # Only creator can add events
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to add events to this space",
        )

    # Frontend uses 'year' for event_date. API spec uses 'event_date'.
    # Pydantic schema MemorialEventCreate expects 'event_date'.
    # Ensure frontend sends 'event_date' or transform 'year' to 'event_date' here if necessary.
    # For now, assuming event_in.event_date is correctly populated.

    db_event = crud.memorial_event.create_for_space(
        db=db, obj_in=event_in, space_id=space_id
    )

    # Map event_date to year for the response to match frontend's TimelineEvent structure
    response_event = schemas.MemorialEventResponse.model_validate(db_event)
    response_event.year = (
        db_event.event_date
    )  # Simple mapping, might need parsing if event_date is complex
    return response_event


@router.get(
    "/", response_model=schemas.MemorialEventListResponse
)  # API Spec: MemorialEventListResponse
def list_memorial_events(
    space_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(
        deps.get_current_active_user
    ),  # Or more permissive for public spaces
) -> Any:
    """
    Retrieve events for a memorial space.
    """
    space = crud.memorial_space.get(db=db, id=space_id)
    if not space:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Memorial space not found"
        )

    # Access control (simplified)
    if (
        space.creator_id != current_user.id
        and space.privacy_level != schemas.PrivacyLevelEnum.public
    ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to view events for this space",
        )

    events = crud.memorial_event.get_multi_by_space(
        db=db, space_id=space_id, skip=skip, limit=limit
    )

    response_events = []
    for event in events:
        pydantic_event = schemas.MemorialEventResponse.model_validate(event)
        pydantic_event.year = event.event_date  # Map event_date to year
        response_events.append(pydantic_event)

    total = crud.memorial_event.count_by_space(db=db, space_id=space_id)
    return schemas.MemorialEventListResponse(
        events=response_events,
        total_count=total,
    )


@router.delete("/{event_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_memorial_event(
    space_id: uuid.UUID,
    event_id: uuid.UUID,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> None:
    """
    Delete an event from a memorial space.
    """
    event = crud.memorial_event.get(db=db, id=event_id)
    if not event:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Event not found"
        )
    if event.memorial_space_id != space_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Event does not belong to this space",
        )

    space = crud.memorial_space.get(db=db, id=event.memorial_space_id)
    if (
        not space or space.creator_id != current_user.id
    ):  # Only creator can delete events
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to delete this event",
        )

    crud.memorial_event.remove(db=db, id=event_id)
    return None


# TODO: Add PUT endpoint for updating event details
# @router.put("/{event_id}", response_model=schemas.MemorialEventResponse)
# def update_memorial_event(...): ...
