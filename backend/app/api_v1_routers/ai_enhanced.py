# 增强AI功能路由
import logging
import os
import uuid
from pathlib import Path
from typing import Dict, Any

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status
from fastapi.responses import JSONResponse

from app.services.replicate_ai_service import replicate_ai_service
from app.api_v1_routers.deps import get_current_active_user
from app.models_sqlalchemy import User

logger = logging.getLogger(__name__)

# 配置上传目录
UPLOAD_FOLDER = Path("uploads")
AI_RESULTS_FOLDER = UPLOAD_FOLDER / "ai_results"
UPLOAD_FOLDER.mkdir(exist_ok=True)
AI_RESULTS_FOLDER.mkdir(exist_ok=True)

router = APIRouter()


async def save_upload_file(upload_file: UploadFile, destination_folder: Path) -> Path:
    """保存上传的文件到指定目录"""
    try:
        # 生成唯一文件名
        filename = f"{uuid.uuid4()}_{upload_file.filename}"
        file_path = destination_folder / filename

        # 确保目录存在
        destination_folder.mkdir(parents=True, exist_ok=True)

        # 异步写入文件
        with open(file_path, "wb") as buffer:
            while contents := await upload_file.read(1024 * 1024):  # Read in 1MB chunks
                buffer.write(contents)
        return file_path
    except Exception as e:
        logger.error(f"保存文件失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"文件保存失败: {e}"
        ) from e


def create_file_url(file_path: Path, base_url: str = "http://localhost:8000") -> str:
    """创建文件访问URL"""
    relative_path = file_path.relative_to(UPLOAD_FOLDER.parent)
    return f"{base_url.rstrip('/')}/{relative_path}"


@router.post("/photo-restore", summary="AI照片修复")
async def photo_restore(
    file: UploadFile = File(..., description="需要修复的照片"),
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    使用AI修复老照片，支持去除噪点、修复破损、增强清晰度
    """
    try:
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件")

        # 验证文件类型
        if not file.content_type or not file.content_type.startswith("image/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="只支持图片文件"
            )

        # 保存原始文件
        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)

        # 创建文件URL供Replicate使用
        original_url = create_file_url(original_file_path)

        # 调用Replicate AI服务
        result = await replicate_ai_service.restore_photo(original_url)

        if result["success"]:
            return JSONResponse(
                content={
                    "success": True,
                    "message": "照片修复成功",
                    "original_url": result["original_url"],
                    "result_url": result["result_url"],
                    "model_used": result.get("model_used"),
                    "options_used": result.get("options_used"),
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("error", "照片修复失败"),
            )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"照片修复失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e


@router.post("/photo-enhance", summary="AI照片增强")
async def photo_enhance(
    file: UploadFile = File(..., description="需要增强的照片"),
    scale: int = Form(4, description="放大倍数 (2-8)", ge=2, le=8),
    face_enhance: bool = Form(True, description="是否增强面部"),
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    使用AI增强照片分辨率和质量
    """
    try:
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件")

        # 验证文件类型
        if not file.content_type or not file.content_type.startswith("image/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="只支持图片文件"
            )

        # 保存原始文件
        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)
        original_url = create_file_url(original_file_path)

        # 调用Replicate AI服务
        result = await replicate_ai_service.enhance_photo(
            original_url, scale=scale, face_enhance=face_enhance
        )

        if result["success"]:
            return JSONResponse(
                content={
                    "success": True,
                    "message": "照片增强成功",
                    "original_url": result["original_url"],
                    "result_url": result["result_url"],
                    "scale_used": result.get("scale_used"),
                    "model_used": result.get("model_used"),
                    "options_used": result.get("options_used"),
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("error", "照片增强失败"),
            )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"照片增强失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e


@router.post("/photo-remove-bg", summary="AI背景移除")
async def photo_remove_bg(
    file: UploadFile = File(..., description="需要移除背景的照片"),
    model: str = Form("u2net", description="AI模型 (u2net 或 silueta)"),
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    使用AI移除照片背景，生成透明背景图片
    """
    try:
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件")

        # 验证文件类型
        if not file.content_type or not file.content_type.startswith("image/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="只支持图片文件"
            )

        # 验证模型类型
        if model not in ["u2net", "silueta"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="模型类型必须是 u2net 或 silueta",
            )

        # 保存原始文件
        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)
        original_url = create_file_url(original_file_path)

        # 调用Replicate AI服务
        result = await replicate_ai_service.remove_background(original_url, model=model)

        if result["success"]:
            return JSONResponse(
                content={
                    "success": True,
                    "message": "背景移除成功",
                    "original_url": result["original_url"],
                    "result_url": result["result_url"],
                    "model_used": result.get("model_used"),
                    "options_used": result.get("options_used"),
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("error", "背景移除失败"),
            )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"背景移除失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e


@router.post("/photo-colorize", summary="AI照片上色")
async def photo_colorize(
    file: UploadFile = File(..., description="需要上色的黑白照片"),
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    使用AI为黑白照片上色
    """
    try:
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件")

        # 验证文件类型
        if not file.content_type or not file.content_type.startswith("image/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="只支持图片文件"
            )

        # 保存原始文件
        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)
        original_url = create_file_url(original_file_path)

        # 调用Replicate AI服务
        result = await replicate_ai_service.colorize_photo(original_url)

        if result["success"]:
            return JSONResponse(
                content={
                    "success": True,
                    "message": "照片上色成功",
                    "original_url": result["original_url"],
                    "result_url": result["result_url"],
                    "model_used": result.get("model_used"),
                    "options_used": result.get("options_used"),
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("error", "照片上色失败"),
            )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"照片上色失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e


@router.get("/health", summary="AI服务健康检查")
async def health_check() -> JSONResponse:
    """
    检查AI服务状态
    """
    try:
        # 这里可以添加实际的健康检查逻辑
        return JSONResponse(
            content={
                "status": "healthy",
                "service": "Replicate AI Service",
                "api_key_configured": bool(replicate_ai_service.api_key),
                "message": "AI服务运行正常",
            }
        )
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"status": "unhealthy", "error": str(e)},
        )


@router.get("/models", summary="获取可用AI模型")
async def get_available_models() -> JSONResponse:
    """
    获取可用的AI模型列表
    """
    models = {
        "photo_restoration": {
            "name": "Real-ESRGAN",
            "description": "照片修复和增强",
            "capabilities": ["去噪", "修复破损", "提升清晰度"],
        },
        "photo_enhancement": {
            "name": "Real-ESRGAN",
            "description": "照片分辨率增强",
            "capabilities": ["2-8倍放大", "面部增强", "细节优化"],
        },
        "background_removal": {
            "name": "REMBG",
            "description": "背景移除",
            "models": {"u2net": "通用背景移除模型", "silueta": "人像专用背景移除模型"},
        },
        "photo_colorization": {
            "name": "Bringing Old Photos Back to Life",
            "description": "黑白照片上色",
            "capabilities": ["智能上色", "高分辨率输出", "保持原始细节"],
        },
    }

    return JSONResponse(
        content={"success": True, "models": models, "total_models": len(models)}
    )
