# 增强AI功能路由 - Phase 1.2 优化版本
import logging
import os
import time
import uuid
import asyncio
from pathlib import Path
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status, BackgroundTasks
from fastapi.responses import J<PERSON>NResponse

from app.ai_services.replicate_service import ReplicateService
from app.ai_services.ai_task_manager import AITaskManager, TaskType, TaskStatus, task_manager
from app.ai_services.batch_processor import BatchProcessor
from app.ai_services.voice_service import MultiLanguageVoiceCloner, VoicePersonalizationService
from app.core.config import settings
from app.api_v1_routers.deps import get_current_active_user
from app.models_sqlalchemy import User

logger = logging.getLogger(__name__)

# 配置上传目录
UPLOAD_FOLDER = Path("uploads")
AI_RESULTS_FOLDER = UPLOAD_FOLDER / "ai_results"
BATCH_FOLDER = UPLOAD_FOLDER / "batch"
VOICE_FOLDER = UPLOAD_FOLDER / "voice"

for folder in [UPLOAD_FOLDER, AI_RESULTS_FOLDER, BATCH_FOLDER, VOICE_FOLDER]:
    folder.mkdir(exist_ok=True)

router = APIRouter()

# 全局服务实例
_replicate_service = None
_batch_processor = None
_voice_cloner = None
_voice_personalization = None


def get_replicate_service() -> ReplicateService:
    """获取Replicate服务实例"""
    global _replicate_service
    if _replicate_service is None:
        _replicate_service = ReplicateService(api_key=settings.REPLICATE_API_KEY)
    return _replicate_service


def get_batch_processor() -> BatchProcessor:
    """获取批量处理器实例"""
    global _batch_processor
    if _batch_processor is None:
        _batch_processor = BatchProcessor(get_replicate_service())
    return _batch_processor


def get_voice_cloner() -> MultiLanguageVoiceCloner:
    """获取语音克隆器实例"""
    global _voice_cloner
    if _voice_cloner is None:
        _voice_cloner = MultiLanguageVoiceCloner(get_replicate_service())
    return _voice_cloner


def get_voice_personalization() -> VoicePersonalizationService:
    """获取语音个性化服务实例"""
    global _voice_personalization
    if _voice_personalization is None:
        _voice_personalization = VoicePersonalizationService(get_voice_cloner())
    return _voice_personalization


async def save_upload_file(upload_file: UploadFile, destination_folder: Path) -> Path:
    """保存上传的文件到指定目录"""
    try:
        # 生成唯一文件名
        filename = f"{uuid.uuid4()}_{upload_file.filename}"
        file_path = destination_folder / filename

        # 确保目录存在
        destination_folder.mkdir(parents=True, exist_ok=True)

        # 异步写入文件
        with open(file_path, "wb") as buffer:
            while contents := await upload_file.read(1024 * 1024):  # Read in 1MB chunks
                buffer.write(contents)
        return file_path
    except Exception as e:
        logger.error(f"保存文件失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"文件保存失败: {e}"
        ) from e


def create_file_url(file_path: Path, base_url: str = "http://localhost:8000") -> str:
    """创建文件访问URL"""
    relative_path = file_path.relative_to(UPLOAD_FOLDER.parent)
    return f"{base_url.rstrip('/')}/{relative_path}"


@router.post("/photo-restore", summary="AI照片修复")
async def photo_restore(
    file: UploadFile = File(..., description="需要修复的照片"),
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    使用AI修复老照片，支持去除噪点、修复破损、增强清晰度
    """
    try:
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件")

        # 验证文件类型
        if not file.content_type or not file.content_type.startswith("image/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="只支持图片文件"
            )

        # 保存原始文件
        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)

        # 创建文件URL供Replicate使用
        original_url = create_file_url(original_file_path)

        # 调用Replicate AI服务
        result = await replicate_ai_service.restore_photo(original_url)

        if result["success"]:
            return JSONResponse(
                content={
                    "success": True,
                    "message": "照片修复成功",
                    "original_url": result["original_url"],
                    "result_url": result["result_url"],
                    "model_used": result.get("model_used"),
                    "options_used": result.get("options_used"),
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("error", "照片修复失败"),
            )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"照片修复失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e


# ==================== 批量处理API ====================

@router.post("/batch/photo-process", summary="批量照片处理")
async def batch_photo_process(
    files: List[UploadFile] = File(..., description="要处理的照片文件列表"),
    operation: str = Form(..., description="处理操作 (restore, colorize, enhance, remove_bg)"),
    scale: Optional[int] = Form(4, description="增强倍数 (仅用于enhance)", ge=2, le=8),
    face_enhance: Optional[bool] = Form(True, description="是否增强面部 (仅用于enhance)"),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    批量处理照片，支持修复、上色、增强、背景移除等操作
    """
    try:
        # 验证操作类型
        valid_operations = ['restore', 'colorize', 'enhance', 'remove_bg']
        if operation not in valid_operations:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的操作类型: {operation}，支持的操作: {', '.join(valid_operations)}"
            )

        # 验证文件数量
        if len(files) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="至少需要提供一个文件"
            )

        if len(files) > 50:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="单次最多处理50个文件"
            )

        # 保存上传的文件
        file_paths = []
        for file in files:
            if not file.content_type or not file.content_type.startswith("image/"):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"文件 {file.filename} 不是图片格式"
                )

            file_path = await save_upload_file(file, BATCH_FOLDER)
            file_paths.append(str(file_path))

        # 创建批量处理任务
        options = {}
        if operation == 'enhance':
            options = {'scale': scale, 'face_enhance': face_enhance}

        task_id = await task_manager.create_task(
            TaskType.BATCH_PROCESS,
            {
                'file_paths': file_paths,
                'operation': operation,
                'options': options,
                'user_id': str(current_user.id)
            },
            user_id=str(current_user.id),
            priority=5
        )

        # 获取处理优化建议
        batch_processor = get_batch_processor()
        optimization = await batch_processor.optimize_batch_processing(
            file_paths, operation
        )

        return JSONResponse(content={
            "success": True,
            "message": "批量处理任务已创建",
            "task_id": task_id,
            "total_files": len(file_paths),
            "operation": operation,
            "optimization": optimization,
            "status_url": f"/api/v1/ai-enhanced/task/{task_id}/status"
        })

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"批量处理创建失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量处理创建失败: {str(e)}"
        ) from e


@router.get("/task/{task_id}/status", summary="获取任务状态")
async def get_task_status(
    task_id: str,
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    获取AI任务的处理状态和进度
    """
    try:
        task = await task_manager.get_task(task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        # 检查用户权限
        if task.user_id != str(current_user.id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此任务"
            )

        response_data = {
            "task_id": task.task_id,
            "task_type": task.task_type.value,
            "status": task.status.value,
            "progress": {
                "current_step": task.progress.current_step,
                "total_steps": task.progress.total_steps,
                "percentage": task.progress.percentage,
                "step_description": task.progress.step_description,
                "estimated_time_remaining": task.progress.estimated_time_remaining
            },
            "created_at": task.created_at.isoformat() if task.created_at else None,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None
        }

        # 如果任务完成，添加结果
        if task.status == TaskStatus.COMPLETED:
            response_data["result"] = task.output_data

        # 如果任务失败，添加错误信息
        if task.status == TaskStatus.FAILED:
            response_data["error"] = task.error_message

        return JSONResponse(content=response_data)

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取任务状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务状态失败: {str(e)}"
        ) from e


@router.post("/task/{task_id}/cancel", summary="取消任务")
async def cancel_task(
    task_id: str,
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    取消正在进行的AI任务
    """
    try:
        task = await task_manager.get_task(task_id)
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        # 检查用户权限
        if task.user_id != str(current_user.id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权取消此任务"
            )

        # 检查任务状态
        if task.status not in [TaskStatus.PENDING, TaskStatus.PROCESSING]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"任务状态为 {task.status.value}，无法取消"
            )

        await task_manager.cancel_task(task_id)

        return JSONResponse(content={
            "success": True,
            "message": "任务已取消",
            "task_id": task_id
        })

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"取消任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消任务失败: {str(e)}"
        ) from e


@router.get("/tasks", summary="获取用户任务列表")
async def get_user_tasks(
    status: Optional[str] = None,
    limit: int = 50,
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    获取当前用户的AI任务列表
    """
    try:
        # 验证状态参数
        task_status = None
        if status:
            try:
                task_status = TaskStatus(status)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的任务状态: {status}"
                )

        tasks = await task_manager.get_user_tasks(
            str(current_user.id),
            status=task_status,
            limit=limit
        )

        task_list = []
        for task in tasks:
            task_info = {
                "task_id": task.task_id,
                "task_type": task.task_type.value,
                "status": task.status.value,
                "progress_percentage": task.progress.percentage,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None
            }

            if task.status == TaskStatus.FAILED:
                task_info["error"] = task.error_message

            task_list.append(task_info)

        return JSONResponse(content={
            "success": True,
            "tasks": task_list,
            "total": len(task_list),
            "filter_status": status
        })

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务列表失败: {str(e)}"
        ) from e


@router.post("/photo-enhance", summary="AI照片增强")
async def photo_enhance(
    file: UploadFile = File(..., description="需要增强的照片"),
    scale: int = Form(4, description="放大倍数 (2-8)", ge=2, le=8),
    face_enhance: bool = Form(True, description="是否增强面部"),
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    使用AI增强照片分辨率和质量
    """
    try:
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件")

        # 验证文件类型
        if not file.content_type or not file.content_type.startswith("image/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="只支持图片文件"
            )

        # 保存原始文件
        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)
        original_url = create_file_url(original_file_path)

        # 调用Replicate AI服务
        result = await replicate_ai_service.enhance_photo(
            original_url, scale=scale, face_enhance=face_enhance
        )

        if result["success"]:
            return JSONResponse(
                content={
                    "success": True,
                    "message": "照片增强成功",
                    "original_url": result["original_url"],
                    "result_url": result["result_url"],
                    "scale_used": result.get("scale_used"),
                    "model_used": result.get("model_used"),
                    "options_used": result.get("options_used"),
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("error", "照片增强失败"),
            )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"照片增强失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e


@router.post("/photo-remove-bg", summary="AI背景移除")
async def photo_remove_bg(
    file: UploadFile = File(..., description="需要移除背景的照片"),
    model: str = Form("u2net", description="AI模型 (u2net 或 silueta)"),
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    使用AI移除照片背景，生成透明背景图片
    """
    try:
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件")

        # 验证文件类型
        if not file.content_type or not file.content_type.startswith("image/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="只支持图片文件"
            )

        # 验证模型类型
        if model not in ["u2net", "silueta"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="模型类型必须是 u2net 或 silueta",
            )

        # 保存原始文件
        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)
        original_url = create_file_url(original_file_path)

        # 调用Replicate AI服务
        result = await replicate_ai_service.remove_background(original_url, model=model)

        if result["success"]:
            return JSONResponse(
                content={
                    "success": True,
                    "message": "背景移除成功",
                    "original_url": result["original_url"],
                    "result_url": result["result_url"],
                    "model_used": result.get("model_used"),
                    "options_used": result.get("options_used"),
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("error", "背景移除失败"),
            )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"背景移除失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e


@router.post("/photo-colorize", summary="AI照片上色")
async def photo_colorize(
    file: UploadFile = File(..., description="需要上色的黑白照片"),
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    使用AI为黑白照片上色
    """
    try:
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="未提供文件")

        # 验证文件类型
        if not file.content_type or not file.content_type.startswith("image/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="只支持图片文件"
            )

        # 保存原始文件
        original_file_path = await save_upload_file(file, UPLOAD_FOLDER)
        original_url = create_file_url(original_file_path)

        # 调用Replicate AI服务
        result = await replicate_ai_service.colorize_photo(original_url)

        if result["success"]:
            return JSONResponse(
                content={
                    "success": True,
                    "message": "照片上色成功",
                    "original_url": result["original_url"],
                    "result_url": result["result_url"],
                    "model_used": result.get("model_used"),
                    "options_used": result.get("options_used"),
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("error", "照片上色失败"),
            )

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"照片上色失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"处理失败: {str(e)}"
        ) from e


# ==================== 语音处理API ====================

@router.post("/voice/validate-sample", summary="验证语音样本")
async def validate_voice_sample(
    file: UploadFile = File(..., description="语音样本文件"),
    language: str = Form("zh", description="语言代码"),
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    验证语音样本的质量和适用性
    """
    try:
        # 验证文件类型
        if not file.content_type or not file.content_type.startswith("audio/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只支持音频文件"
            )

        # 保存音频文件
        audio_path = await save_upload_file(file, VOICE_FOLDER)

        # 验证语音样本
        voice_cloner = get_voice_cloner()
        validation_result = voice_cloner.validate_voice_sample(str(audio_path), language)

        return JSONResponse(content={
            "success": True,
            "validation": validation_result,
            "language": language,
            "supported_languages": voice_cloner.get_supported_languages()
        })

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"语音样本验证失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"语音样本验证失败: {str(e)}"
        ) from e


@router.post("/voice/clone-enhanced", summary="增强语音克隆")
async def clone_voice_enhanced(
    voice_file: UploadFile = File(..., description="语音样本文件"),
    text: str = Form(..., description="要生成的文本"),
    language: str = Form("zh", description="语言代码"),
    enhance_quality: bool = Form(True, description="是否增强音质"),
    speed: float = Form(1.0, description="语速调整 (0.5-2.0)", ge=0.5, le=2.0),
    pitch_shift: float = Form(0.0, description="音调调整 (-12到12半音)", ge=-12, le=12),
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    使用增强功能进行语音克隆
    """
    try:
        # 验证文件类型
        if not voice_file.content_type or not voice_file.content_type.startswith("audio/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只支持音频文件"
            )

        # 验证文本长度
        if len(text.strip()) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文本内容不能为空"
            )

        if len(text) > 1000:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文本长度不能超过1000字符"
            )

        # 保存语音文件
        voice_path = await save_upload_file(voice_file, VOICE_FOLDER)

        # 创建语音克隆任务
        task_id = await task_manager.create_task(
            TaskType.VOICE_CLONE,
            {
                'voice_sample_path': str(voice_path),
                'text': text,
                'language': language,
                'enhance_quality': enhance_quality,
                'speed': speed,
                'pitch_shift': pitch_shift,
                'user_id': str(current_user.id)
            },
            user_id=str(current_user.id),
            priority=7
        )

        return JSONResponse(content={
            "success": True,
            "message": "语音克隆任务已创建",
            "task_id": task_id,
            "text_length": len(text),
            "language": language,
            "estimated_time": "30-60秒",
            "status_url": f"/api/v1/ai-enhanced/task/{task_id}/status"
        })

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"语音克隆失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"语音克隆失败: {str(e)}"
        ) from e


@router.post("/voice/batch-clone", summary="批量语音克隆")
async def batch_voice_clone(
    voice_file: UploadFile = File(..., description="语音样本文件"),
    texts: str = Form(..., description="要生成的文本列表，用换行符分隔"),
    language: str = Form("zh", description="语言代码"),
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    批量语音克隆，一次生成多个语音
    """
    try:
        # 验证文件类型
        if not voice_file.content_type or not voice_file.content_type.startswith("audio/"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只支持音频文件"
            )

        # 解析文本列表
        text_list = [text.strip() for text in texts.split('\n') if text.strip()]

        if len(text_list) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="至少需要提供一个文本"
            )

        if len(text_list) > 20:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="单次最多处理20个文本"
            )

        # 验证每个文本的长度
        for i, text in enumerate(text_list):
            if len(text) > 500:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"第{i+1}个文本长度超过500字符限制"
                )

        # 保存语音文件
        voice_path = await save_upload_file(voice_file, VOICE_FOLDER)

        # 创建批量语音克隆任务
        task_id = await task_manager.create_task(
            TaskType.BATCH_PROCESS,
            {
                'voice_sample_path': str(voice_path),
                'texts': text_list,
                'language': language,
                'operation': 'voice_clone',
                'user_id': str(current_user.id)
            },
            user_id=str(current_user.id),
            priority=6
        )

        return JSONResponse(content={
            "success": True,
            "message": "批量语音克隆任务已创建",
            "task_id": task_id,
            "total_texts": len(text_list),
            "language": language,
            "estimated_time": f"{len(text_list) * 45}秒",
            "status_url": f"/api/v1/ai-enhanced/task/{task_id}/status"
        })

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"批量语音克隆失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量语音克隆失败: {str(e)}"
        ) from e


@router.post("/voice/detect-language", summary="检测文本语言")
async def detect_text_language(
    text: str = Form(..., description="要检测的文本"),
    current_user: User = Depends(get_current_active_user),
) -> JSONResponse:
    """
    检测文本的语言并推荐最适合的语音克隆语言
    """
    try:
        if len(text.strip()) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文本内容不能为空"
            )

        voice_cloner = get_voice_cloner()
        recommendations = voice_cloner.get_language_recommendations(text)

        return JSONResponse(content={
            "success": True,
            "text": text,
            "recommendations": recommendations,
            "supported_languages": voice_cloner.get_supported_languages()
        })

    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"语言检测失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"语言检测失败: {str(e)}"
        ) from e


@router.get("/voice/languages", summary="获取支持的语言")
async def get_supported_languages() -> JSONResponse:
    """
    获取语音克隆支持的语言列表
    """
    try:
        voice_cloner = get_voice_cloner()
        languages = voice_cloner.get_supported_languages()

        return JSONResponse(content={
            "success": True,
            "languages": languages,
            "total_languages": len(languages)
        })

    except Exception as e:
        logger.error(f"获取支持语言失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取支持语言失败: {str(e)}"
        ) from e


@router.get("/health", summary="AI服务健康检查")
async def health_check() -> JSONResponse:
    """
    检查AI服务状态和组件健康状况
    """
    try:
        health_status = {
            "status": "healthy",
            "timestamp": time.time(),
            "services": {}
        }

        # 检查Replicate服务
        try:
            replicate_service = get_replicate_service()
            health_status["services"]["replicate"] = {
                "status": "healthy",
                "api_key_configured": bool(replicate_service.api_key),
                "message": "Replicate服务正常"
            }
        except Exception as e:
            health_status["services"]["replicate"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["status"] = "degraded"

        # 检查任务管理器
        try:
            if task_manager.redis_client:
                await task_manager.redis_client.ping()
                health_status["services"]["task_manager"] = {
                    "status": "healthy",
                    "redis_connected": True,
                    "message": "任务管理器正常"
                }
            else:
                health_status["services"]["task_manager"] = {
                    "status": "degraded",
                    "redis_connected": False,
                    "message": "使用内存存储"
                }
        except Exception as e:
            health_status["services"]["task_manager"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["status"] = "degraded"

        # 检查语音服务
        try:
            voice_cloner = get_voice_cloner()
            supported_languages = voice_cloner.get_supported_languages()
            health_status["services"]["voice_service"] = {
                "status": "healthy",
                "supported_languages": len(supported_languages),
                "message": "语音服务正常"
            }
        except Exception as e:
            health_status["services"]["voice_service"] = {
                "status": "unhealthy",
                "error": str(e)
            }
            health_status["status"] = "degraded"

        # 检查存储目录
        storage_status = {}
        for name, folder in [
            ("uploads", UPLOAD_FOLDER),
            ("ai_results", AI_RESULTS_FOLDER),
            ("batch", BATCH_FOLDER),
            ("voice", VOICE_FOLDER)
        ]:
            storage_status[name] = {
                "exists": folder.exists(),
                "writable": os.access(folder, os.W_OK) if folder.exists() else False
            }

        health_status["storage"] = storage_status

        # 确定总体状态
        if health_status["status"] == "healthy":
            status_code = status.HTTP_200_OK
        else:
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE

        return JSONResponse(
            status_code=status_code,
            content=health_status
        )

    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }
        )


@router.get("/models", summary="获取可用AI模型")
async def get_available_models() -> JSONResponse:
    """
    获取可用的AI模型列表和功能详情
    """
    try:
        models = {
            "photo_restoration": {
                "name": "Real-ESRGAN",
                "description": "照片修复和增强",
                "capabilities": ["去噪", "修复破损", "提升清晰度", "细节恢复"],
                "supported_formats": ["jpg", "jpeg", "png", "webp"],
                "max_resolution": "4096x4096",
                "processing_time": "15-30秒",
                "batch_support": True
            },
            "photo_enhancement": {
                "name": "Real-ESRGAN",
                "description": "照片分辨率增强",
                "capabilities": ["2-8倍放大", "面部增强", "细节优化", "锐化处理"],
                "supported_formats": ["jpg", "jpeg", "png", "webp"],
                "scale_options": [2, 4, 6, 8],
                "processing_time": "20-40秒",
                "batch_support": True
            },
            "background_removal": {
                "name": "REMBG",
                "description": "背景移除",
                "models": {
                    "u2net": "通用背景移除模型，适用于各种图片",
                    "silueta": "人像专用背景移除模型，更精确的人体轮廓"
                },
                "supported_formats": ["jpg", "jpeg", "png"],
                "output_format": "png",
                "processing_time": "10-20秒",
                "batch_support": True
            },
            "photo_colorization": {
                "name": "Bringing Old Photos Back to Life",
                "description": "黑白照片上色",
                "capabilities": ["智能上色", "高分辨率输出", "保持原始细节", "历史照片修复"],
                "supported_formats": ["jpg", "jpeg", "png"],
                "processing_time": "20-35秒",
                "batch_support": True
            },
            "voice_cloning": {
                "name": "XTTS",
                "description": "多语言语音克隆",
                "capabilities": ["语音克隆", "多语言支持", "音质增强", "语速调整"],
                "supported_languages": list(MultiLanguageVoiceCloner.get_supported_languages().keys()),
                "supported_formats": ["wav", "mp3", "m4a", "flac"],
                "min_sample_duration": "6秒",
                "recommended_sample_duration": "10秒",
                "processing_time": "30-60秒",
                "batch_support": True
            },
            "photo_to_3d": {
                "name": "TripoSR",
                "description": "照片转3D模型",
                "capabilities": ["单张照片生成3D", "网格优化", "纹理映射"],
                "supported_formats": ["jpg", "jpeg", "png"],
                "output_formats": ["obj", "ply", "glb"],
                "processing_time": "60-120秒",
                "batch_support": False,
                "status": "开发中"
            }
        }

        # 获取语音服务支持的语言详情
        try:
            voice_cloner = get_voice_cloner()
            supported_languages = voice_cloner.get_supported_languages()
            models["voice_cloning"]["language_details"] = supported_languages
        except Exception as e:
            logger.warning(f"获取语音语言详情失败: {e}")

        # 统计信息
        stats = {
            "total_models": len(models),
            "batch_supported": len([m for m in models.values() if m.get("batch_support", False)]),
            "image_models": len([m for k, m in models.items() if k.startswith("photo_")]),
            "voice_models": len([m for k, m in models.items() if k.startswith("voice_")]),
            "3d_models": len([m for k, m in models.items() if "3d" in k])
        }

        return JSONResponse(content={
            "success": True,
            "models": models,
            "statistics": stats,
            "features": {
                "batch_processing": True,
                "progress_tracking": True,
                "quality_enhancement": True,
                "multi_language_support": True,
                "task_cancellation": True,
                "result_preview": True
            }
        })

    except Exception as e:
        logger.error(f"获取模型信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取模型信息失败: {str(e)}"
        ) from e


# ==================== 任务处理器注册 ====================

async def register_task_processors():
    """注册任务处理器"""
    try:
        await task_manager.initialize()

        # 注册批量照片处理器
        async def batch_photo_processor(task_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
            batch_processor = get_batch_processor()
            result = await batch_processor.process_batch_photos(
                task_id,
                input_data['file_paths'],
                input_data['operation'],
                input_data.get('options', {})
            )
            return {
                "batch_result": result.__dict__,
                "success_rate": result.success_rate,
                "total_processing_time": result.total_processing_time
            }

        # 注册语音克隆处理器
        async def voice_clone_processor(task_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
            voice_cloner = get_voice_cloner()
            result = voice_cloner.clone_voice_enhanced(
                input_data['voice_sample_path'],
                input_data['text'],
                input_data.get('language', 'zh'),
                input_data.get('enhance_quality', True),
                input_data.get('speed', 1.0),
                input_data.get('pitch_shift', 0.0)
            )
            return result

        # 注册批量语音克隆处理器
        async def batch_voice_processor(task_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
            batch_processor = get_batch_processor()
            result = await batch_processor.process_batch_voice_clone(
                task_id,
                input_data['voice_sample_path'],
                input_data['texts'],
                input_data.get('language', 'zh')
            )
            return {
                "batch_result": result.__dict__,
                "success_rate": result.success_rate,
                "total_processing_time": result.total_processing_time
            }

        # 注册处理器
        task_manager.register_processor(TaskType.BATCH_PROCESS, batch_photo_processor)
        task_manager.register_processor(TaskType.VOICE_CLONE, voice_clone_processor)

        logger.info("AI任务处理器注册完成")

    except Exception as e:
        logger.error(f"任务处理器注册失败: {e}")


# 在应用启动时注册处理器
import asyncio
asyncio.create_task(register_task_processors())
