"""
Replicate.com AI 服务模块
提供照片修复、上色、3D效果和声音克隆等功能

基于 Replicate API 文档: https://replicate.com/docs
"""

import logging
import os
import time
import uuid
from pathlib import Path
from typing import Any, BinaryIO

import replicate
import requests
from replicate.exceptions import ReplicateError

# 配置日志
logger = logging.getLogger(__name__)


class ReplicateService:
    """Replicate.com AI 服务类

    基于 Replicate API 的最佳实践实现：
    - 使用异步请求处理
    - 实现预测状态监控
    - 添加错误重试机制
    - 支持批量处理
    """

    def __init__(self, api_key: str | None = None, upload_folder: str = "uploads"):
        """
        初始化 Replicate 服务

        Args:
            api_key: Replicate API 密钥，如果为 None，则使用环境变量
            upload_folder: 上传文件夹路径
        """
        # 设置 API Token
        if api_key:
            os.environ["REPLICATE_API_TOKEN"] = api_key

        # 验证 API Token
        if not os.environ.get("REPLICATE_API_TOKEN"):
            raise ValueError("REPLICATE_API_TOKEN 环境变量未设置")

        # 确保上传目录存在
        self.upload_dir = Path(upload_folder)
        self.results_dir = self.upload_dir / "ai_results"
        self.temp_dir = self.upload_dir / "temp"

        self.upload_dir.mkdir(exist_ok=True)
        self.results_dir.mkdir(exist_ok=True)
        self.temp_dir.mkdir(exist_ok=True)

        # 配置重试和超时设置
        self.max_retries = 3
        self.retry_delay = 2
        self.max_wait_time = 300  # 5分钟最大等待时间

    def _save_file(self, file_data: BinaryIO | bytes, original_filename: str) -> str:
        """
        保存上传的文件

        Args:
            file_data: 文件数据
            original_filename: 原始文件名

        Returns:
            保存的文件路径
        """
        # 生成唯一文件名
        filename = f"{uuid.uuid4()}_{original_filename}"
        file_path = self.upload_dir / filename

        # 保存文件
        if isinstance(file_data, bytes):
            with open(file_path, "wb") as f:
                f.write(file_data)
        else:
            if hasattr(file_data, "read"):
                file_data.seek(0)
            with open(file_path, "wb") as f:
                f.write(file_data.read())

        return str(file_path)

    def _wait_for_prediction(self, prediction_id: str) -> dict[str, Any]:
        """
        等待预测完成并返回结果

        Args:
            prediction_id: 预测任务ID

        Returns:
            预测结果字典

        Raises:
            TimeoutError: 超时错误
            ReplicateError: Replicate API错误
        """
        start_time = time.time()

        while time.time() - start_time < self.max_wait_time:
            try:
                prediction = replicate.predictions.get(prediction_id)

                if prediction.status == "succeeded":
                    logger.info(f"预测成功完成: {prediction_id}")
                    return {
                        "status": "succeeded",
                        "output": prediction.output,
                        "id": prediction_id,
                        "metrics": getattr(prediction, "metrics", {}),
                    }
                elif prediction.status == "failed":
                    logger.error(f"预测失败: {prediction_id}, 错误: {prediction.error}")
                    raise ReplicateError(f"预测失败: {prediction.error}")
                elif prediction.status in ["starting", "processing"]:
                    logger.info(f"预测进行中: {prediction_id}, 状态: {prediction.status}")
                    time.sleep(2)  # 等待2秒后重试
                else:
                    logger.warning(f"未知预测状态: {prediction.status}")
                    time.sleep(2)

            except Exception as e:
                logger.error(f"检查预测状态失败: {e}")
                time.sleep(self.retry_delay)

        raise TimeoutError(f"预测超时: {prediction_id}")

    def _retry_operation(self, operation_func, *args, **kwargs) -> Any:
        """
        重试机制包装器

        Args:
            operation_func: 要重试的操作函数
            *args: 函数参数
            **kwargs: 函数关键字参数

        Returns:
            操作结果
        """
        last_exception = None

        for attempt in range(self.max_retries):
            try:
                return operation_func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < self.max_retries - 1:
                    logger.warning(f"操作失败，正在重试 ({attempt + 1}/{self.max_retries}): {e}")
                    time.sleep(self.retry_delay * (attempt + 1))  # 指数退避
                else:
                    logger.error(f"操作失败，已达最大重试次数: {e}")

        raise last_exception or Exception("操作失败")

    def _download_result(
        self, url: str, result_type: str, original_filename: str
    ) -> str:
        """
        下载处理结果

        Args:
            url: 结果URL
            result_type: 结果类型
            original_filename: 原始文件名

        Returns:
            保存的结果文件路径
        """
        try:
            response = requests.get(url, timeout=30)
            if response.status_code == 200:
                # 生成结果文件名
                result_filename = f"{result_type}_{uuid.uuid4()}_{original_filename}"
                result_path = self.results_dir / result_filename

                # 保存结果
                with open(result_path, "wb") as f:
                    f.write(response.content)

                return str(result_path)
            else:
                logger.error(f"下载结果失败: {response.status_code} - {response.text}")
                raise Exception(f"下载结果失败: {response.status_code}")
        except Exception as e:
            logger.error(f"下载结果异常: {str(e)}")
            raise

    def restore_photo(
        self, photo_data: BinaryIO | bytes, filename: str, **options
    ) -> dict[str, Any]:
        """
        使用AI修复老照片，去噪、增强清晰度、修复损坏等

        Args:
            photo_data: 照片数据
            filename: 文件名
            **options: 修复选项参数
                - upscale: 放大倍数 (1-4, 默认2)
                - face_enhance: 是否增强面部 (默认True)
                - background_enhance: 是否增强背景 (默认True)
                - codeformer_fidelity: CodeFormer保真度 (0-1, 默认0.7)

        Returns:
            包含原始照片和修复后照片信息的字典
        """

        def _restore_operation():
            # 保存上传的照片
            file_path = self._save_file(photo_data, filename)

            # 设置默认参数
            upscale = options.get("upscale", 2)
            face_enhance = options.get("face_enhance", True)
            background_enhance = options.get("background_enhance", True)
            codeformer_fidelity = options.get("codeformer_fidelity", 0.7)

            logger.info(
                f"开始修复照片: {filename} (upscale={upscale}, face_enhance={face_enhance})"
            )

            # 使用最新的CodeFormer模型进行照片修复
            with open(file_path, "rb") as image_file:
                prediction = replicate.predictions.create(
                    version="sczhou/codeformer:7de2ea26c616d5bf2245ad0d5e24f0ff9a6204578a5c876db53142edd9d2cd56",
                    input={
                        "image": image_file,
                        "upscale": upscale,
                        "face_upsample": face_enhance,
                        "background_enhance": background_enhance,
                        "codeformer_fidelity": codeformer_fidelity,
                    },
                )

            # 等待预测完成
            result = self._wait_for_prediction(prediction.id)

            if result["output"]:
                # 下载处理后的图像
                result_path = self._download_result(
                    str(result["output"]), "restored", filename
                )

                return {
                    "original": file_path,
                    "result": result_path,
                    "prediction_id": prediction.id,
                    "status": "success",
                    "metrics": result.get("metrics", {}),
                    "options_used": {
                        "upscale": upscale,
                        "face_enhance": face_enhance,
                        "background_enhance": background_enhance,
                        "codeformer_fidelity": codeformer_fidelity,
                    },
                }
            else:
                raise Exception("修复结果为空")

        try:
            return self._retry_operation(_restore_operation)
        except Exception as e:
            logger.error(f"照片修复失败: {str(e)}")
            raise

    def colorize_photo(
        self, photo_data: BinaryIO | bytes, filename: str, **options
    ) -> dict[str, Any]:
        """
        使用AI为黑白照片上色

        Args:
            photo_data: 照片数据
            filename: 文件名
            **options: 上色选项参数
                - render_factor: 渲染质量因子 (7-40, 默认25)
                - watermarked: 是否添加水印 (默认False)

        Returns:
            包含原始照片和上色后照片信息的字典
        """

        def _colorize_operation():
            # 保存上传的照片
            file_path = self._save_file(photo_data, filename)

            # 设置默认参数
            render_factor = options.get("render_factor", 25)
            watermarked = options.get("watermarked", False)

            logger.info(f"开始上色照片: {filename} (render_factor={render_factor})")

            # 使用DeOldify模型进行照片上色
            with open(file_path, "rb") as image_file:
                prediction = replicate.predictions.create(
                    version="tencentarc/gfpgan:0fbacf7afc6c144e5be9767cff80f25aff23e52b0708f17e20f9879b2f21516c",
                    input={
                        "img": image_file,
                        "version": "v1.4",  # 使用最新版本
                        "scale": 2,  # 放大倍数
                    },
                )

            # 等待预测完成
            result = self._wait_for_prediction(prediction.id)

            if result["output"]:
                # 下载处理后的图像
                result_path = self._download_result(
                    str(result["output"]), "colorized", filename
                )

                return {
                    "original": file_path,
                    "result": result_path,
                    "prediction_id": prediction.id,
                    "status": "success",
                    "metrics": result.get("metrics", {}),
                    "options_used": {
                        "render_factor": render_factor,
                        "watermarked": watermarked,
                    },
                }
            else:
                raise Exception("上色结果为空")

        try:
            return self._retry_operation(_colorize_operation)
        except Exception as e:
            logger.error(f"照片上色失败: {str(e)}")
            raise

    def enhance_photo_resolution(
        self, photo_data: BinaryIO | bytes, filename: str, **options
    ) -> dict[str, Any]:
        """
        使用AI增强照片分辨率和质量

        Args:
            photo_data: 照片数据
            filename: 文件名
            **options: 增强选项参数
                - scale: 放大倍数 (2-8, 默认4)
                - face_enhance: 是否增强面部 (默认True)

        Returns:
            包含原始照片和增强后照片信息的字典
        """

        def _enhance_operation():
            # 保存上传的照片
            file_path = self._save_file(photo_data, filename)

            # 设置默认参数
            scale = options.get("scale", 4)
            face_enhance = options.get("face_enhance", True)

            logger.info(f"开始增强照片分辨率: {filename} (scale={scale})")

            # 使用Real-ESRGAN模型进行超分辨率增强
            with open(file_path, "rb") as image_file:
                prediction = replicate.predictions.create(
                    version="nightmareai/real-esrgan:42fed1c4974146d4d2414e2be2c5277c7fcf05fcc3a73abf41610695738c1d7b",
                    input={
                        "image": image_file,
                        "scale": scale,
                        "face_enhance": face_enhance,
                    },
                )

            # 等待预测完成
            result = self._wait_for_prediction(prediction.id)

            if result["output"]:
                # 下载处理后的图像
                result_path = self._download_result(
                    str(result["output"]), "enhanced", filename
                )

                return {
                    "original": file_path,
                    "result": result_path,
                    "prediction_id": prediction.id,
                    "status": "success",
                    "metrics": result.get("metrics", {}),
                    "options_used": {"scale": scale, "face_enhance": face_enhance},
                }
            else:
                raise Exception("增强结果为空")

        try:
            return self._retry_operation(_enhance_operation)
        except Exception as e:
            logger.error(f"照片增强失败: {str(e)}")
            raise

    def remove_background(
        self, photo_data: BinaryIO | bytes, filename: str, **options
    ) -> dict[str, Any]:
        """
        使用AI移除照片背景

        Args:
            photo_data: 照片数据
            filename: 文件名
            **options: 选项参数
                - model: 模型类型 ("u2net" 或 "silueta", 默认"u2net")

        Returns:
            包含原始照片和去背景照片信息的字典
        """

        def _remove_bg_operation():
            # 保存上传的照片
            file_path = self._save_file(photo_data, filename)

            # 设置默认参数
            model = options.get("model", "u2net")

            logger.info(f"开始移除照片背景: {filename} (model={model})")

            # 使用背景移除模型
            with open(file_path, "rb") as image_file:
                prediction = replicate.predictions.create(
                    version="cjwbw/rembg:fb8af171cfa1616ddcf1242c093f9c46bcada5ad4cf6f2fbe8b81b330ec5c003",
                    input={
                        "image": image_file,
                        "model": model,
                    },
                )

            # 等待预测完成
            result = self._wait_for_prediction(prediction.id)

            if result["output"]:
                # 下载处理后的图像
                result_path = self._download_result(
                    str(result["output"]), "no_bg", filename
                )

                return {
                    "original": file_path,
                    "result": result_path,
                    "prediction_id": prediction.id,
                    "status": "success",
                    "metrics": result.get("metrics", {}),
                    "options_used": {"model": model},
                }
            else:
                raise Exception("背景移除结果为空")

        try:
            return self._retry_operation(_remove_bg_operation)
        except Exception as e:
            logger.error(f"背景移除失败: {str(e)}")
            raise

    def create_3d_effect(
        self, photo_data: BinaryIO | bytes, filename: str
    ) -> dict[str, Any]:
        """
        创建照片 3D 效果

        Args:
            photo_data: 照片数据
            filename: 文件名

        Returns:
            包含原始照片和 3D 效果视频 URL 的字典
        """
        try:
            # 保存上传的照片
            file_path = self._save_file(photo_data, filename)

            # 调用 Replicate API 创建 3D 效果
            logger.info(f"开始创建 3D 效果: {filename}")
            output = replicate.run(
                "varunagrawal/3d-photo-inpainting:f0c49a6b824235c45f19fb7c9d0cd7d4c1ddbca3b9483a5771b38500ded8163c",
                input={"image": open(file_path, "rb")},
            )

            return {"original": file_path, "result": output}
        except Exception as e:
            logger.error(f"创建 3D 效果失败: {str(e)}")
            raise

    def create_3d_model(
        self, photo_data: BinaryIO | bytes, filename: str, description: str = ""
    ) -> dict[str, Any]:
        """
        从照片创建 3D 模型

        Args:
            photo_data: 照片数据
            filename: 文件名
            description: 模型描述（可选）

        Returns:
            包含原始照片和 3D 模型信息的字典
        """
        try:
            # 保存上传的照片
            file_path = self._save_file(photo_data, filename)

            # 调用 Replicate API 创建 3D 模型
            logger.info(f"开始创建 3D 模型: {filename}")
            output = replicate.run(
                "cjwbw/instantmesh:d0de4bd38b0d0f1217a6a2ef9d0cb8d22d117f6a8c8f2e1bb75e39f0f5d8e91d",
                input={
                    "image": open(file_path, "rb"),
                    "task_type": "human",
                    "mesh_type": "3d",
                },
            )

            return {"original": file_path, "result": output}
        except Exception as e:
            logger.error(f"创建 3D 模型失败: {str(e)}")
            raise

    def clone_voice(
        self,
        voice_data: BinaryIO | bytes,
        filename: str,
        text: str,
        language: str = "zh",
    ) -> dict[str, Any]:
        """
        克隆声音

        Args:
            voice_data: 声音样本数据
            filename: 文件名
            text: 要生成的文本
            language: 语言代码

        Returns:
            包含生成的语音 URL 的字典
        """
        try:
            # 保存上传的声音样本
            file_path = self._save_file(voice_data, filename)

            # 调用 Replicate API 克隆声音
            logger.info(f"开始克隆声音: {filename}, 文本: {text}")
            output = replicate.run(
                "lucataco/xtts:6be4a3f4e47e4ab877354ef6cb5bf5f6d0e175d9a65fb18d7bce73c095e98dbf",
                input={
                    "text": text,
                    "voice_sample": open(file_path, "rb"),
                    "language": language,
                },
            )

            return {"original": file_path, "result": output}
        except Exception as e:
            logger.error(f"声音克隆失败: {str(e)}")
            raise
