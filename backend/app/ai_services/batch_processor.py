"""
批量AI处理服务
支持批量图像处理、进度跟踪、错误处理等功能
"""

import asyncio
import logging
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, BinaryIO
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import aiofiles

from .replicate_service import ReplicateService
from .ai_task_manager import AITaskManager, TaskType, task_manager

logger = logging.getLogger(__name__)


@dataclass
class BatchItem:
    """批量处理项"""
    file_path: str
    filename: str
    options: Dict[str, Any] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    processing_time: Optional[float] = None


@dataclass
class BatchResult:
    """批量处理结果"""
    batch_id: str
    total_items: int
    completed_items: int
    failed_items: int
    success_rate: float
    total_processing_time: float
    results: List[BatchItem]
    errors: List[str]


class BatchProcessor:
    """批量AI处理器"""
    
    def __init__(self, replicate_service: ReplicateService):
        self.replicate_service = replicate_service
        self.max_concurrent = 3  # 最大并发处理数
        self.executor = ThreadPoolExecutor(max_workers=self.max_concurrent)
    
    async def process_batch_photos(
        self,
        task_id: str,
        file_paths: List[str],
        operation: str,
        options: Dict[str, Any] = None
    ) -> BatchResult:
        """
        批量处理照片
        
        Args:
            task_id: 任务ID
            file_paths: 文件路径列表
            operation: 操作类型 (restore, colorize, enhance, remove_bg)
            options: 处理选项
        """
        start_time = time.time()
        batch_items = []
        errors = []
        
        # 创建批量处理项
        for file_path in file_paths:
            batch_items.append(BatchItem(
                file_path=file_path,
                filename=Path(file_path).name,
                options=options or {}
            ))
        
        total_items = len(batch_items)
        completed_items = 0
        failed_items = 0
        
        # 更新初始进度
        await task_manager.update_task_progress(
            task_id, 0, total_items, f"开始批量{operation}处理"
        )
        
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_single_item(item: BatchItem) -> BatchItem:
            """处理单个项目"""
            nonlocal completed_items, failed_items
            
            async with semaphore:
                try:
                    item_start_time = time.time()
                    
                    # 根据操作类型调用相应的处理方法
                    with open(item.file_path, 'rb') as file_data:
                        if operation == 'restore':
                            result = self.replicate_service.restore_photo(
                                file_data, item.filename, **item.options
                            )
                        elif operation == 'colorize':
                            result = self.replicate_service.colorize_photo(
                                file_data, item.filename, **item.options
                            )
                        elif operation == 'enhance':
                            result = self.replicate_service.enhance_photo_resolution(
                                file_data, item.filename, **item.options
                            )
                        elif operation == 'remove_bg':
                            result = self.replicate_service.remove_background(
                                file_data, item.filename, **item.options
                            )
                        else:
                            raise ValueError(f"不支持的操作类型: {operation}")
                    
                    item.result = result
                    item.processing_time = time.time() - item_start_time
                    completed_items += 1
                    
                    logger.info(f"批量处理完成: {item.filename} ({item.processing_time:.2f}s)")
                    
                except Exception as e:
                    item.error = str(e)
                    item.processing_time = time.time() - item_start_time
                    failed_items += 1
                    errors.append(f"{item.filename}: {str(e)}")
                    
                    logger.error(f"批量处理失败: {item.filename} - {str(e)}")
                
                # 更新进度
                progress = completed_items + failed_items
                await task_manager.update_task_progress(
                    task_id, 
                    progress, 
                    total_items,
                    f"已处理 {progress}/{total_items} 个文件",
                    estimated_time_remaining=self._estimate_remaining_time(
                        progress, total_items, time.time() - start_time
                    )
                )
                
                return item
        
        # 并发处理所有项目
        tasks = [process_single_item(item) for item in batch_items]
        processed_items = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        for i, result in enumerate(processed_items):
            if isinstance(result, Exception):
                batch_items[i].error = str(result)
                failed_items += 1
                errors.append(f"{batch_items[i].filename}: {str(result)}")
        
        total_processing_time = time.time() - start_time
        success_rate = (completed_items / total_items) * 100 if total_items > 0 else 0
        
        batch_result = BatchResult(
            batch_id=task_id,
            total_items=total_items,
            completed_items=completed_items,
            failed_items=failed_items,
            success_rate=success_rate,
            total_processing_time=total_processing_time,
            results=batch_items,
            errors=errors
        )
        
        logger.info(
            f"批量处理完成: {completed_items}/{total_items} 成功, "
            f"成功率: {success_rate:.1f}%, 总耗时: {total_processing_time:.2f}s"
        )
        
        return batch_result
    
    async def process_batch_voice_clone(
        self,
        task_id: str,
        voice_sample_path: str,
        texts: List[str],
        language: str = "zh"
    ) -> BatchResult:
        """
        批量语音克隆
        
        Args:
            task_id: 任务ID
            voice_sample_path: 语音样本文件路径
            texts: 要生成的文本列表
            language: 语言代码
        """
        start_time = time.time()
        batch_items = []
        errors = []
        
        # 创建批量处理项
        for i, text in enumerate(texts):
            batch_items.append(BatchItem(
                file_path=voice_sample_path,
                filename=f"voice_clone_{i+1}.wav",
                options={"text": text, "language": language}
            ))
        
        total_items = len(batch_items)
        completed_items = 0
        failed_items = 0
        
        # 更新初始进度
        await task_manager.update_task_progress(
            task_id, 0, total_items, "开始批量语音克隆"
        )
        
        # 语音克隆通常需要串行处理以避免资源冲突
        for i, item in enumerate(batch_items):
            try:
                item_start_time = time.time()
                
                with open(item.file_path, 'rb') as voice_data:
                    result = self.replicate_service.clone_voice(
                        voice_data, 
                        f"sample_{i}.wav",
                        item.options["text"],
                        item.options["language"]
                    )
                
                item.result = result
                item.processing_time = time.time() - item_start_time
                completed_items += 1
                
                logger.info(f"语音克隆完成: {item.filename} ({item.processing_time:.2f}s)")
                
            except Exception as e:
                item.error = str(e)
                item.processing_time = time.time() - item_start_time
                failed_items += 1
                errors.append(f"{item.filename}: {str(e)}")
                
                logger.error(f"语音克隆失败: {item.filename} - {str(e)}")
            
            # 更新进度
            progress = i + 1
            await task_manager.update_task_progress(
                task_id,
                progress,
                total_items,
                f"已处理 {progress}/{total_items} 个语音",
                estimated_time_remaining=self._estimate_remaining_time(
                    progress, total_items, time.time() - start_time
                )
            )
        
        total_processing_time = time.time() - start_time
        success_rate = (completed_items / total_items) * 100 if total_items > 0 else 0
        
        batch_result = BatchResult(
            batch_id=task_id,
            total_items=total_items,
            completed_items=completed_items,
            failed_items=failed_items,
            success_rate=success_rate,
            total_processing_time=total_processing_time,
            results=batch_items,
            errors=errors
        )
        
        logger.info(
            f"批量语音克隆完成: {completed_items}/{total_items} 成功, "
            f"成功率: {success_rate:.1f}%, 总耗时: {total_processing_time:.2f}s"
        )
        
        return batch_result
    
    def _estimate_remaining_time(
        self, 
        completed: int, 
        total: int, 
        elapsed_time: float
    ) -> Optional[int]:
        """估算剩余时间"""
        if completed == 0:
            return None
        
        avg_time_per_item = elapsed_time / completed
        remaining_items = total - completed
        estimated_remaining = avg_time_per_item * remaining_items
        
        return int(estimated_remaining)
    
    async def optimize_batch_processing(
        self,
        file_paths: List[str],
        operation: str
    ) -> Dict[str, Any]:
        """
        优化批量处理策略
        
        Args:
            file_paths: 文件路径列表
            operation: 操作类型
            
        Returns:
            优化建议
        """
        total_files = len(file_paths)
        
        # 分析文件大小
        total_size = 0
        file_sizes = []
        
        for file_path in file_paths:
            try:
                size = Path(file_path).stat().st_size
                file_sizes.append(size)
                total_size += size
            except Exception:
                file_sizes.append(0)
        
        avg_file_size = total_size / total_files if total_files > 0 else 0
        
        # 根据文件数量和大小推荐处理策略
        if total_files <= 5:
            recommended_concurrent = min(total_files, 3)
            batch_size = total_files
        elif total_files <= 20:
            recommended_concurrent = 3
            batch_size = 10
        else:
            recommended_concurrent = 2
            batch_size = 15
        
        # 估算处理时间
        base_time_per_file = {
            'restore': 30,      # 30秒
            'colorize': 25,     # 25秒
            'enhance': 35,      # 35秒
            'remove_bg': 15,    # 15秒
            'voice_clone': 45   # 45秒
        }
        
        estimated_time_per_file = base_time_per_file.get(operation, 30)
        
        # 大文件需要更多时间
        if avg_file_size > 5 * 1024 * 1024:  # 5MB
            estimated_time_per_file *= 1.5
        
        estimated_total_time = (total_files * estimated_time_per_file) / recommended_concurrent
        
        return {
            "total_files": total_files,
            "total_size_mb": total_size / (1024 * 1024),
            "avg_file_size_mb": avg_file_size / (1024 * 1024),
            "recommended_concurrent": recommended_concurrent,
            "recommended_batch_size": batch_size,
            "estimated_total_time_minutes": estimated_total_time / 60,
            "estimated_time_per_file_seconds": estimated_time_per_file,
            "optimization_tips": self._get_optimization_tips(total_files, avg_file_size, operation)
        }
    
    def _get_optimization_tips(
        self, 
        total_files: int, 
        avg_file_size: float, 
        operation: str
    ) -> List[str]:
        """获取优化建议"""
        tips = []
        
        if total_files > 50:
            tips.append("建议分批处理，每批不超过20个文件")
        
        if avg_file_size > 10 * 1024 * 1024:  # 10MB
            tips.append("文件较大，建议先压缩图片以提高处理速度")
        
        if operation in ['restore', 'enhance']:
            tips.append("修复和增强操作耗时较长，建议在非高峰时段处理")
        
        if operation == 'voice_clone':
            tips.append("语音克隆需要串行处理，请耐心等待")
        
        tips.append("处理过程中请保持网络连接稳定")
        tips.append("大批量处理建议在服务器负载较低时进行")
        
        return tips
