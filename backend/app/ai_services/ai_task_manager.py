"""
AI任务管理器
提供异步任务处理、进度跟踪、批量处理等功能
"""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime, timed<PERSON>ta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, asdict
import aioredis
from fastapi import BackgroundTasks

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(Enum):
    """任务类型枚举"""
    PHOTO_RESTORE = "photo_restore"
    PHOTO_COLORIZE = "photo_colorize"
    PHOTO_ENHANCE = "photo_enhance"
    PHOTO_REMOVE_BG = "photo_remove_bg"
    PHOTO_TO_3D = "photo_to_3d"
    CREATE_3D_MODEL = "create_3d_model"
    VOICE_CLONE = "voice_clone"
    BATCH_PROCESS = "batch_process"


@dataclass
class TaskProgress:
    """任务进度信息"""
    current_step: int = 0
    total_steps: int = 1
    step_description: str = ""
    percentage: float = 0.0
    estimated_time_remaining: Optional[int] = None  # 秒


@dataclass
class AITask:
    """AI任务数据结构"""
    task_id: str
    task_type: TaskType
    status: TaskStatus
    user_id: Optional[str] = None
    input_data: Dict[str, Any] = None
    output_data: Dict[str, Any] = None
    progress: TaskProgress = None
    error_message: Optional[str] = None
    created_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    priority: int = 1  # 1-10, 10为最高优先级
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.progress is None:
            self.progress = TaskProgress()
        if self.input_data is None:
            self.input_data = {}
        if self.output_data is None:
            self.output_data = {}


class AITaskManager:
    """AI任务管理器"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.redis_client: Optional[aioredis.Redis] = None
        self.task_processors: Dict[TaskType, Callable] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.max_concurrent_tasks = 5
        self.task_timeout = 600  # 10分钟超时
        
    async def initialize(self):
        """初始化Redis连接"""
        try:
            self.redis_client = await aioredis.from_url(self.redis_url)
            await self.redis_client.ping()
            logger.info("AI任务管理器初始化成功")
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            # 如果Redis不可用，使用内存存储作为后备
            self.redis_client = None
            logger.warning("使用内存存储作为任务状态后备")
    
    def register_processor(self, task_type: TaskType, processor: Callable):
        """注册任务处理器"""
        self.task_processors[task_type] = processor
        logger.info(f"注册任务处理器: {task_type.value}")
    
    async def create_task(
        self, 
        task_type: TaskType, 
        input_data: Dict[str, Any],
        user_id: Optional[str] = None,
        priority: int = 1
    ) -> str:
        """创建新任务"""
        task_id = str(uuid.uuid4())
        task = AITask(
            task_id=task_id,
            task_type=task_type,
            status=TaskStatus.PENDING,
            user_id=user_id,
            input_data=input_data,
            priority=priority
        )
        
        await self._save_task(task)
        logger.info(f"创建任务: {task_id} ({task_type.value})")
        
        # 启动任务处理
        asyncio.create_task(self._process_task(task_id))
        
        return task_id
    
    async def get_task(self, task_id: str) -> Optional[AITask]:
        """获取任务信息"""
        if self.redis_client:
            try:
                task_data = await self.redis_client.get(f"task:{task_id}")
                if task_data:
                    data = json.loads(task_data)
                    # 重建TaskProgress对象
                    if 'progress' in data and data['progress']:
                        data['progress'] = TaskProgress(**data['progress'])
                    # 转换枚举
                    data['task_type'] = TaskType(data['task_type'])
                    data['status'] = TaskStatus(data['status'])
                    # 转换日期时间
                    for field in ['created_at', 'started_at', 'completed_at']:
                        if data.get(field):
                            data[field] = datetime.fromisoformat(data[field])
                    return AITask(**data)
            except Exception as e:
                logger.error(f"获取任务失败: {e}")
        return None
    
    async def update_task_progress(
        self, 
        task_id: str, 
        current_step: int, 
        total_steps: int,
        step_description: str = "",
        estimated_time_remaining: Optional[int] = None
    ):
        """更新任务进度"""
        task = await self.get_task(task_id)
        if task:
            task.progress.current_step = current_step
            task.progress.total_steps = total_steps
            task.progress.step_description = step_description
            task.progress.percentage = (current_step / total_steps) * 100
            task.progress.estimated_time_remaining = estimated_time_remaining
            await self._save_task(task)
    
    async def complete_task(self, task_id: str, output_data: Dict[str, Any]):
        """完成任务"""
        task = await self.get_task(task_id)
        if task:
            task.status = TaskStatus.COMPLETED
            task.output_data = output_data
            task.completed_at = datetime.utcnow()
            task.progress.percentage = 100.0
            await self._save_task(task)
            logger.info(f"任务完成: {task_id}")
    
    async def fail_task(self, task_id: str, error_message: str):
        """标记任务失败"""
        task = await self.get_task(task_id)
        if task:
            task.status = TaskStatus.FAILED
            task.error_message = error_message
            task.completed_at = datetime.utcnow()
            await self._save_task(task)
            logger.error(f"任务失败: {task_id} - {error_message}")
    
    async def cancel_task(self, task_id: str):
        """取消任务"""
        task = await self.get_task(task_id)
        if task and task.status in [TaskStatus.PENDING, TaskStatus.PROCESSING]:
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.utcnow()
            await self._save_task(task)
            
            # 取消正在运行的任务
            if task_id in self.running_tasks:
                self.running_tasks[task_id].cancel()
                del self.running_tasks[task_id]
            
            logger.info(f"任务已取消: {task_id}")
    
    async def get_user_tasks(
        self, 
        user_id: str, 
        status: Optional[TaskStatus] = None,
        limit: int = 50
    ) -> List[AITask]:
        """获取用户的任务列表"""
        # 这里简化实现，实际应该使用Redis的索引功能
        tasks = []
        if self.redis_client:
            try:
                # 获取用户任务键列表
                pattern = f"user_tasks:{user_id}:*"
                keys = await self.redis_client.keys(pattern)
                
                for key in keys[:limit]:
                    task_id = key.decode().split(':')[-1]
                    task = await self.get_task(task_id)
                    if task and (status is None or task.status == status):
                        tasks.append(task)
                
                # 按创建时间排序
                tasks.sort(key=lambda x: x.created_at, reverse=True)
            except Exception as e:
                logger.error(f"获取用户任务失败: {e}")
        
        return tasks
    
    async def _save_task(self, task: AITask):
        """保存任务到存储"""
        if self.redis_client:
            try:
                # 转换为可序列化的字典
                task_dict = asdict(task)
                # 转换枚举为字符串
                task_dict['task_type'] = task.task_type.value
                task_dict['status'] = task.status.value
                # 转换日期时间为ISO字符串
                for field in ['created_at', 'started_at', 'completed_at']:
                    if task_dict.get(field):
                        task_dict[field] = task_dict[field].isoformat()
                
                # 保存任务
                await self.redis_client.set(
                    f"task:{task.task_id}", 
                    json.dumps(task_dict),
                    ex=86400 * 7  # 7天过期
                )
                
                # 为用户任务建立索引
                if task.user_id:
                    await self.redis_client.set(
                        f"user_tasks:{task.user_id}:{task.task_id}",
                        task.task_id,
                        ex=86400 * 7
                    )
                    
            except Exception as e:
                logger.error(f"保存任务失败: {e}")
    
    async def _process_task(self, task_id: str):
        """处理任务"""
        task = await self.get_task(task_id)
        if not task:
            return
        
        # 检查并发限制
        if len(self.running_tasks) >= self.max_concurrent_tasks:
            logger.warning(f"达到最大并发任务数，任务排队: {task_id}")
            await asyncio.sleep(5)  # 等待5秒后重试
            asyncio.create_task(self._process_task(task_id))
            return
        
        try:
            # 标记任务开始
            task.status = TaskStatus.PROCESSING
            task.started_at = datetime.utcnow()
            await self._save_task(task)
            
            # 获取处理器
            processor = self.task_processors.get(task.task_type)
            if not processor:
                raise Exception(f"未找到任务处理器: {task.task_type.value}")
            
            # 创建任务协程
            task_coroutine = asyncio.wait_for(
                processor(task_id, task.input_data),
                timeout=self.task_timeout
            )
            
            # 记录运行中的任务
            self.running_tasks[task_id] = asyncio.current_task()
            
            # 执行任务
            result = await task_coroutine
            
            # 完成任务
            await self.complete_task(task_id, result)
            
        except asyncio.TimeoutError:
            await self.fail_task(task_id, "任务执行超时")
        except asyncio.CancelledError:
            logger.info(f"任务被取消: {task_id}")
        except Exception as e:
            # 检查是否需要重试
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                task.status = TaskStatus.PENDING
                await self._save_task(task)
                logger.warning(f"任务重试 ({task.retry_count}/{task.max_retries}): {task_id}")
                # 延迟重试
                await asyncio.sleep(2 ** task.retry_count)
                asyncio.create_task(self._process_task(task_id))
            else:
                await self.fail_task(task_id, str(e))
        finally:
            # 清理运行中的任务记录
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
    
    async def cleanup_expired_tasks(self):
        """清理过期任务"""
        if not self.redis_client:
            return
        
        try:
            # 获取所有任务键
            keys = await self.redis_client.keys("task:*")
            expired_count = 0
            
            for key in keys:
                task_data = await self.redis_client.get(key)
                if task_data:
                    data = json.loads(task_data)
                    created_at = datetime.fromisoformat(data['created_at'])
                    
                    # 删除7天前的已完成任务
                    if (datetime.utcnow() - created_at) > timedelta(days=7):
                        if data['status'] in ['completed', 'failed', 'cancelled']:
                            await self.redis_client.delete(key)
                            expired_count += 1
            
            if expired_count > 0:
                logger.info(f"清理了 {expired_count} 个过期任务")
                
        except Exception as e:
            logger.error(f"清理过期任务失败: {e}")


# 全局任务管理器实例
task_manager = AITaskManager()
