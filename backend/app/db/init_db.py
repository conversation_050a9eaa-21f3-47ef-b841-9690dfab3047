import logging

from sqlalchemy.orm import Session

from app import (
    crud,
)  # Adjusted import based on file structure
from app import (
    schemas_pydantic as schemas,
)
from app.core.config import settings
from app.db.session import engine  # To create tables
from app.models_sqlalchemy import (
    Base,
)  # To create tables, assuming Base is in models_sqlalchemy.py

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# make sure all SQL Alchemy models are imported (app.models_sqlalchemy) before initializing DB
# otherwise, SQL Alchemy might fail to initialize relationships properly
# for more details: https://github.com/tiangolo/full-stack-fastapi-postgresql/issues/28


def init_db(db: Session) -> None:
    # Tables should be created with Alembic migrations
    # But if you don't want to use migrations, create
    # the tables un-commenting the next line
    logger.info("Creating initial database tables...")
    Base.metadata.create_all(bind=engine)
    logger.info("Database tables created.")

    user = crud.user.get_by_email(db, email=settings.FIRST_SUPERUSER_EMAIL)
    if not user:
        logger.info(f"Creating superuser: {settings.FIRST_SUPERUSER_EMAIL}")
        user_in = schemas.UserCreate(
            email=settings.FIRST_SUPERUSER_EMAIL,
            username=settings.FIRST_SUPERUSER_USERNAME,  # Added username
            password=settings.FIRST_SUPERUSER_PASSWORD,
            is_superuser=True,
            full_name="Default Admin",  # Optional: Add a default full name
            phone=None,  # Add phone field
            is_active=True,  # Ensure superuser is active
            is_verified=True,  # Superuser should be verified by default
            role="admin",  # Ensure superuser has admin role
        )
        user = crud.user.create(db, obj_in=user_in)
        logger.info("Superuser created.")
    else:
        logger.info(
            f"Superuser {settings.FIRST_SUPERUSER_EMAIL} already exists in database."
        )


if __name__ == "__main__":
    # This part is for standalone execution, e.g., during initial setup or testing.
    # In a real application, you might call init_db() from your main application startup logic
    # or a dedicated CLI command.
    from app.db.session import SessionLocal

    logger.info("Creating initial data")
    db_session = SessionLocal()
    try:
        init_db(db_session)
    finally:
        db_session.close()
    logger.info("Initial data created")
