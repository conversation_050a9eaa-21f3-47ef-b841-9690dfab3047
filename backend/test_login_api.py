#!/usr/bin/env python3
"""
测试登录API端点的脚本
"""
import sys

import requests


def test_login_endpoint():
    """测试新的JSON登录端点"""
    url = "http://localhost:5001/api/v1/auth/login"
    payload = {
        "login_identifier": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        response = requests.post(
            url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 400:
            print("✅ API端点正常响应（用户不存在是预期行为）")
            return True
        elif response.status_code == 200:
            print("✅ 登录成功！")
            return True
        else:
            print(f"❌ 意外的状态码: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务器 (http://localhost:5001)")
        print("请确保后端服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🧪 测试登录API端点...")
    success = test_login_endpoint()
    sys.exit(0 if success else 1)
