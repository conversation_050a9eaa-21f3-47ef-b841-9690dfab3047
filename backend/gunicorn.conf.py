# 服务器套接字绑定设置
bind = "0.0.0.0:5001"
backlog = 2048  # 等待连接的最大数量

# 工作进程设置
workers = 4  # 减少工作进程数量，适合开发环境
worker_class = "uvicorn.workers.UvicornWorker"  # 使用 Uvicorn worker 作为 FastAPI 的异步工作进程类型
worker_connections = 1000  # 每个工作进程的最大并发连接数
timeout = 30  # 工作进程超时时间（秒）
keepalive = 2  # 在keep-alive连接上等待请求的秒数

# 进程命名
proc_name = "memorial_api"
# 指向 FastAPI 应用实例，例如在启动命令中 gunicorn -c gunicorn.conf.py app.main:app
default_proc_name = "app.main:app"

# 安全设置
limit_request_line = 4096  # HTTP请求行的最大大小（字节）
limit_request_fields = 100  # HTTP请求头字段的最大数量
limit_request_field_size = 8190  # HTTP请求头字段的最大大小（字节）

# 调试设置
reload = False  # 生产环境中禁用自动重载
spew = False  # 不要在控制台上显示跟踪信息

# 服务器机制
daemon = False  # 不要将Gunicorn守护进程化
raw_env = [
    "APP_ENV=production",
]

# 日志设置
accesslog = "./logs/access.log"
errorlog = "./logs/error.log"
loglevel = "debug"  # 使用debug级别以获取更多信息
capture_output = True  # 捕获标准输出和标准错误
access_log_format = (
    '%({X-Forwarded-For}i)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s"'
)
# 同时输出到控制台
accesslog = "-"  # "-" 表示输出到标准输出
errorlog = "-"  # "-" 表示输出到标准错误

# 进程管理
preload_app = True  # 在工作进程fork之前加载应用
max_requests = 1000  # 工作进程在处理这么多请求后将重启
max_requests_jitter = 50  # 添加随机抖动以避免所有工作进程同时重启

# 优雅的关闭和启动
graceful_timeout = 30  # 优雅关闭超时（秒）
worker_tmp_dir = "/tmp"  # 使用系统临时目录

# 钩子函数
def on_starting(server):
    """服务器启动时执行"""
    print("Gunicorn服务器正在启动...")


def on_exit(server):
    """服务器退出时执行"""
    print("Gunicorn服务器正在关闭...")


def post_fork(server, worker):
    """在工作进程fork后执行"""
    server.log.info("工作进程已启动")
