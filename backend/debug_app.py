#!/usr/bin/env python3
"""
调试脚本 - 用于检查应用是否正常启动
"""

import sys
import traceback

try:
    from app import create_app

    app = create_app()

    print("应用创建成功")
    print(f"应用类型: {type(app)}")
    print(f"应用路由: {app.url_map}")

    # 检查认证路由
    auth_routes = [rule for rule in app.url_map.iter_rules() if "auth" in rule.endpoint]
    print(f"认证路由: {auth_routes}")

    # 检查API路由
    api_routes = [rule for rule in app.url_map.iter_rules() if "api" in rule.endpoint]
    print(f"API路由: {api_routes}")

except Exception as e:
    print(f"应用创建失败: {e}")
    traceback.print_exc()
    sys.exit(1)

print("调试完成")
