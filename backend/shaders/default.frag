
#version 330

uniform vec3 light_direction;
uniform vec3 light_color;
uniform vec3 ambient_color;
uniform sampler2D texture0;
uniform bool has_texture;

in vec3 v_normal;
in vec2 v_texcoord_0;

out vec4 f_color;

void main() {
    vec3 normal = normalize(v_normal);
    float diffuse = max(0.0, dot(normal, -light_direction));

    vec3 color;
    if (has_texture) {
        color = texture(texture0, v_texcoord_0).rgb;
    } else {
        color = vec3(0.8, 0.8, 0.8);
    }

    vec3 final_color = color * (ambient_color + light_color * diffuse);
    f_color = vec4(final_color, 1.0);
}
