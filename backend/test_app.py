#!/usr/bin/env python3
"""
应用测试脚本 - 测试Flask-RESTX API
"""

import json
import traceback

from app import create_app

app = create_app()


def test_login():
    """测试登录功能"""
    print("测试登录功能...")

    # 管理员登录
    admin_data = {"email": "<EMAIL>", "password": "admin123"}

    with app.test_client() as client:
        try:
            # 发送登录请求
            print(f"发送登录请求: {admin_data}")
            response = client.post("/api/auth/login", json=admin_data)
            print(f"状态码: {response.status_code}")
            print(f"响应头: {response.headers}")

            # 获取响应内容
            response_data = response.get_json()
            print(f"响应内容: {json.dumps(response_data, indent=2, ensure_ascii=False)}")

            if (
                response.status_code == 200
                and response_data
                and response_data.get("access_token")
            ):
                admin_token = response_data.get("access_token")
                print(f"管理员登录成功，令牌: {admin_token[:10]}...")

                # 测试获取当前用户信息
                print("\n测试获取当前用户信息...")
                headers = {"Authorization": f"Bearer {admin_token}"}
                response = client.get("/api/auth/me", headers=headers)
                print(f"状态码: {response.status_code}")
                print(
                    f"响应内容: {json.dumps(response.get_json(), indent=2, ensure_ascii=False)}"
                )

                # 测试获取环境列表
                print("\n测试获取环境列表...")
                response = client.get("/api/main/environments")
                print(f"状态码: {response.status_code}")
                print(
                    f"响应内容: {json.dumps(response.get_json(), indent=2, ensure_ascii=False)}"
                )

                # 测试获取先人列表
                print("\n测试获取先人列表...")
                response = client.get("/api/main/ancestors", headers=headers)
                print(f"状态码: {response.status_code}")
                print(
                    f"响应内容: {json.dumps(response.get_json(), indent=2, ensure_ascii=False)}"
                )

                # 测试渲染服务状态
                print("\n测试渲染服务状态...")
                response = client.get("/api/render/status")
                print(f"状态码: {response.status_code}")
                print(
                    f"响应内容: {json.dumps(response.get_json(), indent=2, ensure_ascii=False)}"
                )
            else:
                print("登录失败或未获取到访问令牌")
        except Exception as e:
            print(f"测试过程中出现异常: {e}")
            traceback.print_exc()


def test_api_docs():
    """测试API文档"""
    print("\n测试API文档...")

    with app.test_client() as client:
        try:
            response = client.get("/api/docs")
            print(f"状态码: {response.status_code}")
            if response.status_code == 200:
                print("API文档可访问")
            else:
                print(f"API文档不可访问: {response.status_code}")
        except Exception as e:
            print(f"测试过程中出现异常: {e}")
            traceback.print_exc()


if __name__ == "__main__":
    with app.app_context():
        test_login()
        test_api_docs()
