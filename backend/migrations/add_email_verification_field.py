"""Add email verification field to users table

Revision ID: add_email_verification
Revises: 
Create Date: 2024-01-01 00:00:00.000000

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = 'add_email_verification'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Add is_email_verified column to users table
    op.add_column('users', sa.Column('is_email_verified', sa.<PERSON>(), nullable=False, server_default='false'))


def downgrade():
    # Remove is_email_verified column from users table
    op.drop_column('users', 'is_email_verified')