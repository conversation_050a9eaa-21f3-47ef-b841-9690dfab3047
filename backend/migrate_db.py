#!/usr/bin/env python3
"""
数据库迁移脚本 - 用于更新数据库结构
"""


from app import create_app, db
from app.models import User


def migrate_db():
    """执行数据库迁移"""
    app = create_app()

    with app.app_context():
        # 首先删除所有表并重新创建
        print("删除并重新创建所有表...")
        db.drop_all()
        db.create_all()
        print("数据库表已创建")

        # 添加初始管理员用户
        print("添加初始管理员用户...")
        admin = User(
            username="admin", email="<EMAIL>", role="admin", is_active=True
        )
        admin.set_password("admin123")
        db.session.add(admin)

        # 添加测试用户
        test_user = User(
            username="test", email="<EMAIL>", role="user", is_active=True
        )
        test_user.set_password("test123")
        db.session.add(test_user)

        # 提交更改
        db.session.commit()
        print("初始用户已添加")

        print("数据库迁移完成")


if __name__ == "__main__":
    migrate_db()
