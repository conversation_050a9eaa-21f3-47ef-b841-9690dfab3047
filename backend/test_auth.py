#!/usr/bin/env python3
"""
认证系统测试脚本
"""

import json
import sys

import requests

BASE_URL = "http://localhost:5001/api/auth"


def test_login():
    """测试登录功能"""
    print("测试登录功能...")

    # 管理员登录
    admin_data = {"email": "<EMAIL>", "password": "admin123"}

    response = requests.post(f"{BASE_URL}/login", json=admin_data)
    print(f"状态码: {response.status_code}")
    print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")

    if response.status_code == 200:
        admin_token = response.json().get("access_token")
        admin_refresh = response.json().get("refresh_token")
        print(f"管理员登录成功，令牌: {admin_token[:10]}...")

        # 测试获取当前用户信息
        print("\n测试获取当前用户信息...")
        headers = {"Authorization": f"Bearer {admin_token}"}
        response = requests.get(f"{BASE_URL}/me", headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")

        # 测试刷新令牌
        print("\n测试刷新令牌...")
        headers = {"Authorization": f"Bearer {admin_refresh}"}
        response = requests.post(f"{BASE_URL}/refresh", headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")

        if response.status_code == 200:
            new_token = response.json().get("access_token")
            print(f"令牌刷新成功，新令牌: {new_token[:10]}...")

            # 测试登出
            print("\n测试登出...")
            headers = {"Authorization": f"Bearer {new_token}"}
            response = requests.post(f"{BASE_URL}/logout", headers=headers)
            print(f"状态码: {response.status_code}")
            print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")

    # 测试用户登录
    print("\n测试普通用户登录...")
    user_data = {"email": "<EMAIL>", "password": "test123"}

    response = requests.post(f"{BASE_URL}/login", json=user_data)
    print(f"状态码: {response.status_code}")
    print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")

    if response.status_code == 200:
        user_token = response.json().get("access_token")
        print(f"用户登录成功，令牌: {user_token[:10]}...")


def test_register():
    """测试注册功能"""
    print("\n测试注册功能...")

    # 注册新用户
    user_data = {
        "username": "newuser",
        "email": "<EMAIL>",
        "password": "newuser123",
    }

    response = requests.post(f"{BASE_URL}/register", json=user_data)
    print(f"状态码: {response.status_code}")
    print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")


def test_password_reset():
    """测试密码重置功能"""
    print("\n测试密码重置功能...")

    # 忘记密码
    data = {"email": "<EMAIL>"}

    response = requests.post(f"{BASE_URL}/forgot-password", json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")

    if response.status_code == 200 and "reset_token" in response.json():
        # 重置密码
        reset_data = {
            "email": "<EMAIL>",
            "token": response.json()["reset_token"],
            "password": "newpassword123",
        }

        response = requests.post(f"{BASE_URL}/reset-password", json=reset_data)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "login":
            test_login()
        elif sys.argv[1] == "register":
            test_register()
        elif sys.argv[1] == "reset":
            test_password_reset()
        else:
            print(f"未知的测试类型: {sys.argv[1]}")
            print("可用的测试类型: login, register, reset")
    else:
        # 运行所有测试
        test_login()
        test_register()
        test_password_reset()
