from datetime import datetime

from app import create_app, db
from app.models import Environment, ReligiousCulturalSetting, User


def init_db():
    app = create_app()
    with app.app_context():
        # 创建所有表
        db.create_all()

        # 检查是否已有数据
        if User.query.first() is not None:
            print("数据库已初始化，跳过")
            return

        # 创建管理员用户
        admin = User(username="admin", email="<EMAIL>")
        admin.set_password("admin123")
        db.session.add(admin)

        # 创建基础环境
        environments = [
            {
                "name": "中国传统祠堂",
                "description": "传统中式祠堂环境，适合祭祀祖先",
                "style_type": "东方传统",
                "religious_affiliation": ["儒家", "道教"],
                "cultural_elements": {
                    "architecture": "中式木结构",
                    "colors": ["红色", "金色", "褐色"],
                    "symbols": ["龙", "凤", "祥云"],
                },
                "season_support": True,
                "weather_support": True,
                "model_path": "/3d-assets/environments/chinese_temple.glb",
                "low_poly_path": "/3d-assets/environments/chinese_temple_low.glb",
                "thumbnail": "/images/environments/chinese_temple.jpg",
            },
            {
                "name": "佛教寺庙",
                "description": "禅宗风格的佛教寺庙环境",
                "style_type": "东方传统",
                "religious_affiliation": ["佛教"],
                "cultural_elements": {
                    "architecture": "佛塔式",
                    "colors": ["金色", "红色", "褐色"],
                    "symbols": ["佛像", "莲花", "法轮"],
                },
                "season_support": True,
                "weather_support": True,
                "model_path": "/3d-assets/environments/buddhist_temple.glb",
                "low_poly_path": "/3d-assets/environments/buddhist_temple_low.glb",
                "thumbnail": "/images/environments/buddhist_temple.jpg",
            },
            {
                "name": "西方教堂",
                "description": "哥特式基督教教堂环境",
                "style_type": "西方宗教",
                "religious_affiliation": ["基督教", "天主教"],
                "cultural_elements": {
                    "architecture": "哥特式",
                    "colors": ["白色", "金色", "蓝色"],
                    "symbols": ["十字架", "圣经", "天使"],
                },
                "season_support": True,
                "weather_support": True,
                "model_path": "/3d-assets/environments/gothic_church.glb",
                "low_poly_path": "/3d-assets/environments/gothic_church_low.glb",
                "thumbnail": "/images/environments/gothic_church.jpg",
            },
            {
                "name": "自然森林",
                "description": "宁静的森林环境，适合自然崇拜或无宗教信仰者",
                "style_type": "现代/中性",
                "religious_affiliation": ["自然崇拜", "无宗教"],
                "cultural_elements": {
                    "architecture": "自然景观",
                    "colors": ["绿色", "棕色", "蓝色"],
                    "symbols": ["树木", "溪流", "岩石"],
                },
                "season_support": True,
                "weather_support": True,
                "model_path": "/3d-assets/environments/forest.glb",
                "low_poly_path": "/3d-assets/environments/forest_low.glb",
                "thumbnail": "/images/environments/forest.jpg",
            },
        ]

        for env_data in environments:
            env = Environment(
                name=env_data["name"],
                description=env_data["description"],
                style_type=env_data["style_type"],
                season_support=env_data["season_support"],
                weather_support=env_data["weather_support"],
                model_path=env_data["model_path"],
                low_poly_path=env_data["low_poly_path"],
                thumbnail=env_data["thumbnail"],
                creation_date=datetime.utcnow(),
            )
            env.set_religious_affiliation_list(env_data["religious_affiliation"])
            env.set_cultural_elements(env_data["cultural_elements"])
            db.session.add(env)

        # 创建宗教文化设置
        religious_settings = [
            {
                "name": "儒家祭祀",
                "description": "中国传统儒家祭祀礼仪",
                "ritual_elements": [
                    {
                        "name": "香炉",
                        "description": "用于焚香祭祀",
                        "model_path": "/3d-assets/items/incense_burner.glb",
                    },
                    {
                        "name": "祭品",
                        "description": "传统祭品如水果、糕点等",
                        "model_path": "/3d-assets/items/offerings.glb",
                    },
                    {
                        "name": "祭文",
                        "description": "传统祭文",
                        "model_path": "/3d-assets/items/prayer_scroll.glb",
                    },
                ],
                "special_dates": [
                    {"name": "清明节", "date": "04-05", "description": "扫墓祭祖的传统节日"},
                    {"name": "中元节", "date": "08-15", "description": "祭祀祖先和亡灵的传统节日"},
                    {"name": "冬至", "date": "12-21", "description": "祭祀祖先的传统节气"},
                ],
                "default_prayers": "谨以清香、庄严敬献，愿先人在天之灵安息，保佑后人平安健康，家族兴旺。",
                "icon_path": "/images/icons/confucian.png",
            },
            {
                "name": "佛教追思",
                "description": "佛教传统追思仪式",
                "ritual_elements": [
                    {
                        "name": "莲花灯",
                        "description": "象征佛教中的纯洁与觉悟",
                        "model_path": "/3d-assets/items/lotus_lamp.glb",
                    },
                    {
                        "name": "佛香",
                        "description": "用于佛教祭祀的特殊香",
                        "model_path": "/3d-assets/items/buddhist_incense.glb",
                    },
                    {
                        "name": "佛经",
                        "description": "佛教经文",
                        "model_path": "/3d-assets/items/sutra.glb",
                    },
                ],
                "special_dates": [
                    {"name": "佛诞", "date": "05-12", "description": "纪念佛祖释迦牟尼诞辰"},
                    {"name": "盂兰盆节", "date": "08-15", "description": "佛教祭祀先人的节日"},
                ],
                "default_prayers": "南无阿弥陀佛，愿逝者往生净土，得闻佛法，早日成佛。",
                "icon_path": "/images/icons/buddhist.png",
            },
            {
                "name": "基督教追思",
                "description": "基督教传统追思仪式",
                "ritual_elements": [
                    {
                        "name": "十字架",
                        "description": "基督教信仰的核心象征",
                        "model_path": "/3d-assets/items/cross.glb",
                    },
                    {
                        "name": "蜡烛",
                        "description": "象征光明与希望",
                        "model_path": "/3d-assets/items/candle.glb",
                    },
                    {
                        "name": "圣经",
                        "description": "基督教经文",
                        "model_path": "/3d-assets/items/bible.glb",
                    },
                ],
                "special_dates": [
                    {"name": "万灵节", "date": "11-01", "description": "纪念所有圣人和逝者的节日"},
                    {"name": "复活节", "date": "variable", "description": "纪念耶稣复活的节日"},
                ],
                "default_prayers": "主啊，求你接纳逝者的灵魂，让他们在天国得享永恒的安息与喜乐。阿们。",
                "icon_path": "/images/icons/christian.png",
            },
            {
                "name": "自然纪念",
                "description": "基于自然崇拜的纪念方式，适合无特定宗教信仰者",
                "ritual_elements": [
                    {
                        "name": "植物",
                        "description": "象征生命循环",
                        "model_path": "/3d-assets/items/plant.glb",
                    },
                    {
                        "name": "石头",
                        "description": "象征永恒",
                        "model_path": "/3d-assets/items/stone.glb",
                    },
                    {
                        "name": "水",
                        "description": "象征生命之源",
                        "model_path": "/3d-assets/items/water.glb",
                    },
                ],
                "special_dates": [
                    {"name": "春分", "date": "03-20", "description": "象征新生的节气"},
                    {"name": "秋分", "date": "09-23", "description": "象征收获与感恩的节气"},
                ],
                "default_prayers": "生命如同自然的循环，逝者已融入宇宙的永恒。愿我们铭记逝者的爱与智慧，继续生命的旅程。",
                "icon_path": "/images/icons/nature.png",
            },
        ]

        for setting_data in religious_settings:
            setting = ReligiousCulturalSetting(
                name=setting_data["name"],
                description=setting_data["description"],
                default_prayers=setting_data["default_prayers"],
                icon_path=setting_data["icon_path"],
                created_at=datetime.utcnow(),
            )
            setting.set_ritual_elements(setting_data["ritual_elements"])
            setting.set_special_dates(setting_data["special_dates"])
            db.session.add(setting)

        # 提交所有更改
        db.session.commit()
        print("数据库初始化完成")


if __name__ == "__main__":
    init_db()
