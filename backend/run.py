import logging

import uvicorn

from app.core.config import settings
from app.db.init_db import init_db
from app.db.session import SessionLocal

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def run_initialization():
    logger.info("Initializing database...")
    db = SessionLocal()
    try:
        init_db(db)
        logger.info("Database initialization complete.")
    except Exception as e:
        logger.error(f"Error during database initialization: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    # Run database initialization before starting the server
    # In a production environment, you might want to separate this step
    # or use a migration tool like Alembic more formally.
    run_initialization()

    # Extract host and port from SERVER_HOST if it's a full URL, otherwise default
    host = "0.0.0.0"
    port = 9010  # Default port, changed to 9010 as per user requirement

    logger.info(f"Starting server on http://{host}:{port}")

    if settings.SERVER_HOST:
        try:
            # Attempt to parse host and port from SERVER_HOST
            from urllib.parse import urlparse

            parsed_url = urlparse(str(settings.SERVER_HOST))
            if parsed_url.hostname:
                host = parsed_url.hostname
            if parsed_url.port:
                port = parsed_url.port
        except Exception as e:
            logger.warning(
                f"Could not parse SERVER_HOST ('{settings.SERVER_HOST}'), using defaults. Error: {e}"
            )

    uvicorn.run(
        "app.main:app",
        host=host,
        port=port,
        reload=True,  # Enable auto-reload for development
        log_level="info",
    )
