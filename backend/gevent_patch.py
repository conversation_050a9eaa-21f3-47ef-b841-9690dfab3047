"""
预加载脚本，在应用启动前进行 gevent monkey patching
"""
from gevent import monkey

from app import create_app, db
from app.models import Ancestor, Environment, ReligiousCulturalSetting, User

# 在任何其他导入之前进行 monkey patching
monkey.patch_all()

app = create_app()


@app.shell_context_processor
def make_shell_context():
    return {
        "db": db,
        "User": User,
        "Ancestor": Ancestor,
        "Environment": Environment,
        "ReligiousCulturalSetting": ReligiousCulturalSetting,
    }


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5001)
