#!/usr/bin/env python3
"""
登录测试脚本 - 直接测试登录路由
"""

import json

import requests


def test_login():
    """测试登录功能"""
    print("测试登录功能...")

    # 管理员登录
    admin_data = {"email": "<EMAIL>", "password": "admin123"}

    # 直接使用完整URL
    url = "http://localhost:5001/api/auth/login"
    print(f"请求URL: {url}")
    print(f"请求数据: {admin_data}")

    response = requests.post(url, json=admin_data)
    print(f"状态码: {response.status_code}")
    print(f"响应头: {response.headers}")
    print(f"响应内容: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")


if __name__ == "__main__":
    test_login()
