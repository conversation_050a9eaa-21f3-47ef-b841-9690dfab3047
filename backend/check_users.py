#!/usr/bin/env python3
"""
检查用户数据脚本 - 用于检查数据库中的用户数据
"""

from app import create_app
from app.models.user import User

app = create_app()


def check_users():
    """检查数据库中的用户数据"""
    with app.app_context():
        # 获取所有用户
        users = User.query.all()

        print(f"数据库中共有 {len(users)} 个用户:")

        for user in users:
            print(f"ID: {user.id}")
            print(f"用户名: {user.username}")
            print(f"邮箱: {user.email}")
            print(f"角色: {user.role}")
            print(f"是否激活: {user.is_active}")
            print(f"创建时间: {user.created_at}")
            print(f"最后登录时间: {user.last_login}")
            print("-" * 50)


if __name__ == "__main__":
    check_users()
