#!/bin/bash

# 统一服务启动脚本
# 使用方法: ./start_unified_service.sh

# 切换到项目目录
cd "$(dirname "$0")"

# 确保日志目录存在
mkdir -p backend/logs

# 停止可能已经运行的 Gunicorn 进程
pkill -f gunicorn || true

# 等待进程完全停止
sleep 1

# 检查是否已安装所需的依赖
echo "检查依赖..."
python3 -c "import moderngl" 2>/dev/null || { echo "安装 moderngl..."; pip install moderngl; }
python3 -c "import trimesh" 2>/dev/null || { echo "安装 trimesh..."; pip install trimesh; }
python3 -c "import pyrr" 2>/dev/null || { echo "安装 pyrr..."; pip install pyrr; }
python3 -c "import numpy" 2>/dev/null || { echo "安装 numpy..."; pip install numpy; }
python3 -c "import PIL" 2>/dev/null || { echo "安装 Pillow..."; pip install Pillow; }
python3 -c "import flask" 2>/dev/null || { echo "安装 Flask..."; pip install flask flask-cors; }
python3 -c "import glm" 2>/dev/null || { echo "安装 PyGLM..."; pip install PyGLM; }

echo "启动统一服务..."
cd backend && gunicorn -c gunicorn.conf.py gevent_patch:app

# 如果 Gunicorn 启动失败，尝试使用 Flask 开发服务器
if [ $? -ne 0 ]; then
    echo "Gunicorn 启动失败，尝试使用 Flask 开发服务器..."
    cd backend && python run.py
fi
