# Memorial 项目快速启动指南

## 🚀 一键启动

```bash
# 1. 测试环境
./test_startup.sh

# 2. 启动服务
./start_server.sh

# 3. 停止服务 (可选)
./stop_server.sh
```

## 📍 服务地址

启动成功后，你可以通过以下地址访问：

- **🌐 前端应用**: [http://localhost:4001](http://localhost:4001)
- **🔧 后端 API**: [http://localhost:8008](http://localhost:8008)
- **📚 API 文档**: [http://localhost:8008/docs](http://localhost:8008/docs)
- **🔍 健康检查**: [http://localhost:8008/health](http://localhost:8008/health)

## 🔧 端口配置

- **后端 FastAPI**: 8008 端口
- **前端 Vite**: 4001 端口
- **前端代理**: `/api/v1` → `http://localhost:8008`

## 📋 环境要求

- **Python**: 3.11+
- **Node.js**: 18+
- **PostgreSQL**: 15+ (可选，用于数据持久化)

## 🛠️ 手动启动步骤

如果自动启动脚本有问题，可以手动启动：

### 1. 后端启动
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --host 0.0.0.0 --port 8008 --reload
```

### 2. 前端启动
```bash
cd frontend
npm install
npm run dev
```

## 🔧 环境配置

1. **复制环境文件**: `cp .env.example .env`
2. **修改数据库配置** (如需要):
   ```
   DATABASE_URL=postgresql://memorial_user:memorial_pass@localhost/memorial_db
   ```
3. **设置API密钥** (如需要):
   ```
   REPLICATE_API_TOKEN=your_token_here
   ```

## 🗄️ 数据库设置 (可选)

如果需要数据持久化，请设置PostgreSQL：

```bash
# 启动 PostgreSQL
brew services start postgresql

# 创建数据库
createdb memorial_db

# 创建用户
psql -c "CREATE USER memorial_user WITH PASSWORD 'memorial_pass';"
psql -c "GRANT ALL PRIVILEGES ON DATABASE memorial_db TO memorial_user;"
```

## ⚠️ 常见问题

### 端口被占用
```bash
# 检查端口占用
lsof -i :8008
lsof -i :4001

# 停止占用进程
./stop_server.sh
```

### 依赖安装失败
```bash
# 后端依赖
cd backend && pip install -r requirements.txt

# 前端依赖
cd frontend && npm install
```

### 数据库连接失败
```bash
# 检查 PostgreSQL 状态
brew services list | grep postgresql

# 启动 PostgreSQL
brew services start postgresql
```

## 📊 项目功能

启动成功后，你可以体验：

- ✅ **3D纪念空间**: 沉浸式3D环境
- ✅ **AI照片修复**: 照片增强、背景移除等
- ✅ **祭拜系统**: 虚拟祭品、留言系统
- ✅ **用户管理**: 注册、登录、权限控制
- ✅ **商业化功能**: 虚拟商店、订阅体系、支付系统

## 🎯 下一步

1. 访问前端应用: [http://localhost:4001](http://localhost:4001)
2. 查看API文档: [http://localhost:8008/docs](http://localhost:8008/docs)
3. 体验3D纪念空间功能
4. 测试AI照片处理功能

## 💡 开发提示

- **热重载**: 前后端都支持代码修改自动重载
- **调试**: 后端使用 `--reload` 模式，前端使用 Vite dev server
- **日志**: 查看终端输出获取详细信息
- **API测试**: 使用 `/docs` 页面进行API测试

---

🎉 欢迎使用 Memorial 数字纪念平台！