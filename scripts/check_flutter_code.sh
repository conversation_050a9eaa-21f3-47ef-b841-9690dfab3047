#!/bin/bash
# Flutter 代码质量检查脚本
# 包含代码分析、格式化检查和测试运行

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
FLUTTER_DIR="$PROJECT_ROOT/flutter"
REPORTS_DIR="$PROJECT_ROOT/reports"

# 创建报告目录（如果不存在）
mkdir -p "$REPORTS_DIR"

echo "===== Flutter 代码质量检查 ====="
echo "Flutter 项目目录: $FLUTTER_DIR"
echo "报告目录: $REPORTS_DIR"
echo ""

# 检查 Flutter 项目是否存在
if [ ! -d "$FLUTTER_DIR" ]; then
    echo "错误: Flutter 项目目录不存在: $FLUTTER_DIR"
    exit 1
fi

# 进入 Flutter 项目目录
cd "$FLUTTER_DIR"

# 检查 Flutter 环境
echo "===== 检查 Flutter 环境 ====="
flutter doctor --version
echo ""

# 获取依赖
echo "===== 获取 Flutter 依赖 ====="
flutter pub get
echo ""

# 代码分析
echo "===== Flutter 代码分析 ====="
echo "运行 flutter analyze..."
if flutter analyze > "$REPORTS_DIR/flutter-analyze.txt" 2>&1; then
    echo "✅ 代码分析通过"
else
    echo "❌ 代码分析发现问题，详情请查看: $REPORTS_DIR/flutter-analyze.txt"
    cat "$REPORTS_DIR/flutter-analyze.txt"
fi
echo ""

# 代码格式化检查
echo "===== Flutter 代码格式化检查 ====="
echo "检查代码格式..."
if dart format --set-exit-if-changed --output=none lib/ test/ > "$REPORTS_DIR/flutter-format.txt" 2>&1; then
    echo "✅ 代码格式符合规范"
else
    echo "❌ 代码格式需要调整，详情请查看: $REPORTS_DIR/flutter-format.txt"
    echo "运行以下命令自动格式化代码:"
    echo "  cd $FLUTTER_DIR && dart format lib/ test/"
    cat "$REPORTS_DIR/flutter-format.txt"
fi
echo ""

# 运行测试
echo "===== Flutter 测试 ====="
echo "运行单元测试和 Widget 测试..."
if flutter test > "$REPORTS_DIR/flutter-test.txt" 2>&1; then
    echo "✅ 所有测试通过"
else
    echo "⚠️  测试存在问题，详情请查看: $REPORTS_DIR/flutter-test.txt"
    echo "注意: 测试失败可能是由于布局溢出等非关键问题导致"
    # 不让测试失败阻止整个检查流程
fi
echo ""

# 检查依赖更新
echo "===== 检查依赖更新 ====="
echo "检查过时的依赖包..."
flutter pub outdated > "$REPORTS_DIR/flutter-outdated.txt" 2>&1 || true
echo "依赖更新信息已保存到: $REPORTS_DIR/flutter-outdated.txt"
echo ""

# 生成代码覆盖率报告（如果有测试）
echo "===== 生成代码覆盖率报告 ====="
if [ -d "test" ] && [ "$(find test -name '*.dart' | wc -l)" -gt 0 ]; then
    echo "生成代码覆盖率报告..."
    if flutter test --coverage > "$REPORTS_DIR/flutter-coverage.txt" 2>&1; then
        echo "✅ 代码覆盖率报告生成成功"
        if command -v lcov >/dev/null 2>&1; then
            echo "生成 HTML 覆盖率报告..."
            genhtml coverage/lcov.info -o "$REPORTS_DIR/coverage_html" 2>/dev/null || true
            echo "HTML 覆盖率报告: $REPORTS_DIR/coverage_html/index.html"
        fi
    else
        echo "❌ 代码覆盖率报告生成失败"
        cat "$REPORTS_DIR/flutter-coverage.txt"
    fi
else
    echo "⚠️  未找到测试文件，跳过代码覆盖率检查"
fi
echo ""

echo "===== Flutter 代码质量检查完成 ====="
echo "所有报告已保存到: $REPORTS_DIR"
echo "主要报告文件:"
echo "  - flutter-analyze.txt: 代码分析结果"
echo "  - flutter-format.txt: 代码格式化检查结果"
echo "  - flutter-test.txt: 测试运行结果"
echo "  - flutter-outdated.txt: 依赖更新信息"
echo "  - flutter-coverage.txt: 代码覆盖率信息"
echo ""