#!/bin/bash
# 后端代码质量检查脚本
# 运行MyPy、Black、Ruff和Bandit检查

# set -e  # 已移除，避免提前退出

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../" && pwd)"
BACKEND_DIR="$PROJECT_ROOT/backend"
REPORTS_DIR="$PROJECT_ROOT/reports"

# 创建报告目录（如果不存在）
mkdir -p "$REPORTS_DIR"

EXIT_CODE=0  # 显式初始化

echo "开始后端代码质量检查..."

# 运行MyPy类型检查
echo "运行MyPy类型检查..."
# 清除Python缓存
find "$BACKEND_DIR" -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
find "$BACKEND_DIR" -name "*.pyc" -delete 2>/dev/null || true

# 添加--exclude参数来排除模块名称冲突
cd "$BACKEND_DIR" && { python -m mypy --config-file="$PROJECT_ROOT/mypy.ini" --no-namespace-packages --ignore-missing-imports --follow-imports=skip --exclude="app/api/namespaces/auth" app > "$REPORTS_DIR/mypy-report.txt" 2>&1; } || {
    echo "MyPy类型检查失败，请查看报告: $REPORTS_DIR/mypy-report.txt"
    EXIT_CODE=1
}

# 运行Black代码格式检查
echo "运行Black代码格式检查..."
cd "$BACKEND_DIR" && { python -m black --check app > "$REPORTS_DIR/black-report.txt" 2>&1; } || {
    echo "Black格式检查失败，请查看报告: $REPORTS_DIR/black-report.txt"
    echo "运行 'python -m black app' 来自动格式化代码"
    EXIT_CODE=1
}

# 运行Ruff代码质量检查
echo "运行Ruff代码质量检查..."
cd "$BACKEND_DIR" && { python -m ruff check app --config="$PROJECT_ROOT/pyproject.toml" > "$REPORTS_DIR/ruff-report.txt" 2>&1; } || {
    echo "Ruff检查失败，请查看报告: $REPORTS_DIR/ruff-report.txt"
    EXIT_CODE=1
}

# 运行Bandit安全检查
echo "运行Bandit安全检查..."
cd "$BACKEND_DIR" && python -m bandit -r app -c "$PROJECT_ROOT/pyproject.toml" > "$REPORTS_DIR/bandit-report.txt" 2>&1

if grep -q "Severity: High" "$REPORTS_DIR/bandit-report.txt"; then
    echo "❌ Bandit 检查发现高危安全问题，请查看报告: $REPORTS_DIR/bandit-report.txt"
    EXIT_CODE=1
else
    echo "✅ Bandit 未发现高危安全问题。详细报告: $REPORTS_DIR/bandit-report.txt"
fi

if [ "${EXIT_CODE:-0}" -eq 0 ]; then
    echo "✅ 后端代码质量检查通过！"
else
    echo "❌ 后端代码质量检查失败，请查看上述报告修复问题。"
    exit $EXIT_CODE
fi

echo "DEBUG: EXIT_CODE=$EXIT_CODE"
