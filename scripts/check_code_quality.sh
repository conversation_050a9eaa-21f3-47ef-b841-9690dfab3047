#!/bin/bash
# 代码质量检查主脚本
# 运行前端和后端的所有代码质量检查

# set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPORTS_DIR="$(cd "$SCRIPT_DIR/../reports" && pwd)"

# 创建报告目录（如果不存在）
mkdir -p "$REPORTS_DIR"

echo "===== 开始全面代码质量检查 ====="
echo "报告将保存在: $REPORTS_DIR"
echo ""

# 运行后端代码质量检查
echo "===== 后端代码质量检查 ====="
bash "$SCRIPT_DIR/check_backend_code.sh"
BACKEND_EXIT_CODE=$?
echo ""

# 运行前端代码质量检查
echo "===== 前端代码质量检查 ====="
bash "$SCRIPT_DIR/check_frontend_code.sh"
FRONTEND_EXIT_CODE=$?
echo ""

# 检查结果汇总
echo "===== 代码质量检查结果汇总 ====="
if [ $BACKEND_EXIT_CODE -eq 0 ] && [ $FRONTEND_EXIT_CODE -eq 0 ]; then
    echo "✅ 所有代码质量检查通过！"
    exit 0
else
    echo "❌ 代码质量检查失败，请查看上述报告修复问题。"
    echo "后端检查状态: $([ $BACKEND_EXIT_CODE -eq 0 ] && echo '通过 ✅' || echo '失败 ❌')"
    echo "前端检查状态: $([ $FRONTEND_EXIT_CODE -eq 0 ] && echo '通过 ✅' || echo '失败 ❌')"
    exit 1
fi
