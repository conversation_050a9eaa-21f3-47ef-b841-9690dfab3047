#!/bin/bash
# 检查前端代码质量

# 检查 ESLint
if [ -f "$(dirname "${BASH_SOURCE[0]}")/../frontend/node_modules/.bin/eslint" ]; then
    echo "正在运行 ESLint 检查..."
    cd "$(dirname "${BASH_SOURCE[0]}")/../frontend" && pnpm run lint || {
        echo "ESLint 检查失败，请查看报告: $(dirname "${BASH_SOURCE[0]}")/../reports/eslint-report.json"
        echo "运行 'pnpm run lint:fix' 来自动修复可自动修复的问题"
        exit 1
    }
else
    echo "警告: ESLint 未安装，跳过检查"
fi

# 检查 Prettier
cd "$(dirname "${BASH_SOURCE[0]}")/../frontend"
if [ -f "node_modules/.bin/prettier" ]; then
    echo "正在运行 Prettier 检查..."
    npx prettier --check "src/**/*.{ts,tsx,css,html}" || {
        echo "Prettier 检查失败，请查看报告: $(dirname "${BASH_SOURCE[0]}")/../reports/prettier-report.txt"
        echo "运行 'npm run format' 来自动格式化代码"
        exit 1
    }
else
    echo "警告: Prettier 未安装，跳过检查"
fi

echo "前端代码检查完成，未发现错误"
