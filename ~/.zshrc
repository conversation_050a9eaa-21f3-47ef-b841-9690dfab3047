# =============================================================================
# ZSH Configuration File
# =============================================================================

# 禁用 compinit 安全检查（解决权限警告）
ZSH_DISABLE_COMPFIX=true

# =============================================================================
# Conda 初始化
# =============================================================================
# >>> conda initialize >>>
# !! Contents within this block are managed by 'conda init' !!
__conda_setup="$('/Volumes/acasis/miniconda3/miniconda3/bin/conda' 'shell.zsh' 'hook' 2> /dev/null)"
if [ $? -eq 0 ]; then
    eval "$__conda_setup"
else
    if [ -f "/Volumes/acasis/miniconda3/miniconda3/etc/profile.d/conda.sh" ]; then
        . "/Volumes/acasis/miniconda3/miniconda3/etc/profile.d/conda.sh"
    else
        export PATH="/Volumes/acasis/miniconda3/miniconda3/bin:$PATH"
    fi
fi
unset __conda_setup
# <<< conda initialize <<<

# =============================================================================
# 环境管理函数（优化版 - 避免与 direnv 冲突）
# =============================================================================
function activate_project_env() {
    # 跳过 memorial 项目，让 direnv 处理
    if [[ "$(pwd)" == "/Volumes/acasis/memorial"* ]]; then
        return
    fi
    
    # 处理其他项目的环境文件
    local env_file="environmentMACOS.yml"
    if [[ -f "$env_file" ]]; then
        local env_name=$(grep 'name:' "$env_file" | awk '{print $2}')
        if [[ -n "$env_name" && "$CONDA_DEFAULT_ENV" != "$env_name" ]]; then
            conda activate "$env_name" 2>/dev/null
        fi
    fi
}

# =============================================================================
# 提示符自定义
# =============================================================================
function update_prompt() {
    if [[ -n "$CONDA_DEFAULT_ENV" ]]; then
        export PS1="(%F{green}${CONDA_DEFAULT_ENV}%f) %n@%m %1~ %# "
    else
        export PS1="%n@%m %1~ %# "
    fi
}

# =============================================================================
# ZSH 钩子函数
# =============================================================================
precmd_functions+=(update_prompt)
chpwd_functions+=(activate_project_env)

# 初始化当前目录环境
activate_project_env

# =============================================================================
# 系统路径配置
# =============================================================================
# PostgreSQL
export PATH="/opt/homebrew/opt/postgresql@15/bin:$PATH"

# =============================================================================
# Node.js 环境管理
# =============================================================================
# NVM (Node Version Manager)
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

# FNM (Fast Node Manager) - 更快的 Node 版本管理器
eval "$(fnm env --use-on-cd --shell zsh)"

# PNPM 包管理器
export PNPM_HOME="/Users/<USER>/Library/pnpm"
case ":$PATH:" in
  *":$PNPM_HOME:"*) ;;
  *) export PATH="$PNPM_HOME:$PATH" ;;
esac

# =============================================================================
# 开发工具配置
# =============================================================================
# Docker CLI 补全
fpath=(/Users/<USER>/.docker/completions $fpath)

# ZSH 补全系统
autoload -Uz compinit
compinit -i

# Windsurf 编辑器
export PATH="/Users/<USER>/.codeium/windsurf/bin:$PATH"

# LM Studio CLI
export PATH="$PATH:/Users/<USER>/.lmstudio/bin"

# =============================================================================
# 移动开发环境
# =============================================================================
# Flutter
export PATH="$PATH:/Volumes/acasis/memorial/mobile/flutter/bin"

# Ruby 环境管理 (rbenv)
export PATH="$HOME/.rbenv/bin:$PATH"
eval "$(rbenv init - zsh)"
eval "$(rbenv init -)"

# =============================================================================
# 系统配置
# =============================================================================
# 语言环境
export LANG=en_US.UTF-8

# =============================================================================
# 项目环境管理 (direnv)
# =============================================================================
# direnv 钩子 - 必须在文件末尾（静默模式）
export DIRENV_LOG_FORMAT=""
eval "$(direnv hook zsh)" 2>/dev/null

# =============================================================================
# 可选配置（已注释，需要时取消注释）
# =============================================================================
# InsightFace 环境变量
# export INSIGHTFACE_HOME="/Users/<USER>/.insightface"
# export INSIGHTFACE_DISABLE_DOWNLOAD=1

# 项目特定的 PYTHONPATH（根据需要取消注释）
# export PYTHONPATH="$PYTHONPATH:$HOME/path/to/your/project"
# export PYTHONPATH="$PYTHONPATH:/Volumes/acasis/ema20115/ema2_20250115/backend"