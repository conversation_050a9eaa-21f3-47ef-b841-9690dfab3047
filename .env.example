# Memorial 项目环境变量配置示例
# 复制此文件为 .env 并修改相应值

# 后端配置
SECRET_KEY=memorial-dev-secret-key-2025
JWT_SECRET_KEY=memorial-jwt-secret-key-2025

# 数据库配置
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_USER=memorial
POSTGRES_PASSWORD=memorial
POSTGRES_DB=memorial
DATABASE_URL=postgresql://memorial:memorial@localhost/memorial

# Replicate AI API (用于AI功能)
REPLICATE_API_TOKEN=your_replicate_api_token_here

# 邮件服务配置 (可选)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=Memorial Platform

# 服务器配置
SERVER_HOST=http://localhost:8008
FRONTEND_HOST=http://localhost:4001

# 文件上传配置
UPLOAD_DIRECTORY=./uploads
STATIC_FILES_BASE_URL=/static/uploads

# 管理员用户配置
FIRST_SUPERUSER_EMAIL=<EMAIL>
FIRST_SUPERUSER_USERNAME=admin
FIRST_SUPERUSER_PASSWORD=admin123456

# 开发环境配置
DEBUG=true
ENVIRONMENT=development