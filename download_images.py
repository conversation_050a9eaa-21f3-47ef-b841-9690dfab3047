import os
import requests
import time

# 创建目录
os.makedirs('frontend/public/images', exist_ok=True)

# 图片URL列表 - 使用多个免费图片网站
image_urls = {
    # 已成功下载的图片不再重复下载
    # 'chinese-temple.jpg': 'https://cdn.pixabay.com/photo/2016/10/09/05/48/old-buildings-1725202_1280.jpg',
    # 'modern-space.jpg': 'https://cdn.pixabay.com/photo/2017/03/28/12/11/chairs-2181960_1280.jpg',
    # 'nature.jpg': 'https://cdn.pixabay.com/photo/2015/12/01/20/28/forest-1072828_1280.jpg',
    # 'cosmos.jpg': 'https://cdn.pixabay.com/photo/2016/11/29/05/45/astronomy-1867616_1280.jpg',

    # 使用Unsplash和Pexels的图片
    # 'buddhist-temple.jpg': 'https://images.unsplash.com/photo-1470115636492-6d2b56f9146d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1280&q=80',
    # 'christian-church.jpg': 'https://images.unsplash.com/photo-1438032005730-c779502df39b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1280&q=80',

    # 尝试新的URL
    # 'taoist-shrine.jpg': 'https://images.pexels.com/photos/2846217/pexels-photo-2846217.jpeg?auto=compress&cs=tinysrgb&w=1280',

    # 最后一个图片
    'jewish-memorial.jpg': 'https://images.pexels.com/photos/6647037/pexels-photo-6647037.jpeg?auto=compress&cs=tinysrgb&w=1280',
}

# 下载图片
for filename, url in image_urls.items():
    try:
        print(f"正在下载 {filename}...")
        response = requests.get(url)
        if response.status_code == 200:
            with open(f'frontend/public/images/{filename}', 'wb') as f:
                f.write(response.content)
            print(f"已下载 {filename}")
        else:
            print(f"下载 {filename} 失败，状态码: {response.status_code}")
        # 添加延迟，避免过快请求
        time.sleep(1)
    except Exception as e:
        print(f"下载 {filename} 时出错: {e}")

print("所有图片下载完成！")
