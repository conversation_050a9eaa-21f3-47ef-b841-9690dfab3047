
00:00 +0: loading /Volumes/acasis/memorial/flutter/test/widget_test.dart                                                                                                                               
00:01 +0: loading /Volumes/acasis/memorial/flutter/test/widget_test.dart                                                                                                                               
00:01 +0: Memorial app smoke test                                                                                                                                                                      
00:01 +0: Memorial app smoke test                                                                                                                                                                      
══╡ EXCEPTION CAUGHT BY IMAGE RESOURCE SERVICE ╞════════════════════════════════════════════════════
The following _Exception was thrown resolving an image codec:
Exception: Invalid image data

When the exception was thrown, this was the stack:
#0      _futurize (dart:ui/painting.dart:7977:5)
#1      ImageDescriptor.encoded (dart:ui/painting.dart:7771:12)
#2      instantiateImageCodecWithSize (dart:ui/painting.dart:2558:60)
#3      PaintingBinding.instantiateImageCodecWithSize (package:flutter/src/painting/binding.dart:147:15)
#4      AssetBundleImageProvider._loadAsync (package:flutter/src/painting/image_provider.dart:792:18)
<asynchronous suspension>
#5      MultiFrameImageStreamCompleter._handleCodecReady (package:flutter/src/painting/image_stream.dart:1027:3)
<asynchronous suspension>

Image provider: AssetImage(bundle: null, name: "assets/images/hero_background.jpg")
Image key: AssetBundleImageKey(bundle: PlatformAssetBundle#7e2a2(), name:
  "assets/images/hero_background.jpg", scale: 1.0)
════════════════════════════════════════════════════════════════════════════════════════════════════

00:01 +0 -1: Memorial app smoke test [E]                                                                                                                                                               
  Test failed. See exception logs above.
  The test description was: Memorial app smoke test
  

To run this test again: /opt/homebrew/Caskroom/flutter/3.32.1/flutter/bin/cache/dart-sdk/bin/dart test /Volumes/acasis/memorial/flutter/test/widget_test.dart -p vm --plain-name 'Memorial app smoke test'

00:01 +0 -1: Some tests failed.                                                                                                                                                                        
