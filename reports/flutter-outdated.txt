Showing outdated packages.
[*] indicates versions that are not the latest available.

Package Name                                    Current  Upgradable  Resolvable  Latest    

direct dependencies:                           
file_picker                                     *8.3.7   *8.3.7      10.1.9      10.1.9    
flutter_local_notifications                     *18.0.1  *18.0.1     19.2.1      19.2.1    
go_router                                       *14.8.1  *14.8.1     15.1.3      15.1.3    
permission_handler                              *11.4.0  *11.4.0     12.0.0+1    12.0.0+1  
share_plus                                      *10.1.4  *10.1.4     11.0.0      11.0.0    

dev_dependencies:                              
flutter_launcher_icons                          *0.14.3  0.14.4      0.14.4      0.14.4    
json_serializable                               *6.9.0   *6.9.0      *6.9.0      6.9.5     
mockito                                         *5.4.5   *5.4.5      *5.4.5      5.4.6     

transitive dependencies:                       
flutter_local_notifications_linux               *5.0.0   *5.0.0      6.0.0       6.0.0     
flutter_local_notifications_platform_interface  *8.0.0   *8.0.0      9.0.0       9.0.0     
flutter_local_notifications_windows             -        -           1.0.0       1.0.0     
flutter_secure_storage_linux                    *1.2.3   *1.2.3      *1.2.3      2.0.1     
flutter_secure_storage_macos                    *3.1.3   *3.1.3      *3.1.3      4.0.0     
flutter_secure_storage_platform_interface       *1.1.2   *1.1.2      *1.1.2      2.0.1     
flutter_secure_storage_web                      *1.2.1   *1.2.1      *1.2.1      2.0.0     
flutter_secure_storage_windows                  *3.1.2   *3.1.2      *3.1.2      4.0.0     
js                                              *0.6.7   *0.6.7      *0.6.7      0.7.2     (discontinued)  
material_color_utilities                        *0.11.1  *0.11.1     *0.11.1     0.13.0    
meta                                            *1.16.0  *1.16.0     *1.16.0     1.17.0    
permission_handler_android                      *12.1.0  *12.1.0     13.0.1      13.0.1    
pointycastle                                    *3.9.1   *3.9.1      *3.9.1      4.0.0     
share_plus_platform_interface                   *5.0.2   *5.0.2      6.0.0       6.0.0     
vector_graphics                                 *1.1.18  1.1.19      1.1.19      1.1.19    
vector_math                                     *2.1.4   *2.1.4      *2.1.4      2.2.0     

transitive dev_dependencies:                   
_fe_analyzer_shared                             *76.0.0  *76.0.0     *76.0.0     82.0.0    
analyzer                                        *6.11.0  *6.11.0     *6.11.0     7.4.5     
checked_yaml                                    *2.0.3   2.0.4       2.0.4       2.0.4     
dart_style                                      *2.3.8   *2.3.8      *2.3.8      3.1.0     
leak_tracker                                    *10.0.9  *10.0.9     *10.0.9     11.0.1    
leak_tracker_flutter_testing                    *3.0.9   *3.0.9      *3.0.9      3.0.10    
leak_tracker_testing                            *3.0.1   *3.0.1      *3.0.1      3.0.2     
process                                         *5.0.3   *5.0.3      *5.0.3      5.0.4     
source_gen                                      *1.5.0   *1.5.0      *1.5.0      2.0.0     
test_api                                        *0.7.4   *0.7.4      *0.7.4      0.7.6     
vm_service                                      *15.0.0  *15.0.0     *15.0.0     15.0.2    

3 upgradable dependencies are locked (in pubspec.lock) to older versions.
To update these dependencies, use `flutter pub upgrade`.

9  dependencies are constrained to versions that are older than a resolvable version.
To update these dependencies, edit pubspec.yaml, or run `flutter pub upgrade --major-versions`.

js
    Package js has been discontinued. See https://dart.dev/go/package-discontinue
