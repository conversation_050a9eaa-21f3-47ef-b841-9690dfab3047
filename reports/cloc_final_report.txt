github.com/AlDanial/cloc v 2.02  T=2.09 s (409.7 files/s, 62767.8 lines/s)
--------------------------------------------------------------------------------
Language                      files          blank        comment           code
--------------------------------------------------------------------------------
JavaScript                       11            587            400          42198
Markdown                         60           2178              0          17859
TypeScript                       70           1475           1182          11819
Text                              7              3              0          11782
CSV                               1              0              0          11760
JSON                            556              9              0           6344
HTML                             29            308            255           5993
Python                           62           1273           1418           4987
YAML                              5            769              0           2877
Dart                             19            164            193           2540
CSS                              15            242             25           1589
Bourne Shell                     10             79             71            272
GLSL                              3             12              0            159
TOML                              1              5              2             43
INI                               1             13              0             40
XML                               1              0              0             17
SVG                               3              1              2             16
Bourne Again Shell                1              2              2             12
--------------------------------------------------------------------------------
SUM:                            855           7120           3550         120307
--------------------------------------------------------------------------------
