test_app.py:9: error: Module "app" has no attribute "create_app"  [attr-defined]
debug_app.py:10: error: Module "app" has no attribute "create_app"  [attr-defined]
app/models_sqlalchemy.py:128: error: Need type annotation for "deceased_gender"  [var-annotated]
app/models_sqlalchemy.py:141: error: Need type annotation for "privacy_level"  [var-annotated]
app/models_sqlalchemy.py:187: error: Need type annotation for "asset_type"  [var-annotated]
app/models_sqlalchemy.py:285: error: Need type annotation for "privacy_level"  [var-annotated]
app/models_sqlalchemy.py:495: error: Need type annotation for "product_type"  [var-annotated]
app/models_sqlalchemy.py:527: error: Need type annotation for "status"  [var-annotated]
app/models/user.py:62: error: Need type annotation for "deceased_gender"  [var-annotated]
check_users.py:6: error: Module "app" has no attribute "create_app"  [attr-defined]
migrate_db.py:7: error: Module "app" has no attribute "create_app"  [attr-defined]
init_db.py:3: error: Module "app" has no attribute "create_app"  [attr-defined]
gevent_patch.py:4: error: Skipping analyzing "gevent": module is installed, but missing library stubs or py.typed marker  [import]
gevent_patch.py:6: error: Module "app" has no attribute "create_app"  [attr-defined]
app/ai_services/replicate_service.py:14: error: Cannot find implementation or library stub for module named "flask"  [import]
app/ai_services/replicate_service.py:14: note: See https://mypy.readthedocs.io/en/stable/running_mypy.html#missing-imports
app/core/config.py:13: error: Extra keys ("case_sensitive", "env_file", "env_file_encoding") for TypedDict "ConfigDict"  [typeddict-unknown-key]
app/core/config.py:13: error: Incompatible types in assignment (expression has type "ConfigDict", base class "BaseSettings" defined the type as "SettingsConfigDict")  [assignment]
app/db/session.py:6: error: Argument 1 to "create_engine" has incompatible type "str | None"; expected "str | URL"  [arg-type]
app/core/security.py:84: error: Incompatible types in assignment (expression has type "Any | None", variable has type "str")  [assignment]
app/core/security.py:85: error: Incompatible types in assignment (expression has type "Any | None", variable has type "str")  [assignment]
app/core/security.py:91: error: Module has no attribute "JWTError"  [attr-defined]
app/core/security.py:121: error: Incompatible types in assignment (expression has type "Any | None", variable has type "str")  [assignment]
app/core/security.py:122: error: Incompatible types in assignment (expression has type "Any | None", variable has type "str")  [assignment]
app/core/security.py:128: error: Module has no attribute "JWTError"  [attr-defined]
app/crud.py:27: error: "type[ModelType]" has no attribute "id"  [attr-defined]
app/crud.py:43: error: "type[ModelType]" has no attribute "creator_id"  [attr-defined]
app/crud.py:52: error: "type[ModelType]" has no attribute "owner_id"  [attr-defined]
app/crud.py:226: error: Incompatible types in assignment (expression has type "str | None", variable has type "Column[str]")  [assignment]
app/crud_family.py:48: error: "type[FamilyMember]" has no attribute "status"  [attr-defined]
app/crud_family.py:89: error: Incompatible return value type (got "Literal[False] | ColumnElement[bool] | Any", expected "bool")  [return-value]
app/crud_family.py:89: error: "FamilyMember" has no attribute "status"  [attr-defined]
app/crud_family.py:96: error: "FamilyMember" has no attribute "status"  [attr-defined]
app/crud_family.py:201: error: "type[GenealogyRelationship]" has no attribute "parent_node_id"  [attr-defined]
app/crud_family.py:202: error: "type[GenealogyRelationship]" has no attribute "child_node_id"  [attr-defined]
app/db/init_db.py:36: error: Missing named argument "phone" for "UserCreate"  [call-arg]
app/api_v1_routers/users.py:19: error: Missing named argument "phone" for "UserResponse"  [call-arg]
app/api_v1_routers/users.py:20: error: Argument "id" to "UserResponse" has incompatible type "Column[UUID]"; expected "UUID"  [arg-type]
app/api_v1_routers/users.py:21: error: Argument "username" to "UserResponse" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/users.py:22: error: Argument "email" to "UserResponse" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/users.py:23: error: Argument "full_name" to "UserResponse" has incompatible type "Column[str]"; expected "str | None"  [arg-type]
app/api_v1_routers/users.py:24: error: Argument "avatar_url" to "UserResponse" has incompatible type "Column[str]"; expected "HttpUrl | None"  [arg-type]
app/api_v1_routers/users.py:25: error: Argument "bio" to "UserResponse" has incompatible type "Column[str]"; expected "str | None"  [arg-type]
app/api_v1_routers/users.py:26: error: Argument "is_active" to "UserResponse" has incompatible type "Column[bool]"; expected "bool"  [arg-type]
app/api_v1_routers/users.py:27: error: Argument "is_verified" to "UserResponse" has incompatible type "Column[bool]"; expected "bool"  [arg-type]
app/api_v1_routers/users.py:28: error: Argument "created_at" to "UserResponse" has incompatible type "Column[datetime]"; expected "datetime"  [arg-type]
app/api_v1_routers/users.py:29: error: Argument "updated_at" to "UserResponse" has incompatible type "Column[datetime]"; expected "datetime"  [arg-type]
app/api_v1_routers/users.py:30: error: Argument "last_login_at" to "UserResponse" has incompatible type "Column[datetime]"; expected "datetime | None"  [arg-type]
app/api_v1_routers/users.py:31: error: Argument "role" to "UserResponse" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/users.py:54: error: Missing named argument "phone" for "UserResponse"  [call-arg]
app/api_v1_routers/users.py:55: error: Argument "id" to "UserResponse" has incompatible type "Column[UUID]"; expected "UUID"  [arg-type]
app/api_v1_routers/users.py:56: error: Argument "username" to "UserResponse" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/users.py:57: error: Argument "email" to "UserResponse" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/users.py:58: error: Argument "full_name" to "UserResponse" has incompatible type "Column[str]"; expected "str | None"  [arg-type]
app/api_v1_routers/users.py:59: error: Argument "avatar_url" to "UserResponse" has incompatible type "Column[str]"; expected "HttpUrl | None"  [arg-type]
app/api_v1_routers/users.py:60: error: Argument "bio" to "UserResponse" has incompatible type "Column[str]"; expected "str | None"  [arg-type]
app/api_v1_routers/users.py:61: error: Argument "is_active" to "UserResponse" has incompatible type "Column[bool]"; expected "bool"  [arg-type]
app/api_v1_routers/users.py:62: error: Argument "is_verified" to "UserResponse" has incompatible type "Column[bool]"; expected "bool"  [arg-type]
app/api_v1_routers/users.py:63: error: Argument "created_at" to "UserResponse" has incompatible type "Column[datetime]"; expected "datetime"  [arg-type]
app/api_v1_routers/users.py:64: error: Argument "updated_at" to "UserResponse" has incompatible type "Column[datetime]"; expected "datetime"  [arg-type]
app/api_v1_routers/users.py:65: error: Argument "last_login_at" to "UserResponse" has incompatible type "Column[datetime]"; expected "datetime | None"  [arg-type]
app/api_v1_routers/users.py:66: error: Argument "role" to "UserResponse" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/users.py:80: error: Module has no attribute "get_memorial_spaces_by_creator"  [attr-defined]
app/api_v1_routers/users.py:84: error: Module has no attribute "count_memorial_spaces_by_creator"  [attr-defined]
app/api_v1_routers/users.py:86: error: Unexpected keyword argument "items" for "MemorialSpaceListResponse"  [call-arg]
app/api_v1_routers/users.py:86: error: Unexpected keyword argument "total" for "MemorialSpaceListResponse"  [call-arg]
app/api_v1_routers/users.py:86: error: Unexpected keyword argument "skip" for "MemorialSpaceListResponse"  [call-arg]
app/api_v1_routers/users.py:86: error: Unexpected keyword argument "limit" for "MemorialSpaceListResponse"  [call-arg]
app/api_v1_routers/users.py:88: error: Unexpected keyword argument "biography" for "MemorialSpaceResponse"  [call-arg]
app/api_v1_routers/tributes.py:25: error: Module has no attribute "get_memorial_space"; maybe "memorial_space"?  [attr-defined]
app/api_v1_routers/tributes.py:32: error: Module has no attribute "can_access_memorial_space"  [attr-defined]
app/api_v1_routers/tributes.py:41: error: Module has no attribute "create_tribute"  [attr-defined]
app/api_v1_routers/tributes.py:45: error: Unexpected keyword argument "space_id" for "TributeResponse"  [call-arg]
app/api_v1_routers/tributes.py:52: error: Argument "user_name" to "TributeResponse" has incompatible type "Column[str]"; expected "str | None"  [arg-type]
app/api_v1_routers/tributes.py:68: error: Module has no attribute "get_memorial_space"; maybe "memorial_space"?  [attr-defined]
app/api_v1_routers/tributes.py:75: error: Module has no attribute "can_access_memorial_space"  [attr-defined]
app/api_v1_routers/tributes.py:84: error: Module has no attribute "get_tributes_by_space"  [attr-defined]
app/api_v1_routers/tributes.py:88: error: Module has no attribute "count_tributes_by_space"  [attr-defined]
app/api_v1_routers/tributes.py:92: error: Unexpected keyword argument "space_id" for "TributeResponse"  [call-arg]
app/api_v1_routers/tributes.py:119: error: Module has no attribute "get_memorial_space"; maybe "memorial_space"?  [attr-defined]
app/api_v1_routers/tributes.py:126: error: Module has no attribute "can_access_memorial_space"  [attr-defined]
app/api_v1_routers/tributes.py:135: error: Module has no attribute "get_tribute_stats"  [attr-defined]
app/api_v1_routers/tributes.py:137: error: Unexpected keyword argument "recent_tributes_count" for "TributeStatsResponse"; did you mean "recent_tributes"?  [call-arg]
app/api_v1_routers/tributes.py:137: error: Unexpected keyword argument "unique_visitors" for "TributeStatsResponse"  [call-arg]
app/api_v1_routers/messages.py:25: error: Module has no attribute "get_memorial_space"; maybe "memorial_space"?  [attr-defined]
app/api_v1_routers/messages.py:32: error: Module has no attribute "can_access_memorial_space"  [attr-defined]
app/api_v1_routers/messages.py:41: error: Module has no attribute "create_message"  [attr-defined]
app/api_v1_routers/messages.py:45: error: Module has no attribute "MessageResponse"  [attr-defined]
app/api_v1_routers/messages.py:72: error: Module has no attribute "get_memorial_space"; maybe "memorial_space"?  [attr-defined]
app/api_v1_routers/messages.py:79: error: Module has no attribute "can_access_memorial_space"  [attr-defined]
app/api_v1_routers/messages.py:88: error: Module has no attribute "get_messages_by_space"  [attr-defined]
app/api_v1_routers/messages.py:92: error: Module has no attribute "count_messages_by_space"  [attr-defined]
app/api_v1_routers/messages.py:94: error: Module has no attribute "MessageListResponse"  [attr-defined]
app/api_v1_routers/messages.py:96: error: Module has no attribute "MessageResponse"  [attr-defined]
app/api_v1_routers/messages.py:131: error: Module has no attribute "get_message"  [attr-defined]
app/api_v1_routers/messages.py:152: error: Module has no attribute "update_message"  [attr-defined]
app/api_v1_routers/messages.py:156: error: Module has no attribute "MessageResponse"  [attr-defined]
app/api_v1_routers/messages.py:184: error: Module has no attribute "get_message"  [attr-defined]
app/api_v1_routers/messages.py:198: error: Module has no attribute "get_memorial_space"; maybe "memorial_space"?  [attr-defined]
app/api_v1_routers/messages.py:215: error: Module has no attribute "delete_message"  [attr-defined]
app/api_v1_routers/memorial_spaces.py:46: error: Unexpected keyword argument "relationship" for "MemorialSpaceCreate"  [call-arg]
app/api_v1_routers/memorial_spaces.py:50: error: Argument "birth_date" to "MemorialSpaceCreate" has incompatible type "str | None"; expected "date | None"  [arg-type]
app/api_v1_routers/memorial_spaces.py:52: error: Argument "death_date" to "MemorialSpaceCreate" has incompatible type "str | None"; expected "date | None"  [arg-type]
app/api_v1_routers/memorial_events.py:49: error: "MemorialEventResponse" has no attribute "year"  [attr-defined]
app/api_v1_routers/memorial_events.py:93: error: "MemorialEventResponse" has no attribute "year"  [attr-defined]
app/api_v1_routers/memorial_events.py:97: error: Unexpected keyword argument "items" for "MemorialEventListResponse"  [call-arg]
app/api_v1_routers/memorial_events.py:97: error: Unexpected keyword argument "total" for "MemorialEventListResponse"  [call-arg]
app/api_v1_routers/memorial_events.py:97: error: Unexpected keyword argument "page" for "MemorialEventListResponse"  [call-arg]
app/api_v1_routers/memorial_events.py:97: error: Unexpected keyword argument "size" for "MemorialEventListResponse"  [call-arg]
app/api_v1_routers/memorial_assets.py:53: error: Argument 1 to "Path" has incompatible type "str | None"; expected "str | PathLike[str]"  [arg-type]
app/api_v1_routers/memorial_assets.py:77: error: Missing named argument "original_filename" for "MemorialAssetCreate"  [call-arg]
app/api_v1_routers/memorial_assets.py:77: error: Missing named argument "file_size" for "MemorialAssetCreate"  [call-arg]
app/api_v1_routers/memorial_assets.py:77: error: Missing named argument "asset_metadata" for "MemorialAssetCreate"  [call-arg]
app/api_v1_routers/memorial_assets.py:77: error: Missing named argument "is_ai_enhanced" for "MemorialAssetCreate"  [call-arg]
app/api_v1_routers/memorial_assets.py:77: error: Missing named argument "file_url" for "MemorialAssetCreate"  [call-arg]
app/api_v1_routers/memorial_assets.py:159: error: Unexpected keyword argument "items" for "MemorialAssetListResponse"  [call-arg]
app/api_v1_routers/memorial_assets.py:159: error: Unexpected keyword argument "total" for "MemorialAssetListResponse"  [call-arg]
app/api_v1_routers/memorial_assets.py:159: error: Unexpected keyword argument "page" for "MemorialAssetListResponse"  [call-arg]
app/api_v1_routers/memorial_assets.py:159: error: Unexpected keyword argument "size" for "MemorialAssetListResponse"  [call-arg]
app/api_v1_routers/families.py:24: error: Module has no attribute "create_family"  [attr-defined]
app/api_v1_routers/families.py:40: error: Argument "creator_name" to "FamilyResponse" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/families.py:54: error: Module has no attribute "get_family"  [attr-defined]
app/api_v1_routers/families.py:61: error: Module has no attribute "is_family_member"  [attr-defined]
app/api_v1_routers/families.py:69: error: Module has no attribute "count_family_members"  [attr-defined]
app/api_v1_routers/families.py:98: error: Module has no attribute "get_family"  [attr-defined]
app/api_v1_routers/families.py:105: error: Module has no attribute "can_manage_family"  [attr-defined]
app/api_v1_routers/families.py:112: error: Module has no attribute "update_family"  [attr-defined]
app/api_v1_routers/families.py:117: error: Module has no attribute "count_family_members"  [attr-defined]
app/api_v1_routers/families.py:149: error: Module has no attribute "get_family"  [attr-defined]
app/api_v1_routers/families.py:156: error: Module has no attribute "can_invite_to_family"  [attr-defined]
app/api_v1_routers/families.py:165: error: Module has no attribute "create_family_invitation"  [attr-defined]
app/api_v1_routers/families.py:182: error: Argument "inviter_name" to "FamilyInvitationResponse" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/families.py:197: error: Module has no attribute "get_family"  [attr-defined]
app/api_v1_routers/families.py:204: error: Module has no attribute "is_family_member"  [attr-defined]
app/api_v1_routers/families.py:213: error: Module has no attribute "validate_invitation_code"  [attr-defined]
app/api_v1_routers/families.py:230: error: Module has no attribute "add_family_member"  [attr-defined]
app/api_v1_routers/families.py:243: error: Argument "user_name" to "FamilyMemberResponse" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/families.py:244: error: Argument "user_avatar" to "FamilyMemberResponse" has incompatible type "Column[str]"; expected "str | None"  [arg-type]
app/api_v1_routers/families.py:260: error: Module has no attribute "get_family"  [attr-defined]
app/api_v1_routers/families.py:267: error: Module has no attribute "can_view_family_members"  [attr-defined]
app/api_v1_routers/families.py:276: error: Module has no attribute "get_family_members"  [attr-defined]
app/api_v1_routers/families.py:280: error: Module has no attribute "count_family_members"  [attr-defined]
app/api_v1_routers/families.py:315: error: Module has no attribute "get_family"  [attr-defined]
app/api_v1_routers/families.py:321: error: Module has no attribute "can_edit_genealogy"  [attr-defined]
app/api_v1_routers/families.py:328: error: Module has no attribute "create_genealogy_node"  [attr-defined]
app/api_v1_routers/families.py:359: error: Module has no attribute "get_family"  [attr-defined]
app/api_v1_routers/families.py:365: error: Module has no attribute "can_view_genealogy"  [attr-defined]
app/api_v1_routers/families.py:372: error: Module has no attribute "get_genealogy_nodes"  [attr-defined]
app/api_v1_routers/families.py:373: error: Module has no attribute "get_genealogy_relationships"  [attr-defined]
app/api_v1_routers/auth.py:98: error: Argument 1 to "create_email_verification_token" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/auth.py:100: error: Argument "to_email" to "send_verification_email" of "EmailService" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/auth.py:102: error: Argument "username" to "send_verification_email" of "EmailService" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/auth.py:108: error: Incompatible types in assignment (expression has type "bool", variable has type "Column[bool]")  [assignment]
app/api_v1_routers/auth.py:179: error: Argument 1 to "create_password_reset_token" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/auth.py:183: error: Argument "to_email" to "send_password_reset_email" of "EmailService" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/auth.py:183: error: Argument "username" to "send_password_reset_email" of "EmailService" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/auth.py:213: error: Incompatible types in assignment (expression has type "str", variable has type "Column[str]")  [assignment]
app/api_v1_routers/auth.py:241: error: Incompatible types in assignment (expression has type "bool", variable has type "Column[bool]")  [assignment]
app/api_v1_routers/auth.py:268: error: Argument 1 to "create_email_verification_token" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/auth.py:272: error: Argument "to_email" to "send_verification_email" of "EmailService" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/api_v1_routers/auth.py:274: error: Argument "username" to "send_verification_email" of "EmailService" has incompatible type "Column[str]"; expected "str"  [arg-type]
app/main.py:25: error: Unexpected keyword argument "allow_origins" for "add_middleware" of "Starlette"  [call-arg]
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.11/site-packages/starlette/applications.py:123: note: "add_middleware" of "Starlette" defined here
app/main.py:25: error: Unexpected keyword argument "allow_credentials" for "add_middleware" of "Starlette"  [call-arg]
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.11/site-packages/starlette/applications.py:123: note: "add_middleware" of "Starlette" defined here
app/main.py:25: error: Unexpected keyword argument "allow_methods" for "add_middleware" of "Starlette"  [call-arg]
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.11/site-packages/starlette/applications.py:123: note: "add_middleware" of "Starlette" defined here
app/main.py:25: error: Unexpected keyword argument "allow_headers" for "add_middleware" of "Starlette"  [call-arg]
/Volumes/acasis/miniconda3/miniconda3/envs/memorial/lib/python3.11/site-packages/starlette/applications.py:123: note: "add_middleware" of "Starlette" defined here
app/main.py:26: error: Argument 1 to "add_middleware" of "Starlette" has incompatible type "type[CORSMiddleware]"; expected "_MiddlewareFactory[[MutableMapping[str, Any], Callable[[], Awaitable[MutableMapping[str, Any]]], Callable[[MutableMapping[str, Any]], Awaitable[None]]]]"  [arg-type]
app/main.py:26: note: Following member(s) of "CORSMiddleware" have conflicts:
app/main.py:26: note:     Expected:
app/main.py:26: note:         def __call__(Callable[[MutableMapping[str, Any], Callable[[], Awaitable[MutableMapping[str, Any]]], Callable[[MutableMapping[str, Any]], Awaitable[None]]], Awaitable[None]], /, scope: MutableMapping[str, Any], receive: Callable[[], Awaitable[MutableMapping[str, Any]]], send: Callable[[MutableMapping[str, Any]], Awaitable[None]]) -> Callable[[MutableMapping[str, Any], Callable[[], Awaitable[MutableMapping[str, Any]]], Callable[[MutableMapping[str, Any]], Awaitable[None]]], Awaitable[None]]
app/main.py:26: note:     Got:
app/main.py:26: note:         def __init__(app: Callable[[MutableMapping[str, Any], Callable[[], Awaitable[MutableMapping[str, Any]]], Callable[[MutableMapping[str, Any]], Awaitable[None]]], Awaitable[None]], allow_origins: Sequence[str] = ..., allow_methods: Sequence[str] = ..., allow_headers: Sequence[str] = ..., allow_credentials: bool = ..., allow_origin_regex: str | None = ..., expose_headers: Sequence[str] = ..., max_age: int = ...) -> CORSMiddleware
Found 165 errors in 24 files (checked 56 source files)
