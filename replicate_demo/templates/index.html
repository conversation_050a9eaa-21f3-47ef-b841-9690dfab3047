<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>祭祀网站 AI 功能演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">祭祀网站 AI 功能演示</h1>
        <p class="text-center mb-5">使用 Replicate.com API 实现老照片修复、上色、3D 效果和声音克隆</p>
        
        <div class="row">
            <div class="col-md-3">
                <div class="list-group">
                    <a href="#photo-restore" class="list-group-item list-group-item-action active" data-bs-toggle="list">照片修复</a>
                    <a href="#photo-colorize" class="list-group-item list-group-item-action" data-bs-toggle="list">照片上色</a>
                    <a href="#photo-to-3d" class="list-group-item list-group-item-action" data-bs-toggle="list">照片 3D 效果</a>
                    <a href="#create-3d-model" class="list-group-item list-group-item-action" data-bs-toggle="list">创建 3D 模型</a>
                    <a href="#clone-voice" class="list-group-item list-group-item-action" data-bs-toggle="list">声音克隆</a>
                </div>
            </div>
            
            <div class="col-md-9">
                <div class="tab-content">
                    <!-- 照片修复 -->
                    <div class="tab-pane fade show active" id="photo-restore">
                        <div class="card">
                            <div class="card-header">
                                <h5>照片修复</h5>
                            </div>
                            <div class="card-body">
                                <p>上传老照片，AI 将自动修复和增强照片质量。</p>
                                <form id="photo-restore-form" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="photo-restore-input" class="form-label">选择照片</label>
                                        <input class="form-control" type="file" id="photo-restore-input" name="photo" accept="image/*">
                                    </div>
                                    <button type="submit" class="btn btn-primary" id="photo-restore-btn">开始修复</button>
                                </form>
                                <div class="mt-4 d-none" id="photo-restore-result">
                                    <h6>处理结果</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p>原始照片</p>
                                            <img id="photo-restore-original" class="img-fluid" src="">
                                        </div>
                                        <div class="col-md-6">
                                            <p>修复后</p>
                                            <img id="photo-restore-processed" class="img-fluid" src="">
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3 d-none" id="photo-restore-error">
                                    <div class="alert alert-danger"></div>
                                </div>
                                <div class="mt-3 d-none" id="photo-restore-loading">
                                    <div class="d-flex justify-content-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                    <p class="text-center mt-2">处理中，请稍候...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 照片上色 -->
                    <div class="tab-pane fade" id="photo-colorize">
                        <div class="card">
                            <div class="card-header">
                                <h5>照片上色</h5>
                            </div>
                            <div class="card-body">
                                <p>上传黑白照片，AI 将自动为照片上色。</p>
                                <form id="photo-colorize-form" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="photo-colorize-input" class="form-label">选择照片</label>
                                        <input class="form-control" type="file" id="photo-colorize-input" name="photo" accept="image/*">
                                    </div>
                                    <button type="submit" class="btn btn-primary" id="photo-colorize-btn">开始上色</button>
                                </form>
                                <div class="mt-4 d-none" id="photo-colorize-result">
                                    <h6>处理结果</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p>原始照片</p>
                                            <img id="photo-colorize-original" class="img-fluid" src="">
                                        </div>
                                        <div class="col-md-6">
                                            <p>上色后</p>
                                            <img id="photo-colorize-processed" class="img-fluid" src="">
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3 d-none" id="photo-colorize-error">
                                    <div class="alert alert-danger"></div>
                                </div>
                                <div class="mt-3 d-none" id="photo-colorize-loading">
                                    <div class="d-flex justify-content-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                    <p class="text-center mt-2">处理中，请稍候...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 照片 3D 效果 -->
                    <div class="tab-pane fade" id="photo-to-3d">
                        <div class="card">
                            <div class="card-header">
                                <h5>照片 3D 效果</h5>
                            </div>
                            <div class="card-body">
                                <p>上传照片，AI 将创建 3D 视差效果。</p>
                                <form id="photo-to-3d-form" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="photo-to-3d-input" class="form-label">选择照片</label>
                                        <input class="form-control" type="file" id="photo-to-3d-input" name="photo" accept="image/*">
                                    </div>
                                    <button type="submit" class="btn btn-primary" id="photo-to-3d-btn">创建 3D 效果</button>
                                </form>
                                <div class="mt-4 d-none" id="photo-to-3d-result">
                                    <h6>处理结果</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p>原始照片</p>
                                            <img id="photo-to-3d-original" class="img-fluid" src="">
                                        </div>
                                        <div class="col-md-6">
                                            <p>3D 效果</p>
                                            <video id="photo-to-3d-processed" class="img-fluid" controls autoplay loop></video>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3 d-none" id="photo-to-3d-error">
                                    <div class="alert alert-danger"></div>
                                </div>
                                <div class="mt-3 d-none" id="photo-to-3d-loading">
                                    <div class="d-flex justify-content-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                    <p class="text-center mt-2">处理中，请稍候...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 创建 3D 模型 -->
                    <div class="tab-pane fade" id="create-3d-model">
                        <div class="card">
                            <div class="card-header">
                                <h5>创建 3D 模型</h5>
                            </div>
                            <div class="card-body">
                                <p>上传照片，AI 将创建 3D 模型。</p>
                                <form id="create-3d-model-form" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="create-3d-model-input" class="form-label">选择照片</label>
                                        <input class="form-control" type="file" id="create-3d-model-input" name="photo" accept="image/*">
                                    </div>
                                    <div class="mb-3">
                                        <label for="create-3d-model-description" class="form-label">描述（可选）</label>
                                        <textarea class="form-control" id="create-3d-model-description" name="description" rows="2"></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary" id="create-3d-model-btn">创建 3D 模型</button>
                                </form>
                                <div class="mt-4 d-none" id="create-3d-model-result">
                                    <h6>处理结果</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p>原始照片</p>
                                            <img id="create-3d-model-original" class="img-fluid" src="">
                                        </div>
                                        <div class="col-md-6">
                                            <p>3D 模型</p>
                                            <div id="create-3d-model-processed-container">
                                                <a id="create-3d-model-download" href="" target="_blank" class="btn btn-success">下载 3D 模型</a>
                                                <p class="mt-2">模型预览：</p>
                                                <img id="create-3d-model-preview" class="img-fluid mt-2" src="">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3 d-none" id="create-3d-model-error">
                                    <div class="alert alert-danger"></div>
                                </div>
                                <div class="mt-3 d-none" id="create-3d-model-loading">
                                    <div class="d-flex justify-content-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                    <p class="text-center mt-2">处理中，请稍候...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 声音克隆 -->
                    <div class="tab-pane fade" id="clone-voice">
                        <div class="card">
                            <div class="card-header">
                                <h5>声音克隆</h5>
                            </div>
                            <div class="card-body">
                                <p>上传声音样本，AI 将克隆声音并生成新的语音。</p>
                                <form id="clone-voice-form" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="clone-voice-input" class="form-label">选择声音样本（MP3/WAV）</label>
                                        <input class="form-control" type="file" id="clone-voice-input" name="voice" accept="audio/*">
                                    </div>
                                    <div class="mb-3">
                                        <label for="clone-voice-text" class="form-label">要说的文字</label>
                                        <textarea class="form-control" id="clone-voice-text" name="text" rows="3" placeholder="输入要用克隆声音说的文字..."></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary" id="clone-voice-btn">生成语音</button>
                                </form>
                                <div class="mt-4 d-none" id="clone-voice-result">
                                    <h6>处理结果</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p>原始声音样本</p>
                                            <audio id="clone-voice-original" controls></audio>
                                        </div>
                                        <div class="col-md-6">
                                            <p>克隆后的语音</p>
                                            <audio id="clone-voice-processed" controls></audio>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3 d-none" id="clone-voice-error">
                                    <div class="alert alert-danger"></div>
                                </div>
                                <div class="mt-3 d-none" id="clone-voice-loading">
                                    <div class="d-flex justify-content-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                    <p class="text-center mt-2">处理中，请稍候...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/main.js"></script>
</body>
</html>
