import os
import replicate
import requests
from flask import Flask, request, jsonify, render_template, send_from_directory
from werkzeug.utils import secure_filename
import uuid
import json
from io import BytesIO
from PIL import Image
import base64

app = Flask(__name__, static_folder='static')
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'results'), exist_ok=True)

# 设置 Replicate API 令牌
os.environ["REPLICATE_API_TOKEN"] = "****************************************"

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/uploads/<path:filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/api/photo-restore', methods=['POST'])
def photo_restore():
    if 'photo' not in request.files:
        return jsonify({"error": "No photo provided"}), 400
    
    photo = request.files['photo']
    if photo.filename == '':
        return jsonify({"error": "No photo selected"}), 400
    
    # 保存上传的照片
    filename = secure_filename(f"{uuid.uuid4()}_{photo.filename}")
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    photo.save(file_path)
    
    try:
        # 调用 Replicate API 进行照片修复
        output = replicate.run(
            "sczhou/codeformer:7de2ea26c616d5bf2245ad0d5e24f0ff9a6204578a5c876db53142edd9d2cd56",
            input={
                "image": open(file_path, "rb"),
                "upscale": 2,
                "face_upsample": True,
                "background_enhance": True,
                "codeformer_fidelity": 0.7
            }
        )
        
        # 下载处理后的图像
        response = requests.get(output)
        if response.status_code == 200:
            result_filename = f"restored_{filename}"
            result_path = os.path.join(app.config['UPLOAD_FOLDER'], 'results', result_filename)
            with open(result_path, 'wb') as f:
                f.write(response.content)
            
            return jsonify({
                "original": f"/uploads/{filename}",
                "result": f"/uploads/results/{result_filename}"
            })
        else:
            return jsonify({"error": "Failed to download result"}), 500
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/photo-colorize', methods=['POST'])
def photo_colorize():
    if 'photo' not in request.files:
        return jsonify({"error": "No photo provided"}), 400
    
    photo = request.files['photo']
    if photo.filename == '':
        return jsonify({"error": "No photo selected"}), 400
    
    # 保存上传的照片
    filename = secure_filename(f"{uuid.uuid4()}_{photo.filename}")
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    photo.save(file_path)
    
    try:
        # 调用 Replicate API 进行照片上色
        output = replicate.run(
            "madhurjindal/de-oldify-image-colorization:9d9b8d0f19f4f179a7ed0a271ce73f0f618d779c4e1c41a5da9ff2d4c3e41b89",
            input={"image": open(file_path, "rb")}
        )
        
        # 下载处理后的图像
        response = requests.get(output)
        if response.status_code == 200:
            result_filename = f"colorized_{filename}"
            result_path = os.path.join(app.config['UPLOAD_FOLDER'], 'results', result_filename)
            with open(result_path, 'wb') as f:
                f.write(response.content)
            
            return jsonify({
                "original": f"/uploads/{filename}",
                "result": f"/uploads/results/{result_filename}"
            })
        else:
            return jsonify({"error": "Failed to download result"}), 500
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/photo-to-3d', methods=['POST'])
def photo_to_3d():
    if 'photo' not in request.files:
        return jsonify({"error": "No photo provided"}), 400
    
    photo = request.files['photo']
    if photo.filename == '':
        return jsonify({"error": "No photo selected"}), 400
    
    # 保存上传的照片
    filename = secure_filename(f"{uuid.uuid4()}_{photo.filename}")
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    photo.save(file_path)
    
    try:
        # 调用 Replicate API 生成 3D 效果
        output = replicate.run(
            "varunagrawal/3d-photo-inpainting:f0c49a6b824235c45f19fb7c9d0cd7d4c1ddbca3b9483a5771b38500ded8163c",
            input={"image": open(file_path, "rb")}
        )
        
        return jsonify({
            "original": f"/uploads/{filename}",
            "result": output
        })
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/create-3d-model', methods=['POST'])
def create_3d_model():
    if 'photo' not in request.files:
        return jsonify({"error": "No photo provided"}), 400
    
    photo = request.files['photo']
    description = request.form.get('description', '')
    
    if photo.filename == '':
        return jsonify({"error": "No photo selected"}), 400
    
    # 保存上传的照片
    filename = secure_filename(f"{uuid.uuid4()}_{photo.filename}")
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    photo.save(file_path)
    
    try:
        # 调用 Replicate API 创建 3D 模型
        output = replicate.run(
            "cjwbw/instantmesh:d0de4bd38b0d0f1217a6a2ef9d0cb8d22d117f6a8c8f2e1bb75e39f0f5d8e91d",
            input={
                "image": open(file_path, "rb"),
                "task_type": "human",
                "mesh_type": "3d"
            }
        )
        
        return jsonify({
            "original": f"/uploads/{filename}",
            "result": output
        })
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/clone-voice', methods=['POST'])
def clone_voice():
    if 'voice' not in request.files:
        return jsonify({"error": "No voice sample provided"}), 400
    
    voice = request.files['voice']
    text = request.form.get('text', '')
    
    if voice.filename == '' or not text:
        return jsonify({"error": "Voice sample or text missing"}), 400
    
    # 保存上传的声音样本
    filename = secure_filename(f"{uuid.uuid4()}_{voice.filename}")
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    voice.save(file_path)
    
    try:
        # 调用 Replicate API 克隆声音
        output = replicate.run(
            "lucataco/xtts:6be4a3f4e47e4ab877354ef6cb5bf5f6d0e175d9a65fb18d7bce73c095e98dbf",
            input={
                "text": text,
                "voice_sample": open(file_path, "rb"),
                "language": "zh"
            }
        )
        
        return jsonify({
            "result": output
        })
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True)
