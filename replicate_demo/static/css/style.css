body {
    background-color: #f8f9fa;
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #f1f8ff;
}

.img-fluid {
    max-height: 300px;
    width: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
}

audio, video {
    width: 100%;
    margin-top: 10px;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

.tab-content {
    padding: 10px;
}

.list-group-item.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .col-md-3, .col-md-9 {
        margin-bottom: 20px;
    }
    
    .list-group {
        margin-bottom: 20px;
    }
}
