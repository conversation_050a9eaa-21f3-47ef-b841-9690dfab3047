document.addEventListener('DOMContentLoaded', function() {
    // 照片修复
    const photoRestoreForm = document.getElementById('photo-restore-form');
    const photoRestoreBtn = document.getElementById('photo-restore-btn');
    const photoRestoreResult = document.getElementById('photo-restore-result');
    const photoRestoreOriginal = document.getElementById('photo-restore-original');
    const photoRestoreProcessed = document.getElementById('photo-restore-processed');
    const photoRestoreError = document.getElementById('photo-restore-error');
    const photoRestoreLoading = document.getElementById('photo-restore-loading');
    
    photoRestoreForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(photoRestoreForm);
        
        // 显示加载状态
        photoRestoreBtn.disabled = true;
        photoRestoreResult.classList.add('d-none');
        photoRestoreError.classList.add('d-none');
        photoRestoreLoading.classList.remove('d-none');
        
        fetch('/api/photo-restore', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            
            // 显示结果
            photoRestoreOriginal.src = data.original;
            photoRestoreProcessed.src = data.result;
            photoRestoreResult.classList.remove('d-none');
        })
        .catch(error => {
            // 显示错误
            photoRestoreError.querySelector('.alert').textContent = error.message || '处理失败，请重试';
            photoRestoreError.classList.remove('d-none');
        })
        .finally(() => {
            // 隐藏加载状态
            photoRestoreLoading.classList.add('d-none');
            photoRestoreBtn.disabled = false;
        });
    });
    
    // 照片上色
    const photoColorizeForm = document.getElementById('photo-colorize-form');
    const photoColorizeBtn = document.getElementById('photo-colorize-btn');
    const photoColorizeResult = document.getElementById('photo-colorize-result');
    const photoColorizeOriginal = document.getElementById('photo-colorize-original');
    const photoColorizeProcessed = document.getElementById('photo-colorize-processed');
    const photoColorizeError = document.getElementById('photo-colorize-error');
    const photoColorizeLoading = document.getElementById('photo-colorize-loading');
    
    photoColorizeForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(photoColorizeForm);
        
        // 显示加载状态
        photoColorizeBtn.disabled = true;
        photoColorizeResult.classList.add('d-none');
        photoColorizeError.classList.add('d-none');
        photoColorizeLoading.classList.remove('d-none');
        
        fetch('/api/photo-colorize', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            
            // 显示结果
            photoColorizeOriginal.src = data.original;
            photoColorizeProcessed.src = data.result;
            photoColorizeResult.classList.remove('d-none');
        })
        .catch(error => {
            // 显示错误
            photoColorizeError.querySelector('.alert').textContent = error.message || '处理失败，请重试';
            photoColorizeError.classList.remove('d-none');
        })
        .finally(() => {
            // 隐藏加载状态
            photoColorizeLoading.classList.add('d-none');
            photoColorizeBtn.disabled = false;
        });
    });
    
    // 照片 3D 效果
    const photoTo3dForm = document.getElementById('photo-to-3d-form');
    const photoTo3dBtn = document.getElementById('photo-to-3d-btn');
    const photoTo3dResult = document.getElementById('photo-to-3d-result');
    const photoTo3dOriginal = document.getElementById('photo-to-3d-original');
    const photoTo3dProcessed = document.getElementById('photo-to-3d-processed');
    const photoTo3dError = document.getElementById('photo-to-3d-error');
    const photoTo3dLoading = document.getElementById('photo-to-3d-loading');
    
    photoTo3dForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(photoTo3dForm);
        
        // 显示加载状态
        photoTo3dBtn.disabled = true;
        photoTo3dResult.classList.add('d-none');
        photoTo3dError.classList.add('d-none');
        photoTo3dLoading.classList.remove('d-none');
        
        fetch('/api/photo-to-3d', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            
            // 显示结果
            photoTo3dOriginal.src = data.original;
            photoTo3dProcessed.src = data.result;
            photoTo3dResult.classList.remove('d-none');
        })
        .catch(error => {
            // 显示错误
            photoTo3dError.querySelector('.alert').textContent = error.message || '处理失败，请重试';
            photoTo3dError.classList.remove('d-none');
        })
        .finally(() => {
            // 隐藏加载状态
            photoTo3dLoading.classList.add('d-none');
            photoTo3dBtn.disabled = false;
        });
    });
    
    // 创建 3D 模型
    const create3dModelForm = document.getElementById('create-3d-model-form');
    const create3dModelBtn = document.getElementById('create-3d-model-btn');
    const create3dModelResult = document.getElementById('create-3d-model-result');
    const create3dModelOriginal = document.getElementById('create-3d-model-original');
    const create3dModelDownload = document.getElementById('create-3d-model-download');
    const create3dModelPreview = document.getElementById('create-3d-model-preview');
    const create3dModelError = document.getElementById('create-3d-model-error');
    const create3dModelLoading = document.getElementById('create-3d-model-loading');
    
    create3dModelForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(create3dModelForm);
        
        // 显示加载状态
        create3dModelBtn.disabled = true;
        create3dModelResult.classList.add('d-none');
        create3dModelError.classList.add('d-none');
        create3dModelLoading.classList.remove('d-none');
        
        fetch('/api/create-3d-model', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            
            // 显示结果
            create3dModelOriginal.src = data.original;
            
            // 处理 3D 模型结果
            if (data.result && typeof data.result === 'object') {
                if (data.result.mesh) {
                    create3dModelDownload.href = data.result.mesh;
                    create3dModelPreview.src = data.result.preview || '';
                } else if (data.result.output) {
                    create3dModelDownload.href = data.result.output;
                    create3dModelPreview.src = data.result.preview || data.result.output;
                }
            } else if (typeof data.result === 'string') {
                create3dModelDownload.href = data.result;
                create3dModelPreview.src = data.result;
            }
            
            create3dModelResult.classList.remove('d-none');
        })
        .catch(error => {
            // 显示错误
            create3dModelError.querySelector('.alert').textContent = error.message || '处理失败，请重试';
            create3dModelError.classList.remove('d-none');
        })
        .finally(() => {
            // 隐藏加载状态
            create3dModelLoading.classList.add('d-none');
            create3dModelBtn.disabled = false;
        });
    });
    
    // 声音克隆
    const cloneVoiceForm = document.getElementById('clone-voice-form');
    const cloneVoiceBtn = document.getElementById('clone-voice-btn');
    const cloneVoiceResult = document.getElementById('clone-voice-result');
    const cloneVoiceOriginal = document.getElementById('clone-voice-original');
    const cloneVoiceProcessed = document.getElementById('clone-voice-processed');
    const cloneVoiceError = document.getElementById('clone-voice-error');
    const cloneVoiceLoading = document.getElementById('clone-voice-loading');
    
    cloneVoiceForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(cloneVoiceForm);
        const voiceFile = document.getElementById('clone-voice-input').files[0];
        
        if (voiceFile) {
            cloneVoiceOriginal.src = URL.createObjectURL(voiceFile);
        }
        
        // 显示加载状态
        cloneVoiceBtn.disabled = true;
        cloneVoiceResult.classList.add('d-none');
        cloneVoiceError.classList.add('d-none');
        cloneVoiceLoading.classList.remove('d-none');
        
        fetch('/api/clone-voice', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            
            // 显示结果
            cloneVoiceProcessed.src = data.result;
            cloneVoiceResult.classList.remove('d-none');
        })
        .catch(error => {
            // 显示错误
            cloneVoiceError.querySelector('.alert').textContent = error.message || '处理失败，请重试';
            cloneVoiceError.classList.remove('d-none');
        })
        .finally(() => {
            // 隐藏加载状态
            cloneVoiceLoading.classList.add('d-none');
            cloneVoiceBtn.disabled = false;
        });
    });
});
