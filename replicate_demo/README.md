# Replicate.com AI 功能演示网站

这是一个使用 Replicate.com API 的演示网站，展示了多种 AI 功能，特别适合祭祀网站使用。

## 功能

1. **照片修复** - 使用 CodeFormer 模型修复和增强老照片
2. **照片上色** - 使用 DeOldify 模型为黑白照片上色
3. **照片 3D 效果** - 使用 3D Photo Inpainting 模型创建 3D 视差效果
4. **创建 3D 模型** - 使用 InstantMesh 模型从照片创建 3D 模型
5. **声音克隆** - 使用 XTTS 模型克隆声音并生成新的语音

## 安装

1. 克隆仓库
2. 安装依赖项

```bash
pip install -r requirements.txt
```

3. 设置 Replicate API 令牌（已在代码中设置）

## 运行

```bash
python app.py
```

然后在浏览器中访问 http://127.0.0.1:5000/

## 使用说明

1. 选择要使用的功能选项卡
2. 上传照片或声音样本
3. 点击相应的按钮开始处理
4. 等待处理完成后查看结果

## 注意事项

- 处理时间取决于 Replicate.com 的服务器负载和模型复杂度
- 某些功能可能需要较长时间处理
- 所有上传的文件和生成的结果都保存在 `uploads` 目录中

## 技术栈

- 后端：Flask
- 前端：Bootstrap 5, JavaScript
- AI 模型：Replicate.com API

## 扩展建议

- 添加更多 AI 模型和功能
- 实现用户认证和历史记录
- 优化移动端体验
- 添加批量处理功能
