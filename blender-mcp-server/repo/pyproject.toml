[project]
name = "blender-mcp"
version = "1.2"
description = "Blender integration through the Model Context Protocol"
readme = "README.md"
requires-python = ">=3.10"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
license = {text = "MIT"}
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "mcp[cli]>=1.3.0",
]

[project.scripts]
blender-mcp = "blender_mcp.server:main"

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
package-dir = {"" = "src"}

[project.urls]
"Homepage" = "https://github.com/yourusername/blender-mcp"
"Bug Tracker" = "https://github.com/yourusername/blender-mcp/issues"
