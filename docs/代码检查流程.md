# 代码检查流程

## 前端代码检查 pip install mypy black ruff bandit

### 1. ESLint (JavaScript/TypeScript 代码检查)

检查 ESLint 版本：
```bash
cd frontend && npx eslint --version
```

检查整个前端代码库：
```bash
cd frontend && npx eslint src
```

检查特定文件：
```bash
cd frontend && npx eslint src/admin/pages/InviteCodes.tsx
```

自动修复可修复的问题：
```bash
cd frontend && npx eslint src --fix
```

### 2. Prettier (代码格式化)

检查格式问题：
```bash
cd frontend && npx prettier --check "src/**/*.{js,jsx,ts,tsx}"
```

自动格式化代码：
```bash
cd frontend && npx prettier --write "src/**/*.{js,jsx,ts,tsx}"
```

## 后端代码检查

检查已安装的代码检查工具：
```bash
cd backend && pip list | grep -E "ruff|black|mypy|bandit"
```

### 1. Ruff (Python 代码检查)

Ruff 是一个快速的 Python linter，可以检查代码风格和潜在问题：

检查代码：
```bash
cd backend && ruff check app
```

自动修复可修复的问题：
```bash
cd backend && ruff check --fix app
```

### 2. Black (Python 代码格式化)

Black 是一个 Python 代码格式化工具，可以自动格式化代码：

检查代码格式：
```bash
cd backend && black --check app
```

自动格式化代码：
```bash
cd backend && black app
```

### 3. MyPy (Python 类型检查)

MyPy 是一个静态类型检查器，可以检查 Python 代码中的类型提示：

检查代码类型：
```bash
cd backend && mypy app
```

安装缺失的类型定义：
```bash
cd backend && mypy --install-types
```

### 4. Bandit (Python 安全性检查)

Bandit 是一个用于查找 Python 代码中常见安全问题的工具：

检查代码安全性：
```bash
cd backend && bandit -r app
```

只检查高严重度的问题：
```bash
cd backend && bandit -r -ll app
```

## 综合检查和最佳实践

### 前端代码检查流程

1. **定期检查**：每次提交代码前运行 ESLint 和 Prettier 检查
2. **自动修复**：先使用 `eslint --fix` 修复简单问题，再手动解决复杂问题
3. **集成到编辑器**：在 VSCode 或其他编辑器中安装 ESLint 和 Prettier 插件，实现实时代码检查

### 后端代码检查流程

1. **顺序检查**：先运行 Black 格式化代码，再运行 Ruff 检查代码风格，最后运行 MyPy 和 Bandit 检查类型和安全性
2. **自动修复**：使用 `black app` 和 `ruff check --fix app` 自动修复格式和简单问题
3. **集成到 Git 钩子**：考虑使用 pre-commit 钩子在提交前自动运行代码检查

### 自动化检查脚本

项目中已集成了自动化代码检查脚本，位于 `scripts` 目录下：

1. **全面代码检查**：运行所有前端和后端代码检查
   ```bash
   ./scripts/check_code_quality.sh
   ```

2. **仅检查后端代码**：运行 MyPy、Black、Ruff 和 Bandit 检查
   ```bash
   ./scripts/check_backend_code.sh
   ```

3. **仅检查前端代码**：运行 ESLint 和 Prettier 检查
   ```bash
   ./scripts/check_frontend_code.sh
   ```

所有检查结果将保存在 `reports` 目录下，方便查看和分析。

## CI/CD 集成

本项目已将代码质量检查集成到 CI/CD 流程中，使用 GitHub Actions 自动运行检查。

### GitHub Actions 工作流

项目中已配置了 GitHub Actions 工作流（位于 `.github/workflows/code-quality.yml`），它会在以下情况自动运行代码质量检查：

1. 向 main、master 或 develop 分支推送代码时
2. 创建针对 main、master 或 develop 分支的 Pull Request 时
3. 手动触发工作流时

工作流包含两个主要任务：

1. **后端代码检查**：运行 MyPy、Black、Ruff 和 Bandit 检查
2. **前端代码检查**：运行 ESLint、Prettier 和 TypeScript 类型检查

检查结果将作为构建产物保存，可以在 GitHub Actions 页面下载查看。

### 本地与 CI/CD 检查的关系

- **本地检查**：开发人员应在提交代码前在本地运行检查，确保代码质量
- **CI/CD 检查**：作为安全网，捕获可能被遗漏的问题，并确保所有代码符合项目标准

建议开发人员：

1. 在本地开发时使用编辑器插件进行实时检查
2. 提交代码前运行 `./scripts/check_code_quality.sh` 进行全面检查
3. 推送代码前确保所有检查都已通过

### 常见问题及解决方案

1. **未使用的变量和导入**：使用 ESLint 和 Ruff 检测并清除
2. **React Hook 依赖数组问题**：确保在 useEffect 和 useCallback 中正确设置依赖数组
3. **类型错误**：使用 TypeScript 和 MyPy 检测并修复类型不匹配问题
4. **安全问题**：使用 Bandit 检测并修复潜在的安全问题，如 SQL 注入、硬编码密码等
5. **CI 检查失败但本地通过**：确保本地环境与 CI 环境使用相同版本的检查工具