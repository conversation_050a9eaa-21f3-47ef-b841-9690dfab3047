# FastAPI 迁移计划

## 概述
本项目目前同时使用了 Flask 和 FastAPI。根据用户需求，所有活跃的 Flask 路由（包括认证逻辑）都需要完全迁移到 FastAPI。

## 1. 准备工作

### 1.1 深入分析 Flask 路由和依赖
*   **目标：** 全面理解 Flask 路由的功能、输入/输出、依赖关系。
*   **操作：**
    *   详细审查 `backend/app/api/routes.py` 和 `backend/app/api/restx_api.py` 中的所有 Flask 路由。
    *   识别每个路由的功能、输入参数、输出格式以及它们所依赖的数据库操作、业务逻辑和外部服务。
    *   特别关注 `Flask-JWT-Extended` 的使用方式，包括令牌的生成、验证和用户身份的获取。

### 1.2 确定 FastAPI 认证方案
*   **目标：** 选择并规划 FastAPI 的认证和授权机制。
*   **操作：**
    *   根据项目需求，选择合适的 FastAPI 认证方案（例如 OAuth2 with Password (and Hashing), JWT, API Key 等）。
    *   考虑如何将现有 `Flask-JWT-Extended` 的逻辑（如用户密码哈希、令牌生成和验证）平滑迁移到 FastAPI 的依赖注入系统。

### 1.3 规划 FastAPI 模块结构
*   **目标：** 设计清晰、可维护的 FastAPI 路由器结构。
*   **操作：**
    *   根据现有 Flask 路由的功能模块，规划新的 FastAPI 路由器（`APIRouter`）结构。
    *   例如，可以将 `environments`、`religious-settings` 和 `ancestors` 相关的路由分别组织到独立的 FastAPI 路由器文件中。

## 2. 核心功能迁移

### 2.1 迁移 Flask 路由到 FastAPI 路由
*   **目标：** 将所有 Flask 路由转换为对应的 FastAPI 路径操作。
*   **操作：**
    *   为每个 Flask 路由创建对应的 FastAPI 路径操作函数。
    *   将 Flask 的 `request` 对象操作（如 `request.get_json()`）转换为 FastAPI 的 Pydantic 模型请求体。
    *   将 Flask 的 `jsonify` 响应转换为 FastAPI 的 Pydantic 模型响应或直接返回 Python 字典/列表。
    *   处理 Flask 路由中的错误处理和异常捕获，并将其适配到 FastAPI 的异常处理机制。

### 2.2 迁移认证和授权逻辑
*   **目标：** 在 FastAPI 中实现完整的认证和授权功能。
*   **操作：**
    *   根据选择的 FastAPI 认证方案，实现用户认证（登录、注册）和授权（JWT 验证）。
    *   将 `Flask-JWT-Extended` 的 `jwt_required()` 装饰器替换为 FastAPI 的 `Depends` 依赖注入，并实现相应的认证依赖函数。
    *   确保用户身份信息（`get_jwt_identity()`）在 FastAPI 中也能正确获取和使用。

### 2.3 更新数据模型操作
*   **目标：** 确保数据库操作在 FastAPI 环境中正常工作。
*   **操作：**
    *   虽然 SQLAlchemy 兼容两个框架，但需要检查 Flask 路由中是否有直接依赖 Flask-SQLAlchemy 扩展的特定用法（例如 `db.session` 的管理）。
    *   确保数据库会话管理在 FastAPI 中通过依赖注入（例如 `Depends(get_db)`）正确处理。

## 3. 测试与验证

### 3.1 编写或更新单元测试
*   **目标：** 验证每个迁移后的 FastAPI 路由的功能正确性。
*   **操作：**
    *   为每个迁移后的 FastAPI 路由编写单元测试。
    *   使用 `pytest` 和 `httpx`（或 `pytest-asyncio`）来测试 FastAPI 端点。

### 3.2 进行集成测试
*   **目标：** 验证不同模块之间的交互。
*   **操作：**
    *   测试不同模块之间的交互，确保整个 API 链条（认证、数据操作、响应）正常工作。
    *   模拟客户端请求，验证端点的行为是否符合预期。

### 3.3 功能测试
*   **目标：** 对迁移后的 API 进行端到端的功能验证。
*   **操作：**
    *   手动或使用自动化工具对迁移后的 API 进行端到端的功能测试，确保所有业务逻辑正确无误。

## 4. 清理与优化

### 4.1 移除 Flask 相关代码和依赖
*   **目标：** 清理项目，移除所有不再需要的 Flask 相关组件。
*   **操作：**
    *   在确认所有 Flask 路由已成功迁移并测试通过后，删除 `backend/app/api/routes.py` 和 `backend/app/api/restx_api.py` 文件。
    *   从 `backend/requirements.txt` 中移除 `Flask`、`Flask-Cors`、`Flask-SQLAlchemy`、`Flask-Migrate`、`Flask-JWT-Extended`、`flask-restx` 等 Flask 相关依赖。
    *   更新 `backend/requirements.txt` 并重新安装依赖。

### 4.2 优化 FastAPI 应用
*   **目标：** 提升 FastAPI 应用的性能和可维护性。
*   **操作：**
    *   审查 FastAPI 代码，确保遵循最佳实践，例如使用 `APIRouter` 进行模块化、正确使用依赖注入、Pydantic 模型定义清晰等。
    *   考虑性能优化，例如异步操作、数据库连接池管理等。

## 迁移流程图

```mermaid
graph TD
    A[开始] --> B{分析现有Flask路由和依赖};
    B --> C{确定FastAPI认证方案};
    C --> D[规划FastAPI模块结构];
    D --> E[迁移Flask路由到FastAPI路由];
    E --> F[迁移认证和授权逻辑];
    F --> G[更新数据模型操作];
    G --> H[编写或更新单元测试];
    H --> I[进行集成测试];
    I --> J[功能测试];
    J --> K{所有测试通过?};
    K -- 是 --> L[移除Flask相关代码和依赖];
    K -- 否 --> E;
    L --> M[优化FastAPI应用];
    M --> N[完成];