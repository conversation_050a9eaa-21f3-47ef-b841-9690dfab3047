# 统一服务架构

## 概述

为了简化应用程序架构并解决API路由问题，我们将原本独立的渲染服务整合到了主应用中。这样所有API都可以通过同一个端口访问，避免了跨域问题和端口冲突。

## 架构变更

### 1. 渲染服务蓝图化

将原本独立的`render_service.py`转换为Flask蓝图，并注册到主应用中：

- 创建了`app/render_service/__init__.py`和`app/render_service/routes.py`
- 将所有渲染服务相关的路由移动到蓝图中
- 在主应用的`create_app()`函数中注册了渲染服务蓝图

### 2. 添加根路由处理

为主应用添加了根路径(/)的处理器，返回API状态和文档信息，避免用户访问根路径时看到404错误。

### 3. 统一启动脚本

创建了新的启动和停止脚本：

- `start_unified_service.sh`：启动统一的应用服务
- `stop_unified_service.sh`：停止统一的应用服务

## API路由

所有API都通过同一个端口(5001)访问，路由结构如下：

### 根路由
- `GET /`：返回API状态和文档信息

### 主应用API
- `GET /api/environments`：获取所有环境
- `GET /api/environments/<id>`：获取特定环境
- `GET /api/religious-settings`：获取所有宗教/文化设置
- `GET /api/ancestors`：获取当前用户的所有先人（需要认证）
- `POST /api/ancestors`：创建新先人（需要认证）
- `POST /api/auth/register`：用户注册
- `POST /api/auth/login`：用户登录

### 渲染服务API
- `GET /api/ping`：简单的ping接口，用于检测网络延迟
- `GET /api/render-service/status`：检查渲染服务状态
- `POST /api/render-service/init`：初始化渲染会话
- `POST /api/render-control`：处理渲染控制命令
- `GET /api/render-frame`：渲染并返回当前帧
- `GET /api/render-stream`：提供渲染流
- `GET /api/prerendered/<elev>/<angle>`：获取预渲染图像

### 文档和监控API
- `GET /api/docs`：API文档（Swagger UI）
- `GET /api/health`：健康检查端点
- `GET /api/monitoring`：详细监控信息

## 使用方法

### 启动服务

使用以下命令启动统一服务：

```bash
./start_unified_service.sh
```

这个脚本会：
1. 检查必要的依赖是否已安装
2. 停止可能已经运行的Gunicorn进程
3. 启动Gunicorn服务器，使用`gevent_patch:app`作为入口点
4. 如果Gunicorn启动失败，会尝试使用Flask开发服务器

### 停止服务

使用以下命令停止统一服务：

```bash
./stop_unified_service.sh
```

这个脚本会：
1. 停止Gunicorn进程
2. 检查端口5001是否仍被占用，如果是，尝试强制终止占用进程

## 开发注意事项

1. 不再需要单独启动渲染服务，所有功能都已整合到主应用中
2. 修改渲染服务相关代码时，应该修改`app/render_service/routes.py`，而不是原来的`render_service.py`
3. 原来的`render_service.py`保留作为参考，但不再直接使用
4. 所有API请求都应该发送到同一个端口(5001)

## API文档

项目使用Flask-RESTX提供API文档，可以通过以下URL访问：

```
http://localhost:5001/api/docs
```

API文档提供了所有API端点的详细信息，包括：
- 请求参数
- 响应格式
- 示例请求和响应
- 认证要求

## 监控和日志

项目提供了详细的监控和日志功能，可以通过以下URL访问：

```
http://localhost:5001/api/health      # 基本健康检查
http://localhost:5001/api/monitoring  # 详细监控信息
```

监控信息包括：
- 请求计数（总数、按端点、按方法、按状态码）
- 性能指标（平均响应时间、最小响应时间、最大响应时间）
- 错误计数
- 应用运行时间

日志文件位于`backend/logs`目录：
- `api.log`：API请求日志
- `performance.log`：性能日志
- `error.log`：错误日志
- `access.log`：访问日志
- `error.log`：Gunicorn错误日志

## 故障排除

如果遇到问题，可以检查以下日志文件：
- 访问日志：`backend/logs/access.log`
- 错误日志：`backend/logs/error.log`
- API请求日志：`backend/logs/api.log`
- 性能日志：`backend/logs/performance.log`

如果服务无法启动，可能是端口冲突导致的。使用以下命令检查端口占用情况：

```bash
lsof -i:5001
```

然后使用`stop_unified_service.sh`脚本停止占用端口的进程，或者手动终止：

```bash
kill -9 <进程ID>
```
