# 归处 App - 设计规范说明文档

本文档旨在为“归处”App 的 UI/UX 设计提供统一的指导原则和具体规范，确保产品在视觉呈现、用户体验和品牌形象上的一致性与高品质。

## 1. 设计原则

*   **情感关怀**: 设计应体现对用户的理解与慰藉，营造温暖、宁静的氛围。
*   **简洁易用**: 界面清晰直观，操作流程便捷，降低用户学习成本。
*   **沉浸体验**: 尤其在 3D 纪念空间中，追求真实感与代入感，让用户能够寄托哀思。
*   **文化尊重**: 设计元素应尊重不同地域和文化背景下的习俗与信仰。
*   **一致性**: 跨平台、跨界面的视觉风格和交互模式保持统一。

## 2. 目标平台与视觉风格

*   **主要目标平台**: 移动端 (iOS, Android) - 基于Figma设计的移动应用为主要交付目标。
*   **其他考虑平台**: Web (响应式设计) 和 微信小程序 (作为补充，方便社交分享和轻量级访问)。
*   **设计语言/风格**:
    *   移动端 iOS: **优先使用 Cupertino 设计语言**，遵循 Apple Human Interface Guidelines
    *   移动端 Android: 兼容 Material Design 3，但以iOS风格为主
    *   Web: 响应式设计，保持与移动端一致的视觉风格
    *   微信小程序: 微信小程序官方设计规范
*   **设备模拟**:
    *   移动端 iOS: **模拟 iPhone 16 Pro 屏幕** (基于Figma设计)
    *   移动端 Android: 模拟对应尺寸的Android设备
    *   Web: 模拟标准浏览器窗口，支持响应式布局
    *   微信小程序: 模拟标准小程序界面
*   **主题**: **优先实现浅色主题 (Light Mode)**，基于Figma设计的明亮、温暖视觉体验。暗黑主题作为可选功能。

## 3. 色彩方案

基于Figma设计，色彩选择旨在营造温暖、宁静、现代且富有情感的氛围。

*   **主色调 (Primary Colors)**:
    *   纯白背景 (Pure White): `#FFFFFF` - 主要背景色，营造清洁、纯净的感觉
    *   深炭黑 (Deep Charcoal): `#1E1E1E` - 主要文字颜色，确保良好的可读性
    *   天空蓝渐变起点 (Sky Blue): `#87CEEB` - Hero区域渐变起始色，带来希望与宁静
    *   暖金棕渐变终点 (Warm Golden Brown): `#D69E2E` - Hero区域渐变结束色，增添温暖感
*   **辅助色 (Secondary Colors)**:
    *   活力橙红 (Vibrant Orange): `#FF6B35` - 用于重要图标和强调元素
    *   柔和粉红 (Soft Pink): `#FF6B6B` - 用于激活状态和重要按钮
    *   中性灰 (Neutral Gray): `#757575` - 用于非激活状态的图标和次要文字
*   **功能卡片色彩 (Feature Card Colors)**:
    *   浅蓝卡片 (Light Blue Card): `#E3F2FD` - 不受时空限制功能卡片
    *   浅绿卡片 (Light Green Card): `#E8F5E8` - 3D沉浸体验功能卡片
    *   浅紫卡片 (Light Purple Card): `#F3E5F5` - AI赋能服务功能卡片
    *   浅橙卡片 (Light Orange Card): `#FFF3E0` - 家族共享传承功能卡片
    *   浅粉卡片 (Light Pink Card): `#FCE4EC` - 在线祭拜与互动功能卡片
    *   浅黄卡片 (Light Yellow Card): `#FFFDE7` - 创建个性化纪念空间功能卡片
*   **中性色 (Neutral Colors)**:
    *   浅灰边框 (Light Gray Border): `#E0E0E0` - 用于分隔线和边框
    *   中灰文字 (Medium Gray Text): `#999999` - 用于辅助文字
    *   深灰文字 (Dark Gray Text): `#666666` - 用于次要标题
*   **语义色 (Semantic Colors)**:
    *   成功/确认 (Success): `#4CAF50` (绿色)
    *   警告/提示 (Warning): `#FF9800` (橙色)
    *   错误/危险 (Error): `#F44336` (红色)

## 4. 排版规范

基于Figma设计，选择清晰易读、具有现代感的字体，确保信息传递的准确性和舒适性。

*   **主要字体 (Primary Font Family)**:
    *   中文: **Hiragino Kaku Gothic StdN** (主要) / 苹方 (PingFang SC) - 基于Figma设计规范
    *   英文/数字: **Hiragino Kaku Gothic Std** / SF Pro Display - 与中文字体搭配和谐
    *   备用字体: 系统默认无衬线字体
*   **字号层级 (Font Size Hierarchy)** (基于Figma设计):
    *   应用标题 (App Title): `14px` (Weight: 800) - 如"Everloom 归处"
    *   Hero主标题 (Hero Title): `13px` (Weight: 800) - 如"跨越时空的思念"
    *   功能卡片标题 (Feature Title): `12px` (Weight: 600) - 功能卡片主标题
    *   正文内容 (Body Text): `10px` (Weight: 400) - 功能描述文字
    *   导航标签 (Navigation Label): `8px` (Weight: 400) - 底部导航文字
    *   聊天文字 (Chat Text): `10px` (Weight: 400) - 对话气泡文字
    *   AI服务文字 (AI Service Text): `8px` (Weight: 400) - AI服务描述
*   **行高 (Line Height)**: 
    *   标题文字: `1.4` - `1.5` 倍字号
    *   正文内容: `1.5` - `1.6` 倍字号，保证阅读舒适度
*   **字重 (Font Weight)**:
    *   超粗体 (Extra Bold): `800` - 用于重要标题
    *   粗体 (Bold): `600` - 用于次级标题
    *   常规 (Regular): `400` - 用于正文和描述文字

## 5. 图标系统

基于Figma设计，图标设计应简洁、表意明确，风格统一，与整体视觉风格保持一致。

*   **图标库**: 
    *   **Cupertino Icons** (主要) - 用于iOS风格的移动应用
    *   **Lucide Icons** (备用) - 用于Web端和自定义图标
    *   **自定义SVG图标** - 特殊功能图标，如扫描按钮
*   **风格**: **线性风格为主**，符合iOS设计语言，简洁现代
*   **尺寸规范** (基于Figma设计):
    *   导航图标: `18px` - 底部导航栏图标
    *   功能图标: `20px` - 功能卡片中的图标
    *   头像图标: `40px` - 用户头像尺寸
    *   扫描按钮图标: `28px` - 中央扫描按钮
    *   装饰图标: `24px` - 其他装饰性图标
*   **颜色规范**:
    *   激活状态: `#FF6B6B` (柔和粉红)
    *   非激活状态: `#757575` (中性灰)
    *   重要图标: `#FF6B35` (活力橙红)
    *   白色图标: `#FFFFFF` (用于深色背景)

## 6. UI 组件库 (基于 Flutter Cupertino)

基于Figma设计，所有 UI 组件均采用 **Flutter Cupertino 组件库**，确保iOS风格的一致性和原生体验。

*   **按钮 (Buttons)**:
    *   主按钮 (Primary): 背景色为静谧蓝，文字为月光白。
    *   次按钮 (Secondary): 边框为静谧蓝，文字为静谧蓝，背景透明或浅灰。
    *   文本按钮 (Text): 无边框无背景，文字为静谧蓝。
    *   禁用状态 (Disabled): 透明度降低，鼠标指针变为 `not-allowed`。
    *   尺寸: 提供大、中、小三种尺寸。
*   **输入框 (Input Fields)**:
    *   边框: 浅灰色，获取焦点时变为静谧蓝。
    *   背景: 深空灰的较浅变体或月光白（根据主题）。
    *   提示文字 (Placeholder): 中灰色。
*   **卡片 (Cards)**:
    *   背景: 深空灰的较浅变体。
    *   圆角: `8px`。
    *   阴影: 营造轻微的悬浮感。
*   **导航栏 (Navigation Bars)** (基于Figma设计):
    *   **底部Tab导航**: 
        *   5个导航项: 首页、空间、开始创建、动态、我的
        *   中央"开始创建"按钮: 特殊的圆形白色背景设计，带扫描图标
        *   激活状态: 图标和文字使用柔和粉红色 `#FF6B6B`
        *   非激活状态: 图标和文字使用中性灰色 `#757575`
        *   背景: 纯白色 `#FFFFFF`，顶部细边框 `#E0E0E0`
    *   **顶部导航**: 
        *   应用标题居中: "Everloom 归处"
        *   右侧装饰图标: 火焰图标 `#FF6B35`
        *   背景: 渐变背景或透明
*   **列表 (Lists)**:
    *   分隔线: 浅灰色。
    *   项目间距: 适中，保证信息区隔。
*   **弹窗/模态框 (Modals/Dialogs)**:
    *   背景遮罩: 半透明深色。
    *   内容区域: 圆角，包含标题、内容、操作按钮。
*   **头像 (Avatars)**:
    *   形状: 圆形。
    *   占位符: 使用通用用户图标或姓氏首字母。
*   **标签 (Tags/Badges)**:
    *   小巧，圆角，用于状态标记或分类。

## 7. 交互模式与动效

交互应自然、流畅，动效应简洁、有意义，避免过度动画干扰用户。

*   **触摸反馈**: 点击、长按等操作应有明确的视觉反馈 (如涟漪效果、高亮变化)。
*   **页面切换**: 采用平滑的过渡动画 (如左右滑动、淡入淡出)。
*   **加载状态**: 使用骨架屏 (Skeleton Screens) 或进度指示器 (Spinners, Progress Bars) 缓解等待焦虑。
*   **手势操作**: (如果适用，如移动端滑动返回) 应符合平台规范。
*   **错误提示**: 清晰、友好地指示错误原因和解决方法。

## 8. 图片与媒体资源

*   **图片来源**: Unsplash, Pexels, Apple 官方 UI 资源。
*   **风格**: 选择高质量、意境相符、情感共鸣的图片。
*   **优化**: 图片需经过压缩优化，保证加载速度。
*   **视频/音频**: 保证清晰度，提供标准播放控件。

## 9. 无障碍设计 (Accessibility - A11Y)

致力于让所有用户，包括有特殊需求的用户，都能顺畅使用 App。

*   **对比度**: 确保文本与背景有足够的对比度。
*   **可点击区域**: 按钮和链接的可点击区域应足够大。
*   **键盘导航**: (Web/桌面端) 支持完整的键盘导航。
*   **屏幕阅读器**: (如果适用) 确保内容能被屏幕阅读器正确解读，为图片提供 `alt` 文本。

## 10. 文档维护

本设计规范将随着产品的迭代和用户反馈持续更新。所有团队成员应遵循此规范进行设计与开发工作。

## 11. Figma设计特定规范

基于提供的Figma设计文件，以下是具体的设计实现要求：

*   **Hero区域设计**:
    *   背景: 天空蓝到暖金棕的线性渐变 (`#87CEEB` → `#D69E2E`)
    *   高度: `240px`
    *   主标题: "跨越时空的思念" (13px, Weight: 800)
    *   副标题: "归处为您提供一个不受时间、地点限制的个性化在线纪念空间，让思念永不消逝"
    *   半透明卡片: 黑色背景，30%透明度，12px圆角

*   **功能卡片网格**:
    *   6个功能卡片，2列3行布局
    *   每个卡片: 8px圆角，不同的浅色背景
    *   卡片阴影: `0px 4px 4px 0px rgba(0, 0, 0, 0.25)`
    *   内边距: 16px

*   **聊天界面设计**:
    *   用户头像: 圆形，40px直径
    *   对话气泡: 不同方向的圆角设计
    *   用户消息: 右对齐，特定圆角 `50px 0px 50px 50px`
    *   AI回复: 左对齐，标准圆角 `50px`

*   **AI服务展示**:
    *   3D头像展示
    *   服务描述文字: "长小养照护智能为您服务"
    *   分隔线设计

---

**最后更新**: 2024-12-05
**基于**: Figma设计文件 (iPhone 16 Pro - 1)