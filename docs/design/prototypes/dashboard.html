<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表盘 - 归处</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'dark-space-gray': '#333333',
                        'serene-blue': '#4A90E2',
                        'warm-gold': '#F5A623',
                        'moonlight-white': '#F8F8F8',
                        'light-gray': '#CCCCCC',
                        'medium-gray': '#999999',
                        'dark-gray': '#666666',
                        'success-green': '#2ECC71',
                        'warning-orange': '#F39C12',
                        'error-red': '#E74C3C',
                        'app-bg-dark': '#111827',      // Tailwind gray-900
                        'card-bg-dark': '#1f2937',      // Tailwind gray-800
                        'input-bg-dark': '#374151',     // Tailwind gray-700
                        'text-dark-primary': '#F8F8F8', // Moonlight White
                        'text-dark-secondary': '#9ca3af', // Tailwind gray-400
                        'border-dark': '#374151',    // Tailwind gray-700
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.25);
        }
    </style>
</head>
<body class="bg-app-bg-dark text-text-dark-primary min-h-screen">
    <!-- Navigation Bar -->
    <nav class="bg-card-bg-dark shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <a href="dashboard.html" class="flex-shrink-0 flex items-center">
                        <i class="fas fa-leaf text-serene-blue text-3xl"></i>
                        <span class="ml-2 text-2xl font-semibold text-moonlight-white">归处</span>
                    </a>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="dashboard.html" class="bg-app-bg-dark text-white px-3 py-2 rounded-md text-sm font-medium" aria-current="page">仪表盘</a>
                        <a href="#memorials" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">我的纪念馆</a>
                        <a href="#family" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">家族管理</a>
                        <a href="#ai-services" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">AI服务</a>
                        <a href="#store" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">在线商店</a>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-4 flex items-center md:ml-6">
                        <button type="button" class="p-1 bg-gray-800 rounded-full text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white">
                            <span class="sr-only">View notifications</span>
                            <i class="fas fa-bell"></i>
                        </button>
                        <!-- Profile dropdown -->
                        <div class="ml-3 relative">
                            <div>
                                <button type="button" class="max-w-xs bg-gray-800 rounded-full flex items-center text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white" id="user-menu-button" aria-expanded="false" aria-haspopup="true">
                                    <span class="sr-only">Open user menu</span>
                                    <!-- Image from Unsplash, Source: https://unsplash.com/photos/random-person -->
                                    <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="User profile picture">
                                </button>
                            </div>
                            <!-- Dropdown menu, show/hide based on menu state. -->
                            <div id="user-menu" class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-card-bg-dark ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="user-menu-button" tabindex="-1">
                                <a href="profile.html" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700" role="menuitem" tabindex="-1" id="user-menu-item-0">我的主页</a>
                                <a href="settings.html" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700" role="menuitem" tabindex="-1" id="user-menu-item-1">账户设置</a>
                                <a href="login.html" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700" role="menuitem" tabindex="-1" id="user-menu-item-2">退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="-mr-2 flex md:hidden">
                    <!-- Mobile menu button -->
                    <button type="button" id="mobile-menu-open-button" class="bg-gray-800 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white" aria-controls="mobile-menu" aria-expanded="false">
                        <span class="sr-only">Open main menu</span>
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu, show/hide based on menu state. -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="dashboard.html" class="bg-app-bg-dark text-white block px-3 py-2 rounded-md text-base font-medium" aria-current="page">仪表盘</a>
                <a href="#memorials" class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium">我的纪念馆</a>
                <a href="#family" class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium">家族管理</a>
                <a href="#ai-services" class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium">AI服务</a>
                <a href="#store" class="text-gray-300 hover:bg-gray-700 hover:text-white block px-3 py-2 rounded-md text-base font-medium">在线商店</a>
            </div>
            <div class="pt-4 pb-3 border-t border-gray-700">
                <div class="flex items-center px-5">
                    <div class="flex-shrink-0">
                         <!-- Image from Unsplash, Source: https://unsplash.com/photos/random-person -->
                        <img class="h-10 w-10 rounded-full" src="https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="">
                    </div>
                    <div class="ml-3">
                        <div class="text-base font-medium leading-none text-white">李明</div>
                        <div class="text-sm font-medium leading-none text-gray-400"><EMAIL></div>
                    </div>
                    <button type="button" class="ml-auto bg-gray-800 flex-shrink-0 p-1 rounded-full text-gray-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white">
                        <span class="sr-only">View notifications</span>
                        <i class="fas fa-bell"></i>
                    </button>
                </div>
                <div class="mt-3 px-2 space-y-1">
                    <a href="profile.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-400 hover:text-white hover:bg-gray-700">我的主页</a>
                    <a href="settings.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-400 hover:text-white hover:bg-gray-700">账户设置</a>
                    <a href="login.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-400 hover:text-white hover:bg-gray-700">退出登录</a>
                </div>
            </div>
        </div>
    </nav>

    <header class="bg-card-bg-dark shadow-md">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex justify-between items-center">
            <h1 class="text-3xl font-bold text-moonlight-white">仪表盘</h1>
            <a href="create_memorial.html" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-serene-blue hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-card-bg-dark focus:ring-serene-blue">
                <i class="fas fa-plus mr-2"></i> 创建新的纪念空间
            </a>
        </div>
    </header>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- Statistics Section -->
        <section class="mb-8">
            <h2 class="text-xl font-semibold text-gray-300 mb-4">概览</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Stat Card 1 -->
                <div class="stat-card bg-card-bg-dark p-6 rounded-xl shadow-lg transition-all duration-300 ease-in-out">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-serene-blue bg-opacity-20 text-serene-blue">
                            <i class="fas fa-landmark fa-2x"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-3xl font-bold text-moonlight-white">5</p>
                            <p class="text-sm text-text-dark-secondary">我的纪念馆</p>
                        </div>
                    </div>
                </div>
                <!-- Stat Card 2 -->
                <div class="stat-card bg-card-bg-dark p-6 rounded-xl shadow-lg transition-all duration-300 ease-in-out">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-warm-gold bg-opacity-20 text-warm-gold">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-3xl font-bold text-moonlight-white">3</p>
                            <p class="text-sm text-text-dark-secondary">家族空间</p>
                        </div>
                    </div>
                </div>
                <!-- Stat Card 3 -->
                <div class="stat-card bg-card-bg-dark p-6 rounded-xl shadow-lg transition-all duration-300 ease-in-out">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-success-green bg-opacity-20 text-success-green">
                            <i class="fas fa-hand-holding-heart fa-2x"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-3xl font-bold text-moonlight-white">128</p>
                            <p class="text-sm text-text-dark-secondary">总祭拜次数</p>
                        </div>
                    </div>
                </div>
                <!-- Stat Card 4 -->
                <div class="stat-card bg-card-bg-dark p-6 rounded-xl shadow-lg transition-all duration-300 ease-in-out">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-error-red bg-opacity-20 text-error-red">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-3xl font-bold text-moonlight-white">2</p>
                            <p class="text-sm text-text-dark-secondary">近期纪念日</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Recent Memorials Section -->
        <section id="memorials" class="mb-8">
            <h2 class="text-xl font-semibold text-gray-300 mb-4">最近管理的纪念馆</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Memorial Card 1 -->
                <div class="bg-card-bg-dark rounded-xl shadow-lg overflow-hidden transform hover:scale-105 transition-transform duration-300">
                    <!-- Image from Unsplash, Source: https://unsplash.com/photos/a-pathway-in-a-cemetery-with-trees-and-gravestones-lFvXhA22rYk -->
                    <img class="h-48 w-full object-cover" src="https://images.unsplash.com/photo-1548005099-7revXhA22rYk?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="纪念馆封面">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-moonlight-white mb-1">慈父李建国纪念馆</h3>
                        <p class="text-sm text-text-dark-secondary mb-3">1950 - 2023</p>
                        <p class="text-xs text-gray-500 mb-4">创建于: 2023-10-15</p>
                        <div class="flex justify-between items-center">
                            <a href="memorial_detail.html?id=1" class="text-sm text-serene-blue hover:underline">进入空间 <i class="fas fa-arrow-right ml-1"></i></a>
                            <div class="flex space-x-2">
                                <button class="text-gray-400 hover:text-warm-gold"><i class="fas fa-edit"></i></button>
                                <button class="text-gray-400 hover:text-error-red"><i class="fas fa-trash"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Memorial Card 2 -->
                <div class="bg-card-bg-dark rounded-xl shadow-lg overflow-hidden transform hover:scale-105 transition-transform duration-300">
                    <!-- Image from Unsplash, Source: https://unsplash.com/photos/a-statue-of-a-woman-holding-a-flower-in-a-cemetery-g3QBQCVoHhQ -->
                    <img class="h-48 w-full object-cover" src="https://images.unsplash.com/photo-1585869210190-g3QBQCVoHhQ?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="纪念馆封面">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-moonlight-white mb-1">慈母王秀英纪念馆</h3>
                        <p class="text-sm text-text-dark-secondary mb-3">1955 - 2020</p>
                        <p class="text-xs text-gray-500 mb-4">创建于: 2022-05-20</p>
                        <div class="flex justify-between items-center">
                            <a href="memorial_detail.html?id=2" class="text-sm text-serene-blue hover:underline">进入空间 <i class="fas fa-arrow-right ml-1"></i></a>
                            <div class="flex space-x-2">
                                <button class="text-gray-400 hover:text-warm-gold"><i class="fas fa-edit"></i></button>
                                <button class="text-gray-400 hover:text-error-red"><i class="fas fa-trash"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Add New Memorial Card -->
                <a href="create_memorial.html" class="bg-card-bg-dark rounded-xl shadow-lg flex flex-col items-center justify-center text-center p-6 border-2 border-dashed border-gray-600 hover:border-serene-blue hover:bg-gray-700 transition-all duration-300 transform hover:scale-105">
                    <i class="fas fa-plus-circle text-serene-blue text-4xl mb-3"></i>
                    <h3 class="text-lg font-semibold text-moonlight-white">创建新纪念馆</h3>
                    <p class="text-sm text-text-dark-secondary">为逝去的亲人建立一个数字家园</p>
                </a>
            </div>
        </section>

        <!-- Quick Actions Section -->
        <section id="quick-actions" class="mb-8">
            <h2 class="text-xl font-semibold text-gray-300 mb-4">快捷操作</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <a href="#family" class="bg-card-bg-dark p-6 rounded-xl shadow-lg flex flex-col items-center text-center hover:bg-gray-700 transition-colors duration-300">
                    <i class="fas fa-users text-3xl text-serene-blue mb-3"></i>
                    <h4 class="font-semibold text-moonlight-white">家族管理</h4>
                    <p class="text-xs text-text-dark-secondary">管理家族成员与族谱</p>
                </a>
                <a href="#ai-services" class="bg-card-bg-dark p-6 rounded-xl shadow-lg flex flex-col items-center text-center hover:bg-gray-700 transition-colors duration-300">
                    <i class="fas fa-magic text-3xl text-warm-gold mb-3"></i>
                    <h4 class="font-semibold text-moonlight-white">AI照片修复</h4>
                    <p class="text-xs text-text-dark-secondary">修复珍贵的老照片</p>
                </a>
                <a href="#events" class="bg-card-bg-dark p-6 rounded-xl shadow-lg flex flex-col items-center text-center hover:bg-gray-700 transition-colors duration-300">
                    <i class="fas fa-calendar-check text-3xl text-success-green mb-3"></i>
                    <h4 class="font-semibold text-moonlight-white">纪念日提醒</h4>
                    <p class="text-xs text-text-dark-secondary">查看重要纪念日</p>
                </a>
                <a href="#settings" class="bg-card-bg-dark p-6 rounded-xl shadow-lg flex flex-col items-center text-center hover:bg-gray-700 transition-colors duration-300">
                    <i class="fas fa-cog text-3xl text-light-gray mb-3"></i>
                    <h4 class="font-semibold text-moonlight-white">账户设置</h4>
                    <p class="text-xs text-text-dark-secondary">管理您的账户信息</p>
                </a>
            </div>
        </section>

    </main>

    <footer class="bg-card-bg-dark mt-12">
        <div class="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8 text-center text-gray-400 text-sm">
            &copy; <span id="current-year"></span> 归处项目 - 保留所有权利。
        </div>
    </footer>

    <script>
        // Navbar toggle
        const userMenuButton = document.getElementById('user-menu-button');
        const userMenu = document.getElementById('user-menu');
        const mobileMenuOpenButton = document.getElementById('mobile-menu-open-button');
        const mobileMenu = document.getElementById('mobile-menu');

        userMenuButton.addEventListener('click', () => {
            const isExpanded = userMenuButton.getAttribute('aria-expanded') === 'true' || false;
            userMenuButton.setAttribute('aria-expanded', !isExpanded);
            userMenu.classList.toggle('hidden');
        });

        mobileMenuOpenButton.addEventListener('click', () => {
            const isExpanded = mobileMenuOpenButton.getAttribute('aria-expanded') === 'true' || false;
            mobileMenuOpenButton.setAttribute('aria-expanded', !isExpanded);
            mobileMenu.classList.toggle('hidden');
            // Toggle icon
            const icon = mobileMenuOpenButton.querySelector('i');
            if (mobileMenu.classList.contains('hidden')) {
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            } else {
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            }
        });

        // Set current year in footer
        document.getElementById('current-year').textContent = new Date().getFullYear();
    </script>
</body>
</html>