<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>归处 - 高保真HTML原型预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'dark-space-gray': '#333333',
                        'serene-blue': '#4A90E2',
                        'warm-gold': '#F5A623',
                        'moonlight-white': '#F8F8F8',
                        'light-gray': '#CCCCCC',
                        'medium-gray': '#999999',
                        'dark-gray': '#666666',
                        'success-green': '#2ECC71',
                        'warning-orange': '#F39C12',
                        'error-red': '#E74C3C',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            background-color: #111827; /* Tailwind gray-900, slightly darker for better contrast */
            color: #F8F8F8;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
        .prototype-frame-container {
            margin-bottom: 50px; /* Increased margin for better separation */
            border: 1px solid #374151; /* Tailwind gray-700 */
            border-radius: 12px; /* Increased border-radius for a softer look */
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); /* Softer shadow */
        }
        .prototype-title {
            background-color: #1f2937; /* Tailwind gray-800 */
            color: #d1d5db; /* Tailwind gray-300 */
            padding: 16px 24px; /* Increased padding */
            font-size: 1.25rem; /* text-xl */
            font-weight: 600; /* font-semibold */
            border-bottom: 1px solid #374151; /* Tailwind gray-700 */
        }
        .prototype-iframe {
            width: 100%;
            height: 75vh; /* Default height, can be adjusted per iframe or content */
            border: none;
            background-color: #ffffff; /* Default iframe background, pages should set their own */
        }
        .browser-mockup {
            /* Removed direct border and shadow from here, applied to prototype-frame-container */
            border-radius: 0 0 11px 11px; /* Match parent's bottom radius */
            overflow: hidden;
            background-color: #1f2937; /* bg-gray-800 */
        }
        .browser-header {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem; /* p-3 px-4 */
            background-color: #111827; /* bg-gray-900 */
            border-bottom: 1px solid #374151; /* border-b border-gray-700 */
        }
        .browser-dots span {
            display: block;
            width: 0.875rem; /* w-3.5 */
            height: 0.875rem; /* h-3.5 */
            border-radius: 9999px; /* rounded-full */
            margin-right: 0.625rem; /* mr-2.5 */
        }
        .browser-address-bar {
            flex-grow: 1;
            background-color: #374151; /* bg-gray-700 */
            color: #d1d5db; /* text-gray-300 */
            padding: 0.5rem 1rem; /* py-2 px-4 */
            border-radius: 0.375rem; /* rounded-md */
            font-size: 0.875rem; /* text-sm */
            margin-left: 1.25rem; /* ml-5 */
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-100 p-6 md:p-10">
    <header class="text-center mb-12 md:mb-16">
        <h1 class="text-3xl md:text-4xl font-bold text-serene-blue">归处 - 高保真HTML原型预览</h1>
        <p class="text-md md:text-lg text-gray-400 mt-3">所有核心界面的高保真HTML实现概览 (Web平台 - 暗黑主题优先)</p>
    </header>

    <main id="prototypes-container" class="space-y-12 md:space-y-16 max-w-5xl mx-auto">
        <!-- Prototype iframes will be added here by main.js or manually -->
        <div class="text-center text-gray-500 py-10">
            <p class="text-xl"><i class="fas fa-spinner fa-spin mr-2"></i>正在加载原型...</p>
            <p class="mt-2">如果长时间未显示，请检查控制台错误或确保原型文件已正确创建。</p>
        </div>
    </main>

    <footer class="text-center mt-16 md:mt-20 py-8 border-t border-gray-700">
        <p class="text-gray-500">&copy; {{YYYY}} 归处项目 - HTML原型. 使用 <a href="https://tailwindcss.com/" target="_blank" class="text-serene-blue hover:underline">Tailwind CSS</a> & <a href="https://fontawesome.com/" target="_blank" class="text-serene-blue hover:underline">FontAwesome</a>.</p>
    </footer>

    <script>
        const prototypes = [
            { title: '登录页', src: 'login.html', height: '600px' },
            { title: '注册页', src: 'register.html', height: '600px'},
            { title: '忘记密码', src: 'forgot_password.html', height: '500px'},
            { title: '首页 (仪表盘)', src: 'dashboard.html', height: '800px' },
            { title: '通用主页', src: 'home.html', height: '800px' },
            { title: '创建纪念空间', src: 'create_memorial.html', height: '900px' },
            { title: '纪念空间 (原上传资料页)', src: 'memorial_space.html', height: '1000px' },
            { title: '场景选择', src: 'scene_selection.html', height: '700px'},
            { title: '祭奠流程', src: 'worship.html', height: '800px'},
            { title: 'AI 修复', src: 'ai_repair.html', height: '700px'},
            { title: '家族树', src: 'family_tree.html', height: '800px'},
            { title: '商店', src: 'store.html', height: '800px'},
            { title: '设置页', src: 'settings.html', height: '750px' },
            // { title: '用户个人资料页', src: 'profile.html', height: '700px' }, // profile.html 不在桌面原型目录, 存在于 mobile/profile.html
            // { title: '纪念空间详情页', src: 'memorial_detail.html', height: '1000px' }, // memorial_detail.html 不存在
            // { title: 'AI 互动完成页', src: 'completion.html', height: '600px' }, // completion.html 存在，可按需添加
        ];

        const container = document.getElementById('prototypes-container');

        function loadPrototypes() {
            if (prototypes.length === 0) {
                container.innerHTML = '<div class="text-center text-gray-500 py-10"><p class="text-xl"><i class="fas fa-folder-open mr-2"></i>暂无原型页面配置。</p><p class="mt-2">请在 `index.html` 的 `prototypes` 数组中添加页面信息。</p></div>';
                return;
            }
            container.innerHTML = ''; // Clear loading message

            prototypes.forEach(proto => {
                const prototypeWrapper = document.createElement('div');
                prototypeWrapper.className = 'prototype-frame-container bg-gray-800'; // Added bg-gray-800 for the container itself

                const titleDiv = document.createElement('div');
                titleDiv.className = 'prototype-title';
                titleDiv.innerHTML = `<i class="fas fa-file-alt mr-2"></i> ${proto.title}`;

                const browserMockupDiv = document.createElement('div');
                browserMockupDiv.className = 'browser-mockup';

                const browserHeaderDiv = document.createElement('div');
                browserHeaderDiv.className = 'browser-header';
                browserHeaderDiv.innerHTML = `
                    <div class="browser-dots flex items-center">
                        <span class="bg-red-500"></span>
                        <span class="bg-yellow-400"></span>
                        <span class="bg-green-500"></span>
                    </div>
                    <div class="browser-address-bar truncate">https://yunian.com/${proto.src.replace('.html','')}</div>
                `;

                const iframe = document.createElement('iframe');
                iframe.src = proto.src;
                iframe.className = 'prototype-iframe';
                iframe.style.height = proto.height || '75vh';
                iframe.title = proto.title;
                iframe.setAttribute('loading', 'lazy'); // Lazy load iframes
                // Consider adding sandbox attributes for security if these were untrusted
                // iframe.sandbox = 'allow-scripts allow-same-origin';

                browserMockupDiv.appendChild(browserHeaderDiv);
                browserMockupDiv.appendChild(iframe);
                
                prototypeWrapper.appendChild(titleDiv);
                prototypeWrapper.appendChild(browserMockupDiv);
                container.appendChild(prototypeWrapper);
            });
        }

        // Load prototypes on page load
        document.addEventListener('DOMContentLoaded', () => {
            loadPrototypes(); 
            // Replace {{YYYY}} in footer
            const footer = document.querySelector('footer');
            if (footer) {
                footer.innerHTML = footer.innerHTML.replace('{{YYYY}}', new Date().getFullYear());
            }
        });
    </script>
</body>
</html>