#!/bin/bash

# 归处在线祭祀平台 - 批量添加暗黑模式支持脚本
# 此脚本会为所有HTML页面添加暗黑模式支持

# 获取所有HTML文件（除了index.html）
HTML_FILES=$(find . -name "*.html" -not -name "index.html")

# 遍历所有HTML文件
for file in $HTML_FILES; do
  echo "正在处理: $file"
  
  # 1. 添加class="light"到html标签
  sed -i '' 's/<html lang="zh-CN">/<html lang="zh-CN" class="light">/' "$file"
  
  # 2. 添加dark-mode.js脚本
  sed -i '' 's/<script src="https:\/\/cdn.tailwindcss.com"><\/script>/<script src="https:\/\/cdn.tailwindcss.com"><\/script>\n    <script src="dark-mode.js"><\/script>/' "$file"
  
  # 3. 添加暗黑模式初始化脚本到body标签后
  sed -i '' 's/<body \(.*\)>/<body \1>\n    <!-- 暗黑模式接收器 -->\n    <script>\n        \/\/ 检查本地存储中的主题偏好\n        const savedTheme = localStorage.getItem(\'theme\');\n        if (savedTheme === \'dark\') {\n            document.documentElement.classList.add(\'dark\');\n            document.body.classList.add(\'dark-mode\');\n        }\n    <\/script>/' "$file"
  
  # 4. 为常见的白色背景元素添加暗黑模式类
  sed -i '' 's/class="bg-white /class="bg-white dark:bg-gray-800 /g' "$file"
  sed -i '' 's/class="text-gray-800 /class="text-gray-800 dark:text-gray-200 /g' "$file"
  sed -i '' 's/class="text-gray-600 /class="text-gray-600 dark:text-gray-400 /g' "$file"
  sed -i '' 's/class="text-indigo-800 /class="text-indigo-800 dark:text-indigo-400 /g' "$file"
  sed -i '' 's/class="text-indigo-600 /class="text-indigo-600 dark:text-indigo-500 /g' "$file"
  sed -i '' 's/class="border-gray-100 /class="border-gray-100 dark:border-gray-700 /g' "$file"
  sed -i '' 's/class="bg-gray-50 /class="bg-gray-50 dark:bg-gray-900 /g' "$file"
  
  echo "完成: $file"
done

echo "所有页面已更新完成！"