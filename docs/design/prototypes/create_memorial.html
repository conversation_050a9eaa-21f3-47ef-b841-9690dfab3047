<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建纪念空间 - 归处</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'dark-space-gray': '#333333',
                        'serene-blue': '#4A90E2',
                        'warm-gold': '#F5A623',
                        'moonlight-white': '#F8F8F8',
                        'light-gray': '#CCCCCC',
                        'medium-gray': '#999999',
                        'dark-gray': '#666666',
                        'success-green': '#2ECC71',
                        'warning-orange': '#F39C12',
                        'error-red': '#E74C3C',
                        'app-bg-dark': '#111827',      // Tailwind gray-900
                        'card-bg-dark': '#1f2937',      // Tailwind gray-800
                        'input-bg-dark': '#374151',     // Tailwind gray-700
                        'text-dark-primary': '#F8F8F8', // Moonlight White
                        'text-dark-secondary': '#9ca3af', // Tailwind gray-400
                        'border-dark': '#374151',    // Tailwind gray-700
                    }
                }
            }
        }
    </script>
    <style>
        body {
            overscroll-behavior-y: contain; /* Prevents pull-to-refresh or other scroll boundary effects on the body itself */
        }
        .form-input {
            @apply w-full bg-input-bg-dark border border-border-dark text-text-dark-primary rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-serene-blue focus:border-transparent placeholder-text-dark-secondary;
        }
        .form-label {
            @apply block text-sm font-medium text-text-dark-secondary mb-1;
        }
        .form-select {
            @apply w-full bg-input-bg-dark border border-border-dark text-text-dark-primary rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-serene-blue focus:border-transparent;
        }
        .btn-primary {
            @apply bg-serene-blue hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-serene-blue focus:ring-opacity-50;
        }
        .btn-secondary {
            @apply bg-dark-gray hover:bg-gray-600 text-moonlight-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-dark-gray focus:ring-opacity-50;
        }
    </style>
</head>
<body class="bg-app-bg-dark text-text-dark-primary min-h-screen flex flex-col items-center justify-center p-4 sm:p-6">

    <div class="w-full max-w-3xl bg-card-bg-dark p-6 sm:p-8 rounded-xl shadow-2xl">
        <header class="mb-8 text-center">
            <h1 class="text-3xl font-bold text-serene-blue"><i class="fas fa-plus-circle mr-2"></i>创建新的纪念空间</h1>
            <p class="text-text-dark-secondary mt-2">请填写以下信息，为逝者创建一个专属的数字纪念空间。</p>
        </header>

        <form action="#" method="POST" class="space-y-6">
            <!-- 基本信息 -->
            <section>
                <h2 class="text-xl font-semibold text-moonlight-white border-b border-border-dark pb-2 mb-4">基本信息</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="deceased-name" class="form-label">逝者姓名</label>
                        <input type="text" name="deceased-name" id="deceased-name" class="form-input" placeholder="例如：李明" required>
                    </div>
                    <div>
                        <label for="gender" class="form-label">性别</label>
                        <select id="gender" name="gender" class="form-select">
                            <option value="male">男</option>
                            <option value="female">女</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                    <div>
                        <label for="birth-date" class="form-label">出生日期</label>
                        <input type="date" name="birth-date" id="birth-date" class="form-input">
                    </div>
                    <div>
                        <label for="death-date" class="form-label">逝世日期</label>
                        <input type="date" name="death-date" id="death-date" class="form-input">
                    </div>
                    <div class="md:col-span-2">
                        <label for="birth-place" class="form-label">籍贯</label>
                        <input type="text" name="birth-place" id="birth-place" class="form-input" placeholder="例如：北京">
                    </div>
                </div>
            </section>

            <!-- 生平简介 -->
            <section>
                <h2 class="text-xl font-semibold text-moonlight-white border-b border-border-dark pb-2 mb-4">生平简介</h2>
                <div>
                    <label for="biography" class="form-label">简介内容 (支持Markdown)</label>
                    <textarea id="biography" name="biography" rows="6" class="form-input" placeholder="请在此处填写逝者的生平事迹、重要经历、性格特点等..."></textarea>
                </div>
            </section>

            <!-- 封面图片 -->
            <section>
                <h2 class="text-xl font-semibold text-moonlight-white border-b border-border-dark pb-2 mb-4">封面图片</h2>
                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-border-dark border-dashed rounded-md bg-input-bg-dark/30">
                    <div class="space-y-1 text-center">
                        <!-- Image source: https://images.unsplash.com/photo-1506744038136-46273834b3fb (example) -->
                        <img id="cover-image-preview" src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8bGFuZHNjYXBlfGVufDB8fDB8fHww&auto=format&fit=crop&w=600&q=60" alt="封面图片预览" class="mx-auto h-32 w-auto object-cover rounded-md mb-4 opacity-75">
                        <div class="flex text-sm text-text-dark-secondary">
                            <label for="cover-image-upload" class="relative cursor-pointer bg-serene-blue/20 hover:bg-serene-blue/30 rounded-md font-medium text-serene-blue hover:text-blue-400 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-offset-input-bg-dark focus-within:ring-serene-blue px-3 py-1.5">
                                <span>上传文件</span>
                                <input id="cover-image-upload" name="cover-image-upload" type="file" class="sr-only" accept="image/*">
                            </label>
                            <p class="pl-1">或拖拽到此处</p>
                        </div>
                        <p class="text-xs text-text-dark-secondary">PNG, JPG, GIF, WEBP, 不超过 10MB</p>
                    </div>
                </div>
            </section>

            <!-- 空间设置 -->
            <section>
                <h2 class="text-xl font-semibold text-moonlight-white border-b border-border-dark pb-2 mb-4">空间设置</h2>
                <div class="space-y-4">
                    <div>
                        <label for="privacy-setting" class="form-label">可见性</label>
                        <select id="privacy-setting" name="privacy-setting" class="form-select">
                            <option value="public">公开 (任何人可见)</option>
                            <option value="private">私密 (凭密码访问)</option>
                            <option value="friends">好友可见 (需登录)</option>
                        </select>
                    </div>
                    <div id="password-field" class="hidden">
                        <label for="access-password" class="form-label">访问密码 (设为私密时)</label>
                        <input type="password" name="access-password" id="access-password" class="form-input" placeholder="请输入至少6位密码">
                    </div>
                </div>
            </section>

            <!-- 操作按钮 -->
            <div class="pt-6 border-t border-border-dark flex items-center justify-end space-x-4">
                <button type="button" class="btn-secondary">取消</button>
                <button type="submit" class="btn-primary"><i class="fas fa-check mr-2"></i>确认创建</button>
            </div>
        </form>
    </div>

    <script>
        // Script to show/hide password field based on privacy setting
        const privacySetting = document.getElementById('privacy-setting');
        const passwordField = document.getElementById('password-field');
        privacySetting.addEventListener('change', function() {
            if (this.value === 'private') {
                passwordField.classList.remove('hidden');
            } else {
                passwordField.classList.add('hidden');
            }
        });

        // Script for image preview
        const coverImageUpload = document.getElementById('cover-image-upload');
        const coverImagePreview = document.getElementById('cover-image-preview');
        const defaultImageSrc = coverImagePreview.src; // Store default image

        coverImageUpload.addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    coverImagePreview.src = e.target.result;
                }
                reader.readAsDataURL(file);
            } else {
                // If no file is selected (e.g., user cancels file dialog), revert to default or clear
                // For now, let's revert to default if one was set, or clear if not.
                // coverImagePreview.src = defaultImageSrc; // Or some placeholder
            }
        });
    </script>
</body>
</html>