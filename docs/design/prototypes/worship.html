<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>祭拜互动 - 归处在线祭祀平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="dark-mode.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }
        .sidebar {
            background-color: #fff;
            border-right: 1px solid #e5e7eb;
            height: calc(100vh - 64px); /* 假设顶部导航栏高度为64px */
        }
        .dark-mode .sidebar {
            background-color: #1a1a1a;
            border-right: 1px solid #333;
        }
        .sidebar-item {
            border-radius: 0.5rem;
            transition: all 0.2s;
        }
        .sidebar-item:hover {
            background-color: #f3f4f6;
        }
        .dark-mode .sidebar-item:hover {
            background-color: #2d2d2d;
        }
        .sidebar-item.active {
            background-color: #e0e7ff;
            color: #4f46e5;
        }
        .dark-mode .sidebar-item.active {
            background-color: rgba(79, 70, 229, 0.2);
            color: #818cf8;
        }
        .interactive-item {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .interactive-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="flex flex-col min-h-screen">
    <!-- 暗黑模式接收器 -->
    <script>
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.documentElement.classList.add('dark');
            document.body.classList.add('dark-mode');
        }
    </script>

    <!-- 顶部导航栏 -->
    <header class="bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <i class="fas fa-pray text-indigo-600 text-2xl mr-3"></i>
                <span class="text-xl font-bold text-indigo-800 dark:text-indigo-400">归处</span>
            </div>
            <nav class="hidden md:flex space-x-4">
                <a href="index.html#home" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">首页</a>
                <a href="dashboard.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">我的纪念馆</a>
                <a href="#" class="text-indigo-600 dark:text-indigo-400 font-semibold">祭拜互动</a>
                <a href="store.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">祭品商城</a>
                <a href="family_tree.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">家族树</a>
            </nav>
            <div class="flex items-center space-x-4">
                <button id="theme-toggle-button" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                    <i class="fas fa-sun"></i>
                </button>
                <div class="relative">
                    <button class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                        <i class="fas fa-bell text-xl"></i>
                    </button>
                </div>
                <a href="login.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">登录</a>
                <a href="register.html" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium">注册</a>
            </div>
        </div>
    </header>

    <!-- 主体内容 -->
    <main class="flex-grow container mx-auto px-4 py-8">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 dark:text-white mb-4">祭拜互动体验</h1>
            <p class="text-lg text-gray-600 dark:text-gray-300">选择一种方式，寄托您的哀思与敬意。</p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 敬献鲜花 -->
            <div class="interactive-item bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
                <!-- 图片来源: https://images.unsplash.com/photo-1527066575791-0965fed54658 (Unsplash) -->
                <img src="https://images.unsplash.com/photo-1527066575791-0965fed54658?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Zmxvd2VycyUyMGJvdXF1ZXR8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=300&q=60" alt="鲜花" class="w-32 h-32 mx-auto rounded-full mb-4 object-cover">
                <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-2">敬献鲜花</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">选择一束美丽的鲜花，表达您的思念之情。</p>
                <button class="bg-indigo-500 hover:bg-indigo-600 text-white font-semibold py-2 px-4 rounded-lg transition duration-300">
                    <i class="fas fa-spa mr-2"></i>选择鲜花
                </button>
            </div>

            <!-- 点燃香烛 -->
            <div class="interactive-item bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
                <!-- 图片来源: https://images.unsplash.com/photo-1588670938099-58e0ff015a5b (Unsplash) -->
                <img src="https://images.unsplash.com/photo-1588670938099-58e0ff015a5b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8Y2FuZGxlc3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=300&q=60" alt="香烛" class="w-32 h-32 mx-auto rounded-full mb-4 object-cover">
                <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-2">点燃香烛</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">点燃一炷清香或一对明烛，祈愿逝者安息。</p>
                <button class="bg-amber-500 hover:bg-amber-600 text-white font-semibold py-2 px-4 rounded-lg transition duration-300">
                    <i class="fas fa-fire mr-2"></i>点燃香烛
                </button>
            </div>

            <!-- 敬奉供品 -->
            <div class="interactive-item bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
                <!-- 图片来源: https://images.unsplash.com/photo-1598023003006-9668c6ff7f1a (Unsplash - 水果) -->
                <img src="https://images.unsplash.com/photo-1598023003006-9668c6ff7f1a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGZydWl0JTIwYmFza2V0fGVufDB8fDB8fHww&auto=format&fit=crop&w=300&q=60" alt="供品" class="w-32 h-32 mx-auto rounded-full mb-4 object-cover">
                <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-2">敬奉供品</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">选择水果、点心等供品，表达您的敬意。</p>
                <button class="bg-lime-500 hover:bg-lime-600 text-white font-semibold py-2 px-4 rounded-lg transition duration-300">
                    <i class="fas fa-apple-alt mr-2"></i>选择供品
                </button>
            </div>

            <!-- 留言祈福 -->
            <div class="interactive-item bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
                <!-- 图片来源: https://images.unsplash.com/photo-1516467508483-a7212fe43291 (Unsplash - 纸笔) -->
                <img src="https://images.unsplash.com/photo-1516467508483-a7212fe43291?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8bm90ZXBhZCUyMGFuZCUyMHBlbnxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=300&q=60" alt="留言" class="w-32 h-32 mx-auto rounded-full mb-4 object-cover">
                <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-2">留言祈福</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">写下您的思念与祝福，让心意永存。</p>
                <button class="bg-sky-500 hover:bg-sky-600 text-white font-semibold py-2 px-4 rounded-lg transition duration-300">
                    <i class="fas fa-pencil-alt mr-2"></i>写下留言
                </button>
            </div>

            <!-- 播放音乐 -->
            <div class="interactive-item bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center">
                <!-- 图片来源: https://images.unsplash.com/photo-1511379938547-c1f69419868d (Unsplash - 耳机) -->
                <img src="https://images.unsplash.com/photo-1511379938547-c1f69419868d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8bXVzaWN8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=300&q=60" alt="音乐" class="w-32 h-32 mx-auto rounded-full mb-4 object-cover">
                <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-2">播放追思音乐</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">选择一首宁静的音乐，在旋律中缅怀。</p>
                <button class="bg-purple-500 hover:bg-purple-600 text-white font-semibold py-2 px-4 rounded-lg transition duration-300">
                    <i class="fas fa-music mr-2"></i>选择音乐
                </button>
            </div>

            <!-- 虚拟祭扫 (3D场景入口) -->
            <div class="interactive-item bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center md:col-span-1 lg:col-span-1">
                 <!-- 图片来源: https://images.unsplash.com/photo-1612287230202-95aaa7e84e8f (Unsplash - 3D 场景示意) -->
                <img src="https://images.unsplash.com/photo-1612287230202-95aaa7e84e8f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8M2QlMjBzY2VuZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=300&q=60" alt="3D祭扫" class="w-32 h-32 mx-auto rounded-full mb-4 object-cover">
                <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-2">进入3D纪念堂</h3>
                <p class="text-gray-500 dark:text-gray-400 mb-4">沉浸式体验，身临其境进行虚拟祭扫。</p>
                <button class="bg-teal-500 hover:bg-teal-600 text-white font-semibold py-2 px-4 rounded-lg transition duration-300">
                    <i class="fas fa-vr-cardboard mr-2"></i>进入场景
                </button>
            </div>
        </div>

    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400 py-8 text-center">
        <div class="container mx-auto">
            <p>&copy; 2024 归处在线祭祀平台. 保留所有权利.</p>
            <p class="text-sm mt-2">用心连接思念，科技传承记忆。</p>
        </div>
    </footer>

    <script>
        // 暗黑模式切换逻辑
        const themeToggleButton = document.getElementById('theme-toggle-button');
        const htmlElement = document.documentElement;
        const bodyElement = document.body;

        // 初始化按钮图标
        if (localStorage.getItem('theme') === 'dark') {
            htmlElement.classList.add('dark');
            bodyElement.classList.add('dark-mode');
            themeToggleButton.innerHTML = '<i class="fas fa-moon"></i>';
        } else {
            themeToggleButton.innerHTML = '<i class="fas fa-sun"></i>';
        }

        themeToggleButton.addEventListener('click', () => {
            if (htmlElement.classList.contains('dark')) {
                htmlElement.classList.remove('dark');
                bodyElement.classList.remove('dark-mode');
                localStorage.setItem('theme', 'light');
                themeToggleButton.innerHTML = '<i class="fas fa-sun"></i>';
            } else {
                htmlElement.classList.add('dark');
                bodyElement.classList.add('dark-mode');
                localStorage.setItem('theme', 'dark');
                themeToggleButton.innerHTML = '<i class="fas fa-moon"></i>';
            }
        });

        // 确保 dark-mode.js 脚本在 DOM 加载后执行，如果它依赖 DOM 元素
        // 如果 dark-mode.js 只是设置 localStorage 或 class, 则位置无关紧要
    </script>
</body>
</html>