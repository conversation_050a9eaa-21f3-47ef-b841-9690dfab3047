/**
 * 归处在线祭祀平台 - 暗黑模式支持脚本
 * 此脚本用于子页面接收并应用主页面的暗黑模式设置
 */

// 监听来自父页面的消息
window.addEventListener('message', function(event) {
    // 检查消息是否包含主题信息
    if (event.data && event.data.theme) {
        applyTheme(event.data.theme);
    }
});

// 应用主题
function applyTheme(theme) {
    const htmlElement = document.documentElement;
    
    // 移除所有可能的主题类
    htmlElement.classList.remove('dark', 'light');
    
    // 添加当前主题类
    htmlElement.classList.add(theme);
    
    // 如果是暗黑模式，添加Tailwind暗黑模式类
    if (theme === 'dark') {
        document.body.classList.add('dark-mode');
    } else {
        document.body.classList.remove('dark-mode');
    }
}

// 初始化Tailwind暗黑模式配置
if (typeof tailwind !== 'undefined') {
    try {
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {}
            }
        };
    } catch (e) {
        console.error('无法配置Tailwind暗黑模式:', e);
    }
}

// 添加暗黑模式CSS变量
document.addEventListener('DOMContentLoaded', function() {
    // 创建样式元素
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        /* 暗黑模式基础样式 */
        body.dark-mode,
        .dark body {
            background-color: #121212;
            color: #e0e0e0;
        }
        
        /* 导航栏暗黑模式 */
        .dark .bg-white,
        .dark-mode .bg-white {
            background-color: #1a1a1a !important;
        }
        
        /* 侧边栏暗黑模式 */
        .dark .sidebar,
        .dark-mode .sidebar {
            background-color: #1a1a1a !important;
            border-right-color: #333 !important;
        }
        
        .dark .sidebar-item,
        .dark-mode .sidebar-item {
            color: #e0e0e0 !important;
        }
        
        .dark .sidebar-item:hover,
        .dark-mode .sidebar-item:hover {
            background-color: #2d2d2d !important;
        }
        
        .dark .sidebar-item.active,
        .dark-mode .sidebar-item.active {
            background-color: rgba(79, 70, 229, 0.2) !important;
            color: #818cf8 !important;
        }
        
        /* 文本颜色 */
        .dark .text-gray-800,
        .dark-mode .text-gray-800 {
            color: #e0e0e0 !important;
        }
        
        .dark .text-gray-600,
        .dark-mode .text-gray-600 {
            color: #a0a0a0 !important;
        }
        
        .dark .text-indigo-800,
        .dark-mode .text-indigo-800 {
            color: #a5b4fc !important;
        }
        
        .dark .text-indigo-600,
        .dark-mode .text-indigo-600 {
            color: #818cf8 !important;
        }
        
        /* 卡片和容器 */
        .dark .bg-gray-50,
        .dark-mode .bg-gray-50 {
            background-color: #1a1a1a !important;
        }
        
        .dark .bg-white,
        .dark-mode .bg-white {
            background-color: #1e1e1e !important;
        }
        
        .dark .border-gray-100,
        .dark-mode .border-gray-100,
        .dark .border,
        .dark-mode .border {
            border-color: #333 !important;
        }
        
        .dark .shadow-md,
        .dark-mode .shadow-md {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3) !important;
        }
        
        /* 按钮 */
        .dark .bg-indigo-100,
        .dark-mode .bg-indigo-100 {
            background-color: rgba(99, 102, 241, 0.2) !important;
        }
        
        /* 表单元素 */
        .dark input,
        .dark-mode input,
        .dark textarea,
        .dark-mode textarea,
        .dark select,
        .dark-mode select {
            background-color: #2d2d2d !important;
            border-color: #444 !important;
            color: #e0e0e0 !important;
        }
        
        /* 过渡效果 */
        body,
        .bg-white,
        .bg-gray-50,
        .text-gray-800,
        .text-gray-600,
        .text-indigo-800,
        .text-indigo-600,
        input,
        textarea,
        select,
        .border,
        .shadow-md {
            transition: background-color 0.3s, color 0.3s, border-color 0.3s, box-shadow 0.3s;
        }
    `;
    
    // 添加到文档头部
    document.head.appendChild(styleElement);
    
    // 检查URL参数中是否有主题设置
    const urlParams = new URLSearchParams(window.location.search);
    const themeParam = urlParams.get('theme');
    
    if (themeParam === 'dark' || themeParam === 'light') {
        applyTheme(themeParam);
    }
});