/**
 * 归处在线祭祀平台 - 暗黑模式自动添加脚本
 * 此脚本会自动为所有iframe页面添加暗黑模式支持
 */

document.addEventListener('DOMContentLoaded', function() {
    // 获取所有iframe元素
    const iframes = document.querySelectorAll('iframe');
    
    // 为每个iframe添加加载事件
    iframes.forEach(iframe => {
        iframe.addEventListener('load', function() {
            try {
                // 获取当前主题
                const currentTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
                
                // 向iframe发送主题消息
                iframe.contentWindow.postMessage({ theme: currentTheme }, '*');
                
                // 向iframe注入暗黑模式样式
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                // 检查是否已经注入了暗黑模式样式
                if (!iframeDoc.getElementById('dark-mode-style')) {
                    // 创建样式元素
                    const styleElement = iframeDoc.createElement('style');
                    styleElement.id = 'dark-mode-style';
                    styleElement.textContent = `
                        /* 暗黑模式基础样式 */
                        .dark body, body.dark-mode {
                            background-color: #121212 !important;
                            color: #e0e0e0 !important;
                        }
                        
                        /* 导航栏暗黑模式 */
                        .dark .bg-white, .dark-mode .bg-white {
                            background-color: #1a1a1a !important;
                        }
                        
                        /* 文本颜色 */
                        .dark .text-gray-800, .dark-mode .text-gray-800 {
                            color: #e0e0e0 !important;
                        }
                        
                        .dark .text-gray-600, .dark-mode .text-gray-600 {
                            color: #a0a0a0 !important;
                        }
                        
                        .dark .text-indigo-800, .dark-mode .text-indigo-800 {
                            color: #a5b4fc !important;
                        }
                        
                        .dark .text-indigo-600, .dark-mode .text-indigo-600 {
                            color: #818cf8 !important;
                        }
                        
                        /* 卡片和容器 */
                        .dark .bg-gray-50, .dark-mode .bg-gray-50 {
                            background-color: #1a1a1a !important;
                        }
                        
                        .dark .bg-white, .dark-mode .bg-white {
                            background-color: #1e1e1e !important;
                        }
                        
                        .dark .border-gray-100, .dark-mode .border-gray-100,
                        .dark .border, .dark-mode .border {
                            border-color: #333 !important;
                        }
                        
                        .dark .shadow-md, .dark-mode .shadow-md {
                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3) !important;
                        }
                        
                        /* 按钮 */
                        .dark .bg-indigo-100, .dark-mode .bg-indigo-100 {
                            background-color: rgba(99, 102, 241, 0.2) !important;
                        }
                        
                        /* 表单元素 */
                        .dark input, .dark-mode input,
                        .dark textarea, .dark-mode textarea,
                        .dark select, .dark-mode select {
                            background-color: #2d2d2d !important;
                            border-color: #444 !important;
                            color: #e0e0e0 !important;
                        }
                    `;
                    
                    // 添加到iframe文档头部
                    iframeDoc.head.appendChild(styleElement);
                    
                    // 创建脚本元素，用于接收主题消息
                    const scriptElement = iframeDoc.createElement('script');
                    scriptElement.textContent = `
                        // 监听来自父页面的消息
                        window.addEventListener('message', function(event) {
                            // 检查消息是否包含主题信息
                            if (event.data && event.data.theme) {
                                // 应用主题
                                const htmlElement = document.documentElement;
                                
                                // 移除所有可能的主题类
                                htmlElement.classList.remove('dark', 'light');
                                
                                // 添加当前主题类
                                htmlElement.classList.add(event.data.theme);
                                
                                // 如果是暗黑模式，添加body类
                                if (event.data.theme === 'dark') {
                                    document.body.classList.add('dark-mode');
                                } else {
                                    document.body.classList.remove('dark-mode');
                                }
                            }
                        });
                    `;
                    
                    // 添加到iframe文档
                    iframeDoc.body.appendChild(scriptElement);
                }
                
            } catch (e) {
                console.error('无法向iframe注入暗黑模式:', e);
            }
        });
    });
    
    // 主题切换时同步到所有iframe
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            setTimeout(function() {
                const currentTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
                iframes.forEach(iframe => {
                    try {
                        iframe.contentWindow.postMessage({ theme: currentTheme }, '*');
                    } catch (e) {
                        console.error('无法向iframe发送消息:', e);
                    }
                });
            }, 100); // 短暂延迟确保主题已切换
        });
    }
});