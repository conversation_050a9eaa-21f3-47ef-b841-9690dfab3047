<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择3D场景 - 归处在线祭祀平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="dark-mode.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }
        .sidebar {
            background-color: #fff;
            border-right: 1px solid #e5e7eb;
            height: calc(100vh - 64px);
        }
        .dark-mode .sidebar {
            background-color: #1a1a1a;
            border-right: 1px solid #333;
        }
        .sidebar-item {
            border-radius: 0.5rem;
            transition: all 0.2s;
        }
        .sidebar-item:hover {
            background-color: #f3f4f6;
        }
        .dark-mode .sidebar-item:hover {
            background-color: #2d2d2d;
        }
        .sidebar-item.active {
            background-color: #e0e7ff;
            color: #4f46e5;
        }
        .dark-mode .sidebar-item.active {
            background-color: rgba(79, 70, 229, 0.2);
            color: #818cf8;
        }
        .step-item {
            position: relative;
        }
        .step-item:not(:last-child):after {
            content: '';
            position: absolute;
            top: 15px;
            left: 100%;
            height: 2px;
            width: 100%;
            background-color: #e5e7eb;
            transform: translateX(-50%);
            z-index: 0;
        }
        .step-item.active:not(:last-child):after,
        .step-item.completed:not(:last-child):after {
            background-color: #4f46e5;
        }
        .step-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e5e7eb;
            color: #9ca3af;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            z-index: 1;
            position: relative;
        }
        .step-item.active .step-circle {
            background-color: #4f46e5;
            color: white;
        }
        .step-item.completed .step-circle {
            background-color: #10b981;
            color: white;
        }
        .scene-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .scene-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .scene-card.selected {
            border-color: #4f46e5;
            box-shadow: 0 0 0 2px #4f46e5;
        }
    </style>
</head>
<body>
    <!-- 暗黑模式接收器 -->
    <script>
        // 检查本地存储中的主题偏好
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.documentElement.classList.add('dark');
            document.body.classList.add('dark-mode');
        }
    </script>
    <!-- 顶部导航栏 -->
    <header class="bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <i class="fas fa-pray text-indigo-600 text-2xl mr-3"></i>
                <span class="text-xl font-bold text-indigo-800 dark:text-indigo-400">归处</span>
            </div>
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <button class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-bell text-xl"></i>
                        <span class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full">2</span>
                    </button>
                </div>
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=100&q=80" alt="用户头像" class="w-8 h-8 rounded-full mr-2">
                    <!-- 图片来源：Unsplash -->
                    <span class="text-gray-700">李明</span>
                    <i class="fas fa-chevron-down ml-2 text-gray-500 text-xs"></i>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- 侧边栏 -->
        <aside class="sidebar w-64 p-4 dark:bg-gray-800">
            <nav>
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.html" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-home mr-3"></i>
                            <span>我的纪念空间</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-users mr-3"></i>
                            <span>我的家族</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-sitemap mr-3"></i>
                            <span>族谱管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-magic mr-3"></i>
                            <span>AI服务</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-shopping-cart mr-3"></i>
                            <span>祭品商店</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-cog mr-3"></i>
                            <span>账户设置</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="flex-1 p-6 bg-gray-50">
            <div class="mb-8">
                <h1 class="text-2xl font-bold text-gray-800 mb-4">选择3D场景</h1>
                <p class="text-gray-600">为您的纪念空间选择一个合适的3D场景，营造庄重肃穆的氛围</p>
            </div>

            <!-- 步骤指示器 -->
            <div class="flex justify-between mb-10 px-10">
                <div class="step-item completed flex flex-col items-center w-1/4">
                    <div class="step-circle"><i class="fas fa-check"></i></div>
                    <div class="mt-2 text-sm font-medium text-green-600">基本信息</div>
                </div>
                <div class="step-item active flex flex-col items-center w-1/4">
                    <div class="step-circle">2</div>
                    <div class="mt-2 text-sm font-medium text-indigo-600">选择场景</div>
                </div>
                <div class="step-item flex flex-col items-center w-1/4">
                    <div class="step-circle">3</div>
                    <div class="mt-2 text-sm font-medium text-gray-500">上传资料</div>
                </div>
                <div class="step-item flex flex-col items-center w-1/4">
                    <div class="step-circle">4</div>
                    <div class="mt-2 text-sm font-medium text-gray-500">完成创建</div>
                </div>
            </div>

            <!-- 场景选择 -->
            <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-800">选择场景风格</h2>
                    <div class="flex items-center">
                        <span class="text-sm text-gray-600 mr-2">宗教元素：</span>
                        <select class="py-1 px-3 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 text-sm">
                            <option value="none">无</option>
                            <option value="buddhism">佛教</option>
                            <option value="taoism">道教</option>
                            <option value="christianity">基督教</option>
                            <option value="catholicism">天主教</option>
                            <option value="islam">伊斯兰教</option>
                        </select>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- 场景1：中式陵园 -->
                    <div class="scene-card selected border-2 rounded-xl overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1518623001395-125242310d0c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=600&q=80')">
                            <!-- 图片来源：Unsplash -->
                        </div>
                        <div class="p-4">
                            <div class="flex justify-between items-center mb-2">
                                <h3 class="font-bold text-gray-800">中式陵园</h3>
                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">免费</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">传统中式风格，庄重肃穆，适合大多数祭祀场景</p>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-check-circle text-green-500 mr-1"></i> 已选择
                            </div>
                        </div>
                    </div>
                    
                    <!-- 场景2：西式墓园 -->
                    <div class="scene-card border border-gray-200 rounded-xl overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1527593625869-2e9b8b9a7fb0?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=600&q=80')">
                            <!-- 图片来源：Unsplash -->
                        </div>
                        <div class="p-4">
                            <div class="flex justify-between items-center mb-2">
                                <h3 class="font-bold text-gray-800">西式墓园</h3>
                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">免费</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">西方风格墓园，简约庄严，适合西式祭祀习惯</p>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="far fa-circle mr-1"></i> 点击选择
                            </div>
                        </div>
                    </div>
                    
                    <!-- 场景3：自然山水 -->
                    <div class="scene-card border border-gray-200 rounded-xl overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1501785888041-af3ef285b470?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=600&q=80')">
                            <!-- 图片来源：Unsplash -->
                        </div>
                        <div class="p-4">
                            <div class="flex justify-between items-center mb-2">
                                <h3 class="font-bold text-gray-800">自然山水</h3>
                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">免费</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">回归自然，山水相依，宁静祥和的纪念环境</p>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="far fa-circle mr-1"></i> 点击选择
                            </div>
                        </div>
                    </div>
                    
                    <!-- 场景4：星空 -->
                    <div class="scene-card border border-gray-200 rounded-xl overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1475274047050-1d0c0975c63e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=600&q=80')">
                            <!-- 图片来源：Unsplash -->
                        </div>
                        <div class="p-4">
                            <div class="flex justify-between items-center mb-2">
                                <h3 class="font-bold text-gray-800">星空</h3>
                                <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">高级</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">浩瀚星空，寄托对逝者的思念，象征永恒</p>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="far fa-circle mr-1"></i> 点击选择
                            </div>
                        </div>
                    </div>
                    
                    <!-- 场景5：书房 -->
                    <div class="scene-card border border-gray-200 rounded-xl overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1507842217343-583bb7270b66?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=600&q=80')">
                            <!-- 图片来源：Unsplash -->
                        </div>
                        <div class="p-4">
                            <div class="flex justify-between items-center mb-2">
                                <h3 class="font-bold text-gray-800">书房</h3>
                                <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">高级</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">温馨书房，适合纪念学者、教师等知识分子</p>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="far fa-circle mr-1"></i> 点击选择
                            </div>
                        </div>
                    </div>
                    
                    <!-- 场景6：定制场景 -->
                    <div class="scene-card border border-gray-200 rounded-xl overflow-hidden bg-gray-50 border-dashed">
                        <div class="h-48 flex items-center justify-center">
                            <div class="text-center">
                                <i class="fas fa-plus-circle text-gray-400 text-4xl mb-2"></i>
                                <p class="text-gray-500">定制专属场景</p>
                            </div>
                        </div>
                        <div class="p-4">
                            <div class="flex justify-between items-center mb-2">
                                <h3 class="font-bold text-gray-800">定制场景</h3>
                                <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">付费</span>
                            </div>
                            <p class="text-gray-600 text-sm mb-3">根据您的需求定制专属3D场景，更具个性化</p>
                            <div class="flex items-center text-sm text-indigo-600">
                                <i class="fas fa-crown mr-1"></i> 联系客服定制
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-8">
                    <h3 class="font-bold text-gray-800 mb-4">场景预览</h3>
                    <div class="border border-gray-200 rounded-xl overflow-hidden">
                        <div class="h-80 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1518623001395-125242310d0c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1200&q=80')">
                            <!-- 图片来源：Unsplash -->
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-between mt-10">
                    <a href="create_memorial.html" class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition duration-300">
                        <i class="fas fa-arrow-left mr-2"></i> 上一步
                    </a>
                    <a href="memorial_space.html" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-6 rounded-lg transition duration-300">
                        下一步：上传资料 <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>
        </main>
    </div>
</body>
</html>