<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 归处</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'dark-space-gray': '#333333',
                        'serene-blue': '#4A90E2',
                        'warm-gold': '#F5A623',
                        'moonlight-white': '#F8F8F8',
                        'light-gray': '#CCCCCC',
                        'medium-gray': '#999999',
                        'dark-gray': '#666666',
                        'success-green': '#2ECC71',
                        'warning-orange': '#F39C12',
                        'error-red': '#E74C3C',
                        'app-bg-dark': '#111827', // Tailwind gray-900
                        'card-bg-dark': '#1f2937', // Tailwind gray-800
                        'input-bg-dark': '#374151', // Tailwind gray-700
                        'text-dark-primary': '#F8F8F8', // Moonlight White
                        'text-dark-secondary': '#9ca3af', // Tailwind gray-400
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
    </style>
</head>
<body class="bg-app-bg-dark text-text-dark-primary flex items-center justify-center min-h-screen p-4">
    <div class="w-full max-w-md bg-card-bg-dark shadow-2xl rounded-xl p-8 md:p-12">
        <div class="text-center mb-8">
            <i class="fas fa-leaf text-serene-blue text-5xl mb-3"></i>
            <h1 class="text-3xl font-bold text-moonlight-white">登录归处</h1>
            <p class="text-text-dark-secondary mt-2">继续您的数字传承之旅</p>
        </div>

        <form action="#" method="POST" class="space-y-6">
            <div>
                <label for="email" class="block text-sm font-medium text-text-dark-secondary mb-1">邮箱地址</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-envelope text-gray-400"></i>
                    </div>
                    <input id="email" name="email" type="email" autocomplete="email" required
                           class="w-full pl-10 pr-3 py-3 bg-input-bg-dark border border-gray-600 text-text-dark-primary rounded-lg focus:ring-2 focus:ring-serene-blue focus:border-serene-blue outline-none transition duration-150 ease-in-out"
                           placeholder="<EMAIL>">
                </div>
            </div>

            <div>
                <div class="flex items-center justify-between mb-1">
                    <label for="password" class="block text-sm font-medium text-text-dark-secondary">密码</label>
                    <a href="forgot_password.html" class="text-sm text-serene-blue hover:underline">忘记密码?</a>
                </div>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-lock text-gray-400"></i>
                    </div>
                    <input id="password" name="password" type="password" autocomplete="current-password" required
                           class="w-full pl-10 pr-3 py-3 bg-input-bg-dark border border-gray-600 text-text-dark-primary rounded-lg focus:ring-2 focus:ring-serene-blue focus:border-serene-blue outline-none transition duration-150 ease-in-out"
                           placeholder="请输入密码">
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <input id="remember-me" name="remember-me" type="checkbox"
                           class="h-4 w-4 text-serene-blue bg-gray-700 border-gray-600 rounded focus:ring-serene-blue">
                    <label for="remember-me" class="ml-2 block text-sm text-text-dark-secondary">记住我</label>
                </div>
            </div>

            <div>
                <button type="submit"
                        class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-serene-blue hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-app-bg-dark focus:ring-serene-blue transition duration-150 ease-in-out">
                    <i class="fas fa-sign-in-alt mr-2"></i> 登录
                </button>
            </div>
        </form>

        <p class="mt-8 text-center text-sm text-text-dark-secondary">
            还没有账户? 
            <a href="register.html" class="font-medium text-serene-blue hover:underline">立即注册</a>
        </p>

        <div class="mt-6">
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-600"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-card-bg-dark text-text-dark-secondary">或通过以下方式登录</span>
                </div>
            </div>

            <div class="mt-4 grid grid-cols-1 gap-3">
                <div>
                    <a href="#" class="w-full inline-flex justify-center py-2.5 px-4 border border-gray-600 rounded-lg shadow-sm bg-input-bg-dark text-sm font-medium text-text-dark-secondary hover:bg-gray-600 transition duration-150 ease-in-out">
                        <i class="fab fa-weixin mr-2 text-xl text-green-500"></i> 使用微信登录
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>