<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>归处 App - 创建纪念空间</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'custom-gray-dark': '#1a1a1a',
                        'custom-gray-light': '#f3f4f6',
                        'brand-blue': '#3b82f6',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            overscroll-behavior-y: contain;
        }
        .ios-status-bar {
            height: 44px; 
            background-color: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        .ios-status-bar.light-mode {
            background-color: rgba(243, 244, 246, 0.8);
            color: black;
        }
        .ios-nav-bar {
            height: 44px; 
            background-color: rgba(38, 38, 38, 0.85);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            display: flex;
            justify-content: space-between; /* Changed to space-between for back button */
            align-items: center;
            font-size: 17px;
            font-weight: 600;
            position: fixed;
            top: 44px; 
            left: 0;
            right: 0;
            z-index: 999;
            padding: 0 10px; /* Added padding for nav items */
            border-bottom: 0.5px solid rgba(255,255,255,0.2);
        }
        .ios-nav-bar.light-mode {
            background-color: rgba(249, 249, 249, 0.85);
            color: black;
            border-bottom: 0.5px solid rgba(0,0,0,0.2);
        }
        .ios-nav-bar .nav-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }
        .ios-nav-bar .nav-button {
            color: #3b82f6; /* iOS blue for interactive elements */
            font-size: 16px;
            font-weight: normal;
        }
        .content-area {
            padding-top: 88px; 
            padding-bottom: calc(16px + env(safe-area-inset-bottom)); /* No tab bar, just safe area */
            height: 100vh;
            overflow-y: auto;
        }
        .iphone-frame {
            width: 375px; 
            height: 812px; 
            border: 16px solid black;
            border-radius: 40px;
            position: relative;
            margin: 20px auto;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
            background-color: #1a1a1a;
        }
        .iphone-screen {
            width: 100%;
            height: 100%;
            background-color: #1a1a1a;
            position: relative;
            overflow: hidden;
        }
        .iphone-screen.light-mode {
            background-color: #f3f4f6;
        }
        /* iOS Form Styles */
        .form-group {
            background-color: #2c2c2e; /* Dark mode form group background */
            border-radius: 10px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .form-group.light-mode {
            background-color: white;
        }
        .form-row {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 0.5px solid rgba(255,255,255,0.15);
        }
        .form-group.light-mode .form-row {
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        .form-row:last-child {
            border-bottom: none;
        }
        .form-label {
            width: 100px;
            color: white;
            font-size: 16px;
        }
        .form-group.light-mode .form-label {
            color: black;
        }
        .form-input {
            flex-grow: 1;
            background-color: transparent;
            border: none;
            color: white;
            font-size: 16px;
            text-align: right;
        }
        .form-group.light-mode .form-input {
            color: #8e8e93; /* Light mode placeholder-like color */
        }
        .form-input:focus {
            outline: none;
        }
        .form-input::placeholder {
            color: #5c5c5e; /* Dark mode placeholder */
        }
        .form-group.light-mode .form-input::placeholder {
            color: #c7c7cc; /* Light mode placeholder */
        }
        .form-textarea {
            width: 100%;
            background-color: transparent;
            border: none;
            color: white;
            font-size: 16px;
            padding: 12px 16px;
            min-height: 100px;
            resize: none;
        }
        .form-group.light-mode .form-textarea {
            color: black;
        }
        .form-textarea:focus {
            outline: none;
        }
        .form-textarea::placeholder {
            color: #5c5c5e;
        }
        .form-group.light-mode .form-textarea::placeholder {
            color: #c7c7cc;
        }
        .upload-button-container {
            padding: 16px;
        }
        .upload-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100px;
            height: 100px;
            border: 2px dashed #5c5c5e;
            border-radius: 10px;
            color: #5c5c5e;
            cursor: pointer;
        }
        .form-group.light-mode .upload-button {
            border-color: #c7c7cc;
            color: #c7c7cc;
        }
        .upload-button i {
            font-size: 24px;
            margin-bottom: 8px;
        }
        .upload-button span {
            font-size: 14px;
        }
    </style>
</head>
<body class="bg-custom-gray-dark text-white">

    <div class="iphone-frame">
        <div class="iphone-screen dark" id="appScreen">
            <!-- iOS Status Bar -->
            <div class="ios-status-bar dark" id="statusBar">
                <div><i class="fa-solid fa-signal"></i> LTE</div>
                <div>9:43 AM</div>
                <div><i class="fa-solid fa-battery-half"></i> 50%</div>
            </div>

            <!-- iOS Navigation Bar -->
            <div class="ios-nav-bar dark" id="navBar">
                <button class="nav-button" onclick="history.back()">取消</button>
                <span class="nav-title">创建纪念空间</span>
                <button class="nav-button font-semibold">完成</button>
            </div>

            <!-- Main Content Area -->
            <div class="content-area p-4 bg-custom-gray-dark text-white" id="mainContent">
                
                <div class="form-group dark" id="formGroup1">
                    <div class="form-row">
                        <label for="memorialName" class="form-label">空间名称</label>
                        <input type="text" id="memorialName" class="form-input" placeholder="例如：慈父李明远纪念堂">
                    </div>
                    <div class="form-row">
                        <label for="deceasedName" class="form-label">逝者姓名</label>
                        <input type="text" id="deceasedName" class="form-input" placeholder="逝者全名">
                    </div>
                    <div class="form-row">
                        <label for="birthDate" class="form-label">出生日期</label>
                        <input type="date" id="birthDate" class="form-input" placeholder="选择日期">
                    </div>
                    <div class="form-row">
                        <label for="deathDate" class="form-label">逝世日期</label>
                        <input type="date" id="deathDate" class="form-input" placeholder="选择日期">
                    </div>
                </div>

                <div class="form-group dark" id="formGroup2">
                     <textarea id="description" class="form-textarea" placeholder="空间简介（选填，例如生平事迹、悼词等）"></textarea>
                </div>
                
                <p class="text-sm text-gray-400 mb-2 ml-1">上传封面照片</p>
                <div class="form-group dark upload-button-container" id="formGroup3">
                    <div class="upload-button" onclick="document.getElementById('coverImageUpload').click()">
                        <i class="fa-solid fa-camera"></i>
                        <span>选择照片</span>
                        <input type="file" id="coverImageUpload" class="hidden" accept="image/*">
                    </div>
                    <!-- Placeholder for image preview -->
                    <img id="imagePreview" src="" alt="封面预览" class="mt-2 rounded-md w-full h-48 object-cover hidden">
                    <!-- Image source: User uploaded -->
                </div>

                <!-- Toggle for light/dark mode for demonstration -->
                <button id="themeToggle" class="mt-8 p-2 bg-brand-blue text-white rounded-lg fixed bottom-5 right-5 z-[1001]">
                    <i class="fa-solid fa-sun"></i> / <i class="fa-solid fa-moon"></i>
                </button>
            </div>
        </div>
    </div>

    <script>
        const themeToggle = document.getElementById('themeToggle');
        const appScreen = document.getElementById('appScreen');
        const statusBar = document.getElementById('statusBar');
        const navBar = document.getElementById('navBar');
        const mainContent = document.getElementById('mainContent');
        const formGroups = [document.getElementById('formGroup1'), document.getElementById('formGroup2'), document.getElementById('formGroup3')];
        const inputs = document.querySelectorAll('.form-input, .form-textarea');
        const labels = document.querySelectorAll('.form-label');
        const uploadButton = document.querySelector('.upload-button');

        themeToggle.addEventListener('click', () => {
            document.documentElement.classList.toggle('dark');
            appScreen.classList.toggle('dark');
            appScreen.classList.toggle('light-mode');
            statusBar.classList.toggle('dark');
            statusBar.classList.toggle('light-mode');
            navBar.classList.toggle('dark');
            navBar.classList.toggle('light-mode');
            mainContent.classList.toggle('bg-custom-gray-dark');
            mainContent.classList.toggle('text-white');
            mainContent.classList.toggle('bg-custom-gray-light');
            mainContent.classList.toggle('text-black');
            
            formGroups.forEach(group => {
                group.classList.toggle('dark');
                group.classList.toggle('light-mode');
            });
            inputs.forEach(input => {
                input.classList.toggle('dark');
                input.classList.toggle('light-mode');
            });
            labels.forEach(label => {
                // No specific class toggle for labels, color is inherited or directly set
            });
            if (uploadButton) {
                uploadButton.classList.toggle('dark');
                uploadButton.classList.toggle('light-mode');
            }
        });

        // Image preview logic
        const coverImageUpload = document.getElementById('coverImageUpload');
        const imagePreview = document.getElementById('imagePreview');
        coverImageUpload.addEventListener('change', function(event) {
            if (event.target.files && event.target.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    imagePreview.src = e.target.result;
                    imagePreview.classList.remove('hidden');
                }
                reader.readAsDataURL(event.target.files[0]);
            }
        });
    </script>
</body>
</html>