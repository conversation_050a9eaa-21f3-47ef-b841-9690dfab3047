<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>归处 App - 注册</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'custom-gray-dark': '#1a1a1a',
                        'custom-gray-light': '#f3f4f6',
                        'brand-blue': '#3b82f6',
                        'ios-gray-bg-dark': '#000000',
                        'ios-gray-bg-light': '#f2f2f7',
                        'ios-input-dark': '#1c1c1e',
                        'ios-input-light': '#ffffff',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            overscroll-behavior-y: contain;
        }
        .ios-status-bar {
            height: 44px; 
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        .ios-status-bar.light-mode {
            color: black;
        }
        .ios-nav-bar {
            height: 44px; 
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            display: flex;
            justify-content: space-between; 
            align-items: center;
            font-size: 17px;
            font-weight: 600;
            position: fixed;
            top: 44px; 
            left: 0;
            right: 0;
            z-index: 999;
            padding: 0 10px; 
        }
        .ios-nav-bar.light-mode {
            background-color: rgba(242, 242, 247, 0.8);
            color: black;
        }
        .ios-nav-bar .nav-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }
        .ios-nav-bar .nav-button {
            color: #3b82f6; 
            font-size: 16px;
            font-weight: normal;
            cursor: pointer;
        }
        .content-area {
            padding-top: 88px; /* Status bar + Nav bar */
            padding-bottom: env(safe-area-inset-bottom);
            height: 100vh;
            overflow-y: auto;
        }
        .iphone-frame {
            width: 375px; 
            height: 812px; 
            border: 16px solid black;
            border-radius: 40px;
            position: relative;
            margin: 20px auto;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
            background-color: var(--ios-gray-bg-dark, #000000);
        }
        .iphone-frame.light-mode {
            background-color: var(--ios-gray-bg-light, #f2f2f7);
        }
        .iphone-screen {
            width: 100%;
            height: 100%;
            background-color: var(--ios-gray-bg-dark, #000000);
            position: relative;
            overflow: hidden;
        }
        .iphone-screen.light-mode {
            background-color: var(--ios-gray-bg-light, #f2f2f7);
        }
        .register-form-container {
            padding: 24px 16px;
            width: 100%;
        }
        .form-input-ios {
            width: 100%;
            padding: 12px 16px;
            font-size: 16px;
            border: 0.5px solid #545458;
            border-radius: 8px;
            background-color: var(--ios-input-dark, #1c1c1e);
            color: white;
            margin-bottom: 16px;
        }
        .iphone-screen.light-mode .form-input-ios {
            background-color: var(--ios-input-light, #ffffff);
            border-color: #c6c6c8;
            color: black;
        }
        .form-input-ios::placeholder {
            color: #8e8e93;
        }
        .register-button {
            width: 100%;
            padding: 14px;
            font-size: 17px;
            font-weight: 500;
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 24px;
        }
        .register-button:disabled {
            background-color: #8e8e93;
            opacity: 0.7;
        }
        .terms-text {
            font-size: 12px;
            color: #8e8e93;
            text-align: center;
            margin-top: 16px;
        }
        .terms-text a {
            color: #3b82f6;
            text-decoration: none;
        }
    </style>
</head>
<body class="bg-custom-gray-dark">

    <div class="iphone-frame dark" id="iphoneFrame">
        <div class="iphone-screen dark" id="appScreen">
            <!-- iOS Status Bar -->
            <div class="ios-status-bar dark" id="statusBar">
                <div><i class="fa-solid fa-signal"></i> LTE</div>
                <div>9:40 AM</div>
                <div><i class="fa-solid fa-battery-full"></i> 100%</div>
            </div>

            <!-- iOS Navigation Bar -->
            <div class="ios-nav-bar dark" id="navBar">
                <button class="nav-button" onclick="window.location.href='mobile_login.html'">取消</button>
                <span class="nav-title">创建账户</span>
                <!-- <button class="nav-button font-semibold">完成</button> -->
            </div>

            <!-- Main Content Area -->
            <div class="content-area">
                <div class="register-form-container">
                    <input type="text" id="nickname" class="form-input-ios" placeholder="昵称">
                    <input type="email" id="email" class="form-input-ios" placeholder="邮箱地址或手机号">
                    <input type="password" id="password" class="form-input-ios" placeholder="设置密码 (至少8位)">
                    <input type="password" id="confirmPassword" class="form-input-ios" placeholder="确认密码">
                    
                    <button id="registerButton" class="register-button">注册</button>

                    <p class="terms-text">
                        点击“注册”即表示您同意归处的
                        <a href="#">服务条款</a> 和 <a href="#">隐私政策</a>。
                    </p>
                </div>
            </div>
            <!-- Toggle for light/dark mode for demonstration -->
            <button id="themeToggle" class="p-2 bg-gray-500 text-white rounded-full fixed bottom-5 right-5 z-[1001]">
                <i class="fa-solid fa-sun"></i> / <i class="fa-solid fa-moon"></i>
            </button>
        </div>
    </div>

    <script>
        const themeToggle = document.getElementById('themeToggle');
        const iphoneFrame = document.getElementById('iphoneFrame');
        const appScreen = document.getElementById('appScreen');
        const statusBar = document.getElementById('statusBar');
        const navBar = document.getElementById('navBar');
        const inputs = document.querySelectorAll('.form-input-ios');

        function applyTheme() {
            const isDarkMode = document.documentElement.classList.contains('dark');
            iphoneFrame.className = 'iphone-frame ' + (isDarkMode ? 'dark' : 'light-mode');
            appScreen.className = 'iphone-screen ' + (isDarkMode ? 'dark' : 'light-mode');
            statusBar.className = 'ios-status-bar ' + (isDarkMode ? 'dark' : 'light-mode');
            navBar.className = 'ios-nav-bar ' + (isDarkMode ? 'dark' : 'light-mode');
        }

        themeToggle.addEventListener('click', () => {
            document.documentElement.classList.toggle('dark');
            applyTheme();
        });
        
        // Apply theme on initial load
        applyTheme();
    </script>
</body>
</html>