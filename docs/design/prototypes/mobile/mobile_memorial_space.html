<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>归处 App - 纪念空间</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'custom-gray-dark': '#1a1a1a',
                        'custom-gray-light': '#f3f4f6',
                        'brand-blue': '#3b82f6',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            overscroll-behavior-y: contain;
        }
        .ios-status-bar {
            height: 44px; 
            background-color: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        .ios-status-bar.light-mode {
            background-color: rgba(243, 244, 246, 0.8);
            color: black;
        }
        .ios-nav-bar {
            height: 44px; 
            background-color: rgba(38, 38, 38, 0.85);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 17px;
            font-weight: 600;
            position: fixed;
            top: 44px; 
            left: 0;
            right: 0;
            z-index: 999;
            border-bottom: 0.5px solid rgba(255,255,255,0.2);
        }
        .ios-nav-bar.light-mode {
            background-color: rgba(249, 249, 249, 0.85);
            color: black;
            border-bottom: 0.5px solid rgba(0,0,0,0.2);
        }
        .ios-tab-bar {
            height: 50px; 
            background-color: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: #8e8e93; 
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 999;
            padding-bottom: env(safe-area-inset-bottom);
            border-top: 0.5px solid rgba(255,255,255,0.2);
        }
        .ios-tab-bar.light-mode {
            background-color: rgba(243, 244, 246, 0.8);
            color: #8e8e93;
            border-top: 0.5px solid rgba(0,0,0,0.2);
        }
        .ios-tab-bar-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex-grow: 1;
            font-size: 10px;
            padding-top: 4px;
        }
        .ios-tab-bar-item.active {
            color: #3b82f6;
        }
        .ios-tab-bar-item i {
            font-size: 20px;
            margin-bottom: 2px;
        }
        .content-area {
            padding-top: 88px; 
            padding-bottom: calc(50px + env(safe-area-inset-bottom));
            height: 100vh;
            overflow-y: auto;
        }
        .iphone-frame {
            width: 375px; 
            height: 812px; 
            border: 16px solid black;
            border-radius: 40px;
            position: relative;
            margin: 20px auto;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
            background-color: #1a1a1a;
        }
        .iphone-screen {
            width: 100%;
            height: 100%;
            background-color: #1a1a1a;
            position: relative;
            overflow: hidden;
        }
        .iphone-screen.light-mode {
            background-color: #f3f4f6;
        }
        .search-bar-ios {
            padding: 8px 16px;
            background-color: #2c2c2e; /* Dark mode search bar background */
        }
        .search-bar-ios.light-mode {
            background-color: #efeff4; /* Light mode search bar background */
        }
        .search-bar-ios input {
            background-color: #3a3a3c; /* Dark mode input field */
            color: white;
            border-radius: 10px;
            padding: 8px 12px 8px 30px; /* Padding for icon */
            width: 100%;
            font-size: 16px;
            border: none;
        }
        .search-bar-ios.light-mode input {
            background-color: #e0e0e5; /* Light mode input field */
            color: black;
        }
        .search-bar-ios .search-icon {
            position: absolute;
            left: 26px; /* Adjust based on padding */
            top: 50%;
            transform: translateY(-50%);
            color: #8e8e93;
        }
    </style>
</head>
<body class="bg-custom-gray-dark text-white">

    <div class="iphone-frame">
        <div class="iphone-screen dark" id="appScreen">
            <!-- iOS Status Bar -->
            <div class="ios-status-bar dark" id="statusBar">
                <div><i class="fa-solid fa-signal"></i> LTE</div>
                <div>9:42 AM</div>
                <div><i class="fa-solid fa-battery-three-quarters"></i> 75%</div>
            </div>

            <!-- iOS Navigation Bar -->
            <div class="ios-nav-bar dark" id="navBar">
                纪念空间
            </div>

            <!-- Main Content Area -->
            <div class="content-area bg-custom-gray-dark text-white" id="mainContent">
                <!-- Search Bar -->
                <div class="search-bar-ios dark sticky top-0 z-10" id="searchBarContainer">
                    <div class="relative">
                        <i class="fa-solid fa-magnifying-glass search-icon"></i>
                        <input type="search" placeholder="搜索纪念空间" class="focus:outline-none">
                    </div>
                </div>

                <div class="p-4 space-y-3">
                    <!-- Memorial Space Item 1 -->
                    <div class="bg-gray-700 rounded-lg shadow overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1518806118471-f28b20a1d79d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Grandfather's Memorial" class="w-full h-40 object-cover">
                        <!-- Image source: https://unsplash.com/photos/a-man-standing-on-top-of-a-mountain-at-sunset-pXQeNqZ7XLA -->
                        <div class="p-3">
                            <h3 class="text-lg font-semibold">慈父李明远纪念堂</h3>
                            <p class="text-sm text-gray-400">1950 - 2020</p>
                            <p class="text-xs text-gray-500 mt-1">创建者：李小华</p>
                            <div class="mt-2 flex items-center justify-between">
                                <span class="text-xs text-gray-400"><i class="fa-solid fa-users mr-1"></i> 1.2k 亲友</span>
                                <button class="text-xs bg-brand-blue text-white px-3 py-1 rounded-md hover:bg-blue-500">进入空间</button>
                            </div>
                        </div>
                    </div>

                    <!-- Memorial Space Item 2 -->
                    <div class="bg-gray-700 rounded-lg shadow overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1470252649378-9c29740c9fa8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Grandmother's Memorial" class="w-full h-40 object-cover">
                        <!-- Image source: https://unsplash.com/photos/green-leafed-trees-during-foggy-time-2q_frVRXWfQ -->
                        <div class="p-3">
                            <h3 class="text-lg font-semibold">慈母王秀英纪念馆</h3>
                            <p class="text-sm text-gray-400">1955 - 2022</p>
                            <p class="text-xs text-gray-500 mt-1">创建者：张伟</p>
                            <div class="mt-2 flex items-center justify-between">
                                <span class="text-xs text-gray-400"><i class="fa-solid fa-users mr-1"></i> 875 亲友</span>
                                <button class="text-xs bg-brand-blue text-white px-3 py-1 rounded-md hover:bg-blue-500">进入空间</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- More items can be added here -->

                </div>
                 <!-- Toggle for light/dark mode for demonstration -->
                <button id="themeToggle" class="mt-8 p-2 bg-brand-blue text-white rounded-lg fixed bottom-20 right-5 z-[1001]">
                    <i class="fa-solid fa-sun"></i> / <i class="fa-solid fa-moon"></i>
                </button>
            </div>

            <!-- iOS Tab Bar -->
            <div class="ios-tab-bar dark" id="tabBar">
                <div class="ios-tab-bar-item" onclick="window.location.href='mobile_home.html'">
                    <i class="fa-solid fa-house"></i>
                    <span>首页</span>
                </div>
                <div class="ios-tab-bar-item active">
                    <i class="fa-solid fa-users"></i>
                    <span>空间</span>
                </div>
                <div class="ios-tab-bar-item">
                    <i class="fa-solid fa-plus-circle text-3xl text-brand-blue -mt-1"></i>
                </div>
                <div class="ios-tab-bar-item">
                    <i class="fa-solid fa-store"></i>
                    <span>商城</span>
                </div>
                <div class="ios-tab-bar-item">
                    <i class="fa-solid fa-user-circle"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        const themeToggle = document.getElementById('themeToggle');
        const appScreen = document.getElementById('appScreen');
        const statusBar = document.getElementById('statusBar');
        const navBar = document.getElementById('navBar');
        const tabBar = document.getElementById('tabBar');
        const mainContent = document.getElementById('mainContent');
        const searchBarContainer = document.getElementById('searchBarContainer');
        const searchInput = searchBarContainer.querySelector('input');

        themeToggle.addEventListener('click', () => {
            document.documentElement.classList.toggle('dark');
            appScreen.classList.toggle('dark');
            appScreen.classList.toggle('light-mode');
            statusBar.classList.toggle('dark');
            statusBar.classList.toggle('light-mode');
            navBar.classList.toggle('dark');
            navBar.classList.toggle('light-mode');
            tabBar.classList.toggle('dark');
            tabBar.classList.toggle('light-mode');
            mainContent.classList.toggle('bg-custom-gray-dark');
            mainContent.classList.toggle('text-white');
            mainContent.classList.toggle('bg-custom-gray-light');
            mainContent.classList.toggle('text-black');
            searchBarContainer.classList.toggle('dark');
            searchBarContainer.classList.toggle('light-mode');
            searchInput.classList.toggle('dark');
            searchInput.classList.toggle('light-mode');
        });
    </script>
</body>
</html>