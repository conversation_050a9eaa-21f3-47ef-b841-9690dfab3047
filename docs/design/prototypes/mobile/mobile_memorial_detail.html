<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>归处 App - 纪念详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'custom-gray-dark': '#1a1a1a', // Page background for dark mode
                        'custom-gray-light': '#f3f4f6', // Page background for light mode
                        'brand-blue': '#3b82f6',
                        'ios-header-bg-dark': 'rgba(26, 26, 26, 0.8)',
                        'ios-header-bg-light': 'rgba(247, 247, 247, 0.8)',
                        'ios-content-bg-dark': '#1c1c1e',
                        'ios-content-bg-light': '#efeff4',
                        'ios-text-primary-dark': '#ffffff',
                        'ios-text-primary-light': '#000000',
                        'ios-text-secondary-dark': '#8e8e93',
                        'ios-text-secondary-light': '#6d6d72',
                        'ios-separator-dark': 'rgba(255,255,255,0.15)',
                        'ios-separator-light': 'rgba(0,0,0,0.1)',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            overscroll-behavior-y: contain;
        }
        .ios-status-bar {
            height: 44px; 
            color: var(--ios-text-primary-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        html.dark .ios-status-bar {
            background-color: var(--ios-header-bg-dark);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            --ios-text-primary-color: theme('colors.ios-text-primary-dark');
        }
        html.light .ios-status-bar {
            background-color: var(--ios-header-bg-light);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            --ios-text-primary-color: theme('colors.ios-text-primary-light');
        }
        .ios-nav-bar {
            height: 44px;
            color: var(--ios-text-primary-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 8px;
            font-size: 17px;
            font-weight: 600;
            position: fixed;
            top: 44px; /* Below status bar */
            left: 0;
            right: 0;
            z-index: 999;
            border-bottom: 1px solid var(--ios-separator-color);
        }
        html.dark .ios-nav-bar {
            background-color: var(--ios-header-bg-dark);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            --ios-text-primary-color: theme('colors.ios-text-primary-dark');
            --ios-separator-color: theme('colors.ios-separator-dark');
        }
        html.light .ios-nav-bar {
            background-color: var(--ios-header-bg-light);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            --ios-text-primary-color: theme('colors.ios-text-primary-light');
            --ios-separator-color: theme('colors.ios-separator-light');
        }
        .ios-nav-bar .nav-title {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            font-weight: 600;
        }
        .ios-nav-bar .nav-button {
            padding: 0 8px;
            font-size: 17px;
            color: theme('colors.brand-blue');
        }
        .page-content {
            padding-top: 88px; /* Status bar + Nav bar */
            padding-bottom: 83px; /* Tab bar height */
        }
        html.dark .page-content {
            background-color: theme('colors.ios-content-bg-dark');
            color: theme('colors.ios-text-primary-dark');
        }
        html.light .page-content {
            background-color: theme('colors.ios-content-bg-light');
            color: theme('colors.ios-text-primary-light');
        }
        .tab-bar {
            height: 83px; /* Standard iOS tab bar height with safe area */
            padding-bottom: 34px; /* iPhone X safe area */
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            align-items: flex-start; /* Align icons to top part of tab item */
            padding-top: 6px;
            border-top: 1px solid var(--ios-separator-color);
            z-index: 990;
        }
        html.dark .tab-bar {
            background-color: var(--ios-header-bg-dark);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            --ios-separator-color: theme('colors.ios-separator-dark');
        }
        html.light .tab-bar {
            background-color: var(--ios-header-bg-light);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            --ios-separator-color: theme('colors.ios-separator-light');
        }
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 10px;
            color: var(--tab-item-color);
        }
        html.dark .tab-item { --tab-item-color: theme('colors.ios-text-secondary-dark'); }
        html.light .tab-item { --tab-item-color: theme('colors.ios-text-secondary-light'); }
        .tab-item.active { color: theme('colors.brand-blue'); }
        .tab-item i { font-size: 22px; margin-bottom: 2px; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center;}

        /* Specific styles for memorial detail page */
        .memorial-header {
            position: relative;
            height: 300px;
            background-size: cover;
            background-position: center;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            padding: 1.5rem;
        }
        .memorial-header::before {
            content: '';
            position: absolute;
            top: 0; left: 0; right: 0; bottom: 0;
            background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
        }
        .memorial-header * { z-index: 1; }

        .section-title {
            font-size: 1.25rem; /* 20px */
            font-weight: 600;
            margin-bottom: 0.75rem; /* 12px */
            padding: 0 1rem;
        }
        html.dark .section-title { color: theme('colors.ios-text-primary-dark'); }
        html.light .section-title { color: theme('colors.ios-text-primary-light'); }

        .biography-text, .tribute-text {
            padding: 0 1rem;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        html.dark .biography-text, html.dark .tribute-text { color: theme('colors.ios-text-secondary-dark'); }
        html.light .biography-text, html.light .tribute-text { color: theme('colors.ios-text-secondary-light'); }

        .photo-gallery {
            display: flex;
            overflow-x: auto;
            gap: 0.75rem; /* 12px */
            padding: 0 1rem 1rem;
            -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
        }
        .photo-gallery img {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid var(--ios-separator-color);
        }
        html.dark .photo-gallery img { --ios-separator-color: theme('colors.ios-separator-dark'); }
        html.light .photo-gallery img { --ios-separator-color: theme('colors.ios-separator-light'); }

        .interactive-buttons {
            display: flex;
            justify-content: space-around;
            padding: 1rem;
            border-top: 1px solid var(--ios-separator-color);
            border-bottom: 1px solid var(--ios-separator-color);
            margin-bottom: 1.5rem;
        }
        html.dark .interactive-buttons { --ios-separator-color: theme('colors.ios-separator-dark'); }
        html.light .interactive-buttons { --ios-separator-color: theme('colors.ios-separator-light'); }

        .interactive-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem; /* 4px */
            font-size: 0.875rem; /* 14px */
            color: theme('colors.brand-blue');
        }
        .interactive-button i { font-size: 1.5rem; /* 24px */ }

    </style>
</head>
<body class="bg-custom-gray-dark">
    <!-- iOS Status Bar -->
    <div class="ios-status-bar">
        <span id="currentTime">9:41 AM</span>
        <div>
            <i class="fa-solid fa-signal"></i>
            <i class="fa-solid fa-wifi"></i>
            <i class="fa-solid fa-battery-full"></i>
        </div>
    </div>

    <!-- iOS Navigation Bar -->
    <div class="ios-nav-bar">
        <button class="nav-button" onclick="history.back()">
            <i class="fa-solid fa-chevron-left"></i> 返回
        </button>
        <span class="nav-title">纪念空间</span>
        <button class="nav-button">
            <i class="fa-solid fa-ellipsis"></i>
        </button>
    </div>

    <!-- Page Content -->
    <div class="page-content min-h-screen">
        <!-- Memorial Header with Background Image -->
        <!-- Image from Unsplash: https://unsplash.com/photos/a-black-and-white-photo-of-a-persons-face-1_CMoFsCUSY -->
        <div class="memorial-header text-white" style="background-image: url('https://images.unsplash.com/photo-1519345182560-3f2917c472ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8cG9ydHJhaXR8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=800&q=60');">
            <h1 class="text-3xl font-bold">李明华</h1>
            <p class="text-sm">1950年3月15日 - 2023年10月26日</p>
        </div>

        <div class="py-4">
            <!-- Biography Section -->
            <section class="mb-6">
                <h2 class="section-title">生平简介</h2>
                <p class="biography-text">
                    李明华先生，生于书香门第，毕生致力于教育事业，桃李满天下。他热爱生活，乐于助人，深受同事、学生和邻里的爱戴。退休后，他依然笔耕不辍，著有多部文学作品，并积极参与社区公益活动。他的善良、智慧和乐观精神将永远激励着我们。
                </p>
            </section>

            <!-- Photo Gallery Section -->
            <section class="mb-6">
                <h2 class="section-title">珍贵影像</h2>
                <div class="photo-gallery">
                    <!-- Image from Unsplash: https://unsplash.com/photos/a-man-smiling-at-the-camera-while-holding-a-camera-ZCHj_2lJP00 -->
                    <img src="https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8cG9ydHJhaXR8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=300&q=60" alt="李明华照片1">
                    <!-- Image from Unsplash: https://unsplash.com/photos/a-woman-with-curly-hair-smiles-at-the-camera-pAs4IM6OGWI -->
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8cG9ydHJhaXR8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=300&q=60" alt="李明华照片2">
                    <!-- Image from Unsplash: https://unsplash.com/photos/a-man-in-a-suit-and-tie-smiles-at-the-camera-mEZ3PoFGs_k -->
                    <img src="https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8cG9ydHJhaXR8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=300&q=60" alt="李明华照片3">
                    <!-- Image from Unsplash: https://unsplash.com/photos/a-woman-with-long-brown-hair-is-smiling-at-the-camera-WNoLnJo7tS8 -->
                    <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8cG9ydHJhaXR8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=300&q=60" alt="李明华照片4">
                    <!-- Image from Unsplash: https://unsplash.com/photos/a-man-with-a-beard-and-glasses-smiles-at-the-camera-iFgRcqHznqg -->
                    <img src="https://images.unsplash.com/photo-1521119989659-a83eee488004?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8N3x8cG9ydHJhaXR8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=300&q=60" alt="李明华照片5">
                </div>
            </section>

            <!-- Interactive Tribute Section -->
            <section class="mb-6">
                <h2 class="section-title">祭奠与追思</h2>
                <div class="interactive-buttons">
                    <button class="interactive-button">
                        <i class="fa-solid fa-spa"></i>
                        <span>敬献鲜花</span>
                    </button>
                    <button class="interactive-button">
                        <i class="fa-solid fa-lightbulb"></i> <!-- Using lightbulb as candle placeholder -->
                        <span>点燃蜡烛</span>
                    </button>
                    <button class="interactive-button">
                        <i class="fa-solid fa-wine-glass"></i>
                        <span>敬奉贡品</span>
                    </button>
                </div>
                <div class="px-4">
                    <textarea class="w-full p-3 border rounded-lg min-h-[100px] bg-transparent placeholder-gray-500 dark:border-gray-600 light:border-gray-300 dark:text-white light:text-black" placeholder="写下您的思念与祝福..."></textarea>
                    <button class="mt-2 w-full bg-brand-blue text-white py-2.5 rounded-lg font-semibold hover:bg-blue-600 transition-colors">
                        发表留言
                    </button>
                </div>
            </section>

            <!-- Video Section (Placeholder) -->
            <section class="mb-6">
                <h2 class="section-title">追忆视频</h2>
                <div class="px-4">
                    <div class="aspect-w-16 aspect-h-9 bg-gray-700 dark:bg-gray-700 light:bg-gray-300 rounded-lg flex items-center justify-center">
                        <i class="fa-solid fa-play-circle text-5xl text-gray-400 dark:text-gray-400 light:text-gray-600"></i>
                        <p class="ml-2 text-gray-400 dark:text-gray-400 light:text-gray-600">暂无视频</p>
                    </div>
                </div>
            </section>

        </div>
    </div>

    <!-- iOS Tab Bar (Placeholder, not active on this page usually) -->
    <div class="tab-bar">
        <a href="mobile_home.html" class="tab-item">
            <i class="fa-solid fa-house"></i>
            <span>首页</span>
        </a>
        <a href="mobile_memorial_space.html" class="tab-item active">
            <i class="fa-solid fa-users"></i>
            <span>纪念空间</span>
        </a>
        <a href="mobile_store.html" class="tab-item">
            <i class="fa-solid fa-store"></i>
            <span>商城</span>
        </a>
        <a href="mobile_profile.html" class="tab-item">
            <i class="fa-solid fa-user"></i>
            <span>我的</span>
        </a>
    </div>

    <script>
        // Theme toggle logic (can be enhanced to sync with index.html)
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && prefersDark)) {
            document.documentElement.classList.add('dark');
            document.documentElement.classList.remove('light');
        } else {
            document.documentElement.classList.add('light');
            document.documentElement.classList.remove('dark');
        }

        // Update time in status bar
        function updateTime() {
            const now = new Date();
            const hours = now.getHours();
            const minutes = now.getMinutes();
            const ampm = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12; // Convert to 12-hour format
            const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;
            document.getElementById('currentTime').textContent = `${formattedHours}:${formattedMinutes} ${ampm}`;
        }
        updateTime();
        setInterval(updateTime, 1000 * 30); // Update every 30 seconds
    </script>
</body>
</html>