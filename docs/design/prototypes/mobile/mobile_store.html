<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>归处 App - 商城</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'custom-gray-dark': '#1a1a1a',
                        'custom-gray-light': '#f3f4f6',
                        'brand-blue': '#3b82f6',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            overscroll-behavior-y: contain;
        }
        .ios-status-bar {
            height: 44px; 
            background-color: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        .ios-status-bar.light-mode {
            background-color: rgba(243, 244, 246, 0.8);
            color: black;
        }
        .ios-nav-bar {
            height: 44px; 
            background-color: rgba(38, 38, 38, 0.85);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 17px;
            font-weight: 600;
            position: fixed;
            top: 44px; 
            left: 0;
            right: 0;
            z-index: 999;
            border-bottom: 0.5px solid rgba(255,255,255,0.2);
        }
        .ios-nav-bar.light-mode {
            background-color: rgba(249, 249, 249, 0.85);
            color: black;
            border-bottom: 0.5px solid rgba(0,0,0,0.2);
        }
        .ios-tab-bar {
            height: 50px; 
            background-color: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: #8e8e93; 
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 999;
            padding-bottom: env(safe-area-inset-bottom);
            border-top: 0.5px solid rgba(255,255,255,0.2);
        }
        .ios-tab-bar.light-mode {
            background-color: rgba(243, 244, 246, 0.8);
            color: #8e8e93;
            border-top: 0.5px solid rgba(0,0,0,0.2);
        }
        .ios-tab-bar-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex-grow: 1;
            font-size: 10px;
            padding-top: 4px;
        }
        .ios-tab-bar-item.active {
            color: #3b82f6;
        }
        .ios-tab-bar-item i {
            font-size: 20px;
            margin-bottom: 2px;
        }
        .content-area {
            padding-top: 88px; 
            padding-bottom: calc(50px + env(safe-area-inset-bottom));
            height: 100vh;
            overflow-y: auto;
        }
        .iphone-frame {
            width: 375px; 
            height: 812px; 
            border: 16px solid black;
            border-radius: 40px;
            position: relative;
            margin: 20px auto;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
            background-color: #1a1a1a;
        }
        .iphone-screen {
            width: 100%;
            height: 100%;
            background-color: #1a1a1a;
            position: relative;
            overflow: hidden;
        }
        .iphone-screen.light-mode {
            background-color: #f3f4f6;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }
        .product-card {
            background-color: #2c2c2e; /* Dark mode card bg */
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .iphone-screen.light-mode .product-card {
            background-color: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .product-card img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        .product-info {
            padding: 10px;
        }
        .product-name {
            font-size: 14px;
            font-weight: 500;
            color: white;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .iphone-screen.light-mode .product-name {
            color: black;
        }
        .product-price {
            font-size: 16px;
            font-weight: 600;
            color: #3b82f6;
        }
        .product-description {
            font-size: 12px;
            color: #8e8e93;
            margin-bottom: 6px;
            height: 2.6em; /* Approx 2 lines */
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
    </style>
</head>
<body class="bg-custom-gray-dark text-white">

    <div class="iphone-frame">
        <div class="iphone-screen dark" id="appScreen">
            <!-- iOS Status Bar -->
            <div class="ios-status-bar dark" id="statusBar">
                <div><i class="fa-solid fa-signal"></i> LTE</div>
                <div>9:44 AM</div>
                <div><i class="fa-solid fa-battery-half"></i> 55%</div>
            </div>

            <!-- iOS Navigation Bar -->
            <div class="ios-nav-bar dark" id="navBar">
                商城
            </div>

            <!-- Main Content Area -->
            <div class="content-area p-4 bg-custom-gray-dark text-white" id="mainContent">
                <h2 class="text-xl font-semibold mb-3">精选祭品</h2>
                <div class="product-grid">
                    <!-- Product Item 1 -->
                    <div class="product-card">
                        <img src="https://images.unsplash.com/photo-1598191492008-7397962519e1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=60" alt="鲜花">
                        <!-- Image source: https://unsplash.com/photos/a-bunch-of-flowers-that-are-in-a-vase-w0z0SWb9y0o -->
                        <div class="product-info">
                            <h3 class="product-name">感恩白菊</h3>
                            <p class="product-description">寄托哀思，传递敬意。</p>
                            <p class="product-price">¥29.90</p>
                        </div>
                    </div>
                    <!-- Product Item 2 -->
                    <div class="product-card">
                        <img src="https://images.unsplash.com/photo-1604496009930-c1aba50e4254?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=60" alt="香烛">
                        <!-- Image source: https://unsplash.com/photos/a-lit-candle-sits-on-a-wooden-table-next-to-a-book-and-a-pair-of-glasses-gYnRs_cmZ3M -->
                        <div class="product-info">
                            <h3 class="product-name">长明莲花烛</h3>
                            <p class="product-description">光明永照，福佑安康。</p>
                            <p class="product-price">¥19.90</p>
                        </div>
                    </div>
                    <!-- Product Item 3 -->
                    <div class="product-card">
                        <img src="https://images.unsplash.com/photo-1516138185111-3918e11771a5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=60" alt="水果">
                        <!-- Image source: https://unsplash.com/photos/a-basket-of-fruit-sitting-on-a-table-0JUnc2xOd20 -->
                        <div class="product-info">
                            <h3 class="product-name">时令鲜果篮</h3>
                            <p class="product-description">四季供奉，心意满满。</p>
                            <p class="product-price">¥58.00</p>
                        </div>
                    </div>
                    <!-- Product Item 4 -->
                    <div class="product-card">
                        <img src="https://images.unsplash.com/photo-1557800636-e74a3648f2c7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=60" alt="祭酒">
                        <!-- Image source: https://unsplash.com/photos/a-bottle-of-wine-and-a-glass-of-wine-on-a-table-7gYQy32k55U -->
                        <div class="product-info">
                            <h3 class="product-name">上品祭祀酒</h3>
                            <p class="product-description">醇香敬献，追思绵长。</p>
                            <p class="product-price">¥88.00</p>
                        </div>
                    </div>
                     <!-- Product Item 5 -->
                    <div class="product-card">
                        <img src="https://images.unsplash.com/photo-1620966312011-f6952780941a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=60" alt="元宝">
                        <!-- Image source: https://unsplash.com/photos/gold-bar-on-white-textile-2Y_059m8gqA (Note: this is a gold bar, used as a visual proxy for元宝) -->
                        <div class="product-info">
                            <h3 class="product-name">环保元宝组合</h3>
                            <p class="product-description">心意传递，绿色环保。</p>
                            <p class="product-price">¥39.00</p>
                        </div>
                    </div>
                     <!-- Product Item 6 -->
                    <div class="product-card">
                        <img src="https://images.unsplash.com/photo-1586474339596-9f02f0afc0a0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=60" alt="祭文">
                        <!-- Image source: https://unsplash.com/photos/a-close-up-of-a-piece-of-paper-with-writing-on-it-sYh3s4yBZnU -->
                        <div class="product-info">
                            <h3 class="product-name">代写电子祭文</h3>
                            <p class="product-description">专业撰写，情真意切。</p>
                            <p class="product-price">¥99.00</p>
                        </div>
                    </div>
                </div>

                <!-- Toggle for light/dark mode for demonstration -->
                <button id="themeToggle" class="mt-8 p-2 bg-brand-blue text-white rounded-lg fixed bottom-20 right-5 z-[1001]">
                    <i class="fa-solid fa-sun"></i> / <i class="fa-solid fa-moon"></i>
                </button>
            </div>

            <!-- iOS Tab Bar -->
            <div class="ios-tab-bar dark" id="tabBar">
                <div class="ios-tab-bar-item" onclick="window.location.href='mobile_home.html'">
                    <i class="fa-solid fa-house"></i>
                    <span>首页</span>
                </div>
                <div class="ios-tab-bar-item" onclick="window.location.href='mobile_memorial_space.html'">
                    <i class="fa-solid fa-users"></i>
                    <span>空间</span>
                </div>
                <div class="ios-tab-bar-item" onclick="window.location.href='mobile_create_memorial.html'">
                    <i class="fa-solid fa-plus-circle text-3xl text-brand-blue -mt-1"></i>
                </div>
                <div class="ios-tab-bar-item active">
                    <i class="fa-solid fa-store"></i>
                    <span>商城</span>
                </div>
                <div class="ios-tab-bar-item">
                    <i class="fa-solid fa-user-circle"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        const themeToggle = document.getElementById('themeToggle');
        const appScreen = document.getElementById('appScreen');
        const statusBar = document.getElementById('statusBar');
        const navBar = document.getElementById('navBar');
        const tabBar = document.getElementById('tabBar');
        const mainContent = document.getElementById('mainContent');
        const productCards = document.querySelectorAll('.product-card');
        const productNames = document.querySelectorAll('.product-name');

        themeToggle.addEventListener('click', () => {
            document.documentElement.classList.toggle('dark');
            appScreen.classList.toggle('dark');
            appScreen.classList.toggle('light-mode');
            statusBar.classList.toggle('dark');
            statusBar.classList.toggle('light-mode');
            navBar.classList.toggle('dark');
            navBar.classList.toggle('light-mode');
            tabBar.classList.toggle('dark');
            tabBar.classList.toggle('light-mode');
            mainContent.classList.toggle('bg-custom-gray-dark');
            mainContent.classList.toggle('text-white');
            mainContent.classList.toggle('bg-custom-gray-light');
            mainContent.classList.toggle('text-black');
            
            productCards.forEach(card => {
                // card.classList.toggle('dark-card-bg'); // Example if specific card bg classes were used
            });
            productNames.forEach(name => {
                // name.classList.toggle('dark-text'); // Example if specific text color classes were used
            });
        });
    </script>
</body>
</html>