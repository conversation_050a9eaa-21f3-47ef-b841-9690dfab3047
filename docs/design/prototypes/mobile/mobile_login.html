<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>归处 App - 登录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'custom-gray-dark': '#1a1a1a',
                        'custom-gray-light': '#f3f4f6',
                        'brand-blue': '#3b82f6',
                        'ios-gray-bg-dark': '#000000', /* Login screen often full black or very dark */
                        'ios-gray-bg-light': '#f2f2f7', /* Standard light grouped table view background */
                        'ios-input-dark': '#1c1c1e',
                        'ios-input-light': '#ffffff',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            overscroll-behavior-y: contain;
        }
        .ios-status-bar {
            height: 44px; 
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        .ios-status-bar.light-mode {
            color: black;
        }
        /* No Nav Bar for typical full-screen login */
        .content-area {
            padding-top: 44px; /* Only Status bar */
            padding-bottom: env(safe-area-inset-bottom);
            height: 100vh;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically */
        }
        .iphone-frame {
            width: 375px; 
            height: 812px; 
            border: 16px solid black;
            border-radius: 40px;
            position: relative;
            margin: 20px auto;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
            background-color: var(--ios-gray-bg-dark, #000000);
        }
        .iphone-frame.light-mode {
            background-color: var(--ios-gray-bg-light, #f2f2f7);
        }
        .iphone-screen {
            width: 100%;
            height: 100%;
            background-color: var(--ios-gray-bg-dark, #000000);
            position: relative;
            overflow: hidden;
        }
        .iphone-screen.light-mode {
            background-color: var(--ios-gray-bg-light, #f2f2f7);
        }
        .login-form-container {
            padding: 0 32px;
            width: 100%;
        }
        .app-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 40px auto;
            border-radius: 18px; /* iOS app icon style */
            background-color: #3b82f6; /* Placeholder logo */
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
        }
        .form-input-ios {
            width: 100%;
            padding: 12px 16px;
            font-size: 16px;
            border: 0.5px solid #545458;
            border-radius: 8px;
            background-color: var(--ios-input-dark, #1c1c1e);
            color: white;
            margin-bottom: 16px;
        }
        .iphone-screen.light-mode .form-input-ios {
            background-color: var(--ios-input-light, #ffffff);
            border-color: #c6c6c8;
            color: black;
        }
        .form-input-ios::placeholder {
            color: #8e8e93;
        }
        .login-button {
            width: 100%;
            padding: 14px;
            font-size: 17px;
            font-weight: 500;
            background-color: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 8px;
        }
        .login-button:disabled {
            background-color: #8e8e93;
            opacity: 0.7;
        }
        .extra-links {
            margin-top: 24px;
            text-align: center;
            font-size: 14px;
        }
        .extra-links a {
            color: #3b82f6;
            text-decoration: none;
        }
        .extra-links span {
            color: #8e8e93;
            margin: 0 8px;
        }
    </style>
</head>
<body class="bg-custom-gray-dark">

    <div class="iphone-frame dark" id="iphoneFrame">
        <div class="iphone-screen dark" id="appScreen">
            <!-- iOS Status Bar -->
            <div class="ios-status-bar dark" id="statusBar">
                <div><i class="fa-solid fa-signal"></i> LTE</div>
                <div>9:41 AM</div>
                <div><i class="fa-solid fa-battery-full"></i> 100%</div>
            </div>

            <!-- Main Content Area -->
            <div class="content-area">
                <div class="login-form-container">
                    <div class="app-logo">
                        <i class="fa-solid fa-cloud"></i> <!-- Placeholder Icon -->
                    </div>
                    <h1 class="text-3xl font-semibold text-center mb-8 text-white dark:text-white">登录归处</h1>
                    <input type="email" id="email" class="form-input-ios" placeholder="邮箱地址或手机号">
                    <input type="password" id="password" class="form-input-ios" placeholder="密码">
                    <button id="loginButton" class="login-button">登录</button>
                    <div class="extra-links">
                        <a href="#">忘记密码?</a>
                        <span>|</span>
                        <a href="mobile_register.html">创建新账户</a>
                    </div>
                </div>
            </div>
            <!-- Toggle for light/dark mode for demonstration -->
            <button id="themeToggle" class="p-2 bg-gray-500 text-white rounded-full fixed bottom-5 right-5 z-[1001]">
                <i class="fa-solid fa-sun"></i> / <i class="fa-solid fa-moon"></i>
            </button>
        </div>
    </div>

    <script>
        const themeToggle = document.getElementById('themeToggle');
        const iphoneFrame = document.getElementById('iphoneFrame');
        const appScreen = document.getElementById('appScreen');
        const statusBar = document.getElementById('statusBar');
        const loginTitle = document.querySelector('h1');
        const inputs = document.querySelectorAll('.form-input-ios');

        function applyTheme() {
            const isDarkMode = document.documentElement.classList.contains('dark');
            iphoneFrame.className = 'iphone-frame ' + (isDarkMode ? 'dark' : 'light-mode');
            appScreen.className = 'iphone-screen ' + (isDarkMode ? 'dark' : 'light-mode');
            statusBar.className = 'ios-status-bar ' + (isDarkMode ? 'dark' : 'light-mode');
            
            if (isDarkMode) {
                loginTitle.classList.add('text-white');
                loginTitle.classList.remove('text-black');
            } else {
                loginTitle.classList.add('text-black');
                loginTitle.classList.remove('text-white');
            }
        }

        themeToggle.addEventListener('click', () => {
            document.documentElement.classList.toggle('dark');
            applyTheme();
        });
        
        // Apply theme on initial load
        applyTheme();
    </script>
</body>
</html>