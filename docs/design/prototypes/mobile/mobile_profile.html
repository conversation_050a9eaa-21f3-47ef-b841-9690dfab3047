<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>归处 App - 我的</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'custom-gray-dark': '#1a1a1a',
                        'custom-gray-light': '#f3f4f6',
                        'brand-blue': '#3b82f6',
                        'ios-gray-bg-dark': '#1c1c1e',
                        'ios-gray-bg-light': '#efeff4',
                        'ios-list-item-dark': '#2c2c2e',
                        'ios-list-item-light': '#ffffff',
                        'ios-separator-dark': 'rgba(255,255,255,0.15)',
                        'ios-separator-light': 'rgba(0,0,0,0.1)',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            overscroll-behavior-y: contain;
        }
        .ios-status-bar {
            height: 44px; 
            background-color: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        .ios-status-bar.light-mode {
            background-color: rgba(243, 244, 246, 0.8);
            color: black;
        }
        .ios-nav-bar {
            height: 44px; 
            background-color: rgba(28, 28, 30, 0.85); /* Slightly different for profile page, more solid */
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 17px;
            font-weight: 600;
            position: fixed;
            top: 44px; 
            left: 0;
            right: 0;
            z-index: 999;
            border-bottom: 0.5px solid var(--ios-separator-dark, rgba(255,255,255,0.2));
        }
        .ios-nav-bar.light-mode {
            background-color: rgba(239, 239, 244, 0.85);
            color: black;
            border-bottom: 0.5px solid var(--ios-separator-light, rgba(0,0,0,0.2));
        }
        .ios-tab-bar {
            height: 50px; 
            background-color: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: #8e8e93; 
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 999;
            padding-bottom: env(safe-area-inset-bottom);
            border-top: 0.5px solid var(--ios-separator-dark, rgba(255,255,255,0.2));
        }
        .ios-tab-bar.light-mode {
            background-color: rgba(243, 244, 246, 0.8);
            color: #8e8e93;
            border-top: 0.5px solid var(--ios-separator-light, rgba(0,0,0,0.2));
        }
        .ios-tab-bar-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex-grow: 1;
            font-size: 10px;
            padding-top: 4px;
            cursor: pointer;
        }
        .ios-tab-bar-item.active {
            color: #3b82f6;
        }
        .ios-tab-bar-item i {
            font-size: 20px;
            margin-bottom: 2px;
        }
        .content-area {
            padding-top: 88px; 
            padding-bottom: calc(50px + env(safe-area-inset-bottom));
            height: 100vh;
            overflow-y: auto;
            background-color: var(--ios-gray-bg-dark, #1c1c1e);
        }
        .iphone-screen.light-mode .content-area {
            background-color: var(--ios-gray-bg-light, #efeff4);
        }
        .iphone-frame {
            width: 375px; 
            height: 812px; 
            border: 16px solid black;
            border-radius: 40px;
            position: relative;
            margin: 20px auto;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
            background-color: #1a1a1a;
        }
        .iphone-screen {
            width: 100%;
            height: 100%;
            background-color: var(--ios-gray-bg-dark, #1c1c1e);
            position: relative;
            overflow: hidden;
        }
        .iphone-screen.light-mode {
            background-color: var(--ios-gray-bg-light, #efeff4);
        }
        .profile-header {
            padding: 24px 16px;
            display: flex;
            align-items: center;
            background-color: var(--ios-list-item-dark, #2c2c2e);
            border-bottom: 0.5px solid var(--ios-separator-dark, rgba(255,255,255,0.15));
        }
        .iphone-screen.light-mode .profile-header {
            background-color: var(--ios-list-item-light, #ffffff);
            border-bottom: 0.5px solid var(--ios-separator-light, rgba(0,0,0,0.1));
        }
        .profile-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 16px;
            border: 2px solid rgba(255,255,255,0.1);
        }
        .iphone-screen.light-mode .profile-avatar {
             border: 2px solid rgba(0,0,0,0.05);
        }
        .profile-info h2 {
            font-size: 20px;
            font-weight: 600;
            color: white;
        }
        .iphone-screen.light-mode .profile-info h2 {
            color: black;
        }
        .profile-info p {
            font-size: 14px;
            color: #8e8e93;
        }
        .list-group {
            margin-top: 20px;
            background-color: var(--ios-list-item-dark, #2c2c2e);
        }
        .iphone-screen.light-mode .list-group {
            background-color: var(--ios-list-item-light, #ffffff);
        }
        .list-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 0.5px solid var(--ios-separator-dark, rgba(255,255,255,0.15));
            color: white;
            font-size: 16px;
            cursor: pointer;
        }
        .iphone-screen.light-mode .list-item {
            color: black;
            border-bottom: 0.5px solid var(--ios-separator-light, rgba(0,0,0,0.1));
        }
        .list-item:last-child {
            border-bottom: none;
        }
        .list-item i.icon {
            font-size: 18px;
            width: 24px;
            text-align: center;
            margin-right: 12px;
            color: #8e8e93;
        }
        .list-item .icon-custom-color {
            color: #3b82f6; /* Example: blue for settings */
        }
        .list-item span {
            flex-grow: 1;
        }
        .list-item .chevron {
            color: #3c3c43;
        }
        .iphone-screen.light-mode .list-item .chevron {
            color: #c7c7cc;
        }
        .logout-button-container {
            margin: 30px 16px 0 16px;
        }
        .logout-button {
            width: 100%;
            padding: 12px;
            background-color: var(--ios-list-item-dark, #2c2c2e);
            color: #ff3b30; /* iOS destructive red */
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
        }
        .iphone-screen.light-mode .logout-button {
            background-color: var(--ios-list-item-light, #ffffff);
        }
    </style>
</head>
<body class="bg-custom-gray-dark text-white">

    <div class="iphone-frame">
        <div class="iphone-screen dark" id="appScreen">
            <!-- iOS Status Bar -->
            <div class="ios-status-bar dark" id="statusBar">
                <div><i class="fa-solid fa-signal"></i> LTE</div>
                <div>9:45 AM</div>
                <div><i class="fa-solid fa-battery-full"></i> 98%</div>
            </div>

            <!-- iOS Navigation Bar -->
            <div class="ios-nav-bar dark" id="navBar">
                我的
            </div>

            <!-- Main Content Area -->
            <div class="content-area" id="mainContent">
                <div class="profile-header">
                    <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=160&h=160&q=80" alt="User Avatar" class="profile-avatar">
                    <!-- Image source: https://unsplash.com/photos/a-man-wearing-a-blue-collared-shirt-and-a-black-suit-jacket-g1Kr4Ozfoac -->
                    <div class="profile-info">
                        <h2 id="profileName">访客用户</h2>
                        <p id="profileId">ID: 10001</p>
                    </div>
                </div>

                <div class="list-group">
                    <div class="list-item">
                        <i class="fa-solid fa-receipt icon" style="color: #ff9500;"></i>
                        <span>我的订单</span>
                        <i class="fa-solid fa-chevron-right chevron"></i>
                    </div>
                    <div class="list-item">
                        <i class="fa-solid fa-users icon" style="color: #34c759;"></i>
                        <span>我的家族</span>
                        <i class="fa-solid fa-chevron-right chevron"></i>
                    </div>
                    <div class="list-item">
                        <i class="fa-solid fa-shield-halved icon" style="color: #007aff;"></i>
                        <span>账号与安全</span>
                        <i class="fa-solid fa-chevron-right chevron"></i>
                    </div>
                </div>

                <div class="list-group">
                    <div class="list-item">
                        <i class="fa-solid fa-gear icon icon-custom-color"></i>
                        <span>应用设置</span>
                        <i class="fa-solid fa-chevron-right chevron"></i>
                    </div>
                    <div class="list-item">
                        <i class="fa-solid fa-circle-question icon" style="color: #5856d6;"></i>
                        <span>帮助与反馈</span>
                        <i class="fa-solid fa-chevron-right chevron"></i>
                    </div>
                    <div class="list-item">
                        <i class="fa-solid fa-info-circle icon" style="color: #8e8e93;"></i>
                        <span>关于归处</span>
                        <i class="fa-solid fa-chevron-right chevron"></i>
                    </div>
                </div>
                
                <div class="logout-button-container">
                    <button class="logout-button">退出登录</button>
                </div>

                <!-- Toggle for light/dark mode for demonstration -->
                <button id="themeToggle" class="p-2 bg-brand-blue text-white rounded-lg fixed bottom-[calc(50px+env(safe-area-inset-bottom)+20px)] right-5 z-[1001]">
                    <i class="fa-solid fa-sun"></i> / <i class="fa-solid fa-moon"></i>
                </button>
            </div>

            <!-- iOS Tab Bar -->
            <div class="ios-tab-bar dark" id="tabBar">
                <div class="ios-tab-bar-item" onclick="window.location.href='mobile_home.html'">
                    <i class="fa-solid fa-house"></i>
                    <span>首页</span>
                </div>
                <div class="ios-tab-bar-item" onclick="window.location.href='mobile_memorial_space.html'">
                    <i class="fa-solid fa-users"></i>
                    <span>空间</span>
                </div>
                <div class="ios-tab-bar-item" onclick="window.location.href='mobile_create_memorial.html'">
                    <i class="fa-solid fa-plus-circle text-3xl text-brand-blue -mt-1"></i>
                </div>
                <div class="ios-tab-bar-item" onclick="window.location.href='mobile_store.html'">
                    <i class="fa-solid fa-store"></i>
                    <span>商城</span>
                </div>
                <div class="ios-tab-bar-item active">
                    <i class="fa-solid fa-user-circle"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        const themeToggle = document.getElementById('themeToggle');
        const appScreen = document.getElementById('appScreen');
        const statusBar = document.getElementById('statusBar');
        const navBar = document.getElementById('navBar');
        const tabBar = document.getElementById('tabBar');
        const mainContent = document.getElementById('mainContent');
        const profileHeader = document.querySelector('.profile-header');
        const profileName = document.getElementById('profileName');
        const listGroups = document.querySelectorAll('.list-group');
        const listItems = document.querySelectorAll('.list-item');
        const logoutButton = document.querySelector('.logout-button');

        // Function to apply current theme to dynamic elements
        function applyTheme() {
            const isDarkMode = document.documentElement.classList.contains('dark');
            
            appScreen.className = 'iphone-screen ' + (isDarkMode ? 'dark' : 'light-mode');
            statusBar.className = 'ios-status-bar ' + (isDarkMode ? 'dark' : 'light-mode');
            navBar.className = 'ios-nav-bar ' + (isDarkMode ? 'dark' : 'light-mode');
            tabBar.className = 'ios-tab-bar ' + (isDarkMode ? 'dark' : 'light-mode');
            mainContent.style.backgroundColor = isDarkMode ? 'var(--ios-gray-bg-dark)' : 'var(--ios-gray-bg-light)';
            
            if (profileHeader) {
                profileHeader.style.backgroundColor = isDarkMode ? 'var(--ios-list-item-dark)' : 'var(--ios-list-item-light)';
                profileHeader.style.borderColor = isDarkMode ? 'var(--ios-separator-dark)' : 'var(--ios-separator-light)';
            }
            if (profileName) {
                profileName.style.color = isDarkMode ? 'white' : 'black';
            }
            document.querySelector('.profile-info p').style.color = '#8e8e93'; // Stays same
            document.querySelector('.profile-avatar').style.borderColor = isDarkMode ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)';

            listGroups.forEach(group => {
                group.style.backgroundColor = isDarkMode ? 'var(--ios-list-item-dark)' : 'var(--ios-list-item-light)';
            });
            listItems.forEach(item => {
                item.style.color = isDarkMode ? 'white' : 'black';
                item.style.borderBottomColor = isDarkMode ? 'var(--ios-separator-dark)' : 'var(--ios-separator-light)';
                item.querySelector('.chevron').style.color = isDarkMode ? '#3c3c43' : '#c7c7cc';
            });
            if (logoutButton) {
                logoutButton.style.backgroundColor = isDarkMode ? 'var(--ios-list-item-dark)' : 'var(--ios-list-item-light)';
            }
        }

        themeToggle.addEventListener('click', () => {
            document.documentElement.classList.toggle('dark');
            applyTheme();
        });
        
        // Apply theme on initial load
        applyTheme();
    </script>
</body>
</html>