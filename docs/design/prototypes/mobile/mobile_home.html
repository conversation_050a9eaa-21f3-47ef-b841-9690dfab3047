<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>归处 App - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'custom-gray-dark': '#1a1a1a',
                        'custom-gray-light': '#f3f4f6',
                        'brand-blue': '#3b82f6',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            overscroll-behavior-y: contain; /* Prevent pull-to-refresh on body */
        }
        .ios-status-bar {
            height: 44px; /* Approximate iOS status bar height */
            background-color: rgba(26, 26, 26, 0.8); /* Dark mode translucent */
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 14px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
        .ios-status-bar.light-mode {
            background-color: rgba(243, 244, 246, 0.8);
            color: black;
        }
        .ios-nav-bar {
            height: 44px; /* Approximate iOS nav bar height */
            background-color: rgba(38, 38, 38, 0.85); /* Dark mode slightly less translucent */
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 17px;
            font-weight: 600;
            position: fixed;
            top: 44px; /* Below status bar */
            left: 0;
            right: 0;
            z-index: 999;
            border-bottom: 0.5px solid rgba(255,255,255,0.2);
        }
        .ios-nav-bar.light-mode {
            background-color: rgba(249, 249, 249, 0.85);
            color: black;
            border-bottom: 0.5px solid rgba(0,0,0,0.2);
        }
        .ios-tab-bar {
            height: 50px; /* Approximate iOS tab bar height, excluding safe area */
            background-color: rgba(26, 26, 26, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            color: #8e8e93; /* iOS inactive tab color */
            display: flex;
            justify-content: space-around;
            align-items: center;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 999;
            padding-bottom: env(safe-area-inset-bottom); /* For iPhone X and newer */
            border-top: 0.5px solid rgba(255,255,255,0.2);
        }
        .ios-tab-bar.light-mode {
            background-color: rgba(243, 244, 246, 0.8);
            color: #8e8e93;
            border-top: 0.5px solid rgba(0,0,0,0.2);
        }
        .ios-tab-bar-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            flex-grow: 1;
            font-size: 10px;
            padding-top: 4px;
        }
        .ios-tab-bar-item.active {
            color: #3b82f6; /* iOS active tab color (blue) */
        }
        .ios-tab-bar-item i {
            font-size: 20px;
            margin-bottom: 2px;
        }
        .content-area {
            padding-top: 88px; /* Status bar + Nav bar */
            padding-bottom: calc(50px + env(safe-area-inset-bottom)); /* Tab bar + safe area */
            height: 100vh;
            overflow-y: auto;
        }
        /* iPhone X like notch simulation (optional, for visual representation) */
        .iphone-frame {
            width: 375px; /* iPhone X/XS/11 Pro width */
            height: 812px; /* iPhone X/XS/11 Pro height */
            border: 16px solid black;
            border-radius: 40px;
            position: relative;
            margin: 20px auto;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
            background-color: #1a1a1a; /* Dark background for the frame */
        }
        .iphone-screen {
            width: 100%;
            height: 100%;
            background-color: #1a1a1a; /* Dark mode default screen bg */
            position: relative;
            overflow: hidden; /* Important for scrolling inside the screen */
        }
        .iphone-screen.light-mode {
            background-color: #f3f4f6; /* Light mode default screen bg */
        }
    </style>
</head>
<body class="bg-custom-gray-dark text-white">

    <div class="iphone-frame">
        <div class="iphone-screen dark" id="appScreen">
            <!-- iOS Status Bar -->
            <div class="ios-status-bar dark" id="statusBar">
                <div><i class="fa-solid fa-signal"></i> LTE</div>
                <div>9:41 AM</div>
                <div><i class="fa-solid fa-battery-full"></i> 100%</div>
            </div>

            <!-- iOS Navigation Bar -->
            <div class="ios-nav-bar dark" id="navBar">
                归处
            </div>

            <!-- Main Content Area -->
            <div class="content-area p-4 bg-custom-gray-dark text-white" id="mainContent">
                <h1 class="text-2xl font-semibold mb-4">首页</h1>
                
                <!-- Placeholder for content -->
                <div class="space-y-4">
                    <div class="bg-gray-700 p-4 rounded-lg shadow">
                        <h2 class="text-lg font-medium">纪念空间推荐</h2>
                        <p class="text-sm text-gray-400 mt-1">发现那些触动人心的故事。</p>
                        <!-- Image from Unsplash -->
                        <img src="https://images.unsplash.com/photo-1506702315536-75858095c429?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" alt="Peaceful lake scene" class="mt-2 rounded-md w-full h-48 object-cover">
                        <!-- Image source: https://unsplash.com/photos/a-small-body-of-water-surrounded-by-trees-and-mountains-L0k0x90pc_A -->
                    </div>

                    <div class="bg-gray-700 p-4 rounded-lg shadow">
                        <h2 class="text-lg font-medium">最新动态</h2>
                        <p class="text-sm text-gray-400 mt-1">亲友的思念与祝福。</p>
                    </div>

                    <div class="bg-gray-700 p-4 rounded-lg shadow">
                        <h2 class="text-lg font-medium">家族树</h2>
                        <p class="text-sm text-gray-400 mt-1">连接过去，传承未来。</p>
                    </div>
                </div>
                
                <!-- Toggle for light/dark mode for demonstration -->
                <button id="themeToggle" class="mt-8 p-2 bg-brand-blue text-white rounded-lg fixed bottom-20 right-5 z-[1001]">
                    <i class="fa-solid fa-sun"></i> / <i class="fa-solid fa-moon"></i>
                </button>
            </div>

            <!-- iOS Tab Bar -->
            <div class="ios-tab-bar dark" id="tabBar">
                <div class="ios-tab-bar-item active">
                    <i class="fa-solid fa-house"></i>
                    <span>首页</span>
                </div>
                <div class="ios-tab-bar-item">
                    <i class="fa-solid fa-users"></i>
                    <span>空间</span>
                </div>
                <div class="ios-tab-bar-item">
                    <i class="fa-solid fa-plus-circle text-3xl text-brand-blue -mt-1"></i>
                    <!-- <span>创建</span> -->
                </div>
                <div class="ios-tab-bar-item">
                    <i class="fa-solid fa-store"></i>
                    <span>商城</span>
                </div>
                <div class="ios-tab-bar-item">
                    <i class="fa-solid fa-user-circle"></i>
                    <span>我的</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        const themeToggle = document.getElementById('themeToggle');
        const appScreen = document.getElementById('appScreen');
        const statusBar = document.getElementById('statusBar');
        const navBar = document.getElementById('navBar');
        const tabBar = document.getElementById('tabBar');
        const mainContent = document.getElementById('mainContent');

        themeToggle.addEventListener('click', () => {
            document.documentElement.classList.toggle('dark');
            appScreen.classList.toggle('dark');
            appScreen.classList.toggle('light-mode');
            statusBar.classList.toggle('dark');
            statusBar.classList.toggle('light-mode');
            navBar.classList.toggle('dark');
            navBar.classList.toggle('light-mode');
            tabBar.classList.toggle('dark');
            tabBar.classList.toggle('light-mode');
            mainContent.classList.toggle('bg-custom-gray-dark');
            mainContent.classList.toggle('text-white');
            mainContent.classList.toggle('bg-custom-gray-light');
            mainContent.classList.toggle('text-black');
        });
    </script>
</body>
</html>