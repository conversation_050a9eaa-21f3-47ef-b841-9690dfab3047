<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>归处 App - 原型预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'custom-gray-dark': '#121212', // Main page dark bg
                        'custom-gray-light': '#f9fafb', // Main page light bg
                        'brand-blue': '#3b82f6',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
        }
        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(390px, 1fr)); /* Responsive grid, min 375px + padding/border */
            gap: 2rem;
            padding: 2rem;
        }
        .prototype-card {
            border: 1px solid #4b5563; /* dark:border-gray-600 */
            border-radius: 8px;
            overflow: hidden;
            background-color: #1f2937; /* dark:bg-gray-800 */
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        html.light .prototype-card {
            border-color: #d1d5db; /* light:border-gray-300 */
            background-color: #ffffff; /* light:bg-white */
        }
        .prototype-card iframe {
            width: 100%;
            height: 812px; /* Match iPhone frame height */
            border: none;
            transform-origin: top left;
        }
        .prototype-title {
            padding: 0.75rem 1rem;
            font-weight: 500;
            border-bottom: 1px solid #4b5563; /* dark:border-gray-600 */
            color: #e5e7eb; /* dark:text-gray-200 */
        }
        html.light .prototype-title {
            border-bottom-color: #d1d5db; /* light:border-gray-300 */
            color: #111827; /* light:text-gray-900 */
        }
    </style>
</head>
<body class="bg-custom-gray-dark text-gray-200">
    <div class="container mx-auto px-4 py-8">
        <header class="mb-8 text-center">
            <h1 class="text-4xl font-bold text-brand-blue">归处 App - 移动端原型</h1>
            <p class="mt-2 text-lg text-gray-400">以下为归处 App 主要界面的高保真 HTML 原型（iOS 风格）。</p>
            <button id="themeToggle" class="mt-4 px-4 py-2 bg-brand-blue text-white rounded-lg">
                <i class="fa-solid fa-sun"></i> / <i class="fa-solid fa-moon"></i> 切换主题
            </button>
        </header>

        <div class="prototype-grid">
            <!-- mobile_home.html -->
            <div class="prototype-card">
                <h2 class="prototype-title">首页 (mobile_home.html)</h2>
                <iframe src="mobile_home.html" title="归处 App - 首页"></iframe>
            </div>

            <!-- mobile_memorial_space.html -->
            <div class="prototype-card">
                <h2 class="prototype-title">纪念空间列表 (mobile_memorial_space.html)</h2>
                <iframe src="mobile_memorial_space.html" title="归处 App - 纪念空间"></iframe>
            </div>

            <!-- mobile_create_memorial.html -->
            <div class="prototype-card">
                <h2 class="prototype-title">创建纪念空间 (mobile_create_memorial.html)</h2>
                <iframe src="mobile_create_memorial.html" title="归处 App - 创建纪念空间"></iframe>
            </div>

            <!-- mobile_store.html -->
            <div class="prototype-card">
                <h2 class="prototype-title">商城 (mobile_store.html)</h2>
                <iframe src="mobile_store.html" title="归处 App - 商城"></iframe>
            </div>

            <!-- mobile_profile.html -->
            <div class="prototype-card">
                <h2 class="prototype-title">我的 (mobile_profile.html)</h2>
                <iframe src="mobile_profile.html" title="归处 App - 我的"></iframe>
            </div>

            <!-- mobile_login.html -->
            <div class="prototype-card">
                <h2 class="prototype-title">登录页 (mobile_login.html)</h2>
                <iframe src="mobile_login.html" title="归处 App - 登录"></iframe>
            </div>

            <!-- mobile_register.html -->
            <div class="prototype-card">
                <h2 class="prototype-title">注册页 (mobile_register.html)</h2>
                <iframe src="mobile_register.html" title="归处 App - 注册"></iframe>
            </div>

            <!-- mobile_memorial_detail.html -->
            <div class="prototype-card">
                <h2 class="prototype-title">纪念空间详情 (mobile_memorial_detail.html)</h2>
                <iframe src="mobile_memorial_detail.html" title="归处 App - 纪念空间详情"></iframe>
            </div>

            <!-- Add more prototypes here as they are created -->
        </div>

        <footer class="text-center mt-12 py-6 border-t border-gray-700">
            <p class="text-sm text-gray-500">&copy; 2023 归处团队. 使用 Tailwind CSS & FontAwesome 构建.</p>
        </footer>
    </div>

    <script>
        const themeToggle = document.getElementById('themeToggle');
        themeToggle.addEventListener('click', () => {
            document.documentElement.classList.toggle('dark');
            document.documentElement.classList.toggle('light');
            // Optionally, you might need to reload iframes or send a message to them to toggle their themes
            // For simplicity, this example assumes iframes might pick up the change or have their own toggles
        });
        // Set initial theme based on system preference or saved state if any
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
            document.documentElement.classList.remove('light');
        } else {
            document.documentElement.classList.add('light');
            document.documentElement.classList.remove('dark');
        }
    </script>
</body>
</html>