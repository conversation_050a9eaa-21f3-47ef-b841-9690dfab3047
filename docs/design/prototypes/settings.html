<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账户设置 - 归处在线祭祀平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="dark-mode.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }
        .form-input {
            @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:focus:ring-indigo-400 dark:focus:border-indigo-400;
        }
        .form-label {
            @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
        }
        .tab-button {
            @apply px-4 py-2 font-medium text-sm rounded-md transition-colors duration-150;
        }
        .tab-button.active {
            @apply bg-indigo-100 text-indigo-700 dark:bg-indigo-700 dark:text-indigo-100;
        }
        .tab-button:not(.active) {
            @apply text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700;
        }
        .tab-content {
            @apply p-6 bg-white dark:bg-gray-800 rounded-b-lg md:rounded-r-lg md:rounded-bl-none shadow;
        }
    </style>
</head>
<body class="flex flex-col min-h-screen">
    <!-- 暗黑模式接收器 -->
    <script>
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.documentElement.classList.add('dark');
            document.body.classList.add('dark-mode');
        }
    </script>

    <!-- 顶部导航栏 -->
    <header class="bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <i class="fas fa-user-cog text-indigo-600 text-2xl mr-3"></i>
                <span class="text-xl font-bold text-indigo-800 dark:text-indigo-400">归处</span>
            </div>
            <nav class="hidden md:flex space-x-4">
                <a href="index.html#home" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">首页</a>
                <a href="dashboard.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">我的纪念馆</a>
                <a href="worship.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">祭拜互动</a>
                <a href="ai_repair.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">AI照片修复</a>
                <a href="store.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">祭品商城</a>
                <a href="family_tree.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">家族树</a>
                <a href="#" class="text-indigo-600 dark:text-indigo-400 font-semibold">账户设置</a>
            </nav>
            <div class="flex items-center space-x-4">
                <button id="theme-toggle-button" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                    <i class="fas fa-sun"></i>
                </button>
                <a href="login.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">登录</a>
                <a href="register.html" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium">注册</a>
            </div>
        </div>
    </header>

    <!-- 主体内容 -->
    <main class="flex-grow container mx-auto px-4 py-8">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 dark:text-white mb-4">账户设置</h1>
            <p class="text-lg text-gray-600 dark:text-gray-300">管理您的个人信息、安全设置和偏好。</p>
        </div>

        <div class="max-w-4xl mx-auto md:flex">
            <!-- 左侧导航 -->
            <div class="md:w-1/4 mb-6 md:mb-0 md:mr-6">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-2">
                    <nav class="flex flex-col space-y-1">
                        <button data-tab="profile" class="tab-button active flex items-center">
                            <i class="fas fa-user-edit w-5 mr-3"></i>个人资料
                        </button>
                        <button data-tab="security" class="tab-button flex items-center">
                            <i class="fas fa-shield-alt w-5 mr-3"></i>安全设置
                        </button>
                        <button data-tab="notifications" class="tab-button flex items-center">
                            <i class="fas fa-bell w-5 mr-3"></i>通知偏好
                        </button>
                        <button data-tab="privacy" class="tab-button flex items-center">
                            <i class="fas fa-user-secret w-5 mr-3"></i>隐私选项
                        </button>
                    </nav>
                </div>
            </div>

            <!-- 右侧内容区域 -->
            <div class="md:w-3/4">
                <!-- 个人资料 -->
                <div id="profile-content" class="tab-content">
                    <h2 class="text-2xl font-semibold text-gray-800 dark:text-white mb-6">个人资料</h2>
                    <form space-y-6>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label for="nickname" class="form-label">昵称</label>
                                <input type="text" id="nickname" value="示例用户" class="form-input">
                            </div>
                            <div>
                                <label for="email" class="form-label">电子邮箱</label>
                                <input type="email" id="email" value="<EMAIL>" class="form-input" readonly>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">邮箱不可更改。</p>
                            </div>
                        </div>
                        <div class="mb-6">
                            <label class="form-label">头像</label>
                            <div class="mt-1 flex items-center space-x-4">
                                <!-- 图片来源: https://images.unsplash.com/photo-1535713875002-d1d0cf377fde (Unsplash) -->
                                <img class="h-20 w-20 rounded-full object-cover" src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlciUyMHByb2ZpbGV8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=100&q=60" alt="Current avatar">
                                <button type="button" class="px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                                    更改头像
                                </button>
                            </div>
                        </div>
                        <div class="mb-6">
                            <label for="bio" class="form-label">个人简介</label>
                            <textarea id="bio" rows="3" class="form-input" placeholder="简单介绍一下自己..."></textarea>
                        </div>
                        <div>
                            <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-md transition duration-300">
                                保存更改
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 安全设置 -->
                <div id="security-content" class="tab-content hidden">
                    <h2 class="text-2xl font-semibold text-gray-800 dark:text-white mb-6">安全设置</h2>
                    <div class="space-y-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">修改密码</h3>
                            <form class="mt-2 space-y-4">
                                <div>
                                    <label for="current-password" class="form-label">当前密码</label>
                                    <input type="password" id="current-password" class="form-input">
                                </div>
                                <div>
                                    <label for="new-password" class="form-label">新密码</label>
                                    <input type="password" id="new-password" class="form-input">
                                </div>
                                <div>
                                    <label for="confirm-password" class="form-label">确认新密码</label>
                                    <input type="password" id="confirm-password" class="form-input">
                                </div>
                                <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-md transition duration-300">
                                    更新密码
                                </button>
                            </form>
                        </div>
                        <hr class="dark:border-gray-700">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">双因素认证 (2FA)</h3>
                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">启用双因素认证以增强账户安全。</p>
                            <button type="button" class="mt-3 bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-md transition duration-300">
                                设置2FA
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 通知偏好 -->
                <div id="notifications-content" class="tab-content hidden">
                    <h2 class="text-2xl font-semibold text-gray-800 dark:text-white mb-6">通知偏好</h2>
                    <div class="space-y-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">邮件通知</h3>
                            <div class="mt-2 space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox h-5 w-5 text-indigo-600 dark:bg-gray-700 dark:border-gray-600 rounded focus:ring-indigo-500" checked>
                                    <span class="ml-2 text-gray-700 dark:text-gray-300">新功能和更新</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox h-5 w-5 text-indigo-600 dark:bg-gray-700 dark:border-gray-600 rounded focus:ring-indigo-500">
                                    <span class="ml-2 text-gray-700 dark:text-gray-300">纪念日提醒</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox h-5 w-5 text-indigo-600 dark:bg-gray-700 dark:border-gray-600 rounded focus:ring-indigo-500" checked>
                                    <span class="ml-2 text-gray-700 dark:text-gray-300">账户安全警报</span>
                                </label>
                            </div>
                        </div>
                        <hr class="dark:border-gray-700">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">站内通知</h3>
                             <div class="mt-2 space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox h-5 w-5 text-indigo-600 dark:bg-gray-700 dark:border-gray-600 rounded focus:ring-indigo-500" checked>
                                    <span class="ml-2 text-gray-700 dark:text-gray-300">亲友互动提醒</span>
                                </label>
                            </div>
                        </div>
                        <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-md transition duration-300">
                            保存偏好
                        </button>
                    </div>
                </div>

                <!-- 隐私选项 -->
                <div id="privacy-content" class="tab-content hidden">
                    <h2 class="text-2xl font-semibold text-gray-800 dark:text-white mb-6">隐私选项</h2>
                    <div class="space-y-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">个人资料可见性</h3>
                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">控制谁可以看到您的个人资料信息。</p>
                            <select class="mt-2 form-input max-w-xs">
                                <option>所有人可见</option>
                                <option selected>仅好友可见</option>
                                <option>仅自己可见</option>
                            </select>
                        </div>
                        <hr class="dark:border-gray-700">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">纪念馆访问权限</h3>
                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-300">设置谁可以访问您创建的纪念馆。</p>
                            <select class="mt-2 form-input max-w-xs">
                                <option>公开（任何人可搜索和访问）</option>
                                <option selected>凭密码访问</option>
                                <option>仅限邀请</option>
                            </select>
                        </div>
                         <hr class="dark:border-gray-700">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">数据管理</h3>
                            <button type="button" class="mt-3 mr-2 bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-md transition duration-300">
                                下载我的数据
                            </button>
                            <button type="button" class="mt-3 bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-md transition duration-300">
                                删除我的账户
                            </button>
                        </div>
                        <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-md transition duration-300">
                            保存隐私设置
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400 py-8 text-center mt-12">
        <div class="container mx-auto">
            <p>&copy; 2024 归处在线祭祀平台. 保留所有权利.</p>
            <p class="text-sm mt-2">用心连接思念，科技传承记忆。</p>
        </div>
    </footer>

    <script>
        // 暗黑模式切换逻辑
        const themeToggleButton = document.getElementById('theme-toggle-button');
        const htmlElement = document.documentElement;
        const bodyElement = document.body;

        if (localStorage.getItem('theme') === 'dark') {
            htmlElement.classList.add('dark');
            bodyElement.classList.add('dark-mode');
            themeToggleButton.innerHTML = '<i class="fas fa-moon"></i>';
        } else {
            themeToggleButton.innerHTML = '<i class="fas fa-sun"></i>';
        }

        themeToggleButton.addEventListener('click', () => {
            if (htmlElement.classList.contains('dark')) {
                htmlElement.classList.remove('dark');
                bodyElement.classList.remove('dark-mode');
                localStorage.setItem('theme', 'light');
                themeToggleButton.innerHTML = '<i class="fas fa-sun"></i>';
            } else {
                htmlElement.classList.add('dark');
                bodyElement.classList.add('dark-mode');
                localStorage.setItem('theme', 'dark');
                themeToggleButton.innerHTML = '<i class="fas fa-moon"></i>';
            }
        });

        // Tab 切换逻辑
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.dataset.tab;

                tabButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                tabContents.forEach(content => {
                    if (content.id === `${tabName}-content`) {
                        content.classList.remove('hidden');
                    } else {
                        content.classList.add('hidden');
                    }
                });
            });
        });
    </script>
</body>
</html>