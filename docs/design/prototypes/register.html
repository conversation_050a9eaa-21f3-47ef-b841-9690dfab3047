<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 归处</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'dark-space-gray': '#333333',
                        'serene-blue': '#4A90E2',
                        'warm-gold': '#F5A623',
                        'moonlight-white': '#F8F8F8',
                        'light-gray': '#CCCCCC',
                        'medium-gray': '#999999',
                        'dark-gray': '#666666',
                        'success-green': '#2ECC71',
                        'warning-orange': '#F39C12',
                        'error-red': '#E74C3C',
                        'app-bg-dark': '#111827',
                        'card-bg-dark': '#1f2937',
                        'input-bg-dark': '#374151',
                        'text-dark-primary': '#F8F8F8',
                        'text-dark-secondary': '#9ca3af',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }
    </style>
</head>
<body class="bg-app-bg-dark text-text-dark-primary flex items-center justify-center min-h-screen p-4">
    <div class="w-full max-w-md bg-card-bg-dark shadow-2xl rounded-xl p-8 md:p-12">
        <div class="text-center mb-8">
            <i class="fas fa-user-plus text-serene-blue text-5xl mb-3"></i>
            <h1 class="text-3xl font-bold text-moonlight-white">创建归处账户</h1>
            <p class="text-text-dark-secondary mt-2">开启您的数字纪念之旅</p>
        </div>

        <form action="#" method="POST" class="space-y-6">
            <div>
                <label for="username" class="block text-sm font-medium text-text-dark-secondary mb-1">用户名</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-user text-gray-400"></i>
                    </div>
                    <input id="username" name="username" type="text" autocomplete="username" required
                           class="w-full pl-10 pr-3 py-3 bg-input-bg-dark border border-gray-600 text-text-dark-primary rounded-lg focus:ring-2 focus:ring-serene-blue focus:border-serene-blue outline-none transition duration-150 ease-in-out"
                           placeholder="设置您的用户名">
                </div>
            </div>

            <div>
                <label for="email" class="block text-sm font-medium text-text-dark-secondary mb-1">邮箱地址</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-envelope text-gray-400"></i>
                    </div>
                    <input id="email" name="email" type="email" autocomplete="email" required
                           class="w-full pl-10 pr-3 py-3 bg-input-bg-dark border border-gray-600 text-text-dark-primary rounded-lg focus:ring-2 focus:ring-serene-blue focus:border-serene-blue outline-none transition duration-150 ease-in-out"
                           placeholder="<EMAIL>">
                </div>
            </div>

            <div>
                <label for="password" class="block text-sm font-medium text-text-dark-secondary mb-1">密码</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-lock text-gray-400"></i>
                    </div>
                    <input id="password" name="password" type="password" autocomplete="new-password" required
                           class="w-full pl-10 pr-3 py-3 bg-input-bg-dark border border-gray-600 text-text-dark-primary rounded-lg focus:ring-2 focus:ring-serene-blue focus:border-serene-blue outline-none transition duration-150 ease-in-out"
                           placeholder="创建您的密码">
                </div>
            </div>

            <div>
                <label for="confirm-password" class="block text-sm font-medium text-text-dark-secondary mb-1">确认密码</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-check-circle text-gray-400"></i>
                    </div>
                    <input id="confirm-password" name="confirm-password" type="password" autocomplete="new-password" required
                           class="w-full pl-10 pr-3 py-3 bg-input-bg-dark border border-gray-600 text-text-dark-primary rounded-lg focus:ring-2 focus:ring-serene-blue focus:border-serene-blue outline-none transition duration-150 ease-in-out"
                           placeholder="再次输入密码">
                </div>
            </div>
            
            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input id="terms" name="terms" type="checkbox" required
                           class="h-4 w-4 text-serene-blue bg-gray-700 border-gray-600 rounded focus:ring-serene-blue">
                </div>
                <div class="ml-3 text-sm">
                    <label for="terms" class="text-text-dark-secondary">我同意归处的<a href="#" class="font-medium text-serene-blue hover:underline">服务条款</a>和<a href="#" class="font-medium text-serene-blue hover:underline">隐私政策</a>。</label>
                </div>
            </div>

            <div>
                <button type="submit"
                        class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-serene-blue hover:bg-opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-app-bg-dark focus:ring-serene-blue transition duration-150 ease-in-out">
                    <i class="fas fa-check mr-2"></i> 创建账户
                </button>
            </div>
        </form>

        <p class="mt-8 text-center text-sm text-text-dark-secondary">
            已经有账户了? 
            <a href="login.html" class="font-medium text-serene-blue hover:underline">在此登录</a>
        </p>
    </div>
</body>
</html>