<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>祭品商城 - 归处在线祭祀平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="dark-mode.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }
        .product-card {
            transition: all 0.3s ease;
        }
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .dark-mode .product-card {
            background-color: #1f2937; /* 更深的灰色背景 */
            border-color: #374151;
        }
        .dark-mode .product-card:hover {
             box-shadow: 0 10px 20px rgba(255,255,255,0.05);
        }
    </style>
</head>
<body class="flex flex-col min-h-screen">
    <!-- 暗黑模式接收器 -->
    <script>
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.documentElement.classList.add('dark');
            document.body.classList.add('dark-mode');
        }
    </script>

    <!-- 顶部导航栏 -->
    <header class="bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <i class="fas fa-store text-indigo-600 text-2xl mr-3"></i>
                <span class="text-xl font-bold text-indigo-800 dark:text-indigo-400">归处</span>
            </div>
            <nav class="hidden md:flex space-x-4">
                <a href="index.html#home" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">首页</a>
                <a href="dashboard.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">我的纪念馆</a>
                <a href="worship.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">祭拜互动</a>
                <a href="ai_repair.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">AI照片修复</a>
                <a href="#" class="text-indigo-600 dark:text-indigo-400 font-semibold">祭品商城</a>
                <a href="family_tree.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">家族树</a>
            </nav>
            <div class="flex items-center space-x-4">
                <button id="theme-toggle-button" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                    <i class="fas fa-sun"></i>
                </button>
                <a href="login.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">登录</a>
                <a href="register.html" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium">注册</a>
            </div>
        </div>
    </header>

    <!-- 主体内容 -->
    <main class="flex-grow container mx-auto px-4 py-8">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 dark:text-white mb-4">归处祭品商城</h1>
            <p class="text-lg text-gray-600 dark:text-gray-300">为逝者选购一份心意，寄托您的哀思。</p>
        </div>

        <!-- 商品筛选与分类 -->
        <div class="mb-8 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg shadow">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex items-center space-x-2">
                    <label for="category-filter" class="text-sm font-medium text-gray-700 dark:text-gray-200">分类:</label>
                    <select id="category-filter" class="rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 text-sm py-1.5 px-2">
                        <option value="all">所有分类</option>
                        <option value="flowers">鲜花花束</option>
                        <option value="candles">香烛纸钱</option>
                        <option value="offerings">供品果点</option>
                        <option value="virtual">虚拟祭品</option>
                    </select>
                </div>
                <div class="relative w-full sm:w-auto max-w-xs">
                    <input type="text" placeholder="搜索商品..." class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 text-sm py-1.5 px-3 pr-10">
                    <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500"></i>
                </div>
            </div>
        </div>

        <!-- 商品列表 -->
        <div class="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <!-- 商品卡片示例 1 -->
            <div class="product-card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-200 dark:border-gray-700">
                <!-- 图片来源: https://images.unsplash.com/photo-1541160097335-7d8c00d15f99 (Unsplash - 白菊花) -->
                <img src="https://images.unsplash.com/photo-1541160097335-7d8c00d15f99?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8d2hpdGUlMjBjaHJ5c2FudGhlbXVtfGVufDB8fDB8fHww&auto=format&fit=crop&w=300&q=60" alt="素雅白菊" class="w-full h-48 object-cover">
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-1">素雅白菊</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">寄托哀思，纯洁高雅</p>
                    <div class="flex justify-between items-center mb-3">
                        <span class="text-xl font-bold text-indigo-600 dark:text-indigo-400">¥28.00</span>
                        <span class="text-xs text-gray-400 dark:text-gray-500">已售 120+</span>
                    </div>
                    <button class="w-full bg-indigo-500 hover:bg-indigo-600 text-white font-medium py-2 px-4 rounded-md text-sm transition duration-300">
                        <i class="fas fa-cart-plus mr-2"></i>加入购物车
                    </button>
                </div>
            </div>

            <!-- 商品卡片示例 2 -->
            <div class="product-card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-200 dark:border-gray-700">
                <!-- 图片来源: https://images.unsplash.com/photo-1604706511593-3994790f0e79 (Unsplash - 电子香烛) -->
                <img src="https://images.unsplash.com/photo-1604706511593-3994790f0e79?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8ZWxlY3Ryb25pYyUyMGNhbmRsZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=300&q=60" alt="电子长明灯" class="w-full h-48 object-cover">
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-1">电子长明灯</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">环保安全，心意永续</p>
                    <div class="flex justify-between items-center mb-3">
                        <span class="text-xl font-bold text-indigo-600 dark:text-indigo-400">¥45.00</span>
                        <span class="text-xs text-gray-400 dark:text-gray-500">已售 80+</span>
                    </div>
                    <button class="w-full bg-indigo-500 hover:bg-indigo-600 text-white font-medium py-2 px-4 rounded-md text-sm transition duration-300">
                        <i class="fas fa-cart-plus mr-2"></i>加入购物车
                    </button>
                </div>
            </div>

            <!-- 商品卡片示例 3 -->
            <div class="product-card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-200 dark:border-gray-700">
                <!-- 图片来源: https://images.unsplash.com/photo-1557844352-761f2565b576 (Unsplash - 水果篮) -->
                <img src="https://images.unsplash.com/photo-1557844352-761f2565b576?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8ZnJ1aXQlMjBvZmZlcmluZ3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=300&q=60" alt="时令鲜果篮" class="w-full h-48 object-cover">
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-1">时令鲜果篮</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">新鲜饱满，敬奉佳品</p>
                    <div class="flex justify-between items-center mb-3">
                        <span class="text-xl font-bold text-indigo-600 dark:text-indigo-400">¥88.00</span>
                        <span class="text-xs text-gray-400 dark:text-gray-500">已售 200+</span>
                    </div>
                    <button class="w-full bg-indigo-500 hover:bg-indigo-600 text-white font-medium py-2 px-4 rounded-md text-sm transition duration-300">
                        <i class="fas fa-cart-plus mr-2"></i>加入购物车
                    </button>
                </div>
            </div>

            <!-- 商品卡片示例 4 -->
            <div class="product-card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden border border-gray-200 dark:border-gray-700">
                <!-- 图片来源: https://images.unsplash.com/photo-1599791090080-0003b7ac1c47 (Unsplash - 纸钱元宝) -->
                <img src="https://images.unsplash.com/photo-1599791090080-0003b7ac1c47?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8am9zcyUyMHBhcGVyfGVufDB8fDB8fHww&auto=format&fit=crop&w=300&q=60" alt="环保纸钱元宝" class="w-full h-48 object-cover">
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-1">环保纸钱元宝</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">传统习俗，现代环保</p>
                    <div class="flex justify-between items-center mb-3">
                        <span class="text-xl font-bold text-indigo-600 dark:text-indigo-400">¥18.00</span>
                        <span class="text-xs text-gray-400 dark:text-gray-500">已售 500+</span>
                    </div>
                    <button class="w-full bg-indigo-500 hover:bg-indigo-600 text-white font-medium py-2 px-4 rounded-md text-sm transition duration-300">
                        <i class="fas fa-cart-plus mr-2"></i>加入购物车
                    </button>
                </div>
            </div>
            <!-- 更多商品卡片... -->
        </div>

        <!-- 分页 -->
        <div class="mt-12 flex justify-center">
            <nav aria-label="Page navigation">
                <ul class="inline-flex items-center -space-x-px">
                    <li>
                        <a href="#" class="py-2 px-3 ml-0 leading-tight text-gray-500 bg-white rounded-l-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">上一页</a>
                    </li>
                    <li>
                        <a href="#" aria-current="page" class="py-2 px-3 text-indigo-600 bg-indigo-50 border border-indigo-300 hover:bg-indigo-100 hover:text-indigo-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white">1</a>
                    </li>
                    <li>
                        <a href="#" class="py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">2</a>
                    </li>
                    <li>
                        <a href="#" class="py-2 px-3 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">3</a>
                    </li>
                    <li>
                        <a href="#" class="py-2 px-3 leading-tight text-gray-500 bg-white rounded-r-lg border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">下一页</a>
                    </li>
                </ul>
            </nav>
        </div>

    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400 py-8 text-center mt-12">
        <div class="container mx-auto">
            <p>&copy; 2024 归处在线祭祀平台. 保留所有权利.</p>
            <p class="text-sm mt-2">用心连接思念，科技传承记忆。</p>
        </div>
    </footer>

    <script>
        // 暗黑模式切换逻辑
        const themeToggleButton = document.getElementById('theme-toggle-button');
        const htmlElement = document.documentElement;
        const bodyElement = document.body;

        if (localStorage.getItem('theme') === 'dark') {
            htmlElement.classList.add('dark');
            bodyElement.classList.add('dark-mode');
            themeToggleButton.innerHTML = '<i class="fas fa-moon"></i>';
        } else {
            themeToggleButton.innerHTML = '<i class="fas fa-sun"></i>';
        }

        themeToggleButton.addEventListener('click', () => {
            if (htmlElement.classList.contains('dark')) {
                htmlElement.classList.remove('dark');
                bodyElement.classList.remove('dark-mode');
                localStorage.setItem('theme', 'light');
                themeToggleButton.innerHTML = '<i class="fas fa-sun"></i>';
            } else {
                htmlElement.classList.add('dark');
                bodyElement.classList.add('dark-mode');
                localStorage.setItem('theme', 'dark');
                themeToggleButton.innerHTML = '<i class="fas fa-moon"></i>';
            }
        });
    </script>
</body>
</html>