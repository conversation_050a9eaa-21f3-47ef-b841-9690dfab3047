<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>家族树 - 归处在线祭祀平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="dark-mode.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }
        .tree-container {
            /* Basic styling for tree, actual tree rendering would need a library or complex CSS/JS */
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            min-height: 400px;
        }
        .dark-mode .tree-container {
            border-color: #374151;
        }
        .member-node {
            background-color: #fff;
            border: 1px solid #cbd5e1;
            border-radius: 0.375rem;
            padding: 0.75rem 1rem;
            text-align: center;
            box-shadow: 0 1px 3px 0 rgba(0,0,0,0.1), 0 1px 2px 0 rgba(0,0,0,0.06);
            transition: all 0.2s ease-in-out;
        }
        .dark-mode .member-node {
            background-color: #2d3748;
            border-color: #4a5568;
            color: #e2e8f0;
        }
        .member-node:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 6px -1px rgba(0,0,0,0.1), 0 2px 4px -1px rgba(0,0,0,0.06);
        }
    </style>
</head>
<body class="flex flex-col min-h-screen">
    <!-- 暗黑模式接收器 -->
    <script>
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.documentElement.classList.add('dark');
            document.body.classList.add('dark-mode');
        }
    </script>

    <!-- 顶部导航栏 -->
    <header class="bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <i class="fas fa-sitemap text-indigo-600 text-2xl mr-3"></i>
                <span class="text-xl font-bold text-indigo-800 dark:text-indigo-400">归处</span>
            </div>
            <nav class="hidden md:flex space-x-4">
                <a href="index.html#home" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">首页</a>
                <a href="dashboard.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">我的纪念馆</a>
                <a href="worship.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">祭拜互动</a>
                <a href="ai_repair.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">AI照片修复</a>
                <a href="store.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">祭品商城</a>
                <a href="#" class="text-indigo-600 dark:text-indigo-400 font-semibold">家族树</a>
            </nav>
            <div class="flex items-center space-x-4">
                <button id="theme-toggle-button" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                    <i class="fas fa-sun"></i>
                </button>
                <a href="login.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">登录</a>
                <a href="register.html" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium">注册</a>
            </div>
        </div>
    </header>

    <!-- 主体内容 -->
    <main class="flex-grow container mx-auto px-4 py-8">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 dark:text-white mb-4">家族脉络传承</h1>
            <p class="text-lg text-gray-600 dark:text-gray-300">记录家族历史，传承家族记忆，一目了然的家族树。</p>
        </div>

        <div class="bg-white dark:bg-gray-800 p-6 sm:p-8 rounded-lg shadow-xl mb-8">
            <div class="flex flex-col sm:flex-row justify-between items-center mb-6">
                <h2 class="text-2xl font-semibold text-gray-700 dark:text-gray-200 mb-4 sm:mb-0">王氏家族树</h2>
                <div>
                    <button class="bg-indigo-500 hover:bg-indigo-600 text-white font-semibold py-2 px-4 rounded-lg transition duration-300 mr-2">
                        <i class="fas fa-plus mr-2"></i>添加成员
                    </button>
                    <button class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 font-semibold py-2 px-4 rounded-lg transition duration-300">
                        <i class="fas fa-edit mr-2"></i>编辑模式
                    </button>
                </div>
            </div>
            
            <div class="tree-container dark:bg-gray-700/50 flex flex-col items-center justify-center">
                <p class="text-gray-500 dark:text-gray-400 text-center py-10">
                    <i class="fas fa-sitemap text-4xl mb-4"></i><br>
                    家族树可视化区域。<br>
                    (此处通常需要专门的JS库来实现动态、可交互的树状图，例如 D3.js, GoJS, OrgChart.js 等。<br>
                    为简化原型，这里仅作占位示意。)
                </p>
                <!-- 示例静态节点 (非常简化) -->
                <div class="space-y-4 mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded">
                    <div class="member-node mx-auto w-48">
                        <p class="font-bold">王大锤 (曾祖父)</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">1890 - 1965</p>
                    </div>
                    <div class="flex justify-center space-x-8">
                        <div class="member-node w-40">
                            <p class="font-bold">王建国 (祖父)</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">1920 - 1990</p>
                        </div>
                        <div class="member-node w-40">
                            <p class="font-bold">王秀英 (祖母)</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400">1925 - 2005</p>
                        </div>
                    </div>
                    <div class="member-node mx-auto w-48">
                        <p class="font-bold">王强 (父亲)</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">1950 - 2020</p>
                    </div>
                </div>
            </div>
        </div>

    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400 py-8 text-center mt-12">
        <div class="container mx-auto">
            <p>&copy; 2024 归处在线祭祀平台. 保留所有权利.</p>
            <p class="text-sm mt-2">用心连接思念，科技传承记忆。</p>
        </div>
    </footer>

    <script>
        // 暗黑模式切换逻辑
        const themeToggleButton = document.getElementById('theme-toggle-button');
        const htmlElement = document.documentElement;
        const bodyElement = document.body;

        if (localStorage.getItem('theme') === 'dark') {
            htmlElement.classList.add('dark');
            bodyElement.classList.add('dark-mode');
            themeToggleButton.innerHTML = '<i class="fas fa-moon"></i>';
        } else {
            themeToggleButton.innerHTML = '<i class="fas fa-sun"></i>';
        }

        themeToggleButton.addEventListener('click', () => {
            if (htmlElement.classList.contains('dark')) {
                htmlElement.classList.remove('dark');
                bodyElement.classList.remove('dark-mode');
                localStorage.setItem('theme', 'light');
                themeToggleButton.innerHTML = '<i class="fas fa-sun"></i>';
            } else {
                htmlElement.classList.add('dark');
                bodyElement.classList.add('dark-mode');
                localStorage.setItem('theme', 'dark');
                themeToggleButton.innerHTML = '<i class="fas fa-moon"></i>';
            }
        });
    </script>
</body>
</html>