<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建完成 - 归处在线祭祀平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="dark-mode.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }
        .sidebar {
            background-color: #fff;
            border-right: 1px solid #e5e7eb;
            height: calc(100vh - 64px);
        }
        .dark-mode .sidebar {
            background-color: #1a1a1a;
            border-right: 1px solid #333;
        }
        .sidebar-item {
            border-radius: 0.5rem;
            transition: all 0.2s;
        }
        .sidebar-item:hover {
            background-color: #f3f4f6;
        }
        .dark-mode .sidebar-item:hover {
            background-color: #2d2d2d;
        }
        .sidebar-item.active {
            background-color: #e0e7ff;
            color: #4f46e5;
        }
        .dark-mode .sidebar-item.active {
            background-color: rgba(79, 70, 229, 0.2);
            color: #818cf8;
        }
        .step-item {
            position: relative;
        }
        .step-item:not(:last-child):after {
            content: '';
            position: absolute;
            top: 15px;
            left: 100%;
            height: 2px;
            width: 100%;
            background-color: #e5e7eb;
            transform: translateX(-50%);
            z-index: 0;
        }
        .step-item.active:not(:last-child):after,
        .step-item.completed:not(:last-child):after {
            background-color: #4f46e5;
        }
        .step-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e5e7eb;
            color: #9ca3af;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            z-index: 1;
            position: relative;
        }
        .step-item.active .step-circle {
            background-color: #4f46e5;
            color: white;
        }
        .step-item.completed .step-circle {
            background-color: #10b981;
            color: white;
        }
        .success-animation {
            animation: scale-up 0.5s ease-out;
        }
        @keyframes scale-up {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            70% {
                transform: scale(1.1);
                opacity: 1;
            }
            100% {
                transform: scale(1);
            }
        }
        .feature-card {
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body>
    <!-- 暗黑模式接收器 -->
    <script>
        // 检查本地存储中的主题偏好
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.documentElement.classList.add('dark');
            document.body.classList.add('dark-mode');
        }
    </script>
    <!-- 顶部导航栏 -->
    <header class="bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <i class="fas fa-pray text-indigo-600 text-2xl mr-3"></i>
                <span class="text-xl font-bold text-indigo-800 dark:text-indigo-400">归处</span>
            </div>
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <button class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-bell text-xl"></i>
                        <span class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full">2</span>
                    </button>
                </div>
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=100&q=80" alt="用户头像" class="w-8 h-8 rounded-full mr-2">
                    <!-- 图片来源：Unsplash -->
                    <span class="text-gray-700">李明</span>
                    <i class="fas fa-chevron-down ml-2 text-gray-500 text-xs"></i>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- 侧边栏 -->
        <aside class="sidebar w-64 p-4">
            <nav>
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.html" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-home mr-3"></i>
                            <span>我的纪念空间</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-users mr-3"></i>
                            <span>我的家族</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-sitemap mr-3"></i>
                            <span>族谱管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-magic mr-3"></i>
                            <span>AI服务</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-shopping-cart mr-3"></i>
                            <span>祭品商店</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-cog mr-3"></i>
                            <span>账户设置</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="flex-1 p-6 bg-gray-50">
            <div class="mb-8">
                <h1 class="text-2xl font-bold text-gray-800 mb-4">创建完成</h1>
                <p class="text-gray-600">您的纪念空间已成功创建，可以开始使用了</p>
            </div>

            <!-- 步骤指示器 -->
            <div class="flex justify-between mb-10 px-10">
                <div class="step-item completed flex flex-col items-center w-1/4">
                    <div class="step-circle"><i class="fas fa-check"></i></div>
                    <div class="mt-2 text-sm font-medium text-green-600">基本信息</div>
                </div>
                <div class="step-item completed flex flex-col items-center w-1/4">
                    <div class="step-circle"><i class="fas fa-check"></i></div>
                    <div class="mt-2 text-sm font-medium text-green-600">选择场景</div>
                </div>
                <div class="step-item completed flex flex-col items-center w-1/4">
                    <div class="step-circle"><i class="fas fa-check"></i></div>
                    <div class="mt-2 text-sm font-medium text-green-600">上传资料</div>
                </div>
                <div class="step-item active flex flex-col items-center w-1/4">
                    <div class="step-circle">4</div>
                    <div class="mt-2 text-sm font-medium text-indigo-600">完成创建</div>
                </div>
            </div>

            <!-- 完成信息 -->
            <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                <!-- 成功提示 -->
                <div class="flex flex-col items-center justify-center py-8 mb-8">
                    <div class="success-animation bg-green-100 text-green-600 rounded-full p-4 mb-4">
                        <i class="fas fa-check-circle text-5xl"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-2">创建成功！</h2>
                    <p class="text-gray-600 text-center max-w-md">您已成功创建「张伯伦先生」的纪念空间，现在您可以开始使用或邀请家人一起祭奠。</p>
                </div>
                
                <!-- 纪念空间预览 -->
                <div class="mb-8">
                    <h3 class="font-bold text-gray-800 mb-4">纪念空间预览</h3>
                    <div class="border border-gray-200 rounded-xl overflow-hidden">
                        <div class="h-64 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1518623001395-125242310d0c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1200&q=80')">
                            <!-- 图片来源：Unsplash -->
                            <div class="h-full w-full flex items-center justify-center bg-black bg-opacity-40">
                                <div class="text-center">
                                    <h3 class="text-white text-2xl font-bold mb-2">张伯伦先生纪念馆</h3>
                                    <p class="text-gray-200">1950 - 2023</p>
                                </div>
                            </div>
                        </div>
                        <div class="p-4 bg-white">
                            <div class="flex justify-between items-center">
                                <div>
                                    <span class="text-sm text-gray-500">创建于 2023年10月15日</span>
                                </div>
                                <div class="flex space-x-2">
                                    <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">公开</span>
                                    <span class="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded-full">中式陵园</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 功能介绍 -->
                <div class="mb-8">
                    <h3 class="font-bold text-gray-800 mb-4">您可以做什么？</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <!-- 功能1 -->
                        <div class="feature-card p-4 border border-gray-200 rounded-lg">
                            <div class="text-indigo-600 mb-3">
                                <i class="fas fa-share-alt text-2xl"></i>
                            </div>
                            <h4 class="font-bold text-gray-800 mb-2">分享纪念空间</h4>
                            <p class="text-gray-600 text-sm">邀请亲友一起祭奠，共同缅怀逝者</p>
                        </div>
                        
                        <!-- 功能2 -->
                        <div class="feature-card p-4 border border-gray-200 rounded-lg">
                            <div class="text-indigo-600 mb-3">
                                <i class="fas fa-edit text-2xl"></i>
                            </div>
                            <h4 class="font-bold text-gray-800 mb-2">编辑资料</h4>
                            <p class="text-gray-600 text-sm">随时更新照片、生平事迹等信息</p>
                        </div>
                        
                        <!-- 功能3 -->
                        <div class="feature-card p-4 border border-gray-200 rounded-lg">
                            <div class="text-indigo-600 mb-3">
                                <i class="fas fa-gifts text-2xl"></i>
                            </div>
                            <h4 class="font-bold text-gray-800 mb-2">献上祭品</h4>
                            <p class="text-gray-600 text-sm">选择鲜花、贡品等虚拟祭品表达思念</p>
                        </div>
                    </div>
                </div>
                
                <!-- 底部按钮 -->
                <div class="flex justify-between mt-10">
                    <a href="memorial_space.html" class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition duration-300">
                        <i class="fas fa-arrow-left mr-2"></i> 上一步
                    </a>
                    <div class="flex space-x-4">
                        <a href="#" class="border border-indigo-600 text-indigo-600 hover:bg-indigo-50 font-medium py-2 px-4 rounded-lg transition duration-300">
                            <i class="fas fa-share-alt mr-2"></i> 分享
                        </a>
                        <a href="dashboard.html" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-6 rounded-lg transition duration-300">
                            进入纪念空间 <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>