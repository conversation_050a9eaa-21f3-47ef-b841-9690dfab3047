<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI照片修复 - 归处在线祭祀平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="dark-mode.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }
        .upload-area {
            border: 2px dashed #cbd5e1;
            transition: all 0.3s ease;
        }
        .dark-mode .upload-area {
            border-color: #4a5568;
        }
        .upload-area:hover {
            border-color: #4f46e5;
            background-color: #f0f2f5;
        }
        .dark-mode .upload-area:hover {
            border-color: #818cf8;
            background-color: #1e1e1e;
        }
        .preview-image {
            max-height: 300px;
            object-fit: contain;
        }
    </style>
</head>
<body class="flex flex-col min-h-screen">
    <!-- 暗黑模式接收器 -->
    <script>
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.documentElement.classList.add('dark');
            document.body.classList.add('dark-mode');
        }
    </script>

    <!-- 顶部导航栏 -->
    <header class="bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <i class="fas fa-microchip text-indigo-600 text-2xl mr-3"></i>
                <span class="text-xl font-bold text-indigo-800 dark:text-indigo-400">归处</span>
            </div>
            <nav class="hidden md:flex space-x-4">
                <a href="index.html#home" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">首页</a>
                <a href="dashboard.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">我的纪念馆</a>
                <a href="worship.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">祭拜互动</a>
                <a href="#" class="text-indigo-600 dark:text-indigo-400 font-semibold">AI照片修复</a>
                <a href="store.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">祭品商城</a>
                <a href="family_tree.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">家族树</a>
            </nav>
            <div class="flex items-center space-x-4">
                <button id="theme-toggle-button" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                    <i class="fas fa-sun"></i>
                </button>
                <a href="login.html" class="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">登录</a>
                <a href="register.html" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium">注册</a>
            </div>
        </div>
    </header>

    <!-- 主体内容 -->
    <main class="flex-grow container mx-auto px-4 py-8">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 dark:text-white mb-4">AI 智能照片修复</h1>
            <p class="text-lg text-gray-600 dark:text-gray-300">上传您珍贵的老照片，让AI赋予它们新生。</p>
        </div>

        <div class="max-w-3xl mx-auto bg-white dark:bg-gray-800 p-8 rounded-lg shadow-xl">
            <div id="upload-section">
                <label for="photo-upload" class="upload-area block w-full p-10 text-center rounded-lg cursor-pointer mb-6">
                    <i class="fas fa-cloud-upload-alt text-4xl text-indigo-500 dark:text-indigo-400 mb-4"></i>
                    <p class="text-gray-700 dark:text-gray-300 font-semibold">点击此处或拖拽照片到这里上传</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">支持 JPG, PNG, WEBP 格式，最大 10MB</p>
                </label>
                <input type="file" id="photo-upload" class="hidden" accept="image/jpeg, image/png, image/webp">
            </div>

            <div id="processing-section" class="hidden">
                <div class="text-center py-10">
                    <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-indigo-500 mx-auto mb-4"></div>
                    <p class="text-xl font-semibold text-gray-700 dark:text-gray-200">正在智能修复中，请稍候...</p>
                </div>
            </div>

            <div id="result-section" class="hidden">
                <h2 class="text-2xl font-semibold text-gray-800 dark:text-white mb-6 text-center">修复效果对比</h2>
                <div class="grid md:grid-cols-2 gap-6 items-center">
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">原始照片</h3>
                        <!-- 图片来源: https://images.unsplash.com/photo-1560000059-5c5553e00912 (Unsplash - 老照片示例) -->
                        <img id="original-image" src="https://images.unsplash.com/photo-1560000059-5c5553e00912?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fG9sZCUyMHBob3RvfGVufDB8fDB8fHww&auto=format&fit=crop&w=400&q=60" alt="原始照片" class="preview-image w-full rounded-lg shadow-md border dark:border-gray-700">
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">修复后照片</h3>
                        <!-- 图片来源: https://images.unsplash.com/photo-1560000059-5c5553e00912 (Unsplash - 假设修复效果) -->
                        <img id="restored-image" src="https://images.unsplash.com/photo-1560000059-5c5553e00912?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fG9sZCUyMHBob3RvfGVufDB8fDB8fHww&auto=format&fit=crop&w=400&q=60&duotone=0077FF%2CFFFFFF&filt=sepia&sat=-50&con=5" alt="修复后照片" class="preview-image w-full rounded-lg shadow-md border dark:border-gray-700">
                    </div>
                </div>
                <div class="mt-8 text-center">
                    <button id="download-button" class="bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-6 rounded-lg transition duration-300 mr-4">
                        <i class="fas fa-download mr-2"></i>下载修复照片
                    </button>
                    <button id="upload-another-button" class="bg-indigo-500 hover:bg-indigo-600 text-white font-semibold py-3 px-6 rounded-lg transition duration-300">
                        <i class="fas fa-redo mr-2"></i>上传另一张
                    </button>
                </div>
            </div>
        </div>

    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400 py-8 text-center mt-12">
        <div class="container mx-auto">
            <p>&copy; 2024 归处在线祭祀平台. 保留所有权利.</p>
            <p class="text-sm mt-2">用心连接思念，科技传承记忆。</p>
        </div>
    </footer>

    <script>
        // 暗黑模式切换逻辑
        const themeToggleButton = document.getElementById('theme-toggle-button');
        const htmlElement = document.documentElement;
        const bodyElement = document.body;

        if (localStorage.getItem('theme') === 'dark') {
            htmlElement.classList.add('dark');
            bodyElement.classList.add('dark-mode');
            themeToggleButton.innerHTML = '<i class="fas fa-moon"></i>';
        } else {
            themeToggleButton.innerHTML = '<i class="fas fa-sun"></i>';
        }

        themeToggleButton.addEventListener('click', () => {
            if (htmlElement.classList.contains('dark')) {
                htmlElement.classList.remove('dark');
                bodyElement.classList.remove('dark-mode');
                localStorage.setItem('theme', 'light');
                themeToggleButton.innerHTML = '<i class="fas fa-sun"></i>';
            } else {
                htmlElement.classList.add('dark');
                bodyElement.classList.add('dark-mode');
                localStorage.setItem('theme', 'dark');
                themeToggleButton.innerHTML = '<i class="fas fa-moon"></i>';
            }
        });

        // AI 修复页面交互逻辑 (简单模拟)
        const uploadSection = document.getElementById('upload-section');
        const processingSection = document.getElementById('processing-section');
        const resultSection = document.getElementById('result-section');
        const photoUploadInput = document.getElementById('photo-upload');
        const originalImage = document.getElementById('original-image');
        const restoredImage = document.getElementById('restored-image');
        const uploadAnotherButton = document.getElementById('upload-another-button');

        photoUploadInput.addEventListener('change', (event) => {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    originalImage.src = e.target.result;
                    // 模拟修复后的图片，实际应用中这里会是AI处理后的图片URL
                    // 为演示，我们简单地对原图URL加一些参数来改变它，或者使用一个不同的图片
                    restoredImage.src = e.target.result + '?repaired=true'; // 仅为演示
                }
                reader.readAsDataURL(file);

                uploadSection.classList.add('hidden');
                processingSection.classList.remove('hidden');
                resultSection.classList.add('hidden');

                // 模拟处理时间
                setTimeout(() => {
                    processingSection.classList.add('hidden');
                    resultSection.classList.remove('hidden');
                }, 3000);
            }
        });

        uploadAnotherButton.addEventListener('click', () => {
            uploadSection.classList.remove('hidden');
            processingSection.classList.add('hidden');
            resultSection.classList.add('hidden');
            photoUploadInput.value = ''; // 清空文件选择
        });

    </script>
</body>
</html>