# 归处 App - 高保真 HTML 原型

本项目包含“归处”App 的高保真 HTML 原型，旨在模拟核心用户界面和交互流程，主要针对 Web 平台进行设计。

## 主要技术/库

*   HTML5
*   Tailwind CSS (通过 CDN)
*   FontAwesome (通过 CDN)
*   图片来源: Unsplash, Pexels (将在具体图片旁注释来源)

## 预览

通过打开 `index.html` 文件可以在浏览器中预览所有原型界面。

## 目标平台

*   主要: Web (桌面浏览器)
*   主题: 优先实现暗黑主题

## 结构

*   `index.html`: 所有原型页面的概览入口。
*   `*.html`: 各个独立的界面原型文件。
*   `css/`: (可选) 存放自定义的全局 CSS 文件。
*   `js/`: (可选) 存放用于增强交互的 JavaScript 文件。
*   `img/`: 存放原型中使用的本地图片资源。