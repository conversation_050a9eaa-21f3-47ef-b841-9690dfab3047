<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传资料 - 归处在线祭祀平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="dark-mode.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        body.dark-mode {
            background-color: #121212;
            color: #e0e0e0;
        }
        .sidebar {
            background-color: #fff;
            border-right: 1px solid #e5e7eb;
            height: calc(100vh - 64px);
        }
        .dark-mode .sidebar {
            background-color: #1a1a1a;
            border-right: 1px solid #333;
        }
        .sidebar-item {
            border-radius: 0.5rem;
            transition: all 0.2s;
        }
        .sidebar-item:hover {
            background-color: #f3f4f6;
        }
        .dark-mode .sidebar-item:hover {
            background-color: #2d2d2d;
        }
        .sidebar-item.active {
            background-color: #e0e7ff;
            color: #4f46e5;
        }
        .dark-mode .sidebar-item.active {
            background-color: rgba(79, 70, 229, 0.2);
            color: #818cf8;
        }
        .step-item {
            position: relative;
        }
        .step-item:not(:last-child):after {
            content: '';
            position: absolute;
            top: 15px;
            left: 100%;
            height: 2px;
            width: 100%;
            background-color: #e5e7eb;
            transform: translateX(-50%);
            z-index: 0;
        }
        .step-item.active:not(:last-child):after,
        .step-item.completed:not(:last-child):after {
            background-color: #4f46e5;
        }
        .step-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e5e7eb;
            color: #9ca3af;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            z-index: 1;
            position: relative;
        }
        .step-item.active .step-circle {
            background-color: #4f46e5;
            color: white;
        }
        .step-item.completed .step-circle {
            background-color: #10b981;
            color: white;
        }
        .upload-area {
            border: 2px dashed #cbd5e1;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #94a3b8;
            background-color: #f8fafc;
        }
        .form-input:focus {
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
        }
    </style>
</head>
<body>
    <!-- 暗黑模式接收器 -->
    <script>
        // 检查本地存储中的主题偏好
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            document.documentElement.classList.add('dark');
            document.body.classList.add('dark-mode');
        }
    </script>
    <!-- 顶部导航栏 -->
    <header class="bg-white dark:bg-gray-800 shadow-sm">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <i class="fas fa-pray text-indigo-600 text-2xl mr-3"></i>
                <span class="text-xl font-bold text-indigo-800 dark:text-indigo-400">归处</span>
            </div>
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <button class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-bell text-xl"></i>
                        <span class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full">2</span>
                    </button>
                </div>
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=100&q=80" alt="用户头像" class="w-8 h-8 rounded-full mr-2">
                    <!-- 图片来源：Unsplash -->
                    <span class="text-gray-700">李明</span>
                    <i class="fas fa-chevron-down ml-2 text-gray-500 text-xs"></i>
                </div>
            </div>
        </div>
    </header>

    <div class="flex">
        <!-- 侧边栏 -->
        <aside class="sidebar w-64 p-4 dark:bg-gray-800">
            <nav>
                <ul class="space-y-2">
                    <li>
                        <a href="dashboard.html" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-home mr-3"></i>
                            <span>我的纪念空间</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-users mr-3"></i>
                            <span>我的家族</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-sitemap mr-3"></i>
                            <span>族谱管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-magic mr-3"></i>
                            <span>AI服务</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-shopping-cart mr-3"></i>
                            <span>祭品商店</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" class="sidebar-item flex items-center p-3">
                            <i class="fas fa-cog mr-3"></i>
                            <span>账户设置</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="flex-1 p-6 bg-gray-50">
            <div class="mb-8">
                <h1 class="text-2xl font-bold text-gray-800 mb-4">上传资料</h1>
                <p class="text-gray-600">上传逝者照片、生平事迹等资料，完善纪念空间</p>
            </div>

            <!-- 步骤指示器 -->
            <div class="flex justify-between mb-10 px-10">
                <div class="step-item completed flex flex-col items-center w-1/4">
                    <div class="step-circle"><i class="fas fa-check"></i></div>
                    <div class="mt-2 text-sm font-medium text-green-600">基本信息</div>
                </div>
                <div class="step-item completed flex flex-col items-center w-1/4">
                    <div class="step-circle"><i class="fas fa-check"></i></div>
                    <div class="mt-2 text-sm font-medium text-green-600">选择场景</div>
                </div>
                <div class="step-item active flex flex-col items-center w-1/4">
                    <div class="step-circle">3</div>
                    <div class="mt-2 text-sm font-medium text-indigo-600">上传资料</div>
                </div>
                <div class="step-item flex flex-col items-center w-1/4">
                    <div class="step-circle">4</div>
                    <div class="mt-2 text-sm font-medium text-gray-500">完成创建</div>
                </div>
            </div>

            <!-- 上传资料表单 -->
            <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                <!-- 照片上传 -->
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">上传照片</h2>
                    <p class="text-gray-600 mb-4">上传逝者照片，将作为纪念空间的主要展示图片</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 主照片上传 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">主照片（必选）</label>
                            <div class="upload-area flex flex-col items-center justify-center p-6 cursor-pointer">
                                <div class="mb-4 text-center">
                                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-500">点击或拖拽上传照片</p>
                                    <p class="text-xs text-gray-400 mt-1">支持 JPG、PNG 格式，建议尺寸 800x1000px</p>
                                </div>
                                <button class="bg-indigo-50 hover:bg-indigo-100 text-indigo-600 font-medium py-2 px-4 rounded-lg transition duration-300">
                                    选择文件
                                </button>
                            </div>
                        </div>
                        
                        <!-- 生活照上传 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">生活照（可选，最多5张）</label>
                            <div class="upload-area flex flex-col items-center justify-center p-6 cursor-pointer">
                                <div class="mb-4 text-center">
                                    <i class="fas fa-images text-4xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-500">点击或拖拽上传多张照片</p>
                                    <p class="text-xs text-gray-400 mt-1">支持 JPG、PNG 格式，单张不超过5MB</p>
                                </div>
                                <button class="bg-indigo-50 hover:bg-indigo-100 text-indigo-600 font-medium py-2 px-4 rounded-lg transition duration-300">
                                    选择文件
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 基本资料 -->
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">基本资料</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="birth_date" class="block text-sm font-medium text-gray-700 mb-2">出生日期</label>
                            <input type="date" id="birth_date" class="form-input w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none">
                        </div>
                        <div>
                            <label for="death_date" class="block text-sm font-medium text-gray-700 mb-2">逝世日期</label>
                            <input type="date" id="death_date" class="form-input w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none">
                        </div>
                        <div class="md:col-span-2">
                            <label for="hometown" class="block text-sm font-medium text-gray-700 mb-2">籍贯</label>
                            <input type="text" id="hometown" placeholder="例如：江苏省南京市" class="form-input w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none">
                        </div>
                    </div>
                </div>
                
                <!-- 生平事迹 -->
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">生平事迹</h2>
                    <div class="mb-4">
                        <label for="biography" class="block text-sm font-medium text-gray-700 mb-2">生平简介</label>
                        <textarea id="biography" rows="5" placeholder="请输入逝者生平简介..." class="form-input w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none resize-none"></textarea>
                    </div>
                    
                    <!-- 生平大事记 -->
                    <div>
                        <div class="flex justify-between items-center mb-4">
                            <label class="block text-sm font-medium text-gray-700">生平大事记（可选）</label>
                            <button class="text-indigo-600 hover:text-indigo-800 text-sm font-medium flex items-center">
                                <i class="fas fa-plus mr-1"></i> 添加事件
                            </button>
                        </div>
                        
                        <!-- 事件列表 -->
                        <div class="space-y-4">
                            <!-- 事件1 -->
                            <div class="p-4 border border-gray-200 rounded-lg">
                                <div class="flex justify-between items-start mb-3">
                                    <div class="flex items-center">
                                        <span class="bg-indigo-100 text-indigo-800 text-xs font-medium px-2.5 py-1 rounded-full mr-2">1950年</span>
                                        <h3 class="font-medium text-gray-800">出生于江苏南京</h3>
                                    </div>
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <p class="text-gray-600 text-sm">1950年6月15日，出生于江苏省南京市鼓楼区一个知识分子家庭。</p>
                            </div>
                            
                            <!-- 事件2 -->
                            <div class="p-4 border border-gray-200 rounded-lg">
                                <div class="flex justify-between items-start mb-3">
                                    <div class="flex items-center">
                                        <span class="bg-indigo-100 text-indigo-800 text-xs font-medium px-2.5 py-1 rounded-full mr-2">1972年</span>
                                        <h3 class="font-medium text-gray-800">大学毕业</h3>
                                    </div>
                                    <button class="text-gray-400 hover:text-gray-600">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <p class="text-gray-600 text-sm">1972年7月，从南京大学中文系毕业，随后进入南京日报社工作。</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 视频资料 -->
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">视频资料（可选）</h2>
                    <div class="upload-area flex flex-col items-center justify-center p-6 cursor-pointer">
                        <div class="mb-4 text-center">
                            <i class="fas fa-film text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-500">上传纪念视频</p>
                            <p class="text-xs text-gray-400 mt-1">支持 MP4 格式，不超过200MB</p>
                        </div>
                        <button class="bg-indigo-50 hover:bg-indigo-100 text-indigo-600 font-medium py-2 px-4 rounded-lg transition duration-300">
                            选择文件
                        </button>
                    </div>
                </div>
                
                <!-- 访问权限设置 -->
                <div class="mb-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">访问权限设置</h2>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <input type="radio" id="public" name="access" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300" checked>
                            <label for="public" class="ml-2 block text-sm text-gray-700">
                                <span class="font-medium">公开</span>
                                <p class="text-gray-500 text-xs mt-1">所有人可访问，可通过搜索引擎找到</p>
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="family" name="access" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                            <label for="family" class="ml-2 block text-sm text-gray-700">
                                <span class="font-medium">仅限家族成员</span>
                                <p class="text-gray-500 text-xs mt-1">只有您邀请的家族成员可以访问</p>
                            </label>
                        </div>
                        <div class="flex items-center">
                            <input type="radio" id="private" name="access" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                            <label for="private" class="ml-2 block text-sm text-gray-700">
                                <span class="font-medium">私密</span>
                                <p class="text-gray-500 text-xs mt-1">仅自己可见</p>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 底部按钮 -->
                <div class="flex justify-between mt-10">
                    <a href="scene_selection.html" class="bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 px-4 rounded-lg transition duration-300">
                        <i class="fas fa-arrow-left mr-2"></i> 上一步
                    </a>
                    <a href="completion.html" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-6 rounded-lg transition duration-300">
                        下一步：完成创建 <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>
            </div>
        </main>
    </div>
</body>
</html>