# Babylon.js组件使用指南

*最后更新日期：2025年5月10日*

## 概述

本文档提供了项目中Babylon.js组件的使用指南，包括场景创建、物理系统、音频系统、性能监控和资源管理等方面的内容。

## 组件列表

项目中包含以下Babylon.js组件：

### 场景组件

- **BabylonScene.tsx** - 基础场景组件
- **ModelViewer.tsx** - 模型查看器组件
- **BuddhistTemple.tsx** - 佛教寺庙场景组件
- **PhysicsDemo.tsx** - 物理交互演示组件
- **BabylonMobileOptimizationDemo.tsx** - 移动设备优化演示组件
- **BabylonPerformanceDemo.tsx** - 性能监控演示组件

### 工具组件

- **BabylonPhysicsObject.tsx** - 物理对象组件
- **BabylonPerformanceMonitor.tsx** - 性能监控组件

### 工具类

- **BabylonResourceManager.ts** - 资源管理器
- **BabylonAudioManager.ts** - 音频管理器
- **BabylonPhysicsSystem.ts** - 物理系统
- **BabylonPhysicsComponentFactory.ts** - 物理组件工厂
- **BabylonSceneSystem.ts** - 场景系统
- **BabylonUtils.ts** - 工具函数
- **BabylonTestUtils.ts** - 测试工具
- **BabylonAssetPreloader.ts** - 资源预加载器

## 使用指南

### 创建基础场景

使用`BabylonScene`组件创建基础场景：

```tsx
import React from 'react';
import BabylonScene from '../components/BabylonScene';

const MyPage: React.FC = () => {
  return (
    <div style={{ width: '100%', height: '100vh' }}>
      <BabylonScene />
    </div>
  );
};

export default MyPage;
```

### 加载3D模型

使用`ModelViewer`组件加载3D模型：

```tsx
import React from 'react';
import ModelViewer from '../components/ModelViewer';

const MyModelPage: React.FC = () => {
  return (
    <div style={{ width: '100%', height: '100vh' }}>
      <ModelViewer modelPath="/models/my-model.glb" />
    </div>
  );
};

export default MyModelPage;
```

### 添加物理对象

使用`BabylonPhysicsObject`组件添加物理对象：

```tsx
import React from 'react';
import { Scene } from '@babylonjs/core';
import BabylonPhysicsObject from '../components/BabylonPhysicsObject';
import { PhysicsBodyType, PhysicsShapeType } from '../utils/BabylonPhysicsSystem';

// 在场景中使用
const MyPhysicsScene: React.FC<{ scene: Scene }> = ({ scene }) => {
  return (
    <>
      <BabylonPhysicsObject
        scene={scene}
        name="myBox"
        position={new Vector3(0, 5, 0)}
        size={new Vector3(1, 1, 1)}
        mass={1}
        bodyType={PhysicsBodyType.DYNAMIC}
        shapeType={PhysicsShapeType.BOX}
        color="#ff0000"
        onCollision={(collidedWith) => {
          console.log(`碰撞: ${collidedWith}`);
        }}
      />
    </>
  );
};
```

### 监控性能

使用`BabylonPerformanceMonitor`组件监控性能：

```tsx
import React, { useRef } from 'react';
import { Engine, Scene } from '@babylonjs/core';
import BabylonPerformanceMonitor from '../components/BabylonPerformanceMonitor';

const MyPerformanceScene: React.FC = () => {
  const sceneRef = useRef<Scene | null>(null);
  const engineRef = useRef<Engine | null>(null);
  
  // 场景创建代码...
  
  return (
    <div style={{ width: '100%', height: '100vh' }}>
      {/* 场景渲染代码... */}
      
      {sceneRef.current && engineRef.current && (
        <BabylonPerformanceMonitor
          scene={sceneRef.current}
          engine={engineRef.current}
          showInspector={false}
          enableOptimizer={true}
          position="top-right"
          showFps={true}
          showDrawCalls={true}
          showTriangles={true}
        />
      )}
    </div>
  );
};
```

### 使用音频系统

使用`BabylonAudioManager`管理音频：

```typescript
import { Scene } from '@babylonjs/core';
import BabylonAudioManager, { AudioType } from '../utils/BabylonAudioManager';

// 初始化音频系统
const initAudio = (scene: Scene) => {
  const audioManager = BabylonAudioManager;
  audioManager.init(scene);
  
  // 加载音频
  audioManager.loadAudio(
    'background',
    '/audio/background.mp3',
    AudioType.MUSIC,
    {
      loop: true,
      volume: 0.5,
      autoplay: true
    }
  );
  
  audioManager.loadAudio(
    'effect',
    '/audio/effect.mp3',
    AudioType.SOUND_EFFECT
  );
  
  // 播放音效
  audioManager.play('effect');
  
  // 设置音量
  audioManager.setVolume('background', 0.3);
  
  // 暂停音频
  audioManager.pause('background');
  
  // 恢复播放
  audioManager.play('background');
};
```

### 使用物理系统

使用`BabylonPhysicsSystem`管理物理：

```typescript
import { Scene, Vector3, MeshBuilder } from '@babylonjs/core';
import BabylonPhysicsSystem, { PhysicsBodyType, PhysicsShapeType } from '../utils/BabylonPhysicsSystem';

// 初始化物理系统
const initPhysics = async (scene: Scene) => {
  const physicsSystem = BabylonPhysicsSystem.getInstance();
  await physicsSystem.init(scene);
  
  // 创建地面
  const ground = MeshBuilder.CreateGround('ground', { width: 10, height: 10 }, scene);
  
  // 添加物理特性
  physicsSystem.addBody(
    'ground',
    ground,
    PhysicsBodyType.STATIC,
    PhysicsShapeType.BOX,
    {
      restitution: 0.5,
      friction: 0.1
    }
  );
  
  // 创建盒子
  const box = MeshBuilder.CreateBox('box', { size: 1 }, scene);
  box.position = new Vector3(0, 5, 0);
  
  // 添加物理特性
  physicsSystem.addBody(
    'box',
    box,
    PhysicsBodyType.DYNAMIC,
    PhysicsShapeType.BOX,
    {
      mass: 1,
      restitution: 0.5,
      friction: 0.5
    }
  );
  
  // 应用力
  physicsSystem.applyForce('box', new Vector3(0, 0, -10));
  
  // 监听碰撞事件
  physicsSystem.on('collision_start', (event) => {
    console.log(`碰撞: ${event.bodyA.id} 和 ${event.bodyB.id}`);
  });
};
```

### 预加载资源

使用`BabylonAssetPreloader`预加载资源：

```typescript
import { Scene } from '@babylonjs/core';
import BabylonAssetPreloader, { PreloadStatus } from '../utils/BabylonAssetPreloader';
import { AudioType } from '../utils/BabylonAudioManager';

// 预加载资源
const preloadAssets = async (scene: Scene) => {
  const preloader = BabylonAssetPreloader.getInstance();
  preloader.init(scene);
  
  // 配置预加载资源
  const config = {
    models: [
      {
        id: 'temple',
        path: '/models/buddhist-temple.glb'
      }
    ],
    textures: [
      {
        id: 'ground',
        path: '/textures/ground.jpg'
      }
    ],
    cubeTextures: [
      {
        id: 'skybox',
        rootUrl: '/textures/skybox/',
        extensions: ['px.jpg', 'py.jpg', 'pz.jpg', 'nx.jpg', 'ny.jpg', 'nz.jpg']
      }
    ],
    audio: [
      {
        id: 'background',
        path: '/audio/background.mp3',
        type: AudioType.MUSIC,
        options: {
          loop: true,
          volume: 0.5
        }
      }
    ]
  };
  
  // 开始预加载
  const success = await preloader.preload(config, (progress) => {
    console.log(`加载进度: ${progress.percentage}%`);
    
    if (progress.status === PreloadStatus.COMPLETED) {
      console.log('加载完成');
    } else if (progress.status === PreloadStatus.ERROR) {
      console.error('加载错误:', progress.errors);
    }
  });
  
  return success;
};
```

## 最佳实践

1. **性能优化**：
   - 使用`BabylonPerformanceMonitor`监控性能
   - 使用`SceneOptimizer`自动优化场景
   - 使用LOD（细节层次）技术减少远处物体的复杂度
   - 合并静态网格以减少绘制调用

2. **资源管理**：
   - 使用`BabylonAssetPreloader`预加载资源
   - 使用`BabylonResourceManager`管理资源生命周期
   - 释放不再使用的资源以减少内存占用

3. **物理系统**：
   - 尽量使用简单的碰撞体（盒子、球体）
   - 对于静态物体使用`PhysicsBodyType.STATIC`
   - 使用事件系统处理碰撞事件

4. **音频系统**：
   - 预加载音频文件
   - 使用适当的音频类型（音乐、音效）
   - 实现音量控制和静音功能

5. **移动设备优化**：
   - 使用`DeviceDetector`检测设备性能
   - 根据设备性能调整渲染质量
   - 减少阴影和后处理效果
   - 使用压缩纹理减少内存占用

## 故障排除

1. **性能问题**：
   - 使用`BabylonPerformanceMonitor`查找性能瓶颈
   - 减少场景中的网格数量和多边形数量
   - 优化光照和阴影设置
   - 使用`SceneOptimizer`自动优化场景

2. **物理问题**：
   - 检查碰撞体是否正确设置
   - 调整物理参数（质量、摩擦力、弹性）
   - 使用`PhysicsViewer`可视化碰撞体

3. **资源加载问题**：
   - 检查资源路径是否正确
   - 使用`BabylonAssetPreloader`的错误回调捕获加载错误
   - 确保资源格式兼容（glTF、glb）

4. **音频问题**：
   - 检查音频文件格式是否兼容（mp3、wav）
   - 确保音频管理器已正确初始化
   - 检查音量设置和静音状态

## 参考资源

- [Babylon.js官方文档](https://doc.babylonjs.com/)
- [Babylon.js示例](https://www.babylonjs.com/demos/)
- [Babylon.js论坛](https://forum.babylonjs.com/)
- [Babylon.js GitHub仓库](https://github.com/BabylonJS/Babylon.js)
