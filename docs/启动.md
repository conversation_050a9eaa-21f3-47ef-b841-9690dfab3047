# 项目启动指南

## 后端启动

### 使用统一服务启动脚本（推荐）

项目根目录下提供了一个统一服务启动脚本，可以一键启动后端服务器（包含渲染服务）：

```bash
# 在项目根目录下执行
./start_unified_service.sh
```

这个脚本会自动处理以下工作：
- 检查必要的依赖是否已安装
- 确保日志目录存在
- 停止可能已经运行的 Gunicorn 进程
- 启动 Gunicorn 服务器，使用`gevent_patch:app`作为入口点
- 如果 Gunicorn 启动失败，会自动尝试使用 Flask 开发服务器

> 注意：不再需要单独启动渲染服务，所有功能都已整合到统一服务中。详细信息请参阅 [统一服务文档](统一服务.md)。

### 使用原始启动脚本（不推荐）

如果需要，仍然可以使用原始的启动脚本：

```bash
# 在项目根目录下执行
./start_backend.sh
```

这个脚本的功能与统一服务启动脚本类似，但不包含渲染服务的依赖检查。

### 手动启动

推荐使用以下命令启动后端服务器（解决了 gevent monkey patching 警告问题）：

```bash
cd backend && gunicorn -c gunicorn.conf.py gevent_patch:app
```

这个命令使用了预加载脚本 `gevent_patch.py`，它在导入其他模块之前执行 gevent 的 monkey patching，从而避免了警告消息。

### 替代启动方式

如果出现问题，也可以使用以下替代方式：

```bash
# 使用原始入口点（推荐方式，与 gunicorn.conf.py 配置一致）
cd backend && gunicorn -c gunicorn.conf.py app.main:app

# 不使用配置文件的简化方式
cd backend && gunicorn --bind 0.0.0.0:5001 --workers 4 --worker-class gevent gevent_patch:app

# 开发模式（使用 Flask 内置服务器）
cd backend && python run.py
```

## 性能监控

启动后，可以使用以下命令检查服务器状态：

```bash
# 检查监听端口
lsof -i :5001

# 查看 Gunicorn 进程
ps aux | grep gunicorn
```

## 日志查看

日志文件位于：
- 访问日志：`backend/logs/access.log`
- 错误日志：`backend/logs/error.log`

同时，日志也会输出到控制台。
