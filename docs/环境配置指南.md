# Memorial项目环境配置指南

## 概述

本指南将帮助您配置Memorial项目的开发环境，实现自动激活conda环境的功能。

## 自动环境激活方案

### 方案1: 使用direnv (推荐)

direnv是一个环境变量管理工具，可以在进入项目目录时自动加载环境变量和激活conda环境。

#### 安装direnv

**macOS:**
```bash
# 使用Homebrew安装
brew install direnv

# 或使用MacPorts
sudo port install direnv
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt-get update
sudo apt-get install direnv
```

**Linux (CentOS/RHEL):**
```bash
sudo yum install direnv
# 或
sudo dnf install direnv
```

#### 配置Shell

将direnv集成到您的shell中：

**Bash (.bashrc):**
```bash
echo 'eval "$(direnv hook bash)"' >> ~/.bashrc
source ~/.bashrc
```

**Zsh (.zshrc):**
```bash
echo 'eval "$(direnv hook zsh)"' >> ~/.zshrc
source ~/.zshrc
```

**Fish (.config/fish/config.fish):**
```bash
echo 'direnv hook fish | source' >> ~/.config/fish/config.fish
```

#### 激活项目环境

```bash
# 进入项目目录
cd /path/to/memorial

# 首次使用需要允许.envrc文件
direnv allow

# 之后每次进入目录都会自动激活环境
```

### 方案2: VSCode集成

项目已配置VSCode设置，打开项目时会自动：
- 选择memorial conda环境作为Python解释器
- 在终端中激活conda环境
- 设置正确的PYTHONPATH

#### VSCode配置说明

`.vscode/settings.json`文件包含以下配置：

```json
{
    "python.defaultInterpreterPath": "~/anaconda3/envs/memorial/bin/python",
    "python.terminal.activateEnvironment": true,
    "python.terminal.activateEnvInCurrentTerminal": true,
    "terminal.integrated.env.osx": {
        "CONDA_DEFAULT_ENV": "memorial",
        "PYTHONPATH": "${workspaceFolder}/backend"
    }
}
```

### 方案3: 手动激活脚本

使用项目提供的激活脚本：

```bash
# 运行激活脚本
./activate_env.sh
```

该脚本会：
1. 检查conda是否安装
2. 检查memorial环境是否存在，不存在则创建
3. 激活memorial环境
4. 设置项目相关环境变量
5. 检查关键依赖是否安装

## 环境创建和管理

### 使用environment.yml创建环境

```bash
# 创建完整的memorial环境
conda env create -f environment.yml

# 更新现有环境
conda env update -f environment.yml --prune
```

### 手动创建环境

```bash
# 创建基础环境
conda create -n memorial python=3.11 -y

# 激活环境
conda activate memorial

# 安装基础依赖
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
conda install transformers diffusers gradio -c conda-forge
pip install fastapi uvicorn sqlalchemy alembic

# 安装AI相关依赖
pip install deepface mediapipe insightface ultralytics

# 安装3D渲染依赖
pip install moderngl moderngl-window PyOpenGL trimesh
```

### 环境变量配置

项目使用以下环境变量：

```bash
# 核心配置
CONDA_DEFAULT_ENV=memorial
PYTHONPATH=./backend
PROJECT_ROOT=.
MEMORIAL_ENV=development

# 数据库配置
DATABASE_URL=postgresql://memorial_user:memorial_pass@localhost:5432/memorial_db
REDIS_URL=redis://localhost:6379/0

# API配置
API_V1_STR=/api/v1
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

## 故障排除

### 常见问题

1. **conda命令未找到**
   ```bash
   # 检查conda是否在PATH中
   echo $PATH | grep conda
   
   # 重新初始化conda
   conda init
   source ~/.bashrc  # 或 ~/.zshrc
   ```

2. **direnv不工作**
   ```bash
   # 检查direnv是否正确安装
   which direnv
   
   # 检查shell hook是否配置
   echo $PROMPT_COMMAND | grep direnv  # bash
   echo $precmd_functions | grep direnv  # zsh
   ```

3. **VSCode不识别Python环境**
   - 按 `Cmd+Shift+P` (macOS) 或 `Ctrl+Shift+P` (Windows/Linux)
   - 输入 "Python: Select Interpreter"
   - 选择memorial环境的Python解释器

4. **环境变量未生效**
   ```bash
   # 检查环境变量
   echo $PYTHONPATH
   echo $CONDA_DEFAULT_ENV
   
   # 重新加载配置
   source .envrc  # 如果使用direnv
   ./activate_env.sh  # 或使用激活脚本
   ```

### 验证环境配置

运行以下命令验证环境是否正确配置：

```bash
# 检查Python环境
python --version
which python

# 检查关键依赖
python -c "import torch; print(f'PyTorch: {torch.__version__}')"
python -c "import transformers; print(f'Transformers: {transformers.__version__}')"
python -c "import fastapi; print(f'FastAPI: {fastapi.__version__}')"

# 检查环境变量
echo "PYTHONPATH: $PYTHONPATH"
echo "PROJECT_ROOT: $PROJECT_ROOT"
echo "CONDA_DEFAULT_ENV: $CONDA_DEFAULT_ENV"
```

## 最佳实践

1. **使用direnv**: 推荐使用direnv实现自动环境激活
2. **定期更新**: 定期更新environment.yml文件
3. **版本锁定**: 在生产环境中锁定依赖版本
4. **环境隔离**: 为不同项目使用不同的conda环境
5. **备份配置**: 定期备份环境配置文件

## 相关文档

- [direnv官方文档](https://direnv.net/)
- [Conda用户指南](https://docs.conda.io/projects/conda/en/latest/user-guide/)
- [VSCode Python配置](https://code.visualstudio.com/docs/python/environments)