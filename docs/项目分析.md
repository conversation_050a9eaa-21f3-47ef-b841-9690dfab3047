# Memorial - AI驱动的数字纪念馆平台 - 项目分析总结

## 1. 项目概述与愿景

*   **项目名称**：Memorial - AI驱动的数字纪念馆平台
*   **核心愿景**：成为全球领先的在线祭祀平台，致力于传承记忆、连接情感、构建永恒的数字传承空间。
*   **主要业务目标**：用户稳步增长、年收入达到3000万、社会影响力逐步扩大。
*   **解决痛点**：解决多位先人难以同时关照、扫墓不便、家人难以凑齐等问题，并满足用户缅怀先人、设置族谱、分享记忆的核心需求。
*   **核心特性**：AI智能服务（图像修复、语音克隆、3D重建）、3D沉浸体验、多平台支持、个性化定制、隐私保护、多语言支持。
*   **独特卖点 (USP)**：最新的3D技术、多宗教兼容、后台AI模型（图片修复、声音克隆）。

## 2. 技术架构分析

*   **整体架构**：采用分层架构设计，包括API层、服务层、数据访问层、基础设施层。初期采用单体应用架构，未来可平滑迁移至微服务。
*   **前端技术栈**：
    *   **框架**：React 18 + TypeScript 5.x
    *   **3D渲染**：Babylon.js 8.0 + WebGL/WebGPU (已完成从Three.js的核心迁移)
    *   **UI/CSS**：Tailwind CSS
    *   **构建工具**：Vite
    *   **状态管理**：React Hooks + Context API
    *   **性能优化**：自适应渲染、设备检测、代码分割、图像懒加载、智能缓存。
*   **后端技术栈**：
    *   **框架**：FastAPI + Python 3.11 (高性能异步框架)
    *   **数据库**：PostgreSQL 14+ (关系型数据库，支持JSON、全文搜索、地理位置)
    *   **ORM**：SQLAlchemy + Alembic (数据库迁移)
    *   **认证**：JWT + OAuth2
    *   **3D渲染**：ModernGL (用于预处理，与前端Babylon.js分工明确)
    *   **AI/ML**：PyTorch + Transformers + OpenCV
    *   **任务队列**：Celery + Redis (处理AI等耗时任务，异步处理)
    *   **缓存**：Redis
    *   **文件存储**：对象存储服务 (如阿里云OSS)
    *   **搜索引擎**：PostgreSQL全文搜索 (初期) / Elasticsearch (后期)
    *   **日志管理**：Loguru + ELK Stack
    *   **监控**：Prometheus + Grafana
*   **移动端技术栈**：
    *   **框架**：Flutter 3.x + Dart
    *   **架构**：功能模块化 (Clean Architecture)
    *   **UI设计**：Cupertino Design + Material Design
    *   **状态管理**：Provider + Bloc Pattern
    *   **平台**：iOS + Android + Web PWA
    *   **性能优化**：懒加载、分页、图片缓存、代码分割。

## 3. 核心功能模块分析

*   **用户中心**：注册、登录、个人信息管理、认证授权。
*   **纪念空间**：创建、管理、查询、个性化定制（场景、音乐、隐私级别）。
*   **资源管理**：照片、视频、音频等纪念资源的上传、管理、AI增强。
*   **祭拜互动**：在线祭拜（献花、点烛等）、留言板功能。
*   **家族与族谱**：家族创建、成员管理、族谱编辑、复杂关系支持（多配偶、收养、旁系等），以交互式可视化图谱展示。
*   **AI服务**：图像修复、语音克隆、3D重建等AI任务的提交、查询、状态管理。主要集成Replicate.com，通过异步队列处理。
*   **商店与支付**：虚拟祭品、增值服务（高级场景、AI点数包）的购买与支付。

## 4. 3D渲染与AI集成细节

*   **3D模型生命周期**：
    *   **创建**：3D模型通过Blender等工具创建，以 `.blend` 和 `.glb` 格式存储在 `3d-assets` 目录。
    *   **预处理**：后端使用 `ModernGL` 对3D模型进行预处理，可能包括优化、格式转换、生成LOD (Level of Detail) 模型等，以适应不同设备性能。
    *   **前端渲染**：前端 `Babylon.js` 负责在Web端进行实时3D渲染，加载预处理后的 `.glb` 模型，并处理场景交互、光照、物理效果等。
*   **AI服务集成**：
    *   **集成方式**：AI服务主要通过集成第三方服务 `Replicate.com` 实现，辅以部分自研能力。
    *   **处理流程**：用户提交AI任务后，后端将其放入异步队列 (Celery + Redis) 进行处理。AI服务调用 `Replicate.com` 的API执行具体任务（如图像修复、语音克隆），完成后将结果（如处理后的图片URL）返回给用户。

## 5. 部署与运维策略

*   **部署计划**：项目计划部署到生产环境，采用 `Docker + Kubernetes` 进行容器化部署和编排，以实现高可用、可伸缩的部署。
*   **日志管理**：使用 `Loguru` 进行日志记录，并计划集成 `ELK Stack` (Elasticsearch, Logstash, Kibana) 进行集中式日志收集、存储和分析。
*   **性能监控**：使用 `Prometheus + Grafana` 进行系统和业务指标监控，包括服务器资源、API性能、错误率、用户活跃度等，并已部署。

## 6. 项目当前阶段与未来展望

*   **当前优先级**：目前最核心的开发任务是继续完善3D渲染功能和加强AI服务。
*   **未来展望**：
    *   继续迁移和优化剩余的3D场景到Babylon.js。
    *   深化AI服务能力，探索更多创新应用。
    *   持续优化移动端体验，确保多平台一致性。
    *   完善家族与族谱的复杂关系管理和可视化。
    *   根据业务发展，逐步扩展商店功能和支付方式。
    *   持续关注性能优化、安全性和隐私保护。

## 7. 架构与流程图 (Mermaid)

```mermaid
graph TD
    A[用户] -->|访问| B(Web前端 / 移动端)
    B -->|API请求| C(后端服务 FastAPI)
    C -->|数据操作| D(PostgreSQL 数据库)
    C -->|异步任务| E(Celery 任务队列)
    E -->|调用| F(AI服务 Replicate.com / 自研AI)
    F -->|结果存储| G(对象存储 OSS)
    C -->|缓存| H(Redis)
    B -->|3D渲染| I(Babylon.js)
    I -->|加载模型| J(3D资产 .glb)
    J -->|预处理| K(ModernGL)
    C -->|认证| L(JWT / OAuth2)
    C -->|日志| M(ELK Stack)
    C -->|监控| N(Prometheus / Grafana)

    subgraph 前端
        B
        I
    end

    subgraph 后端
        C
        D
        E
        F
        G
        H
        K
        L
        M
        N
    end

    subgraph 核心功能
        C1(用户管理)
        C2(纪念空间管理)
        C3(资源管理)
        C4(祭拜互动)
        C5(家族与族谱)
        C6(AI服务接口)
        C7(商店与支付)
    end

    C --> C1
    C --> C2
    C --> C3
    C --> C4
    C --> C5
    C --> C6
    C --> C7
    C2 --> I
    C3 --> G
    C6 --> F
    C7 --> H