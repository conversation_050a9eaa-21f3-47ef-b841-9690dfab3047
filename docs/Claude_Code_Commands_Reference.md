# Claude Code 命令参考手册

## 概述

Claude Code 提供了一系列内置命令来增强开发体验。这些命令以 `/` 开头，可以在任何对话中使用来管理会话、配置环境和获取帮助。

---

## 📋 命令分类

### 🐛 反馈与支持
#### `/bug` - 提交反馈
**功能**: 提交关于 Claude Code 的错误报告或功能建议

**使用场景**:
- 遇到软件缺陷或异常行为
- 希望建议新功能或改进
- 报告性能问题或兼容性问题

**示例**:
```bash
/bug
# 会打开反馈表单或提供提交反馈的渠道
```

**最佳实践**:
- 详细描述问题的复现步骤
- 包含环境信息（操作系统、版本等）
- 附上相关的错误信息或截图

---

### 🧹 会话管理
#### `/clear` - 清除对话历史
**功能**: 完全清除当前会话的对话历史，释放上下文空间

**使用场景**:
- 会话变得过长，影响性能
- 需要重新开始新的讨论主题
- 上下文信息过多导致响应不准确

**示例**:
```bash
/clear
# 清除所有对话历史，从头开始
```

**注意事项**:
- ⚠️ 会丢失所有之前的对话上下文
- 💡 如果需要保留重要信息，考虑使用 `/compact`

#### `/compact` - 压缩对话历史
**功能**: 清除详细的对话历史，但保留关键信息摘要

**语法**:
```bash
/compact [自定义摘要指示]
```

**使用场景**:
- 会话过长但仍需保留项目上下文
- 希望优化性能同时保持连续性
- 需要重新聚焦到特定主题

**示例**:
```bash
# 基础用法
/compact

# 自定义摘要重点
/compact 重点保留认证系统开发进度和架构决策

# 项目相关摘要
/compact 保留已完成的功能模块和下一步开发计划
```

**优势**:
- ✅ 保持项目连续性
- ✅ 释放上下文空间
- ✅ 提高响应效率

---

### ⚙️ 配置与设置
#### `/config` - 配置面板
**功能**: 打开 Claude Code 的配置界面

**语法**:
```bash
/config [theme]
```

**配置选项**:
- **主题设置**: 明亮/暗色主题切换
- **编辑器偏好**: 字体、缩进、语法高亮
- **API设置**: 模型选择、超时配置
- **安全设置**: 权限管理、文件访问

**示例**:
```bash
# 打开通用配置
/config

# 直接打开主题设置
/config theme
```

**常用配置项**:
- 🎨 界面主题和颜色方案
- 📝 代码格式化偏好
- 🔧 工具集成设置
- 🛡️ 安全和隐私选项

---

### 💰 使用监控
#### `/cost` - 查看使用成本
**功能**: 显示当前会话的总成本和持续时间

**信息包含**:
- **Token使用量**: 输入/输出token统计
- **会话时长**: 从开始到当前的时间
- **估算成本**: 基于API使用的费用预估
- **使用趋势**: 最近的使用模式分析

**示例输出**:
```
📊 会话统计信息
─────────────────────
⏱️  会话时长: 45分钟
🔤 输入Tokens: 12,450
📤 输出Tokens: 8,230
💰 估算成本: $0.84
📈 平均速度: 23 tokens/秒
```

**使用场景**:
- 监控API使用成本
- 优化长时间会话
- 评估项目开发预算

---

### 🔧 系统诊断
#### `/doctor` - 健康检查
**功能**: 检查 Claude Code 安装和运行环境的健康状态

**检查项目**:
- **环境变量**: API密钥和配置
- **依赖包**: 必需的软件包版本
- **文件权限**: 读写访问权限
- **网络连接**: API服务可用性
- **系统资源**: 内存和存储空间

**示例输出**:
```
🏥 Claude Code 健康检查
─────────────────────
✅ API连接正常
✅ 配置文件完整
✅ 文件权限正确
⚠️  内存使用偏高 (建议重启)
❌ 某些依赖包需要更新

🔧 建议操作:
1. 运行 npm update 更新依赖
2. 重启应用以释放内存
```

**使用场景**:
- 安装后验证环境
- 排查功能异常问题
- 定期维护检查

---

### 🚪 会话控制
#### `/exit` (或 `/quit`) - 退出REPL
**功能**: 安全退出 Claude Code REPL 环境

**特性**:
- 自动保存当前会话状态
- 清理临时文件和缓存
- 优雅关闭所有连接

**示例**:
```bash
/exit
# 或
/quit
```

**等效操作**:
- `Ctrl+C` (可能不够优雅)
- `Ctrl+D` (EOF信号)

---

### ❓ 帮助系统
#### `/help` - 显示帮助信息
**功能**: 显示所有可用命令和使用说明

**输出内容**:
- 命令列表和简要说明
- 基本使用语法
- 常见问题解答
- 快速入门指南

**示例**:
```bash
/help
# 显示完整的命令参考和帮助信息
```

---

### 🔗 IDE集成
#### `/ide` - IDE集成管理
**功能**: 管理与各种IDE的集成状态和配置

**支持的IDE**:
- **VS Code**: Claude Code扩展
- **IntelliJ IDEA**: 插件集成
- **Sublime Text**: 包管理
- **Vim/Neovim**: 插件配置

**功能选项**:
- 查看当前集成状态
- 配置IDE连接设置
- 同步代码片段和模板
- 管理快捷键绑定

**示例输出**:
```
🔧 IDE 集成状态
─────────────────
✅ VS Code: 已连接 (v1.2.3)
❌ IntelliJ: 未安装插件
⚠️  Vim: 配置需要更新

🔧 可用操作:
1. 安装 IntelliJ 插件
2. 更新 Vim 配置
3. 同步 VS Code 设置
```

---

### 📁 项目初始化
#### `/init` - 初始化CLAUDE.md
**功能**: 在当前目录创建 CLAUDE.md 项目文档文件

**生成内容**:
- 项目基本信息和描述
- 技术栈和依赖列表
- 开发环境配置指南
- 常用命令和脚本
- 项目结构说明

**使用场景**:
- 新项目开始时建立文档
- 为现有项目添加Claude支持
- 团队协作时统一项目理解

**示例**:
```bash
/init
# 在当前目录创建 CLAUDE.md 文件
```

**生成的文档模板**:
```markdown
# 项目名称

## 项目概述
[项目描述和目标]

## 技术栈
[使用的技术和工具]

## 开发指南
[环境配置和启动方式]

## 项目结构
[目录结构说明]
```

---

## 🎯 最佳实践建议

### 📈 效率优化
1. **定期使用 `/compact`** 而不是 `/clear` 来保持上下文连续性
2. **用 `/cost` 监控** 长时间开发会话的使用情况
3. **通过 `/doctor`** 定期检查环境健康状态

### 🔧 配置管理
1. **初次使用前** 运行 `/config` 设置偏好
2. **项目开始时** 使用 `/init` 创建文档
3. **团队协作中** 确保 IDE 集成配置一致

### 🐛 问题排查
1. **遇到异常时** 首先运行 `/doctor` 诊断
2. **功能问题** 通过 `/bug` 及时反馈
3. **性能问题** 使用 `/cost` 分析资源使用

### 💡 会话管理
```bash
# 推荐的会话清理流程
/cost                    # 检查当前使用情况
/compact 保留项目核心信息  # 压缩历史但保留重要上下文
/doctor                  # 确认环境状态正常
```

---

## 🔗 相关资源

- **官方文档**: [Claude Code Documentation](https://docs.anthropic.com/claude-code)
- **GitHub Issues**: 问题报告和功能请求
- **社区论坛**: 用户交流和经验分享
- **更新日志**: 新功能和修复说明

---

*本文档最后更新时间: 2025-06-10*  
*Claude Code 版本要求: v1.0+*