<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>候选人齐天下 - 深度研究与潜力分析报告</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!--
    Narrative & Structure Plan:
    1.  Headline: Introduce the "product" - <PERSON>, a high-potential talent from Tsinghua.
    2.  Core Competency Overview: Visualize the dual-major background (Engineering + Finance) using a Venn diagram and highlight key stats.
    3.  Comprehensive Skill Matrix: Use a Radar chart to display the breadth and depth of technical and soft skills.
    4.  Project Deep Dive: Illustrate the JUNO research project with a simple HTML/CSS flowchart.
    5.  Leadership & Impact Analysis: Showcase leadership roles and their quantifiable impact using a Bar chart and "big number" callouts with icons.
    6.  SWOT Analysis: Provide a strategic summary of the candidate's profile.
    7.  Conclusion/Contact: Summarize the potential and provide contact placeholders.

    Visualization Selection:
    -   Market Size (Dual Major): Venn Diagram (Goal: Organize, Method: HTML/CSS, NO SVG).
    -   Key Stats (GPA, Award): Big Number (Goal: Inform, Method: HTML/CSS).
    -   Skill Distribution: Radar Chart (Goal: Compare, Method: Chart.js/Canvas, NO SVG).
    -   Project Process: Flow Chart (Goal: Organize, Method: HTML/CSS, NO SVG).
    -   Leadership Impact: Bar Chart & Pictograph (Goal: Compare/Inform, Method: Chart.js/Canvas & HTML/Unicode, NO SVG).
    -   Strategic Profile: SWOT Table (Goal: Organize, Method: HTML/CSS, NO SVG).

    Color Palette Selection: Energetic & Playful (#00A6A6, #F2A413, #E66C37, #C44E52, #58508D).

    Constraint Confirmation:
    -   NO SVG graphics were used in this document.
    -   NO Mermaid JS was used for diagrams. All diagrams are built with HTML/CSS.
    -->
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #f0f4f8;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
        .venn-diagram {
            position: relative;
            width: 280px;
            height: 180px;
        }
        .venn-circle {
            position: absolute;
            width: 180px;
            height: 180px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 1.25rem;
            text-align: center;
            padding: 1rem;
        }
        .venn-circle-1 {
            background-color: rgba(0, 166, 166, 0.7); /* #00A6A6 */
            top: 0;
            left: 0;
            z-index: 1;
        }
        .venn-circle-2 {
            background-color: rgba(242, 164, 19, 0.7); /* #F2A413 */
            top: 0;
            right: 0;
            z-index: 1;
        }
        .venn-intersection {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2;
            color: #58508D;
            font-weight: bold;
            font-size: 1.1rem;
            width: 100px;
            text-align: center;
        }
        .flowchart {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        .flowchart-step {
            background-color: #ffffff;
            border: 2px solid #58508D;
            color: #58508D;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 500;
            text-align: center;
            flex-shrink: 0;
        }
        .flowchart-arrow {
            color: #58508D;
            font-size: 2rem;
            font-weight: bold;
        }
    </style>
</head>
<body class="text-gray-800">

    <div class="container p-4 mx-auto md:p-8">

        <header class="mb-12 text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-[#58508D] mb-2">深度研究报告</h1>
            <p class="text-xl md:text-2xl text-[#C44E52] font-semibold">高潜力复合型人才分析：齐天下</p>
            <div class="w-24 h-1.5 bg-[#F2A413] mx-auto mt-4 rounded-full"></div>
        </header>

        <section id="overview" class="mb-12">
            <div class="grid items-center grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                <div class="p-6 bg-white rounded-lg shadow-md lg:col-span-2">
                    <h2 class="text-2xl font-bold text-[#58508D] mb-4">核心竞争力概览</h2>
                     <p class="mb-6 text-gray-600">
                        本报告分析对象为清华大学在读学生齐天下。其独特的教育背景融合了前沿的核工程技术与严谨的经济金融理论，构成了其核心竞争力。这种跨学科的知识结构预示着其在处理复杂问题、连接技术与商业方面具备独特的视角和强大的潜力。
                    </p>
                    <div class="flex items-center justify-center">
                        <div class="venn-diagram">
                            <div class="venn-circle venn-circle-1">核工程与<br>核技术</div>
                            <div class="venn-circle venn-circle-2">经济与<br>金融</div>
                            <div class="venn-intersection">复合型<br>知识体系</div>
                        </div>
                    </div>
                </div>

                <div class="space-y-6">
                    <div class="p-6 text-center bg-white rounded-lg shadow-md">
                        <h3 class="text-lg font-bold text-[#58508D] mb-2">平均学分绩 (GPA)</h3>
                        <p class="text-5xl font-bold text-[#00A6A6]">2.87<span class="text-2xl text-gray-500">/4.0</span></p>
                        <p class="mt-2 text-xs text-gray-500">反映了其在顶尖学府扎实的学习基础</p>
                    </div>
                    <div class="p-6 text-center bg-white rounded-lg shadow-md">
                        <h3 class="text-lg font-bold text-[#58508D] mb-2">课程竞赛奖项</h3>
                        <p class="text-4xl font-bold text-[#F2A413]">优胜奖</p>
                        <p class="mt-2 text-sm text-gray-600">《金融学专题研究》</p>
                    </div>
                </div>
            </div>
        </section>
        
        <section id="skills" class="p-6 mb-12 bg-white rounded-lg shadow-md">
            <h2 class="text-2xl font-bold text-center text-[#58508D] mb-2">综合能力矩阵</h2>
            <p class="mb-6 text-center text-gray-600">通过雷达图直观展示候选人在多个维度的能力分布，体现其在编程、专业知识和金融思维上的均衡发展。</p>
            <div class="chart-container">
                <canvas id="skillsRadarChart"></canvas>
            </div>
        </section>

        <section id="project" class="p-6 mb-12 bg-white rounded-lg shadow-md">
            <h2 class="text-2xl font-bold text-center text-[#58508D] mb-2">科研项目深度解析</h2>
            <p class="mb-8 text-center text-gray-600">以江门中微子实验（JUNO）的探测器模拟项目为例，展示其将理论知识应用于复杂科研实践的能力。</p>
            <div class="pb-4 overflow-x-auto">
                <div class="flowchart min-w-[600px]">
                    <div class="flowchart-step">理论学习<br>(大数据方法)</div>
                    <div class="flowchart-arrow">&rarr;</div>
                    <div class="flowchart-step">环境搭建<br>(Linux/Debian)</div>
                    <div class="flowchart-arrow">&rarr;</div>
                    <div class="flowchart-step">编程实现<br>(Python模拟)</div>
                    <div class="flowchart-arrow">&rarr;</div>
                    <div class="flowchart-step">数据分析<br>& 结果验证</div>
                </div>
            </div>
        </section>

        <section id="leadership" class="mb-12">
            <h2 class="text-2xl font-bold text-center text-[#58508D] mb-8">领导力与社会影响力</h2>
             <div class="grid grid-cols-1 gap-8 md:grid-cols-2">
                <div class="p-6 bg-white rounded-lg shadow-md">
                    <h3 class="text-xl font-bold text-[#58508D] mb-4">实践活动影响力</h3>
                     <p class="mb-6 text-gray-600">“汾韵乡情”社会实践支队的核心经历，不仅获得了校内最高荣誉，更吸引了国家级媒体的广泛报道，展现了卓越的团队协作和成果转化能力。</p>
                    <div class="flex items-center justify-around text-center">
                        <div>
                            <p class="text-5xl text-[#E66C37]">🏆</p>
                            <p class="text-lg font-bold text-[#E66C37]">校级金奖</p>
                            <p class="text-sm text-gray-500">实践最高荣誉</p>
                        </div>
                        <div>
                            <p class="text-5xl text-[#E66C37]">📺</p>
                            <p class="text-lg font-bold text-[#E66C37]">4+</p>
                            <p class="text-sm text-gray-500">国家级媒体报道</p>
                        </div>
                    </div>
                </div>
                <div class="p-6 bg-white rounded-lg shadow-md">
                     <h3 class="text-xl font-bold text-[#58508D] mb-4">组织管理量化分析</h3>
                    <p class="mb-6 text-gray-600">在多个学生组织中担任要职，其活动组织、对外联络和社群管理的成果可通过数据直观体现。</p>
                    <div class="h-64 chart-container md:h-72">
                         <canvas id="leadershipBarChart"></canvas>
                    </div>
                </div>
            </div>
        </section>
        
        <section id="swot" class="mb-12">
             <h2 class="text-2xl font-bold text-center text-[#58508D] mb-8">候选人SWOT战略分析</h2>
             <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div class="bg-white border-l-4 border-[#00A6A6] rounded-r-lg shadow p-6">
                    <h3 class="text-xl font-bold text-[#00A6A6] mb-2">优势 (Strengths)</h3>
                    <ul class="space-y-1 text-gray-700 list-disc list-inside">
                        <li>清华大学顶尖教育背景</li>
                        <li>核工+金融复合知识体系，稀缺性强</li>
                        <li>具备经国家级媒体报道的社会实践成果</li>
                        <li>丰富的学生组织领导经验与组织能力</li>
                    </ul>
                </div>
                 <div class="bg-white border-l-4 border-[#F2A413] rounded-r-lg shadow p-6">
                    <h3 class="text-xl font-bold text-[#F2A413] mb-2">劣势 (Weaknesses)</h3>
                    <ul class="space-y-1 text-gray-700 list-disc list-inside">
                        <li>GPA（2.87）在顶尖学生中不具绝对优势</li>
                        <li>缺乏企业级项目的直接工作经验</li>
                        <li>金融领域的实践经验主要来自课程与社团</li>
                    </ul>
                </div>
                <div class="bg-white border-l-4 border-[#E66C37] rounded-r-lg shadow p-6">
                    <h3 class="text-xl font-bold text-[#E66C37] mb-2">机会 (Opportunities)</h3>
                    <ul class="space-y-1 text-gray-700 list-disc list-inside">
                        <li>金融科技、硬科技投资等交叉领域需求旺盛</li>
                        <li>核能产业的商业化与市场化发展</li>
                        <li>咨询行业对具备深度技术理解的分析师需求</li>
                        <li>强大的校友网络与社会资源</li>
                    </ul>
                </div>
                 <div class="bg-white border-l-4 border-[#C44E52] rounded-r-lg shadow p-6">
                    <h3 class="text-xl font-bold text-[#C44E52] mb-2">挑战 (Threats)</h3>
                    <ul class="space-y-1 text-gray-700 list-disc list-inside">
                        <li>需在求职市场与专业背景更纯粹的候选人竞争</li>
                        <li>如何将双重背景快速转化为职场生产力</li>
                        <li>技术与金融领域知识更新迭代速度快</li>
                    </ul>
                </div>
             </div>
        </section>

        <footer class="pt-8 text-center border-t border-gray-300">
             <h3 class="text-xl font-bold text-[#58508D] mb-2">结论与展望</h3>
             <p class="max-w-3xl mx-auto mb-4 text-gray-600">
                齐天下是一位具备罕见跨学科背景和杰出领导潜力的候选人。其在硬核科技领域的深度和在经济金融领域的广度，共同塑造了一个能够理解技术内核、洞察商业价值的独特视角。虽然在校成绩和企业经验方面尚有提升空间，但其卓越的实践成果和强大的学习能力预示着巨大的成长潜力。对于寻求能够驾驭技术与商业复杂性的创新型岗位的公司而言，他是一个值得重点关注的高潜力投资对象。
            </p>
            <p class="font-semibold text-gray-800">[联系电话] | [电子邮箱]</p>
        </footer>

    </div>

    <script>
        const FONT_COLOR = '#374151';
        const GRID_COLOR = '#e5e7eb';

        function processLabels(labels, maxLength = 16) {
            return labels.map(label => {
                if (typeof label !== 'string' || label.length <= maxLength) {
                    return label;
                }
                const words = label.split(' ');
                let currentLine = '';
                const lines = [];
                for (const word of words) {
                    if ((currentLine + ' ' + word).trim().length > maxLength) {
                        lines.push(currentLine.trim());
                        currentLine = word;
                    } else {
                        currentLine = (currentLine + ' ' + word).trim();
                    }
                }
                if (currentLine) {
                    lines.push(currentLine.trim());
                }
                return lines;
            });
        }

        const tooltipTitleCallback = (tooltipItems) => {
            const item = tooltipItems[0];
            if (!item) return '';
            let label = item.chart.data.labels[item.dataIndex];
            return Array.isArray(label) ? label.join(' ') : label;
        };

        const chartPlugins = {
            legend: {
                labels: {
                    color: FONT_COLOR,
                    font: {
                        size: 14
                    }
                }
            },
            tooltip: {
                callbacks: {
                    title: tooltipTitleCallback
                },
                bodyFont: {
                    size: 14
                },
                titleFont: {
                    size: 16,
                    weight: 'bold'
                }
            }
        };

        function createSkillsRadarChart() {
            const ctx = document.getElementById('skillsRadarChart').getContext('2d');
            const labels = processLabels([
                'Python编程', '硬件语言(Verilog)', '金融理论', 
                '核物理专业知识', '数据分析与处理', '领导与组织'
            ]);
            new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '能力评估',
                        data: [85, 75, 80, 90, 85, 95],
                        backgroundColor: 'rgba(0, 166, 166, 0.2)',
                        borderColor: 'rgba(0, 166, 166, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(0, 166, 166, 1)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgba(0, 166, 166, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: chartPlugins,
                    scales: {
                        r: {
                            angleLines: { color: GRID_COLOR },
                            grid: { color: GRID_COLOR },
                            pointLabels: {
                                color: FONT_COLOR,
                                font: {
                                    size: 14
                                }
                            },
                            ticks: {
                                color: FONT_COLOR,
                                backdropColor: 'rgba(255, 255, 255, 0.75)',
                                stepSize: 20
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });
        }
        
        function createLeadershipBarChart() {
            const ctx = document.getElementById('leadershipBarChart').getContext('2d');
            const labels = processLabels(['高校联络', '活动覆盖人数', '社团管理人数']);
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '量化指标',
                        data: [20, 500, 100],
                        backgroundColor: [
                            'rgba(242, 164, 19, 0.7)',
                            'rgba(230, 108, 55, 0.7)',
                            'rgba(88, 80, 141, 0.7)'
                        ],
                        borderColor: [
                            'rgba(242, 164, 19, 1)',
                            'rgba(230, 108, 55, 1)',
                            'rgba(88, 80, 141, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            callbacks: {
                                title: tooltipTitleCallback
                            }
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            grid: { color: GRID_COLOR },
                            ticks: { color: FONT_COLOR }
                        },
                        y: {
                            grid: { display: false },
                            ticks: { color: FONT_COLOR }
                        }
                    }
                }
            });
        }
        
        window.onload = function() {
            createSkillsRadarChart();
            createLeadershipBarChart();
        };

    </script>
</body>
</html>
