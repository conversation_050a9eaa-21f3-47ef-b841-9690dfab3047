# 从Three.js迁移到Babylon.js 8.0指南

## 概述

本文档提供了从Three.js迁移到Babylon.js 8.0的详细指南，包括概念对比、API差异、迁移步骤和最佳实践。

## 迁移准备

### 安装Babylon.js

```bash
# 使用pnpm安装
pnpm add babylonjs babylonjs-loaders babylonjs-materials babylonjs-gui

# 如果需要物理引擎
pnpm add babylonjs-physics
```

### React集成

对于React项目，可以使用react-babylonjs：

```bash
pnpm add react-babylonjs
```

## 核心概念对比

| Three.js | Babylon.js | 说明 |
|----------|------------|------|
| Scene | Scene | 场景容器，但Babylon.js的Scene功能更丰富 |
| PerspectiveCamera | FreeCamera, ArcRotateCamera | Babylon提供多种预设相机类型 |
| WebGLRenderer | Engine | 渲染引擎，Babylon自动处理WebGL/WebGPU |
| Mesh | Mesh | 网格对象，但API不同 |
| Material | Material | 材质系统，Babylon有更多内置材质类型 |
| Light | Light | 光照系统，Babylon支持更多光源类型 |
| Object3D.position | Mesh.position | 位置属性，但Babylon使用Vector3 |
| OrbitControls | ArcRotateCamera | 相机控制，Babylon集成在相机中 |
| GLTFLoader | SceneLoader | 模型加载，Babylon有统一的加载API |
| BoxGeometry | MeshBuilder.CreateBox | 创建立方体网格 |
| SphereGeometry | MeshBuilder.CreateSphere | 创建球体网格 |
| PlaneGeometry | MeshBuilder.CreateGround | 创建平面/地面网格 |
| CylinderGeometry | MeshBuilder.CreateCylinder | 创建圆柱体网格 |
| MeshStandardMaterial | StandardMaterial | 标准PBR材质 |
| MeshBasicMaterial | StandardMaterial (不受光照影响) | 基础材质 |
| AmbientLight | HemisphericLight | 环境光照 |
| DirectionalLight | DirectionalLight | 平行光 |
| PointLight | PointLight | 点光源 |
| SpotLight | SpotLight | 聚光灯 |
| TextureLoader | Texture | 纹理加载 |
| Vector3 | Vector3 | 三维向量 |
| Quaternion | Quaternion | 四元数 |
| Color | Color3, Color4 | 颜色(Color4包含透明度) |
| Raycaster | PickingInfo | 射线检测/拾取 |
| InstancedMesh | InstancedMesh | 实例化渲染 |
| Group | TransformNode | 对象分组/父节点 |
| AnimationMixer | AnimationGroup | 动画系统 |
| EffectComposer | PostProcess | 后期处理效果 |
| CSS3DRenderer | HTML元素 | HTML集成 |
| WebGLCubeRenderTarget | CubeTexture | 立方体贴图 |
| PMREMGenerator | CubeTexture.CreateFromPrefilteredData | 预过滤环境贴图 |
| ShaderMaterial | ShaderMaterial | 自定义着色器材质 |
| Clock | Engine.getDeltaTime | 时间/帧率管理 |
| LoadingManager | SceneLoader事件 | 加载管理 |
| AudioListener | Sound | 音频系统 |

## 基础场景迁移

### Three.js场景

```javascript
// Three.js
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';

// 创建场景
const scene = new THREE.Scene();

// 创建相机
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
camera.position.set(0, 5, 10);

// 创建渲染器
const renderer = new THREE.WebGLRenderer();
renderer.setSize(window.innerWidth, window.innerHeight);
document.body.appendChild(renderer.domElement);

// 添加控制器
const controls = new OrbitControls(camera, renderer.domElement);

// 添加光源
const light = new THREE.DirectionalLight(0xffffff, 1);
light.position.set(1, 1, 1);
scene.add(light);

// 添加网格
const geometry = new THREE.BoxGeometry(1, 1, 1);
const material = new THREE.MeshStandardMaterial({ color: 0x00ff00 });
const cube = new THREE.Mesh(geometry, material);
scene.add(cube);

// 渲染循环
function animate() {
  requestAnimationFrame(animate);
  cube.rotation.x += 0.01;
  cube.rotation.y += 0.01;
  controls.update();
  renderer.render(scene, camera);
}
animate();
```

### 迁移到Babylon.js

```javascript
// Babylon.js
import * as BABYLON from 'babylonjs';

// 创建画布
const canvas = document.createElement('canvas');
document.body.appendChild(canvas);

// 创建引擎
const engine = new BABYLON.Engine(canvas, true);

// 创建场景
const createScene = function() {
  const scene = new BABYLON.Scene(engine);

  // 创建相机（等同于OrbitControls）
  const camera = new BABYLON.ArcRotateCamera("camera", -Math.PI / 2, Math.PI / 2.5, 10, new BABYLON.Vector3(0, 0, 0), scene);
  camera.attachControl(canvas, true);

  // 添加光源
  const light = new BABYLON.DirectionalLight("light", new BABYLON.Vector3(-1, -1, -1), scene);

  // 添加网格
  const box = BABYLON.MeshBuilder.CreateBox("box", {size: 1}, scene);
  const material = new BABYLON.StandardMaterial("material", scene);
  material.diffuseColor = new BABYLON.Color3(0, 1, 0);
  box.material = material;

  // 添加动画
  scene.registerBeforeRender(function() {
    box.rotation.x += 0.01;
    box.rotation.y += 0.01;
  });

  return scene;
};

// 创建场景
const scene = createScene();

// 渲染循环
engine.runRenderLoop(function() {
  scene.render();
});

// 响应窗口大小变化
window.addEventListener('resize', function() {
  engine.resize();
});
```

## React组件迁移

### Three.js (react-three-fiber)

```jsx
// Three.js with react-three-fiber
import React, { useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';

function Box(props) {
  const meshRef = useRef();

  useFrame(() => {
    meshRef.current.rotation.x += 0.01;
    meshRef.current.rotation.y += 0.01;
  });

  return (
    <mesh {...props} ref={meshRef}>
      <boxGeometry args={[1, 1, 1]} />
      <meshStandardMaterial color="green" />
    </mesh>
  );
}

export default function ThreeScene() {
  return (
    <Canvas>
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 5]} />
      <Box position={[0, 0, 0]} />
      <OrbitControls />
    </Canvas>
  );
}
```

### Babylon.js (react-babylonjs)

```jsx
// Babylon.js with react-babylonjs
import React, { useRef } from 'react';
import { Engine, Scene, useBeforeRender } from 'react-babylonjs';
import { Vector3, Color3 } from '@babylonjs/core';

function RotatingBox(props) {
  const boxRef = useRef(null);

  useBeforeRender((scene) => {
    if (boxRef.current) {
      boxRef.current.rotation.x += 0.01;
      boxRef.current.rotation.y += 0.01;
    }
  });

  return (
    <box name="box" ref={boxRef} size={1} position={props.position}>
      <standardMaterial name="material" diffuseColor={Color3.Green()} />
    </box>
  );
}

export default function BabylonScene() {
  return (
    <Engine antialias adaptToDeviceRatio canvasId="babylonJS">
      <Scene>
        <arcRotateCamera name="camera" target={Vector3.Zero()} alpha={-Math.PI / 2} beta={Math.PI / 2.5} radius={3} />
        <hemisphericLight name="light" intensity={0.7} direction={Vector3.Up()} />
        <RotatingBox position={new Vector3(0, 0, 0)} />
      </Scene>
    </Engine>
  );
}
```

### 模型加载组件迁移

#### Three.js (react-three-fiber)

```jsx
// Three.js 模型加载组件
import React, { Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import { useGLTF, OrbitControls } from '@react-three/drei';

function Model({ path }) {
  const { scene } = useGLTF(path);
  return <primitive object={scene} />;
}

export default function ModelViewer() {
  return (
    <Canvas>
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 5]} />
      <Suspense fallback={null}>
        <Model path="/models/model.glb" />
      </Suspense>
      <OrbitControls />
    </Canvas>
  );
}
```

#### Babylon.js (react-babylonjs)

```jsx
// Babylon.js 模型加载组件
import React, { useState } from 'react';
import { Engine, Scene, Model } from 'react-babylonjs';
import { Vector3 } from '@babylonjs/core';

export default function ModelViewer() {
  const [modelReady, setModelReady] = useState(false);

  return (
    <div style={{ width: '100%', height: '100vh' }}>
      <Engine antialias adaptToDeviceRatio canvasId="modelViewer">
        <Scene>
          <arcRotateCamera
            name="camera"
            target={Vector3.Zero()}
            alpha={-Math.PI / 2}
            beta={Math.PI / 3}
            radius={10}
          />

          <hemisphericLight
            name="light"
            intensity={0.7}
            direction={new Vector3(0, 1, 0)}
          />

          <Model
            rootUrl="/models/"
            sceneFilename="model.glb"
            onModelLoaded={() => setModelReady(true)}
            scaling={new Vector3(1, 1, 1)}
          />
        </Scene>
      </Engine>

      {!modelReady && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: 'white',
          background: 'rgba(0,0,0,0.5)',
          padding: '10px 20px',
          borderRadius: '5px'
        }}>
          加载模型中...
        </div>
      )}
    </div>
  );
}
```

## 高级功能迁移

### 模型加载

#### Three.js
```javascript
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';

const loader = new GLTFLoader();
loader.load('model.glb', (gltf) => {
  scene.add(gltf.scene);
});
```

#### Babylon.js
```javascript
// 使用SceneLoader.ImportMesh加载模型
BABYLON.SceneLoader.ImportMesh("", "/", "model.glb", scene, (meshes) => {
  // meshes包含所有加载的网格
});

// 或者使用LoadAssetContainer (Babylon.js 8.0推荐)
const assetContainer = await BABYLON.SceneLoader.LoadAssetContainerAsync("/", "model.glb", scene);
assetContainer.addAllToScene();
```

### LOD (Level of Detail) 系统

#### Three.js
```javascript
import * as THREE from 'three';
import { LOD } from 'three';

// 创建LOD对象
const lod = new LOD();

// 创建不同细节级别的网格
const highDetailGeometry = new THREE.SphereGeometry(1, 64, 64);
const mediumDetailGeometry = new THREE.SphereGeometry(1, 32, 32);
const lowDetailGeometry = new THREE.SphereGeometry(1, 16, 16);

const material = new THREE.MeshStandardMaterial({ color: 0x00ff00 });

// 创建不同细节级别的网格
const highDetailMesh = new THREE.Mesh(highDetailGeometry, material);
const mediumDetailMesh = new THREE.Mesh(mediumDetailGeometry, material);
const lowDetailMesh = new THREE.Mesh(lowDetailGeometry, material);

// 添加到LOD对象，指定距离
lod.addLevel(highDetailMesh, 0);    // 近距离使用高细节
lod.addLevel(mediumDetailMesh, 10); // 中等距离使用中等细节
lod.addLevel(lowDetailMesh, 20);    // 远距离使用低细节

// 添加到场景
scene.add(lod);
```

#### Babylon.js
```javascript
// 创建不同细节级别的网格
const highDetailMesh = BABYLON.MeshBuilder.CreateSphere("highDetail", { segments: 64 }, scene);
const mediumDetailMesh = BABYLON.MeshBuilder.CreateSphere("mediumDetail", { segments: 32 }, scene);
const lowDetailMesh = BABYLON.MeshBuilder.CreateSphere("lowDetail", { segments: 16 }, scene);

// 设置材质
const material = new BABYLON.StandardMaterial("material", scene);
material.diffuseColor = new BABYLON.Color3(0, 1, 0);
highDetailMesh.material = material;
mediumDetailMesh.material = material;
lowDetailMesh.material = material;

// 设置LOD
highDetailMesh.addLODLevel(10, mediumDetailMesh);
highDetailMesh.addLODLevel(20, lowDetailMesh);

// 默认情况下，中等和低细节网格会被隐藏，只有当相机距离达到指定值时才会显示
```

### React组件中的LOD系统

#### Three.js (react-three-fiber)
```jsx
import React, { useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { LOD, useGLTF } from '@react-three/drei';

function ModelWithLOD() {
  // 加载不同细节级别的模型
  const { scene: highDetail } = useGLTF('/models/high-detail.glb');
  const { scene: mediumDetail } = useGLTF('/models/medium-detail.glb');
  const { scene: lowDetail } = useGLTF('/models/low-detail.glb');

  return (
    <LOD>
      <primitive object={highDetail.clone()} distance={0} />
      <primitive object={mediumDetail.clone()} distance={10} />
      <primitive object={lowDetail.clone()} distance={20} />
    </LOD>
  );
}

export default function Scene() {
  return (
    <Canvas>
      <ambientLight intensity={0.5} />
      <ModelWithLOD />
    </Canvas>
  );
}
```

#### Babylon.js (react-babylonjs)
```jsx
import React, { useState } from 'react';
import { Engine, Scene, useScene, useBeforeRender, Model } from 'react-babylonjs';
import { Vector3 } from '@babylonjs/core';

// 自适应LOD组件
function AdaptiveLOD({ modelPath, levels = [], position = [0, 0, 0] }) {
  const scene = useScene();
  const [currentLevel, setCurrentLevel] = useState(0);

  // 确保有默认级别
  const allLevels = [
    { distance: 0, path: modelPath },
    ...levels
  ].sort((a, b) => a.distance - b.distance);

  // 更新LOD可见性
  useBeforeRender(() => {
    if (!scene || !scene.activeCamera) return;

    // 获取相机
    const camera = scene.activeCamera;

    // 计算相机到模型位置的距离
    const modelPosition = new Vector3(position[0], position[1], position[2]);
    const distance = camera.globalPosition.subtract(modelPosition).length();

    // 确定当前LOD级别
    let newLevelIndex = 0;
    for (let i = 1; i < allLevels.length; i++) {
      if (distance >= allLevels[i].distance) {
        newLevelIndex = i;
      } else {
        break;
      }
    }

    if (newLevelIndex !== currentLevel) {
      setCurrentLevel(newLevelIndex);
    }
  });

  // 获取当前应该显示的模型路径
  const currentModelPath = allLevels[currentLevel]?.path || modelPath;

  // 解析路径获取rootUrl和文件名
  const lastSlashIndex = currentModelPath.lastIndexOf('/');
  const rootUrl = currentModelPath.substring(0, lastSlashIndex + 1);
  const fileName = currentModelPath.substring(lastSlashIndex + 1);

  return (
    <transformNode
      name="LODRootNode"
      position={new Vector3(position[0], position[1], position[2])}
    >
      <Model
        name={`model-lod-${currentLevel}`}
        rootUrl={rootUrl}
        sceneFilename={fileName}
      />
    </transformNode>
  );
}

export default function Scene() {
  return (
    <Engine antialias adaptToDeviceRatio canvasId="babylonJS">
      <Scene>
        <arcRotateCamera
          name="camera"
          target={Vector3.Zero()}
          alpha={-Math.PI / 2}
          beta={Math.PI / 3}
          radius={20}
        />
        <hemisphericLight
          name="light"
          intensity={0.7}
          direction={new Vector3(0, 1, 0)}
        />
        <AdaptiveLOD
          modelPath="/models/high-detail.glb"
          levels={[
            { distance: 10, path: "/models/medium-detail.glb" },
            { distance: 20, path: "/models/low-detail.glb" }
          ]}
        />
      </Scene>
    </Engine>
  );
}
```

### 物理引擎

#### Three.js (使用cannon.js)
```javascript
import * as CANNON from 'cannon';

// 创建物理世界
const world = new CANNON.World();
world.gravity.set(0, -9.82, 0);

// 创建物理物体
const boxBody = new CANNON.Body({
  mass: 1,
  position: new CANNON.Vec3(0, 5, 0),
  shape: new CANNON.Box(new CANNON.Vec3(0.5, 0.5, 0.5))
});
world.addBody(boxBody);

// 更新循环
function animate() {
  requestAnimationFrame(animate);
  world.step(1/60);
  // 同步Three.js网格与物理物体
  cube.position.copy(boxBody.position);
  cube.quaternion.copy(boxBody.quaternion);
  renderer.render(scene, camera);
}
```

#### Babylon.js
```javascript
// 启用物理引擎 (Babylon.js 8.0支持多种物理引擎)
scene.enablePhysics(new BABYLON.Vector3(0, -9.81, 0), new BABYLON.HavokPlugin());
// 或使用Ammo.js
// scene.enablePhysics(new BABYLON.Vector3(0, -9.81, 0), new BABYLON.AmmoJSPlugin());

// 创建物理物体
const box = BABYLON.MeshBuilder.CreateBox("box", {size: 1}, scene);
box.position.y = 5;
// 添加物理属性
box.physicsImpostor = new BABYLON.PhysicsImpostor(
  box,
  BABYLON.PhysicsImpostor.BoxImpostor,
  { mass: 1, restitution: 0.7 },
  scene
);
```

### 粒子系统

#### Three.js
```javascript
import * as THREE from 'three';

// 创建粒子系统
const particleCount = 1000;
const particles = new THREE.BufferGeometry();
const positions = new Float32Array(particleCount * 3);

for (let i = 0; i < particleCount; i++) {
  positions[i * 3] = (Math.random() - 0.5) * 10;
  positions[i * 3 + 1] = (Math.random() - 0.5) * 10;
  positions[i * 3 + 2] = (Math.random() - 0.5) * 10;
}

particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));

const particleMaterial = new THREE.PointsMaterial({
  color: 0xffffff,
  size: 0.1
});

const particleSystem = new THREE.Points(particles, particleMaterial);
scene.add(particleSystem);
```

#### Babylon.js
```javascript
// 创建粒子系统
const particleSystem = new BABYLON.ParticleSystem("particles", 1000, scene);
particleSystem.particleTexture = new BABYLON.Texture("textures/particle.png", scene);

// 设置粒子发射器
particleSystem.emitter = new BABYLON.Vector3(0, 0, 0);
particleSystem.minEmitBox = new BABYLON.Vector3(-5, -5, -5);
particleSystem.maxEmitBox = new BABYLON.Vector3(5, 5, 5);

// 设置粒子属性
particleSystem.color1 = new BABYLON.Color4(1, 1, 1, 1);
particleSystem.color2 = new BABYLON.Color4(0.8, 0.8, 0.8, 1);
particleSystem.minSize = 0.1;
particleSystem.maxSize = 0.5;
particleSystem.minLifeTime = 0.3;
particleSystem.maxLifeTime = 1.5;
particleSystem.emitRate = 100;

// 启动粒子系统
particleSystem.start();
```
```

## 性能优化迁移

### 实例化渲染

#### Three.js
```javascript
const geometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
const material = new THREE.MeshStandardMaterial({ color: 0xff0000 });
const instancedMesh = new THREE.InstancedMesh(geometry, material, 1000);

for (let i = 0; i < 1000; i++) {
  const matrix = new THREE.Matrix4();
  matrix.setPosition(
    Math.random() * 10 - 5,
    Math.random() * 10 - 5,
    Math.random() * 10 - 5
  );
  instancedMesh.setMatrixAt(i, matrix);
}
scene.add(instancedMesh);
```

#### Babylon.js
```javascript
const box = BABYLON.MeshBuilder.CreateBox("box", {size: 0.1}, scene);
const material = new BABYLON.StandardMaterial("material", scene);
material.diffuseColor = new BABYLON.Color3(1, 0, 0);
box.material = material;

// 创建实例
const instances = [];
for (let i = 0; i < 1000; i++) {
  const instance = box.createInstance("instance" + i);
  instance.position.x = Math.random() * 10 - 5;
  instance.position.y = Math.random() * 10 - 5;
  instance.position.z = Math.random() * 10 - 5;
  instances.push(instance);
}
```

### 合并静态网格

#### Three.js
```javascript
import { BufferGeometryUtils } from 'three/examples/jsm/utils/BufferGeometryUtils.js';

// 创建多个几何体
const geometries = [];
for (let i = 0; i < 50; i++) {
  const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
  geometry.translate(
    Math.random() * 10 - 5,
    Math.random() * 10 - 5,
    Math.random() * 10 - 5
  );
  geometries.push(geometry);
}

// 合并几何体
const mergedGeometry = BufferGeometryUtils.mergeBufferGeometries(geometries);
const material = new THREE.MeshStandardMaterial({ color: 0x00ff00 });
const mergedMesh = new THREE.Mesh(mergedGeometry, material);
scene.add(mergedMesh);
```

#### Babylon.js
```javascript
// 创建多个网格
const meshes = [];
for (let i = 0; i < 50; i++) {
  const box = BABYLON.MeshBuilder.CreateBox("box" + i, {size: 0.5}, scene);
  box.position.x = Math.random() * 10 - 5;
  box.position.y = Math.random() * 10 - 5;
  box.position.z = Math.random() * 10 - 5;
  meshes.push(box);
}

// 合并网格
const material = new BABYLON.StandardMaterial("material", scene);
material.diffuseColor = new BABYLON.Color3(0, 1, 0);
const mergedMesh = BABYLON.Mesh.MergeMeshes(meshes, true, true);
mergedMesh.material = material;
```

### 自适应渲染质量

#### Three.js
```javascript
// 使用第三方库如stats.js监控性能
import Stats from 'stats.js';
const stats = new Stats();
document.body.appendChild(stats.dom);

// 根据帧率调整渲染质量
let quality = 1.0; // 1.0 = 100%
let lastTime = 0;
let frames = 0;

function animate(time) {
  requestAnimationFrame(animate);
  stats.begin();

  // 计算帧率
  frames++;
  if (time - lastTime > 1000) {
    const fps = frames * 1000 / (time - lastTime);
    frames = 0;
    lastTime = time;

    // 根据帧率调整质量
    if (fps < 30) {
      quality = Math.max(0.5, quality - 0.1);
      renderer.setPixelRatio(window.devicePixelRatio * quality);
    } else if (fps > 55 && quality < 1.0) {
      quality = Math.min(1.0, quality + 0.1);
      renderer.setPixelRatio(window.devicePixelRatio * quality);
    }
  }

  renderer.render(scene, camera);
  stats.end();
}
```

#### Babylon.js
```javascript
// Babylon.js内置了自适应质量系统
const adaptiveQuality = new BABYLON.AdaptivePerformanceConfigurator(scene);
adaptiveQuality.init({
  targetFrameRate: 60,
  trackerRate: 1000, // 每秒检查一次
  useAdaptiveQuality: true,
  adaptiveQualityConfidenceRange: 5
});

// 添加质量级别
adaptiveQuality.addQualityLevel(0, {
  particleDensity: 0.2,
  textureQuality: 0.5,
  renderScale: 0.7,
  shadowQuality: 0
});

adaptiveQuality.addQualityLevel(1, {
  particleDensity: 0.5,
  textureQuality: 0.75,
  renderScale: 0.85,
  shadowQuality: 1
});

adaptiveQuality.addQualityLevel(2, {
  particleDensity: 1.0,
  textureQuality: 1.0,
  renderScale: 1.0,
  shadowQuality: 2
});

// 启动自适应质量系统
adaptiveQuality.start();
```

### 视锥体剔除

#### Three.js
```javascript
// Three.js自动执行视锥体剔除，但可以手动控制
mesh.frustumCulled = true; // 默认为true

// 对于大型场景，可以使用八叉树等空间分割结构
import { Octree } from 'three/examples/jsm/math/Octree.js';
const octree = new Octree();
octree.fromGraphNode(scene);

function animate() {
  requestAnimationFrame(animate);

  // 使用八叉树获取可见对象
  const visibleObjects = octree.cull(camera.frustum);

  // 只渲染可见对象
  visibleObjects.forEach(object => {
    object.visible = true;
  });

  renderer.render(scene, camera);
}
```

#### Babylon.js
```javascript
// Babylon.js自动执行视锥体剔除
// 可以通过设置网格的isVisible属性来控制
mesh.isVisible = true;

// 对于大型场景，Babylon.js提供了自动LOD和实例化渲染
// 还可以使用场景优化器
const optimizer = new BABYLON.SceneOptimizer(scene, {
  targetFrameRate: 60,
  trackerRate: 1000
});

// 添加优化任务
optimizer.addOptimization(new BABYLON.HardwareScalingOptimization(0, 1));
optimizer.addOptimization(new BABYLON.ShadowsOptimization(1));
optimizer.addOptimization(new BABYLON.PostProcessesOptimization(2));
optimizer.addOptimization(new BABYLON.LensFlaresOptimization(3));
optimizer.addOptimization(new BABYLON.ParticlesOptimization(4));
optimizer.addOptimization(new BABYLON.TextureOptimization(5, 512));

// 启动优化器
optimizer.start();
```

### 纹理压缩和管理

#### Three.js
```javascript
// 使用压缩纹理
const loader = new THREE.KTX2Loader()
  .setTranscoderPath('js/libs/basis/')
  .detectSupport(renderer);

loader.load('textures/compressed.ktx2', function(texture) {
  const material = new THREE.MeshStandardMaterial({ map: texture });
  mesh.material = material;
});

// 手动管理纹理内存
function cleanupScene() {
  // 释放纹理
  mesh.material.map.dispose();
  mesh.material.dispose();
  mesh.geometry.dispose();
}
```

#### Babylon.js
```javascript
// 使用压缩纹理
const material = new BABYLON.StandardMaterial("material", scene);
material.diffuseTexture = new BABYLON.Texture("textures/compressed.ktx2", scene);

// Babylon.js提供了资源管理器
scene.onDispose = function() {
  // 场景销毁时自动释放所有资源
};

// 也可以手动释放
function cleanupResources() {
  material.diffuseTexture.dispose();
  material.dispose();
  mesh.dispose();
}
```

### WebGPU支持

#### Three.js
```javascript
import * as THREE from 'three';
import WebGPURenderer from 'three/examples/jsm/renderers/webgpu/WebGPURenderer.js';

async function init() {
  // 检查WebGPU支持
  if (navigator.gpu) {
    const renderer = new WebGPURenderer();
    await renderer.init();
    renderer.setSize(window.innerWidth, window.innerHeight);
    document.body.appendChild(renderer.domElement);

    // 其余设置与WebGL相同
  } else {
    console.error('WebGPU not supported');
    // 回退到WebGL
    const renderer = new THREE.WebGLRenderer();
    // ...
  }
}
```

#### Babylon.js
```javascript
// Babylon.js 8.0原生支持WebGPU，无需额外代码
const engine = new BABYLON.Engine(canvas, true, {
  // 自动检测并使用WebGPU，如果不支持则回退到WebGL
  useHighPrecisionMatrix: true,
  adaptToDeviceRatio: true,
  powerPreference: "high-performance"
});

// 检查是否使用WebGPU
if (engine.isWebGPU) {
  console.log("Using WebGPU");
} else {
  console.log("Using WebGL");
}
```

## 常见问题与解决方案

### 1. 坐标系差异

**问题**：Three.js和Babylon.js的坐标系有所不同。

**解决方案**：
- Three.js使用右手坐标系，Y轴向上
- Babylon.js默认使用右手坐标系，Y轴向上，但Z轴方向与Three.js相反
- 迁移模型时，可能需要调整旋转：`mesh.rotation.y = Math.PI;`

### 2. 材质参数命名差异

**问题**：两个引擎的材质参数命名和行为有差异。

**解决方案**：
- Three.js: `material.color` → Babylon.js: `material.diffuseColor`
- Three.js: `material.map` → Babylon.js: `material.diffuseTexture`
- Three.js: `material.normalMap` → Babylon.js: `material.bumpTexture`
- Three.js: `material.roughnessMap` → Babylon.js: `material.reflectionTexture`

### 3. 事件处理

**问题**：事件处理系统不同。

**解决方案**：
- Three.js通常使用Raycaster手动处理
- Babylon.js提供了内置的事件系统：
```javascript
mesh.actionManager = new BABYLON.ActionManager(scene);
mesh.actionManager.registerAction(
  new BABYLON.ExecuteCodeAction(
    BABYLON.ActionManager.OnPickTrigger,
    function(evt) {
      console.log("点击了网格");
    }
  )
);
```

### 4. 相机控制

**问题**：Three.js需要单独的控制器，而Babylon.js集成在相机中。

**解决方案**：
- Three.js: `new OrbitControls(camera, renderer.domElement);`
- Babylon.js: `camera.attachControl(canvas, true);`

### 5. 着色器迁移

**问题**：着色器语法和结构不同。

**解决方案**：
- Three.js使用GLSL，Babylon.js也使用GLSL但结构不同
- 使用Babylon.js的ShaderMaterial或NodeMaterial
- 参考Babylon.js的着色器示例进行重写

### 6. 加载进度处理

**问题**：加载进度处理方式不同。

**解决方案**：
```javascript
// Three.js
const manager = new THREE.LoadingManager();
manager.onProgress = function(url, loaded, total) {
  console.log((loaded / total * 100) + '% loaded');
};

// Babylon.js
BABYLON.SceneLoader.ShowLoadingScreen = false;
const assetsManager = new BABYLON.AssetsManager(scene);
assetsManager.onProgress = function(remainingCount, totalCount) {
  console.log((1 - (remainingCount / totalCount)) * 100 + '% loaded');
};
assetsManager.onFinish = function() {
  // 所有资源加载完成
};
```

### 7. 动画系统

**问题**：动画系统的API和概念不同。

**解决方案**：
- Three.js使用AnimationMixer，Babylon.js使用AnimationGroup
- 使用Babylon.js的Animation类创建关键帧动画
- 对于骨骼动画，使用Babylon.js的SkeletonViewer进行调试

### 8. 后期处理效果

**问题**：后期处理效果的实现方式不同。

**解决方案**：
- Three.js使用EffectComposer，Babylon.js使用PostProcessRenderPipeline
- 使用Babylon.js的DefaultRenderingPipeline可以快速设置常用效果
- 自定义后期处理效果需要使用PostProcess类

## 资源与参考

- [Babylon.js官方文档](https://doc.babylonjs.com/)
- [Babylon.js Playground](https://playground.babylonjs.com/)
- [React-BabylonJS文档](https://brianzinn.github.io/react-babylonjs/)
- [Babylon.js论坛](https://forum.babylonjs.com/)
- [Babylon.js GitHub仓库](https://github.com/BabylonJS/Babylon.js)
- [Babylon.js NPM包](https://www.npmjs.com/package/@babylonjs/core)
- [Babylon.js 8.0发布说明](https://doc.babylonjs.com/guidedLearning/babylonNative/babylonNativeWhatIs)

## 迁移检查清单

### 基础结构
- [ ] 安装Babylon.js相关包
- [ ] 移除Three.js相关包
- [ ] 设置基础场景结构
- [ ] 配置渲染引擎
- [ ] 设置相机系统
- [ ] 配置光照系统

### 核心功能
- [ ] 迁移网格和几何体
- [ ] 迁移材质和纹理
- [ ] 迁移模型加载
- [ ] 迁移动画系统
- [ ] 迁移用户交互
- [ ] 迁移事件处理

### 高级功能
- [ ] 迁移物理系统
- [ ] 迁移粒子系统
- [ ] 迁移LOD系统
- [ ] 迁移后期处理效果
- [ ] 迁移环境和天空盒
- [ ] 迁移阴影系统

### 性能优化
- [ ] 实现实例化渲染
- [ ] 配置自适应质量
- [ ] 优化资源加载
- [ ] 实现网格合并
- [ ] 配置WebGPU支持
- [ ] 优化移动设备支持

### 测试与调试
- [ ] 设置性能监控
- [ ] 测试不同设备兼容性
- [ ] 检查内存泄漏
- [ ] 验证渲染质量
- [ ] 确认功能完整性

_文档最后更新日期：2025年5月8日_
