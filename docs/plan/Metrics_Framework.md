# 产品评估指标框架 (Metrics Framework)

## 1. 指标框架概述

本产品评估指标框架旨在为“在线祭祀平台”定义一套清晰、可衡量、可行动的指标体系。它将帮助团队跟踪产品表现、评估功能效果、发现潜在问题、驱动数据驱动的决策，并最终衡量产品是否达成了其商业目标和用户价值。

本文档将与产品需求文档 (PRD)、产品路线图 (Roadmap) 和用户故事地图 (User Story Map) 紧密关联，确保指标与产品战略和用户需求保持一致。

## 2. 北极星指标定义

- **北极星指标 (North Star Metric)**: **有效纪念行为总次数**
    - **定义**: 指在特定时间周期内（如每日/每周/每月），用户在平台上完成的有意义的、能体现产品核心价值的纪念相关行为的总和。这些行为可以包括但不限于：
        - 成功创建并发布一个新的纪念空间。
        - 在纪念空间内完成一次完整的祭拜流程（如献花、点烛、留言）。
        - 对族谱进行一次有效的编辑或添加（如新增成员、完善信息）。
        - 成功使用一次AI增强服务（如照片修复、声音克隆并保存结果）。
        - 成功邀请一位新用户加入家族或参与纪念空间。
    - **计算方式 (示例)**: `有效纪念行为总次数 = (新创建空间数 * 权重A) + (祭拜次数 * 权重B) + (族谱编辑次数 * 权重C) + (AI服务使用次数 * 权重D) + (成功邀请数 * 权重E)`
        - *权重A, B, C, D, E 需要根据不同行为对核心价值的贡献度进行设定和调整。*
- **选择依据**:
    - **反映核心价值**: 该指标直接关联产品的核心使命——“缅怀先人，设置族谱”，以及通过技术提供情感连接。
    - **驱动用户增长与留存**: 更多的有效纪念行为意味着用户更深度地使用产品，从而提升用户粘性和长期留存。
    - **指引产品方向**: 关注此指标有助于团队优先开发那些能促进更多有效纪念行为的功能。
    - **可衡量与可行动**: 各项组成行为均可被追踪和量化，团队可以针对性地优化特定行为的转化路径。
    - **与商业目标间接关联**: 虽然不是直接的收入指标，但高活跃度和高参与度是实现商业目标（如付费转化、年收入）的基础。

## 3. 核心指标体系 (HEART / AARRR)

我们将结合使用 HEART 框架 (适用于评估用户体验) 和 AARRR 模型 (适用于评估用户生命周期和增长) 来构建全面的指标体系。

### 3.1 HEART 框架

- **Happiness (愉悦度)**: 用户对产品的主观感受，通常通过调研获得。
    - **指标**: 
        - **NPS (净推荐值)**: 通过问卷“您有多大可能将本产品推荐给朋友或同事？”衡量。
        - **用户满意度评分 (CSAT)**: 针对特定功能或整体体验的满意度打分。
        - **应用商店评分/评论**: 监控用户在公开渠道的评价。
    - **目标**: NPS > X, CSAT > Y%。
    - **监测方法**: 定期用户调研、应用内反馈、应用商店监控。

- **Engagement (参与度)**: 用户使用产品的深度和频率。
    - **指标**: 
        - **日/月活跃用户 (DAU/MAU)**。
        - **平均用户会话时长**。
        - **核心功能使用频率**: 如平均每用户每月祭拜次数、纪念空间访问频率。
        - **内容贡献量**: 如用户上传的照片/视频数量、发表的留言数量、族谱条目创建数。
        - **AI服务使用率**: 如照片修复功能使用用户占比、声音克隆功能使用用户占比。
    - **目标**: 持续提升各项参与度指标。
    - **监测方法**: 后台数据统计、用户行为分析平台。

- **Adoption (接受度)**: 新用户对产品或新功能的接受和使用情况。
    - **指标**: 
        - **新用户注册数**。
        - **新功能使用率**: 新功能上线后，在目标用户中的渗透率和使用频率。
        - **纪念空间创建转化率**: 注册用户中成功创建纪念空间的比例。
        - **家族功能激活率**: 用户创建或加入家族的比例。
    - **目标**: 提高新用户转化和新功能采纳。
    - **监测方法**: 后台数据统计、A/B测试结果分析。

- **Retention (留存率)**: 用户在一段时间后持续使用产品的比例。
    - **指标**: 
        - **次日留存率、7日留存率、30日留存率**。
        - **用户流失率 (Churn Rate)**。
        - **长期活跃用户比例**: 如连续三个月均有活跃的用户占比。
    - **目标**: 提高各周期留存率，降低流失率。
    - **监测方法**: 用户队列分析、后台数据统计。

- **Task Success (任务完成率)**: 用户能否成功完成产品中的关键任务。
    - **指标**: 
        - **纪念空间创建成功率**: 开始创建到成功发布的转化率。
        - **祭拜流程完成率**: 进入祭拜到完成献花/留言等关键步骤的转化率。
        - **AI服务任务成功率**: AI照片修复/声音克隆从提交到获得满意结果的比例。
        - **族谱信息添加成功率**。
        - **用户求助率/客服工单量**: 反映用户在完成任务时遇到的障碍。
    - **目标**: 提升关键任务的完成率，降低用户操作障碍。
    - **监测方法**: 用户行为路径分析、可用性测试、客服数据分析。

### 3.2 AARRR 模型 (海盗指标)

- **Acquisition (获取用户)**: 用户如何发现并来到我们的产品。
    - **指标**: 
        - **各渠道新增用户数**: 如自然搜索、付费广告、社交媒体推荐、口碑传播等。
        - **渠道转化率**: 从各渠道触达到注册的转化率。
        - **获客成本 (CAC)**: 获取单个新用户的平均成本。
    - **目标**: 提升高价值渠道的用户获取量，降低CAC。
    - **监测方法**: 网站分析工具 (如Google Analytics)、广告平台数据、UTM参数跟踪。

- **Activation (激活用户)**: 用户首次使用产品时的良好体验。
    - **指标**: 
        - **新用户完成核心操作比例**: 如新用户在首次会话内成功创建纪念空间或完成一次祭拜的比例 (Aha! Moment)。
        - **新用户引导流程完成率**。
        - **新用户次日留存率**。
    - **目标**: 确保新用户能快速体验到产品核心价值。
    - **监测方法**: 用户行为分析、新用户队列分析。

- **Retention (提高留存)**: 用户持续使用产品。
    - *(同HEART框架中的Retention指标)*

- **Revenue (获取收入)**: 产品如何产生收益。
    - **指标**: 
        - **总收入 (GMV/Net Revenue)**。
        - **付费用户数 (Paying Users)**。
        - **付费转化率 (Conversion to Paid)**: 活跃用户中付费用户的比例。
        - **平均每用户收入 (ARPU)** / **平均每付费用户收入 (ARPPU)**。
        - **用户生命周期价值 (LTV)**。
        - **各付费点收入贡献**: 如虚拟祭品销售额、AI服务订阅费、高级功能费等。
    - **目标**: 实现年收入3000万，持续优化商业模式和提升LTV。
    - **监测方法**: 支付系统数据、后台订单统计、财务报表。

- **Referral (用户推荐)**: 用户自发传播产品。
    - **指标**: 
        - **病毒系数 (K因子)**: 每个现有用户带来的新用户数量。
        - **分享率**: 用户分享纪念空间/活动链接的频率。
        - **邀请成功率**: 通过邀请链接成功注册新用户的比例。
        - **NPS (净推荐值)**: (也可归于此)
    - **目标**: 提高用户推荐意愿和效果，实现口碑传播。
    - **监测方法**: 分享功能埋点、邀请码跟踪、用户调研。

## 4. 功能级评估指标

针对PRD中定义的核心功能模块，设定更细化的评估指标。

| 功能模块         | 关键指标                                                                 | 目标/衡量标准                                     |
| ---------------- | ------------------------------------------------------------------------ | ------------------------------------------------- |
| **纪念空间创建** | 创建流程各步骤转化率、平均创建时长、信息完整度                             | 提升转化率，缩短时长，提高信息完整性                |
| **3D场景体验**   | 场景选择率、场景加载时间、场景内平均停留时间、交互元素点击率                 | 优化加载性能，提升用户在场景内的探索和互动          |
| **祭拜互动**     | 祭拜功能使用率、各类祭品选择比例、留言率、祭拜动作使用频率                 | 提高用户参与祭拜的积极性和互动深度                  |
| **家族与族谱**   | 家族创建/加入率、族谱创建率、族谱成员平均数、族谱编辑活跃度                | 促进家族社群形成和族谱内容的丰富                  |
| **AI照片修复**   | 功能使用率、修复成功率、用户对修复效果的满意度评分、修复后照片保存/分享率    | 提升修复效果和用户满意度，促进成果应用              |
| **AI声音克隆**   | (Beta阶段) 申请试用数、音频上传成功率、克隆声音试听次数、用户反馈          | (Beta)验证技术可行性和用户初步接受度              |
| **在线商店**     | 商店访问量、商品浏览量、商品点击率、下单转化率、客单价、各商品销售额占比 | 提升商店营收能力和用户付费意愿                      |

## 5. 指标监测计划

- **数据收集工具**: 
    - **前端埋点**: Google Analytics, Mixpanel, 或自建埋点系统 (如使用 Segment)。
    - **后端日志**: ELK Stack (Elasticsearch, Logstash, Kibana) 或类似日志管理系统。
    - **数据库**: 直接从生产数据库或数据仓库中提取聚合数据。
    - **第三方服务**: 支付平台API、AI服务提供商API、应用商店后台。
- **数据看板与报告**: 
    - **实时监控大盘**: 展示核心KPI (DAU, 收入, 服务器状态等)，供日常监控。
    - **产品数据周报/月报**: 深入分析各维度指标变化、趋势、原因，提出改进建议。
    - **功能上线效果分析报告**: 针对新功能或重要改版，进行专项数据分析和评估。
    - **A/B测试报告**: 清晰呈现测试方案、数据结果和结论。
- **数据分析流程**: 
    1. **定义问题/假设**: 基于业务需求或观察到的现象提出分析目标。
    2. **数据提取与清洗**: 从各数据源获取相关数据，进行预处理。
    3. **数据分析与可视化**:运用统计方法和可视化工具进行分析，发现规律和洞察。
    4. **解读与结论**: 结合业务背景解读分析结果，形成结论和建议。
    5. **行动与迭代**: 基于数据洞察推动产品优化和运营策略调整。
- **指标负责人与复盘机制**: 
    - **明确各指标的Owner**: 通常由产品经理、运营或数据分析师负责跟踪。
    - **定期指标复盘会议**: 每周或每双周召开数据会议，回顾核心指标表现，讨论问题，制定行动计划。
    - **建立指标字典**: 维护统一的指标定义、计算口径和数据来源，确保团队对指标理解一致。

*(注: 本指标框架为初始版本，会根据产品发展阶段、业务目标变化和数据分析的深入进行持续迭代和优化。)*