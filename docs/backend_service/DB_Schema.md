# 数据库设计文档

## 数据库概览

### 数据库状态
- **数据库类型**: PostgreSQL
- **总表数**: 22个表
- **功能覆盖**: 完整的家族纪念平台 + 商城系统 + AI服务

### 表结构概览 (22个表)

#### 核心用户与认证 (2个表)
- `users` - 用户基本信息
- `user_tokens` - 用户令牌管理

#### 纪念空间功能 (4个表)
- `memorial_spaces` - 纪念空间
- `memorial_assets` - 纪念空间资源
- `memorial_events` - 纪念事件
- `memorial_messages` - 纪念留言

#### 3D场景与环境 (3个表)
- `scenes` - 3D场景
- `environments` - 环境设置
- `religious_cultural_settings` - 宗教文化设置

#### 祭拜与互动 (3个表)
- `tributes` - 祭拜记录
- `tribute_items` - 祭品
- `tribute_records` - 祭拜记录详情

#### 家族管理系统 (5个表)
- `families` - 家族
- `family_members` - 家族成员
- `family_invitations` - 家族邀请
- `genealogy_nodes` - 族谱节点
- `genealogy_relationships` - 族谱关系

#### 祖先信息 (1个表)
- `ancestors` - 祖先信息

#### AI服务集成 (2个表)
- `ai_tasks` - AI任务
- `ai_models` - AI模型

#### 商城系统 (3个表)
- `products` - 商品
- `orders` - 订单
- `order_items` - 订单项

#### 系统管理 (1个表)
- `alembic_version` - 数据库版本管理

## 1. 数据库选择

本项目选择 **PostgreSQL** 作为主要数据库系统，原因如下：

- 强大的关系型数据库功能，支持复杂查询和事务
- 内置JSON数据类型，便于存储非结构化或半结构化数据
- 优秀的全文搜索能力，适合搜索纪念空间和族谱信息
- 地理位置数据支持，可用于未来基于位置的功能扩展
- 开源免费，社区活跃，文档完善
- 良好的可扩展性和性能表现

## 2. 数据库表结构设计

### 2.1 用户与认证相关表

#### 2.1.1 users 表 - 用户基本信息

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 用户唯一标识 |
| username | VARCHAR(50) | UNIQUE, NOT NULL | 用户名 |
| email | VARCHAR(100) | UNIQUE, NOT NULL | 电子邮箱 |
| phone | VARCHAR(20) | UNIQUE | 手机号码 |
| password_hash | VARCHAR(255) | NOT NULL | 密码哈希值 |
| full_name | VARCHAR(100) | | 用户真实姓名 |
| avatar_url | VARCHAR(255) | | 头像URL |
| bio | TEXT | | 个人简介 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE | 账号是否激活 |
| is_verified | BOOLEAN | NOT NULL, DEFAULT FALSE | 邮箱/手机是否验证 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 更新时间 |
| last_login_at | TIMESTAMP | | 最后登录时间 |
| role | VARCHAR(20) | NOT NULL, DEFAULT 'user' | 用户角色(user/admin) |

**索引**：
- 主键索引：id
- 唯一索引：username, email, phone
- 普通索引：created_at, role

#### 2.1.2 user_tokens 表 - 用户令牌

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 令牌ID |
| user_id | UUID | FK(users.id), NOT NULL | 关联用户ID |
| token_type | VARCHAR(20) | NOT NULL | 令牌类型(refresh/reset/verify) |
| token | VARCHAR(255) | NOT NULL | 令牌值 |
| expires_at | TIMESTAMP | NOT NULL | 过期时间 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| is_revoked | BOOLEAN | NOT NULL, DEFAULT FALSE | 是否已撤销 |

**索引**：
- 主键索引：id
- 外键索引：user_id
- 普通索引：token, token_type, expires_at

### 2.2 纪念空间相关表

#### 2.2.1 memorial_spaces 表 - 纪念空间

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 纪念空间ID |
| creator_id | UUID | FK(users.id), NOT NULL | 创建者ID |
| deceased_name | VARCHAR(100) | NOT NULL | 逝者姓名 |
| deceased_gender | VARCHAR(10) | | 逝者性别 |
| birth_date | DATE | | 出生日期 |
| death_date | DATE | | 逝世日期 |
| relationship | VARCHAR(50) | | 与创建者关系 |
| bio | TEXT | | 生平简介 |
| scene_id | UUID | FK(scenes.id) | 3D场景ID |
| music_url | VARCHAR(255) | | 背景音乐URL |
| privacy_level | VARCHAR(20) | NOT NULL, DEFAULT 'private' | 隐私级别(public/private/password/family) |
| access_password | VARCHAR(255) | | 访问密码(加密存储) |
| visit_count | INTEGER | NOT NULL, DEFAULT 0 | 访问次数 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 更新时间 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE | 是否激活 |
| custom_settings | JSONB | | 自定义设置(JSON格式) |

**索引**：
- 主键索引：id
- 外键索引：creator_id, scene_id
- 普通索引：deceased_name, created_at, privacy_level
- 全文搜索索引：deceased_name, bio

#### 2.2.2 memorial_assets 表 - 纪念空间资源

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 资源ID |
| space_id | UUID | FK(memorial_spaces.id), NOT NULL | 纪念空间ID |
| uploader_id | UUID | FK(users.id), NOT NULL | 上传者ID |
| asset_type | VARCHAR(20) | NOT NULL | 资源类型(image/video/audio/document) |
| title | VARCHAR(255) | | 资源标题 |
| description | TEXT | | 资源描述 |
| file_url | VARCHAR(255) | NOT NULL | 文件URL |
| thumbnail_url | VARCHAR(255) | | 缩略图URL |
| original_filename | VARCHAR(255) | | 原始文件名 |
| file_size | INTEGER | | 文件大小(字节) |
| metadata | JSONB | | 元数据(JSON格式) |
| display_order | INTEGER | DEFAULT 0 | 显示顺序 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 更新时间 |
| is_ai_enhanced | BOOLEAN | DEFAULT FALSE | 是否经AI增强 |

**索引**：
- 主键索引：id
- 外键索引：space_id, uploader_id
- 普通索引：asset_type, created_at, display_order

#### 2.2.3 scenes 表 - 3D场景

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 场景ID |
| name | VARCHAR(100) | NOT NULL | 场景名称 |
| description | TEXT | | 场景描述 |
| preview_url | VARCHAR(255) | | 预览图URL |
| model_url | VARCHAR(255) | NOT NULL | 3D模型URL |
| category | VARCHAR(50) | | 场景分类(中式/西式/宗教/自然等) |
| is_premium | BOOLEAN | NOT NULL, DEFAULT FALSE | 是否为高级场景 |
| price | DECIMAL(10,2) | DEFAULT 0 | 价格(如适用) |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 更新时间 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE | 是否激活 |
| config_options | JSONB | | 配置选项(JSON格式) |

**索引**：
- 主键索引：id
- 普通索引：category, is_premium, is_active

### 2.3 祭拜互动相关表

#### 2.3.1 tributes 表 - 祭拜记录

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 祭拜ID |
| space_id | UUID | FK(memorial_spaces.id), NOT NULL | 纪念空间ID |
| user_id | UUID | FK(users.id) | 用户ID(可为空表示匿名) |
| tribute_type | VARCHAR(50) | NOT NULL | 祭拜类型(flower/candle/incense/food/etc) |
| tribute_item_id | UUID | FK(tribute_items.id) | 祭品ID(如适用) |
| message | TEXT | | 祭拜留言 |
| position_data | JSONB | | 位置数据(JSON格式) |
| is_anonymous | BOOLEAN | NOT NULL, DEFAULT FALSE | 是否匿名 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| ip_address | VARCHAR(50) | | IP地址 |

**索引**：
- 主键索引：id
- 外键索引：space_id, user_id, tribute_item_id
- 普通索引：tribute_type, created_at

#### 2.3.2 tribute_items 表 - 祭品

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 祭品ID |
| name | VARCHAR(100) | NOT NULL | 祭品名称 |
| description | TEXT | | 祭品描述 |
| category | VARCHAR(50) | NOT NULL | 分类(flower/candle/incense/food/etc) |
| image_url | VARCHAR(255) | | 图片URL |
| model_url | VARCHAR(255) | | 3D模型URL(如适用) |
| is_premium | BOOLEAN | NOT NULL, DEFAULT FALSE | 是否为高级祭品 |
| price | DECIMAL(10,2) | DEFAULT 0 | 价格(如适用) |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 更新时间 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE | 是否激活 |

**索引**：
- 主键索引：id
- 普通索引：category, is_premium, is_active

#### 2.3.3 messages 表 - 留言板

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 留言ID |
| space_id | UUID | FK(memorial_spaces.id), NOT NULL | 纪念空间ID |
| user_id | UUID | FK(users.id) | 用户ID(可为空表示匿名) |
| content | TEXT | NOT NULL | 留言内容 |
| is_anonymous | BOOLEAN | NOT NULL, DEFAULT FALSE | 是否匿名 |
| is_public | BOOLEAN | NOT NULL, DEFAULT TRUE | 是否公开 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 更新时间 |
| status | VARCHAR(20) | NOT NULL, DEFAULT 'pending' | 状态(pending/approved/rejected) |
| ip_address | VARCHAR(50) | | IP地址 |

**索引**：
- 主键索引：id
- 外键索引：space_id, user_id
- 普通索引：created_at, status, is_public

### 2.4 家族与族谱相关表

#### 2.4.1 families 表 - 家族

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 家族ID |
| name | VARCHAR(100) | NOT NULL | 家族名称 |
| description | TEXT | | 家族描述 |
| creator_id | UUID | FK(users.id), NOT NULL | 创建者ID |
| avatar_url | VARCHAR(255) | | 家族头像URL |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 更新时间 |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE | 是否激活 |
| member_count | INTEGER | NOT NULL, DEFAULT 1 | 成员数量 |
| settings | JSONB | | 家族设置(JSON格式) |

**索引**：
- 主键索引：id
- 外键索引：creator_id
- 普通索引：created_at, is_active
- 全文搜索索引：name, description

#### 2.4.2 family_members 表 - 家族成员

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 成员关系ID |
| family_id | UUID | FK(families.id), NOT NULL | 家族ID |
| user_id | UUID | FK(users.id), NOT NULL | 用户ID |
| role | VARCHAR(20) | NOT NULL, DEFAULT 'member' | 角色(admin/editor/member) |
| nickname | VARCHAR(50) | | 在家族中的昵称 |
| joined_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 加入时间 |
| invited_by | UUID | FK(users.id) | 邀请人ID |
| status | VARCHAR(20) | NOT NULL, DEFAULT 'active' | 状态(pending/active/blocked) |

**索引**：
- 主键索引：id
- 外键索引：family_id, user_id, invited_by
- 唯一索引：(family_id, user_id) - 一个用户在一个家族中只能有一条记录
- 普通索引：role, status

#### 2.4.3 genealogy_nodes 表 - 族谱节点

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 节点ID |
| family_id | UUID | FK(families.id), NOT NULL | 家族ID |
| name | VARCHAR(100) | NOT NULL | 姓名 |
| gender | VARCHAR(10) | | 性别 |
| birth_date | DATE | | 出生日期 |
| death_date | DATE | | 逝世日期 |
| bio | TEXT | | 生平简介 |
| avatar_url | VARCHAR(255) | | 头像URL |
| generation | INTEGER | | 世代/辈分 |
| memorial_space_id | UUID | FK(memorial_spaces.id) | 关联的纪念空间ID |
| created_by | UUID | FK(users.id), NOT NULL | 创建者ID |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 更新时间 |
| metadata | JSONB | | 元数据(JSON格式) |

**索引**：
- 主键索引：id
- 外键索引：family_id, memorial_space_id, created_by
- 普通索引：generation, name
- 全文搜索索引：name, bio

#### 2.4.4 genealogy_relationships 表 - 族谱关系

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 关系ID |
| family_id | UUID | FK(families.id), NOT NULL | 家族ID |
| from_node_id | UUID | FK(genealogy_nodes.id), NOT NULL | 起始节点ID |
| to_node_id | UUID | FK(genealogy_nodes.id), NOT NULL | 目标节点ID |
| relationship_type | VARCHAR(20) | NOT NULL | 关系类型(parent/child/spouse) |
| created_by | UUID | FK(users.id), NOT NULL | 创建者ID |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| metadata | JSONB | | 元数据(JSON格式) |

**索引**：
- 主键索引：id
- 外键索引：family_id, from_node_id, to_node_id, created_by
- 唯一索引：(from_node_id, to_node_id, relationship_type) - 两个节点之间的特定关系只能有一条记录
- 普通索引：relationship_type

### 2.5 AI服务相关表

#### 2.5.1 ai_tasks 表 - AI任务

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 任务ID |
| user_id | UUID | FK(users.id), NOT NULL | 用户ID |
| task_type | VARCHAR(50) | NOT NULL | 任务类型(photo_repair/voice_clone/etc) |
| status | VARCHAR(20) | NOT NULL, DEFAULT 'pending' | 状态(pending/processing/completed/failed) |
| input_data | JSONB | NOT NULL | 输入数据(JSON格式) |
| output_data | JSONB | | 输出数据(JSON格式) |
| error_message | TEXT | | 错误信息 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 更新时间 |
| completed_at | TIMESTAMP | | 完成时间 |
| priority | INTEGER | NOT NULL, DEFAULT 0 | 优先级 |
| worker_id | VARCHAR(100) | | 处理该任务的worker ID |

**索引**：
- 主键索引：id
- 外键索引：user_id
- 普通索引：task_type, status, created_at, priority

#### 2.5.2 ai_models 表 - AI模型

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 模型ID |
| name | VARCHAR(100) | NOT NULL | 模型名称 |
| description | TEXT | | 模型描述 |
| model_type | VARCHAR(50) | NOT NULL | 模型类型(photo_repair/voice_clone/etc) |
| version | VARCHAR(20) | NOT NULL | 版本号 |
| config | JSONB | | 配置参数(JSON格式) |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE | 是否激活 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 更新时间 |

**索引**：
- 主键索引：id
- 普通索引：model_type, version, is_active

### 2.6 商店与支付相关表

#### 2.6.1 products 表 - 商品

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 商品ID |
| name | VARCHAR(100) | NOT NULL | 商品名称 |
| description | TEXT | | 商品描述 |
| product_type | VARCHAR(50) | NOT NULL | 商品类型(virtual_item/service/subscription) |
| category | VARCHAR(50) | | 分类 |
| price | DECIMAL(10,2) | NOT NULL | 价格 |
| discount_price | DECIMAL(10,2) | | 折扣价 |
| image_url | VARCHAR(255) | | 图片URL |
| reference_id | UUID | | 关联ID(如tribute_item_id, scene_id等) |
| is_active | BOOLEAN | NOT NULL, DEFAULT TRUE | 是否激活 |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 更新时间 |
| metadata | JSONB | | 元数据(JSON格式) |

**索引**：
- 主键索引：id
- 普通索引：product_type, category, is_active, price

#### 2.6.2 orders 表 - 订单

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 订单ID |
| user_id | UUID | FK(users.id), NOT NULL | 用户ID |
| order_number | VARCHAR(50) | UNIQUE, NOT NULL | 订单编号 |
| total_amount | DECIMAL(10,2) | NOT NULL | 总金额 |
| status | VARCHAR(20) | NOT NULL, DEFAULT 'pending' | 状态(pending/paid/cancelled/refunded) |
| payment_method | VARCHAR(50) | | 支付方式 |
| payment_id | VARCHAR(100) | | 支付平台交易ID |
| created_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | NOT NULL, DEFAULT NOW() | 更新时间 |
| paid_at | TIMESTAMP | | 支付时间 |
| notes | TEXT | | 备注 |

**索引**：
- 主键索引：id
- 外键索引：user_id
- 唯一索引：order_number
- 普通索引：status, created_at, paid_at

#### 2.6.3 order_items 表 - 订单项

| 列名 | 数据类型 | 约束 | 说明 |
| --- | --- | --- | --- |
| id | UUID | PK | 订单项ID |
| order_id | UUID | FK(orders.id), NOT NULL | 订单ID |
| product_id | UUID | FK(products.id), NOT NULL | 商品ID |
| quantity | INTEGER | NOT NULL, DEFAULT 1 | 数量 |
| unit_price | DECIMAL(10,2) | NOT NULL | 单价 |
| total_price | DECIMAL(10,2) | NOT NULL | 总价 |
| metadata | JSONB | | 元数据(JSON格式) |

**索引**：
- 主键索引：id
- 外键索引：order_id, product_id

## 3. 数据库关系图

```mermaid
erDiagram
    USERS ||--o{ USER_TOKENS : has
    USERS ||--o{ MEMORIAL_SPACES : creates
    USERS ||--o{ MEMORIAL_ASSETS : uploads
    USERS ||--o{ TRIBUTES : performs
    USERS ||--o{ MESSAGES : writes
    USERS ||--o{ FAMILIES : creates
    USERS ||--o{ FAMILY_MEMBERS : belongs_to
    USERS ||--o{ GENEALOGY_NODES : creates
    USERS ||--o{ AI_TASKS : requests
    USERS ||--o{ ORDERS : places
    
    MEMORIAL_SPACES ||--o{ MEMORIAL_ASSETS : contains
    MEMORIAL_SPACES ||--o{ TRIBUTES : receives
    MEMORIAL_SPACES ||--o{ MESSAGES : has
    MEMORIAL_SPACES ||--|| SCENES : uses
    MEMORIAL_SPACES ||--o{ GENEALOGY_NODES : linked_to
    
    TRIBUTES ||--o{ TRIBUTE_ITEMS : uses
    
    FAMILIES ||--o{ FAMILY_MEMBERS : has
    FAMILIES ||--o{ GENEALOGY_NODES : contains
    FAMILIES ||--o{ GENEALOGY_RELATIONSHIPS : defines
    
    GENEALOGY_NODES ||--o{ GENEALOGY_RELATIONSHIPS : participates_in
    
    AI_TASKS }|--|| AI_MODELS : uses
    
    ORDERS ||--o{ ORDER_ITEMS : contains
    ORDER_ITEMS }|--|| PRODUCTS : references
```

## 4. 数据库优化策略

### 4.1 索引优化

- 为高频查询字段创建适当的索引
- 对大表使用部分索引和条件索引
- 定期维护和重建索引

### 4.2 查询优化

- 使用预编译语句和参数化查询
- 避免使用SELECT *，只查询需要的列
- 合理使用JOIN，避免过多的表连接
- 对于复杂查询，考虑使用视图或存储过程

### 4.3 性能优化

- 使用连接池管理数据库连接
- 实施读写分离策略
- 对热点数据使用缓存
- 定期进行VACUUM和ANALYZE操作
- 对大表实施分区策略

### 4.4 数据安全策略

- 敏感数据加密存储
- 实施行级安全策略
- 定期备份数据
- 实施审计日志记录关键操作

## 5. 数据迁移与版本控制

- 使用Alembic进行数据库迁移管理
- 每次架构变更创建迁移脚本
- 迁移脚本包含向前和向后迁移的逻辑
- 在CI/CD流程中集成数据库迁移步骤