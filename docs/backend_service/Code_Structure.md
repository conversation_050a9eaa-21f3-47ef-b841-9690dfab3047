```markdown
# 归处在线祭祀平台 - 后端代码结构方案

## 1. 技术栈核心

- **编程语言**: Python 3.11+
- **Web 框架**: FastAPI
- **数据库 ORM**: SQLAlchemy (异步模式 with AsyncPG)
- **数据校验**: Pydantic
- **任务队列**: Celery (with <PERSON>is/RabbitMQ as broker)
- **缓存**: Redis
- **API 文档**: OpenAPI (自动生成 by FastAPI)

## 2. 项目根目录结构 (`backend/`)

```
memorial_backend/
├── app/                                # 核心应用代码
│   ├── __init__.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── v1/                         # API 版本 v1
│   │   │   ├── __init__.py
│   │   │   ├── endpoints/              # API 路由处理
│   │   │   │   ├── __init__.py
│   │   │   │   ├── auth.py             # 用户认证
│   │   │   │   ├── users.py            # 用户管理
│   │   │   │   ├── memorial_spaces.py  # 纪念空间
│   │   │   │   ├── memorial_assets.py  # 纪念资源
│   │   │   │   ├── scenes.py           # 3D场景
│   │   │   │   ├── tributes.py         # 祭拜互动
│   │   │   │   ├── messages.py         # 留言板
│   │   │   │   ├── families.py         # 家族管理
│   │   │   │   ├── genealogy.py        # 族谱管理
│   │   │   │   ├── ai_services.py      # AI 服务
│   │   │   │   └── store.py            # 商店与支付
│   │   │   └── deps.py                 # API 依赖项 (如获取当前用户)
│   │   └── schemas/                    # Pydantic 模型 (请求/响应体)
│   │       ├── __init__.py
│   │       ├── auth.py
│   │       ├── user.py
│   │       ├── memorial_space.py
│   │       ├── memorial_asset.py
│   │       ├── scene.py
│   │       ├── tribute.py
│   │       ├── message.py
│   │       ├── family.py
│   │       ├── genealogy.py
│   │       ├── ai_service.py
│   │       ├── store.py
│   │       └── common.py               # 通用 Schema (如分页、ID等)
│   ├── auth/                           # 认证逻辑 (JWT, OAuth2等)
│   │   ├── __init__.py
│   │   ├── jwt.py
│   │   └── security.py
│   ├── core/                           # 核心配置与工具
│   │   ├── __init__.py
│   │   ├── config.py                 # 应用配置 (环境变量等)
│   │   └── security.py               # 密码哈希等安全相关
│   ├── crud/                           # 数据库操作 (Create, Read, Update, Delete)
│   │   ├── __init__.py
│   │   ├── base.py                     # CRUD 基类
│   │   ├── crud_user.py
│   │   ├── crud_memorial_space.py
│   │   └── ... (其他模块的 CRUD 操作)
│   ├── db/                             # 数据库相关
│   │   ├── __init__.py
│   │   ├── base_class.py             # SQLAlchemy Base
│   │   ├── session.py                #数据库会话管理 (AsyncSession)
│   │   └── init_db.py                # 初始化数据库 (可选)
│   ├── models/                         # SQLAlchemy 数据模型
│   │   ├── __init__.py
│   │   ├── user.py
│   │   ├── memorial_space.py
│   │   ├── memorial_asset.py
│   │   ├── scene.py
│   │   ├── tribute.py
│   │   ├── message.py
│   │   ├── family.py
│   │   ├── genealogy.py
│   │   ├── ai_task.py
│   │   └── ... (其他数据模型)
│   ├── services/                       # 业务逻辑服务层
│   │   ├── __init__.py
│   │   ├── user_service.py
│   │   ├── memorial_space_service.py
│   │   └── ... (其他业务服务)
│   ├── tasks/                          # 后台任务 (Celery)
│   │   ├── __init__.py
│   │   ├── ai_tasks.py               # AI 相关任务
│   │   └── notification_tasks.py     # 通知任务
│   ├── utils/                          # 通用工具函数
│   │   ├── __init__.py
│   │   └── file_utils.py             # 文件处理工具
│   └── main.py                       # FastAPI 应用入口
├── alembic/                            # 数据库迁移 (Alembic)
│   ├── versions/
│   └── env.py
├── tests/                              # 测试代码
│   ├── __init__.py
│   ├── conftest.py                     # Pytest 配置文件
│   ├── api_tests/
│   │   └── v1/
│   │       ├── test_auth.py
│   │       └── ...
│   └── unit_tests/
│       ├── test_users.py
│       └── ...
├── .env                                # 环境变量文件 (开发用, 不提交到 Git)
├── .env.example                        # 环境变量示例文件
├── .gitignore
├── alembic.ini                         # Alembic 配置文件
├── pyproject.toml                      # 项目元数据和依赖 (Poetry/PDM)
├── README.md                           # 项目说明
└── scripts/                            # 辅助脚本 (如启动、测试、lint)
    ├── run_dev.sh
    ├── run_tests.sh
    └── lint.sh
```

## 3. 模块职责说明

- **`app/api/v1/endpoints/`**: 包含各个模块的 API 路由定义和请求处理逻辑。每个文件对应一组相关的端点 (如 `auth.py` 处理所有认证相关的请求)。
- **`app/api/v1/schemas/`**: 定义 Pydantic 模型，用于请求体验证、响应体序列化以及 API 文档生成。每个文件对应相关模块的数据结构。
- **`app/auth/`**: 实现用户认证和授权的具体逻辑，如 JWT 生成、验证、密码策略等。
- **`app/core/`**: 存放应用的核心配置（如从环境变量加载配置）、安全相关的工具函数（如密码哈希）。
- **`app/crud/`**: 包含与数据库直接交互的 CRUD (Create, Read, Update, Delete) 操作。每个 `crud_*.py` 文件对应一个或多个数据模型。
- **`app/db/`**: 数据库连接、会话管理、基础模型类定义。
- **`app/models/`**: 定义 SQLAlchemy 的数据模型，与数据库表结构一一对应。
- **`app/services/`**: 封装核心业务逻辑。服务层会调用 CRUD 操作，并可能组合多个操作来完成一个业务功能。API 端点通常调用服务层的方法。
- **`app/tasks/`**: 定义后台异步任务，例如 AI 处理、发送邮件/短信通知等。使用 Celery 实现。
- **`app/utils/`**: 存放通用的辅助函数和工具类，例如文件处理、日期时间转换等。
- **`app/main.py`**: FastAPI 应用的入口点，负责初始化应用、挂载路由、中间件等。
- **`alembic/`**: 使用 Alembic进行数据库 schema 迁移管理。
- **`tests/`**: 包含单元测试和集成测试。使用 Pytest 作为测试框架。
- **`scripts/`**: 存放常用的开发和运维脚本。

## 4. 依赖管理

推荐使用 Poetry 或 PDM 进行 Python 项目的依赖管理和打包。

## 5. 代码规范与质量

- **Linter**: Ruff (或 Flake8 + Black + iSort)
- **Formatter**: Black
- **Type Checking**: Mypy
- **Testing**: Pytest
- **Pre-commit hooks**: 使用 `pre-commit` 工具集成代码检查和格式化。

## 6. 部署建议

- **Web Server**: Uvicorn (with Gunicorn for process management in production)
- **Containerization**: Docker
- **Orchestration**: Kubernetes (可选, 适用于大规模部署)

此代码结构旨在提供一个清晰、可维护、可扩展的 FastAPI 项目骨架，便于团队协作和长期发展。
```