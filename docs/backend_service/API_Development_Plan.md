# Memorial项目后端API开发计划

## 基于PRD的阶段一开发计划

### 1. 用户认证与基础功能模块 (2-3周)

#### 1.1 用户注册/登录系统

**API端点设计:**

```yaml
# 用户注册
POST /api/v1/auth/register
# 用户登录
POST /api/v1/auth/login
# 刷新Token
POST /api/v1/auth/refresh
# 密码找回
POST /api/v1/auth/forgot-password
# 重置密码
POST /api/v1/auth/reset-password
# 登出
POST /api/v1/auth/logout
```

**数据模型:**
- User: 用户基础信息
- UserSession: 用户会话管理
- PasswordReset: 密码重置记录

#### 1.2 用户个人中心

**API端点设计:**

```yaml
# 获取用户信息
GET /api/v1/users/profile
# 更新用户信息
PUT /api/v1/users/profile
# 获取我的纪念空间列表
GET /api/v1/users/memorial-spaces
# 获取我的家族列表
GET /api/v1/users/families
```

### 2. 纪念空间核心功能模块 (4-5周)

#### 2.1 纪念空间管理

**API端点设计:**

```yaml
# 创建纪念空间
POST /api/v1/memorial-spaces
# 获取纪念空间详情
GET /api/v1/memorial-spaces/{space_id}
# 更新纪念空间
PUT /api/v1/memorial-spaces/{space_id}
# 删除纪念空间
DELETE /api/v1/memorial-spaces/{space_id}
# 获取纪念空间列表
GET /api/v1/memorial-spaces
```

#### 2.2 3D场景管理

**API端点设计:**

```yaml
# 获取可用场景列表
GET /api/v1/scenes
# 获取场景详情
GET /api/v1/scenes/{scene_id}
# 设置纪念空间场景
PUT /api/v1/memorial-spaces/{space_id}/scene
```

#### 2.3 资产管理 (照片/视频上传)

**API端点设计:**

```yaml
# 上传资产
POST /api/v1/memorial-spaces/{space_id}/assets
# 获取资产列表
GET /api/v1/memorial-spaces/{space_id}/assets
# 更新资产信息
PUT /api/v1/memorial-spaces/{space_id}/assets/{asset_id}
# 删除资产
DELETE /api/v1/memorial-spaces/{space_id}/assets/{asset_id}
```

### 3. 基础祭拜功能模块 (2-3周)

#### 3.1 虚拟祭拜

**API端点设计:**

```yaml
# 执行祭拜动作
POST /api/v1/memorial-spaces/{space_id}/tributes
# 获取祭拜记录
GET /api/v1/memorial-spaces/{space_id}/tributes
# 获取祭拜统计
GET /api/v1/memorial-spaces/{space_id}/tribute-stats
```

#### 3.2 留言功能

**API端点设计:**

```yaml
# 添加留言
POST /api/v1/memorial-spaces/{space_id}/messages
# 获取留言列表
GET /api/v1/memorial-spaces/{space_id}/messages
# 更新留言
PUT /api/v1/memorial-spaces/{space_id}/messages/{message_id}
# 删除留言
DELETE /api/v1/memorial-spaces/{space_id}/messages/{message_id}
```

### 4. 家族功能基础版 (2-3周)

#### 4.1 家族管理

**API端点设计:**

```yaml
# 创建家族
POST /api/v1/families
# 获取家族详情
GET /api/v1/families/{family_id}
# 更新家族信息
PUT /api/v1/families/{family_id}
# 邀请成员
POST /api/v1/families/{family_id}/invitations
# 加入家族
POST /api/v1/families/{family_id}/members
# 获取家族成员
GET /api/v1/families/{family_id}/members
```

#### 4.2 简单族谱功能

**API端点设计:**

```yaml
# 创建族谱节点
POST /api/v1/families/{family_id}/genealogy/nodes
# 获取族谱数据
GET /api/v1/families/{family_id}/genealogy
# 更新族谱节点
PUT /api/v1/families/{family_id}/genealogy/nodes/{node_id}
# 建立关系
POST /api/v1/families/{family_id}/genealogy/relationships
```

## 数据库设计要点

### 核心表结构

1. **users** - 用户表
2. **memorial_spaces** - 纪念空间表
3. **assets** - 资产表 (照片/视频)
4. **tributes** - 祭拜记录表
5. **messages** - 留言表
6. **families** - 家族表
7. **family_members** - 家族成员表
8. **genealogy_nodes** - 族谱节点表
9. **genealogy_relationships** - 族谱关系表
10. **scenes** - 3D场景表

## 技术实施优先级

### 第1周-第2周: 用户认证系统
- 实现JWT认证机制
- 用户注册/登录/密码重置
- 基础用户信息管理

### 第3周-第5周: 纪念空间核心功能
- 纪念空间CRUD操作
- 文件上传服务
- 3D场景选择功能

### 第6周-第7周: 祭拜功能
- 虚拟祭拜记录
- 留言系统
- 祭拜统计

### 第8周-第10周: 家族功能
- 家族创建与管理
- 成员邀请系统
- 基础族谱功能

## 关键技术决策

1. **认证方式**: JWT + Refresh Token
2. **文件存储**: 本地存储 + 后期云存储迁移
3. **数据库**: PostgreSQL (已确定)
4. **API文档**: OpenAPI 3.0 (Swagger)
5. **权限控制**: RBAC (基于角色的访问控制)

## 性能与安全考虑

1. **API限流**: 防止恶意请求
2. **数据验证**: 严格的输入验证
3. **文件安全**: 文件类型检查和大小限制
4. **隐私保护**: 纪念空间访问权限控制
5. **数据备份**: 定期数据备份策略

## 测试策略

1. **单元测试**: 覆盖核心业务逻辑
2. **集成测试**: API端点测试
3. **性能测试**: 并发访问测试
4. **安全测试**: 权限和数据安全测试

## 部署计划

1. **开发环境**: 本地Docker容器
2. **测试环境**: 云服务器部署
3. **生产环境**: 高可用部署方案
4. **监控**: 日志收集和性能监控

---

*此计划基于PRD文档制定，将根据开发进度和用户反馈进行调整优化。*