# Memorial API 规范文档

## OpenAPI 3.0 规范

```yaml
openapi: 3.0.3
info:
  title: Memorial API
  description: |
    Memorial项目后端API服务，提供纪念空间管理、用户认证、祭拜功能等核心服务。
    
    ## 认证方式
    API使用JWT Bearer Token认证。在请求头中添加：
    ```
    Authorization: Bearer <your_jwt_token>
    ```
    
    ## 错误处理
    API使用标准HTTP状态码，错误响应格式统一为：
    ```json
    {
      "error": {
        "code": "ERROR_CODE",
        "message": "错误描述",
        "details": {}
      }
    }
    ```
  version: 1.0.0
  contact:
    name: Memorial API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8011/api/v1
    description: 开发环境
  - url: https://api-test.memorial.com/api/v1
    description: 测试环境
  - url: https://api.memorial.com/api/v1
    description: 生产环境

security:
  - BearerAuth: []

paths:
  # 认证相关接口
  /auth/register:
    post:
      tags:
        - Authentication
      summary: 用户注册
      description: 创建新用户账户
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
            example:
              email: "<EMAIL>"
              password: "SecurePass123!"
              username: "testuser"
              phone: "+86-13800138000"
      responses:
        '201':
          description: 注册成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: 用户已存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/login:
    post:
      tags:
        - Authentication
      summary: 用户登录
      description: 用户登录获取访问令牌
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
            example:
              email: "<EMAIL>"
              password: "SecurePass123!"
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: 认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/refresh:
    post:
      tags:
        - Authentication
      summary: 刷新访问令牌
      description: 使用刷新令牌获取新的访问令牌
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                refresh_token:
                  type: string
                  description: 刷新令牌
              required:
                - refresh_token
      responses:
        '200':
          description: 刷新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '401':
          description: 刷新令牌无效
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: 用户登出
      description: 注销当前用户会话
      responses:
        '200':
          description: 登出成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "登出成功"

  # 用户相关接口
  /users/profile:
    get:
      tags:
        - Users
      summary: 获取用户信息
      description: 获取当前用户的个人信息
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    
    put:
      tags:
        - Users
      summary: 更新用户信息
      description: 更新当前用户的个人信息
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfile'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /users/memorial-spaces:
    get:
      tags:
        - Users
      summary: 获取我的纪念空间列表
      description: 获取当前用户创建或有权访问的纪念空间列表
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: limit
          in: query
          description: 每页数量
          schema:
            type: integer
            default: 20
            minimum: 1
            maximum: 100
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialSpaceList'

  # 纪念空间相关接口
  /memorial-spaces:
    get:
      tags:
        - Memorial Spaces
      summary: 获取纪念空间列表
      description: 获取公开的纪念空间列表
      security: []
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: limit
          in: query
          description: 每页数量
          schema:
            type: integer
            default: 20
            minimum: 1
            maximum: 100
        - name: search
          in: query
          description: 搜索关键词
          schema:
            type: string
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialSpaceList'
    
    post:
      tags:
        - Memorial Spaces
      summary: 创建纪念空间
      description: 创建新的纪念空间
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMemorialSpaceRequest'
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialSpace'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /memorial-spaces/{space_id}:
    get:
      tags:
        - Memorial Spaces
      summary: 获取纪念空间详情
      description: 获取指定纪念空间的详细信息
      parameters:
        - name: space_id
          in: path
          required: true
          description: 纪念空间ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialSpace'
        '404':
          description: 纪念空间不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: 无权访问
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    
    put:
      tags:
        - Memorial Spaces
      summary: 更新纪念空间
      description: 更新纪念空间信息
      parameters:
        - name: space_id
          in: path
          required: true
          description: 纪念空间ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMemorialSpaceRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemorialSpace'
        '404':
          description: 纪念空间不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: 无权限操作
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    
    delete:
      tags:
        - Memorial Spaces
      summary: 删除纪念空间
      description: 删除指定的纪念空间
      parameters:
        - name: space_id
          in: path
          required: true
          description: 纪念空间ID
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: 删除成功
        '404':
          description: 纪念空间不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: 无权限操作
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # 资产管理接口
  /memorial-spaces/{space_id}/assets:
    get:
      tags:
        - Assets
      summary: 获取资产列表
      description: 获取纪念空间的资产列表
      parameters:
        - name: space_id
          in: path
          required: true
          description: 纪念空间ID
          schema:
            type: string
            format: uuid
        - name: type
          in: query
          description: 资产类型过滤
          schema:
            type: string
            enum: [photo, video, audio, document]
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  assets:
                    type: array
                    items:
                      $ref: '#/components/schemas/Asset'
                  total:
                    type: integer
    
    post:
      tags:
        - Assets
      summary: 上传资产
      description: 上传照片、视频等资产到纪念空间
      parameters:
        - name: space_id
          in: path
          required: true
          description: 纪念空间ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: 上传的文件
                title:
                  type: string
                  description: 资产标题
                description:
                  type: string
                  description: 资产描述
                tags:
                  type: array
                  items:
                    type: string
                  description: 标签列表
              required:
                - file
      responses:
        '201':
          description: 上传成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Asset'
        '400':
          description: 文件格式不支持或大小超限
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # 祭拜功能接口
  /memorial-spaces/{space_id}/tributes:
    get:
      tags:
        - Tributes
      summary: 获取祭拜记录
      description: 获取纪念空间的祭拜记录列表
      parameters:
        - name: space_id
          in: path
          required: true
          description: 纪念空间ID
          schema:
            type: string
            format: uuid
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: limit
          in: query
          description: 每页数量
          schema:
            type: integer
            default: 20
            minimum: 1
            maximum: 100
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  tributes:
                    type: array
                    items:
                      $ref: '#/components/schemas/Tribute'
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer
    
    post:
      tags:
        - Tributes
      summary: 执行祭拜
      description: 在纪念空间执行祭拜动作
      parameters:
        - name: space_id
          in: path
          required: true
          description: 纪念空间ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTributeRequest'
      responses:
        '201':
          description: 祭拜成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tribute'

  # 留言功能接口
  /memorial-spaces/{space_id}/messages:
    get:
      tags:
        - Messages
      summary: 获取留言列表
      description: 获取纪念空间的留言列表
      parameters:
        - name: space_id
          in: path
          required: true
          description: 纪念空间ID
          schema:
            type: string
            format: uuid
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: limit
          in: query
          description: 每页数量
          schema:
            type: integer
            default: 20
            minimum: 1
            maximum: 100
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  messages:
                    type: array
                    items:
                      $ref: '#/components/schemas/Message'
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer
    
    post:
      tags:
        - Messages
      summary: 添加留言
      description: 在纪念空间添加留言
      parameters:
        - name: space_id
          in: path
          required: true
          description: 纪念空间ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateMessageRequest'
      responses:
        '201':
          description: 留言成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Message'

  # 3D场景接口
  /scenes:
    get:
      tags:
        - Scenes
      summary: 获取可用场景列表
      description: 获取所有可用的3D场景列表
      security: []
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  scenes:
                    type: array
                    items:
                      $ref: '#/components/schemas/Scene'

  /scenes/{scene_id}:
    get:
      tags:
        - Scenes
      summary: 获取场景详情
      description: 获取指定场景的详细信息
      security: []
      parameters:
        - name: scene_id
          in: path
          required: true
          description: 场景ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Scene'
        '404':
          description: 场景不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT认证，格式：Bearer <token>

  schemas:
    # 认证相关模型
    RegisterRequest:
      type: object
      required:
        - email
        - password
        - username
      properties:
        email:
          type: string
          format: email
          description: 邮箱地址
          example: "<EMAIL>"
        password:
          type: string
          minLength: 8
          description: 密码（至少8位）
          example: "SecurePass123!"
        username:
          type: string
          minLength: 2
          maxLength: 50
          description: 用户名
          example: "testuser"
        phone:
          type: string
          description: 手机号码
          example: "+86-13800138000"
        avatar_url:
          type: string
          format: uri
          description: 头像URL

    LoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
          description: 邮箱地址
        password:
          type: string
          description: 密码

    AuthResponse:
      type: object
      properties:
        access_token:
          type: string
          description: 访问令牌
        refresh_token:
          type: string
          description: 刷新令牌
        token_type:
          type: string
          example: "Bearer"
        expires_in:
          type: integer
          description: 令牌过期时间（秒）
          example: 3600
        user:
          $ref: '#/components/schemas/UserProfile'

    # 用户相关模型
    UserProfile:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: 用户ID
        email:
          type: string
          format: email
          description: 邮箱地址
        username:
          type: string
          description: 用户名
        phone:
          type: string
          description: 手机号码
        avatar_url:
          type: string
          format: uri
          description: 头像URL
        created_at:
          type: string
          format: date-time
          description: 创建时间
        updated_at:
          type: string
          format: date-time
          description: 更新时间
        profile:
          type: object
          properties:
            real_name:
              type: string
              description: 真实姓名
            birth_date:
              type: string
              format: date
              description: 出生日期
            location:
              type: string
              description: 所在地
            bio:
              type: string
              description: 个人简介

    UpdateUserRequest:
      type: object
      properties:
        username:
          type: string
          minLength: 2
          maxLength: 50
          description: 用户名
        phone:
          type: string
          description: 手机号码
        avatar_url:
          type: string
          format: uri
          description: 头像URL
        profile:
          type: object
          properties:
            real_name:
              type: string
              description: 真实姓名
            birth_date:
              type: string
              format: date
              description: 出生日期
            location:
              type: string
              description: 所在地
            bio:
              type: string
              description: 个人简介

    # 纪念空间相关模型
    MemorialSpace:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: 纪念空间ID
        title:
          type: string
          description: 纪念空间标题
        description:
          type: string
          description: 描述
        deceased_name:
          type: string
          description: 逝者姓名
        deceased_birth_date:
          type: string
          format: date
          description: 逝者出生日期
        deceased_death_date:
          type: string
          format: date
          description: 逝者逝世日期
        deceased_avatar_url:
          type: string
          format: uri
          description: 逝者头像URL
        privacy_level:
          type: string
          enum: [public, family_only, private]
          description: 隐私级别
        scene_id:
          type: string
          format: uuid
          description: 3D场景ID
        owner_id:
          type: string
          format: uuid
          description: 创建者ID
        created_at:
          type: string
          format: date-time
          description: 创建时间
        updated_at:
          type: string
          format: date-time
          description: 更新时间
        stats:
          type: object
          properties:
            visit_count:
              type: integer
              description: 访问次数
            tribute_count:
              type: integer
              description: 祭拜次数
            message_count:
              type: integer
              description: 留言数量
            asset_count:
              type: integer
              description: 资产数量

    CreateMemorialSpaceRequest:
      type: object
      required:
        - title
        - deceased_name
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 200
          description: 纪念空间标题
        description:
          type: string
          maxLength: 1000
          description: 描述
        deceased_name:
          type: string
          minLength: 1
          maxLength: 100
          description: 逝者姓名
        deceased_birth_date:
          type: string
          format: date
          description: 逝者出生日期
        deceased_death_date:
          type: string
          format: date
          description: 逝者逝世日期
        deceased_avatar_url:
          type: string
          format: uri
          description: 逝者头像URL
        privacy_level:
          type: string
          enum: [public, family_only, private]
          default: public
          description: 隐私级别
        scene_id:
          type: string
          format: uuid
          description: 3D场景ID

    UpdateMemorialSpaceRequest:
      type: object
      properties:
        title:
          type: string
          minLength: 1
          maxLength: 200
          description: 纪念空间标题
        description:
          type: string
          maxLength: 1000
          description: 描述
        deceased_birth_date:
          type: string
          format: date
          description: 逝者出生日期
        deceased_death_date:
          type: string
          format: date
          description: 逝者逝世日期
        deceased_avatar_url:
          type: string
          format: uri
          description: 逝者头像URL
        privacy_level:
          type: string
          enum: [public, family_only, private]
          description: 隐私级别
        scene_id:
          type: string
          format: uuid
          description: 3D场景ID

    MemorialSpaceList:
      type: object
      properties:
        spaces:
          type: array
          items:
            $ref: '#/components/schemas/MemorialSpace'
        total:
          type: integer
          description: 总数量
        page:
          type: integer
          description: 当前页码
        limit:
          type: integer
          description: 每页数量
        has_next:
          type: boolean
          description: 是否有下一页

    # 资产相关模型
    Asset:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: 资产ID
        title:
          type: string
          description: 资产标题
        description:
          type: string
          description: 资产描述
        file_url:
          type: string
          format: uri
          description: 文件URL
        thumbnail_url:
          type: string
          format: uri
          description: 缩略图URL
        file_type:
          type: string
          enum: [photo, video, audio, document]
          description: 文件类型
        file_size:
          type: integer
          description: 文件大小（字节）
        mime_type:
          type: string
          description: MIME类型
        tags:
          type: array
          items:
            type: string
          description: 标签列表
        uploaded_by:
          type: string
          format: uuid
          description: 上传者ID
        created_at:
          type: string
          format: date-time
          description: 创建时间

    # 祭拜相关模型
    Tribute:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: 祭拜记录ID
        tribute_type:
          type: string
          enum: [incense, flower, candle, bow, prayer]
          description: 祭拜类型
        message:
          type: string
          description: 祭拜留言
        user_id:
          type: string
          format: uuid
          description: 祭拜者ID
        user_name:
          type: string
          description: 祭拜者姓名
        created_at:
          type: string
          format: date-time
          description: 祭拜时间

    CreateTributeRequest:
      type: object
      required:
        - tribute_type
      properties:
        tribute_type:
          type: string
          enum: [incense, flower, candle, bow, prayer]
          description: 祭拜类型
        message:
          type: string
          maxLength: 500
          description: 祭拜留言

    # 留言相关模型
    Message:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: 留言ID
        content:
          type: string
          description: 留言内容
        user_id:
          type: string
          format: uuid
          description: 留言者ID
        user_name:
          type: string
          description: 留言者姓名
        user_avatar:
          type: string
          format: uri
          description: 留言者头像
        is_anonymous:
          type: boolean
          description: 是否匿名
        created_at:
          type: string
          format: date-time
          description: 留言时间
        updated_at:
          type: string
          format: date-time
          description: 更新时间

    CreateMessageRequest:
      type: object
      required:
        - content
      properties:
        content:
          type: string
          minLength: 1
          maxLength: 1000
          description: 留言内容
        is_anonymous:
          type: boolean
          default: false
          description: 是否匿名

    # 3D场景相关模型
    Scene:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: 场景ID
        name:
          type: string
          description: 场景名称
        description:
          type: string
          description: 场景描述
        preview_url:
          type: string
          format: uri
          description: 预览图URL
        model_url:
          type: string
          format: uri
          description: 3D模型URL
        category:
          type: string
          enum: [traditional, modern, nature, religious]
          description: 场景分类
        is_premium:
          type: boolean
          description: 是否为付费场景
        price:
          type: number
          format: float
          description: 价格（如果是付费场景）
        created_at:
          type: string
          format: date-time
          description: 创建时间

    # 错误响应模型
    ErrorResponse:
      type: object
      properties:
        error:
          type: object
          properties:
            code:
              type: string
              description: 错误代码
              example: "VALIDATION_ERROR"
            message:
              type: string
              description: 错误描述
              example: "请求参数验证失败"
            details:
              type: object
              description: 错误详情
              additionalProperties: true
          required:
            - code
            - message
      required:
        - error

tags:
  - name: Authentication
    description: 用户认证相关接口
  - name: Users
    description: 用户管理相关接口
  - name: Memorial Spaces
    description: 纪念空间管理相关接口
  - name: Assets
    description: 资产管理相关接口
  - name: Tributes
    description: 祭拜功能相关接口
  - name: Messages
    description: 留言功能相关接口
  - name: Scenes
    description: 3D场景相关接口
```

## API使用说明

### 认证流程

1. **用户注册**: `POST /auth/register`
2. **用户登录**: `POST /auth/login` - 获取access_token和refresh_token
3. **API调用**: 在请求头中添加 `Authorization: Bearer <access_token>`
4. **令牌刷新**: `POST /auth/refresh` - 使用refresh_token获取新的access_token

### 权限控制

- **公开接口**: 场景列表、公开纪念空间列表等
- **认证接口**: 需要有效JWT令牌
- **权限接口**: 需要特定权限（如纪念空间所有者权限）

### 文件上传

- 支持的图片格式: JPG, PNG, GIF, WebP
- 支持的视频格式: MP4, MOV, AVI
- 最大文件大小: 100MB
- 图片自动生成缩略图

### 分页参数

大部分列表接口支持分页：
- `page`: 页码，从1开始
- `limit`: 每页数量，默认20，最大100

### 错误处理

所有错误响应都遵循统一格式，包含错误代码和描述信息。常见错误代码：

- `VALIDATION_ERROR`: 请求参数验证失败
- `AUTHENTICATION_ERROR`: 认证失败
- `AUTHORIZATION_ERROR`: 权限不足
- `NOT_FOUND`: 资源不存在
- `CONFLICT`: 资源冲突
- `RATE_LIMIT_EXCEEDED`: 请求频率超限
- `INTERNAL_ERROR`: 服务器内部错误

### 性能优化

- 使用HTTP缓存头控制缓存策略
- 图片和视频资源使用CDN加速
- API响应启用gzip压缩
- 数据库查询优化和索引

---

*此API规范基于OpenAPI 3.0标准，可直接导入Swagger UI或其他API文档工具。*