"""
下载大理石背景图片
"""
import os
import requests
import time

# 创建目录
os.makedirs('frontend/public/images', exist_ok=True)

# 大理石纹理图片URL
marble_url = "https://img.freepik.com/free-photo/white-marble-texture-background_1203-5716.jpg"

try:
    print(f"正在下载大理石背景图片...")
    response = requests.get(marble_url)
    if response.status_code == 200:
        with open('frontend/public/images/marble-bg.jpg', 'wb') as f:
            f.write(response.content)
        print(f"已下载大理石背景图片")
    else:
        print(f"下载大理石背景图片失败，状态码: {response.status_code}")
except Exception as e:
    print(f"下载大理石背景图片时出错: {e}")

print("下载完成！")
