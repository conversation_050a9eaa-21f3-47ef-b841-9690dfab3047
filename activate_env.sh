#!/bin/bash

# Memorial项目环境激活脚本
echo "🚀 正在激活Memorial项目环境..."

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "❌ 错误: 未找到conda命令"
    echo "请先安装Anaconda或Miniconda: https://docs.conda.io/en/latest/miniconda.html"
    exit 1
fi

# 检查memorial环境是否存在
if ! conda env list | grep -q "memorial"; then
    echo "📦 Memorial环境不存在，正在创建..."
    if [ -f "environment.yml" ]; then
        conda env create -f environment.yml
    else
        echo "❌ 未找到environment.yml文件"
        echo "正在创建基础memorial环境..."
        conda create -n memorial python=3.11 -y
    fi
fi

# 激活环境
echo "✅ 激活conda环境: memorial"
conda activate memorial

# 设置环境变量
export PROJECT_ROOT=$(pwd)
export PYTHONPATH="${PROJECT_ROOT}/backend:${PYTHONPATH}"
export MEMORIAL_ENV="development"

echo "✅ Memorial项目环境已激活"
echo "📁 项目根目录: ${PROJECT_ROOT}"
echo "🐍 Python路径: ${PYTHONPATH}"

# 检查关键依赖
echo "🔍 检查关键依赖..."
python -c "
try:
    import torch
    print('✅ PyTorch已安装')
except ImportError:
    print('❌ PyTorch未安装')

try:
    import transformers
    print('✅ Transformers已安装')
except ImportError:
    print('❌ Transformers未安装')

try:
    import fastapi
    print('✅ FastAPI已安装')
except ImportError:
    print('❌ FastAPI未安装')
"

echo ""
echo "🎉 环境准备完成！您现在可以开始开发了。"
echo ""
echo "常用命令:"
echo "  启动后端服务: ./start_backend.sh"
echo "  启动渲染服务: ./start_render_service.sh"
echo "  启动统一服务: ./start_unified_service.sh"