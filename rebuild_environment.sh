#!/bin/bash

# Memorial项目环境重建脚本
# 用于清理和重建conda环境，确保依赖整洁

set -e  # 遇到错误时退出

echo "🧹 Memorial项目环境重建工具"
echo "=" | head -c 50; echo

# 检查conda是否可用
if ! command -v conda &> /dev/null; then
    echo "❌ 未找到conda，请先安装Anaconda或Miniconda"
    exit 1
fi

# 切换到项目根目录
cd "$(dirname "$0")"

echo "📍 当前目录: $(pwd)"

# 检查是否存在memorial环境
if conda env list | grep -q "memorial"; then
    echo "⚠️  发现现有的memorial环境"
    read -p "是否删除现有环境并重新创建？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🗑️  删除现有环境..."
        
        # 检查当前是否在memorial环境中
        current_env=$(conda info --envs | grep '\*' | awk '{print $1}' 2>/dev/null || echo "")
        if [[ "$current_env" == "memorial" ]] || [[ "$CONDA_DEFAULT_ENV" == "memorial" ]] || [[ "$CONDA_PREFIX" == *"memorial"* ]]; then
            echo "📤 检测到当前在memorial环境中，正在退出..."
            
            # 提示用户手动退出环境
            echo "⚠️  请先手动退出memorial环境，然后重新运行此脚本:"
            echo "   conda deactivate"
            echo "   ./rebuild_environment.sh"
            echo ""
            echo "或者在新的终端窗口中运行此脚本"
            exit 1
        fi
        
        # 删除环境
        conda env remove -n memorial -y
        echo "✅ 环境删除完成"
    else
        echo "❌ 用户取消操作"
        exit 0
    fi
fi

# 检查清理后的环境文件是否存在
if [ ! -f "environment_clean.yml" ]; then
    echo "❌ 未找到environment_clean.yml文件"
    echo "请先运行: python cleanup_dependencies.py"
    exit 1
fi

# 创建新的conda环境
echo "🔨 创建新的memorial环境..."
conda env create -f environment_clean.yml

if [ $? -eq 0 ]; then
    echo "✅ Conda环境创建成功"
else
    echo "❌ Conda环境创建失败"
    exit 1
fi

# 激活环境
echo "📦 激活memorial环境..."
source "$(conda info --base)/etc/profile.d/conda.sh"
conda activate memorial

# 安装后端依赖
if [ -f "backend/requirements.txt" ]; then
    echo "📥 安装后端依赖..."
    cd backend
    pip install -r requirements.txt
    cd ..
    echo "✅ 后端依赖安装完成"
else
    echo "⚠️  未找到backend/requirements.txt文件"
fi

# 安装前端依赖
if [ -d "frontend" ] && [ -f "frontend/package.json" ]; then
    echo "📥 安装前端依赖..."
    cd frontend
    
    # 检查pnpm是否可用
    if command -v pnpm &> /dev/null; then
        pnpm install
    elif command -v npm &> /dev/null; then
        npm install
    else
        echo "⚠️  未找到pnpm或npm，跳过前端依赖安装"
    fi
    
    cd ..
    echo "✅ 前端依赖安装完成"
fi

# 验证环境
echo "\n🔍 验证环境..."
echo "Python版本: $(python --version)"
echo "FastAPI: $(python -c 'import fastapi; print(fastapi.__version__)' 2>/dev/null || echo '未安装')"
echo "SQLAlchemy: $(python -c 'import sqlalchemy; print(sqlalchemy.__version__)' 2>/dev/null || echo '未安装')"
echo "Uvicorn: $(python -c 'import uvicorn; print(uvicorn.__version__)' 2>/dev/null || echo '未安装')"

# 检查重复依赖
echo "\n🔍 检查重复依赖..."
python -c "
import subprocess
import sys

def get_conda_packages():
    try:
        result = subprocess.run(['conda', 'list', '-n', 'memorial', '--export'], capture_output=True, text=True, check=True)
        conda_packages = {}
        for line in result.stdout.split('\\n'):
            if '=' in line and not line.startswith('#'):
                parts = line.split('=')
                if len(parts) >= 2:
                    name = parts[0].lower()
                    version = parts[1]
                    if 'pypi_0' not in line:
                        conda_packages[name] = version
        return conda_packages
    except:
        return {}

def get_pip_packages():
    try:
        result = subprocess.run(['pip', 'freeze'], capture_output=True, text=True, check=True)
        pip_packages = {}
        for line in result.stdout.split('\\n'):
            if '==' in line:
                name, version = line.split('==', 1)
                pip_packages[name.lower()] = version
        return pip_packages
    except:
        return {}

conda_packages = get_conda_packages()
pip_packages = get_pip_packages()

duplicates = []
for pip_name in pip_packages:
    for conda_name in conda_packages:
        if pip_name.lower() == conda_name.lower() or pip_name.replace('-', '_') == conda_name.replace('-', '_'):
            duplicates.append({
                'name': pip_name,
                'pip_version': pip_packages[pip_name],
                'conda_name': conda_name,
                'conda_version': conda_packages[conda_name]
            })

if duplicates:
    print(f'⚠️  发现 {len(duplicates)} 个重复依赖:')
    for dup in duplicates:
        print(f'  - {dup[\"name\"]}: pip={dup[\"pip_version\"]}, conda={dup[\"conda_version\"]}')
else:
    print('✅ 未发现重复依赖')
"

echo "\n🎉 环境重建完成！"
echo "\n📋 下一步操作:"
echo "1. 激活环境: conda activate memorial"
echo "2. 启动服务器: ./start_server.sh"
echo "3. 访问API文档: http://localhost:9010/docs"
echo "4. 访问前端应用: http://localhost:4002"

echo "\n💡 提示:"
echo "- 环境配置文件: environment_clean.yml"
echo "- 后端依赖: backend/requirements.txt"
echo "- 前端依赖: frontend/package.json"
echo "- 启动脚本: start_server.sh"