"""Enhance tribute records with additional fields

Revision ID: 7cd1cf0a6ebd
Revises: a0280a6c57cf
Create Date: 2025-06-10 12:00:41.543602

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7cd1cf0a6ebd'
down_revision: Union[str, None] = 'a0280a6c57cf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Add enhanced fields to tribute_records table
    op.add_column('tribute_records', sa.Column('tribute_items', sa.JSON(), nullable=True))
    op.add_column('tribute_records', sa.Column('duration_seconds', sa.Integer(), nullable=True, default=0))
    op.add_column('tribute_records', sa.Column('client_info', sa.JSON(), nullable=True))
    op.add_column('tribute_records', sa.Column('coordinates', sa.J<PERSON>(), nullable=True))


def downgrade() -> None:
    """Downgrade schema."""
    # Remove enhanced fields from tribute_records table
    op.drop_column('tribute_records', 'coordinates')
    op.drop_column('tribute_records', 'client_info') 
    op.drop_column('tribute_records', 'duration_seconds')
    op.drop_column('tribute_records', 'tribute_items')
