"""merge authentication and ancestor branches

Revision ID: a0280a6c57cf
Revises: 7d8086f901e1, add_user_auth_fields
Create Date: 2025-06-10 11:41:49.665360

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a0280a6c57cf'
down_revision: Union[str, None] = ('7d8086f901e1', 'add_user_auth_fields')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
