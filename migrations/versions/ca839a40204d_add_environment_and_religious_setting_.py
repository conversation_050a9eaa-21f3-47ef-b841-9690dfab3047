"""add_environment_and_religious_setting_models_and_adjust_schema

Revision ID: ca839a40204d
Revises: 73cbcbaa6437
Create Date: 2025-06-02 10:03:12.993936

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ca839a40204d'
down_revision: Union[str, None] = '73cbcbaa6437'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('environments',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('style_type', sa.String(length=100), nullable=True),
    sa.Column('religious_affiliation', sa.JSON(), nullable=True),
    sa.Column('cultural_elements', sa.JSON(), nullable=True),
    sa.Column('season_support', sa.Boolean(), nullable=True),
    sa.Column('weather_support', sa.Boolean(), nullable=True),
    sa.Column('model_path', sa.String(length=255), nullable=True),
    sa.Column('low_poly_path', sa.String(length=255), nullable=True),
    sa.Column('thumbnail', sa.String(length=255), nullable=True),
    sa.Column('creation_date', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_environments_id'), 'environments', ['id'], unique=False)
    op.create_index(op.f('ix_environments_name'), 'environments', ['name'], unique=False)
    op.create_table('religious_cultural_settings',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('ritual_elements', sa.JSON(), nullable=True),
    sa.Column('special_dates', sa.JSON(), nullable=True),
    sa.Column('default_prayers', sa.Text(), nullable=True),
    sa.Column('icon_path', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_religious_cultural_settings_id'), 'religious_cultural_settings', ['id'], unique=False)
    op.create_index(op.f('ix_religious_cultural_settings_name'), 'religious_cultural_settings', ['name'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_religious_cultural_settings_name'), table_name='religious_cultural_settings')
    op.drop_index(op.f('ix_religious_cultural_settings_id'), table_name='religious_cultural_settings')
    op.drop_table('religious_cultural_settings')
    op.drop_index(op.f('ix_environments_name'), table_name='environments')
    op.drop_index(op.f('ix_environments_id'), table_name='environments')
    op.drop_table('environments')
    # ### end Alembic commands ###
