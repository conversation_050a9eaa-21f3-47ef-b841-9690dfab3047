"""update_ancestor_model_fields

Revision ID: 7d8086f901e1
Revises: abe146946da6
Create Date: 2025-06-02 10:43:25.437214

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7d8086f901e1'
down_revision: Union[str, None] = 'abe146946da6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ancestors', sa.Column('biography', sa.Text(), nullable=True))
    op.add_column('ancestors', sa.Column('photo_path', sa.String(length=255), nullable=True))
    op.add_column('ancestors', sa.Column('model_path', sa.String(length=255), nullable=True))
    op.drop_column('ancestors', 'bio')
    op.drop_column('ancestors', 'photo_url')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ancestors', sa.Column('photo_url', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('ancestors', sa.Column('bio', sa.TEXT(), autoincrement=False, nullable=True))
    op.drop_column('ancestors', 'model_path')
    op.drop_column('ancestors', 'photo_path')
    op.drop_column('ancestors', 'biography')
    # ### end Alembic commands ###
