"""add_religious_preference_and_cultural_background_to_ancestors

Revision ID: abe146946da6
Revises: bbffc5fc1706
Create Date: 2025-06-02 10:39:30.104081

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'abe146946da6'
down_revision: Union[str, None] = 'bbffc5fc1706'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ancestors', sa.Column('religious_preference', sa.String(length=100), nullable=True))
    op.add_column('ancestors', sa.Column('cultural_background', sa.String(length=100), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ancestors', 'cultural_background')
    op.drop_column('ancestors', 'religious_preference')
    # ### end Alembic commands ###
