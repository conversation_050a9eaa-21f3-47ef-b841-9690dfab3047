"""Add user authentication fields

Revision ID: add_user_auth_fields
Revises: ca839a40204d
Create Date: 2025-06-10 15:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_user_auth_fields'
down_revision = 'ca839a40204d'
branch_labels = None
depends_on = None


def upgrade():
    """Add authentication-related fields to users table"""
    
    # Add password reset fields
    op.add_column('users', sa.Column('password_reset_token', sa.String(255), nullable=True))
    op.add_column('users', sa.Column('password_reset_expires_at', sa.DateTime(), nullable=True))
    
    # Add email verification fields  
    op.add_column('users', sa.Column('email_verification_token', sa.String(255), nullable=True))
    op.add_column('users', sa.Column('email_verification_expires_at', sa.DateTime(), nullable=True))
    
    # Add refresh token storage fields
    op.add_column('users', sa.Column('refresh_token_hash', sa.String(255), nullable=True))
    op.add_column('users', sa.Column('refresh_token_expires_at', sa.DateTime(), nullable=True))


def downgrade():
    """Remove authentication-related fields from users table"""
    
    # Remove refresh token fields
    op.drop_column('users', 'refresh_token_expires_at')
    op.drop_column('users', 'refresh_token_hash')
    
    # Remove email verification fields
    op.drop_column('users', 'email_verification_expires_at')
    op.drop_column('users', 'email_verification_token')
    
    # Remove password reset fields
    op.drop_column('users', 'password_reset_expires_at')
    op.drop_column('users', 'password_reset_token')