"""initial_schema_setup_with_correct_enums

Revision ID: 73cbcbaa6437
Revises: 
Create Date: 2025-06-02 09:34:18.098722

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '73cbcbaa6437'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###

    # Step 1: Drop tables that might have FKs to users.id (if they exist and are being dropped)
    # Ensure these are indeed meant to be dropped as per your models.
    op.drop_table('ancestors', if_exists=True) # Added if_exists for safety
    op.drop_table('religious_cultural_settings', if_exists=True) # Added if_exists for safety
    op.drop_table('environments', if_exists=True) # Added if_exists for safety

    # Step 2: Modify users table (this was Step 1 before, now after dropping dependent tables)
    op.add_column('users', sa.Column('full_name', sa.String(length=100), nullable=True))
    op.add_column('users', sa.Column('avatar_url', sa.String(length=255), nullable=True))
    op.add_column('users', sa.Column('bio', sa.Text(), nullable=True))
    op.add_column('users', sa.Column('is_verified', sa.Boolean(), nullable=False, server_default=sa.false()))
    op.add_column('users', sa.Column('updated_at', sa.DateTime(), nullable=False, server_default=sa.func.now()))
    op.add_column('users', sa.Column('last_login_at', sa.DateTime(), nullable=True))
    op.add_column('users', sa.Column('is_superuser', sa.Boolean(), nullable=False, server_default=sa.false()))
    
    op.alter_column('users', 'id', server_default=None) # Remove old default first

    op.alter_column('users', 'id',
               existing_type=sa.INTEGER(),
               type_=sa.UUID(),
               existing_nullable=False,
               postgresql_using='uuid_generate_v4()'
               )
    
    op.alter_column('users', 'username',
               existing_type=sa.VARCHAR(length=64),
               type_=sa.String(length=50),
               nullable=False)
    op.alter_column('users', 'email',
               existing_type=sa.VARCHAR(length=120),
               type_=sa.String(length=100),
               nullable=False)
    op.alter_column('users', 'password_hash',
               existing_type=sa.VARCHAR(length=128),
               type_=sa.String(length=255),
               nullable=False)
    op.alter_column('users', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               server_default=sa.true())
    op.alter_column('users', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False,
               server_default=sa.func.now())
    op.alter_column('users', 'role',
               existing_type=sa.VARCHAR(length=20),
               nullable=False,
               server_default='user')

    try:
        op.drop_index('ix_users_username', table_name='users')
    except Exception: # pragma: no cover
        pass
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)

    op.drop_column('users', 'reset_token', if_exists=True)
    op.drop_column('users', 'last_login', if_exists=True)
    op.drop_column('users', 'reset_token_expiry', if_exists=True)

    # Step 3: Create new tables that depend on users.id (now UUID)
    op.create_table('memorial_spaces',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('creator_id', sa.UUID(), nullable=False),
    sa.Column('deceased_name', sa.String(length=100), nullable=False),
    sa.Column('deceased_gender', sa.Enum('male', 'female', 'other', 'unknown', name='deceased_gender_enum_type'), nullable=True),
    sa.Column('birth_date', sa.Date(), nullable=True),
    sa.Column('death_date', sa.Date(), nullable=True),
    sa.Column('creator_relationship_to_deceased', sa.String(length=50), nullable=True),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('scene_id', sa.UUID(), nullable=True),
    sa.Column('music_url', sa.String(length=255), nullable=True),
    sa.Column('privacy_level', sa.Enum('public', 'private', 'password', 'family', name='privacy_level_enum_type'), nullable=False),
    sa.Column('access_password', sa.String(length=255), nullable=True),
    sa.Column('visit_count', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('custom_settings', sa.JSON(), nullable=True),
    sa.Column('cover_image_url', sa.String(length=255), nullable=True),
    sa.ForeignKeyConstraint(['creator_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_memorial_spaces_created_at'), 'memorial_spaces', ['created_at'], unique=False)
    op.create_index(op.f('ix_memorial_spaces_creator_id'), 'memorial_spaces', ['creator_id'], unique=False)
    op.create_index(op.f('ix_memorial_spaces_deceased_name'), 'memorial_spaces', ['deceased_name'], unique=False)
    op.create_index(op.f('ix_memorial_spaces_id'), 'memorial_spaces', ['id'], unique=False)
    op.create_index(op.f('ix_memorial_spaces_privacy_level'), 'memorial_spaces', ['privacy_level'], unique=False)
    op.create_index(op.f('ix_memorial_spaces_scene_id'), 'memorial_spaces', ['scene_id'], unique=False)

    op.create_table('memorial_assets',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('space_id', sa.UUID(), nullable=False),
    sa.Column('uploader_id', sa.UUID(), nullable=False),
    sa.Column('asset_type', sa.Enum('image', 'video', 'audio', 'document', 'other', 'cover_image', 'life_photo', name='asset_type_enum_type'), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('file_url', sa.String(length=255), nullable=False),
    sa.Column('thumbnail_url', sa.String(length=255), nullable=True),
    sa.Column('original_filename', sa.String(length=255), nullable=True),
    sa.Column('file_size', sa.Integer(), nullable=True),
    sa.Column('asset_metadata', sa.JSON(), nullable=True),
    sa.Column('display_order', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('is_ai_enhanced', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['space_id'], ['memorial_spaces.id'], ),
    sa.ForeignKeyConstraint(['uploader_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_memorial_assets_asset_type'), 'memorial_assets', ['asset_type'], unique=False)
    op.create_index(op.f('ix_memorial_assets_created_at'), 'memorial_assets', ['created_at'], unique=False)
    op.create_index(op.f('ix_memorial_assets_id'), 'memorial_assets', ['id'], unique=False)
    op.create_index(op.f('ix_memorial_assets_space_id'), 'memorial_assets', ['space_id'], unique=False)
    op.create_index(op.f('ix_memorial_assets_uploader_id'), 'memorial_assets', ['uploader_id'], unique=False)

    op.create_table('memorial_events',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('memorial_space_id', sa.UUID(), nullable=False),
    sa.Column('event_date', sa.String(length=50), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['memorial_space_id'], ['memorial_spaces.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_memorial_events_id'), 'memorial_events', ['id'], unique=False)
    op.create_index(op.f('ix_memorial_events_memorial_space_id'), 'memorial_events', ['memorial_space_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    # Step 1: Drop new tables (reverse of create in Step 3 of upgrade)
    op.drop_index(op.f('ix_memorial_events_memorial_space_id'), table_name='memorial_events')
    op.drop_index(op.f('ix_memorial_events_id'), table_name='memorial_events')
    op.drop_table('memorial_events')
    op.drop_index(op.f('ix_memorial_assets_uploader_id'), table_name='memorial_assets')
    op.drop_index(op.f('ix_memorial_assets_space_id'), table_name='memorial_assets')
    op.drop_index(op.f('ix_memorial_assets_id'), table_name='memorial_assets')
    op.drop_index(op.f('ix_memorial_assets_created_at'), table_name='memorial_assets')
    op.drop_index(op.f('ix_memorial_assets_asset_type'), table_name='memorial_assets')
    op.drop_table('memorial_assets')
    op.drop_index(op.f('ix_memorial_spaces_scene_id'), table_name='memorial_spaces')
    op.drop_index(op.f('ix_memorial_spaces_privacy_level'), table_name='memorial_spaces')
    op.drop_index(op.f('ix_memorial_spaces_id'), table_name='memorial_spaces')
    op.drop_index(op.f('ix_memorial_spaces_deceased_name'), table_name='memorial_spaces')
    op.drop_index(op.f('ix_memorial_spaces_creator_id'), table_name='memorial_spaces')
    op.drop_index(op.f('ix_memorial_spaces_created_at'), table_name='memorial_spaces')
    op.drop_table('memorial_spaces')

    # Step 2: Revert users table modifications (reverse of Step 2 of upgrade)
    op.add_column('users', sa.Column('reset_token_expiry', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('last_login', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.add_column('users', sa.Column('reset_token', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.create_index('ix_users_username', 'users', ['username'], unique=False)


    op.alter_column('users', 'role',
               existing_type=sa.VARCHAR(length=20),
               nullable=True,
               server_default=None)
    op.alter_column('users', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True,
               server_default=None)
    op.alter_column('users', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               server_default=None)
    op.alter_column('users', 'password_hash',
               existing_type=sa.String(length=255),
               type_=sa.VARCHAR(length=128),
               nullable=True)
    op.alter_column('users', 'email',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=120),
               nullable=True)
    op.alter_column('users', 'username',
               existing_type=sa.String(length=50),
               type_=sa.VARCHAR(length=64),
               nullable=True)
    
    # Revert 'id' type change; old server_default might need to be restored if it was specific
    op.alter_column('users', 'id',
               existing_type=sa.UUID(),
               type_=sa.INTEGER(),
               existing_nullable=False,
               server_default=None) # Assuming original int id might have been serial or had a sequence.
                                    # Restoring exact original default is complex and context-dependent.

    op.drop_column('users', 'is_superuser')
    op.drop_column('users', 'last_login_at')
    op.drop_column('users', 'updated_at')
    op.drop_column('users', 'is_verified')
    op.drop_column('users', 'bio')
    op.drop_column('users', 'avatar_url')
    op.drop_column('users', 'full_name')

    # Step 3: Recreate old tables (reverse of Step 1 of upgrade)
    op.create_table('environments',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=64), autoincrement=False, nullable=True),
    # ... (rest of environments columns)
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('style_type', sa.VARCHAR(length=64), autoincrement=False, nullable=True),
    sa.Column('religious_affiliation', sa.VARCHAR(length=128), autoincrement=False, nullable=True),
    sa.Column('cultural_elements', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('season_support', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('weather_support', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('model_path', sa.VARCHAR(length=256), autoincrement=False, nullable=True),
    sa.Column('low_poly_path', sa.VARCHAR(length=256), autoincrement=False, nullable=True),
    sa.Column('thumbnail', sa.VARCHAR(length=256), autoincrement=False, nullable=True),
    sa.Column('creation_date', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='environments_pkey')
    )
    op.create_table('religious_cultural_settings',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=64), autoincrement=False, nullable=True),
    # ... (rest of religious_cultural_settings columns)
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('ritual_elements', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('special_dates', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('default_prayers', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('icon_path', sa.VARCHAR(length=256), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='religious_cultural_settings_pkey')
    )
    op.create_table('ancestors',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=True), # This will now correctly reference users.id as INTEGER
    # ... (rest of ancestors columns)
    sa.Column('name', sa.VARCHAR(length=64), autoincrement=False, nullable=True),
    sa.Column('birth_date', sa.DATE(), autoincrement=False, nullable=True),
    sa.Column('death_date', sa.DATE(), autoincrement=False, nullable=True),
    sa.Column('religious_preference', sa.VARCHAR(length=64), autoincrement=False, nullable=True),
    sa.Column('cultural_background', sa.VARCHAR(length=64), autoincrement=False, nullable=True),
    sa.Column('biography', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('photo_path', sa.VARCHAR(length=256), autoincrement=False, nullable=True),
    sa.Column('model_path', sa.VARCHAR(length=256), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='ancestors_user_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='ancestors_pkey')
    )
    # ### end Alembic commands ###
