"""Add permission and access control tables

Revision ID: f2bd063696ef
Revises: 7cd1cf0a6ebd
Create Date: 2025-06-10 12:05:39.638705

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f2bd063696ef'
down_revision: Union[str, None] = '7cd1cf0a6ebd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Create memorial_space_permissions table
    op.create_table(
        'memorial_space_permissions',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('memorial_space_id', sa.UUID(), nullable=False),
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.Column('permission_type', sa.String(50), nullable=False),
        sa.Column('granted_by', sa.UUID(), nullable=False),
        sa.Column('granted_at', sa.DateTime(), nullable=False),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('can_view_private_info', sa.Boolean(), nullable=False, default=False),
        sa.Column('can_moderate', sa.Boolean(), nullable=False, default=False),
        sa.Column('can_invite_others', sa.Boolean(), nullable=False, default=False),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['memorial_space_id'], ['memorial_spaces.id']),
        sa.ForeignKeyConstraint(['user_id'], ['users.id']),
        sa.ForeignKeyConstraint(['granted_by'], ['users.id']),
    )
    op.create_index('idx_permissions_space_user', 'memorial_space_permissions', ['memorial_space_id', 'user_id'])
    op.create_index('idx_permissions_user', 'memorial_space_permissions', ['user_id'])
    
    # Create access_logs table
    op.create_table(
        'access_logs',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('memorial_space_id', sa.UUID(), nullable=False),
        sa.Column('user_id', sa.UUID(), nullable=True),
        sa.Column('access_type', sa.String(50), nullable=False),
        sa.Column('ip_address', sa.String(45), nullable=True),
        sa.Column('user_agent', sa.String(500), nullable=True),
        sa.Column('access_granted', sa.Boolean(), nullable=False),
        sa.Column('denial_reason', sa.String(100), nullable=True),
        sa.Column('accessed_at', sa.DateTime(), nullable=False),
        sa.Column('session_duration', sa.Integer(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['memorial_space_id'], ['memorial_spaces.id']),
        sa.ForeignKeyConstraint(['user_id'], ['users.id']),
    )
    op.create_index('idx_access_logs_space', 'access_logs', ['memorial_space_id'])
    op.create_index('idx_access_logs_user', 'access_logs', ['user_id'])
    op.create_index('idx_access_logs_accessed_at', 'access_logs', ['accessed_at'])


def downgrade() -> None:
    """Downgrade schema."""
    # Drop indexes first
    op.drop_index('idx_access_logs_accessed_at', table_name='access_logs')
    op.drop_index('idx_access_logs_user', table_name='access_logs') 
    op.drop_index('idx_access_logs_space', table_name='access_logs')
    op.drop_index('idx_permissions_user', table_name='memorial_space_permissions')
    op.drop_index('idx_permissions_space_user', table_name='memorial_space_permissions')
    
    # Drop tables
    op.drop_table('access_logs')
    op.drop_table('memorial_space_permissions')
