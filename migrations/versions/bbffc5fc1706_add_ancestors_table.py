"""add_ancestors_table

Revision ID: bbffc5fc1706
Revises: ca839a40204d
Create Date: 2025-06-02 10:34:21.268130

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'bbffc5fc1706'
down_revision: Union[str, None] = 'ca839a40204d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ancestors',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('birth_date', sa.Date(), nullable=True),
    sa.Column('death_date', sa.Date(), nullable=True),
    sa.Column('bio', sa.Text(), nullable=True),
    sa.Column('photo_url', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ancestors_id'), 'ancestors', ['id'], unique=False)
    op.create_index(op.f('ix_ancestors_name'), 'ancestors', ['name'], unique=False)
    op.create_index(op.f('ix_ancestors_user_id'), 'ancestors', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_ancestors_user_id'), table_name='ancestors')
    op.drop_index(op.f('ix_ancestors_name'), table_name='ancestors')
    op.drop_index(op.f('ix_ancestors_id'), table_name='ancestors')
    op.drop_table('ancestors')
    # ### end Alembic commands ###
