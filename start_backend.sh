#!/bin/bash

# 启动后端服务器脚本
# 使用方法: ./start_backend.sh

# 切换到项目目录
cd "$(dirname "$0")"

# 确保日志目录存在
mkdir -p backend/logs

# 停止可能已经运行的 Gunicorn 进程
pkill -f gunicorn || true

# 等待进程完全停止
sleep 1

# 启动 Gunicorn 服务器
cd backend && gunicorn -c gunicorn.conf.py gevent_patch:app

# 如果 Gunicorn 启动失败，尝试使用 Flask 开发服务器
if [ $? -ne 0 ]; then
    echo "Gunicorn 启动失败，尝试使用 Flask 开发服务器..."
    cd backend && python run.py
fi
