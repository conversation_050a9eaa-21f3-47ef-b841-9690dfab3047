2025-05-05 23:41:03,921 - __main__ - INFO - 成功创建OpenGL上下文，GPU渲染可用
2025-05-05 23:41:03,921 - __main__ - INFO - 已创建默认着色器
2025-05-05 23:41:03,923 - __main__ - INFO - 着色器程序加载成功
2025-05-05 23:41:03,923 - __main__ - INFO - 渲染器初始化成功
'FLASK_ENV' is deprecated and will not be used in Flask 2.3. Use 'FLASK_DEBUG' instead.
 * Serving Flask app 'render_service'
 * Debug mode: on
Address already in use
Port 5001 is in use by another program. Either identify and stop that program, or start the server with a different port.
