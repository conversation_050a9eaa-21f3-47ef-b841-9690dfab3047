# 构建说明

本文档包含在不同平台上构建 Flutter 应用程序的说明。

## 前提条件

- 安装 Flutter SDK: [Flutter 安装指南](https://flutter.dev/docs/get-started/install)
- 配置好你的开发环境 (Android Studio / VS Code, Xcode for iOS).
- 在项目根目录 (`mobile`) 运行 `flutter pub get` 来获取所有依赖。

##构建 Android 应用

### 构建 APK (用于测试和直接安装)

在项目根目录下运行以下命令：

```bash
flutter build apk --release
```

构建完成后，APK 文件通常位于 `build/app/outputs/flutter-apk/app-release.apk`。

你也可以构建 debug 版本的 APK：

```bash
flutter build apk --debug
```

Debug APK 位于 `build/app/outputs/flutter-apk/app-debug.apk`。

### 构建 App Bundle (AAB - 用于发布到 Google Play)

在项目根目录下运行以下命令：

```bash
flutter build appbundle --release
```

构建完成后，AAB 文件通常位于 `build/app/outputs/bundle/release/app-release.aab`。

## 构建 iOS 应用

### 通过 Xcode 构建

1.  在项目根目录下运行 `flutter build ios --release` (或 `--debug` 进行调试构建)。
2.  在 Xcode 中打开 `ios/Runner.xcworkspace`。
3.  选择你的目标设备或模拟器。
4.  在 Xcode 菜单中选择 Product > Archive。
5.  归档完成后，你可以通过 Organizer 窗口分发应用 (例如，上传到 App Store Connect 或导出 .ipa 文件)。

### 通过命令行构建 .ipa (需要配置签名)

如果你已经配置了代码签名，可以使用以下命令构建 .ipa 文件：

```bash
flutter build ipa --release
```

构建完成后，IPA 文件通常位于 `build/ios/ipa/` 目录下 (文件名可能包含项目名和版本号)。

## 运行应用

### 在模拟器/真机上运行

1.  确保至少有一个模拟器正在运行，或者已连接一个真机设备。
2.  在项目根目录下运行：

    ```bash
    flutter run
    ```

    你也可以指定目标设备：

    ```bash
    flutter run -d <device_id>
    ```

    使用 `flutter devices` 查看可用的设备 ID。

## 故障排除

- **依赖问题**: 尝试运行 `flutter clean` 然后 `flutter pub get`。
- **iOS 构建问题**: 确保你的 Xcode 和 CocoaPods 是最新的。有时需要删除 `ios/Pods` 目录和 `ios/Podfile.lock` 文件，然后重新运行 `flutter pub get` (它会自动运行 `pod install`)。
- **Android 构建问题**: 检查 Android SDK 和 NDK 的配置。确保 `android/local.properties` 文件中的 `sdk.dir` 指向正确的 Android SDK 位置。