# 构建应用包指南

本文档提供了为归处 Flutter 应用构建 Release 版本应用包的说明。

## iOS (IPA)

构建 iOS 应用包 (IPA) 通常需要一个有效的 Apple Developer Program 成员资格，并且需要在 macOS 环境下使用 Xcode。

1.  **确保配置正确**:
    *   在 Xcode 中打开项目 (`ios` 文件夹)。
    *   检查 `Runner > Signing & Capabilities` 中的 Bundle Identifier, Team, Provisioning Profile 等设置是否正确。
    *   确保 `Product > Scheme > Edit Scheme...` 中的 `Build Configuration` 设置为 `Release`。

2.  **清理项目 (可选但推荐)**:
    ```bash
    flutter clean
    ```

3.  **构建 IPA**:
    在 Flutter 项目的根目录下运行以下命令：
    ```bash
    flutter build ipa --release
    ```
    构建成功后，IPA 文件通常会位于 `build/ios/ipa/` 目录下，或者 Xcode 会提示其位置（例如通过 `Product > Archive` 后导出的位置）。

4.  **分发**:
    *   **TestFlight**: 将 IPA 上传到 App Store Connect，并通过 TestFlight 分发给测试人员。
    *   **Ad Hoc**: 为特定设备签名并分发。
    *   **App Store**: 提交到 App Store 进行审核和发布。

## Android (APK / App Bundle)

### 构建 APK

APK (Android Package Kit) 可以直接安装在 Android 设备上。

1.  **清理项目 (可选但推荐)**:
    ```bash
    flutter clean
    ```

2.  **构建 APK**:
    在 Flutter 项目的根目录下运行以下命令：
    ```bash
    flutter build apk --release
    ```
    默认情况下，这会为不同的 CPU 架构构建多个 APK 文件 (split APKs)。它们会位于 `build/app/outputs/flutter-apk/` 目录下。
    *   `app-armeabi-v7a-release.apk`
    *   `app-arm64-v8a-release.apk`
    *   `app-x86_64-release.apk`

    如果您需要一个通用的 APK (fat APK)，可以使用：
    ```bash
    flutter build apk --split-per-abi=false --release 
    # 或者 flutter build apk --target-platform android-arm,android-arm64,android-x64 --release (旧版)
    ```
    通用 APK 会位于 `build/app/outputs/apk/release/app-release.apk`。

### 构建 App Bundle (AAB)

App Bundle 是 Google Play 推荐的发布格式，它允许 Google Play 优化 APK 的大小，只向用户设备分发所需的代码和资源。

1.  **清理项目 (可选但推荐)**:
    ```bash
    flutter clean
    ```

2.  **构建 App Bundle**:
    在 Flutter 项目的根目录下运行以下命令：
    ```bash
    flutter build appbundle --release
    ```
    AAB 文件会位于 `build/app/outputs/bundle/release/app-release.aab`。

3.  **签名配置**:
    为了构建 Release 版本的 Android 应用，您需要配置签名密钥。请参考 Flutter 官方文档：
    [https://flutter.dev/docs/deployment/android#signing-the-app](https://flutter.dev/docs/deployment/android#signing-the-app)
    通常涉及创建一个 keystore 文件，并在 `android/app/build.gradle` 或 `android/key.properties` 中配置它。

4.  **分发**:
    将 AAB 文件上传到 Google Play Console 进行发布。

## 注意事项

*   **版本号和构建号**: 在 `pubspec.yaml` 文件中管理 `version` (例如 `1.0.0+1`，其中 `1.0.0` 是版本名，`1` 是版本号/构建号)。每次发布新版本时，务必更新这些值。
*   **图标和启动屏**: 确保您已为应用配置了正确的图标和启动屏。可以使用 `flutter_launcher_icons` 和 `flutter_native_splash` 等包来简化此过程。
*   **权限**: 检查并确保应用在 `AndroidManifest.xml` (Android) 和 `Info.plist` (iOS) 中声明了所有必要的权限，并提供了使用说明（如果适用）。