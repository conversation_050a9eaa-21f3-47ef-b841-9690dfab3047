import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:memorial/core/theme/app_colors.dart';
import 'package:memorial/features/auth/forgot_password_screen.dart';
import 'package:memorial/features/auth/login_screen.dart';
import 'package:memorial/features/auth/register_screen.dart';
import 'package:memorial/features/auth/reset_password_screen.dart';
import 'package:memorial/features/dashboard/dashboard_screen.dart';
import 'package:memorial/features/memorial/create_memorial_screen.dart';
import 'package:memorial/features/memorial/family_tree_screen.dart';
import 'package:memorial/features/memorial/hero_memorial_detail_screen.dart';
import 'package:memorial/features/memorial/heroes_memorial_screen.dart';
import 'package:memorial/features/memorial/memorial_space_detail_screen.dart';
import 'package:memorial/features/memorial/memorial_space_edit_screen.dart';
import 'package:memorial/features/settings/notifications_screen.dart';
import 'package:memorial/features/settings/settings_screen.dart';
import 'package:memorial/main_tab_navigator.dart';

void main() {
  runApp(
    const ProviderScope(
      child: MemorialApp(),
    ),
  );
}

class MemorialApp extends StatelessWidget {
  const MemorialApp({super.key});

  @override
  Widget build(BuildContext context) => CupertinoApp(
      title: 'Everloom 归处', // 基于Figma设计的应用标题
      theme: const CupertinoThemeData(
        brightness: Brightness.light, // 浅色主题
        primaryColor: AppColors.primary, // 柔和粉红色
        scaffoldBackgroundColor: AppColors.background, // 纯白背景
        barBackgroundColor: AppColors.background, // 纯白导航栏背景
        textTheme: CupertinoTextThemeData(
          primaryColor: AppColors.textPrimary, // 深炭黑文字
          textStyle: TextStyle(
            inherit: false, 
            color: AppColors.textPrimary, 
            fontSize: 10, // 基于Figma设计的正文字号
            fontWeight: FontWeight.w400,
            fontFamily: 'HiraginoKakuGothicStdN', // 基于设计规范的字体
          ),
          actionTextStyle: TextStyle(
            inherit: false, 
            color: AppColors.primary, 
            fontSize: 10, 
            fontWeight: FontWeight.w400,
            fontFamily: 'HiraginoKakuGothicStdN',
          ),
          navTitleTextStyle: TextStyle(
            inherit: false, 
            color: AppColors.textPrimary, 
            fontSize: 14, // 应用标题字号
            fontWeight: FontWeight.w800,
            fontFamily: 'HiraginoKakuGothicStdN',
          ),
          navLargeTitleTextStyle: TextStyle(
            inherit: false, 
            color: AppColors.textPrimary, 
            fontSize: 13, // Hero主标题字号
            fontWeight: FontWeight.w800,
            fontFamily: 'HiraginoKakuGothicStdN',
          ),
        ),
      ),
      home: const MainTabNavigator(),
      // routes 配置用于其他页面的导航
      routes: {
        '/login': (context) => const LoginScreen(),
        '/register': (context) => const RegisterScreen(),
        '/forgot-password': (context) => const ForgotPasswordScreen(),
        '/dashboard': (context) => const DashboardScreen(), // Dashboard screen
        '/create-memorial': (context) => const CreateMemorialScreen(),
        '/settings': (context) => const SettingsScreen(),
        '/family_tree': (context) => const FamilyTreeScreen(),
        '/notifications': (context) => const NotificationsScreen(),
        '/heroes-memorial': (context) => const HeroesMemorialScreen(),
        // Detail and Edit screens are handled by onGenerateRoute due to arguments
      },
      onGenerateRoute: (settings) {
        if (settings.name == '/memorial-space-detail') {
          // Expecting arguments like {'spaceId': 'some_id'}
          final args = settings.arguments as Map<String, dynamic>?;
          final spaceId = args?['spaceId'] as String? ?? '';
          return CupertinoPageRoute(
            builder: (context) => MemorialSpaceDetailScreen(spaceId: spaceId),
            settings: settings,
          );
        }
        if (settings.name == '/memorial-space-edit') {
          // Expecting arguments like {'spaceId': 'some_id'}
          final args = settings.arguments as Map<String, dynamic>?;
          final spaceId = args?['spaceId'] as String? ?? '';
          return CupertinoPageRoute(
            builder: (context) => MemorialSpaceEditScreen(spaceId: spaceId),
            settings: settings,
          );
        }
        if (settings.name == '/reset-password') {
          // Expecting arguments like {'token': 'reset_token'}
          final args = settings.arguments as Map<String, dynamic>?;
          final token = args?['token'] as String? ?? '';
          return CupertinoPageRoute(
            builder: (context) => ResetPasswordScreen(token: token),
            settings: settings,
          );
        }
        if (settings.name == '/hero-memorial-detail') {
          // Expecting arguments as heroId string
          final heroId = settings.arguments as String? ?? '';
          return CupertinoPageRoute(
            builder: (context) => HeroMemorialDetailScreen(heroId: heroId),
            settings: settings,
          );
        }
        // Handle other routes or return null for unknown routes
        return null;
      },
      debugShowCheckedModeBanner: false,
    );
}
