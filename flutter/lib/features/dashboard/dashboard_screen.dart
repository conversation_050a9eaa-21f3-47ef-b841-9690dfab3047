import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:memorial/core/theme/app_colors.dart';
import 'package:memorial/providers/app_state_providers.dart';
import 'package:memorial/providers/service_providers.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) =>
    CupertinoPageScaffold(
      backgroundColor: AppColors.background, // 使用系统背景色
      navigationBar: CupertinoNavigationBar(
        middle: const Text('仪表盘'),
        leading: CupertinoButton( // Added settings button to leading for common placement
          padding: EdgeInsets.zero,
          child: const Icon(CupertinoIcons.settings),
          onPressed: () {
            Navigator.pushNamed(context, '/settings');
          },
        ),
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          child: const Icon(CupertinoIcons.add_circled),
          onPressed: () {
            Navigator.pushNamed(context, '/create-memorial');
          },
        ),
      ),
      child: <PERSON><PERSON><PERSON>(
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: <Widget>[
            // Stats Section
            _buildStatsSection(context, ref),
            const SizedBox(height: 24),
            
            // Memorials Section
            _buildMemorialsSection(context, ref),
            const SizedBox(height: 24),
            
            // Quick Actions Section
            _buildQuickActionsSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection(BuildContext context, WidgetRef ref) {
    final memorialSpaces = ref.watch(memorialSpacesProvider);
    final currentUser = ref.watch(currentUserProvider);
    
    // 计算统计数据
    final myMemorialsCount = memorialSpaces.where((space) => space['createdBy'] == currentUser?['id']).length;
    final familySpacesCount = memorialSpaces.where((space) => space['type'] == 'family').length;
    final totalVisitsCount = memorialSpaces.fold<int>(0, (sum, space) => sum + (space['visitCount'] as int? ?? 0));
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '概览',
          style: CupertinoTheme.of(context).textTheme.navLargeTitleTextStyle.copyWith(
            color: AppColors.textPrimary,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _StatCard(
                title: '我的纪念馆',
                value: myMemorialsCount.toString(),
                icon: CupertinoIcons.heart_fill,
                iconColor: AppColors.iconSecondary,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _StatCard(
                title: '家族空间',
                value: familySpacesCount.toString(),
                icon: CupertinoIcons.group_solid,
                iconColor: AppColors.iconGreen,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _StatCard(
                title: '总祭拜次数',
                value: totalVisitsCount.toString(),
                icon: CupertinoIcons.flame_fill,
                iconColor: AppColors.iconRed,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMemorialsSection(BuildContext context, WidgetRef ref) {
    final memorialSpacesAsync = ref.watch(fetchMemorialSpacesProvider);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '纪念馆',
              style: CupertinoTheme.of(context).textTheme.navLargeTitleTextStyle.copyWith(
                color: AppColors.textPrimary,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            CupertinoButton(
              padding: EdgeInsets.zero,
              child: const Text(
                '查看全部',
                style: TextStyle(color: AppColors.primary), // 使用主题色
              ),
              onPressed: () {
                Navigator.pushNamed(context, '/memorials');
              },
            ),
          ],
        ),
        const SizedBox(height: 16),
        memorialSpacesAsync.when(
          data: (memorialSpaces) {
            if (memorialSpaces.isEmpty) {
              return Container(
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  color: AppColors.cardBackground,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Column(
                  children: [
                    Icon(
                      CupertinoIcons.heart,
                      size: 48,
                      color: AppColors.textSecondary,
                    ),
                    SizedBox(height: 16),
                    Text(
                      '还没有纪念馆',
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '创建第一个纪念馆来开始',
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              );
            }
            
            return Column(
              children: [
                ...memorialSpaces.take(3).map((space) => Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: _MemorialCard(
                    id: space['id']?.toString() ?? '',
                    deceasedName: space['deceasedName']?.toString() ?? '未知',
                    deathDate: space['deathDate']?.toString(),
                    coverImageUrl: space['coverImageUrl']?.toString(),
                    createdAt: space['createdAt'] != null ? DateTime.tryParse(space['createdAt'].toString()) : null,
                    onTap: () {
                      Navigator.pushNamed(
                        context,
                        '/memorial-detail',
                        arguments: space['id']?.toString(),
                      );
                    },
                  ),
                )),
                if (memorialSpaces.length > 3)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    decoration: BoxDecoration(
                      color: AppColors.cardBackground,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.primary.withAlpha(77), // 使用主题色，使用withAlpha替代withOpacity, 77约等于0.3*255
                      ),
                    ),
                    child: CupertinoButton(
                      padding: EdgeInsets.zero,
                      child: Text(
                        '查看更多纪念馆 (${memorialSpaces.length - 3})',
                        style: const TextStyle(color: AppColors.primary), // 使用主题色
                      ),
                      onPressed: () {
                        Navigator.pushNamed(context, '/memorials');
                      },
                    ),
                  ),
              ],
            );
          },
          loading: () => Container(
            height: 200,
            decoration: BoxDecoration(
              color: AppColors.cardBackground,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Center(
              child: CupertinoActivityIndicator(),
            ),
          ),
          error: (error, stack) => Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.cardBackground,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                const Icon(
                  CupertinoIcons.exclamationmark_triangle,
                  color: AppColors.iconRed,
                  size: 32,
                ),
                const SizedBox(height: 8),
                const Text(
                  '加载失败',
                  style: TextStyle(color: AppColors.iconRed),
                ),
                const SizedBox(height: 8),
                CupertinoButton(
                  child: const Text('重试'),
                  onPressed: () {
                    ref.invalidate(fetchMemorialSpacesProvider);
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActionsSection(BuildContext context) => Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '快捷操作',
          style: CupertinoTheme.of(context).textTheme.navLargeTitleTextStyle.copyWith(
            color: AppColors.textPrimary,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5,
          children: const [
            _QuickActionCard(
              title: '创建纪念馆',
              description: '为逝者创建纪念空间',
              icon: CupertinoIcons.add_circled_solid,
              link: '/create-memorial',
              iconColor: AppColors.iconSecondary,
            ),
            _QuickActionCard(
              title: '上传照片',
              description: '添加珍贵回忆',
              icon: CupertinoIcons.photo_on_rectangle,
              link: '/upload-photos',
              iconColor: AppColors.iconGreen,
            ),
            _QuickActionCard(
              title: '写追思',
              description: '记录思念之情',
              icon: CupertinoIcons.pencil_circle_fill,
              link: '/write-tribute',
              iconColor: AppColors.primary, // 使用主题色
            ),
            _QuickActionCard(
              title: '家族树',
              description: '管理家族关系',
              icon: CupertinoIcons.tree,
              link: '/family-tree',
              iconColor: AppColors.iconRed,
            ),
          ],
        ),
      ],
    );

// _StatCard Widget - Based on DashboardPage.tsx StatCard
class _StatCard extends StatelessWidget {
  const _StatCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.iconColor, // Color for the icon background (using opacity)
  });

  final String title;
  final String value;
  final IconData icon;
  final Color iconColor;

  @override
  Widget build(BuildContext context) =>
    Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withAlpha(26), // 26 ≈ 0.1 * 255
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 20,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: CupertinoTheme.of(context).textTheme.navLargeTitleTextStyle.copyWith(
              color: AppColors.textPrimary,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: CupertinoTheme.of(context).textTheme.tabLabelTextStyle.copyWith(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

// _MemorialCard Widget - Based on DashboardPage.tsx MemorialCard
class _MemorialCard extends StatelessWidget {
  const _MemorialCard({
    required this.id,
    required this.deceasedName,
    required this.onTap,
    this.deathDate,
    this.coverImageUrl,
    this.createdAt,
  });

  final String id;
  final String deceasedName;
  final String? deathDate;
  final String? coverImageUrl;
  final DateTime? createdAt;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) =>
    GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.cardBackground,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            // Cover Image or Placeholder
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppColors.primary.withAlpha(26), // 使用主题色，26 ≈ 0.1 * 255
                borderRadius: BorderRadius.circular(8),
                image: coverImageUrl != null
                    ? DecorationImage(
                        image: NetworkImage(coverImageUrl!),
                        fit: BoxFit.cover,
                      )
                    : null,
              ),
              child: coverImageUrl == null
                  ? const Icon(
                      CupertinoIcons.person_circle,
                      color: AppColors.primary, // 使用主题色
                      size: 30,
                    )
                  : null,
            ),
            const SizedBox(width: 16),
            // Memorial Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    deceasedName,
                    style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
                      color: AppColors.textPrimary,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (deathDate != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      deathDate!,
                      style: CupertinoTheme.of(context).textTheme.tabLabelTextStyle.copyWith(
                        color: AppColors.textSecondary,
                        fontSize: 14,
                      ),
                    ),
                  ],
                  if (createdAt != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      '创建于 ${createdAt!.year}年${createdAt!.month}月${createdAt!.day}日',
                      style: CupertinoTheme.of(context).textTheme.tabLabelTextStyle.copyWith(
                        color: AppColors.textSecondary,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            // Arrow Icon
            const Icon(
              CupertinoIcons.chevron_right,
              color: AppColors.textSecondary,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

// _QuickActionCard Widget - Based on DashboardPage.tsx QuickActionCard
class _QuickActionCard extends StatelessWidget {
  const _QuickActionCard({
    required this.title,
    required this.description,
    required this.icon,
    required this.link,
    required this.iconColor,
  });

  final String title;
  final String description;
  final IconData icon;
  final String link;
  final Color iconColor;

  @override
  Widget build(BuildContext context) =>
    GestureDetector(
      onTap: () {
        Navigator.pushNamed(context, link);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.cardBackground,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: iconColor.withAlpha(26), // 26 ≈ 0.1 * 255
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 24,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
                color: AppColors.textPrimary,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: CupertinoTheme.of(context).textTheme.tabLabelTextStyle.copyWith(
                color: AppColors.textSecondary,
                fontSize: 11,
              ),
            ),
          ],
        ),
      ),
    );
  }
