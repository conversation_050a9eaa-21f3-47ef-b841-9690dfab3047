import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:memorial/providers/app_state_providers.dart';
import 'package:memorial/providers/service_providers.dart';

/// 示例屏幕：展示如何在 ConsumerWidget 中使用 Riverpod
class RiverpodExampleScreen extends ConsumerWidget {
  const RiverpodExampleScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 监听各种状态
    final isAuthenticated = ref.watch(authStateProvider);
    final currentUser = ref.watch(currentUserProvider);
    final memorialSpaces = ref.watch(memorialSpacesProvider);
    final isLoading = ref.watch(loadingStateProvider);
    final errorMessage = ref.watch(errorMessageProvider);
    
    // 监听异步数据
    final memorialSpacesAsync = ref.watch(fetchMemorialSpacesProvider);

    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: const Text('Riverpod 示例'),
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          child: const Icon(CupertinoIcons.refresh),
          onPressed: () {
            // 刷新数据
            ref.invalidate(fetchMemorialSpacesProvider);
          },
        ),
      ),
      child: SafeArea(
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // 认证状态卡片
            _buildStateCard(
              title: '认证状态',
              content: isAuthenticated ? '已登录' : '未登录',
              color: isAuthenticated ? CupertinoColors.systemGreen : CupertinoColors.systemRed,
            ),
            
            const SizedBox(height: 16),
            
            // 用户信息卡片
            _buildStateCard(
              title: '当前用户',
              content: currentUser?.toString() ?? '无用户信息',
              color: CupertinoColors.systemBlue,
            ),
            
            const SizedBox(height: 16),
            
            // 加载状态卡片
            _buildStateCard(
              title: '加载状态',
              content: isLoading ? '加载中...' : '空闲',
              color: isLoading ? CupertinoColors.systemOrange : CupertinoColors.systemGreen,
            ),
            
            const SizedBox(height: 16),
            
            // 错误信息卡片
            if (errorMessage != null)
              _buildStateCard(
                title: '错误信息',
                content: errorMessage,
                color: CupertinoColors.systemRed,
              ),
            
            const SizedBox(height: 16),
            
            // 纪念空间列表（异步数据）
            _buildAsyncDataCard(
              title: '纪念空间列表（异步）',
              asyncValue: memorialSpacesAsync,
            ),
            
            const SizedBox(height: 16),
            
            // 纪念空间列表（同步状态）
            _buildStateCard(
              title: '纪念空间列表（同步）',
              content: '共 ${memorialSpaces.length} 个空间',
              color: CupertinoColors.systemPurple,
            ),
            
            const SizedBox(height: 24),
            
            // 操作按钮
            _buildActionButtons(ref),
          ],
        ),
      ),
    );
  }

  Widget _buildStateCard({
    required String title,
    required String content,
    required Color color,
  }) => Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGroupedBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              color: CupertinoColors.label,
            ),
          ),
        ],
      ),
    );

  Widget _buildAsyncDataCard({
    required String title,
    required AsyncValue<List<Map<String, dynamic>>> asyncValue,
  }) => Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGroupedBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: CupertinoColors.systemBlue.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: CupertinoColors.systemBlue,
            ),
          ),
          const SizedBox(height: 8),
          asyncValue.when(
            data: (spaces) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '共 ${spaces.length} 个纪念空间',
                  style: const TextStyle(
                    fontSize: 14,
                    color: CupertinoColors.label,
                  ),
                ),
                ...spaces.map((space) => Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    '• ${space['name']}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: CupertinoColors.secondaryLabel,
                    ),
                  ),
                )),
              ],
            ),
            loading: () => const CupertinoActivityIndicator(),
            error: (error, stack) => Text(
              '错误: $error',
              style: const TextStyle(
                fontSize: 14,
                color: CupertinoColors.systemRed,
              ),
            ),
          ),
        ],
      ),
    );

  Widget _buildActionButtons(WidgetRef ref) => Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: CupertinoButton.filled(
            child: const Text('切换认证状态'),
            onPressed: () {
              final current = ref.read(authStateProvider);
              ref.read(authStateProvider.notifier).state = !current;
            },
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: CupertinoButton.filled(
            child: const Text('模拟加载'),
            onPressed: () async {
              ref.read(loadingStateProvider.notifier).state = true;
              await Future<void>.delayed(const Duration(seconds: 2));
              ref.read(loadingStateProvider.notifier).state = false;
            },
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: CupertinoButton(
            child: const Text('设置错误信息'),
            onPressed: () {
              ref.read(errorMessageProvider.notifier).state = '这是一个测试错误信息';
            },
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: CupertinoButton(
            child: const Text('清除错误信息'),
            onPressed: () {
              ref.read(errorMessageProvider.notifier).state = null;
            },
          ),
        ),
      ],
    );
}
