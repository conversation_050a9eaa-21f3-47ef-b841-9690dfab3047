import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:memorial/core/theme/app_colors.dart';

/// 个人资料页面 - 完全按照移动端UI原型实现
class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) => CupertinoPageScaffold(
    backgroundColor: AppColors.background, // 使用系统背景色
    navigationBar: CupertinoNavigationBar(
      backgroundColor: CupertinoColors.systemBackground,
      border: const Border(
        bottom: BorderSide(color: CupertinoColors.separator, width: 0.5),
      ),
      middle: const Text(
        '我的',
        style: TextStyle(
          fontSize: 17,
          fontWeight: FontWeight.w600,
          color: CupertinoColors.label,
        ),
      ),
      trailing: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () {
          Navigator.pushNamed(context, '/settings');
        },
        child: const Icon(
          CupertinoIcons.settings,
          color: CupertinoColors.activeBlue,
          size: 22,
        ),
      ),
    ),
    child: Safe<PERSON>rea(
      child: CustomScrollView(
        slivers: [
          // 用户信息头部
          SliverToBoxAdapter(child: _buildUserHeader(context)),

          // 统计信息
          SliverToBoxAdapter(child: _buildStatsSection()),

          // 功能菜单
          SliverToBoxAdapter(child: _buildMenuSection(context)),

          // 底部间距
          const SliverToBoxAdapter(child: SizedBox(height: 32)),
        ],
      ),
    ),
  );

  /// 构建用户信息头部
  Widget _buildUserHeader(BuildContext context) => Container(
    padding: const EdgeInsets.all(20),
    decoration: const BoxDecoration(
      color: CupertinoColors.systemBackground,
      border: Border(
        bottom: BorderSide(color: CupertinoColors.separator, width: 0.5),
      ),
    ),
    child: Row(
      children: [
        // 头像
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(40),
            image: const DecorationImage(
              image: NetworkImage(
                'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
              ),
              fit: BoxFit.cover,
            ),
            border: Border.all(color: CupertinoColors.separator, width: 2),
          ),
        ),
        const SizedBox(width: 16),

        // 用户信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '张三',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                  color: CupertinoColors.label,
                ),
              ),
              const SizedBox(height: 4),
              const Text(
                '<EMAIL>',
                style: TextStyle(
                  fontSize: 14,
                  color: CupertinoColors.secondaryLabel,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'VIP会员',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.primary, // 使用主题色
                  ),
                ),
              ),
            ],
          ),
        ),

        // 编辑按钮
        CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: () => _showEditProfile(context),
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: CupertinoColors.systemGrey6,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              CupertinoIcons.pencil,
              color: CupertinoColors.activeBlue,
              size: 18,
            ),
          ),
        ),
      ],
    ),
  );

  /// 构建统计信息区域
  Widget _buildStatsSection() => Container(
    margin: const EdgeInsets.all(16),
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: CupertinoColors.secondarySystemGroupedBackground,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: CupertinoColors.black.withValues(alpha: 0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Row(
      children: [
        Expanded(
          child: _buildStatItem(
            icon: CupertinoIcons.heart,
            count: '5',
            label: '纪念空间',
            color: AppColors.primary, // 使用主题色
          ),
        ),
        Container(width: 1, height: 40, color: CupertinoColors.separator),
        Expanded(
          child: _buildStatItem(
            icon: CupertinoIcons.photo,
            count: '128',
            label: '照片',
            color: AppColors.success, // 系统绿色
          ),
        ),
        Container(width: 1, height: 40, color: CupertinoColors.separator),
        Expanded(
          child: _buildStatItem(
            icon: CupertinoIcons.eye,
            count: '1.2K',
            label: '访问量',
            color: AppColors.warning, // 高级橙
          ),
        ),
      ],
    ),
  );

  /// 构建统计项
  Widget _buildStatItem({
    required IconData icon,
    required String count,
    required String label,
    required Color color,
  }) => Column(
    children: [
      Icon(icon, color: color, size: 24),
      const SizedBox(height: 8),
      Text(
        count,
        style: const TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: CupertinoColors.label,
        ),
      ),
      const SizedBox(height: 4),
      Text(
        label,
        style: const TextStyle(
          fontSize: 12,
          color: CupertinoColors.secondaryLabel,
        ),
      ),
    ],
  );

  /// 构建功能菜单区域
  Widget _buildMenuSection(BuildContext context) => Container(
    margin: const EdgeInsets.symmetric(horizontal: 16),
    decoration: BoxDecoration(
      color: CupertinoColors.secondarySystemGroupedBackground,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: CupertinoColors.black.withValues(alpha: 0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Column(
      children: [
        _buildMenuItem(
          icon: CupertinoIcons.heart_fill,
          title: '我的收藏',
          subtitle: '收藏的纪念空间',
          color: AppColors.error, // 系统红色
          onTap: () => _showComingSoon(context, '我的收藏'),
        ),
        _buildDivider(),
        _buildMenuItem(
          icon: CupertinoIcons.clock,
          title: '访问历史',
          subtitle: '最近浏览记录',
          color: const Color(0xFFF8B37F), // 高级橙
          onTap: () => _showComingSoon(context, '访问历史'),
        ),
        _buildDivider(),
        _buildMenuItem(
          icon: CupertinoIcons.share,
          title: '邀请好友',
          subtitle: '分享给更多人',
          color: AppColors.success, // 系统绿色
          onTap: () => _showInviteFriends(context),
        ),
        _buildDivider(),
        _buildMenuItem(
          icon: CupertinoIcons.question_circle,
          title: '帮助与反馈',
          subtitle: '使用帮助和意见反馈',
          color: AppColors.warning, // 高级橙
          onTap: () => _showComingSoon(context, '帮助与反馈'),
        ),
        _buildDivider(),
        _buildMenuItem(
          icon: CupertinoIcons.info_circle,
          title: '关于归处',
          subtitle: '版本信息和服务条款',
          color: AppColors.primary, // 使用主题色
          onTap: () => _showAboutApp(context),
        ),
      ],
    ),
  );

  /// 构建菜单项
  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) => CupertinoButton(
    padding: EdgeInsets.zero,
    onPressed: onTap,
    child: Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: CupertinoColors.label,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 12,
                    color: CupertinoColors.secondaryLabel,
                  ),
                ),
              ],
            ),
          ),
          const Icon(
            CupertinoIcons.chevron_right,
            color: CupertinoColors.systemGrey,
            size: 16,
          ),
        ],
      ),
    ),
  );

  /// 构建分割线
  Widget _buildDivider() => Container(
    margin: const EdgeInsets.only(left: 68),
    height: 0.5,
    color: CupertinoColors.separator,
  );

  /// 显示编辑个人资料
  void _showEditProfile(BuildContext context) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('编辑个人资料'),
        content: const Text('个人资料编辑功能即将推出！'),
        actions: [
          CupertinoDialogAction(
            child: const Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  /// 显示邀请好友
  void _showInviteFriends(BuildContext context) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('邀请好友'),
        content: const Column(
          children: [
            SizedBox(height: 16),
            Text('分享归处给您的朋友，一起缅怀逝者，传承美好回忆。'),
            SizedBox(height: 16),
            Text(
              '邀请码：YN2024',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: CupertinoColors.activeBlue,
              ),
            ),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: const Text('复制邀请码'),
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoon(context, '复制功能');
            },
          ),
          CupertinoDialogAction(
            child: const Text('分享'),
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoon(context, '分享功能');
            },
          ),
        ],
      ),
    );
  }

  /// 显示关于应用
  void _showAboutApp(BuildContext context) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('关于归处'),
        content: const Column(
          children: [
            SizedBox(height: 16),
            Text('归处 v1.0.0'),
            SizedBox(height: 8),
            Text('一个温暖的数字纪念平台'),
            SizedBox(height: 8),
            Text('让爱与思念永远传承'),
            SizedBox(height: 16),
            Text(
              '© 2024 归处团队',
              style: TextStyle(
                fontSize: 12,
                color: CupertinoColors.secondaryLabel,
              ),
            ),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: const Text('服务条款'),
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoon(context, '服务条款');
            },
          ),
          CupertinoDialogAction(
            child: const Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  /// 显示即将推出的功能提示
  void _showComingSoon(BuildContext context, String feature) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text(feature),
        content: const Text('该功能即将推出，敬请期待！'),
        actions: [
          CupertinoDialogAction(
            child: const Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}
