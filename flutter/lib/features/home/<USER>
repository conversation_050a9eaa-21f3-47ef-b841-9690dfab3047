import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:memorial/core/theme/app_colors.dart';

/// 首页界面 - 基于Figma设计的完整实现
class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  final TextEditingController _messageController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void dispose() {
    _messageController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _sendMessage() {
    final message = _messageController.text.trim();
    if (message.isNotEmpty) {
      // TODO(developer): 实现发送消息逻辑
      print('发送消息: $message');
      _messageController.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    
    // 使用最小高度而不是固定高度，让内容自然流动
    final minHeroHeight = screenHeight * 0.3; // 最小30%给Hero区域
    const minFeaturesHeight = 200.0; // 功能区域最小高度
    const minChatHeight = 120.0; // 聊天区域最小高度
    const minAiServiceHeight = 80.0; // AI服务区域最小高度
    
    return CupertinoPageScaffold(
      backgroundColor: AppColors.background,
      child: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Hero Section with gradient background
              ConstrainedBox(
                constraints: BoxConstraints(minHeight: minHeroHeight),
                child: _buildHeroSection(context, minHeroHeight),
              ),
              // Features Grid Section
              ConstrainedBox(
                constraints: const BoxConstraints(minHeight: minFeaturesHeight),
                child: _buildFeaturesGrid(context, minFeaturesHeight),
              ),
              // Chat Interface Section
              ConstrainedBox(
                constraints: const BoxConstraints(minHeight: minChatHeight),
                child: _buildChatInterface(context, minChatHeight),
              ),
              // AI Service Section
              ConstrainedBox(
                constraints: const BoxConstraints(minHeight: minAiServiceHeight),
                child: _buildAIServiceSection(context, minAiServiceHeight),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建Hero区域 - 基于Figma设计
  Widget _buildHeroSection(BuildContext context, double height) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    return Container(
      height: height,
      width: double.infinity,
      decoration: const BoxDecoration(
         image: DecorationImage(
           image: AssetImage('assets/images/hero_background.jpg'),
           fit: BoxFit.cover,
         ),
       ),
      child: DecoratedBox(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              CupertinoColors.black.withValues(alpha: 0.3),
              CupertinoColors.black.withValues(alpha: 0.7),
            ],
          ),
        ),
        child: Stack(
          children: [
            // 主要内容
            Center(
              child: Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: screenWidth * 0.05,
                  vertical: height * 0.1,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                  Text(
                    'Everloom 归处',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: (screenWidth * 0.06).clamp(20.0, 26.0),
                      fontWeight: FontWeight.bold,
                      color: CupertinoColors.white,
                    ),
                  ),
                  SizedBox(height: height * 0.05),
                  Text(
                    '跨越时空的思念',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: (screenWidth * 0.045).clamp(14.0, 18.0),
                      fontWeight: FontWeight.w600,
                      color: CupertinoColors.white,
                    ),
                  ),
                  SizedBox(height: height * 0.03),
                  Text(
                    '为您提供一个不受时间、地点限制的个性化在线\n纪念空间，让思念永不消逝',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: (screenWidth * 0.028).clamp(10.0, 14.0),
                      color: CupertinoColors.white,
                      height: 1.4,
                    ),
                  ),
                ],
                ),
              ),
            ),
            // Logo 在右上角
            Positioned(
              top: height * 0.05,
              right: screenWidth * 0.05,
              child: Container(
                width: (screenWidth * 0.12).clamp(40.0, 60.0),
                height: (screenWidth * 0.12).clamp(40.0, 60.0),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: CupertinoColors.black.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.asset(
                    'assets/images/flame_icon.png',
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建功能网格区域 - 基于Figma设计，支持左右滑动，每页显示单张功能卡片
  Widget _buildFeaturesGrid(BuildContext context, double height) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    // 动态计算间距
    final horizontalMargin = screenWidth * 0.05;
    
    return Container(
      height: height,
      margin: EdgeInsets.symmetric(
        horizontal: horizontalMargin,
        vertical: height * 0.05,
      ),
      child: PageView(
        controller: PageController(viewportFraction: 0.9),
        children: [
          _buildSingleFeatureCard(
            '不受时空限制',
            '随时随地，通过互联网便可表达敬意，不再受限于时间和地点约束。',
            'assets/images/feature_card_bg_1.png',
          ),
          _buildSingleFeatureCard(
            '3D沉浸体验',
            '采用最新3D技术，提供逼真的虚拟纪念场景和互动体验，增强情感连接。',
            'assets/images/feature_card_bg_2.png',
          ),
          _buildSingleFeatureCard(
            'AI赋能服务',
            '利用AI技术修复老照片、照片3D效果生成、克隆声音，让静态图片动起来。',
            'assets/images/feature_card_bg_3.png',
          ),
          _buildSingleFeatureCard(
            '家族共享传承',
            '支持家族成员共同管理和参与，建立数字化族谱，传承家族记忆。',
            'assets/images/feature_card_bg_4.png',
          ),
          _buildSingleFeatureCard(
            '在线祭拜与互动',
            '在3D纪念空间中进行虚拟祭拜，献花、点烛、上香，发表追思留言，与其他亲友共同缅怀。',
            'assets/images/feature_card_bg_5.png',
          ),
          _buildSingleFeatureCard(
            '创建个性化纪念空间',
            '为逝者创建专属的在线纪念馆，上传照片、视频、音频资料，撰写生平事迹，选择合适的3D场景，打造独一无二的纪念空间。',
            'assets/images/feature_card_bg_6.png',
          ),
          _buildHeroesMemorialCard(),
        ],
      ),
    );
  }

  /// 构建单张功能卡片页面
  Widget _buildSingleFeatureCard(String title, String description, String backgroundImage) => Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.pureWhite,
        borderRadius: BorderRadius.circular(16),
        boxShadow: const [
          BoxShadow(
            color: AppColors.shadowLight,
            offset: Offset(0, 2),
            blurRadius: 8,
          ),
        ],
      ),
      child: DecoratedBox(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(backgroundImage),
            fit: BoxFit.cover,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              offset: const Offset(0, 2),
              blurRadius: 8,
            ),
          ],
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                Colors.black.withValues(alpha: 0.6),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: double.infinity,
                child: Text(
                  title,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 6),
              SizedBox(
                width: double.infinity,
                child: Text(
                  description,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white,
                    height: 1.3,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );

  /// 构建AI对话提示区域 - 简化版本
  Widget _buildChatInterface(BuildContext context, double height) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    // 动态计算间距和尺寸
    final horizontalMargin = screenWidth * 0.05;
    final padding = screenWidth * 0.04;
    
    return Container(
      height: height,
      margin: EdgeInsets.symmetric(
        horizontal: horizontalMargin,
        vertical: height * 0.05,
      ),
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: AppColors.pureWhite.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.lightGrayBorder.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 第一行：图标和主标题
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                CupertinoIcons.chat_bubble_2,
                size: (screenWidth * 0.045).clamp(14.0, 20.0),
                color: AppColors.primary.withValues(alpha: 0.7),
              ),
              SizedBox(width: screenWidth * 0.02),
              Text(
                '长小养照护智能为您服务',
                style: TextStyle(
                  fontSize: (screenWidth * 0.03).clamp(10.0, 14.0),
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: height * 0.025),
          Text(
            '在下方输入问题，即可与AI智能助手直接对话',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: (screenWidth * 0.025).clamp(8.0, 12.0),
              color: AppColors.textSecondary,
              height: 1.3,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建AI服务区域 - 基于Figma设计
  Widget _buildAIServiceSection(BuildContext context, double minHeight) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    // 动态计算间距和尺寸
    final horizontalMargin = screenWidth * 0.05;
    final buttonSize = (screenWidth * 0.07).clamp(24.0, 32.0);
    
    return Container(
      constraints: BoxConstraints(minHeight: minHeight),
      margin: EdgeInsets.symmetric(
        horizontal: horizontalMargin,
        vertical: 16, // 使用固定的垂直边距
      ),
      child: Column(
        children: [
          // Divider line
          Container(
            height: 1,
            color: AppColors.lightGrayBorder,
            margin: const EdgeInsets.symmetric(
              vertical: 12, // 使用固定的垂直边距
            ),
          ),
          // Chat input area
          Row(
            children: [
              // Input field
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: screenWidth * 0.04,
                    vertical: 8, // 使用固定的垂直padding
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.inputBackground,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: CupertinoTextField(
                    controller: _messageController,
                    focusNode: _focusNode,
                    placeholder: '请输入您的问题',
                    placeholderStyle: TextStyle(
                      fontSize: (screenWidth * 0.03).clamp(10.0, 14.0),
                      color: AppColors.pureWhite.withValues(alpha: 0.6),
                      fontWeight: FontWeight.w500,
                    ),
                    style: TextStyle(
                      fontSize: (screenWidth * 0.03).clamp(10.0, 14.0),
                      color: AppColors.pureWhite,
                      fontWeight: FontWeight.w500,
                    ),
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
              ),
              SizedBox(width: screenWidth * 0.03),
              // Send button
              GestureDetector(
                onTap: _sendMessage,
                child: Container(
                  width: buttonSize,
                  height: buttonSize,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(buttonSize / 2),
                  ),
                  child: Icon(
                     CupertinoIcons.paperplane_fill,
                     color: AppColors.pureWhite,
                     size: (screenWidth * 0.035).clamp(12.0, 16.0),
                   ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建英雄纪念馆卡片
  Widget _buildHeroesMemorialCard() => GestureDetector(
      onTap: () {
        Navigator.pushNamed(context, '/heroes-memorial');
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 10),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.pureWhite,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [
            BoxShadow(
              color: AppColors.shadowLight,
              offset: Offset(0, 2),
              blurRadius: 8,
            ),
          ],
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.red.withValues(alpha: 0.8),
                Colors.orange.withValues(alpha: 0.9),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                offset: const Offset(0, 2),
                blurRadius: 8,
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 英雄图标
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Icon(
                  CupertinoIcons.star_fill,
                  size: 30,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 16),
              // 标题
              const Text(
                '英雄纪念馆',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              // 描述
              const Text(
                '缅怀为人类进步做出卓越贡献的英雄们，学习他们的精神品格，传承英雄事迹。',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.white,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 16),
              // 进入按钮
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                  ),
                ),
                child: const Text(
                  '进入纪念馆',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
}
