import 'package:flutter/cupertino.dart';

class CommonSection extends StatelessWidget {
  const CommonSection({
    required this.child,
    super.key,
    this.margin = EdgeInsets.zero,
    this.padding,
    this.title,
  });

  final Widget child;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final String? title;

  @override
  Widget build(BuildContext context) => Container(
      margin: margin ?? EdgeInsets.zero,
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null && title!.isNotEmpty)
            Padding(
              padding: EdgeInsets.zero,
              child: Text(
                title!,
                style: CupertinoTheme.of(context).textTheme.navTitleTextStyle.copyWith(fontWeight: FontWeight.bold),
              ),
            ),
          if (title != null && title!.isNotEmpty) const SizedBox(height: 8), // 在标题和子组件之间添加间距
          child,
        ],
      ),
    );
}
