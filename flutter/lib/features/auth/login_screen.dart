import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:memorial/core/theme/app_colors.dart';
import 'package:memorial/providers/auth_provider.dart';

// TODO(design): Define or import colors and styles from a central theme/design spec.
// 移除硬编码颜色，使用AppColors.primary
const Color kTextColor = CupertinoColors.label;
const Color kSecondaryTextColor = CupertinoColors.secondaryLabel;
const Color kBackgroundColor = CupertinoColors.systemGroupedBackground;
const Color kErrorColor = CupertinoColors.systemRed;

class LoginScreen extends ConsumerStatefulWidget {
  const LoginScreen({super.key});

  @override
  ConsumerState<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends ConsumerState<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  Future<void> _login() async {
    if (_formKey.currentState?.validate() ?? false) {
      final authNotifier = ref.read(authProvider.notifier);
      
      final success = await authNotifier.login(
        _usernameController.text,
        _passwordController.text,
      );
      
      if (success && mounted) {
        // 登录成功，导航到仪表板
        unawaited(Navigator.of(context).pushReplacementNamed('/dashboard'));
      }
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 使用 ref.watch 监听状态变化
    final isLoading = ref.watch(authLoadingProvider);
    final errorMessage = ref.watch(authErrorProvider);
    
    return CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('登录'),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // App Logo or Title
                const Icon(
                  CupertinoIcons.memories,
                  size: 80,
                  color: AppColors.primary,
                ),
                const SizedBox(height: 16),
                const Text(
                  '归处',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: kTextColor,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  '永恒的记忆，温暖的怀念',
                  style: TextStyle(
                    fontSize: 16,
                    color: kSecondaryTextColor,
                  ),
                ),
                const SizedBox(height: 48),

                // Username Field
                CupertinoTextFormFieldRow(
                  controller: _usernameController,
                  placeholder: '用户名或邮箱',
                  prefix: const Icon(CupertinoIcons.person),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入用户名或邮箱';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Password Field
                CupertinoTextFormFieldRow(
                  controller: _passwordController,
                  placeholder: '密码',
                  prefix: const Icon(CupertinoIcons.lock),
                  obscureText: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入密码';
                    }
                    if (value.length < 6) {
                      return '密码至少需要6个字符';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Error Message
                if (errorMessage != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Text(
                      errorMessage,
                      style: const TextStyle(
                        color: kErrorColor,
                        fontSize: 14,
                      ),
                    ),
                  ),

                // Login Button
                SizedBox(
                  width: double.infinity,
                  child: CupertinoButton.filled(
                    onPressed: isLoading ? null : _login,
                    child: isLoading
                        ? const CupertinoActivityIndicator(color: CupertinoColors.white)
                        : const Text('登录'),
                  ),
                ),
                const SizedBox(height: 16),

                // Register Link
                CupertinoButton(
                  onPressed: () {
                    Navigator.of(context).pushNamed('/register');
                  },
                  child: const Text('还没有账户？立即注册'),
                ),

                const SizedBox(height: 24),

                // Forgot Password Link
                CupertinoButton(
                  onPressed: () {
                    Navigator.of(context).pushNamed('/forgot-password');
                  },
                  child: const Text(
                    '忘记密码？',
                    style: TextStyle(
                      color: kSecondaryTextColor,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
