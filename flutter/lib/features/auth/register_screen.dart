import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:memorial/core/theme/app_colors.dart';
import 'package:memorial/providers/auth_provider.dart';


// TODO(design): Define or import colors and styles from a central theme/design spec
// 移除硬编码颜色，使用AppColors.primary
const Color kTextColor = CupertinoColors.label;
const Color kSecondaryTextColor = CupertinoColors.secondaryLabel;
const Color kBackgroundColor = CupertinoColors.systemGroupedBackground;
const Color kErrorColor = CupertinoColors.systemRed;

class RegisterScreen extends ConsumerStatefulWidget {
  const RegisterScreen({super.key});

  @override
  ConsumerState<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends ConsumerState<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();



  Future<void> _register() async {
    final authNotifier = ref.read(authProvider.notifier)
      ..clearError(); // 清除之前的错误信息

    // 基本的客户端密码确认验证
    if (_passwordController.text != _confirmPasswordController.text) {
      // TODO(auth): 设置错误信息到状态管理
      return;
    }

    if (_formKey.currentState?.validate() ?? false) {
      final success = await authNotifier.register(
        _usernameController.text,
        _emailController.text,
        _passwordController.text,
      );
      
      if (success && mounted) {
        // 注册成功，导航到登录屏幕
        unawaited(Navigator.of(context).pushReplacementNamed('/login'));
      }
    }
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 监听 Riverpod 状态
    final isLoading = ref.watch(authLoadingProvider);
    final errorMessage = ref.watch(authErrorProvider);
    
    return CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('注册'),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // App logo or title
                const Text(
                  '创建账户',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(height: 32),

                // Username field
                CupertinoTextFormFieldRow(
                  controller: _usernameController,
                  placeholder: '用户名',
                  prefix: const Icon(CupertinoIcons.person, color: kSecondaryTextColor),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入用户名';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Email field
                CupertinoTextFormFieldRow(
                  controller: _emailController,
                  placeholder: '邮箱',
                  keyboardType: TextInputType.emailAddress,
                  prefix: const Icon(CupertinoIcons.mail, color: kSecondaryTextColor),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入邮箱';
                    }
                    if (!value.contains('@')) {
                      return '请输入有效的邮箱地址';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Password field
                CupertinoTextFormFieldRow(
                  controller: _passwordController,
                  placeholder: '密码',
                  obscureText: true,
                  prefix: const Icon(CupertinoIcons.lock, color: kSecondaryTextColor),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入密码';
                    }
                    if (value.length < 6) {
                      return '密码至少需要6个字符';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Confirm password field
                CupertinoTextFormFieldRow(
                  controller: _confirmPasswordController,
                  placeholder: '确认密码',
                  obscureText: true,
                  prefix: const Icon(CupertinoIcons.lock, color: kSecondaryTextColor),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请确认密码';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Error message
                if (errorMessage != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Text(
                      errorMessage,
                      style: const TextStyle(color: kErrorColor),
                      textAlign: TextAlign.center,
                    ),
                  ),

                // Register button
                SizedBox(
                  width: double.infinity,
                  child: CupertinoButton.filled(
                    onPressed: isLoading ? null : _register,
                    child: isLoading
                        ? const CupertinoActivityIndicator(color: CupertinoColors.white)
                        : const Text('注册'),
                  ),
                ),
                const SizedBox(height: 16),

                // Login link
                CupertinoButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('已有账户？立即登录'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
