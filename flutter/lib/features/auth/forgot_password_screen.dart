import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _emailController = TextEditingController();
  bool _isLoading = false;

  Future<void> _sendResetEmail() async {
    final email = _emailController.text.trim();
    
    if (email.isEmpty) {
      _showErrorDialog('请输入您的邮箱地址。');
      return;
    }

    if (!_isValidEmail(email)) {
      _showErrorDialog('请输入有效的邮箱地址。');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      const baseUrl = 'http://localhost:8001'; // 使用8001端口
      final response = await http.post(
        Uri.parse('$baseUrl/auth/forgot-password'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: json.encode({
          'email': email,
        }),
      );

      if (response.statusCode == 200) {
        _showSuccessDialog(email);
      } else {
        final errorData = json.decode(response.body) as Map<String, dynamic>;
        final errorMessage = errorData['detail'] as String? ?? '发送失败，请重试';
        _showErrorDialog(errorMessage);
      }
    } on Exception {
      _showErrorDialog('网络错误，请检查网络连接后重试');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  bool _isValidEmail(String email) =>
      RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);

  void _showSuccessDialog(String email) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('邮件已发送'),
        content: Text('密码重置邮件已发送到 $email，请查收邮件并按照指示重置密码。'),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            child: const Text('好的'),
            onPressed: () {
              Navigator.of(context).pop(); // 关闭对话框
              Navigator.of(context).pop(); // 返回登录页
            },
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('错误'),
        content: Text(message),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            isDefaultAction: true,
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('好的'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) => CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('忘记密码'),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              CupertinoTextField(
                controller: _emailController,
                placeholder: '请输入邮箱地址',
                keyboardType: TextInputType.emailAddress,
                prefix: const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8),
                  child: Icon(CupertinoIcons.mail),
                ),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: CupertinoColors.inactiveGray,
                    width: 0,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              const SizedBox(height: 20),
              CupertinoButton.filled(
                onPressed: _isLoading ? null : _sendResetEmail,
                child: _isLoading
                    ? const CupertinoActivityIndicator(color: CupertinoColors.white)
                    : const Text('发送重置链接'),
              ),
            ],
          ),
        ),
      ),
    );

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }
}
