import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:memorial/core/models/hero.dart' as memorial;
import 'package:memorial/data/heroes_data.dart';

class HeroesMemorialScreen extends StatefulWidget {
  const HeroesMemorialScreen({super.key});

  @override
  State<HeroesMemorialScreen> createState() => _HeroesMemorialScreenState();
}

class _HeroesMemorialScreenState extends State<HeroesMemorialScreen> {
  String selectedCategory = 'all';
  String selectedNationality = 'all';

  final categories = [
    {'value': 'all', 'label': '全部类别'},
    {'value': 'military', 'label': '军事英雄'},
    {'value': 'science', 'label': '科学巨匠'},
    {'value': 'medical', 'label': '医学先锋'},
    {'value': 'civil', 'label': '民权斗士'},
  ];

  final nationalities = [
    {'value': 'all', 'label': '全部国家'},
    {'value': '中国', 'label': '中国'},
    {'value': '美国', 'label': '美国'},
    {'value': '南非', 'label': '南非'},
    {'value': '波兰/法国', 'label': '波兰/法国'},
    {'value': '印度', 'label': '印度'},
  ];

  List<memorial.Hero> get filteredHeroes {
    var heroes = HeroesData.heroes;
    if (selectedCategory != 'all') {
      heroes = heroes.where((h) => h.category == selectedCategory).toList();
    }
    if (selectedNationality != 'all') {
      heroes = heroes.where((h) => h.nationality == selectedNationality).toList();
    }
    return heroes;
  }

  @override
  Widget build(BuildContext context) => Scaffold(
      backgroundColor: CupertinoColors.systemGroupedBackground,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          '英雄纪念馆',
          style: TextStyle(color: Colors.black),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            // Header
            SliverToBoxAdapter(
              child: Container(
                padding: const EdgeInsets.all(16),
                color: Colors.white,
                child: Column(
                  children: [
                    Text(
                      'Heroes Memorial Hall',
                      style: TextStyle(
                        fontSize: 20,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '纪念那些为人类进步、社会公正、科学发展做出卓越贡献的英雄们。他们的精神永远激励着我们前行。',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

            // Filters
            SliverToBoxAdapter(
              child: Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Category Filter
                    SizedBox(
                      height: 40,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: categories.length,
                        itemBuilder: (context, index) {
                          final category = categories[index];
                          final isSelected = selectedCategory == category['value'];
                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: ChoiceChip(
                              label: Text(category['label']!),
                              selected: isSelected,
                              onSelected: (selected) {
                                setState(() {
                                  selectedCategory = category['value']!;
                                });
                              },
                              selectedColor: Colors.blue,
                              labelStyle: TextStyle(
                                color: isSelected ? Colors.white : Colors.black87,
                                fontSize: 14,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Nationality Filter
                    SizedBox(
                      height: 40,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: nationalities.length,
                        itemBuilder: (context, index) {
                          final nationality = nationalities[index];
                          final isSelected = selectedNationality == nationality['value'];
                          return Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: ChoiceChip(
                              label: Text(nationality['label']!),
                              selected: isSelected,
                              onSelected: (selected) {
                                setState(() {
                                  selectedNationality = nationality['value']!;
                                });
                              },
                              selectedColor: Colors.green,
                              labelStyle: TextStyle(
                                color: isSelected ? Colors.white : Colors.black87,
                                fontSize: 14,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Heroes Grid
            SliverPadding(
              padding: const EdgeInsets.all(16),
              sliver: filteredHeroes.isEmpty
                  ? SliverToBoxAdapter(
                      child: Center(
                        child: Padding(
                          padding: const EdgeInsets.all(32),
                          child: Text(
                            '没有找到符合条件的英雄',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                      ),
                    )
                  : SliverGrid(
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 0.65,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                      ),
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final hero = filteredHeroes[index];
                          return _HeroCard(hero: hero);
                        },
                        childCount: filteredHeroes.length,
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
}

class _HeroCard extends StatelessWidget {
  const _HeroCard({required this.hero});

  final memorial.Hero hero;

  @override
  Widget build(BuildContext context) => GestureDetector(
        onTap: () {
          Navigator.pushNamed(
            context,
            '/hero-memorial-detail',
            arguments: hero.id,
          );
        },
        child: DecoratedBox(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Hero Image
              Container(
                height: 120,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                  color: Colors.grey[200],
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                      child: Image.network(
                        hero.imageUrl,
                        width: double.infinity,
                        height: double.infinity,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                              color: Colors.grey[300],
                              child: Center(
                                child: Text(
                                  hero.name[0],
                                  style: const TextStyle(
                                    fontSize: 36,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                      ),
                    ),
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.9),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          hero.nationality,
                          style: const TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Hero Info
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        hero.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (hero.nameEn != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          hero.nameEn!,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                      const SizedBox(height: 4),
                      Text(
                        hero.lifeSpan,
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[500],
                        ),
                      ),
                      const SizedBox(height: 6),
                      Text(
                        hero.title,
                        style: const TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                          color: Colors.blue,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 6),
                      Expanded(
                        child: Text(
                          hero.description,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[700],
                            height: 1.3,
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        width: double.infinity,
                        child: CupertinoButton(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(8),
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              '/hero-memorial-detail',
                              arguments: hero.id,
                            );
                          },
                          child: const Text(
                            '进入纪念空间',
                            style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
}
