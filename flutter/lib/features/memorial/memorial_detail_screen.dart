import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:memorial/core/theme/app_colors.dart';

// Color constants
// 移除硬编码颜色，使用AppColors.primary
const Color kTextColor = CupertinoColors.label;
const Color kSecondaryTextColor = CupertinoColors.secondaryLabel;
const Color kBackgroundColor = CupertinoColors.systemGroupedBackground;
const Color kCardBackgroundColor =
    CupertinoColors.secondarySystemGroupedBackground;

class MemorialDetailScreen extends ConsumerStatefulWidget {
  const MemorialDetailScreen({required this.memorialId, super.key});

  final String memorialId;

  @override
  ConsumerState<MemorialDetailScreen> createState() =>
      _MemorialDetailScreenState();
}

class _MemorialDetailScreenState extends ConsumerState<MemorialDetailScreen> {
  Map<String, dynamic>? _memorialData;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadMemorialData();
  }

  Future<void> _loadMemorialData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // 模拟网络请求延迟
      await Future<void>.delayed(const Duration(seconds: 1));

      // 模拟数据
      setState(() {
        _memorialData = {
          'id': widget.memorialId,
          'name': '张三',
          'type': '父亲',
          'birthDate': '1950-01-15',
          'deathDate': '2020-12-31',
          'description': '一位慈祥的父亲，勤劳一生，为家庭奉献了所有。他的智慧和爱将永远伴随我们。',
          'avatar': 'https://example.com/avatar.jpg',
          'visitCount': 1234,
          'photos': [
            {
              'id': '1',
              'url': 'https://example.com/photo1.jpg',
              'caption': '年轻时的照片',
            },
            {
              'id': '2',
              'url': 'https://example.com/photo2.jpg',
              'caption': '全家福',
            },
            {
              'id': '3',
              'url': 'https://example.com/photo3.jpg',
              'caption': '工作照',
            },
          ],
          'timeline': [
            {'year': '1950', 'event': '出生', 'description': '在一个普通的农村家庭出生'},
            {
              'year': '1975',
              'event': '结婚',
              'description': '与母亲结为夫妻，开始了美好的家庭生活',
            },
            {'year': '1980', 'event': '第一个孩子出生', 'description': '家庭迎来了第一个孩子'},
            {'year': '2020', 'event': '安详离世', 'description': '在家人的陪伴下安详离世'},
          ],
        };
        _isLoading = false;
      });
    } on Exception catch (e) {
      setState(() {
        _errorMessage = '加载纪念空间详情失败：$e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) => CupertinoPageScaffold(
    backgroundColor: kBackgroundColor,
    navigationBar: CupertinoNavigationBar(
      middle: Text((_memorialData?['name'] as String?) ?? '纪念空间详情'),
      trailing: _memorialData != null
          ? CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: _showMoreOptions,
              child: const Icon(CupertinoIcons.ellipsis),
            )
          : null,
    ),
    child: _buildBody(),
  );

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CupertinoActivityIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _errorMessage!,
              style: const TextStyle(color: CupertinoColors.systemRed),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            CupertinoButton(
              onPressed: _loadMemorialData,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_memorialData == null) {
      return const Center(child: Text('未找到纪念空间数据'));
    }

    return SingleChildScrollView(
      child: Column(
        children: [
          // 头部信息
          _buildHeader(),

          // 生平简介
          _buildBiography(),

          // 人生轨迹
          _buildTimeline(),

          // 照片回忆
          _buildPhotos(),

          // 互动区域
          _buildInteractionSection(),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildHeader() => Container(
    width: double.infinity,
    padding: const EdgeInsets.all(24),
    decoration: const BoxDecoration(color: kCardBackgroundColor),
    child: Column(
      children: [
        // 头像
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: CupertinoColors.systemGrey5,
            border: Border.all(color: CupertinoColors.systemGrey4, width: 2),
          ),
          child: const Icon(
            CupertinoIcons.person_fill,
            size: 60,
            color: CupertinoColors.systemGrey,
          ),
        ),
        const SizedBox(height: 16),

        // 姓名
        Text(
          _memorialData!['name'] as String,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: kTextColor,
          ),
        ),
        const SizedBox(height: 8),

        // 关系和日期
        Text(
          _memorialData!['type'] as String,
          style: const TextStyle(fontSize: 16, color: kSecondaryTextColor),
        ),
        const SizedBox(height: 4),
        Text(
          '${_memorialData!['birthDate']} - ${_memorialData!['deathDate']}',
          style: const TextStyle(fontSize: 14, color: kSecondaryTextColor),
        ),
        const SizedBox(height: 8),

        // 访问量
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              CupertinoIcons.eye,
              size: 16,
              color: kSecondaryTextColor,
            ),
            const SizedBox(width: 4),
            Text(
              '${_memorialData!['visitCount']} 次访问',
              style: const TextStyle(fontSize: 14, color: kSecondaryTextColor),
            ),
          ],
        ),
      ],
    ),
  );

  Widget _buildBiography() => Container(
    width: double.infinity,
    margin: const EdgeInsets.all(16),
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: kCardBackgroundColor,
      borderRadius: BorderRadius.circular(12),
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '生平简介',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: kTextColor,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          _memorialData!['description'] as String,
          style: const TextStyle(fontSize: 16, color: kTextColor, height: 1.5),
        ),
      ],
    ),
  );

  Widget _buildTimeline() {
    final timeline = _memorialData!['timeline'] as List;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: kCardBackgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '人生轨迹',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: kTextColor,
            ),
          ),
          const SizedBox(height: 16),
          ...timeline.map((e) => _buildTimelineItem(e as Map<String, dynamic>)),
        ],
      ),
    );
  }

  Widget _buildTimelineItem(Map<String, dynamic> event) => Container(
    margin: const EdgeInsets.only(bottom: 16),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 年份标签
        Container(
          width: 60,
          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          decoration: BoxDecoration(
            color: AppColors.primary,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            event['year'] as String,
            style: const TextStyle(
              color: CupertinoColors.white,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(width: 12),

        // 事件内容
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                event['event'] as String,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: kTextColor,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                event['description'] as String,
                style: const TextStyle(
                  fontSize: 14,
                  color: kSecondaryTextColor,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );

  Widget _buildPhotos() {
    final photos = _memorialData!['photos'] as List;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: kCardBackgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '照片回忆',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: kTextColor,
            ),
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: photos.length,
            itemBuilder: (context, index) {
              final photo = photos[index];
              return GestureDetector(
                onTap: () => _showPhotoDialog(photo as Map<String, dynamic>),
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemGrey5,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    CupertinoIcons.photo,
                    size: 30,
                    color: CupertinoColors.systemGrey,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInteractionSection() => Container(
    width: double.infinity,
    margin: const EdgeInsets.symmetric(horizontal: 16),
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: kCardBackgroundColor,
      borderRadius: BorderRadius.circular(12),
    ),
    child: Column(
      children: [
        const Text(
          '表达思念',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: kTextColor,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildActionButton(
              icon: CupertinoIcons.heart,
              label: '献花',
              onTap: () => _showComingSoon('献花功能'),
            ),
            _buildActionButton(
              icon: CupertinoIcons.camera,
              label: '上传照片',
              onTap: () => _showComingSoon('上传照片功能'),
            ),
            _buildActionButton(
              icon: CupertinoIcons.music_note,
              label: '音乐祭奠',
              onTap: () => _showComingSoon('音乐祭奠功能'),
            ),
            _buildActionButton(
              icon: CupertinoIcons.chat_bubble,
              label: '留言',
              onTap: () => _showComingSoon('留言功能'),
            ),
          ],
        ),
      ],
    ),
  );

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) => GestureDetector(
    onTap: onTap,
    child: Column(
      children: [
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: AppColors.primary, size: 24),
        ),
        const SizedBox(height: 8),
        Text(label, style: const TextStyle(fontSize: 12, color: kTextColor)),
      ],
    ),
  );

  void _showPhotoDialog(Map<String, dynamic> photo) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text((photo['caption'] as String?) ?? '查看照片'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 16),
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                color: CupertinoColors.systemGrey5,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                CupertinoIcons.photo,
                size: 50,
                color: CupertinoColors.systemGrey,
              ),
            ),
            if (photo['caption'] != null) ...[
              const SizedBox(height: 8),
              Text(photo['caption'] as String, style: const TextStyle(fontSize: 14)),
            ],
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: const Text('关闭'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  void _showMoreOptions() {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: const Text('更多操作'),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoon('编辑功能');
            },
            child: const Text('编辑纪念空间'),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoon('分享功能');
            },
            child: const Text('分享'),
          ),
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoon('收藏功能');
            },
            child: const Text('收藏'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
      ),
    );
  }

  void _showComingSoon(String feature) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('即将推出'),
        content: Text('$feature即将推出，敬请期待！'),
        actions: [
          CupertinoDialogAction(
            child: const Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}
