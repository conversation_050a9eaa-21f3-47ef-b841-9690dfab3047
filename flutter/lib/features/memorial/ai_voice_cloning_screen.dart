import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import '../../core/providers/ai_provider.dart';
import '../../core/services/ai_service.dart';

class AiVoiceCloningScreen extends StatefulWidget {
  const AiVoiceCloningScreen({super.key});

  @override
  State<AiVoiceCloningScreen> createState() => _AiVoiceCloningScreenState();
}

class _AiVoiceCloningScreenState extends State<AiVoiceCloningScreen> {
  final TextEditingController _textController = TextEditingController();
  File? _selectedVoiceFile;
  String _selectedLanguage = 'zh';
  bool _enhanceQuality = true;
  double _speed = 1.0;
  double _pitchShift = 0.0;
  bool _isProcessing = false;
  VoiceValidationResult? _validationResult;

  final Map<String, String> _languages = {
    'zh': '中文',
    'en': 'English',
    'ja': '日本語',
    'ko': '한국어',
    'es': 'Español',
    'fr': 'Français',
  };

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: const Text('AI 语音克隆'),
        trailing: _canProcess()
            ? CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: _isProcessing ? null : _processVoice,
                child: _isProcessing
                    ? const CupertinoActivityIndicator()
                    : const Text('生成'),
              )
            : null,
      ),
      child: SafeArea(
        child: CustomScrollView(
          slivers: [
            // 语音文件选择
            SliverToBoxAdapter(
              child: _buildVoiceFileSelector(),
            ),
            
            // 语音验证结果
            if (_validationResult != null)
              SliverToBoxAdapter(
                child: _buildValidationResult(),
              ),
            
            // 文本输入
            SliverToBoxAdapter(
              child: _buildTextInput(),
            ),
            
            // 语言选择
            SliverToBoxAdapter(
              child: _buildLanguageSelector(),
            ),
            
            // 高级设置
            SliverToBoxAdapter(
              child: _buildAdvancedSettings(),
            ),
            
            // 使用说明
            SliverToBoxAdapter(
              child: _buildUsageInfo(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVoiceFileSelector() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '选择语音样本',
            style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 12),
          if (_selectedVoiceFile == null)
            GestureDetector(
              onTap: _pickVoiceFile,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: CupertinoColors.systemGrey4,
                    style: BorderStyle.solid,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Column(
                  children: [
                    Icon(
                      CupertinoIcons.mic_circle,
                      size: 48,
                      color: CupertinoColors.systemBlue,
                    ),
                    SizedBox(height: 12),
                    Text(
                      '点击选择音频文件',
                      style: TextStyle(
                        fontSize: 16,
                        color: CupertinoColors.systemBlue,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '支持 WAV、MP3、M4A 格式\n建议时长 10-30 秒',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14,
                        color: CupertinoColors.secondaryLabel,
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: CupertinoColors.systemGrey6,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  const Icon(
                    CupertinoIcons.music_note,
                    color: CupertinoColors.systemBlue,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _selectedVoiceFile!.path.split('/').last,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '${(_getFileSize() / 1024 / 1024).toStringAsFixed(2)} MB',
                          style: const TextStyle(
                            color: CupertinoColors.secondaryLabel,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: _validateVoiceFile,
                    child: const Text('验证'),
                  ),
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      setState(() {
                        _selectedVoiceFile = null;
                        _validationResult = null;
                      });
                    },
                    child: const Icon(CupertinoIcons.xmark),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildValidationResult() {
    final validation = _validationResult!;
    final isValid = validation.isValid;
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isValid 
          ? CupertinoColors.systemGreen.withOpacity(0.1)
          : CupertinoColors.systemRed.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isValid 
            ? CupertinoColors.systemGreen
            : CupertinoColors.systemRed,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isValid 
                  ? CupertinoIcons.checkmark_circle_fill
                  : CupertinoIcons.exclamationmark_triangle_fill,
                color: isValid 
                  ? CupertinoColors.systemGreen
                  : CupertinoColors.systemRed,
              ),
              const SizedBox(width: 8),
              Text(
                isValid ? '语音样本验证通过' : '语音样本需要改进',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isValid 
                    ? CupertinoColors.systemGreen
                    : CupertinoColors.systemRed,
                ),
              ),
            ],
          ),
          if (validation.duration != null) ...[
            const SizedBox(height: 8),
            Text('时长: ${validation.duration!.toStringAsFixed(1)} 秒'),
          ],
          if (validation.warnings.isNotEmpty) ...[
            const SizedBox(height: 8),
            ...validation.warnings.map((warning) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                '⚠️ $warning',
                style: const TextStyle(
                  color: CupertinoColors.systemOrange,
                  fontSize: 14,
                ),
              ),
            )),
          ],
          if (validation.recommendations.isNotEmpty) ...[
            const SizedBox(height: 8),
            ...validation.recommendations.map((rec) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                '💡 $rec',
                style: const TextStyle(
                  color: CupertinoColors.systemBlue,
                  fontSize: 14,
                ),
              ),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildTextInput() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '输入要生成的文本',
            style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 12),
          CupertinoTextField(
            controller: _textController,
            placeholder: '请输入要转换为语音的文本...',
            maxLines: 5,
            maxLength: 1000,
            decoration: BoxDecoration(
              border: Border.all(color: CupertinoColors.systemGrey4),
              borderRadius: BorderRadius.circular(8),
            ),
            onChanged: (text) {
              setState(() {}); // 更新UI以反映字符计数
            },
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${_textController.text.length}/1000 字符',
                style: const TextStyle(
                  color: CupertinoColors.secondaryLabel,
                  fontSize: 14,
                ),
              ),
              CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: _detectLanguage,
                child: const Text('检测语言'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: CupertinoListSection.insetGrouped(
        header: const Text('语言设置'),
        children: [
          CupertinoListTile(
            title: const Text('目标语言'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(_languages[_selectedLanguage] ?? _selectedLanguage),
                const SizedBox(width: 8),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: _showLanguagePicker,
                  child: const Icon(CupertinoIcons.chevron_right),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedSettings() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: CupertinoListSection.insetGrouped(
        header: const Text('高级设置'),
        children: [
          CupertinoListTile(
            title: const Text('音质增强'),
            trailing: CupertinoSwitch(
              value: _enhanceQuality,
              onChanged: (value) {
                setState(() {
                  _enhanceQuality = value;
                });
              },
            ),
          ),
          CupertinoListTile(
            title: const Text('语速'),
            subtitle: Text('${_speed.toStringAsFixed(1)}x'),
            trailing: SizedBox(
              width: 150,
              child: CupertinoSlider(
                value: _speed,
                min: 0.5,
                max: 2.0,
                divisions: 15,
                onChanged: (value) {
                  setState(() {
                    _speed = value;
                  });
                },
              ),
            ),
          ),
          CupertinoListTile(
            title: const Text('音调'),
            subtitle: Text('${_pitchShift.toStringAsFixed(1)} 半音'),
            trailing: SizedBox(
              width: 150,
              child: CupertinoSlider(
                value: _pitchShift,
                min: -12.0,
                max: 12.0,
                divisions: 24,
                onChanged: (value) {
                  setState(() {
                    _pitchShift = value;
                  });
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsageInfo() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                CupertinoIcons.info_circle,
                color: CupertinoColors.systemBlue,
              ),
              const SizedBox(width: 8),
              Text(
                '使用说明',
                style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            '• 语音样本建议时长 10-30 秒\n'
            '• 样本音质越好，克隆效果越佳\n'
            '• 支持 6 种语言的语音克隆\n'
            '• 处理时间约 30-60 秒\n'
            '• 可调节语速和音调',
            style: TextStyle(
              color: CupertinoColors.secondaryLabel,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  bool _canProcess() {
    return _selectedVoiceFile != null && 
           _textController.text.trim().isNotEmpty &&
           !_isProcessing;
  }

  double _getFileSize() {
    return _selectedVoiceFile?.lengthSync().toDouble() ?? 0;
  }

  Future<void> _pickVoiceFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _selectedVoiceFile = File(result.files.first.path!);
          _validationResult = null;
        });
      }
    } catch (e) {
      _showErrorDialog('选择文件失败: $e');
    }
  }

  Future<void> _validateVoiceFile() async {
    if (_selectedVoiceFile == null) return;

    try {
      final aiProvider = context.read<AIProvider>();
      final result = await aiProvider.validateVoiceSample(
        voiceFile: _selectedVoiceFile!,
        language: _selectedLanguage,
      );

      setState(() {
        _validationResult = result;
      });
    } catch (e) {
      _showErrorDialog('验证失败: $e');
    }
  }

  Future<void> _detectLanguage() async {
    // TODO: 实现语言检测功能
    _showInfoDialog('语言检测功能开发中');
  }

  void _showLanguagePicker() {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (context) => Container(
        height: 200,
        color: CupertinoColors.systemBackground.resolveFrom(context),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CupertinoButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('取消'),
                  ),
                  CupertinoButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('确定'),
                  ),
                ],
              ),
            ),
            Expanded(
              child: CupertinoPicker(
                itemExtent: 32,
                onSelectedItemChanged: (index) {
                  final languages = _languages.keys.toList();
                  setState(() {
                    _selectedLanguage = languages[index];
                  });
                },
                children: _languages.values.map((name) => Text(name)).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _processVoice() async {
    if (!_canProcess()) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final aiProvider = context.read<AIProvider>();
      final taskId = await aiProvider.cloneVoice(
        voiceFile: _selectedVoiceFile!,
        text: _textController.text.trim(),
        language: _selectedLanguage,
        enhanceQuality: _enhanceQuality,
        speed: _speed,
        pitchShift: _pitchShift,
      );

      if (taskId != null) {
        _showSuccessDialog('语音克隆任务已创建，任务ID: $taskId');
      } else {
        _showErrorDialog(aiProvider.error ?? '创建语音克隆任务失败');
      }
    } catch (e) {
      _showErrorDialog('语音克隆失败: $e');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  void _showErrorDialog(String message) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('错误'),
        content: Text(message),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showSuccessDialog(String message) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('成功'),
        content: Text(message),
        actions: [
          CupertinoDialogAction(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // 返回到AI服务页面
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showInfoDialog(String message) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('提示'),
        content: Text(message),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
