import 'package:flutter/cupertino.dart';

class AiServicesScreen extends StatelessWidget {
  const AiServicesScreen({super.key});

  @override
  Widget build(BuildContext context) => CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('AI 服务'),
      ),
      child: ListView(
        children: <Widget>[
          CupertinoListTile(
            title: const Text('AI 照片修复'),
            leading: const Icon(CupertinoIcons.photo_on_rectangle),
            trailing: const CupertinoListTileChevron(),
            onTap: () {
              // TODO(feature): 导航到 AI 照片修复页面
              _showNotImplementedDialog(context, 'AI 照片修复');
            },
          ),
          CupertinoListTile(
            title: const Text('AI 声音克隆'),
            leading: const Icon(CupertinoIcons.speaker_slash_fill),
            trailing: const CupertinoListTileChevron(),
            onTap: () {
              // TODO(feature): 导航到 AI 声音克隆页面
              _showNotImplementedDialog(context, 'AI 声音克隆');
            },
          ),
          // 可以根据PRD添加更多AI服务入口
        ],
      ),
    );

  void _showNotImplementedDialog(BuildContext context, String featureName) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('功能开发中'),
        content: Text('$featureName 功能正在开发中，敬请期待。'),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            isDefaultAction: true,
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('好的'),
          ),
        ],
      ),
    );
  }
}

// 占位符 Widget，用于表示尚未实现的具体AI功能页面
class AiPhotoRepairScreen extends StatelessWidget {
  const AiPhotoRepairScreen({super.key});

  @override
  Widget build(BuildContext context) => const CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text('AI 照片修复'),
      ),
      child: Center(
        child: Text('AI 照片修复页面 - 待实现'),
      ),
    );
}

class AiVoiceCloningScreen extends StatelessWidget {
  const AiVoiceCloningScreen({super.key});

  @override
  Widget build(BuildContext context) => const CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text('AI 声音克隆'),
      ),
      child: Center(
        child: Text('AI 声音克隆页面 - 待实现'),
      ),
    );
}
