import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import '../../core/providers/ai_provider.dart';
import 'ai_photo_repair_screen.dart';
import 'ai_voice_cloning_screen.dart';
import 'ai_task_list_screen.dart';

class AiServicesScreen extends StatefulWidget {
  const AiServicesScreen({super.key});

  @override
  State<AiServicesScreen> createState() => _AiServicesScreenState();
}

class _AiServicesScreenState extends State<AiServicesScreen> {
  @override
  void initState() {
    super.initState();
    // 初始化AI服务
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AIProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('AI 服务'),
      ),
      child: Consumer<AIProvider>(
        builder: (context, aiProvider, child) {
          return CustomScrollView(
            slivers: [
              // 服务状态卡片
              SliverToBoxAdapter(
                child: _buildStatusCard(aiProvider),
              ),

              // AI服务列表
              SliverToBoxAdapter(
                child: _buildServicesList(context, aiProvider),
              ),

              // 任务管理
              SliverToBoxAdapter(
                child: _buildTasksSection(context, aiProvider),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStatusCard(AIProvider aiProvider) {
    final healthStatus = aiProvider.healthStatus;
    final isHealthy = healthStatus?.isHealthy ?? false;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isHealthy
            ? CupertinoColors.systemGreen
            : CupertinoColors.systemRed,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isHealthy
                  ? CupertinoIcons.checkmark_circle_fill
                  : CupertinoIcons.exclamationmark_triangle_fill,
                color: isHealthy
                  ? CupertinoColors.systemGreen
                  : CupertinoColors.systemRed,
              ),
              const SizedBox(width: 8),
              Text(
                'AI服务状态',
                style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (aiProvider.isLoading)
                const CupertinoActivityIndicator(),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            isHealthy ? '所有服务运行正常' : '部分服务不可用',
            style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
              color: CupertinoColors.secondaryLabel,
            ),
          ),
          if (aiProvider.error != null) ...[
            const SizedBox(height: 8),
            Text(
              aiProvider.error!,
              style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
                color: CupertinoColors.systemRed,
                fontSize: 14,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildServicesList(BuildContext context, AIProvider aiProvider) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Text(
              'AI 功能',
              style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
          ),
          CupertinoListSection.insetGrouped(
            children: [
              CupertinoListTile(
                title: const Text('AI 照片修复'),
                subtitle: const Text('老照片修复、增强、上色'),
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemBlue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    CupertinoIcons.photo_on_rectangle,
                    color: CupertinoColors.systemBlue,
                  ),
                ),
                trailing: const CupertinoListTileChevron(),
                onTap: () {
                  Navigator.of(context).push(
                    CupertinoPageRoute(
                      builder: (context) => const AiPhotoRepairScreen(),
                    ),
                  );
                },
              ),
              CupertinoListTile(
                title: const Text('AI 声音克隆'),
                subtitle: const Text('多语言语音克隆和音质增强'),
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: CupertinoColors.systemPurple.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    CupertinoIcons.speaker_3_fill,
                    color: CupertinoColors.systemPurple,
                  ),
                ),
                trailing: const CupertinoListTileChevron(),
                onTap: () {
                  Navigator.of(context).push(
                    CupertinoPageRoute(
                      builder: (context) => const AiVoiceCloningScreen(),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTasksSection(BuildContext context, AIProvider aiProvider) {
    final activeTasks = aiProvider.activeTasks;
    final completedTasks = aiProvider.completedTasks;

    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '任务管理',
                style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  Navigator.of(context).push(
                    CupertinoPageRoute(
                      builder: (context) => const AiTaskListScreen(),
                    ),
                  );
                },
                child: const Text('查看全部'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          CupertinoListSection.insetGrouped(
            children: [
              CupertinoListTile(
                title: const Text('正在进行'),
                subtitle: Text('${activeTasks.length} 个任务'),
                leading: const Icon(
                  CupertinoIcons.clock,
                  color: CupertinoColors.systemOrange,
                ),
                trailing: const CupertinoListTileChevron(),
                onTap: () {
                  Navigator.of(context).push(
                    CupertinoPageRoute(
                      builder: (context) => const AiTaskListScreen(
                        initialFilter: 'processing',
                      ),
                    ),
                  );
                },
              ),
              CupertinoListTile(
                title: const Text('已完成'),
                subtitle: Text('${completedTasks.length} 个任务'),
                leading: const Icon(
                  CupertinoIcons.checkmark_circle,
                  color: CupertinoColors.systemGreen,
                ),
                trailing: const CupertinoListTileChevron(),
                onTap: () {
                  Navigator.of(context).push(
                    CupertinoPageRoute(
                      builder: (context) => const AiTaskListScreen(
                        initialFilter: 'completed',
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

}
