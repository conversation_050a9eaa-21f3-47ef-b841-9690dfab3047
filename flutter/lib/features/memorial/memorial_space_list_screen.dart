import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:memorial/core/theme/app_colors.dart';
import 'package:memorial/features/memorial/memorial_detail_screen.dart';

/// 纪念空间列表页面 - 完全按照移动端UI原型实现
class MemorialSpaceListScreen extends ConsumerWidget {
  const MemorialSpaceListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) => CupertinoPageScaffold(
    backgroundColor: AppColors.background, // 使用系统背景色
    navigationBar: CupertinoNavigationBar(
      backgroundColor: CupertinoColors.systemBackground,
      border: const Border(
        bottom: BorderSide(color: CupertinoColors.separator, width: 0.5),
      ),
      middle: const Text(
        '纪念空间',
        style: TextStyle(
          fontSize: 17,
          fontWeight: FontWeight.w600,
          color: CupertinoColors.label,
        ),
      ),
      trailing: CupertinoButton(
        padding: EdgeInsets.zero,
        onPressed: () => _showSearchDialog(context),
        child: const Icon(
          CupertinoIcons.search,
          color: CupertinoColors.activeBlue,
          size: 22,
        ),
      ),
    ),
    child: SafeArea(
      child: CustomScrollView(
        slivers: [
          // 分类筛选栏
          SliverToBoxAdapter(child: _buildCategoryFilter()),

          // 纪念空间网格
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverGrid(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                childAspectRatio: 0.8,
              ),
              delegate: SliverChildBuilderDelegate(
                _buildMemorialCard,
                childCount: 8, // 示例数据
              ),
            ),
          ),
        ],
      ),
    ),
  );

  /// 显示搜索对话框
  void _showSearchDialog(BuildContext context) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('搜索纪念空间'),
        content: const Column(
          children: [
            SizedBox(height: 16),
            CupertinoTextField(
              placeholder: '请输入姓名或关键词',
              decoration: BoxDecoration(
                color: CupertinoColors.systemGrey6,
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
            ),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: const Text('取消'),
            onPressed: () => Navigator.of(context).pop(),
          ),
          CupertinoDialogAction(
            child: const Text('搜索'),
            onPressed: () {
              Navigator.of(context).pop();
              _showComingSoon(context, '搜索功能');
            },
          ),
        ],
      ),
    );
  }

  /// 显示即将推出的功能提示
  void _showComingSoon(BuildContext context, String feature) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text(feature),
        content: const Text('该功能即将推出，敬请期待！'),
        actions: [
          CupertinoDialogAction(
            child: const Text('确定'),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  /// 构建分类筛选栏
  Widget _buildCategoryFilter() => Container(
    height: 60,
    padding: const EdgeInsets.symmetric(horizontal: 16),
    decoration: const BoxDecoration(
      color: CupertinoColors.systemBackground,
      border: Border(
        bottom: BorderSide(color: CupertinoColors.separator, width: 0.5),
      ),
    ),
    child: Row(
      children: [
        Expanded(
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildCategoryChip('全部', true),
                const SizedBox(width: 8),
                _buildCategoryChip('家人', false),
                const SizedBox(width: 8),
                _buildCategoryChip('朋友', false),
                const SizedBox(width: 8),
                _buildCategoryChip('宠物', false),
                const SizedBox(width: 8),
                _buildCategoryChip('公共', false),
              ],
            ),
          ),
        ),
      ],
    ),
  );

  /// 构建分类筛选标签
  Widget _buildCategoryChip(String title, bool isSelected) => Container(
    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    decoration: BoxDecoration(
      color: isSelected ? AppColors.primary : CupertinoColors.systemGrey6, // 使用主题色
      borderRadius: BorderRadius.circular(20),
    ),
    child: Text(
      title,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: isSelected ? CupertinoColors.white : CupertinoColors.label,
      ),
    ),
  );

  /// 构建纪念空间卡片
  Widget _buildMemorialCard(BuildContext context, int index) {
    final memorialData = <Map<String, dynamic>>[
      {
        'name': '爷爷的回忆',
        'image':
            'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
        'date': '1935-2020',
        'type': '家人',
        'visitors': 128,
      },
      {
        'name': '小白的快乐时光',
        'image':
            'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
        'date': '2018-2023',
        'type': '宠物',
        'visitors': 89,
      },
      {
        'name': '奶奶的温暖',
        'image':
            'https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
        'date': '1940-2021',
        'type': '家人',
        'visitors': 256,
      },
      {
        'name': '好友李明',
        'image':
            'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
        'date': '1990-2022',
        'type': '朋友',
        'visitors': 67,
      },
      {
        'name': '妈妈的爱',
        'image':
            'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
        'date': '1965-2023',
        'type': '家人',
        'visitors': 342,
      },
      {
        'name': '橘猫咪咪',
        'image':
            'https://images.unsplash.com/photo-1574158622682-e40e69881006?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
        'date': '2019-2023',
        'type': '宠物',
        'visitors': 45,
      },
      {
        'name': '同事张华',
        'image':
            'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
        'date': '1985-2022',
        'type': '朋友',
        'visitors': 78,
      },
      {
        'name': '爸爸的故事',
        'image':
            'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80',
        'date': '1960-2021',
        'type': '家人',
        'visitors': 198,
      },
    ];

    final memorial = memorialData[index % memorialData.length];

    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          CupertinoPageRoute<void>(
            builder: (context) => MemorialDetailScreen(
              memorialId: index.toString(),
            ),
          ),
        );
      },
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: CupertinoColors.secondarySystemGroupedBackground,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: CupertinoColors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头像图片
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                  image: DecorationImage(
                    image: NetworkImage(memorial['image'] as String),
                    fit: BoxFit.cover,
                  ),
                ),
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        CupertinoColors.black.withValues(alpha: 0),
                        CupertinoColors.black.withValues(alpha: 0.3),
                      ],
                    ),
                  ),
                  child: Stack(
                    children: [
                      // 类型标签
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: _getTypeColor(memorial['type'] as String),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            memorial['type'] as String,
                            style: const TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                              color: CupertinoColors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // 信息区域
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 姓名
                    Text(
                      memorial['name'] as String,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: CupertinoColors.label,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),

                    // 日期
                    Text(
                      memorial['date'] as String,
                      style: const TextStyle(
                        fontSize: 12,
                        color: CupertinoColors.secondaryLabel,
                      ),
                    ),
                    const Spacer(),

                    // 访问量
                    Row(
                      children: [
                        const Icon(
                          CupertinoIcons.eye,
                          size: 12,
                          color: CupertinoColors.systemGrey,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${memorial['visitors']} 次访问',
                          style: const TextStyle(
                            fontSize: 10,
                            color: CupertinoColors.systemGrey,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 获取类型对应的颜色
  Color _getTypeColor(String type) {
    switch (type) {
      case '家人':
        return AppColors.primary; // 高级蓝
      case '朋友':
        return AppColors.warning; // 高级橙
      case '宠物':
        return AppColors.success; // 系统绿色
      case '公共':
        return AppColors.warning; // 高级橙
      default:
        return CupertinoColors.systemGrey;
    }
  }
}
