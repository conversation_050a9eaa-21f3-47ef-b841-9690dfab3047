import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import '../../core/providers/ai_provider.dart';
import '../../core/services/ai_service.dart';

class AiTaskListScreen extends StatefulWidget {
  final String? initialFilter;
  
  const AiTaskListScreen({
    super.key,
    this.initialFilter,
  });

  @override
  State<AiTaskListScreen> createState() => _AiTaskListScreenState();
}

class _AiTaskListScreenState extends State<AiTaskListScreen> {
  String? _selectedFilter;
  
  final Map<String, String> _filterOptions = {
    'all': '全部',
    'pending': '等待中',
    'processing': '处理中',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消',
  };

  @override
  void initState() {
    super.initState();
    _selectedFilter = widget.initialFilter ?? 'all';
    
    // 加载任务列表
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTasks();
    });
  }

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: const Text('AI 任务'),
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: _loadTasks,
          child: const Icon(CupertinoIcons.refresh),
        ),
      ),
      child: Consumer<AIProvider>(
        builder: (context, aiProvider, child) {
          return CustomScrollView(
            slivers: [
              // 过滤器
              SliverToBoxAdapter(
                child: _buildFilterSelector(),
              ),
              
              // 任务统计
              SliverToBoxAdapter(
                child: _buildTaskStats(aiProvider),
              ),
              
              // 任务列表
              _buildTaskList(aiProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildFilterSelector() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: CupertinoSlidingSegmentedControl<String>(
        groupValue: _selectedFilter,
        children: {
          'all': const Text('全部'),
          'processing': const Text('进行中'),
          'completed': const Text('已完成'),
          'failed': const Text('失败'),
        },
        onValueChanged: (value) {
          setState(() {
            _selectedFilter = value;
          });
          _loadTasks();
        },
      ),
    );
  }

  Widget _buildTaskStats(AIProvider aiProvider) {
    final allTasks = aiProvider.tasks;
    final activeTasks = aiProvider.activeTasks;
    final completedTasks = aiProvider.completedTasks;
    final failedTasks = aiProvider.failedTasks;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              '总计',
              allTasks.length.toString(),
              CupertinoColors.systemBlue,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              '进行中',
              activeTasks.length.toString(),
              CupertinoColors.systemOrange,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              '已完成',
              completedTasks.length.toString(),
              CupertinoColors.systemGreen,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: _buildStatCard(
              '失败',
              failedTasks.length.toString(),
              CupertinoColors.systemRed,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskList(AIProvider aiProvider) {
    final filteredTasks = _getFilteredTasks(aiProvider);

    if (aiProvider.isLoading) {
      return const SliverFillRemaining(
        child: Center(
          child: CupertinoActivityIndicator(),
        ),
      );
    }

    if (filteredTasks.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                CupertinoIcons.doc_text,
                size: 64,
                color: CupertinoColors.systemGrey,
              ),
              const SizedBox(height: 16),
              Text(
                '暂无任务',
                style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
                  color: CupertinoColors.systemGrey,
                  fontSize: 18,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final task = filteredTasks[index];
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
            child: _buildTaskCard(task, aiProvider),
          );
        },
        childCount: filteredTasks.length,
      ),
    );
  }

  Widget _buildTaskCard(AITaskStatus task, AIProvider aiProvider) {
    final taskType = AITaskType.fromValue(task.taskType);
    final statusType = AITaskStatusType.fromValue(task.status);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: CupertinoColors.systemGrey5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 任务头部
          Row(
            children: [
              _buildTaskIcon(taskType),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      taskType?.displayName ?? task.taskType,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      'ID: ${task.taskId.substring(0, 8)}...',
                      style: const TextStyle(
                        color: CupertinoColors.secondaryLabel,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              _buildStatusBadge(statusType),
            ],
          ),
          
          // 进度条（仅处理中的任务显示）
          if (task.isProcessing) ...[
            const SizedBox(height: 12),
            _buildProgressBar(task.progress),
          ],
          
          // 任务详情
          const SizedBox(height: 12),
          _buildTaskDetails(task),
          
          // 操作按钮
          if (task.isProcessing || task.isPending) ...[
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CupertinoButton(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  color: CupertinoColors.systemRed,
                  onPressed: () => _cancelTask(task.taskId, aiProvider),
                  child: const Text('取消'),
                ),
              ],
            ),
          ],
          
          // 错误信息
          if (task.isFailed && task.error != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: CupertinoColors.systemRed.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(
                    CupertinoIcons.exclamationmark_triangle,
                    color: CupertinoColors.systemRed,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      task.error!,
                      style: const TextStyle(
                        color: CupertinoColors.systemRed,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTaskIcon(AITaskType? taskType) {
    IconData icon;
    Color color;
    
    switch (taskType) {
      case AITaskType.photoRestore:
      case AITaskType.photoColorize:
      case AITaskType.photoEnhance:
      case AITaskType.photoRemoveBg:
        icon = CupertinoIcons.photo;
        color = CupertinoColors.systemBlue;
        break;
      case AITaskType.voiceClone:
        icon = CupertinoIcons.speaker_3;
        color = CupertinoColors.systemPurple;
        break;
      case AITaskType.batchProcess:
        icon = CupertinoIcons.square_stack_3d_down_right;
        color = CupertinoColors.systemOrange;
        break;
      default:
        icon = CupertinoIcons.gear;
        color = CupertinoColors.systemGrey;
    }
    
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(icon, color: color, size: 20),
    );
  }

  Widget _buildStatusBadge(AITaskStatusType? statusType) {
    Color color;
    String text;
    
    switch (statusType) {
      case AITaskStatusType.pending:
        color = CupertinoColors.systemGrey;
        text = '等待中';
        break;
      case AITaskStatusType.processing:
        color = CupertinoColors.systemOrange;
        text = '处理中';
        break;
      case AITaskStatusType.completed:
        color = CupertinoColors.systemGreen;
        text = '已完成';
        break;
      case AITaskStatusType.failed:
        color = CupertinoColors.systemRed;
        text = '失败';
        break;
      case AITaskStatusType.cancelled:
        color = CupertinoColors.systemGrey;
        text = '已取消';
        break;
      default:
        color = CupertinoColors.systemGrey;
        text = '未知';
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildProgressBar(AITaskProgress progress) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              progress.stepDescription,
              style: const TextStyle(
                fontSize: 14,
                color: CupertinoColors.secondaryLabel,
              ),
            ),
            Text(
              '${progress.percentage.toInt()}%',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          height: 4,
          decoration: BoxDecoration(
            color: CupertinoColors.systemGrey5,
            borderRadius: BorderRadius.circular(2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progress.percentage / 100,
            child: Container(
              decoration: BoxDecoration(
                color: CupertinoColors.systemBlue,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
        ),
        if (progress.estimatedTimeRemaining != null) ...[
          const SizedBox(height: 4),
          Text(
            '预计剩余时间: ${progress.estimatedTimeRemaining}秒',
            style: const TextStyle(
              fontSize: 12,
              color: CupertinoColors.secondaryLabel,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTaskDetails(AITaskStatus task) {
    final details = <String>[];
    
    if (task.createdAt != null) {
      details.add('创建时间: ${_formatDateTime(task.createdAt!)}');
    }
    
    if (task.startedAt != null) {
      details.add('开始时间: ${_formatDateTime(task.startedAt!)}');
    }
    
    if (task.completedAt != null) {
      details.add('完成时间: ${_formatDateTime(task.completedAt!)}');
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: details.map((detail) => Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: Text(
          detail,
          style: const TextStyle(
            fontSize: 12,
            color: CupertinoColors.secondaryLabel,
          ),
        ),
      )).toList(),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.month}/${dateTime.day} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  List<AITaskStatus> _getFilteredTasks(AIProvider aiProvider) {
    final allTasks = aiProvider.tasks;
    
    if (_selectedFilter == null || _selectedFilter == 'all') {
      return allTasks;
    }
    
    return allTasks.where((task) => task.status == _selectedFilter).toList();
  }

  Future<void> _loadTasks() async {
    final aiProvider = context.read<AIProvider>();
    await aiProvider.loadUserTasks(
      status: _selectedFilter == 'all' ? null : _selectedFilter,
    );
  }

  Future<void> _cancelTask(String taskId, AIProvider aiProvider) async {
    final success = await aiProvider.cancelTask(taskId);
    
    if (success) {
      _showSuccessDialog('任务已取消');
    } else {
      _showErrorDialog(aiProvider.error ?? '取消任务失败');
    }
  }

  void _showErrorDialog(String message) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('错误'),
        content: Text(message),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showSuccessDialog(String message) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('成功'),
        content: Text(message),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
