import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:memorial/core/theme/app_colors.dart';
import 'package:memorial/features/memorial/memorial_space_edit_screen.dart';
import 'package:memorial/providers/app_state_providers.dart';

// 临时数据模型定义
class MemorialSpaceUpdateData {
  MemorialSpaceUpdateData({
    required this.deceasedName,
    required this.relationship,
    required this.birthDate,
    required this.deathDate,
    required this.bio,
    required this.privacyLevel,
  });

  final String deceasedName;
  final String relationship;
  final DateTime birthDate;
  final DateTime deathDate;
  final String bio;
  final String privacyLevel;
}

class TimelineEventData {
  TimelineEventData({
    required this.year,
    required this.title,
    required this.description,
  });

  final String year;
  final String title;
  final String description;
}

class MediaAssetData {
  MediaAssetData({
    required this.id,
    required this.url,
    required this.caption,
    required this.type,
  });

  final String id;
  final String url;
  final String caption;
  final String type;
}

class MemorialSpaceDetailScreen extends ConsumerStatefulWidget {
  const MemorialSpaceDetailScreen({
    required this.spaceId,
    super.key,
  });

  final String spaceId;

  @override
  ConsumerState<MemorialSpaceDetailScreen> createState() => _MemorialSpaceDetailScreenState();
}

class _MemorialSpaceDetailScreenState extends ConsumerState<MemorialSpaceDetailScreen> {
  // 使用 Riverpod 状态管理，移除本地状态变量
  MemorialSpaceUpdateData? _spaceDisplayData; // Basic info
  List<TimelineEventData> _timelineEvents = [];
  List<MediaAssetData> _lifePhotos = [];
  List<MediaAssetData> _videos = [];
  MediaAssetData? _coverImage;

  @override
  void initState() {
    super.initState();
    _fetchMemorialSpaceDetails();
  }

  Future<void> _fetchMemorialSpaceDetails() async {
    // 使用 Riverpod 更新加载状态
    ref.read(loadingStateProvider.notifier).state = true;
    ref.read(errorMessageProvider.notifier).state = null;
    
    try {
      // TODO(api): API call to fetch memorial space details using widget.spaceId.
      await Future<void>.delayed(const Duration(seconds: 1)); // Simulate network delay

      // Mock data based on spaceId, similar to what's in MemorialSpacePage.tsx
      if (widget.spaceId.isNotEmpty) {
        setState(() {
          _spaceDisplayData = MemorialSpaceUpdateData(
            deceasedName: '张三',
            relationship: '父亲',
            birthDate: DateTime(1950),
            deathDate: DateTime(2020, 12, 31),
            bio: '一位慈祥的父亲，勤劳一生，为家庭奉献了所有。他的智慧和爱将永远伴随我们。',
            privacyLevel: 'family',
          );
          _timelineEvents = [
            TimelineEventData(year: '1950', title: '出生', description: '在一个普通的农村家庭出生'),
            TimelineEventData(year: '1975', title: '结婚', description: '与母亲结为夫妻，开始了美好的家庭生活'),
            TimelineEventData(year: '1980', title: '第一个孩子出生', description: '家庭迎来了第一个孩子'),
            TimelineEventData(year: '2020', title: '安详离世', description: '在家人的陪伴下安详离世'),
          ];
          _lifePhotos = [
            MediaAssetData(id: '1', type: 'image', url: 'https://example.com/photo1.jpg', caption: '年轻时的照片'),
            MediaAssetData(id: '2', type: 'image', url: 'https://example.com/photo2.jpg', caption: '全家福'),
          ];
          _videos = [
            MediaAssetData(id: '3', type: 'video', url: 'https://example.com/video1.mp4', caption: '生日庆祝视频'),
          ];
          _coverImage = MediaAssetData(id: 'cover', type: 'image', url: 'https://example.com/cover.jpg', caption: '封面照片');
        });
      } else {
        ref.read(errorMessageProvider.notifier).state = '无效的纪念空间ID';
      }
    } on Exception catch (e) {
      ref.read(errorMessageProvider.notifier).state = '加载纪念空间详情失败：$e';
    } finally {
      ref.read(loadingStateProvider.notifier).state = false;
    }
  }

  void _navigateToEditScreen() {
    Navigator.of(context).push(
      CupertinoPageRoute<void>(
        builder: (context) => MemorialSpaceEditScreen(
          spaceId: widget.spaceId,
        ),
      ),
    ).then((_) {
      // Refresh data when returning from edit screen
      _fetchMemorialSpaceDetails();
    });
  }

  @override
  Widget build(BuildContext context) {
    // 监听 Riverpod 状态
    final isLoading = ref.watch(loadingStateProvider);
    final errorMessage = ref.watch(errorMessageProvider);
    
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: Text(_spaceDisplayData?.deceasedName ?? '纪念空间'),
        trailing: _spaceDisplayData != null
            ? CupertinoButton(
                onPressed: _navigateToEditScreen,
                child: const Icon(CupertinoIcons.pencil),
              )
            : null,
      ),
      child: _buildBody(isLoading, errorMessage),
    );
  }

  Widget _buildBody(bool isLoading, String? errorMessage) => isLoading
      ? const Center(child: CupertinoActivityIndicator())
      : errorMessage != null
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(errorMessage, style: const TextStyle(color: Colors.red)),
                  const SizedBox(height: 16),
                  CupertinoButton(
                    onPressed: _fetchMemorialSpaceDetails,
                    child: const Text('重试'),
                  ),
                ],
              ),
            )
          : _spaceDisplayData == null
              ? const Center(child: Text('未找到纪念空间数据'))
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      // Cover Image
                      if (_coverImage != null) _buildCoverImage(),
                      const SizedBox(height: 16),
                      
                      // Header Info
                      buildHeaderInfo(_spaceDisplayData!),
                      const SizedBox(height: 24),
                      
                      // Bio Section
                      if (_spaceDisplayData!.bio.isNotEmpty) ...[
                        const Text('生平简介', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.cardBackground,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(_spaceDisplayData!.bio, style: const TextStyle(fontSize: 16)),
                        ),
                        const SizedBox(height: 24),
                      ],
                      
                      // Timeline Section
                      const Text('人生轨迹', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      if (_timelineEvents.isEmpty)
                        _buildPlaceholderSection('暂无人生轨迹记录。')
                      else
                        Column(
                          children: _timelineEvents.map((event) => Container(
                            margin: const EdgeInsets.only(bottom: 12),
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: AppColors.cardBackground,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Container(
                                  width: 60,
                                  padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                                  decoration: BoxDecoration(
                                    color: AppColors.primary,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(event.year, style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold), textAlign: TextAlign.center),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    children: [
                                      Text(event.title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                                      const SizedBox(height: 4),
                                      Text(event.description, style: const TextStyle(color: AppColors.textSecondary)),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          )).toList(),
                        ),
                      const SizedBox(height: 24),
                      
                      // Life Photos Section
                      const Text('生活照片', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      _buildMediaGrid(_lifePhotos, 'image'),
                      const SizedBox(height: 24),
                      
                      // Videos Section
                      const Text('视频回忆', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      _buildMediaGrid(_videos, 'video'),
                    ],
                  ),
                );

  Widget _buildCoverImage() => Container(
    width: double.infinity,
    height: 200,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(12),
      image: _coverImage!.url.startsWith('http')
          ? DecorationImage(image: NetworkImage(_coverImage!.url), fit: BoxFit.cover)
          : null,
      color: _coverImage!.url.startsWith('http') ? null : AppColors.cardBackground,
    ),
    child: !_coverImage!.url.startsWith('http')
        ? const Center(child: Icon(CupertinoIcons.photo, size: 50, color: AppColors.textSecondary))
        : null,
  );

  Widget _buildMediaGrid(List<MediaAssetData> mediaAssets, String type) {
    if (mediaAssets.isEmpty) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Text('暂无${type == 'image' ? '照片' : '视频'}。', style: const TextStyle(color: AppColors.textSecondary)),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: mediaAssets.length,
      itemBuilder: (context, index) {
        final asset = mediaAssets[index];
        return GestureDetector(
          onTap: () => _showMediaDialog(asset),
          child: DecoratedBox(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: AppColors.cardBackground,
            ),
            child: asset.type == 'image' && asset.url.startsWith('http')
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      asset.url,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => const Center(
                        child: Icon(CupertinoIcons.photo, color: AppColors.textSecondary),
                      ),
                    ),
                  )
                : Center(
                    child: Icon(
                      asset.type == 'image' ? CupertinoIcons.photo : CupertinoIcons.play_circle,
                      size: 30,
                      color: AppColors.textSecondary,
                    ),
                  ),
          ),
        );
      },
    );
  }

  void _showMediaDialog(MediaAssetData asset) {
    showCupertinoDialog<void>(
      context: context,
      builder: (BuildContext context) => CupertinoAlertDialog(
          title: Text(asset.type == 'image' ? (asset.caption.isNotEmpty ? asset.caption : '查看图片') : (asset.caption.isNotEmpty ? asset.caption : '播放视频')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 16),
              if (asset.type == 'image' && asset.url.startsWith('http'))
                ConstrainedBox(
                  constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * 0.5),
                  child: Image.network(asset.url, fit: BoxFit.contain, errorBuilder: (c,e,s) => const Text('无法加载图片')),
                ),
              if (asset.type == 'video')
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    children: [
Icon(CupertinoIcons.play_circle, size: 50, color: AppColors.primary),
SizedBox(height: 8),
Text('点击播放视频', style: TextStyle(color: AppColors.textSecondary)),
                    ],
                  ),
                ),
              if (asset.caption.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(asset.caption, style: const TextStyle(fontSize: 14)),
              ]
            ],
          ),
          actions: [
            CupertinoDialogAction(
              child: const Text('关闭'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            if (asset.type == 'video')
              CupertinoDialogAction(
                child: const Text('播放'),
                onPressed: () {
                  Navigator.of(context).pop();
                  // TODO(video): Implement video playback functionality
                },
              ),
          ],
        ),
    );
  }

  Widget _buildPrivacyIcon(String privacyLevel) {
    IconData iconData;
    Color iconColor;
    String tooltip;

    switch (privacyLevel) {
      case 'public':
        iconData = CupertinoIcons.globe;
        iconColor = CupertinoColors.systemGreen;
        tooltip = '公开';
      case 'friends':
        iconData = CupertinoIcons.person_2;
        iconColor = const Color(0xFFF8B37F); // 高级橙
        tooltip = '好友可见';
      case 'family':
        iconData = CupertinoIcons.house;
        iconColor = AppColors.primary; // 使用主题色
        tooltip = '家人可见';
      case 'private':
      default:
        iconData = CupertinoIcons.lock;
        iconColor = CupertinoColors.systemRed;
        tooltip = '私密';
    }

    return Tooltip(
      message: tooltip,
      child: Icon(iconData, color: iconColor, size: 18),
    );
  }

  Widget _buildPlaceholderSection(String message) => Container(
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.cardBackground,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Text(
          message,
          style: const TextStyle(color: AppColors.textSecondary, fontSize: 16),
          textAlign: TextAlign.center,
        ),
      ),
    );

  Widget buildHeaderInfo(MemorialSpaceUpdateData data) => Column(
    children: [
      Text(data.deceasedName, style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
      const SizedBox(height: 8),
      Row(
        children: [
          Text('生于: ${data.birthDate.year}年', style: const TextStyle(color: AppColors.textSecondary, fontSize: 15)),
          const Text(' - ', style: TextStyle(color: AppColors.textSecondary, fontSize: 15)),
          Text('卒于: ${data.deathDate.year}年', style: const TextStyle(color: AppColors.textSecondary, fontSize: 15)),
        ],
      ),
      const SizedBox(height: 4),
      Row(
        children: [
          Text('与我关系: ${data.relationship}', style: const TextStyle(color: AppColors.textSecondary, fontSize: 15)),
          const SizedBox(width: 8),
          _buildPrivacyIcon(data.privacyLevel),
        ],
      ),
    ],
  );
}
