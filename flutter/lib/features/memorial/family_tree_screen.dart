import 'package:flutter/cupertino.dart';

// TODO(theme): Define or import colors and styles from a central theme/design spec
const Color kBackgroundColor = CupertinoColors.systemGroupedBackground;

class FamilyTreeScreen extends StatelessWidget {
  const FamilyTreeScreen({super.key});

  @override
  Widget build(BuildContext context) => CupertinoPageScaffold(
      backgroundColor: kBackgroundColor,
      navigationBar: const CupertinoNavigationBar(
        middle: Text('家庭树'),
        // TODO(ui): Add appropriate leading/trailing widgets if needed
      ),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text(
            '家庭树功能正在开发中...',
            style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
                  color: CupertinoColors.secondaryLabel,
                ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
}
