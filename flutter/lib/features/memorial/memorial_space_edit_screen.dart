import 'package:flutter/cupertino.dart';
import 'package:memorial/core/theme/app_colors.dart';
// import 'package:image_picker/image_picker.dart'; // For image selection

// Design System (Consider moving to a separate file/theme)
// 移除硬编码颜色，使用AppColors.primary
const Color kTextColor = CupertinoColors.label;
const Color kSecondaryTextColor = CupertinoColors.secondaryLabel;
const Color kBackgroundColor = CupertinoColors.systemGroupedBackground;
const Color kCardBackgroundColor = CupertinoColors.secondarySystemGroupedBackground;
const Color kErrorColor = CupertinoColors.systemRed;

// Data Models (Consider moving to separate files)
class MemorialSpaceUpdateData {
  MemorialSpaceUpdateData({
    required this.deceasedName,
    required this.relationship,
    required this.bio,
    required this.privacyLevel,
    this.birthDate,
    this.deathDate,
    this.accessPassword,
  });

  String deceasedName;
  String relationship;
  String bio;
  String privacyLevel;
  DateTime? birthDate;
  DateTime? deathDate;
  String? accessPassword;
  // TODO(user): Add fields for cover_image_url, scene_id, music_url if they are directly editable strings.
  // For file uploads, they will be handled separately.
}

class TimelineEventData {
  TimelineEventData({
    required this.year,
    required this.title,
    required this.description,
    this.id,
    this.isNew = false,
  });

  String? id;
  String year;
  String title;
  String description;
  bool isNew; // To track if it's a new event not yet saved
}

class MediaAssetData {
  MediaAssetData({
    required this.url,
    required this.type,
    this.id,
    this.caption,
    this.file,
    this.isNew = false,
  });

  String? id;
  String url; // For existing assets, or local path/preview for new ones
  String type; // 'image', 'video'
  String? caption;
  dynamic file; // For new uploads (e.g., XFile from image_picker)
  bool isNew; // To track if it's a new asset
}

class MemorialSpaceEditScreen extends StatefulWidget {
  const MemorialSpaceEditScreen({required this.spaceId, super.key});

  final String spaceId;

  @override
  State<MemorialSpaceEditScreen> createState() => _MemorialSpaceEditScreenState();
}

class _MemorialSpaceEditScreenState extends State<MemorialSpaceEditScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = true;
  String? _errorMessage;

  // Form data states
  late MemorialSpaceUpdateData _spaceData;
  List<TimelineEventData> _timelineEvents = [];
  List<MediaAssetData> _lifePhotos = [];
  List<MediaAssetData> _videos = [];
  MediaAssetData? _coverImage;

  // final ImagePicker _picker = ImagePicker(); // Uncomment if using image_picker

  @override
  void initState() {
    super.initState();
    _loadMemorialSpaceData();
  }

  Future<void> _loadMemorialSpaceData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // TODO(user): API call to fetch existing memorial space data for editing.
    await Future<void>.delayed(const Duration(seconds: 1)); // Simulate network delay

    if (widget.spaceId.isNotEmpty && widget.spaceId != 'new') {
      // Mock existing data for an existing space
      _spaceData = MemorialSpaceUpdateData(
        deceasedName: '逝者 ${widget.spaceId}',
        birthDate: DateTime.tryParse('1950-06-15'),
        deathDate: DateTime.tryParse('2020-01-01'),
        relationship: '父亲',
        bio: '这是逝者 ${widget.spaceId} 的生平简介 (编辑模式)。\n可以修改并保存。',
        privacyLevel: 'public',
      );
      _coverImage = MediaAssetData(
          id: 'cover1',
          url:
              'https://images.unsplash.com/photo-1506748686214-e9df14d4d9d0?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
          type: 'image');
      _timelineEvents = [
        TimelineEventData(id: 't1', year: '1950年', title: '出生 (编辑)', description: '描述...'),
        TimelineEventData(id: 't2', year: '1972年', title: '毕业 (编辑)', description: '描述...'),
      ];
      _lifePhotos = [
        MediaAssetData(id: 'p1', url: 'https://via.placeholder.com/150?text=Photo1', type: 'image'),
      ];
      _videos = [
        MediaAssetData(id: 'v1', url: 'placeholder_video.mp4', type: 'video', caption: '视频1'),
      ];
    } else {
      // Initialize for creating a new space
      _spaceData = MemorialSpaceUpdateData(
        deceasedName: '',
        relationship: '',
        bio: '',
        privacyLevel: 'private',
      );
      _coverImage = null;
      _timelineEvents = [];
      _lifePhotos = [];
      _videos = [];
    }
    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _saveMemorialSpace() async {
    if (!_formKey.currentState!.validate()) {
      return; // Validation failed
    }
    _formKey.currentState!.save();

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    // TODO(user): Implement API call to save/update memorial space data.
    // This includes:
    // 1. Updating basic info (_spaceData)
    // 2. Uploading new cover image (_coverImage if isNew)
    // 3. Adding/Updating/Deleting timeline events (_timelineEvents)
    // 4. Uploading/Deleting life photos (_lifePhotos)
    // 5. Uploading/Deleting videos (_videos)

    print('--- Saving Data ---');
    print('Deceased Name: ${_spaceData.deceasedName}');
    // ... print other data ...

    await Future<void>.delayed(const Duration(seconds: 2)); // Simulate network delay

    // Simulate API response
    const success = true; // Replace with actual API response
    final newSpaceId = widget.spaceId.isEmpty || widget.spaceId == 'new' ? 'mock_new_id_123' : widget.spaceId;

    if (success) {
      if (mounted) {
        if (widget.spaceId.isEmpty || widget.spaceId == 'new') {
          await Navigator.popAndPushNamed(context, '/memorial-space-detail', arguments: newSpaceId);
        } else {
          Navigator.pop(context, true); // Pass true to indicate save was successful
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) => CupertinoPageScaffold(
      backgroundColor: kBackgroundColor,
      navigationBar: CupertinoNavigationBar(
        middle: Text(widget.spaceId.isEmpty || widget.spaceId == 'new' ? '创建纪念空间' : '编辑纪念空间'),
        leading: CupertinoNavigationBarBackButton(onPressed: () => Navigator.pop(context)),
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: _isLoading ? null : _saveMemorialSpace,
          child: _isLoading ? const CupertinoActivityIndicator() : const Text('保存'),
        ),
      ),
      child: _isLoading
          ? const Center(child: CupertinoActivityIndicator())
          : _buildForm(),
    );

  Widget _buildForm() {
    if (_errorMessage != null && !_isLoading && !(widget.spaceId.isEmpty || widget.spaceId == 'new')) {
      // Show error prominently if initial load failed for an existing space and not in loading state
      return Center(
          child: Padding(
              padding: const EdgeInsets.all(16),
              child: Text(_errorMessage!, style: const TextStyle(color: kErrorColor))));
    }
    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 64), // Added bottom padding for scroll
        children: <Widget>[
          if (_errorMessage != null && !_isLoading)
            Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: Text(_errorMessage!,
                  style: TextStyle(
                      color: (widget.spaceId.isEmpty || widget.spaceId == 'new') &&
                              _errorMessage!.contains('创建')
                          ? kTextColor
                          : kErrorColor,
                      fontWeight: FontWeight.bold)),
            ),
          _buildSectionTitle('基本信息'),
          _buildCupertinoTextFormFieldRow(label: '逝者姓名', initialValue: _spaceData.deceasedName, validator: (val) => val == null || val.isEmpty ? '姓名不能为空' : null, onSaved: (val) => _spaceData.deceasedName = val ?? ''),
          _buildCupertinoTextFormFieldRow(label: '生辰 (格式: YYYY-MM-DD)', initialValue: _spaceData.birthDate?.toIso8601String().substring(0, 10), onSaved: (val) => _spaceData.birthDate = val != null && val.isNotEmpty ? DateTime.tryParse(val) : null, keyboardType: TextInputType.datetime, placeholder: '选填'),
          _buildCupertinoTextFormFieldRow(label: '仙逝日期 (格式: YYYY-MM-DD)', initialValue: _spaceData.deathDate?.toIso8601String().substring(0, 10), onSaved: (val) => _spaceData.deathDate = val != null && val.isNotEmpty ? DateTime.tryParse(val) : null, keyboardType: TextInputType.datetime, placeholder: '选填'),
          _buildCupertinoTextFormFieldRow(label: '与我关系', initialValue: _spaceData.relationship, validator: (val) => val == null || val.isEmpty ? '关系不能为空' : null, onSaved: (val) => _spaceData.relationship = val ?? ''),
          _buildCupertinoTextFormFieldRow(label: '生平简介', initialValue: _spaceData.bio, maxLines: 5, validator: (val) => val == null || val.isEmpty ? '简介不能为空' : null, onSaved: (val) => _spaceData.bio = val ?? '', placeholder: '请填写逝者的生平事迹、贡献与回忆。'),
          _buildPrivacyPicker(),
          if (_spaceData.privacyLevel == 'password')
            _buildCupertinoTextFormFieldRow(label: '访问密码', initialValue: _spaceData.accessPassword, onSaved: (val) => _spaceData.accessPassword = val, obscureText: true, validator: (val) => _spaceData.privacyLevel == 'password' && (val == null || val.isEmpty) ? '密码不能为空' : null),
          const SizedBox(height: 24),
          _buildSectionTitle('封面图片'),
          _buildCoverImagePicker(),
          const SizedBox(height: 24),
          _buildSectionTitle('生平事迹'),
          _buildTimelineEventsManager(),
          const SizedBox(height: 24),
          _buildSectionTitle('生活相册'),
          _buildMediaPickerSection(title: '照片', mediaList: _lifePhotos, assetType: 'image'),
          const SizedBox(height: 24),
          _buildSectionTitle('纪念视频'),
          _buildMediaPickerSection(title: '视频', mediaList: _videos, assetType: 'video'),
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) => Padding(
        padding: const EdgeInsets.only(top: 16, bottom: 8),
        child: Text(title,
            style: CupertinoTheme.of(context)
                .textTheme
                .navTitleTextStyle
                .copyWith(color: kTextColor, fontWeight: FontWeight.bold)),
      );

  Widget _buildCupertinoTextFormFieldRow({
    required String label,
    String? initialValue,
    void Function(String?)? onSaved,
    String? Function(String?)? validator,
    int maxLines = 1,
    bool obscureText = false,
    TextInputType? keyboardType,
    String? placeholder,
  }) => Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: CupertinoFormRow(
        prefix: Text(label, style: const TextStyle(color: kSecondaryTextColor, fontSize: 14)),
        error: validator != null && initialValue != null && validator(initialValue) != null
            ? Text(validator(initialValue)!,
                style: const TextStyle(color: kErrorColor, fontSize: 12))
            : null,
        child: CupertinoTextFormFieldRow(
          initialValue: initialValue,
          placeholder: placeholder ?? '请输入$label',
          onSaved: onSaved,
          validator: validator,
          maxLines: maxLines,
          obscureText: obscureText,
          keyboardType: keyboardType,
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 10), // Corrected to 'padding' for CupertinoTextField
          decoration: BoxDecoration(
            color: kCardBackgroundColor,
            border: Border.all(color: CupertinoColors.separator.withAlpha(128)),
            borderRadius: BorderRadius.circular(8),
          ),
          style: const TextStyle(color: kTextColor, fontSize: 16),
          placeholderStyle: TextStyle(color: kSecondaryTextColor.withAlpha(153)),
        ),
      ),
    );

  Widget _buildInlineCupertinoTextField({
    required String label,
    String? initialValue,
    void Function(String)? onChanged,
    int maxLines = 1,
    String? placeholder,
    TextEditingController? controller,
    bool autofocus = false,
  }) => Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: const TextStyle(color: kSecondaryTextColor, fontSize: 14)),
          const SizedBox(height: 4),
          CupertinoTextField(
            controller: controller ?? TextEditingController(text: initialValue),
            placeholder: placeholder ?? '请输入$label',
            onChanged: onChanged,
            maxLines: maxLines,
            autofocus: autofocus,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: kCardBackgroundColor,
              border: Border.all(color: CupertinoColors.separator.withAlpha(128)),
              borderRadius: BorderRadius.circular(8),
            ),
            style: const TextStyle(color: kTextColor, fontSize: 16),
            placeholderStyle: TextStyle(color: kSecondaryTextColor.withAlpha(153)),
          ),
        ],
      ),
    );

  Widget _buildPrivacyPicker() {
    final privacyOptions = <String, String>{
      'public': '公开',
      'password': '密码',
      'family': '家族',
      'private': '私密',
    };
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: CupertinoFormRow(
        prefix: const Text('隐私设置', style: TextStyle(color: kSecondaryTextColor, fontSize: 14)),
        child: SizedBox(
          width: double.infinity,
          child: CupertinoSlidingSegmentedControl<String>(
            backgroundColor: kCardBackgroundColor,
            thumbColor: AppColors.primary,
            groupValue: _spaceData.privacyLevel,
            children: privacyOptions.map((key, value) => MapEntry(
                key,
                Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    child: Text(value,
                        style: TextStyle(
                            fontSize: 13,
                            color: _spaceData.privacyLevel == key
                                ? CupertinoColors.white
                                : kTextColor))))),
            onValueChanged: (String? newValue) {
              if (newValue != null) {
                setState(() {
                  _spaceData.privacyLevel = newValue;
                  if (newValue != 'password') {
                    _spaceData.accessPassword = null; // Clear password
                  }
                });
              }
            },
          ),
        ),
      ),
    );
  }

  Widget _buildCoverImagePicker() =>
    // TODO(user): Implement image picker logic using image_picker package.
    Column(
      children: [
        GestureDetector(
          onTap: () async {
            // final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
            // if (image != null) {
            //   setState(() {
            //     _coverImage = MediaAssetData(url: image.path, type: 'image', file: image, isNew: true);
            //   });
            // }
            // TODO(user): Implement Cover Image Picker
            // Mock selection for UI update
            setState(() {
              _coverImage = MediaAssetData(
                  url:
                      'https://images.unsplash.com/photo-1541701494587-cb58502866ab?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60',
                  type: 'image',
                  isNew: true);
            });
          },
          child: Container(
            height: 180,
            width: double.infinity,
            decoration: BoxDecoration(
              color: kCardBackgroundColor,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: CupertinoColors.separator.withAlpha(128)),
              image: _coverImage?.url != null && _coverImage!.url.startsWith('http')
                  ? DecorationImage(image: NetworkImage(_coverImage!.url), fit: BoxFit.cover)
                  : null, // TODO(user): Handle local file preview (FileImage)
            ),
            child: _coverImage?.url == null
                ? const Center(
                    child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(CupertinoIcons.photo_camera, size: 40, color: kSecondaryTextColor),
                      SizedBox(height: 8),
                      Text('点击选择封面图片', style: TextStyle(color: kSecondaryTextColor))
                    ],
                  ))
                : (_coverImage!.url.startsWith('http')
                    ? null
                    : Center(child: Text('本地图片预览: ${_coverImage!.url.split('/').last}', style: const TextStyle(color: kTextColor)))),
          ),
        ),
        if (_coverImage != null)
          CupertinoButton(
              child: const Text('移除封面', style: TextStyle(color: kErrorColor)),
              onPressed: () {
                setState(() {
                  _coverImage = null;
                });
              })
      ],
    );

  Widget _buildTimelineEventsManager() => Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_timelineEvents.isEmpty)
          const Padding(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: Center(child: Text('暂无生平事迹', style: TextStyle(color: kSecondaryTextColor)))),
        if (_timelineEvents.isNotEmpty)
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _timelineEvents.length,
            itemBuilder: (context, index) => _buildTimelineEventTile(_timelineEvents[index], index),
          ),
        const SizedBox(height: 12),
        Center(
          child: CupertinoButton(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            color: AppColors.primary.withAlpha((0.15 * 255).round()),
            onPressed: () => _showEditTimelineEventDialog(null), // Added const
            child: const Row(mainAxisSize: MainAxisSize.min, children: <Widget>[ // Added <Widget>
              Icon(CupertinoIcons.add_circled, size: 20, color: AppColors.primary),
              SizedBox(width: 8),
              Text('添加生平事迹', style: TextStyle(color: AppColors.primary, fontWeight: FontWeight.w600))
            ]),
          ),
        ),
      ],
    );

  Widget _buildTimelineEventTile(TimelineEventData event, int index) => Container(
      margin: const EdgeInsets.symmetric(vertical: 6),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: kCardBackgroundColor,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: CupertinoColors.separator.withAlpha((0.3 * 255).round())),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Text(
                  event.title.isNotEmpty ? event.title : '(无标题事迹)',
                  style: const TextStyle(color: kTextColor, fontWeight: FontWeight.w600, fontSize: 16),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Row(
                children: [
                  CupertinoButton(
                    padding: const EdgeInsets.all(4),
                    onPressed: () => _showEditTimelineEventDialog(event),
                    minimumSize: Size.zero, // This is deprecated, but no direct Cupertino replacement for zero-size tap target without affecting layout.
                               // Consider custom gesture detector if exact behavior is needed without deprecation.
                    child: const Icon(CupertinoIcons.pencil, size: 20, color: kSecondaryTextColor),
                  ),
                  CupertinoButton(
                    padding: const EdgeInsets.all(4),
                    onPressed: () => _confirmRemoveTimelineEvent(index),
                    minimumSize: Size.zero, // Similar to above
                    child: const Icon(CupertinoIcons.trash, size: 20, color: kErrorColor),
                  ),
                ],
              ),
            ],
          ),
          if (event.year.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4, bottom: 2),
              child: Text(event.year, style: const TextStyle(color: kSecondaryTextColor, fontSize: 13)),
            ),
          if (event.description.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(event.description,
                  style: const TextStyle(color: kTextColor, fontSize: 14),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis),
            ),
        ],
      ),
    );

  void _confirmRemoveTimelineEvent(int index) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => CupertinoActionSheet(
        title: const Text('确认删除'),
        message: const Text('您确定要删除此生平事迹吗？此操作无法撤销。'),
        actions: <CupertinoActionSheetAction>[
          CupertinoActionSheetAction(
            isDestructiveAction: true,
            onPressed: () {
              setState(() {
                _timelineEvents.removeAt(index);
              });
              Navigator.pop(context);
            },
            child: const Text('删除'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          child: const Text('取消'),
          onPressed: () => Navigator.pop(context),
        ),
      ),
    );
  }

  void _showEditTimelineEventDialog(TimelineEventData? event) {
    final isEditing = event != null;
    final currentEventData = event ?? TimelineEventData(year: '', title: '', description: '', isNew: true);

    final yearController = TextEditingController(text: currentEventData.year);
    final titleController = TextEditingController(text: currentEventData.title);
    final descriptionController = TextEditingController(text: currentEventData.description);

    showCupertinoDialog<void>(
      context: context,
      builder: (BuildContext dialogContext) => CupertinoAlertDialog(
          title: Text(isEditing ? '编辑生平事迹' : '添加生平事迹'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              _buildInlineCupertinoTextField(label: '年份', controller: yearController, placeholder: '例如: 1990年 或 2000-2005'),
              _buildInlineCupertinoTextField(label: '标题', controller: titleController, placeholder: '事迹标题'),
              _buildInlineCupertinoTextField(label: '描述', controller: descriptionController, placeholder: '详细描述', maxLines: 3),
            ],
          ),
          actions: <CupertinoDialogAction>[
            CupertinoDialogAction(
              child: const Text('取消'),
              onPressed: () => Navigator.pop(dialogContext),
            ),
            CupertinoDialogAction(
              isDefaultAction: true,
              child: const Text('保存'),
              onPressed: () {
                setState(() {
                  final updatedEvent = TimelineEventData(
                    id: currentEventData.id,
                    year: yearController.text,
                    title: titleController.text,
                    description: descriptionController.text,
                    isNew: currentEventData.isNew, // Preserve isNew status if editing
                  );
                  if (isEditing) {
                    final eventIndex = _timelineEvents.indexWhere((e) => e.id == currentEventData.id);
                    if (eventIndex != -1) {
                      _timelineEvents[eventIndex] = updatedEvent;
                    }
                  } else {
                    _timelineEvents.add(updatedEvent..isNew = true); // Mark as new if adding
                  }
                });
                Navigator.pop(dialogContext);
              },
            ),
          ],
        ),
    );
  }

  Widget _buildMediaPickerSection({
    required String title,
    required List<MediaAssetData> mediaList, // Placeholder for media URLs or paths
    required String assetType, // 'image' or 'video'
  }) =>
    // TODO(user): Implement image/video picker logic using image_picker or file_picker.
    Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (mediaList.isEmpty)
          Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Center(child: Text('暂无$title', style: const TextStyle(color: kSecondaryTextColor)))),
        if (mediaList.isNotEmpty)
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: mediaList.length,
            itemBuilder: (context, index) {
              final asset = mediaList[index];
              return GestureDetector(
                onTap: () => _showEditMediaCaptionDialog(asset),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    DecoratedBox(
                      decoration: BoxDecoration(
                        color: kCardBackgroundColor,
                        borderRadius: BorderRadius.circular(8),
                        image: asset.url.startsWith('http')
                            ? DecorationImage(image: NetworkImage(asset.url), fit: BoxFit.cover)
                            : null, // TODO(user): Handle local file preview (FileImage)
                      ),
                      child: asset.url.startsWith('http')
                          ? const SizedBox.shrink() // Use SizedBox.shrink() for empty content in DecoratedBox
                          : const Center(child: Icon(CupertinoIcons.photo, size: 40, color: kSecondaryTextColor)),
                    ),
                    if (assetType == 'video')
                      const Icon(CupertinoIcons.play_circle_fill, color: CupertinoColors.white, size: 40),
                    Positioned(
                      top: 4,
                      right: 4,
                      child: CupertinoButton(
                        padding: const EdgeInsets.all(2),
                        color: kErrorColor.withAlpha(180),
                        borderRadius: BorderRadius.circular(12),
                        minimumSize: Size.zero, // Set to Size.zero
                        onPressed: () => _confirmRemoveMedia(mediaList, index, title),
                        child: const Icon(CupertinoIcons.xmark, color: CupertinoColors.white, size: 14),
                      ),
                    ),
                    if (asset.caption != null && asset.caption!.isNotEmpty)
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                          color: CupertinoColors.black.withAlpha(150),
                          child: Text(
                            asset.caption!,
                            style: const TextStyle(color: CupertinoColors.white, fontSize: 10),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      )
                  ],
                ),
              );
            },
          ),
        const SizedBox(height: 12),
        Center(
          child: CupertinoButton(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
            color: AppColors.primary.withAlpha((0.15 * 255).round()),
            onPressed: () async { // Added const
              // final List<XFile>? pickedFiles = assetType == 'image'
              //   ? await _picker.pickMultiImage()
              //   : null; // TODO: Implement video picker
              // if (pickedFiles != null && pickedFiles.isNotEmpty) {
              //   setState(() {
              //     for (var file in pickedFiles) {
              //       mediaList.add(MediaAssetData(url: file.path, type: assetType, file: file, isNew: true));
              //     }
              //   });
              // }
              print('TODO: Implement $assetType Picker');
              setState(() {
                mediaList.add(MediaAssetData(
                    url: 'https://via.placeholder.com/150?text=New${assetType == 'image' ? 'Pic' : 'Vid'}',
                    type: assetType,
                    isNew: true,
                    caption: '新$title'));
              });
            },
            child: Row(mainAxisSize: MainAxisSize.min, children: <Widget>[ // Removed const because onPressed is async
              const Icon(CupertinoIcons.add_circled, size: 20, color: AppColors.primary),
              const SizedBox(width: 8),
              Text('添加$title', style: const TextStyle(color: AppColors.primary, fontWeight: FontWeight.w600))
            ]),
          ),
        ),
      ],
    );

  void _confirmRemoveMedia(List<MediaAssetData> mediaList, int index, String title) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => CupertinoActionSheet(
        title: Text('确认删除$title'),
        message: const Text('您确定要删除此媒体文件吗？此操作无法撤销。'),
        actions: <CupertinoActionSheetAction>[
          CupertinoActionSheetAction(
            isDestructiveAction: true,
            onPressed: () {
              setState(() {
                mediaList.removeAt(index);
              });
              Navigator.pop(context);
            },
            child: const Text('删除'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          child: const Text('取消'),
          onPressed: () => Navigator.pop(context),
        ),
      ),
    );
  }

  void _showEditMediaCaptionDialog(MediaAssetData asset) {
    final captionController = TextEditingController(text: asset.caption);
    showCupertinoDialog<void>(
      context: context,
      builder: (BuildContext dialogContext) => CupertinoAlertDialog(
        title: const Text('编辑标题'),
        content: Padding(
          padding: const EdgeInsets.only(top: 8),
          child: _buildInlineCupertinoTextField(
            label: '', // No label needed here as it's clear from context
            controller: captionController,
            placeholder: '输入标题 (可选)',
            autofocus: true,
          ),
        ),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            child: const Text('取消'),
            onPressed: () => Navigator.pop(dialogContext),
          ),
          CupertinoDialogAction(
            isDefaultAction: true,
            child: const Text('保存'),
            onPressed: () {
              setState(() {
                asset.caption = captionController.text.isNotEmpty ? captionController.text : null;
              });
              Navigator.pop(dialogContext);
            },
          ),
        ],
      ),
    );
  }
}
