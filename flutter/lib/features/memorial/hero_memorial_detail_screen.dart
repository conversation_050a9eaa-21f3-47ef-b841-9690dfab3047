import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:memorial/core/models/hero.dart' as memorial;
import 'package:memorial/data/heroes_data.dart';

class HeroMemorialDetailScreen extends StatefulWidget {
  const HeroMemorialDetailScreen({required this.heroId, super.key});

  final String heroId;

  @override
  State<HeroMemorialDetailScreen> createState() => _HeroMemorialDetailScreenState();
}

class _HeroMemorialDetailScreenState extends State<HeroMemorialDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  memorial.Hero? hero;
  final int visitCount = 5000 + (DateTime.now().millisecondsSinceEpoch % 10000);

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    hero = HeroesData.getHeroById(widget.heroId);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (hero == null) {
      return Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: const Center(
          child: Text(
            '英雄不存在',
            style: TextStyle(color: Colors.white, fontSize: 18),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFF1a1a1a),
      body: CustomScrollView(
        slivers: [
          // Custom App Bar with Hero Info
          SliverAppBar(
            expandedHeight: 280,
            pinned: true,
            backgroundColor: Colors.black.withValues(alpha: 0.8),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
              onPressed: () => Navigator.pop(context),
            ),
            actions: [
              Padding(
                padding: const EdgeInsets.all(8),
                child: Chip(
                  label: Text(
                    '访问: $visitCount',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                  backgroundColor: Colors.white.withValues(alpha: 0.2),
                ),
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: DecoratedBox(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.grey[800]!,
                      Colors.grey[900]!,
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40),
                      // Hero Avatar
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 3),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: ClipOval(
                          child: Image.network(
                            hero!.imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Container(
                                  color: Colors.grey[600],
                                  child: Center(
                                    child: Text(
                                      hero!.name[0],
                                      style: const TextStyle(
                                        fontSize: 48,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      // Hero Name
                      Text(
                        hero!.name,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      if (hero!.nameEn != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          hero!.nameEn!,
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[300],
                          ),
                        ),
                      ],
                      const SizedBox(height: 8),
                      Text(
                        hero!.lifeSpan,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[400],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        hero!.title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                          color: Colors.yellow,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      // Tags
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Chip(
                            label: Text(hero!.nationality),
                            backgroundColor: Colors.blue,
                            labelStyle: const TextStyle(color: Colors.white, fontSize: 12),
                          ),
                          const SizedBox(width: 8),
                          Chip(
                            label: Text(_getCategoryLabel(hero!.category)),
                            backgroundColor: Colors.green,
                            labelStyle: const TextStyle(color: Colors.white, fontSize: 12),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Tab Bar
          SliverToBoxAdapter(
            child: Container(
              color: Colors.grey[850],
              child: TabBar(
                controller: _tabController,
                isScrollable: true,
                indicatorColor: Colors.blue,
                labelColor: Colors.blue,
                unselectedLabelColor: Colors.grey,
                labelStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                tabs: const [
                  Tab(text: '3D空间'),
                  Tab(text: '生平'),
                  Tab(text: '语录'),
                  Tab(text: '祭拜'),
                  Tab(text: '留言'),
                ],
              ),
            ),
          ),

          // Tab Content
          SliverFillRemaining(
            child: TabBarView(
              controller: _tabController,
              children: [
                _build3DTab(),
                _buildBiographyTab(),
                _buildQuotesTab(),
                _buildTributeTab(),
                _buildMessagesTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _build3DTab() => Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(12),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Column(
            children: [
              Container(
                height: 200,
                color: Colors.grey[700],
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.view_in_ar,
                        size: 64,
                        color: Colors.white54,
                      ),
                      SizedBox(height: 16),
                      Text(
                        '3D纪念空间',
                        style: TextStyle(color: Colors.white, fontSize: 18),
                      ),
                      SizedBox(height: 8),
                      Text(
                        '沉浸式纪念体验',
                        style: TextStyle(color: Colors.white54, fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (hero!.scene != null) ...[
                        Text(
                          '场景：${hero!.scene!.name}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          hero!.scene!.description,
                          style: TextStyle(
                            color: Colors.grey[300],
                            fontSize: 14,
                            height: 1.4,
                          ),
                        ),
                      ],
                      const Spacer(),
                      Row(
                        children: [
                          Expanded(
                            child: CupertinoButton(
                              color: Colors.blue,
                              borderRadius: BorderRadius.circular(8),
                              onPressed: _showTributeDialog,
                              child: const Text('开始祭拜'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );

  Widget _buildBiographyTab() => Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(12),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '生平事迹',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                hero!.detailedBio ?? hero!.description,
                style: TextStyle(
                  color: Colors.grey[300],
                  fontSize: 14,
                  height: 1.6,
                ),
              ),
            ],
          ),
        ),
      );

  Widget _buildQuotesTab() => Container(
        margin: const EdgeInsets.all(16),
        child: ListView.builder(
          itemCount: hero!.quotes.length,
          itemBuilder: (context, index) => Container(
            margin: const EdgeInsets.only(bottom: 12),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(12),
              border: Border(
                left: BorderSide(
                  color: Colors.yellow[600]!,
                  width: 4,
                ),
              ),
            ),
            child: Text(
              '"${hero!.quotes[index]}"',
              style: TextStyle(
                color: Colors.grey[300],
                fontSize: 16,
                fontStyle: FontStyle.italic,
                height: 1.5,
              ),
            ),
          ),
        ),
      );

  Widget _buildTributeTab() => Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '祭拜致敬',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '向${hero!.name}表达敬意',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 24),
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                children: [
                  _buildTributeButton(
                    icon: '🕯️',
                    label: '点燃蜡烛',
                    color: Colors.yellow[600]!,
                    onTap: () => _performTribute('candle'),
                  ),
                  _buildTributeButton(
                    icon: '🌸',
                    label: '献花致敬',
                    color: Colors.pink[600]!,
                    onTap: () => _performTribute('flower'),
                  ),
                  _buildTributeButton(
                    icon: '🙏',
                    label: '默哀祈祷',
                    color: Colors.purple[600]!,
                    onTap: () => _performTribute('prayer'),
                  ),
                  _buildTributeButton(
                    icon: '💝',
                    label: '留下祝福',
                    color: Colors.blue[600]!,
                    onTap: _showMessageDialog,
                  ),
                ],
              ),
            ),
          ],
        ),
      );

  Widget _buildMessagesTab() {
    // 模拟留言数据
    final messages = [
      {'author': '热心市民', 'content': '向${hero!.name}致敬！您的精神永远激励着我们。', 'time': '2小时前'},
      {'author': '学生小王', 'content': '学习了${hero!.name}的事迹，深受感动。', 'time': '1天前'},
      {'author': '历史爱好者', 'content': '伟大的${hero!.name}，您的贡献永远不会被忘记。', 'time': '3天前'},
    ];

    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Expanded(
                  child: Text(
                    '留言追思',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                CupertinoButton(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(8),
                  onPressed: _showMessageDialog,
                  child: const Text('写留言'),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              itemCount: messages.length,
              itemBuilder: (context, index) {
                final message = messages[index];
                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[700],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            message['author']!,
                            style: const TextStyle(
                              color: Colors.blue,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            message['time']!,
                            style: TextStyle(
                              color: Colors.grey[400],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        message['content']!,
                        style: TextStyle(
                          color: Colors.grey[300],
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTributeButton({
    required String icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) =>
      GestureDetector(
        onTap: onTap,
        child: DecoratedBox(
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                icon,
                style: const TextStyle(fontSize: 32),
              ),
              const SizedBox(height: 8),
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      );

  void _performTribute(String type) {
    String message;
    switch (type) {
      case 'candle':
        message = '点燃了一支蜡烛，愿您安息';
      case 'flower':
        message = '献上鲜花，表达敬意';
      case 'prayer':
        message = '为您默哀祈祷';
      default:
        message = '';
    }

    _showSnackBar(message);
  }

  void _showTributeDialog() {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('向${hero!.name}致敬'),
        content: const Text('请选择您的祭拜方式'),
        actions: [
          CupertinoDialogAction(
            onPressed: () {
              Navigator.pop(context);
              _performTribute('candle');
            },
            child: const Text('点燃蜡烛'),
          ),
          CupertinoDialogAction(
            onPressed: () {
              Navigator.pop(context);
              _performTribute('flower');
            },
            child: const Text('献花'),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  void _showMessageDialog() {
    final controller = TextEditingController();
    
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('向${hero!.name}留言'),
        content: Padding(
          padding: const EdgeInsets.only(top: 12),
          child: CupertinoTextField(
            controller: controller,
            placeholder: '请输入您的留言...',
            maxLines: 3,
          ),
        ),
        actions: [
          CupertinoDialogAction(
            onPressed: () {
              Navigator.pop(context);
              if (controller.text.isNotEmpty) {
                _showSnackBar('感谢您的留言！');
              }
            },
            child: const Text('提交'),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  String _getCategoryLabel(String category) {
    switch (category) {
      case 'science':
        return '科学';
      case 'medical':
        return '医学';
      case 'military':
        return '军事';
      case 'civil':
        return '民权';
      default:
        return '其他';
    }
  }
}
