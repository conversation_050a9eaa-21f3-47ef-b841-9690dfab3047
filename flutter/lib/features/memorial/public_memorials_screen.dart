import 'package:flutter/cupertino.dart';

class PublicMemorialsScreen extends StatelessWidget {
  const PublicMemorialsScreen({super.key});

  @override
  Widget build(BuildContext context) => CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('公共纪念'),
      ),
      child: ListView(
        children: <Widget>[
          // 示例：可以从服务器获取公共纪念活动列表
          _buildMemorialListItem(context, '缅怀先烈活动', '纪念为国捐躯的英雄们', () {
            // TODO(feature): 导航到特定公共纪念活动详情页
            _showNotImplementedDialog(context, '公共纪念活动详情');
          }),
          _buildMemorialListItem(context, '行业先驱致敬', '纪念在各行业做出杰出贡献的人物', () {
            // TODO(feature): 导航到特定公共纪念活动详情页
            _showNotImplementedDialog(context, '公共纪念活动详情');
          }),
          // 更多公共纪念活动...
        ],
      ),
    );

  Widget _buildMemorialListItem(BuildContext context, String title, String subtitle, VoidCallback onTap) => CupertinoListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      leading: const Icon(CupertinoIcons.group_solid),
      trailing: const CupertinoListTileChevron(),
      onTap: onTap,
    );

  void _showNotImplementedDialog(BuildContext context, String featureName) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('功能开发中'),
        content: Text('$featureName 功能正在开发中，敬请期待。'),
        actions: <CupertinoDialogAction>[
          CupertinoDialogAction(
            isDefaultAction: true,
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('好的'),
          ),
        ],
      ),
    );
  }
}
