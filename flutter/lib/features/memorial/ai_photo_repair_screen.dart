import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../../core/providers/ai_provider.dart';
import '../../core/services/ai_service.dart';

class AiPhotoRepairScreen extends StatefulWidget {
  const AiPhotoRepairScreen({super.key});

  @override
  State<AiPhotoRepairScreen> createState() => _AiPhotoRepairScreenState();
}

class _AiPhotoRepairScreenState extends State<AiPhotoRepairScreen> {
  final ImagePicker _picker = ImagePicker();
  final List<File> _selectedImages = [];
  String _selectedOperation = 'restore';
  int _enhanceScale = 4;
  bool _faceEnhance = true;
  bool _isProcessing = false;

  final Map<String, String> _operations = {
    'restore': '照片修复',
    'colorize': '黑白上色',
    'enhance': '分辨率增强',
    'remove_bg': '背景移除',
  };

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: CupertinoNavigationBar(
        middle: const Text('AI 照片修复'),
        trailing: _selectedImages.isNotEmpty
            ? CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: _isProcessing ? null : _processImages,
                child: _isProcessing
                    ? const CupertinoActivityIndicator()
                    : const Text('处理'),
              )
            : null,
      ),
      child: SafeArea(
        child: CustomScrollView(
          slivers: [
            // 操作选择
            SliverToBoxAdapter(
              child: _buildOperationSelector(),
            ),
            
            // 参数设置
            if (_selectedOperation == 'enhance')
              SliverToBoxAdapter(
                child: _buildEnhanceSettings(),
              ),
            
            // 图片选择区域
            SliverToBoxAdapter(
              child: _buildImageSelector(),
            ),
            
            // 已选择的图片
            if (_selectedImages.isNotEmpty)
              SliverToBoxAdapter(
                child: _buildSelectedImages(),
              ),
            
            // 处理说明
            SliverToBoxAdapter(
              child: _buildProcessingInfo(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOperationSelector() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '选择处理类型',
            style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 12),
          CupertinoSlidingSegmentedControl<String>(
            groupValue: _selectedOperation,
            children: _operations.map((key, value) => MapEntry(
              key,
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  value,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            )),
            onValueChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedOperation = value;
                });
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEnhanceSettings() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: CupertinoListSection.insetGrouped(
        header: const Text('增强设置'),
        children: [
          CupertinoListTile(
            title: const Text('放大倍数'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('${_enhanceScale}x'),
                const SizedBox(width: 8),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  onPressed: () => _showScalePicker(),
                  child: const Icon(CupertinoIcons.chevron_right),
                ),
              ],
            ),
          ),
          CupertinoListTile(
            title: const Text('面部增强'),
            trailing: CupertinoSwitch(
              value: _faceEnhance,
              onChanged: (value) {
                setState(() {
                  _faceEnhance = value;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageSelector() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '选择照片',
            style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: CupertinoButton.filled(
                  onPressed: () => _pickImages(ImageSource.gallery),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(CupertinoIcons.photo_on_rectangle),
                      SizedBox(width: 8),
                      Text('从相册选择'),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: CupertinoButton.filled(
                  onPressed: () => _pickImages(ImageSource.camera),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(CupertinoIcons.camera),
                      SizedBox(width: 8),
                      Text('拍照'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedImages() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '已选择 ${_selectedImages.length} 张照片',
                style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              CupertinoButton(
                padding: EdgeInsets.zero,
                onPressed: () {
                  setState(() {
                    _selectedImages.clear();
                  });
                },
                child: const Text('清空'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          _selectedImages[index],
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover,
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedImages.removeAt(index);
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: CupertinoColors.systemRed,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              CupertinoIcons.xmark,
                              color: CupertinoColors.white,
                              size: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProcessingInfo() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                CupertinoIcons.info_circle,
                color: CupertinoColors.systemBlue,
              ),
              const SizedBox(width: 8),
              Text(
                '处理说明',
                style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _getOperationDescription(),
            style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
              color: CupertinoColors.secondaryLabel,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '• 支持批量处理，最多50张照片\n• 处理时间约15-60秒每张\n• 支持JPG、PNG格式\n• 处理完成后可下载结果',
            style: CupertinoTheme.of(context).textTheme.textStyle.copyWith(
              color: CupertinoColors.secondaryLabel,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  String _getOperationDescription() {
    switch (_selectedOperation) {
      case 'restore':
        return '使用AI技术修复老照片，去除噪点、修复破损、提升清晰度。';
      case 'colorize':
        return '为黑白照片智能上色，保持原始细节和真实感。';
      case 'enhance':
        return '提升照片分辨率和质量，支持2-8倍放大，可选面部增强。';
      case 'remove_bg':
        return '智能移除照片背景，生成透明背景图片。';
      default:
        return '';
    }
  }

  Future<void> _pickImages(ImageSource source) async {
    try {
      if (source == ImageSource.gallery) {
        // 多选图片
        final List<XFile> images = await _picker.pickMultiImage();
        if (images.isNotEmpty) {
          setState(() {
            _selectedImages.addAll(images.map((xfile) => File(xfile.path)));
          });
        }
      } else {
        // 拍照
        final XFile? image = await _picker.pickImage(source: source);
        if (image != null) {
          setState(() {
            _selectedImages.add(File(image.path));
          });
        }
      }
    } catch (e) {
      _showErrorDialog('选择图片失败: $e');
    }
  }

  void _showScalePicker() {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (context) => Container(
        height: 200,
        color: CupertinoColors.systemBackground.resolveFrom(context),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CupertinoButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('取消'),
                  ),
                  CupertinoButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('确定'),
                  ),
                ],
              ),
            ),
            Expanded(
              child: CupertinoPicker(
                itemExtent: 32,
                onSelectedItemChanged: (index) {
                  setState(() {
                    _enhanceScale = [2, 4, 6, 8][index];
                  });
                },
                children: const [
                  Text('2x'),
                  Text('4x'),
                  Text('6x'),
                  Text('8x'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _processImages() async {
    if (_selectedImages.isEmpty) {
      _showErrorDialog('请先选择要处理的照片');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final aiProvider = context.read<AIProvider>();
      final taskId = await aiProvider.batchProcessPhotos(
        files: _selectedImages,
        operation: _selectedOperation,
        scale: _enhanceScale,
        faceEnhance: _faceEnhance,
      );

      if (taskId != null) {
        // 显示成功消息并导航到任务列表
        _showSuccessDialog('处理任务已创建，任务ID: $taskId');
      } else {
        _showErrorDialog(aiProvider.error ?? '创建处理任务失败');
      }
    } catch (e) {
      _showErrorDialog('处理失败: $e');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  void _showErrorDialog(String message) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('错误'),
        content: Text(message),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showSuccessDialog(String message) {
    showCupertinoDialog<void>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('成功'),
        content: Text(message),
        actions: [
          CupertinoDialogAction(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // 返回到AI服务页面
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
