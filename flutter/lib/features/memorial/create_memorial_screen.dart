import 'package:flutter/cupertino.dart';
import 'package:memorial/core/theme/app_colors.dart';

// TODO(design): Define colors and styles from design spec or use from theme
// 移除硬编码颜色，使用AppColors.primary
const Color kTextColor = CupertinoColors.label;
const Color kSecondaryTextColor = CupertinoColors.secondaryLabel;
const Color kBackgroundColor = CupertinoColors.systemGroupedBackground;
const Color kCardBackgroundColor = CupertinoColors.secondarySystemGroupedBackground;
const Color kInputBgDark = CupertinoColors.tertiarySystemFill; // Adjusted for Cupertino
const Color kBorderDark = CupertinoColors.separator; // Adjusted for Cupertino

class CreateMemorialScreen extends StatefulWidget {
  const CreateMemorialScreen({super.key});

  @override
  State<CreateMemorialScreen> createState() => _CreateMemorialScreenState();
}

class _CreateMemorialScreenState extends State<CreateMemorialScreen> {
  final _formKey = GlobalKey<FormState>();
  String? _deceasedName;
  String _gender = 'male';
  DateTime? _birthDate;
  DateTime? _deathDate;
  String? _birthPlace;
  String? _biography;
  String _privacySetting = 'public';
  String? _accessPassword;

  // Retained _buildTextField, to be used within _buildCupertinoFormRow
  Widget _buildTextField({
    required String placeholder,
    bool isPassword = false,
    TextInputType keyboardType = TextInputType.text,
    void Function(String?)? onSaved,
    String? initialValue,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) => CupertinoTextFormFieldRow(
      placeholder: placeholder,
      obscureText: isPassword,
      keyboardType: keyboardType,
      initialValue: initialValue,
      onSaved: onSaved,
      validator: validator,
      maxLines: maxLines,
      style: const TextStyle(color: kTextColor),
      padding: const EdgeInsets.symmetric(vertical: 8),
      // CupertinoTextFormFieldRow handles its own background and borders
    );

  // Added _buildSectionTitle helper method
  Widget _buildSectionTitle(BuildContext context, String title) => Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 8, left: 16, right: 16),
      child: Text(
        title,
        style: CupertinoTheme.of(context).textTheme.navTitleTextStyle.copyWith(color: kTextColor),
      ),
    );

  // Added _buildCupertinoFormRow helper method
  Widget _buildCupertinoFormRow({required String label, required Widget child}) => Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: const TextStyle(color: kSecondaryTextColor, fontSize: 14, fontWeight: FontWeight.normal)),
          const SizedBox(height: 4),
          child,
        ],
      ),
    );

  // Retained and corrected _buildDateField (this was the second one, now the primary one)
  Widget _buildDateField(BuildContext context, String labelText, DateTime? selectedDate, void Function(DateTime) onDateChanged) => CupertinoButton(
      padding: EdgeInsets.zero,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 10),
        decoration: BoxDecoration(
          color: kInputBgDark, // Use defined color
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          selectedDate == null
              ? labelText // Use passed labelText for placeholder, e.g., '选择出生日期'
              : '${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}',
          style: TextStyle(color: selectedDate == null ? kSecondaryTextColor : kTextColor, fontSize: 16, fontWeight: FontWeight.normal),
        ),
      ),
      onPressed: () {
        showCupertinoModalPopup<void>(
          context: context,
          builder: (_) => Container(
            height: 250,
            color: kCardBackgroundColor, // Use consistent card background color
            child: CupertinoDatePicker(
              mode: CupertinoDatePickerMode.date,
              initialDateTime: selectedDate ?? DateTime.now(),
              onDateTimeChanged: onDateChanged,
            ),
          ),
        );
      },
    );

  @override
  Widget build(BuildContext context) => CupertinoPageScaffold(
      backgroundColor: kBackgroundColor,
      navigationBar: CupertinoNavigationBar(
        middle: const Text('创建新的纪念空间'),
        // backgroundColor: kCardBackgroundColor, // Navigation bar color usually from theme
        leading: CupertinoNavigationBarBackButton(onPressed: () => Navigator.of(context).pop()),
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          child: const Text('保存'),
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              _formKey.currentState!.save();
              // TODO(feature): Implement creation logic
              print('Form submitted: $_deceasedName, $_gender, $_birthDate, $_deathDate, $_birthPlace, $_biography, $_privacySetting, $_accessPassword');
              Navigator.of(context).pop(); // Example: Go back after saving
            }
          },
        ),
      ),
      child: SafeArea(
        child: Form(
          key: _formKey,
          child: ListView(
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  '请填写以下信息，为逝者创建一个专属的数字纪念空间。',
                  textAlign: TextAlign.center,
                  style: CupertinoTheme.of(context).textTheme.tabLabelTextStyle.copyWith(color: kSecondaryTextColor),
                ),
              ),

              // 基本信息 Section
              _buildSectionTitle(context, '基本信息'),
              _buildCupertinoFormRow(
                label: '逝者姓名',
                child: _buildTextField( // Uses the retained _buildTextField
                  placeholder: '例如：李明',
                  onSaved: (value) => _deceasedName = value,
                  validator: (value) => (value == null || value.isEmpty) ? '姓名不能为空' : null,
                ),
              ),
              _buildCupertinoFormRow( // Wrapped with _buildCupertinoFormRow
                  label: '性别',
                  child: CupertinoSlidingSegmentedControl<String>(
                      thumbColor: AppColors.primary, // Corrected color
                      groupValue: _gender,
                      children: const {
                        'male': Padding(padding: EdgeInsets.symmetric(horizontal: 20), child: Text('男', style: TextStyle(color: kTextColor, fontSize: 14, fontWeight: FontWeight.normal))),
                        'female': Padding(padding: EdgeInsets.symmetric(horizontal: 20), child: Text('女', style: TextStyle(color: kTextColor, fontSize: 14, fontWeight: FontWeight.normal))),
                        'other': Padding(padding: EdgeInsets.symmetric(horizontal: 20), child: Text('其他', style: TextStyle(color: kTextColor, fontSize: 14, fontWeight: FontWeight.normal))),
                      },
                      onValueChanged: (value) {
                        if (value != null) {
                          setState(() => _gender = value);
                        }
                      },
                    ),
              ),
              _buildCupertinoFormRow(
                  label: '出生日期',
                  child: _buildDateField(context, '选择出生日期', _birthDate, (date) => setState(() => _birthDate = date)),
              ),
              _buildCupertinoFormRow(
                  label: '逝世日期',
                  child: _buildDateField(context, '选择逝世日期', _deathDate, (date) => setState(() => _deathDate = date)),
              ),
              _buildCupertinoFormRow(
                label: '籍贯',
                child: _buildTextField(
                  placeholder: '例如：北京',
                  onSaved: (value) => _birthPlace = value,
                ),
              ),

              // 生平简介 Section
              _buildSectionTitle(context, '生平简介'),
              _buildCupertinoFormRow(
                label: '简介内容',
                child: _buildTextField(
                  placeholder: '请在此处填写逝者的生平事迹、重要经历、性格特点等... (支持Markdown)',
                  maxLines: 6,
                  onSaved: (value) => _biography = value,
                ),
              ),

              // 封面图片 Section
              _buildSectionTitle(context, '封面图片'),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Container(
                  height: 200,
                  decoration: BoxDecoration(
                    color: kInputBgDark.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: kBorderDark),
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Placeholder for image preview
                        const Icon(CupertinoIcons.photo_camera, size: 50, color: kSecondaryTextColor),
                        const SizedBox(height: 8),
                        CupertinoButton(
                          color: AppColors.primary, // Use theme primary color
                          child: const Text('上传文件', style: TextStyle(color: CupertinoColors.white, fontSize: 16)), // Ensure contrast
                          onPressed: () {
                            // TODO(feature): Implement image picking logic
                          },
                        ),
                        const SizedBox(height: 4),
                        Text('PNG, JPG, GIF, WEBP, 不超过 10MB', style: CupertinoTheme.of(context).textTheme.tabLabelTextStyle.copyWith(color: kSecondaryTextColor, fontSize: 12)),
                      ],
                    ),
                  ),
                ),
              ),

              // 空间设置 Section
              _buildSectionTitle(context, '空间设置'),
              _buildCupertinoFormRow(
                label: '可见性',
                child: CupertinoSlidingSegmentedControl<String>(
                      thumbColor: AppColors.primary, // Corrected color
                      groupValue: _privacySetting,
                      children: const {
                        'public': Padding(padding: EdgeInsets.symmetric(horizontal: 10), child: Text('公开', style: TextStyle(color: kTextColor, fontSize: 14, fontWeight: FontWeight.normal))),
                        'private': Padding(padding: EdgeInsets.symmetric(horizontal: 10), child: Text('私密', style: TextStyle(color: kTextColor, fontSize: 14, fontWeight: FontWeight.normal))),
                        'friends': Padding(padding: EdgeInsets.symmetric(horizontal: 10), child: Text('好友可见', style: TextStyle(color: kTextColor, fontSize: 14, fontWeight: FontWeight.normal))),
                      },
                      onValueChanged: (value) {
                        if (value != null) {
                          setState(() => _privacySetting = value);
                        }
                      },
                    ),
              ),
              if (_privacySetting == 'private') // 'private' should match the key in SegmentedControl
                _buildCupertinoFormRow(
                  label: '访问密码',
                  child: _buildTextField(
                    placeholder: '请输入至少6位密码 (设为私密时)',
                    isPassword: true,
                    onSaved: (value) => _accessPassword = value,
                    validator: (value) {
                      if (_privacySetting == 'private' && (value == null || value.length < 6)) {
                        return '密码至少需要6位';
                      }
                      return null;
                    },
                  ),
                ),

              // 操作按钮 (Moved to NavigationBar trailing for standard Cupertino UX)
              // Padding(
              //   padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 32.0),
              //   child: Row(
              //     mainAxisAlignment: MainAxisAlignment.end,
              //     children: [
              //       CupertinoButton(
              //         child: const Text('取消'), // Style from theme
              //         onPressed: () {
              //           Navigator.of(context).pop();
              //         },
              //       ),
              //       const SizedBox(width: 16),
              //       CupertinoButton.filled(
              //         child: const Text('确认创建'), // Style from theme
              //         onPressed: () {
              //           if (_formKey.currentState!.validate()) {
              //             _formKey.currentState!.save();
              //             // TODO: Implement creation logic
              //             print('Form submitted');
              //             Navigator.of(context).pop();
              //           }
              //         },
              //       ),
              //     ],
              //   ),
              // ),
              const SizedBox(height: 32), // Add some padding at the bottom
            ],
          ),
        ),
      ),
    );
}
