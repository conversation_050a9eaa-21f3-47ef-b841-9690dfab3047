import 'package:flutter/cupertino.dart';

class ChangePasswordScreen extends StatelessWidget {
  const ChangePasswordScreen({super.key});

  @override
  Widget build(BuildContext context) => CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('修改密码'),
      ),
      child: Center(
        child: Text(
          '修改密码页面',
          style: CupertinoTheme.of(context).textTheme.textStyle,
        ),
      ),
    );
}
