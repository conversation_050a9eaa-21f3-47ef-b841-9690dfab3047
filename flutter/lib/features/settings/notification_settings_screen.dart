import 'package:flutter/cupertino.dart';

class NotificationSettingsScreen extends StatelessWidget {
  const NotificationSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) => CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('通知设置'),
      ),
      child: Center(
        child: Text(
          '通知设置页面',
          style: CupertinoTheme.of(context).textTheme.textStyle,
        ),
      ),
    );
}
