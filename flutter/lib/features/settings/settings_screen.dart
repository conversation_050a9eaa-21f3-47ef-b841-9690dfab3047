import 'package:flutter/cupertino.dart';
import 'package:memorial/core/theme/app_colors.dart';
import 'package:memorial/features/settings/about_us_screen.dart';
import 'package:memorial/features/settings/change_password_screen.dart';
import 'package:memorial/features/settings/edit_profile_screen.dart';
import 'package:memorial/features/settings/notification_settings_screen.dart';
import 'package:memorial/features/settings/privacy_settings_screen.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) => CupertinoPageScaffold(
      backgroundColor: AppColors.background,
      navigationBar: const CupertinoNavigationBar(
        middle: Text('设置'),
      ),
      child: Safe<PERSON><PERSON>(
        child: ListView(
          children: <Widget>[
            CupertinoListSection.insetGrouped(
              header: const Text('账户'),
              children: <CupertinoListTile>[
                CupertinoListTile.notched(
                  title: const Text('编辑个人资料', style: TextStyle(color: AppColors.textPrimary)),
                  leading: const Icon(CupertinoIcons.person_fill, color: AppColors.primary),
                  trailing: const CupertinoListTileChevron(),
                  onTap: () {
                    Navigator.push(context, CupertinoPageRoute<void>(builder: (context) => const EditProfileScreen()));
                  },
                ),
                CupertinoListTile.notched(
                  title: const Text('修改密码', style: TextStyle(color: AppColors.textPrimary)),
                  leading: const Icon(CupertinoIcons.lock_fill, color: AppColors.primary),
                  trailing: const CupertinoListTileChevron(),
                  onTap: () {
                    Navigator.push(context, CupertinoPageRoute<void>(builder: (context) => const ChangePasswordScreen()));
                  },
                ),
              ],
            ),
            CupertinoListSection.insetGrouped(
              header: const Text('通用'),
              children: <CupertinoListTile>[
                CupertinoListTile.notched(
                  title: const Text('通知设置', style: TextStyle(color: AppColors.textPrimary)),
                  leading: const Icon(CupertinoIcons.bell_fill, color: AppColors.primary),
                  trailing: const CupertinoListTileChevron(),
                  onTap: () {
                    Navigator.push(context, CupertinoPageRoute<void>(builder: (context) => const NotificationSettingsScreen()));
                  },
                ),
                CupertinoListTile.notched(
                  title: const Text('隐私设置', style: TextStyle(color: AppColors.textPrimary)),
                  leading: const Icon(CupertinoIcons.hand_raised_fill, color: AppColors.primary),
                  trailing: const CupertinoListTileChevron(),
                  onTap: () {
                    Navigator.push(context, CupertinoPageRoute<void>(builder: (context) => const PrivacySettingsScreen()));
                  },
                ),
              ],
            ),
            CupertinoListSection.insetGrouped(
              header: const Text('关于'),
              children: <CupertinoListTile>[
                CupertinoListTile.notched(
                  title: const Text('关于我们', style: TextStyle(color: AppColors.textPrimary)),
                  leading: const Icon(CupertinoIcons.info_circle_fill, color: AppColors.primary),
                  trailing: const CupertinoListTileChevron(),
                  onTap: () {
                    Navigator.push(context, CupertinoPageRoute<void>(builder: (context) => const AboutUsScreen()));
                  },
                ),
                CupertinoListTile.notched(
                  title: const Text('版本号', style: TextStyle(color: AppColors.textPrimary)),
                  leading: const Icon(CupertinoIcons.tag_fill, color: AppColors.primary),
                  additionalInfo: const Text('1.0.0', style: TextStyle(color: AppColors.textSecondary)),
                  onTap: () {},
                ),
              ],
            ),
            CupertinoListSection.insetGrouped(
              children: <CupertinoListTile>[
                CupertinoListTile.notched(
                  title: const Text('退出登录', style: TextStyle(color: CupertinoColors.systemRed)),
                  leading: const Icon(CupertinoIcons.square_arrow_left, color: CupertinoColors.systemRed),
                  onTap: () {
                    // TODO(auth): Implement logout functionality
                    // Example: Show confirmation dialog, then navigate to login
                    showCupertinoDialog<void>(
                      context: context,
                      builder: (BuildContext context) => CupertinoAlertDialog(
                        title: const Text('确认退出'),
                        content: const Text('您确定要退出登录吗？'),
                        actions: <CupertinoDialogAction>[
                          CupertinoDialogAction(
                            child: const Text('取消'),
                            onPressed: () {
                              Navigator.pop(context); // Close the dialog
                            },
                          ),
                          CupertinoDialogAction(
                            isDestructiveAction: true,
                            child: const Text('退出'),
                            onPressed: () {
                              Navigator.pop(context); // Close the dialog
                              // Clear user session (if any state management is used for session)
                              // TODO(auth): Implement actual session clearing logic
                              print('Logout action: Clearing session and navigating to login.');
                              Navigator.of(context, rootNavigator: true).pushNamedAndRemoveUntil('/login', (Route<dynamic> route) => false);
                            },
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 30) // Bottom padding
          ],
        ),
      ),
    );
}
