import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;

import 'package:memorial/core/services/auth_service.dart';
import 'package:memorial/providers/auth_provider.dart';

// 数据模型
class DatabaseTable {
  const DatabaseTable({
    required this.name,
    required this.columnCount,
    required this.rowCount,
    required this.primaryKeys,
    required this.foreignKeysCount,
  });

  factory DatabaseTable.fromJson(Map<String, dynamic> json) => DatabaseTable(
      name: json['name'] as String,
      columnCount: json['column_count'] as int,
      rowCount: json['row_count'] as int,
      primaryKeys: List<String>.from(json['primary_keys'] as Iterable<dynamic>? ?? []),
      foreignKeysCount: json['foreign_keys_count'] as int,
    );

  final String name;
  final int columnCount;
  final int rowCount;
  final List<String> primaryKeys;
  final int foreignKeysCount;
}

class DatabaseStats {
  const DatabaseStats({
    required this.databaseName,
    required this.version,
    required this.totalTables,
    required this.totalRows,
    required this.sizeMb,
    required this.tables,
  });

  factory DatabaseStats.fromJson(Map<String, dynamic> json) {
    final dbInfo = json['database_info'] as Map<String, dynamic>;
    final tableStats = json['table_statistics'] as List<dynamic>;
    
    return DatabaseStats(
      databaseName: dbInfo['name'] as String,
      version: dbInfo['version'] as String,
      totalTables: dbInfo['total_tables'] as int,
      totalRows: dbInfo['total_rows'] as int,
      sizeMb: (dbInfo['size_mb'] as num).toDouble(),
      tables: tableStats.map((t) => DatabaseTable.fromJson(t as Map<String, dynamic>)).toList(),
    );
  }

  final String databaseName;
  final String version;
  final int totalTables;
  final int totalRows;
  final double sizeMb;
  final List<DatabaseTable> tables;
}

class HealthStatus {
  const HealthStatus({
    required this.status,
    required this.connection,
    required this.activeConnections,
    required this.databaseStartTime,
    required this.timestamp,
  });

  factory HealthStatus.fromJson(Map<String, dynamic> json) => HealthStatus(
      status: json['status'] as String,
      connection: json['connection'] as String,
      activeConnections: json['active_connections'] as int?,
      databaseStartTime: json['database_start_time'] as String?,
      timestamp: json['timestamp'] as String,
    );

  final String status;
  final String connection;
  final int? activeConnections;
  final String? databaseStartTime;
  final String timestamp;
}

// 数据库管理服务
class DatabaseAdminService {
  const DatabaseAdminService(this._authService);

  final AuthService _authService;

  Future<Map<String, dynamic>> _apiCall(String endpoint) async {
    final token = await _authService.getStoredToken();
    if (token == null) {
      throw Exception('未找到认证token');
    }

    const baseUrl = 'http://localhost:5001/api/v1';
    final url = Uri.parse('$baseUrl$endpoint');
    final response = await http.get(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body) as Map<String, dynamic>;
      
      if (!(data['success'] as bool? ?? false)) {
        throw Exception(data['message'] ?? '操作失败');
      }
      
      return data['data'] as Map<String, dynamic>;
    } else {
      throw Exception('HTTP ${response.statusCode}: 请求失败');
    }
  }

  Future<List<DatabaseTable>> getTables() async {
    final data = await _apiCall('/db-admin/tables');
    final tables = data['tables'] as List<dynamic>;
    return tables.map((t) => DatabaseTable.fromJson(t as Map<String, dynamic>)).toList();
  }

  Future<DatabaseStats> getStatistics() async {
    final data = await _apiCall('/db-admin/statistics');
    return DatabaseStats.fromJson(data);
  }

  Future<HealthStatus> getHealthCheck() async {
    final data = await _apiCall('/db-admin/health');
    return HealthStatus.fromJson(data);
  }
}

// Provider
final databaseAdminServiceProvider = Provider<DatabaseAdminService>((ref) {
  final authService = ref.watch(authServiceProvider);
  return DatabaseAdminService(authService);
});

final databaseTablesProvider = FutureProvider<List<DatabaseTable>>((ref) async {
  final service = ref.watch(databaseAdminServiceProvider);
  return service.getTables();
});

final databaseStatsProvider = FutureProvider<DatabaseStats>((ref) async {
  final service = ref.watch(databaseAdminServiceProvider);
  return service.getStatistics();
});

final healthStatusProvider = FutureProvider<HealthStatus>((ref) async {
  final service = ref.watch(databaseAdminServiceProvider);
  return service.getHealthCheck();
});

// 主界面
class DatabaseAdminScreen extends ConsumerStatefulWidget {
  const DatabaseAdminScreen({super.key});

  @override
  ConsumerState<DatabaseAdminScreen> createState() => _DatabaseAdminScreenState();
}

class _DatabaseAdminScreenState extends ConsumerState<DatabaseAdminScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('数据库管理'),
        backgroundColor: CupertinoColors.systemBackground,
      ),
      child: SafeArea(
        child: Column(
          children: [
            // 标签页导航
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: CupertinoSlidingSegmentedControl<int>(
                groupValue: _tabController.index,
                onValueChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _tabController.index = value;
                    });
                  }
                },
                children: const {
                  0: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    child: Text('概览'),
                  ),
                  1: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    child: Text('表结构'),
                  ),
                  2: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    child: Text('健康检查'),
                  ),
                },
              ),
            ),
            // 内容区域
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildOverviewTab(),
                  _buildTablesTab(),
                  _buildHealthTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );

  Widget _buildOverviewTab() {
    final statsAsync = ref.watch(databaseStatsProvider);
    
    return statsAsync.when(
      data: _buildOverviewContent,
      loading: () => const Center(child: CupertinoActivityIndicator()),
      error: (error, stackTrace) => _buildErrorWidget(error.toString()),
    );
  }

  Widget _buildOverviewContent(DatabaseStats stats) => SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 统计卡片
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            childAspectRatio: 1.5,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            children: [
              _buildStatCard('数据库名称', stats.databaseName),
              _buildStatCard('表总数', '${stats.totalTables}'),
              _buildStatCard('记录总数', '${stats.totalRows}'),
              _buildStatCard('数据库大小', '${stats.sizeMb.toStringAsFixed(1)} MB'),
            ],
          ),
          const SizedBox(height: 24),
          
          // 表列表
          Text(
            '表统计信息',
            style: CupertinoTheme.of(context).textTheme.navLargeTitleTextStyle,
          ),
          const SizedBox(height: 12),
          
          ...stats.tables.map(_buildTableCard),
        ],
      ),
    );

  Widget _buildStatCard(String label, String value) => Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: CupertinoColors.separator,
          width: 0.5,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: CupertinoColors.secondaryLabel,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: CupertinoColors.label,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );

  Widget _buildTableCard(DatabaseTable table) => Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: CupertinoColors.separator,
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                table.name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '${table.rowCount} 条记录',
                style: const TextStyle(
                  fontSize: 14,
                  color: CupertinoColors.secondaryLabel,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              _buildInfoChip('${table.columnCount} 列'),
              const SizedBox(width: 8),
              if (table.primaryKeys.isNotEmpty)
                _buildInfoChip('主键: ${table.primaryKeys.join(', ')}'),
              const SizedBox(width: 8),
              if (table.foreignKeysCount > 0)
                _buildInfoChip('${table.foreignKeysCount} 个外键'),
            ],
          ),
        ],
      ),
    );

  Widget _buildInfoChip(String text) => Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 12,
          color: CupertinoColors.secondaryLabel,
        ),
      ),
    );

  Widget _buildTablesTab() {
    final tablesAsync = ref.watch(databaseTablesProvider);
    
    return tablesAsync.when(
      data: _buildTablesContent,
      loading: () => const Center(child: CupertinoActivityIndicator()),
      error: (error, stackTrace) => _buildErrorWidget(error.toString()),
    );
  }

  Widget _buildTablesContent(List<DatabaseTable> tables) => ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: tables.length,
      itemBuilder: (context, index) {
        final table = tables[index];
        return _buildTableCard(table);
      },
    );

  Widget _buildHealthTab() {
    final healthAsync = ref.watch(healthStatusProvider);
    
    return healthAsync.when(
      data: _buildHealthContent,
      loading: () => const Center(child: CupertinoActivityIndicator()),
      error: (error, stackTrace) => _buildErrorWidget(error.toString()),
    );
  }

  Widget _buildHealthContent(HealthStatus health) => SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 健康状态卡片
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: health.status == 'healthy' 
                  ? CupertinoColors.systemGreen.withOpacity(0.1)
                  : CupertinoColors.systemRed.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: health.status == 'healthy' 
                    ? CupertinoColors.systemGreen
                    : CupertinoColors.systemRed,
              ),
            ),
            child: Column(
              children: [
                Icon(
                  health.status == 'healthy' 
                      ? CupertinoIcons.checkmark_circle_fill
                      : CupertinoIcons.xmark_circle_fill,
                  size: 48,
                  color: health.status == 'healthy' 
                      ? CupertinoColors.systemGreen
                      : CupertinoColors.systemRed,
                ),
                const SizedBox(height: 12),
                Text(
                  health.status == 'healthy' ? '数据库运行正常' : '数据库连接异常',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '连接状态: ${health.connection}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: CupertinoColors.secondaryLabel,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          
          // 详细信息
          Text(
            '详细信息',
            style: CupertinoTheme.of(context).textTheme.navLargeTitleTextStyle,
          ),
          const SizedBox(height: 12),
          
          _buildInfoRow('活跃连接数', '${health.activeConnections ?? '未知'}'),
          _buildInfoRow('数据库启动时间', health.databaseStartTime ?? '未知'),
          _buildInfoRow('检查时间', health.timestamp),
          
          const SizedBox(height: 24),
          
          // 刷新按钮
          SizedBox(
            width: double.infinity,
            child: CupertinoButton.filled(
              onPressed: () => ref.invalidate(healthStatusProvider),
              child: const Text('重新检查'),
            ),
          ),
        ],
      ),
    );

  Widget _buildInfoRow(String label, String value) => Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: CupertinoColors.separator,
          width: 0.5,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: CupertinoColors.secondaryLabel,
            ),
          ),
          Flexible(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );

  Widget _buildErrorWidget(String error) => Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              CupertinoIcons.exclamationmark_triangle,
              size: 48,
              color: CupertinoColors.systemRed,
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: CupertinoTheme.of(context).textTheme.navLargeTitleTextStyle,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: const TextStyle(
                color: CupertinoColors.secondaryLabel,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            CupertinoButton.filled(
              onPressed: () {
                ref
                  ..invalidate(databaseTablesProvider)
                  ..invalidate(databaseStatsProvider)
                  ..invalidate(healthStatusProvider);
              },
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
}
