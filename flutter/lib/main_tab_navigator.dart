import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:memorial/core/theme/app_colors.dart';
import 'package:memorial/features/home/<USER>';
import 'package:memorial/features/memorial/create_memorial_screen.dart';
import 'package:memorial/features/memorial/memorial_space_list_screen.dart';
import 'package:memorial/features/profile/profile_screen.dart';
import 'package:memorial/features/settings/store_screen.dart';

/// 主Tab导航器，实现底部Tab导航
class MainTabNavigator extends ConsumerStatefulWidget {
  const MainTabNavigator({super.key});

  @override
  ConsumerState<MainTabNavigator> createState() => _MainTabNavigatorState();
}

class _MainTabNavigatorState extends ConsumerState<MainTabNavigator> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreen(), // 首页
    const MemorialSpaceListScreen(), // 纪念空间列表
    const SizedBox.shrink(), // 创建按钮占位
    const StoreScreen(), // 商城
    const ProfileScreen(), // 我的页面
  ];

  List<BottomNavigationBarItem> get _tabItems => [
    const BottomNavigationBarItem(
      icon: Icon(CupertinoIcons.house, size: 18), // 基于Figma设计的图标尺寸
      activeIcon: Icon(CupertinoIcons.house_fill, size: 18),
      label: '首页',
    ),
    const BottomNavigationBarItem(
      icon: Icon(CupertinoIcons.group, size: 18),
      activeIcon: Icon(CupertinoIcons.group_solid, size: 18),
      label: '空间',
    ),
    BottomNavigationBarItem(
      icon: Container(
        width: 28, // 扫描按钮图标尺寸
        height: 28,
        decoration: const BoxDecoration(
          color: AppColors.pureWhite, // 特殊的圆形白色背景
          shape: BoxShape.circle,
        ),
        child: const Icon(
          CupertinoIcons.qrcode_viewfinder, // 扫描图标
          size: 16,
          color: AppColors.deepCharcoal,
        ),
      ),
      activeIcon: Container(
        width: 28,
        height: 28,
        decoration: const BoxDecoration(
          color: AppColors.pureWhite,
          shape: BoxShape.circle,
        ),
        child: const Icon(
          CupertinoIcons.qrcode_viewfinder,
          size: 16,
          color: AppColors.deepCharcoal,
        ),
      ),
      label: '开始创建', // 基于Figma设计的标签
    ),
    const BottomNavigationBarItem(
      icon: Icon(CupertinoIcons.news, size: 18), // 动态图标
      activeIcon: Icon(CupertinoIcons.news_solid, size: 18),
      label: '动态',
    ),
    const BottomNavigationBarItem(
      icon: Icon(CupertinoIcons.person_circle, size: 18),
      activeIcon: Icon(CupertinoIcons.person_circle_fill, size: 18),
      label: '我的',
    ),
  ];

  @override
  Widget build(BuildContext context) => CupertinoTabScaffold(
    backgroundColor: AppColors.background, // 纯白背景
    tabBar: CupertinoTabBar(
      backgroundColor: AppColors.pureWhite, // 纯白色背景
      border: const Border(
        top: BorderSide(color: AppColors.lightGrayBorder, width: 0.5), // 顶部细边框
      ),
      items: _tabItems,
      currentIndex: _currentIndex,
      onTap: (index) {
        // 如果点击的是创建按钮（中间的扫描按钮），直接导航到创建页面
        if (index == 2) {
          Navigator.of(context).push(
            CupertinoPageRoute<void>(
              builder: (context) => const CreateMemorialScreen(),
              fullscreenDialog: true,
            ),
          );
          return;
        }
        setState(() {
          _currentIndex = index;
        });
      },
      activeColor: AppColors.iconActive, // 激活状态：柔和粉红色
      inactiveColor: AppColors.iconInactive, // 非激活状态：中性灰色
    ),
    tabBuilder: (context, index) {
      // 创建按钮不需要对应的tab页面
      if (index == 2) {
        return const SizedBox.shrink();
      }

      return CupertinoTabView(builder: (context) => _screens[index]);
    },
  );
}
