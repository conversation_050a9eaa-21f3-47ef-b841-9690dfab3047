import 'package:memorial/core/models/hero.dart';

class HeroesData {
  static const List<Hero> heroes = [
    // Chinese Heroes
    Hero(
      id: 'yuan-longping',
      name: '袁隆平',
      nameEn: 'Yuan Longping',
      lifeSpan: '1930-2021',
      title: '杂交水稻之父',
      titleEn: 'Father of Hybrid Rice',
      description: '中国农学家，被誉为"杂交水稻之父"。他的研究成果大大提高了水稻产量，解决了中国乃至世界的粮食问题，让数亿人免于饥饿。',
      descriptionEn: 'Chinese agronomist known as the "Father of Hybrid Rice". His research greatly increased rice yields, solving food problems in China and worldwide, saving hundreds of millions from hunger.',
      detailedBio: '''
袁隆平院士，1930年9月7日出生于北京，是世界著名的杂交水稻育种专家，中国工程院院士。

主要成就：
• 1973年，成功培育出世界上第一个实用高产杂交水稻品种'南优2号'
• 推动中国水稻产量从每亩300公斤提高到500公斤以上
• 其研究成果在全球推广，解决了数亿人的温饱问题
• 获得国家最高科学技术奖、世界粮食奖等众多荣誉

袁隆平院士一生致力于杂交水稻研究，直到91岁高龄仍在田间工作。他的梦想是'让所有人远离饥饿'，被誉为'当代神农'。2021年5月22日，袁隆平院士在长沙逝世，全国人民深切悼念这位为人类作出巨大贡献的科学家。''',
      imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
      category: 'science',
      nationality: '中国',
      quotes: [
        '我有两个梦，一个是禾下乘凉梦，一个是杂交水稻覆盖全球梦。',
        '人就像种子，要做一粒好种子。',
        '我觉得人就像一粒种子。要做一粒好的种子，身体、精神、情感都要健康。'
      ],
      scene: HeroScene(
        category: 'nature',
        name: '稻田丰收',
        description: '金黄的稻田象征着袁隆平院士一生的奉献',
      ),
    ),
    Hero(
      id: 'zhong-nanshan',
      name: '钟南山',
      nameEn: 'Zhong Nanshan',
      lifeSpan: '1936-',
      title: '抗疫英雄',
      titleEn: 'Anti-epidemic Hero',
      description: '中国工程院院士，呼吸病学专家。在2003年抗击SARS和2020年抗击新冠疫情中做出杰出贡献，被誉为"国士无双"。',
      detailedBio: '''
钟南山院士，1936年10月出生于南京，中国工程院院士、著名呼吸病学专家。

主要成就：
• 2003年SARS疫情期间，勇敢质疑权威，提出正确的治疗方案
• 2020年新冠疫情爆发时，84岁高龄逆行武汉，成为抗疫的定海神针
• 建立了呼吸疾病国家重点实验室
• 在慢性咳嗽、哮喘、慢阻肺等领域取得重大研究成果

钟南山院士始终坚持'医者仁心'的理念，在关键时刻总是挺身而出。他的专业精神和高尚医德激励着一代又一代医务工作者。''',
      imageUrl: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=400&fit=crop&crop=face',
      category: 'medical',
      nationality: '中国',
      quotes: [
        '医生看的不是病，而是病人。',
        '选择医学可能是偶然，但你一旦选择了，就必须用一生的忠诚和热情去对待它。',
        '健康所系，性命相托。'
      ],
      scene: HeroScene(
        category: 'modern',
        name: '医者仁心',
        description: '现代医院环境，象征着医学的进步与希望',
      ),
    ),
    Hero(
      id: 'lei-feng',
      name: '雷锋',
      nameEn: 'Lei Feng',
      lifeSpan: '1940-1962',
      title: '助人为乐的楷模',
      titleEn: 'Model of Selflessness',
      description: '中国人民解放军战士，以其无私奉献、助人为乐的精神成为中国道德楷模。"雷锋精神"激励了几代中国人。',
      detailedBio: '''
雷锋，原名雷正兴，1940年12月18日出生于湖南省望城县。1962年8月15日因公殉职，年仅22岁。

主要事迹：
• 热心帮助战友和群众，做了数不清的好事
• 勤俭节约，将积蓄全部捐给灾区和需要帮助的人
• 刻苦学习，写下了近20万字的日记
• 爱岗敬业，在平凡的岗位上做出不平凡的贡献

雷锋精神的核心是全心全意为人民服务。他的名言'人的生命是有限的，可是为人民服务是无限的'激励着一代又一代中国人。''',
      imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
      category: 'military',
      nationality: '中国',
      quotes: [
        '人的生命是有限的，可是为人民服务是无限的。',
        '一滴水只有放进大海里才永远不会干涸。',
        '我愿做一颗永不生锈的螺丝钉。'
      ],
      scene: HeroScene(
        category: 'chinese-traditional',
        name: '英雄纪念堂',
        description: '庄严肃穆的纪念空间',
      ),
    ),
    Hero(
      id: 'qiu-shaoyun',
      name: '邱少云',
      nameEn: 'Qiu Shaoyun',
      lifeSpan: '1926-1952',
      title: '抗美援朝英雄',
      titleEn: 'Korean War Hero',
      description: '中国人民志愿军战士，在朝鲜战争中为了不暴露部队位置，在烈火中坚持潜伏，直至牺牲，展现了崇高的革命精神。',
      detailedBio: '''
邱少云，1926年出生于重庆市铜梁县，中国人民志愿军战士。

英雄事迹：
• 1952年10月，参加朝鲜平康前线391高地战斗
• 在潜伏中被敌人燃烧弹击中，全身着火
• 为了不暴露部队，强忍剧痛，一动不动
• 直至壮烈牺牲，保证了整个战斗的胜利

邱少云烈士用生命诠释了什么是纪律，什么是忠诚。他的英勇事迹成为中国军人的楷模。''',
      imageUrl: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&h=400&fit=crop&crop=face',
      category: 'military',
      nationality: '中国',
      quotes: [
        '纪律重于生命。',
        '为了胜利，向我开炮！'
      ],
      scene: HeroScene(
        category: 'chinese-traditional',
        name: '烈士陵园',
        description: '青松翠柏，英魂永存',
      ),
    ),
    
    // International Heroes
    Hero(
      id: 'martin-luther-king',
      name: '马丁·路德·金',
      nameEn: 'Martin Luther King Jr.',
      lifeSpan: '1929-1968',
      title: '民权运动领袖',
      titleEn: 'Civil Rights Leader',
      description: '美国民权运动领袖，以非暴力抗争争取种族平等。他的"我有一个梦想"演讲激励了全世界追求平等和正义的人们。',
      detailedBio: '''
马丁·路德·金，1929年1月15日出生于美国佐治亚州亚特兰大。

主要成就：
• 领导蒙哥马利公交车抵制运动
• 1963年组织华盛顿大游行，发表著名的'我有一个梦想'演讲
• 1964年获得诺贝尔和平奖，时年35岁
• 推动美国通过《民权法案》和《投票权法案》

1968年4月4日，马丁·路德·金在田纳西州孟菲斯遇刺身亡。他的非暴力抗争理念影响了全世界的民权运动。''',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/05/Martin_Luther_King%2C_Jr..jpg/220px-Martin_Luther_King%2C_Jr..jpg',
      category: 'civil',
      nationality: '美国',
      quotes: [
        'I have a dream that one day this nation will rise up and live out the true meaning of its creed.',
        'Injustice anywhere is a threat to justice everywhere.',
        'The time is always right to do what is right.'
      ],
      scene: HeroScene(
        category: 'christian',
        name: '和平教堂',
        description: '象征着信仰与正义的力量',
      ),
    ),
    Hero(
      id: 'nelson-mandela',
      name: '纳尔逊·曼德拉',
      nameEn: 'Nelson Mandela',
      lifeSpan: '1918-2013',
      title: '反种族隔离斗士',
      titleEn: 'Anti-Apartheid Revolutionary',
      description: '南非反种族隔离革命家、政治家。历经27年监禁后，成为南非首位黑人总统，致力于种族和解与民主建设。',
      detailedBio: '''
纳尔逊·曼德拉，1918年7月18日出生于南非特兰斯凯。

主要成就：
• 创建非洲人国民大会青年联盟
• 因反种族隔离活动被监禁27年
• 1990年获释后推动和平过渡
• 1994年当选南非首位黑人总统
• 建立真相与和解委员会，促进种族和解

曼德拉用宽容和智慧引领南非走向民主，成为全世界和平与正义的象征。2013年12月5日在约翰内斯堡去世。''',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/02/Nelson_Mandela_1994.jpg/220px-Nelson_Mandela_1994.jpg',
      category: 'civil',
      nationality: '南非',
      quotes: [
        "It always seems impossible until it's done.",
        'Education is the most powerful weapon which you can use to change the world.',
        "For to be free is not merely to cast off one's chains, but to live in a way that respects and enhances the freedom of others."
      ],
      scene: HeroScene(
        category: 'nature',
        name: '自由之地',
        description: '广袤的非洲大地，象征着自由与希望',
      ),
    ),
    Hero(
      id: 'marie-curie',
      name: '玛丽·居里',
      nameEn: 'Marie Curie',
      lifeSpan: '1867-1934',
      title: '诺贝尔奖获得者',
      titleEn: 'Nobel Prize Winner',
      description: '波兰裔法国科学家，首位获得诺贝尔奖的女性，也是唯一在两个不同科学领域获得诺贝尔奖的人。她的研究开创了放射性理论。',
      detailedBio: '''
玛丽·居里，1867年11月7日出生于波兰华沙。

主要成就：
• 发现钋和镭两种放射性元素
• 1903年获诺贝尔物理学奖（与丈夫皮埃尔·居里共同获得）
• 1911年获诺贝尔化学奖
• 创立居里研究所
• 第一次世界大战期间，推动X射线在医疗中的应用

居里夫人不仅是杰出的科学家，更是女性追求科学事业的先驱。1934年7月4日因长期接触放射性物质导致的恶性贫血去世。''',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/7e/Marie_Curie_c1920.jpg/220px-Marie_Curie_c1920.jpg',
      category: 'science',
      nationality: '波兰/法国',
      quotes: [
        'Nothing in life is to be feared, it is only to be understood.',
        'Be less curious about people and more curious about ideas.',
        'Life is not easy for any of us. But what of that? We must have perseverance.'
      ],
      scene: HeroScene(
        category: 'modern',
        name: '科学殿堂',
        description: '实验室环境，象征着科学探索的精神',
      ),
    ),
    Hero(
      id: 'mahatma-gandhi',
      name: '圣雄甘地',
      nameEn: 'Mahatma Gandhi',
      lifeSpan: '1869-1948',
      title: '非暴力抵抗先驱',
      titleEn: 'Pioneer of Nonviolent Resistance',
      description: '印度独立运动领袖，通过非暴力不合作运动领导印度获得独立。他的理念影响了全球的民权和自由运动。',
      detailedBio: '''
莫罕达斯·卡拉姆昌德·甘地，1869年10月2日出生于印度波尔班达尔。

主要成就：
• 创立'非暴力不合作'运动理念
• 领导印度独立运动
• 多次绝食抗议，推动社会改革
• 倡导宗教和谐与种姓平等
• 影响了全世界的民权运动

1948年1月30日，甘地在新德里被刺杀。他的非暴力理念成为世界和平运动的重要思想基础。''',
      imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/7a/Mahatma-Gandhi%2C_studio%2C_1931.jpg/220px-Mahatma-Gandhi%2C_studio%2C_1931.jpg',
      category: 'civil',
      nationality: '印度',
      quotes: [
        'Be the change you wish to see in the world.',
        'An eye for an eye will only make the whole world blind.',
        'The weak can never forgive. Forgiveness is the attribute of the strong.'
      ],
      scene: HeroScene(
        category: 'buddhist',
        name: '和平圣地',
        description: '宁静祥和的精神空间',
      ),
    ),
  ];

  static Hero? getHeroById(String id) {
    final foundHeroes = heroes.where((hero) => hero.id == id);
    return foundHeroes.isEmpty ? null : foundHeroes.first;
  }

  static List<Hero> getHeroesByCategory(String category) {
    if (category == 'all') {
      return heroes;
    }
    return heroes.where((hero) => hero.category == category).toList();
  }

  static List<Hero> getHeroesByNationality(String nationality) {
    if (nationality == 'all') {
      return heroes;
    }
    return heroes.where((hero) => hero.nationality == nationality).toList();
  }
}
