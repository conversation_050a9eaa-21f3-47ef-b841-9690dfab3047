/// 示例数据类，用于开发和调试阶段提供模拟数据
class SampleData {
  /// 示例纪念空间数据
  static const List<Map<String, dynamic>> memorialSpaces = [
    {
      'id': 'memorial_0',
      'name': '张三',
      'birthDate': '1950年3月15日',
      'deathDate': '2020年8月22日',
      'birthYear': '1950',
      'deathYear': '2020',
      'description': '慈祥的父亲，勤劳的园丁。一生致力于教育事业，培养了无数学生。他的智慧和爱心将永远伴随我们。',
      'avatar': 'assets/images/avatar_1.svg',
      'photos': [
        {
          'url': 'assets/images/memorial_photo_1.svg',
          'caption': '与家人的温馨时光',
          'date': '2019年春节'
        },
        {
          'url': 'assets/images/memorial_photo_2.svg',
          'caption': '在花园里的快乐时光',
          'date': '2018年夏天'
        },
      ],
      'timeline': [
        {
          'year': '1950',
          'event': '出生于江苏南京',
          'description': '在一个书香门第的家庭中出生'
        },
        {
          'year': '1975',
          'event': '大学毕业',
          'description': '从南京师范大学毕业，开始教师生涯'
        },
        {
          'year': '1978',
          'event': '结婚成家',
          'description': '与相爱的人步入婚姻殿堂'
        },
        {
          'year': '1980',
          'event': '长子出生',
          'description': '家庭添了新成员，开始为人父'
        },
        {
          'year': '1995',
          'event': '获得优秀教师奖',
          'description': '因教学成绩突出获得市级优秀教师称号'
        },
        {
          'year': '2010',
          'event': '退休',
          'description': '结束35年的教师生涯，开始享受退休生活'
        },
        {
          'year': '2020',
          'event': '安详离世',
          'description': '在家人的陪伴下安详离世，享年70岁'
        },
      ],
      'type': '父亲',
      'relationship': '父子关系'
    },
    {
      'id': 'memorial_1',
      'name': '李四',
      'birthDate': '1945年7月8日',
      'deathDate': '2018年12月3日',
      'birthYear': '1945',
      'deathYear': '2018',
      'description': '温柔的母亲，贤惠的妻子。她用无私的爱照顾着整个家庭，是我们心中永远的港湾。',
      'avatar': 'assets/images/avatar_2.svg',
      'photos': [
        {
          'url': 'assets/images/memorial_photo_2.svg',
          'caption': '在厨房忙碌的身影',
          'date': '2017年中秋节'
        },
        {
          'url': 'assets/images/memorial_photo_3.svg',
          'caption': '与孙子孙女的快乐时光',
          'date': '2016年儿童节'
        },
      ],
      'timeline': [
        {
          'year': '1945',
          'event': '出生于上海',
          'description': '在繁华的上海滩出生'
        },
        {
          'year': '1968',
          'event': '参加工作',
          'description': '在纺织厂开始工作生涯'
        },
        {
          'year': '1970',
          'event': '结婚',
          'description': '与心爱的人组建家庭'
        },
        {
          'year': '1972',
          'event': '女儿出生',
          'description': '迎来了家庭的第一个孩子'
        },
        {
          'year': '1985',
          'event': '升任车间主任',
          'description': '因工作出色被提升为车间主任'
        },
        {
          'year': '2000',
          'event': '退休',
          'description': '结束32年的工作生涯'
        },
        {
          'year': '2018',
          'event': '安详离世',
          'description': '在睡梦中安详离世，享年73岁'
        },
      ],
      'type': '母亲',
      'relationship': '母子关系'
    },
    {
      'id': 'memorial_2',
      'name': '王五',
      'birthDate': '1960年11月20日',
      'deathDate': '2022年5月15日',
      'birthYear': '1960',
      'deathYear': '2022',
      'description': '亲爱的朋友，忠诚的伙伴。我们一起度过了许多美好的时光，他的友谊是我人生中最珍贵的财富。',
      'avatar': 'assets/images/avatar_3.svg',
      'photos': [
        {
          'url': 'assets/images/memorial_photo_1.svg',
          'caption': '一起旅行的美好回忆',
          'date': '2021年国庆节'
        },
        {
          'url': 'assets/images/memorial_photo_3.svg',
          'caption': '在咖啡厅的深谈',
          'date': '2020年春天'
        },
      ],
      'timeline': [
        {
          'year': '1960',
          'event': '出生于北京',
          'description': '在古都北京出生'
        },
        {
          'year': '1982',
          'event': '大学毕业',
          'description': '从北京理工大学计算机系毕业'
        },
        {
          'year': '1985',
          'event': '创业',
          'description': '与朋友一起创办了第一家公司'
        },
        {
          'year': '1990',
          'event': '结婚',
          'description': '与大学同学结为夫妻'
        },
        {
          'year': '2000',
          'event': '公司上市',
          'description': '经过多年努力，公司成功上市'
        },
        {
          'year': '2015',
          'event': '退休',
          'description': '将公司交给下一代，开始享受退休生活'
        },
        {
          'year': '2022',
          'event': '因病离世',
          'description': '因突发疾病离世，享年62岁'
        },
      ],
      'type': '朋友',
      'relationship': '友谊关系'
    },
  ];

  /// 示例最新动态数据
  static const List<Map<String, dynamic>> latestUpdates = [
    {
      'id': '1',
      'title': '张三纪念空间新增照片',
      'description': '家人上传了3张珍贵的老照片',
      'time': '2小时前',
      'type': 'photo',
      'memorialId': 'memorial_0'
    },
    {
      'id': '2',
      'title': '李四生日纪念',
      'description': '今天是李四的生日，让我们一起缅怀',
      'time': '1天前',
      'type': 'birthday',
      'memorialId': 'memorial_1'
    },
    {
      'id': '3',
      'title': '王五纪念空间访问量突破1000',
      'description': '感谢大家对王五的怀念',
      'time': '3天前',
      'type': 'milestone',
      'memorialId': 'memorial_2'
    },
  ];

  /// 示例家族成员数据
  static const List<Map<String, dynamic>> familyMembers = [
    {
      'id': 'member_1',
      'name': '张三',
      'avatar': 'assets/images/avatar_1.svg',
      'relationship': '父亲',
      'birthYear': '1950',
      'deathYear': '2020'
    },
    {
      'id': 'member_2',
      'name': '李四',
      'avatar': 'assets/images/avatar_2.svg',
      'relationship': '母亲',
      'birthYear': '1952',
      'deathYear': '2018'
    },
    {
      'id': 'member_3',
      'name': '王五',
      'avatar': 'assets/images/avatar_3.svg',
      'relationship': '祖父',
      'birthYear': '1925',
      'deathYear': '2015'
    },
    {
      'id': 'member_4',
      'name': '赵六',
      'avatar': 'assets/images/avatar_1.svg',
      'relationship': '祖母',
      'birthYear': '1928',
      'deathYear': '2017'
    },
    {
      'id': 'member_5',
      'name': '孙七',
      'avatar': 'assets/images/avatar_2.svg',
      'relationship': '叔叔',
      'birthYear': '1955',
      'deathYear': '2021'
    },
    {
      'id': 'member_6',
      'name': '周八',
      'avatar': 'assets/images/avatar_3.svg',
      'relationship': '姑姑',
      'birthYear': '1958',
      'deathYear': '2019'
    },
  ];

  /// 示例家族树数据
  static const Map<String, dynamic> familyTree = {
    'id': 'family_1',
    'name': '张氏家族',
    'members': 12,
    'generations': 4,
    'description': '一个有着深厚文化底蕴的书香门第',
    'lastUpdated': '2024年1月15日'
  };

  /// 根据ID获取纪念空间数据
  static Map<String, dynamic>? getMemorialById(String id) {
    for (final memorial in memorialSpaces) {
      if (memorial['id'] == id) {
        return memorial;
      }
    }
    return null;
  }

  /// 获取随机纪念空间数据（用于推荐）
  static Map<String, dynamic> getRandomMemorial() {
    final random = DateTime.now().millisecondsSinceEpoch % memorialSpaces.length;
    return memorialSpaces[random];
  }
}
