import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../services/ai_service.dart';

/// AI服务状态管理
class AIProvider extends ChangeNotifier {
  final AIService _aiService;
  
  // 服务状态
  AIHealthStatus? _healthStatus;
  AIModelsResponse? _modelsResponse;
  Map<String, dynamic>? _supportedLanguages;
  
  // 任务管理
  final Map<String, AITaskStatus> _tasks = {};
  final Map<String, Timer> _taskPollers = {};
  
  // 加载状态
  bool _isLoading = false;
  String? _error;

  AIProvider(this._aiService);

  // Getters
  AIHealthStatus? get healthStatus => _healthStatus;
  AIModelsResponse? get modelsResponse => _modelsResponse;
  Map<String, dynamic>? get supportedLanguages => _supportedLanguages;
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<AITaskStatus> get tasks => _tasks.values.toList()
    ..sort((a, b) => (b.createdAt ?? DateTime.now()).compareTo(a.createdAt ?? DateTime.now()));

  /// 设置认证令牌
  void setAuthToken(String token) {
    _aiService.setAuthToken(token);
  }

  /// 检查服务健康状态
  Future<void> checkHealth() async {
    try {
      _setLoading(true);
      _healthStatus = await _aiService.checkHealth();
      _clearError();
    } catch (e) {
      _setError('检查服务状态失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 获取可用模型
  Future<void> loadModels() async {
    try {
      _setLoading(true);
      _modelsResponse = await _aiService.getAvailableModels();
      _clearError();
    } catch (e) {
      _setError('获取模型失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 获取支持的语言
  Future<void> loadSupportedLanguages() async {
    try {
      _setLoading(true);
      _supportedLanguages = await _aiService.getSupportedLanguages();
      _clearError();
    } catch (e) {
      _setError('获取支持语言失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 批量处理照片
  Future<String?> batchProcessPhotos({
    required List<File> files,
    required String operation,
    int scale = 4,
    bool faceEnhance = true,
  }) async {
    try {
      _setLoading(true);
      final response = await _aiService.batchProcessPhotos(
        files: files,
        operation: operation,
        scale: scale,
        faceEnhance: faceEnhance,
      );
      
      if (response.success) {
        // 开始轮询任务状态
        _startTaskPolling(response.taskId);
        _clearError();
        return response.taskId;
      } else {
        _setError(response.message);
        return null;
      }
    } catch (e) {
      _setError('批量处理失败: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// 语音克隆
  Future<String?> cloneVoice({
    required File voiceFile,
    required String text,
    String language = 'zh',
    bool enhanceQuality = true,
    double speed = 1.0,
    double pitchShift = 0.0,
  }) async {
    try {
      _setLoading(true);
      final response = await _aiService.cloneVoiceEnhanced(
        voiceFile: voiceFile,
        text: text,
        language: language,
        enhanceQuality: enhanceQuality,
        speed: speed,
        pitchShift: pitchShift,
      );
      
      if (response.success) {
        // 开始轮询任务状态
        _startTaskPolling(response.taskId);
        _clearError();
        return response.taskId;
      } else {
        _setError(response.message);
        return null;
      }
    } catch (e) {
      _setError('语音克隆失败: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// 验证语音样本
  Future<VoiceValidationResult?> validateVoiceSample({
    required File voiceFile,
    String language = 'zh',
  }) async {
    try {
      _setLoading(true);
      final result = await _aiService.validateVoiceSample(
        voiceFile: voiceFile,
        language: language,
      );
      _clearError();
      return result;
    } catch (e) {
      _setError('语音验证失败: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// 获取任务状态
  Future<AITaskStatus?> getTaskStatus(String taskId) async {
    try {
      final status = await _aiService.getTaskStatus(taskId);
      _tasks[taskId] = status;
      notifyListeners();
      return status;
    } catch (e) {
      _setError('获取任务状态失败: $e');
      return null;
    }
  }

  /// 取消任务
  Future<bool> cancelTask(String taskId) async {
    try {
      await _aiService.cancelTask(taskId);
      
      // 停止轮询
      _stopTaskPolling(taskId);
      
      // 更新任务状态
      if (_tasks.containsKey(taskId)) {
        final task = _tasks[taskId]!;
        _tasks[taskId] = AITaskStatus(
          taskId: task.taskId,
          taskType: task.taskType,
          status: 'cancelled',
          progress: task.progress,
          createdAt: task.createdAt,
          startedAt: task.startedAt,
          completedAt: DateTime.now(),
        );
        notifyListeners();
      }
      
      _clearError();
      return true;
    } catch (e) {
      _setError('取消任务失败: $e');
      return false;
    }
  }

  /// 加载用户任务列表
  Future<void> loadUserTasks({String? status}) async {
    try {
      _setLoading(true);
      final tasks = await _aiService.getUserTasks(status: status);
      
      // 更新任务映射
      for (final task in tasks) {
        _tasks[task.taskId] = task;
        
        // 如果任务正在处理中，开始轮询
        if (task.isProcessing || task.isPending) {
          _startTaskPolling(task.taskId);
        }
      }
      
      _clearError();
    } catch (e) {
      _setError('加载任务列表失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 开始任务轮询
  void _startTaskPolling(String taskId) {
    // 如果已经在轮询，先停止
    _stopTaskPolling(taskId);
    
    // 开始新的轮询
    _taskPollers[taskId] = Timer.periodic(
      const Duration(seconds: 3),
      (timer) async {
        final status = await getTaskStatus(taskId);
        
        if (status != null && (status.isCompleted || status.isFailed || status.isCancelled)) {
          // 任务完成，停止轮询
          _stopTaskPolling(taskId);
        }
      },
    );
  }

  /// 停止任务轮询
  void _stopTaskPolling(String taskId) {
    _taskPollers[taskId]?.cancel();
    _taskPollers.remove(taskId);
  }

  /// 获取特定任务
  AITaskStatus? getTask(String taskId) {
    return _tasks[taskId];
  }

  /// 获取正在进行的任务
  List<AITaskStatus> get activeTasks {
    return _tasks.values
        .where((task) => task.isProcessing || task.isPending)
        .toList();
  }

  /// 获取已完成的任务
  List<AITaskStatus> get completedTasks {
    return _tasks.values
        .where((task) => task.isCompleted)
        .toList();
  }

  /// 获取失败的任务
  List<AITaskStatus> get failedTasks {
    return _tasks.values
        .where((task) => task.isFailed)
        .toList();
  }

  /// 清除所有任务
  void clearTasks() {
    // 停止所有轮询
    for (final timer in _taskPollers.values) {
      timer.cancel();
    }
    _taskPollers.clear();
    _tasks.clear();
    notifyListeners();
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误
  void _clearError() {
    _error = null;
    notifyListeners();
  }

  /// 初始化
  Future<void> initialize() async {
    await Future.wait([
      checkHealth(),
      loadModels(),
      loadSupportedLanguages(),
      loadUserTasks(),
    ]);
  }

  @override
  void dispose() {
    // 停止所有轮询
    for (final timer in _taskPollers.values) {
      timer.cancel();
    }
    _taskPollers.clear();
    
    // 释放AI服务资源
    _aiService.dispose();
    
    super.dispose();
  }
}

/// AI任务类型枚举
enum AITaskType {
  photoRestore('photo_restore', '照片修复'),
  photoColorize('photo_colorize', '照片上色'),
  photoEnhance('photo_enhance', '照片增强'),
  photoRemoveBg('photo_remove_bg', '背景移除'),
  voiceClone('voice_clone', '语音克隆'),
  batchProcess('batch_process', '批量处理');

  const AITaskType(this.value, this.displayName);
  
  final String value;
  final String displayName;
  
  static AITaskType? fromValue(String value) {
    for (final type in AITaskType.values) {
      if (type.value == value) {
        return type;
      }
    }
    return null;
  }
}

/// AI任务状态枚举
enum AITaskStatusType {
  pending('pending', '等待中'),
  processing('processing', '处理中'),
  completed('completed', '已完成'),
  failed('failed', '失败'),
  cancelled('cancelled', '已取消');

  const AITaskStatusType(this.value, this.displayName);
  
  final String value;
  final String displayName;
  
  static AITaskStatusType? fromValue(String value) {
    for (final status in AITaskStatusType.values) {
      if (status.value == value) {
        return status;
      }
    }
    return null;
  }
}
