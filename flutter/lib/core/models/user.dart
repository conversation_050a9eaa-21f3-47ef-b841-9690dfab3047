// 用户模型
import 'package:flutter/foundation.dart';

@immutable
class User {
  const User({
    required this.id,
    required this.username,
    required this.email,
    required this.isActive,
    required this.isVerified,
    required this.isSuperuser,
    required this.role,
    required this.createdAt,
    required this.updatedAt,
    this.fullName,
    this.avatarUrl,
    this.bio,
    this.lastLoginAt,
  });

  // 从JSON创建User对象
  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json['id'] as String,
        username: json['username'] as String,
        email: json['email'] as String,
        fullName: json['full_name'] as String?,
        avatarUrl: json['avatar_url'] as String?,
        bio: json['bio'] as String?,
        isActive: json['is_active'] as bool? ?? true,
        isVerified: json['is_verified'] as bool? ?? false,
        isSuperuser: json['is_superuser'] as bool? ?? false,
        role: json['role'] as String? ?? 'user',
        createdAt: DateTime.parse(json['created_at'] as String),
        updatedAt: DateTime.parse(json['updated_at'] as String),
        lastLoginAt: json['last_login_at'] != null
            ? DateTime.parse(json['last_login_at'] as String)
            : null,
      );

  final String id;
  final String username;
  final String email;
  final String? fullName;
  final String? avatarUrl;
  final String? bio;
  final bool isActive;
  final bool isVerified;
  final bool isSuperuser;
  final String role;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastLoginAt;

  // 转换为JSON
  Map<String, dynamic> toJson() => {
        'id': id,
        'username': username,
        'email': email,
        'full_name': fullName,
        'avatar_url': avatarUrl,
        'bio': bio,
        'is_active': isActive,
        'is_verified': isVerified,
        'is_superuser': isSuperuser,
        'role': role,
        'created_at': createdAt.toIso8601String(),
        'updated_at': updatedAt.toIso8601String(),
        'last_login_at': lastLoginAt?.toIso8601String(),
      };

  // 复制并修改某些字段
  User copyWith({
    String? id,
    String? username,
    String? email,
    String? fullName,
    String? avatarUrl,
    String? bio,
    bool? isActive,
    bool? isVerified,
    bool? isSuperuser,
    String? role,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
  }) =>
      User(
        id: id ?? this.id,
        username: username ?? this.username,
        email: email ?? this.email,
        fullName: fullName ?? this.fullName,
        avatarUrl: avatarUrl ?? this.avatarUrl,
        bio: bio ?? this.bio,
        isActive: isActive ?? this.isActive,
        isVerified: isVerified ?? this.isVerified,
        isSuperuser: isSuperuser ?? this.isSuperuser,
        role: role ?? this.role,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      );

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'User(id: $id, username: $username, email: $email)';
}
