// 认证状态模型
import 'package:flutter/foundation.dart';

import 'package:memorial/core/models/user.dart';

@immutable
class AuthState {
  const AuthState({
    required this.isAuthenticated,
    required this.isLoading,
    this.user,
    this.token,
    this.refreshToken,
    this.error,
    this.tokenExpiresAt,
  });

  // 初始状态
  factory AuthState.initial() => const AuthState(
        isAuthenticated: false,
        isLoading: false,
      );

  final User? user;
  final String? token;
  final String? refreshToken;
  final bool isAuthenticated;
  final bool isLoading;
  final String? error;
  final DateTime? tokenExpiresAt;

  // 复制并修改状态
  AuthState copyWith({
    User? user,
    String? token,
    String? refreshToken,
    bool? isAuthenticated,
    bool? isLoading,
    String? error,
    DateTime? tokenExpiresAt,
    bool clearUser = false,
    bool clearToken = false,
    bool clearRefreshToken = false,
    bool clearError = false,
    bool clearTokenExpiresAt = false,
  }) =>
      AuthState(
        user: clearUser ? null : (user ?? this.user),
        token: clearToken ? null : (token ?? this.token),
        refreshToken:
            clearRefreshToken ? null : (refreshToken ?? this.refreshToken),
        isAuthenticated: isAuthenticated ?? this.isAuthenticated,
        isLoading: isLoading ?? this.isLoading,
        error: clearError ? null : (error ?? this.error),
        tokenExpiresAt:
            clearTokenExpiresAt ? null : (tokenExpiresAt ?? this.tokenExpiresAt),
      );

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is AuthState &&
        other.user == user &&
        other.token == token &&
        other.refreshToken == refreshToken &&
        other.isAuthenticated == isAuthenticated &&
        other.isLoading == isLoading &&
        other.error == error &&
        other.tokenExpiresAt == tokenExpiresAt;
  }

  @override
  int get hashCode => Object.hash(
        user,
        token,
        refreshToken,
        isAuthenticated,
        isLoading,
        error,
        tokenExpiresAt,
      );

  @override
  String toString() =>
      'AuthState(user: $user, isAuthenticated: $isAuthenticated, isLoading: $isLoading, error: $error)';

}
