// 英雄模型
class Hero {
  const Hero({
    required this.id,
    required this.name,
    required this.lifeSpan,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.category,
    required this.nationality,
    required this.quotes,
    this.nameEn,
    this.titleEn,
    this.descriptionEn,
    this.detailedBio,
    this.scene,
  });

  factory Hero.fromJson(Map<String, dynamic> json) => Hero(
        id: json['id'] as String,
        name: json['name'] as String,
        nameEn: json['nameEn'] as String?,
        lifeSpan: json['lifeSpan'] as String,
        title: json['title'] as String,
        titleEn: json['titleEn'] as String?,
        description: json['description'] as String,
        descriptionEn: json['descriptionEn'] as String?,
        detailedBio: json['detailedBio'] as String?,
        imageUrl: json['imageUrl'] as String,
        category: json['category'] as String,
        nationality: json['nationality'] as String,
        quotes: List<String>.from((json['quotes'] as List<dynamic>?) ?? []),
        scene: json['scene'] != null 
            ? HeroScene.fromJson(json['scene'] as Map<String, dynamic>) 
            : null,
      );

  final String id;
  final String name;
  final String? nameEn;
  final String lifeSpan;
  final String title;
  final String? titleEn;
  final String description;
  final String? descriptionEn;
  final String? detailedBio;
  final String imageUrl;
  final String category;
  final String nationality;
  final List<String> quotes;
  final HeroScene? scene;

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'nameEn': nameEn,
        'lifeSpan': lifeSpan,
        'title': title,
        'titleEn': titleEn,
        'description': description,
        'descriptionEn': descriptionEn,
        'detailedBio': detailedBio,
        'imageUrl': imageUrl,
        'category': category,
        'nationality': nationality,
        'quotes': quotes,
        'scene': scene?.toJson(),
      };
}

class HeroScene {
  const HeroScene({
    required this.category,
    required this.name,
    required this.description,
  });

  factory HeroScene.fromJson(Map<String, dynamic> json) => HeroScene(
        category: json['category'] as String,
        name: json['name'] as String,
        description: json['description'] as String,
      );

  final String category;
  final String name;
  final String description;

  Map<String, dynamic> toJson() => {
        'category': category,
        'name': name,
        'description': description,
      };
}
