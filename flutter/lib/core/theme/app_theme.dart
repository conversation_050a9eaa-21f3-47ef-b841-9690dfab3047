import 'package:flutter/cupertino.dart';
import 'package:memorial/core/theme/app_colors.dart';

/// 应用主题配置
/// 提供统一的主题样式和常量定义
class AppTheme {
  // 私有构造函数，防止实例化
  AppTheme._();

  // 边距和间距
  static const double paddingSmall = 8;
  static const double paddingMedium = 16;
  static const double paddingLarge = 24;
  static const double paddingXLarge = 32;

  // 圆角半径
  static const double radiusSmall = 8;
  static const double radiusMedium = 12;
  static const double radiusLarge = 16;

  // 图标尺寸
  static const double iconSizeSmall = 16;
  static const double iconSizeMedium = 20;
  static const double iconSizeLarge = 24;
  static const double iconSizeXLarge = 32;
  static const double iconSizeXXLarge = 48;

  // 字体大小
  static const double fontSizeSmall = 11;
  static const double fontSizeMedium = 12;
  static const double fontSizeRegular = 14;
  static const double fontSizeLarge = 16;
  static const double fontSizeXLarge = 24;

  // 边框宽度
  static const double borderWidthThin = 1;
  static const double borderWidthMedium = 2;

  // 阴影
  static const List<BoxShadow> cardShadow = [
    BoxShadow(
      color: CupertinoColors.systemGrey,
      blurRadius: 4,
      offset: Offset(0, 2),
    ),
  ];

  // 文本样式
  static TextStyle get titleLarge => const TextStyle(
    fontSize: fontSizeXLarge,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
  );

  static TextStyle get titleMedium => const TextStyle(
    fontSize: fontSizeLarge,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
  );

  static TextStyle get bodyLarge => const TextStyle(
    fontSize: fontSizeLarge,
    color: AppColors.textPrimary,
  );

  static TextStyle get bodyMedium => const TextStyle(
    fontSize: fontSizeRegular,
    color: AppColors.textPrimary,
  );

  static TextStyle get bodySmall => const TextStyle(
    fontSize: fontSizeMedium,
    color: AppColors.textSecondary,
  );

  static TextStyle get caption => const TextStyle(
    fontSize: fontSizeSmall,
    color: AppColors.textSecondary,
  );
}
