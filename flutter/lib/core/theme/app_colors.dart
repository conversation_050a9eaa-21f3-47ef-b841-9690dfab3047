import 'package:flutter/cupertino.dart';

/// 应用颜色主题定义
/// 基于Figma设计规范的颜色系统，支持 iOS 风格的 Cupertino 设计
/// 优先实现浅色主题 (Light Mode)
class AppColors {
  // 私有构造函数，防止实例化
  AppColors._();

  // 主色调 (Primary Colors) - 基于Figma设计
  static const Color pureWhite = Color(0xFFFFFFFF); // 纯白背景
  static const Color deepCharcoal = Color(0xFF1E1E1E); // 深炭黑文字
  static const Color skyBlue = Color(0xFF87CEEB); // 天空蓝渐变起点
  static const Color warmGoldenBrown = Color(0xFFD69E2E); // 暖金棕渐变终点

  // 辅助色 (Secondary Colors)
  static const Color vibrantOrange = Color(0xFFFF6B35); // 活力橙红
  static const Color softPink = Color(0xFFFF6B6B); // 柔和粉红
  static const Color neutralGray = Color(0xFF757575); // 中性灰

  // 功能卡片色彩 (Feature Card Colors)
  static const Color lightBlueCard = Color(0xFFE3F2FD); // 浅蓝卡片
  static const Color lightGreenCard = Color(0xFFE8F5E8); // 浅绿卡片
  static const Color lightPurpleCard = Color(0xFFF3E5F5); // 浅紫卡片
  static const Color lightOrangeCard = Color(0xFFFFF3E0); // 浅橙卡片
  static const Color lightPinkCard = Color(0xFFFCE4EC); // 浅粉卡片
  static const Color lightYellowCard = Color(0xFFFFFDE7); // 浅黄卡片

  // 中性色 (Neutral Colors)
  static const Color lightGrayBorder = Color(0xFFE0E0E0); // 浅灰边框
  static const Color mediumGrayText = Color(0xFF999999); // 中灰文字
  static const Color darkGrayText = Color(0xFF666666); // 深灰文字

  // 语义色 (Semantic Colors)
  static const Color success = Color(0xFF4CAF50); // 成功绿色
  static const Color warning = Color(0xFFFF9800); // 警告橙色
  static const Color error = Color(0xFFF44336); // 错误红色

  // 应用主题色定义 - 兼容现有代码
  static const Color primary = softPink; // 主要强调色
  static const Color secondary = vibrantOrange; // 次要强调色

  // 文本颜色
  static const Color textPrimary = deepCharcoal; // 主要文字
  static const Color textSecondary = neutralGray; // 次要文字
  static const Color textTertiary = mediumGrayText; // 三级文字

  // 背景颜色
  static const Color background = pureWhite; // 主背景
  static const Color cardBackground = pureWhite; // 卡片背景

  // 图标颜色规范
  static const Color iconActive = softPink; // 激活状态图标
  static const Color iconInactive = neutralGray; // 非激活状态图标
  static const Color iconImportant = vibrantOrange; // 重要图标
  static const Color iconWhite = pureWhite; // 白色图标
  static const Color iconRed = error; // 红色图标
  static const Color iconSecondary = vibrantOrange; // 次要图标
  static const Color iconGreen = success; // 绿色图标

  // 分割线和边框
  static const Color separator = lightGrayBorder;
  static const Color border = lightGrayBorder;

  // Hero区域渐变色
  static const List<Color> heroGradient = [skyBlue, warmGoldenBrown];
  static const Color heroGradientStart = skyBlue;
  static const Color heroGradientEnd = warmGoldenBrown;

  // 功能卡片颜色 - 基于Figma设计
  static const Color featureGreen = Color(0xFF4CAF50);
  static const Color featureBlue = Color(0xFF2196F3);
  static const Color featureOrange = Color(0xFFFF9800);
  static const Color featurePurple = Color(0xFF9C27B0);
  static const Color featurePink = Color(0xFFE91E63);
  static const Color featureTeal = Color(0xFF607D8B);

  // 聊天界面颜色
  static const Color chatBubbleLeft = Color(0xFF4CAF50);
  static const Color chatBubbleRight = Color(0xFF9E9E9E);
  static const Color avatarBackground = Color(0xFFE0E0E0);
  static const Color inputBackground = Color(0xFF9E9E9E);

  // 强调色
  static const Color accent = vibrantOrange;

  // 阴影和叠加层
  static const Color shadowLight = Color(0x1A000000); // 10% 黑色透明度
  static const Color overlayDark = Color(0x4D000000); // 30% 黑色透明度

  // 透明度变体
  static Color primaryWithOpacity(double opacity) => primary.withValues(alpha: opacity);
  static Color secondaryWithOpacity(double opacity) => secondary.withValues(alpha: opacity);
  static Color blackWithOpacity(double opacity) => deepCharcoal.withValues(alpha: opacity);
  static Color whiteWithOpacity(double opacity) => pureWhite.withValues(alpha: opacity);
}
