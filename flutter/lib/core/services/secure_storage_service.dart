// 安全存储服务
import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class SecureStorageService {
  SecureStorageService._internal();
  
  static const FlutterSecureStorage _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      sharedPreferencesName: 'memorial_secure_prefs',
      preferencesKeyPrefix: 'memorial_',
    ),
    iOptions: IOSOptions(
      groupId: 'group.com.memorial.app',
      accountName: 'memorial_keychain',
    ),
  );

  // 用于额外加密的密钥（基于设备信息生成）
  static const String _encryptionKeyName = 'memorial_encryption_key';
  
  static final SecureStorageService _instance = SecureStorageService._internal();
  static SecureStorageService get instance => _instance;

  late final Encrypter? _encrypter;
  late final IV _iv;
  bool _initialized = false;

  // 初始化加密器
  Future<void> initialize() async {
    if (_initialized) {
      return;
    }

    try {
      // 尝试获取或生成加密密钥
      final keyString = await _storage.read(key: _encryptionKeyName);
      
      if (keyString == null) {
        // 生成新的加密密钥
        final key = Key.fromSecureRandom(32);
        final keyStringNew = key.base64;
        await _storage.write(key: _encryptionKeyName, value: keyStringNew);
        _encrypter = Encrypter(AES(key));
      } else {
        final key = Key.fromBase64(keyString);
        _encrypter = Encrypter(AES(key));
      }

      _iv = IV.fromSecureRandom(16);
      _initialized = true;
    } on Exception catch (e) {
      // 如果加密初始化失败，使用基础安全存储
      print('加密初始化失败，使用基础安全存储: $e');
      _encrypter = null;
      _initialized = true;
    }
  }

  // 安全存储数据
  Future<void> write(String key, String value) async {
    await initialize();
    
    try {
      // 使用双重加密：Flutter Secure Storage + AES加密
      if (_encrypter != null) {
        final encrypted = _encrypter.encrypt(value, iv: _iv);
        await _storage.write(
          key: key,
          value: json.encode({
            'encrypted': encrypted.base64,
            'iv': _iv.base64,
          }),
        );
      } else {
        // 仅使用Flutter Secure Storage
        await _storage.write(key: key, value: value);
      }
    } on Exception catch (e) {
      throw Exception('存储数据失败: $e');
    }
  }

  // 安全读取数据
  Future<String?> read(String key) async {
    await initialize();
    
    try {
      final storedValue = await _storage.read(key: key);
      if (storedValue == null) {
        return null;
      }

      if (_encrypter != null) {
        try {
          // 尝试解密
          final data = json.decode(storedValue) as Map<String, dynamic>;
          final encrypted = Encrypted.fromBase64(data['encrypted'] as String);
          final iv = IV.fromBase64(data['iv'] as String);
          
          return _encrypter.decrypt(encrypted, iv: iv);
        } on Exception catch (e) {
          // 如果解密失败，可能是旧格式数据，直接返回
          print('解密失败，返回原始数据: $e');
          return storedValue;
        }
      } else {
        return storedValue;
      }
    } on Exception catch (e) {
      print('读取数据失败: $e');
      return null;
    }
  }

  // 删除指定key的数据
  Future<void> delete(String key) async {
    await initialize();
    await _storage.delete(key: key);
  }

  // 清除所有数据
  Future<void> deleteAll() async {
    await initialize();
    await _storage.deleteAll();
  }

  // 检查key是否存在
  Future<bool> containsKey(String key) async {
    await initialize();
    return _storage.containsKey(key: key);
  }

  // 获取所有keys
  Future<Map<String, String>> readAll() async {
    await initialize();
    return _storage.readAll();
  }

  // Token相关的便捷方法
  Future<void> storeToken(String token) => write('auth_token', token);

  Future<String?> getToken() => read('auth_token');

  Future<void> storeRefreshToken(String refreshToken) => 
      write('refresh_token', refreshToken);

  Future<String?> getRefreshToken() => read('refresh_token');

  Future<void> storeTokenExpiresAt(DateTime expiresAt) => 
      write('token_expires_at', expiresAt.toIso8601String());

  Future<DateTime?> getTokenExpiresAt() async {
    final expiresAtStr = await read('token_expires_at');
    if (expiresAtStr == null) {
      return null;
    }
    
    try {
      return DateTime.parse(expiresAtStr);
    } on Exception catch (e) {
      print('解析过期时间失败: $e');
      return null;
    }
  }

  Future<void> storeUserData(String userData) => write('user_data', userData);

  Future<String?> getUserData() => read('user_data');

  // 清除所有认证数据
  Future<void> clearAuthData() async {
    await Future.wait([
      delete('auth_token'),
      delete('refresh_token'),
      delete('token_expires_at'),
      delete('user_data'),
    ]);
  }

  // 生成数据完整性校验
  String _generateChecksum(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  // 验证数据完整性
  Future<bool> verifyDataIntegrity(String key, String data) async {
    final checksumKey = '${key}_checksum';
    final storedChecksum = await read(checksumKey);
    final currentChecksum = _generateChecksum(data);
    
    return storedChecksum == currentChecksum;
  }

  // 存储数据并生成校验码
  Future<void> writeWithIntegrity(String key, String value) async {
    await write(key, value);
    final checksum = _generateChecksum(value);
    await write('${key}_checksum', checksum);
  }

  // 读取数据并验证完整性
  Future<String?> readWithIntegrity(String key) async {
    final value = await read(key);
    if (value == null) {
      return null;
    }

    final isValid = await verifyDataIntegrity(key, value);
    if (!isValid) {
      print('数据完整性验证失败，删除可能被篡改的数据: $key');
      await delete(key);
      await delete('${key}_checksum');
      return null;
    }

    return value;
  }
}
