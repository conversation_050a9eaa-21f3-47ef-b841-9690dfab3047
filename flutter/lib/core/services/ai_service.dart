import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;

/// AI服务客户端
/// 提供与后端AI服务的通信功能
class AIService {
  static const String _baseUrl = 'http://localhost:8000/api/v1/ai-enhanced';
  
  final http.Client _client;
  String? _authToken;

  AIService({http.Client? client}) : _client = client ?? http.Client();

  /// 设置认证令牌
  void setAuthToken(String token) {
    _authToken = token;
  }

  /// 获取请求头
  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    
    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }
    
    return headers;
  }

  /// 获取多部分请求头
  Map<String, String> get _multipartHeaders {
    final headers = <String, String>{};
    
    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }
    
    return headers;
  }

  /// 检查AI服务健康状态
  Future<AIHealthStatus> checkHealth() async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/health'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return AIHealthStatus.fromJson(data);
      } else {
        throw AIServiceException('健康检查失败: ${response.statusCode}');
      }
    } catch (e) {
      throw AIServiceException('网络连接失败: $e');
    }
  }

  /// 获取可用的AI模型
  Future<AIModelsResponse> getAvailableModels() async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/models'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return AIModelsResponse.fromJson(data);
      } else {
        throw AIServiceException('获取模型失败: ${response.statusCode}');
      }
    } catch (e) {
      throw AIServiceException('获取模型失败: $e');
    }
  }

  /// 批量处理照片
  Future<AITaskResponse> batchProcessPhotos({
    required List<File> files,
    required String operation,
    int scale = 4,
    bool faceEnhance = true,
  }) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl/batch/photo-process'),
      );

      // 添加认证头
      request.headers.addAll(_multipartHeaders);

      // 添加文件
      for (final file in files) {
        final stream = http.ByteStream(file.openRead());
        final length = await file.length();
        final multipartFile = http.MultipartFile(
          'files',
          stream,
          length,
          filename: path.basename(file.path),
        );
        request.files.add(multipartFile);
      }

      // 添加参数
      request.fields['operation'] = operation;
      request.fields['scale'] = scale.toString();
      request.fields['face_enhance'] = faceEnhance.toString();

      final streamedResponse = await _client.send(request);
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return AITaskResponse.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw AIServiceException(errorData['detail'] ?? '批量处理失败');
      }
    } catch (e) {
      throw AIServiceException('批量处理失败: $e');
    }
  }

  /// 增强语音克隆
  Future<AITaskResponse> cloneVoiceEnhanced({
    required File voiceFile,
    required String text,
    String language = 'zh',
    bool enhanceQuality = true,
    double speed = 1.0,
    double pitchShift = 0.0,
  }) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl/voice/clone-enhanced'),
      );

      // 添加认证头
      request.headers.addAll(_multipartHeaders);

      // 添加语音文件
      final stream = http.ByteStream(voiceFile.openRead());
      final length = await voiceFile.length();
      final multipartFile = http.MultipartFile(
        'voice_file',
        stream,
        length,
        filename: path.basename(voiceFile.path),
      );
      request.files.add(multipartFile);

      // 添加参数
      request.fields['text'] = text;
      request.fields['language'] = language;
      request.fields['enhance_quality'] = enhanceQuality.toString();
      request.fields['speed'] = speed.toString();
      request.fields['pitch_shift'] = pitchShift.toString();

      final streamedResponse = await _client.send(request);
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return AITaskResponse.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw AIServiceException(errorData['detail'] ?? '语音克隆失败');
      }
    } catch (e) {
      throw AIServiceException('语音克隆失败: $e');
    }
  }

  /// 验证语音样本
  Future<VoiceValidationResult> validateVoiceSample({
    required File voiceFile,
    String language = 'zh',
  }) async {
    try {
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$_baseUrl/voice/validate-sample'),
      );

      // 添加认证头
      request.headers.addAll(_multipartHeaders);

      // 添加语音文件
      final stream = http.ByteStream(voiceFile.openRead());
      final length = await voiceFile.length();
      final multipartFile = http.MultipartFile(
        'file',
        stream,
        length,
        filename: path.basename(voiceFile.path),
      );
      request.files.add(multipartFile);

      // 添加参数
      request.fields['language'] = language;

      final streamedResponse = await _client.send(request);
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return VoiceValidationResult.fromJson(data);
      } else {
        final errorData = json.decode(response.body);
        throw AIServiceException(errorData['detail'] ?? '语音验证失败');
      }
    } catch (e) {
      throw AIServiceException('语音验证失败: $e');
    }
  }

  /// 获取任务状态
  Future<AITaskStatus> getTaskStatus(String taskId) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/task/$taskId/status'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return AITaskStatus.fromJson(data);
      } else if (response.statusCode == 404) {
        throw AIServiceException('任务不存在');
      } else {
        throw AIServiceException('获取任务状态失败: ${response.statusCode}');
      }
    } catch (e) {
      throw AIServiceException('获取任务状态失败: $e');
    }
  }

  /// 取消任务
  Future<void> cancelTask(String taskId) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl/task/$taskId/cancel'),
        headers: _headers,
      );

      if (response.statusCode != 200) {
        final errorData = json.decode(response.body);
        throw AIServiceException(errorData['detail'] ?? '取消任务失败');
      }
    } catch (e) {
      throw AIServiceException('取消任务失败: $e');
    }
  }

  /// 获取用户任务列表
  Future<List<AITaskStatus>> getUserTasks({
    String? status,
    int limit = 50,
  }) async {
    try {
      final queryParams = <String, String>{
        'limit': limit.toString(),
      };
      
      if (status != null) {
        queryParams['status'] = status;
      }

      final uri = Uri.parse('$_baseUrl/tasks').replace(queryParameters: queryParams);
      final response = await _client.get(uri, headers: _headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final tasks = (data['tasks'] as List)
            .map((task) => AITaskStatus.fromJson(task))
            .toList();
        return tasks;
      } else {
        throw AIServiceException('获取任务列表失败: ${response.statusCode}');
      }
    } catch (e) {
      throw AIServiceException('获取任务列表失败: $e');
    }
  }

  /// 获取支持的语言
  Future<Map<String, dynamic>> getSupportedLanguages() async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl/voice/languages'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw AIServiceException('获取支持语言失败: ${response.statusCode}');
      }
    } catch (e) {
      throw AIServiceException('获取支持语言失败: $e');
    }
  }

  /// 释放资源
  void dispose() {
    _client.close();
  }
}

/// AI服务异常
class AIServiceException implements Exception {
  final String message;
  
  AIServiceException(this.message);
  
  @override
  String toString() => 'AIServiceException: $message';
}

/// AI健康状态
class AIHealthStatus {
  final String status;
  final Map<String, dynamic> services;
  final Map<String, dynamic>? storage;

  AIHealthStatus({
    required this.status,
    required this.services,
    this.storage,
  });

  factory AIHealthStatus.fromJson(Map<String, dynamic> json) {
    return AIHealthStatus(
      status: json['status'],
      services: json['services'] ?? {},
      storage: json['storage'],
    );
  }

  bool get isHealthy => status == 'healthy';
}

/// AI模型响应
class AIModelsResponse {
  final bool success;
  final Map<String, dynamic> models;
  final Map<String, dynamic> statistics;
  final Map<String, dynamic> features;

  AIModelsResponse({
    required this.success,
    required this.models,
    required this.statistics,
    required this.features,
  });

  factory AIModelsResponse.fromJson(Map<String, dynamic> json) {
    return AIModelsResponse(
      success: json['success'],
      models: json['models'] ?? {},
      statistics: json['statistics'] ?? {},
      features: json['features'] ?? {},
    );
  }
}

/// AI任务响应
class AITaskResponse {
  final bool success;
  final String message;
  final String taskId;
  final String? statusUrl;
  final Map<String, dynamic>? optimization;

  AITaskResponse({
    required this.success,
    required this.message,
    required this.taskId,
    this.statusUrl,
    this.optimization,
  });

  factory AITaskResponse.fromJson(Map<String, dynamic> json) {
    return AITaskResponse(
      success: json['success'],
      message: json['message'],
      taskId: json['task_id'],
      statusUrl: json['status_url'],
      optimization: json['optimization'],
    );
  }
}

/// AI任务状态
class AITaskStatus {
  final String taskId;
  final String taskType;
  final String status;
  final AITaskProgress progress;
  final DateTime? createdAt;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final Map<String, dynamic>? result;
  final String? error;

  AITaskStatus({
    required this.taskId,
    required this.taskType,
    required this.status,
    required this.progress,
    this.createdAt,
    this.startedAt,
    this.completedAt,
    this.result,
    this.error,
  });

  factory AITaskStatus.fromJson(Map<String, dynamic> json) {
    return AITaskStatus(
      taskId: json['task_id'],
      taskType: json['task_type'],
      status: json['status'],
      progress: AITaskProgress.fromJson(json['progress']),
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      startedAt: json['started_at'] != null ? DateTime.parse(json['started_at']) : null,
      completedAt: json['completed_at'] != null ? DateTime.parse(json['completed_at']) : null,
      result: json['result'],
      error: json['error'],
    );
  }

  bool get isCompleted => status == 'completed';
  bool get isFailed => status == 'failed';
  bool get isProcessing => status == 'processing';
  bool get isPending => status == 'pending';
  bool get isCancelled => status == 'cancelled';
}

/// AI任务进度
class AITaskProgress {
  final int currentStep;
  final int totalSteps;
  final double percentage;
  final String stepDescription;
  final int? estimatedTimeRemaining;

  AITaskProgress({
    required this.currentStep,
    required this.totalSteps,
    required this.percentage,
    required this.stepDescription,
    this.estimatedTimeRemaining,
  });

  factory AITaskProgress.fromJson(Map<String, dynamic> json) {
    return AITaskProgress(
      currentStep: json['current_step'],
      totalSteps: json['total_steps'],
      percentage: json['percentage'].toDouble(),
      stepDescription: json['step_description'],
      estimatedTimeRemaining: json['estimated_time_remaining'],
    );
  }
}

/// 语音验证结果
class VoiceValidationResult {
  final bool success;
  final Map<String, dynamic> validation;
  final String language;
  final Map<String, dynamic> supportedLanguages;

  VoiceValidationResult({
    required this.success,
    required this.validation,
    required this.language,
    required this.supportedLanguages,
  });

  factory VoiceValidationResult.fromJson(Map<String, dynamic> json) {
    return VoiceValidationResult(
      success: json['success'],
      validation: json['validation'],
      language: json['language'],
      supportedLanguages: json['supported_languages'],
    );
  }

  bool get isValid => validation['is_valid'] == true;
  List<String> get warnings => List<String>.from(validation['warnings'] ?? []);
  List<String> get recommendations => List<String>.from(validation['recommendations'] ?? []);
  double? get duration => validation['duration']?.toDouble();
}
