// 认证服务
import 'dart:convert';

import 'package:http/http.dart' as http;

import 'package:memorial/core/models/user.dart';
import 'package:memorial/core/services/secure_storage_service.dart';

class AuthService {
  static const String baseUrl = 'http://localhost:5001/api/v1';
  
  // 使用安全存储服务
  final SecureStorageService _secureStorage = SecureStorageService.instance;

  // 获取存储的token
  Future<String?> getStoredToken() => _secureStorage.getToken();

  // 获取存储的刷新token
  Future<String?> getStoredRefreshToken() => _secureStorage.getRefreshToken();

  // 获取存储的用户信息
  Future<User?> getStoredUser() async {
    final userJson = await _secureStorage.readWithIntegrity('user_data');
    if (userJson != null) {
      try {
        return User.fromJson(json.decode(userJson) as Map<String, dynamic>);
      } on Exception catch (e) {
        print('解析用户信息失败: $e');
        await _secureStorage.delete('user_data');
        return null;
      }
    }
    return null;
  }

  // 检查token是否过期
  Future<bool> isTokenExpired() async {
    final expiresAt = await _secureStorage.getTokenExpiresAt();
    if (expiresAt == null) {
      return true;
    }
    
    return DateTime.now().isAfter(expiresAt);
  }

  // 存储tokens
  Future<void> _storeTokens(String accessToken, String? refreshToken, int expiresIn) async {
    await _secureStorage.storeToken(accessToken);
    
    if (refreshToken != null) {
      await _secureStorage.storeRefreshToken(refreshToken);
    }
    
    final expiresAt = DateTime.now().add(Duration(seconds: expiresIn));
    await _secureStorage.storeTokenExpiresAt(expiresAt);
  }

  // 存储用户信息
  Future<void> _storeUser(User user) async {
    await _secureStorage.writeWithIntegrity('user_data', json.encode(user.toJson()));
  }

  // 清理存储的数据
  Future<void> _clearStoredData() async {
    await _secureStorage.clearAuthData();
  }

  // 登录
  Future<Map<String, dynamic>> login(String loginIdentifier, String password) async {
    final url = Uri.parse('$baseUrl/auth/login');
    
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'login_identifier': loginIdentifier,
          'password': password,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        
        // 存储tokens
        await _storeTokens(
          data['access_token'] as String,
          data['refresh_token'] as String?,
          data['expires_in'] as int,
        );
        
        // 获取用户信息
        final user = await getCurrentUser();
        await _storeUser(user);
        
        return {
          'success': true,
          'user': user,
          'token': data['access_token'],
          'refresh_token': data['refresh_token'],
        };
      } else {
        final errorData = json.decode(response.body) as Map<String, dynamic>;
        throw Exception(errorData['detail'] ?? '登录失败');
      }
    } on Exception catch (e) {
      throw Exception('网络请求失败: $e');
    }
  }

  // 注册
  Future<User> register(String username, String email, String password) async {
    final url = Uri.parse('$baseUrl/auth/register');
    
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'username': username,
          'email': email,
          'password': password,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        return User.fromJson(data);
      } else {
        final errorData = json.decode(response.body) as Map<String, dynamic>;
        throw Exception(errorData['detail'] ?? '注册失败');
      }
    } on Exception catch (e) {
      throw Exception('网络请求失败: $e');
    }
  }

  // 获取当前用户信息
  Future<User> getCurrentUser() async {
    final token = await getStoredToken();
    if (token == null) {
      throw Exception('未找到认证token');
    }

    final url = Uri.parse('$baseUrl/users/me');
    
    try {
      final response = await http.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        return User.fromJson(data);
      } else {
        throw Exception('获取用户信息失败');
      }
    } on Exception catch (e) {
      throw Exception('网络请求失败: $e');
    }
  }

  // 刷新token
  Future<Map<String, dynamic>> refreshToken() async {
    final refreshToken = await getStoredRefreshToken();
    if (refreshToken == null) {
      throw Exception('没有可用的刷新token');
    }

    final url = Uri.parse('$baseUrl/auth/login/refresh-token');
    
    try {
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $refreshToken',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        
        // 更新存储的tokens
        await _storeTokens(
          data['access_token'] as String,
          data['refresh_token'] as String?,
          data['expires_in'] as int,
        );
        
        return {
          'access_token': data['access_token'],
          'refresh_token': data['refresh_token'],
          'expires_in': data['expires_in'],
        };
      } else {
        throw Exception('刷新token失败');
      }
    } on Exception catch (e) {
      throw Exception('网络请求失败: $e');
    }
  }

  // 登出
  Future<void> logout() async {
    final token = await getStoredToken();
    
    if (token != null) {
      final url = Uri.parse('$baseUrl/auth/logout');
      
      try {
        await http.post(
          url,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $token',
          },
        );
      } on Exception catch (e) {
        // 即使服务器登出失败，也要清理本地数据
        print('服务器登出失败: $e');
      }
    }
    
    await _clearStoredData();
  }

  // 密码重置
  Future<String> forgotPassword(String email) async {
    final url = Uri.parse('$baseUrl/auth/forgot-password');
    
    try {
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'email': email}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        return data['message'] as String? ?? '密码重置邮件已发送';
      } else {
        final errorData = json.decode(response.body) as Map<String, dynamic>;
        throw Exception(errorData['detail'] ?? '发送密码重置邮件失败');
      }
    } on Exception catch (e) {
      throw Exception('网络请求失败: $e');
    }
  }
}
