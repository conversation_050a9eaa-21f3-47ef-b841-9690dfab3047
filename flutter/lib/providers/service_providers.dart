import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:memorial/providers/app_state_providers.dart';
import '../core/services/ai_service.dart';
import '../core/providers/ai_provider.dart';

// 模拟的 API 服务类
class ApiService {
  Future<bool> login(String email, String password) async {
    // 模拟网络延迟
    await Future<void>.delayed(const Duration(seconds: 1));
    // 简单的验证逻辑
    return email.isNotEmpty && password.length >= 6;
  }

  Future<List<Map<String, dynamic>>> getMemorialSpaces() async {
    // 模拟网络延迟
    await Future<void>.delayed(const Duration(seconds: 1));
    return [
      {
        'id': '1',
        'name': '爷爷的纪念空间',
        'description': '怀念我们亲爱的爷爷',
        'createdAt': DateTime.now().subtract(const Duration(days: 30)),
      },
      {
        'id': '2',
        'name': '奶奶的纪念空间',
        'description': '永远怀念奶奶的温暖',
        'createdAt': DateTime.now().subtract(const Duration(days: 60)),
      },
    ];
  }
}

// 模拟的本地存储服务类
class StorageService {
  Future<void> saveUserData(Map<String, dynamic> userData) async {
    // 实现本地存储逻辑
  }

  Future<Map<String, dynamic>?> getUserData() async => null;
}

// API 服务 Provider
final apiServiceProvider = Provider<ApiService>((ref) => ApiService());

// 本地存储服务 Provider
final storageServiceProvider = Provider<StorageService>((ref) => StorageService());

// AI 服务 Provider
final aiServiceProvider = Provider<AIService>((ref) => AIService());

// AI Provider
final aiProvider = ChangeNotifierProvider<AIProvider>((ref) {
  final aiService = ref.read(aiServiceProvider);
  return AIProvider(aiService);
});

// 用户登录 FutureProvider
final FutureProviderFamily<bool, Map<String, String>> loginProvider = FutureProvider.family<bool, Map<String, String>>((ref, credentials) async {
  final apiService = ref.read(apiServiceProvider);
  try {
    final result = await apiService.login(credentials['email']!, credentials['password']!);
    if (result) {
      // 更新认证状态
      ref.read(authStateProvider.notifier).state = true;
    }
    return result;
  } on Exception catch (e) {
    ref.read(errorMessageProvider.notifier).state = e.toString();
    return false;
  }
});

// 获取纪念空间列表 FutureProvider
final fetchMemorialSpacesProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  final apiService = ref.read(apiServiceProvider);
  try {
    final spaces = await apiService.getMemorialSpaces();
    // 更新状态
    ref.read(memorialSpacesProvider.notifier).state = spaces;
    return spaces;
  } on Exception catch (e) {
    ref.read(errorMessageProvider.notifier).state = e.toString();
    return [];
  }
});
