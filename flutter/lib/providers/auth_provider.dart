// 认证状态管理Provider
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:memorial/core/models/auth_state.dart';
import 'package:memorial/core/models/user.dart';
import 'package:memorial/core/services/auth_service.dart';

// 认证服务Provider
final authServiceProvider = Provider<AuthService>((ref) => AuthService());

// 认证状态Provider
class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier(this._authService) : super(AuthState.initial());

  final AuthService _authService;

  // 初始化认证状态
  Future<void> initialize() async {
    state = state.copyWith(isLoading: true, clearError: true);

    try {
      // 检查是否有存储的用户信息
      final user = await _authService.getStoredUser();
      final token = await _authService.getStoredToken();
      final isTokenExpired = await _authService.isTokenExpired();

      if (user != null && token != null && !isTokenExpired) {
        state = state.copyWith(
          user: user,
          token: token,
          isAuthenticated: true,
          isLoading: false,
        );

        // 尝试获取最新用户信息
        try {
          final currentUser = await _authService.getCurrentUser();
          state = state.copyWith(user: currentUser);
        } on Exception {
          // 如果token已过期，尝试刷新
          final refreshToken = await _authService.getStoredRefreshToken();
          if (refreshToken != null) {
            try {
              await refreshAuth();
            } on Exception catch (refreshError) {
              print('刷新token失败: $refreshError');
              await logout();
            }
          } else {
            await logout();
          }
        }
      } else {
        state = state.copyWith(isLoading: false);
      }
    } on Exception catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '初始化认证状态失败: $e',
      );
    }
  }

  // 登录
  Future<bool> login(String loginIdentifier, String password) async {
    state = state.copyWith(isLoading: true, clearError: true);

    try {
      final result = await _authService.login(loginIdentifier, password);
      
      state = state.copyWith(
        user: result['user'] as User,
        token: result['token'] as String,
        refreshToken: result['refresh_token'] as String?,
        isAuthenticated: true,
        isLoading: false,
      );

      return true;
    } on Exception catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  // 注册
  Future<bool> register(String username, String email, String password) async {
    state = state.copyWith(isLoading: true, clearError: true);

    try {
      await _authService.register(username, email, password);
      state = state.copyWith(isLoading: false);
      return true;
    } on Exception catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  // 刷新认证
  Future<bool> refreshAuth() async {
    try {
      final result = await _authService.refreshToken();
      
      state = state.copyWith(
        token: result['access_token'] as String,
        refreshToken: result['refresh_token'] as String?,
      );

      return true;
    } on Exception catch (e) {
      state = state.copyWith(error: '刷新token失败: $e');
      return false;
    }
  }

  // 登出
  Future<void> logout() async {
    try {
      await _authService.logout();
    } on Exception catch (e) {
      print('登出请求失败: $e');
    } finally {
      state = AuthState.initial();
    }
  }

  // 清除错误
  void clearError() {
    state = state.copyWith(clearError: true);
  }

  // 更新用户信息
  void updateUser(User user) {
    state = state.copyWith(user: user);
  }

  // 密码重置
  Future<String> forgotPassword(String email) async {
    try {
      return await _authService.forgotPassword(email);
    } on Exception catch (e) {
      state = state.copyWith(error: e.toString());
      rethrow;
    }
  }
}

// 认证状态Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthNotifier(authService);
});

// 便捷的Providers
final currentUserProvider = Provider<User?>((ref) => ref.watch(authProvider).user);

final isAuthenticatedProvider = Provider<bool>((ref) => ref.watch(authProvider).isAuthenticated);

final authLoadingProvider = Provider<bool>((ref) => ref.watch(authProvider).isLoading);

final authErrorProvider = Provider<String?>((ref) => ref.watch(authProvider).error);
