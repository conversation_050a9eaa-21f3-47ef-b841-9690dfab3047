import 'package:flutter_riverpod/flutter_riverpod.dart';

// 用户认证状态 Provider
final authStateProvider = StateProvider<bool>((ref) => false);

// 当前用户信息 Provider
final currentUserProvider = StateProvider<Map<String, dynamic>?>((ref) => null);

// 主题模式 Provider
final themeModeProvider = StateProvider<bool>((ref) => true); // true for dark mode

// 加载状态 Provider
final loadingStateProvider = StateProvider<bool>((ref) => false);

// 错误信息 Provider
final errorMessageProvider = StateProvider<String?>((ref) => null);

// 纪念空间列表 Provider
final memorialSpacesProvider = StateProvider<List<Map<String, dynamic>>>((ref) => []);

// 当前选中的纪念空间 Provider
final selectedMemorialSpaceProvider = StateProvider<Map<String, dynamic>?>((ref) => null);
