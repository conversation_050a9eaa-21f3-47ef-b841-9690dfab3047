# Flutter 启动命令文档

本文档总结了在 macOS 开发环境下，使用 Flutter 启动各种终端和模拟器的命令，包括 iOS、Android 和 Chrome 浏览器。

## 环境要求

- macOS 开发环境
- Flutter SDK 已安装
- iOS 模拟器（Xcode）已安装
- Android 模拟器（Android Studio）已安装
- Chrome 浏览器已安装

## 基础命令

### 1. 查看可用设备

```bash
# 查看所有可用的设备（模拟器和真机）
flutter devices

# 查看详细的设备信息
flutter devices -v
```

### 2. 启动模拟器

#### iOS 模拟器

```bash
# 启动默认的 iOS 模拟器
open -a Simulator

# 使用 xcrun 启动特定的 iOS 模拟器
xcrun simctl list devices
xcrun simctl boot "iPhone 16 Pro"
open -a Simulator

# 直接运行到 iOS 模拟器
flutter run -d ios

# 运行到特定的 iOS 设备（使用设备 ID）
flutter run -d [设备ID]
```

#### Android 模拟器

```bash
# 列出所有 Android 模拟器
emulator -list-avds

# 启动特定的 Android 模拟器
emulator -avd [模拟器名称]
emulator -avd Medium_Phone_API_36.0

# 后台启动 Android 模拟器
emulator -avd [模拟器名称] &

# 直接运行到 Android 模拟器
flutter run -d android
```

#### Chrome 浏览器

```bash
# 直接运行到 Chrome 浏览器
flutter run -d chrome

# 指定端口运行 Web 应用
flutter run -d chrome --web-port 8080

# 指定主机地址运行 Web 应用
flutter run -d chrome --web-hostname 0.0.0.0

# 启用热重载的 Web 开发模式
flutter run -d chrome --hot

# 使用不同的 Web 渲染器
flutter run -d chrome --web-renderer html
flutter run -d chrome --web-renderer canvaskit
```

## Flutter 运行命令

### 1. 基本运行命令

```bash
# 运行到默认设备
flutter run

# 运行到特定平台
flutter run -d ios          # iOS 模拟器
flutter run -d android      # Android 模拟器
flutter run -d macos        # macOS 桌面应用
flutter run -d chrome       # Chrome 浏览器
flutter run -d web-server   # Web 服务器模式
flutter run -d edge         # Microsoft Edge 浏览器（如果安装）
```

### 2. 指定设备运行

```bash
# 使用设备 ID 运行（从 flutter devices 获取）
flutter run -d [设备ID]

# 示例：
flutter run -d 18946203-2BEF-48B8-A706-51AB8FCFAEB4  # iOS 模拟器
flutter run -d emulator-5554                          # Android 模拟器
flutter run -d chrome                                 # Chrome 浏览器
flutter run -d web-server                             # Web 服务器
```

### 3. 调试模式运行

```bash
# Debug 模式（默认）
flutter run

# Profile 模式（性能分析）
flutter run --profile

# Release 模式
flutter run --release

# 启用详细日志
flutter run -v

# 启用热重载（默认开启）
flutter run --hot

# 禁用热重载
flutter run --no-hot
```

### 4. 网络和端口配置

```bash
# 指定端口运行 Web 应用
flutter run -d chrome --web-port 8080

# 指定主机地址运行 Web 应用
flutter run -d chrome --web-hostname 0.0.0.0

# Web 服务器模式（可通过任何浏览器访问）
flutter run -d web-server --web-port 8080 --web-hostname 0.0.0.0

# 启用网络调试
flutter run --enable-software-rendering

# 禁用 Web 安全策略（仅开发时使用）
flutter run -d chrome --web-browser-flag="--disable-web-security"

# 启用 CORS 支持
flutter run -d chrome --web-browser-flag="--disable-web-security" --web-browser-flag="--disable-features=VizDisplayCompositor"
```

## 多设备同时运行

```bash
# 在多个终端窗口中分别运行
# 终端 1：iOS 模拟器
flutter run -d ios

# 终端 2：Android 模拟器
flutter run -d android

# 终端 3：Chrome 浏览器
flutter run -d chrome

# 终端 4：Web 服务器模式
flutter run -d web-server --web-port 8080
```

## 常用组合命令

### 1. 完整的开发流程

```bash
# 1. 清理项目
flutter clean

# 2. 获取依赖
flutter pub get

# 3. 启动 iOS 模拟器并运行
open -a Simulator && flutter run -d ios

# 4. 或者启动 Android 模拟器并运行
emulator -avd Pixel_7_API_34 & sleep 10 && flutter run -d android

# 5. 或者启动 Chrome 浏览器并运行
flutter run -d chrome --web-port 8080
```

### 2. 快速启动脚本

创建 `start_ios.sh`：
```bash
#!/bin/bash
open -a Simulator
sleep 3
flutter run -d ios
```

创建 `start_android.sh`：
```bash
#!/bin/bash
emulator -avd Pixel_7_API_34 &
sleep 10
flutter run -d android
```

创建 `start_web.sh`：
```bash
#!/bin/bash
# 启动 Chrome 浏览器模式
flutter run -d chrome --web-port 8080
```

创建 `start_web_server.sh`：
```bash
#!/bin/bash
# 启动 Web 服务器模式
flutter run -d web-server --web-port 8080 --web-hostname 0.0.0.0
echo "Web 应用已启动，访问地址：http://localhost:8080"
```

使用方法：
```bash
chmod +x start_ios.sh start_android.sh start_web.sh start_web_server.sh
./start_ios.sh
./start_android.sh
./start_web.sh
./start_web_server.sh
```

## DevTools 相关命令

```bash
# 启动 Flutter DevTools
flutter pub global activate devtools
flutter pub global run devtools

# 在运行应用时自动打开 DevTools
flutter run --devtools

# 获取 DevTools URL（应用运行时）
# 在应用运行的终端中按 'v' 键

# Web 应用的 DevTools
flutter run -d chrome --devtools
```

## 故障排除命令

```bash
# 检查 Flutter 环境
flutter doctor
flutter doctor -v

# 清理项目
flutter clean

# 重新获取依赖
flutter pub get

# 升级 Flutter
flutter upgrade

# 检查连接的设备
flutter devices

# 重启 ADB（Android 调试桥）
adb kill-server
adb start-server

# 重置 iOS 模拟器
xcrun simctl erase all

# 清除 Flutter Web 缓存
flutter clean
rm -rf build/web
```

## 性能分析命令

```bash
# Profile 模式运行（用于性能分析）
flutter run --profile

# Web 应用性能分析
flutter run -d chrome --profile

# 启用性能覆盖层
flutter run --enable-software-rendering

# 生成性能报告
flutter build apk --analyze-size
flutter build ios --analyze-size
flutter build web --analyze-size
```

## 构建命令

```bash
# 构建 APK（Android）
flutter build apk
flutter build apk --release

# 构建 App Bundle（Android）
flutter build appbundle

# 构建 iOS 应用
flutter build ios
flutter build ipa

# 构建 macOS 应用
flutter build macos

# 构建 Web 应用
flutter build web
flutter build web --release
flutter build web --web-renderer html    # 使用 HTML 渲染器
flutter build web --web-renderer canvaskit # 使用 CanvasKit 渲染器

# 构建 Web 应用并指定基础路径
flutter build web --base-href "/my-app/"

# 构建 Web 应用并优化
flutter build web --release --web-renderer canvaskit
```

## 常见问题解决

### iOS 模拟器问题

```bash
# 如果 iOS 模拟器无法启动
sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer
sudo xcodebuild -runFirstLaunch

# 重置 iOS 模拟器
xcrun simctl erase all

# 检查可用的 iOS 模拟器
xcrun simctl list devices
```

### Android 模拟器问题

```bash
# 如果 Android 模拟器无法启动
# 检查 ANDROID_HOME 环境变量
echo $ANDROID_HOME

# 重启 ADB
adb kill-server
adb start-server

# 检查模拟器状态
adb devices

# 列出可用的 AVD
emulator -list-avds
```

### Chrome 浏览器问题

```bash
# 如果 Chrome 无法启动 Flutter Web 应用
# 检查 Chrome 是否正确安装
which google-chrome || which chrome

# 清除 Flutter Web 缓存
flutter clean
flutter pub get

# 使用不同的 Web 渲染器
flutter run -d chrome --web-renderer html
flutter run -d chrome --web-renderer canvaskit

# 禁用 Web 安全策略（仅开发时）
flutter run -d chrome --web-browser-flag="--disable-web-security" --web-browser-flag="--disable-features=VizDisplayCompositor"

# 启用详细的 Web 调试
flutter run -d chrome -v

# 检查 Web 服务器模式
flutter run -d web-server --web-port 3000
```

### 网络权限问题

如果遇到本地网络权限错误：
1. 打开系统偏好设置
2. 进入隐私与安全性
3. 选择本地网络
4. 为模拟器应用启用本地网络访问权限

### Web 应用特定问题

```bash
# CORS 问题解决
flutter run -d chrome --web-browser-flag="--disable-web-security"

# 字体加载问题
flutter run -d chrome --web-renderer html

# 性能问题
flutter run -d chrome --web-renderer canvaskit --profile
```

## 快捷键

在 Flutter 应用运行时，可以在终端中使用以下快捷键：

- `r` - 热重载
- `R` - 热重启
- `h` - 显示帮助
- `d` - 分离调试器
- `c` - 清除屏幕
- `q` - 退出应用
- `v` - 打开 DevTools
- `w` - 显示 Widget 检查器
- `t` - 显示渲染树
- `p` - 显示性能覆盖层

## 环境变量配置

在 `~/.zshrc` 或 `~/.bash_profile` 中添加：

```bash
# Flutter
export PATH="$PATH:/path/to/flutter/bin"

# Android
export ANDROID_HOME="$HOME/Library/Android/sdk"
export PATH="$PATH:$ANDROID_HOME/emulator"
export PATH="$PATH:$ANDROID_HOME/tools"
export PATH="$PATH:$ANDROID_HOME/tools/bin"
export PATH="$PATH:$ANDROID_HOME/platform-tools"

# iOS
export PATH="$PATH:/Applications/Xcode.app/Contents/Developer/usr/bin"

# Chrome（如果需要指定路径）
export CHROME_EXECUTABLE="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
```

重新加载配置：
```bash
source ~/.zshrc
# 或
source ~/.bash_profile
```

## Web 开发特定配置

### 1. 本地开发服务器

```bash
# 启动本地开发服务器
flutter run -d web-server --web-port 8080 --web-hostname localhost

# 允许外部访问
flutter run -d web-server --web-port 8080 --web-hostname 0.0.0.0
```

### 2. 生产环境构建

```bash
# 构建生产版本
flutter build web --release

# 构建并指定基础路径
flutter build web --release --base-href "/my-app/"

# 使用 CanvasKit 渲染器（更好的性能）
flutter build web --release --web-renderer canvaskit
```

### 3. Web 应用部署

```bash
# 构建完成后，Web 文件位于 build/web/ 目录
# 可以直接部署到任何 Web 服务器

# 本地测试构建结果
cd build/web
python3 -m http.server 8000
# 或使用 Node.js
npx serve -s . -l 8000
```

---

**注意**：
1. 确保在运行这些命令之前，已经正确安装和配置了 Flutter SDK、Xcode、Android Studio 和 Chrome 浏览器
2. Web 应用的某些功能可能在不同浏览器中表现不同，建议主要使用 Chrome 进行开发
3. 在生产环境中部署 Web 应用时，建议使用 `--release` 模式构建以获得最佳性能
4. 如果遇到 CORS 问题，可以使用 `--web-browser-flag="--disable-web-security"` 标志，但仅限于开发环境使用