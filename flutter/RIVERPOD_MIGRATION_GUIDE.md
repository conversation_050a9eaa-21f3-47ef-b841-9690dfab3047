# Riverpod 2.6.1 状态管理迁移指南

## 概述

本项目已成功迁移到使用 Riverpod 2.6.1 作为统一的状态管理解决方案。本指南将帮助开发者理解如何在项目中使用 Riverpod。

## 已完成的迁移工作

### 1. 依赖更新
- ✅ 移除了 `provider: ^6.1.2` 依赖
- ✅ 保留 `flutter_riverpod: ^2.6.1` 和 `riverpod: ^2.6.1`
- ✅ 在 `main.dart` 中添加了 `ProviderScope`

### 2. 核心 Providers 创建
- ✅ `lib/providers/app_state_providers.dart` - 应用状态管理
- ✅ `lib/providers/service_providers.dart` - 服务层和异步操作

### 3. 示例实现
- ✅ `LoginScreen` 已迁移为 `ConsumerStatefulWidget`
- ✅ 创建了 `RiverpodExampleScreen` 展示各种用法

## Providers 架构

### 应用状态 Providers (`app_state_providers.dart`)

```dart
// 用户认证状态
final authStateProvider = StateProvider<bool>((ref) => false);

// 当前用户信息
final currentUserProvider = StateProvider<Map<String, dynamic>?>((ref) => null);

// 主题模式
final themeModeProvider = StateProvider<bool>((ref) => true);

// 加载状态
final loadingStateProvider = StateProvider<bool>((ref) => false);

// 错误信息
final errorMessageProvider = StateProvider<String?>((ref) => null);

// 纪念空间列表
final memorialSpacesProvider = StateProvider<List<Map<String, dynamic>>>((ref) => []);

// 当前选中的纪念空间
final selectedMemorialSpaceProvider = StateProvider<Map<String, dynamic>?>((ref) => null);
```

### 服务层 Providers (`service_providers.dart`)

```dart
// API 服务
final apiServiceProvider = Provider<ApiService>((ref) => ApiService());

// 本地存储服务
final storageServiceProvider = Provider<StorageService>((ref) => StorageService());

// 用户登录（异步操作）
final loginProvider = FutureProvider.family<bool, Map<String, String>>((ref, credentials) async {
  // 登录逻辑
});

// 获取纪念空间列表（异步操作）
final fetchMemorialSpacesProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  // 获取数据逻辑
});
```

## Widget 迁移模式

### 1. StatefulWidget → ConsumerStatefulWidget

```dart
// 之前
class MyWidget extends StatefulWidget {
  @override
  State<MyWidget> createState() => _MyWidgetState();
}

class _MyWidgetState extends State<MyWidget> {
  // ...
}

// 之后
class MyWidget extends ConsumerStatefulWidget {
  @override
  ConsumerState<MyWidget> createState() => _MyWidgetState();
}

class _MyWidgetState extends ConsumerState<MyWidget> {
  // 使用 ref 访问 providers
}
```

### 2. StatelessWidget → ConsumerWidget

```dart
// 之前
class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // ...
  }
}

// 之后
class MyWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // 使用 ref 访问 providers
  }
}
```

## 常用操作模式

### 1. 读取状态

```dart
// 监听状态变化（会触发重建）
final isLoading = ref.watch(loadingStateProvider);

// 一次性读取（不会触发重建）
final currentValue = ref.read(loadingStateProvider);
```

### 2. 更新状态

```dart
// 更新 StateProvider
ref.read(loadingStateProvider.notifier).state = true;

// 更新复杂状态
ref.read(currentUserProvider.notifier).update((state) => {
  ...?state,
  'lastLogin': DateTime.now().toIso8601String(),
});
```

### 3. 处理异步数据

```dart
final asyncData = ref.watch(fetchMemorialSpacesProvider);

return asyncData.when(
  data: (spaces) => ListView.builder(
    itemCount: spaces.length,
    itemBuilder: (context, index) => ListTile(
      title: Text(spaces[index]['name']),
    ),
  ),
  loading: () => const CupertinoActivityIndicator(),
  error: (error, stack) => Text('错误: $error'),
);
```

### 4. 刷新异步数据

```dart
// 使数据失效并重新获取
ref.invalidate(fetchMemorialSpacesProvider);

// 或者使用 refresh
ref.refresh(fetchMemorialSpacesProvider);
```

## 最佳实践

### 1. Provider 组织
- 将相关的 providers 分组到不同文件中
- 使用描述性的命名
- 为复杂的状态创建专门的 notifier 类

### 2. 状态管理
- 优先使用 `StateProvider` 处理简单状态
- 使用 `FutureProvider` 处理异步操作
- 使用 `StreamProvider` 处理实时数据

### 3. 性能优化
- 使用 `ref.read()` 进行一次性读取
- 使用 `ref.watch()` 监听状态变化
- 合理使用 `family` 修饰符传递参数

### 4. 错误处理
- 在异步 providers 中统一处理错误
- 使用专门的错误状态 provider
- 提供用户友好的错误信息

## 待迁移的组件

以下组件仍需要迁移到 Riverpod：

- [ ] `RegisterScreen`
- [ ] `DashboardScreen`
- [ ] `MemorialSpaceDetailScreen`
- [ ] `MemorialSpaceEditScreen`
- [ ] `CreateMemorialScreen`
- [ ] `FamilyTreeScreen`
- [ ] `SettingsScreen`
- [ ] `NotificationsScreen`

## 示例代码

查看 `lib/features/dashboard/riverpod_example_screen.dart` 了解完整的使用示例，包括：
- 状态监听
- 异步数据处理
- 用户交互
- 错误处理

## 下一步

1. 逐步迁移剩余的屏幕组件
2. 创建更多专门的 providers 处理业务逻辑
3. 添加单元测试验证状态管理逻辑
4. 优化性能和用户体验

---

*本指南将随着项目的发展持续更新。*