# 📱 Memorial App - 移动端架构文档

## 🏗️ 项目结构

```
mobile/
├── lib/
│   ├── main.dart                    # 应用入口点
│   ├── features/                    # 功能模块
│   │   ├── auth/                    # 认证功能
│   │   │   ├── login_screen.dart
│   │   │   ├── register_screen.dart
│   │   │   └── forgot_password_screen.dart
│   │   ├── dashboard/               # 仪表板功能
│   │   │   └── dashboard_screen.dart
│   │   ├── memorial/                # 纪念空间功能
│   │   │   ├── create_memorial_screen.dart
│   │   │   ├── memorial_space_detail_screen.dart
│   │   │   ├── memorial_space_edit_screen.dart
│   │   │   ├── family_tree_screen.dart
│   │   │   ├── public_memorials_screen.dart
│   │   │   └── ai_services_screen.dart
│   │   └── settings/                # 设置功能
│   │       ├── settings_screen.dart
│   │       ├── notifications_screen.dart
│   │       ├── edit_profile_screen.dart
│   │       ├── change_password_screen.dart
│   │       ├── notification_settings_screen.dart
│   │       ├── privacy_settings_screen.dart
│   │       ├── about_us_screen.dart
│   │       └── store_screen.dart
│   ├── core/                        # 核心功能
│   │   ├── widgets/                 # 共享组件
│   │   ├── services/                # 服务层
│   │   ├── models/                  # 数据模型
│   │   └── utils/                   # 工具类
│   └── shared/                      # 共享资源
│       ├── constants/               # 常量
│       ├── themes/                  # 主题配置
│       └── extensions/              # 扩展方法
├── assets/                          # 资源文件
│   ├── images/
│   ├── icons/
│   └── fonts/
├── test/                           # 测试文件
├── integration_test/               # 集成测试
├── android/                        # Android 平台配置
├── ios/                           # iOS 平台配置
├── web/                           # Web 平台配置
├── pubspec.yaml                   # 依赖配置
├── README.md                      # 项目说明
├── BUILD.md                       # 构建说明
├── PACKAGE.md                     # 打包说明
└── ARCHITECTURE.md                # 架构文档
```

## 🎯 架构原则

### 1. **功能模块化 (Feature-based)**
- 按功能领域组织代码，而不是按技术层次
- 每个功能模块包含该功能的所有相关文件
- 便于团队协作和代码维护

### 2. **清晰的分层架构**
- **Presentation Layer**: UI 组件和屏幕
- **Business Logic Layer**: 业务逻辑和状态管理
- **Data Layer**: 数据访问和API调用
- **Core Layer**: 共享功能和工具

### 3. **依赖注入**
- 使用依赖注入容器管理服务
- 便于测试和模块解耦
- 支持不同环境的配置

## 📦 功能模块说明

### 🔐 Auth (认证模块)
- **职责**: 用户登录、注册、密码重置
- **主要文件**:
  - `login_screen.dart` - 登录界面
  - `register_screen.dart` - 注册界面
  - `forgot_password_screen.dart` - 忘记密码界面

### 📊 Dashboard (仪表板模块)
- **职责**: 用户主页、数据概览
- **主要文件**:
  - `dashboard_screen.dart` - 主仪表板界面

### 🏛️ Memorial (纪念空间模块)
- **职责**: 纪念空间的创建、编辑、查看
- **主要文件**:
  - `create_memorial_screen.dart` - 创建纪念空间
  - `memorial_space_detail_screen.dart` - 纪念空间详情
  - `memorial_space_edit_screen.dart` - 编辑纪念空间
  - `family_tree_screen.dart` - 家族树
  - `public_memorials_screen.dart` - 公共纪念空间
  - `ai_services_screen.dart` - AI服务

### ⚙️ Settings (设置模块)
- **职责**: 用户设置、应用配置
- **主要文件**:
  - `settings_screen.dart` - 主设置界面
  - `notifications_screen.dart` - 通知设置
  - `edit_profile_screen.dart` - 编辑个人资料
  - `change_password_screen.dart` - 修改密码
  - `privacy_settings_screen.dart` - 隐私设置
  - `about_us_screen.dart` - 关于我们

## 🔧 核心组件

### Core/Widgets
- 可复用的UI组件
- 自定义Widget库
- 主题相关组件

### Core/Services
- API服务
- 本地存储服务
- 认证服务
- 推送通知服务

### Core/Models
- 数据模型类
- API响应模型
- 本地数据模型

### Core/Utils
- 工具函数
- 常量定义
- 扩展方法

## 🎨 设计系统

### 主题配置
- 深色主题为主
- Cupertino设计风格
- 自定义颜色方案
- 响应式设计

### 颜色方案
- **主色**: `#4A90E2` (宁静蓝)
- **背景色**: `#333333` (深空灰)
- **卡片背景**: `#1F2937`
- **文本色**: `#F8F8F8`

## 🚀 开发指南

### 添加新功能
1. 在 `lib/features/` 下创建新的功能目录
2. 按照现有结构组织文件
3. 更新路由配置
4. 添加相应的测试

### 代码规范
- 遵循 Dart 官方代码规范
- 使用 `flutter_lints` 进行代码检查
- 保持一致的命名约定
- 添加适当的注释和文档

### 测试策略
- **单元测试**: 业务逻辑和工具函数
- **Widget测试**: UI组件测试
- **集成测试**: 端到端功能测试

## 📱 平台特定配置

### iOS
- 最低支持版本: iOS 12.0
- 使用 Cupertino 设计语言
- 支持 Dark Mode

### Android
- 最低支持版本: Android API 21 (5.0)
- Material Design 3
- 支持动态颜色

### Web
- 响应式设计
- PWA 支持
- 现代浏览器兼容

## 🔄 状态管理

### 推荐方案
- **Provider** - 简单状态管理
- **Riverpod** - 高级状态管理
- **Bloc** - 复杂业务逻辑

### 数据流
```
UI → Event → Business Logic → State → UI
```

## 📊 性能优化

### 关键指标
- 应用启动时间 < 3秒
- 页面切换动画流畅 (60fps)
- 内存使用合理
- 网络请求优化

### 优化策略
- 懒加载和分页
- 图片缓存和压缩
- 代码分割和按需加载
- 性能监控和分析

## 🔒 安全考虑

### 数据安全
- 敏感数据加密存储
- 网络传输使用HTTPS
- 用户认证和授权
- 输入验证和清理

### 隐私保护
- 最小权限原则
- 用户数据透明度
- 符合GDPR等法规
- 本地数据保护

## 📈 监控和分析

### 性能监控
- Firebase Performance
- 自定义性能指标
- 崩溃报告
- 用户行为分析

### 日志管理
- 结构化日志
- 不同级别的日志
- 远程日志收集
- 敏感信息过滤 