# 移动端项目说明文档

## 项目概述

这是一个使用 Flutter 框架开发的跨平台移动应用，名为「归处」，专门用于纪念服务，集成了 3D 可视化和 AI 功能。该应用采用 iOS 风格的 Cupertino 设计语言，支持 iOS、Android、Web 和桌面平台。

## 项目基本信息

- **应用名称**: 归处 (Memorial App)
- **版本**: 1.0.0+1
- **Flutter SDK**: >=3.32.0
- **Dart SDK**: >=3.8.0 <4.0.0
- **主要特性**: 纪念服务、3D 可视化、AI 功能
- **设计风格**: iOS Cupertino 风格

## 目录结构详细说明

### 根目录文件

| 文件名 | 用途说明 |
|--------|----------|
| `.gitignore` | Git 版本控制忽略文件配置 |
| `.metadata` | Flutter 项目元数据文件 |
| `README.md` | 项目介绍和使用说明 |
| `ARCHITECTURE.md` | 项目架构设计文档 |
| `BUILD.md` | 构建和打包指南 |
| `PACKAGE.md` | 包管理和依赖说明 |
| `pubspec.yaml` | Flutter 项目配置和依赖管理 |
| `pubspec.lock` | 依赖版本锁定文件 |
| `analysis_options.yaml` | Dart 代码分析配置 |

### 核心目录结构

#### `/lib/` - 主要源代码目录

```
lib/
├── main.dart                    # 应用程序入口点
├── core/                        # 核心功能模块
│   ├── models/                  # 数据模型定义
│   ├── services/                # 业务服务层
│   └── widgets/                 # 共享 UI 组件
└── features/                    # 功能模块
    ├── auth/                    # 用户认证模块
    ├── dashboard/               # 仪表板模块
    ├── memorial/                # 纪念空间模块
    └── settings/                # 设置模块
```

##### 功能模块详细说明

**1. 认证模块 (`features/auth/`)**
- `login_screen.dart` - 用户登录界面
- `register_screen.dart` - 用户注册界面
- `forgot_password_screen.dart` - 忘记密码界面

**2. 仪表板模块 (`features/dashboard/`)**
- `dashboard_screen.dart` - 主仪表板界面，用户登录后的主页

**3. 纪念空间模块 (`features/memorial/`)**
- `create_memorial_screen.dart` - 创建纪念空间界面
- `memorial_space_detail_screen.dart` - 纪念空间详情页
- `memorial_space_edit_screen.dart` - 编辑纪念空间界面
- `family_tree_screen.dart` - 家庭树功能界面
- `public_memorials_screen.dart` - 公共纪念空间浏览
- `ai_services_screen.dart` - AI 服务功能界面

**4. 设置模块 (`features/settings/`)**
- `settings_screen.dart` - 主设置界面
- `notifications_screen.dart` - 通知中心
- `edit_profile_screen.dart` - 编辑个人资料
- `change_password_screen.dart` - 修改密码
- `notification_settings_screen.dart` - 通知设置
- `privacy_settings_screen.dart` - 隐私设置
- `about_us_screen.dart` - 关于我们
- `store_screen.dart` - 商店功能

#### `/assets/` - 资源文件目录

```
assets/
├── fonts/                       # 字体文件
├── icons/                       # 图标资源
├── images/                      # 图片资源
├── models/                      # 3D 模型文件
└── textures/                    # 纹理贴图文件
```

#### `/ios/` - iOS 平台配置

```
ios/
├── .gitignore                   # iOS 特定的 Git 忽略配置
├── Podfile                      # CocoaPods 依赖管理
├── Flutter/                     # Flutter iOS 配置
│   ├── AppFrameworkInfo.plist   # 应用框架信息
│   ├── Debug.xcconfig           # Debug 构建配置
│   └── Release.xcconfig         # Release 构建配置
├── Runner/                      # iOS 应用主体
│   ├── AppDelegate.swift        # iOS 应用委托
│   ├── Info.plist              # iOS 应用信息配置
│   ├── Assets.xcassets/        # iOS 资源目录
│   │   ├── AppIcon.appiconset/ # 应用图标集
│   │   └── LaunchImage.imageset/ # 启动图片集
│   ├── Base.lproj/             # 本地化资源
│   │   ├── Main.storyboard     # 主故事板
│   │   └── LaunchScreen.storyboard # 启动屏故事板
│   └── Runner-Bridging-Header.h # Swift-ObjC 桥接头文件
├── Runner.xcodeproj/           # Xcode 项目文件
├── Runner.xcworkspace/         # Xcode 工作空间
└── RunnerTests/                # iOS 单元测试
    └── RunnerTests.swift       # 测试用例
```

#### `/web/` - Web 平台配置

```
web/
├── favicon.png                  # 网站图标
├── index.html                   # Web 应用入口 HTML
├── manifest.json                # Web 应用清单
└── icons/                       # Web 应用图标
    ├── Icon-192.png            # 192x192 图标
    ├── Icon-512.png            # 512x512 图标
    ├── Icon-maskable-192.png   # 可遮罩 192x192 图标
    └── Icon-maskable-512.png   # 可遮罩 512x512 图标
```

## 技术栈和依赖

### 核心框架
- **Flutter**: 跨平台 UI 框架
- **Cupertino**: iOS 风格设计组件库

### 主要依赖包

#### 状态管理
- `flutter_riverpod: ^2.6.1` - 现代状态管理解决方案
- `provider: ^6.1.2` - 状态管理辅助库
- `riverpod: ^2.6.1` - Riverpod 核心库

#### 网络和数据
- `dio: ^5.7.0` - HTTP 客户端库
- `http: ^1.2.2` - HTTP 请求库
- `cached_network_image: ^3.4.1` - 网络图片缓存
- `json_annotation: ^4.9.0` - JSON 序列化注解

#### 本地存储
- `hive: ^2.2.3` - 轻量级本地数据库
- `hive_flutter: ^1.1.0` - Hive Flutter 集成
- `shared_preferences: ^2.3.3` - 本地偏好设置存储
- `path_provider: ^2.1.5` - 文件路径提供器

#### 路由和导航
- `go_router: ^14.6.2` - 声明式路由管理

#### 设备和权限
- `device_info_plus: ^11.1.0` - 设备信息获取
- `permission_handler: ^11.3.1` - 权限管理
- `package_info_plus: ^8.1.0` - 应用包信息

#### 媒体和文件
- `image_picker: ^1.1.2` - 图片选择器

#### 工具库
- `intl: ^0.20.1` - 国际化支持
- `uuid: ^4.5.1` - UUID 生成器
- `cupertino_icons: ^1.0.8` - Cupertino 图标库

### 开发工具
- `flutter_test` - Flutter 测试框架
- `integration_test` - 集成测试
- `build_runner: ^2.4.13` - 代码生成工具
- `json_serializable: ^6.8.0` - JSON 序列化代码生成
- `hive_generator: ^2.0.1` - Hive 代码生成
- `flutter_lints: ^6.0.0` - Flutter 代码规范检查
- `mockito: ^5.4.4` - 模拟对象测试库

## 应用架构特点

### 设计模式
1. **功能模块化**: 按功能划分的清晰目录结构
2. **分层架构**: Core 层提供基础服务，Features 层实现具体功能
3. **iOS 风格**: 全面采用 Cupertino 设计语言
4. **响应式状态管理**: 使用 Riverpod 进行状态管理

### 代码质量保证
- 严格的 Dart 分析配置 (`analysis_options.yaml`)
- 完整的测试覆盖 (单元测试、集成测试)
- 代码生成工具支持
- 规范的依赖管理

### 平台支持
- **iOS**: 完整的 Xcode 项目配置
- **Android**: 标准 Android 项目结构
- **Web**: PWA 支持
- **桌面**: 跨平台桌面应用支持

## 开发和构建

### 开发环境要求
- Flutter SDK >=3.32.0
- Dart SDK >=3.8.0
- iOS 开发需要 Xcode
- Android 开发需要 Android Studio

### 快速开始
1. 安装依赖: `flutter pub get`
2. 运行应用: `flutter run`
3. 构建发布版: `flutter build apk` (Android) 或 `flutter build ios` (iOS)

### 测试
- 单元测试: `flutter test`
- 集成测试: `flutter test integration_test`
- 代码分析: `flutter analyze`

## 总结

这是一个结构清晰、技术栈现代化的 Flutter 移动应用项目。项目采用了最佳实践的目录结构，使用了成熟的第三方库，并且有完善的文档和构建配置。特别值得注意的是项目严格遵循 iOS 设计规范，使用 Cupertino 组件库，为用户提供原生 iOS 体验。