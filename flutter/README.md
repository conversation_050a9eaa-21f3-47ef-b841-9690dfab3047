# Memorial App (Flutter Client)

这是一个使用 Flutter 构建的移动客户端，用于纪念和回忆重要的人和事。

## 项目简介

该应用旨在提供一个用户友好的界面，让用户能够创建和管理纪念条目、家庭树、查看仪表盘以及自定义设置。

## Flutter 版本和关键依赖

- **Flutter SDK**: (请在此处填写你使用的 Flutter SDK 版本，例如: 3.10.0 或更高)
- **Dart SDK**: (Flutter SDK 会自带相应版本的 Dart SDK)
- **关键依赖**:
  - `cupertino_icons`: ^1.0.2 (或其他版本)
  - (请在 `pubspec.yaml` 查看并列出其他关键依赖及其版本)

## 本地开发环境设置

1.  **安装 Flutter**: 如果你还没有安装 Flutter, 请访问 [Flutter 官方网站](https://flutter.dev/docs/get-started/install) 并按照说明进行安装。
2.  **克隆仓库** (如果适用): 例如 `git clone <repository_url>`
3.  **进入项目目录**: 使用命令行进入本项目根目录 (`mobile`)。
    ```bash
    cd path/to/memorial/mobile
    ```
4.  **获取依赖**: 运行以下命令来安装项目所需的所有依赖包:
    ```bash
    flutter pub get
    ```
5.  **配置开发工具**:
    *   **VS Code**: 安装 Flutter 和 Dart 插件。
    *   **Android Studio / IntelliJ IDEA**: 安装 Flutter 和 Dart 插件。
    *   **Xcode**: (macOS 用户) 确保已安装最新版本的 Xcode 以进行 iOS 开发。
    *   **Android SDK**: 确保已安装并配置好 Android SDK 以进行 Android 开发。
6.  **检查 Flutter 环境**: 运行 `flutter doctor` 命令，确保没有未解决的问题。
    ```bash
    flutter doctor
    ```

## 如何在模拟器或真机上运行应用

1.  **检查连接的设备/模拟器**:
    ```bash
    flutter devices
    ```
    确保至少有一个设备或模拟器列出并可用。

2.  **启动 iOS 模拟器** (如果尚未运行):
    ```bash
    open -a Simulator
    ```
    或者通过 Xcode (Window > Devices and Simulators) 启动。

3.  **启动 Android 模拟器**: 通过 Android Studio 的 AVD Manager 启动，或者如果已配置路径，可以通过命令行启动。

4.  **运行应用**:
    在项目根目录 (`mobile`) 下运行:
    ```bash
    flutter run
    ```
    如果连接了多个设备/模拟器，你可以通过 `-d` 参数指定目标设备:
    ```bash
    flutter run -d <device_id>
    ```
    例如:
    ```bash
    flutter run -d iphone_simulator_id # 替换为实际的 iOS 模拟器 ID
    flutter run -d emulator-5554      # 替换为实际的 Android 模拟器 ID
    ```

## 如何运行测试 (如果实现了)

- **单元测试** (通常在 `test/` 目录下，不包含 `integration_test`):
  ```bash
  flutter test
  ```
  或者指定特定目录/文件:
  ```bash
  flutter test test/unit_tests/
  ```
- **Widget 测试** (通常在 `test/` 目录下):
  ```bash
  flutter test test/widget_tests/
  ```
- **集成测试** (通常在 `integration_test/` 目录下):
  ```bash
  flutter test integration_test/
  ```
- **运行所有测试** (包括单元、Widget 和集成测试):
  ```bash
  flutter test
  ```

## 如何构建 Release 版本的应用包

请参考项目中的 `BUILD.md` 文件获取详细的构建说明，包括如何构建 APK/AAB (Android) 和 IPA (iOS)。

## 平台特定配置说明

如果项目需要修改原生 iOS (`ios/Runner/Info.plist`, `ios/Podfile`) 或 Android (`android/app/src/main/AndroidManifest.xml`, `android/app/build.gradle`) 的配置文件，相关说明会记录在 `BUILD.md` 或特定的文档中。

## 打包与发布

请参考项目中的 `PACKAGE.md` 文件获取详细的打包和发布指南。