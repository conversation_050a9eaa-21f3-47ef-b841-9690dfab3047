name: memorial
version: 1.0.0+1
homepage: https://github.com/your-org/memorial-app
publish_to: none
environment:
  sdk: ">=3.8.0 <4.0.0"
  flutter: ">=3.32.0"
description: A Flutter application for memorial services with 3D visualization and AI-powered features.
dependencies:
  audioplayers: ^6.1.0
  cached_network_image: ^3.4.1
  camera: ^0.11.0+2
  connectivity_plus: ^6.0.5
  crypto: ^3.0.5
  cupertino_icons: ^1.0.8
  device_info_plus: ^11.1.0
  dio: ^5.7.0
  encrypt: ^5.0.3
  file_picker: ^8.1.2
  flutter:
    sdk: flutter
  flutter_local_notifications: ^18.0.1
  flutter_riverpod: ^2.6.1
  flutter_secure_storage: ^9.2.2
  flutter_staggered_animations: ^1.1.1
  flutter_staggered_grid_view: ^0.7.0
  flutter_svg: ^2.0.10+1
  fluttertoast: ^8.2.8
  go_router: ^14.6.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  http: ^1.2.2
  image_picker: ^1.1.2
  intl: ^0.20.1
  json_annotation: ^4.9.0
  lottie: ^3.1.2
  open_file: ^3.5.7
  package_info_plus: ^8.1.0
  path_provider: ^2.1.5
  permission_handler: ^11.3.1
  pull_to_refresh: ^2.0.0
  riverpod: ^2.6.1
  share_plus: ^10.0.2
  shared_preferences: ^2.3.3
  shimmer: ^3.0.0
  sqflite: ^2.3.3+1
  url_launcher: ^6.3.1
  uuid: ^4.5.1
  video_player: ^2.9.1
  webview_flutter: ^4.4.4
dev_dependencies:
  build_runner: ^2.4.13
  flutter_launcher_icons: ^0.14.1
  flutter_native_splash: ^2.4.1
  flutter_test:
    sdk: flutter
  hive_generator: ^2.0.1
  integration_test:
    sdk: flutter
  json_serializable: ^6.8.0
  mockito: ^5.4.4
  pubspec_dependency_sorter: ^1.0.5
  very_good_analysis: ^9.0.0
repository: https://github.com/your-org/memorial-app
issue_tracker: https://github.com/your-org/memorial-app/issues
flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/models/
    - assets/textures/
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700
  #   - family: NotoSans
  #     fonts:
  #       - asset: assets/fonts/NotoSans-Regular.ttf
  #       - asset: assets/fonts/NotoSans-Bold.ttf
  #         weight: 700
flutter_launcher_icons:
  android: launcher_icon
  ios: true
  image_path: assets/images/flame_icon.png
  remove_alpha_ios: true
  min_sdk_android: 21
  web:
    generate: true
    image_path: assets/images/flame_icon.png
    background_color: "#FF6B35"
    theme_color: "#FF6B35"
  windows:
    generate: true
    image_path: assets/images/flame_icon.png
    icon_size: 48
  macos:
    generate: true
    image_path: assets/images/flame_icon.png
flutter_native_splash:
  color: "#ffffff"
  image: assets/images/splash_logo.png
  branding: assets/images/splash_branding.png
  color_dark: "#121212"
  image_dark: assets/images/splash_logo_dark.png
  branding_dark: assets/images/splash_branding_dark.png
  android_12:
    image: assets/images/splash_logo_android12.png
    icon_background_color: "#ffffff"
    image_dark: assets/images/splash_logo_android12_dark.png
    icon_background_color_dark: "#121212"
  web: false
