#!/bin/bash
# 停止3D渲染服务

# 确保脚本在正确的目录中执行
cd "$(dirname "$0")"

# 检查PID文件是否存在
if [ -f render_service.pid ]; then
    PID=$(cat render_service.pid)
    
    # 检查进程是否仍在运行
    if ps -p $PID > /dev/null; then
        echo "停止渲染服务 (PID: $PID)..."
        kill $PID
        
        # 等待进程结束
        sleep 2
        if ps -p $PID > /dev/null; then
            echo "服务未响应，强制终止..."
            kill -9 $PID
        fi
        
        echo "渲染服务已停止"
    else
        echo "渲染服务不在运行状态 (PID: $PID)"
    fi
    
    # 删除PID文件
    rm render_service.pid
else
    echo "未找到渲染服务PID文件，服务可能未启动"
    
    # 尝试查找并终止可能的渲染服务进程
    PIDS=$(ps aux | grep "python3 render_service.py" | grep -v grep | awk '{print $2}')
    if [ -n "$PIDS" ]; then
        echo "找到可能的渲染服务进程: $PIDS"
        echo "正在终止这些进程..."
        for pid in $PIDS; do
            kill $pid
            echo "已终止进程 $pid"
        done
    fi
fi

# 检查端口是否仍被占用
PORT_USAGE=$(lsof -i:5001 | grep LISTEN)
if [ -n "$PORT_USAGE" ]; then
    echo "警告: 端口5001仍被占用:"
    echo "$PORT_USAGE"
    echo "可能需要手动终止这些进程"
else
    echo "端口5001已释放"
fi
