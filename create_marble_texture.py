"""
在Blender中创建大理石材质背景的脚本
使用方法：
1. 打开Blender
2. 切换到Scripting工作区
3. 打开此脚本
4. 点击"Run Script"按钮
5. 渲染图像将保存到指定位置
"""
import bpy
import os
import math

# 清除场景中的所有对象
bpy.ops.object.select_all(action='SELECT')
bpy.ops.object.delete()

# 创建一个平面作为背景
bpy.ops.mesh.primitive_plane_add(size=2, enter_editmode=False, align='WORLD', location=(0, 0, 0))
plane = bpy.context.active_object
plane.name = "MarbleBackground"

# 设置渲染分辨率
bpy.context.scene.render.resolution_x = 1920
bpy.context.scene.render.resolution_y = 1080
bpy.context.scene.render.resolution_percentage = 100

# 创建新材质
marble_mat = bpy.data.materials.new(name="MarbleMaterial")
marble_mat.use_nodes = True
nodes = marble_mat.node_tree.nodes
links = marble_mat.node_tree.links

# 清除默认节点
for node in nodes:
    nodes.remove(node)

# 创建输出节点
output_node = nodes.new(type='ShaderNodeOutputMaterial')
output_node.location = (600, 0)

# 创建Principled BSDF节点
principled_node = nodes.new(type='ShaderNodeBsdfPrincipled')
principled_node.location = (400, 0)
principled_node.inputs['Specular'].default_value = 0.5
principled_node.inputs['Roughness'].default_value = 0.1
principled_node.inputs['Metallic'].default_value = 0.0

# 连接Principled BSDF到输出
links.new(principled_node.outputs['BSDF'], output_node.inputs['Surface'])

# 创建噪波纹理节点 - 用于大理石纹理的基础
noise_texture = nodes.new(type='ShaderNodeTexNoise')
noise_texture.location = (-600, 0)
noise_texture.inputs['Scale'].default_value = 5.0
noise_texture.inputs['Detail'].default_value = 16.0
noise_texture.inputs['Roughness'].default_value = 0.7
noise_texture.inputs['Distortion'].default_value = 0.0

# 创建颜色渐变节点 - 用于大理石的颜色变化
color_ramp = nodes.new(type='ShaderNodeValToRGB')
color_ramp.location = (-400, 0)
# 设置大理石的颜色
color_ramp.color_ramp.elements[0].position = 0.0
color_ramp.color_ramp.elements[0].color = (0.9, 0.9, 0.9, 1.0)  # 白色
color_ramp.color_ramp.elements[1].position = 1.0
color_ramp.color_ramp.elements[1].color = (0.7, 0.7, 0.75, 1.0)  # 浅灰色

# 添加更多颜色停止点，创建大理石纹理
new_element = color_ramp.color_ramp.elements.new(0.3)
new_element.color = (0.85, 0.85, 0.9, 1.0)  # 浅蓝灰色
new_element = color_ramp.color_ramp.elements.new(0.7)
new_element.color = (0.8, 0.8, 0.85, 1.0)  # 另一种灰色

# 连接噪波纹理到颜色渐变
links.new(noise_texture.outputs['Fac'], color_ramp.inputs['Fac'])

# 创建第二个噪波纹理 - 用于大理石的细节纹理
noise_texture2 = nodes.new(type='ShaderNodeTexNoise')
noise_texture2.location = (-600, -200)
noise_texture2.inputs['Scale'].default_value = 15.0
noise_texture2.inputs['Detail'].default_value = 16.0
noise_texture2.inputs['Roughness'].default_value = 0.6

# 创建第二个颜色渐变 - 用于细节
color_ramp2 = nodes.new(type='ShaderNodeValToRGB')
color_ramp2.location = (-400, -200)
color_ramp2.color_ramp.elements[0].position = 0.4
color_ramp2.color_ramp.elements[0].color = (1.0, 1.0, 1.0, 1.0)
color_ramp2.color_ramp.elements[1].position = 0.6
color_ramp2.color_ramp.elements[1].color = (0.9, 0.9, 0.95, 1.0)

# 连接第二个噪波纹理到第二个颜色渐变
links.new(noise_texture2.outputs['Fac'], color_ramp2.inputs['Fac'])

# 创建混合节点 - 混合两种纹理
mix_node = nodes.new(type='ShaderNodeMixRGB')
mix_node.location = (-200, 0)
mix_node.blend_type = 'OVERLAY'
mix_node.inputs['Fac'].default_value = 0.3

# 连接两个颜色渐变到混合节点
links.new(color_ramp.outputs['Color'], mix_node.inputs['Color1'])
links.new(color_ramp2.outputs['Color'], mix_node.inputs['Color2'])

# 创建凹凸贴图节点 - 为大理石添加一些凹凸感
bump_node = nodes.new(type='ShaderNodeBump')
bump_node.location = (200, -200)
bump_node.inputs['Strength'].default_value = 0.2

# 连接混合节点到Principled BSDF和凹凸贴图
links.new(mix_node.outputs['Color'], principled_node.inputs['Base Color'])
links.new(mix_node.outputs['Color'], bump_node.inputs['Height'])
links.new(bump_node.outputs['Normal'], principled_node.inputs['Normal'])

# 添加纹理坐标节点
tex_coord = nodes.new(type='ShaderNodeTexCoord')
tex_coord.location = (-800, 0)

# 添加映射节点以控制纹理
mapping = nodes.new(type='ShaderNodeMapping')
mapping.location = (-700, 0)
mapping.inputs['Scale'].default_value[0] = 1.0
mapping.inputs['Scale'].default_value[1] = 1.0
mapping.inputs['Scale'].default_value[2] = 1.0

# 连接纹理坐标到映射
links.new(tex_coord.outputs['Generated'], mapping.inputs['Vector'])

# 连接映射到噪波纹理
links.new(mapping.outputs['Vector'], noise_texture.inputs['Vector'])
links.new(mapping.outputs['Vector'], noise_texture2.inputs['Vector'])

# 将材质分配给平面
if plane.data.materials:
    plane.data.materials[0] = marble_mat
else:
    plane.data.materials.append(marble_mat)

# 创建相机
bpy.ops.object.camera_add(location=(0, -3, 1.5), rotation=(math.radians(60), 0, 0))
camera = bpy.context.active_object
bpy.context.scene.camera = camera

# 创建灯光
bpy.ops.object.light_add(type='AREA', radius=1, location=(0, -2, 3))
light = bpy.context.active_object
light.data.energy = 500
light.data.size = 5

# 设置渲染引擎为Cycles以获得更好的质量
bpy.context.scene.render.engine = 'CYCLES'
bpy.context.scene.cycles.samples = 128  # 设置采样数量，增加此值可提高质量但会增加渲染时间

# 设置输出路径
output_path = os.path.join(os.path.expanduser("~"), "blender-marble-bg.jpg")
bpy.context.scene.render.filepath = output_path

# 渲染图像
bpy.ops.render.render(write_still=True)

print(f"大理石背景已渲染并保存到: {output_path}")
