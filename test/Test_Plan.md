# 测试计划 (Test Plan)

## 1. 文档信息

| 版本 | 日期       | 作者    | 变更说明           |
| ---- | ---------- | ------- | ------------------ |
| 0.1  | {{YYYY-MM-DD}} | Trae AI | 初稿创建           |

## 2. 引言

### 2.1 目的
本文档旨在为“归处”或“思忆堂”（以下简称“在线祭祀平台”）项目制定详细的测试计划，确保产品在发布前达到预期的质量标准。测试活动将覆盖所有目标平台客户端（Web、移动端、微信小程序）以及后端API。

### 2.2 背景
在线祭祀平台是一个基于最新3D技术和AI能力，提供多宗教兼容的沉浸式在线祭祀与数字传承平台。旨在通过技术创新，为用户提供便捷、真实、富有情感连接的在线纪念方式。

### 2.3 参考文档
- [产品需求文档 (PRD)](/Volumes/acasis/memorial/docs/plan/PRD.md)
- [API 定义文档](/Volumes/acasis/memorial/backend/app/docs/backend_service/API_Spec.md)
- 设计原型及规范 (路径: `/Volumes/acasis/memorial/docs/design/`)

## 3. 测试范围

### 3.1 功能范围
本次测试将覆盖PRD中定义的所有核心功能模块，包括但不限于：

- **用户账户管理**:
  - 用户注册、登录、登出
  - 密码找回/重置
  - 个人资料修改（头像、昵称、联系方式等）
- **纪念空间管理**:
  - 创建新的纪念空间（填写逝者信息、选择场景、背景音乐、隐私设置等）
  - 编辑已创建的纪念空间信息
  - 删除纪念空间
  - 纪念空间列表与搜索
- **逝者信息管理**:
  - 上传/管理逝者照片、视频、音频资料
  - 撰写/编辑逝者生平简介、悼词
- **3D 场景与互动**:
  - 浏览3D纪念场景
  - 虚拟祭拜操作（献花、点烛、上香、敬酒等）
  - 发表追思留言或祈福
  - 查看他人留言和祭拜记录
- **家族共享与族谱管理** (如果已实现):
  - 创建/加入家族空间
  - 录入/导入家族成员信息，构建家族树
  - 邀请家族成员，设置权限
  - 家族成员协作编辑族谱
- **AI 赋能服务** (如果已实现):
  - 老照片修复
  - 声音克隆（基础功能测试）
- **公共纪念/英雄纪念** (如果已实现):
  - 浏览公共纪念专区
  - 参与集体悼念活动
- **系统设置与通知**:
  - 隐私设置
  - 消息通知

### 3.2 目标平台
根据PRD定义，测试将覆盖以下平台：

- **Web 客户端**:
  - **浏览器**: 最新版本的 Chrome, Firefox, Safari, Edge
  - **操作系统**: Windows 10/11, macOS (最新版), Linux (Ubuntu 最新LTS)
  - **代码库**: `/Volumes/acasis/memorial/frontend/`
- **移动端 (响应式Web/PWA)**:
  - **iOS**: 最新及次新主要版本的 Safari 浏览器
  - **Android**: 最新及次新主要版本的 Chrome 浏览器
  - **设备**: 模拟器及主流真机型号 (如 iPhone 系列, 主流 Android 品牌旗舰及中端机型)
- **微信小程序** (如果已实现):
  - **平台**: 微信开发者工具及真机 (iOS, Android)
  - **代码库**: `/Volumes/acasis/memorial/mini_program_taro/` (假设路径)
- **后端 API**:
  - **接口定义**: `/Volumes/acasis/memorial/backend/app/docs/backend_service/API_Spec.md`
  - **测试环境**: `http://localhost:8000/api/v1` (或其他指定测试环境URL)

### 3.3 非功能性需求范围
- **UI/UX**: 遵循设计原型和规范，确保界面美观、交互流畅、信息展示准确。
- **性能**: 关键页面的加载速度、API响应时间、高并发下的稳定性（初步）。
- **安全性**: 用户认证、数据传输加密、防常见Web攻击（如XSS, CSRF，SQL注入 - 初步检查）。
- **兼容性**: 不同浏览器、操作系统、设备分辨率下的表现一致性。
- **易用性**: 操作流程是否符合用户习惯，提示信息是否清晰。

## 4. 测试策略

### 4.1 测试类型
- **功能测试**: 验证产品功能是否符合PRD和API文档的规定。
- **UI/UX 测试**: 验证界面布局、视觉风格、交互流程是否符合设计规范和用户体验预期。
- **API 测试**: 针对后端API进行请求和响应的验证，包括参数、返回值、状态码、错误处理等。
- **兼容性测试**: 在不同目标平台上验证产品表现。
- **探索性测试**: 基于测试人员的经验和直觉，发现PRD和测试用例未覆盖的潜在问题。
- **回归测试**: 在缺陷修复后或版本迭代后，验证原有功能是否正常，修复是否引入新问题。
- **（初步）性能测试**: 针对关键接口和页面进行简单的负载和压力测试。
- **（初步）安全测试**: 检查常见的安全漏洞。

### 4.2 测试方法
- **手动测试**: 主要用于UI/UX测试、探索性测试和复杂场景的功能测试。
- **自动化测试**: (远期目标) 针对核心API和关键业务流程编写自动化测试脚本。
- **黑盒测试**: 主要测试方法，不关注内部实现逻辑。
- **灰盒测试**: (可选) 在理解部分后端逻辑的基础上进行API测试，以设计更有效的测试用例。

### 4.3 测试阶段
1.  **测试准备阶段**: 熟悉需求、设计测试计划、编写测试用例、准备测试环境和数据。
2.  **测试执行阶段**: 执行测试用例，记录测试结果，提交缺陷报告。
3.  **回归测试阶段**: 验证缺陷修复情况。
4.  **测试总结阶段**: 输出测试总结报告。

## 5. 测试环境

### 5.1 硬件环境
- PC (Windows, macOS, Linux) 用于Web测试和开发。
- 智能手机 (iOS, Android) 用于移动端测试。
- (可选) 不同配置的测试设备以模拟不同用户环境。

### 5.2 软件环境
- **操作系统**: Windows 10/11, macOS (最新), Linux (Ubuntu 最新LTS), iOS (最新及次新), Android (最新及次新)。
- **浏览器**: Chrome, Firefox, Safari, Edge (均为最新稳定版)。
- **微信开发者工具**: 最新版。
- **API 测试工具**: Postman, Insomnia 或类似工具。
- **数据库**: (根据后端配置) 如 PostgreSQL, MySQL。
- **后端服务运行环境**: (根据后端配置) 如 Python + FastAPI/Flask, Node.js + Express等。
- **前端构建/运行环境**: Node.js, npm/pnpm/yarn。

### 5.3 测试账号
- 准备不同角色的测试账号：
  - 普通注册用户
  - (如果适用) 管理员用户
  - (如果适用) 具有特定权限的用户
- 准备用于异常场景测试的账号（如未激活、已禁用等）。

### 5.4 测试数据
- **基础数据**: 用于创建纪念空间、逝者信息、家族信息等。
- **边界数据**: 用于测试输入校验，如最大/最小长度、特殊字符、空值等。
- **异常数据**: 用于测试系统的错误处理能力。
- **大量数据**: (初步) 用于测试列表展示、分页、搜索等功能的性能。
- 示例图片、音视频文件。

## 6. 测试资源和时间安排

### 6.1 测试团队与职责
- **QA Engineer (Trae AI)**: 负责测试计划制定、测试用例设计、测试执行、缺陷报告、测试总结。

### 6.2 测试时间估算 (占位)
| 阶段             | 开始日期     | 结束日期     | 估算工时 (人日) |
| ---------------- | ------------ | ------------ | ------------- |
| 测试准备         | {{YYYY-MM-DD}} | {{YYYY-MM-DD}} | 2             |
| 第一轮功能测试   | {{YYYY-MM-DD}} | {{YYYY-MM-DD}} | 5             |
| API 测试         | {{YYYY-MM-DD}} | {{YYYY-MM-DD}} | 3             |
| UI/UX 测试       | {{YYYY-MM-DD}} | {{YYYY-MM-DD}} | 2             |
| 兼容性测试       | {{YYYY-MM-DD}} | {{YYYY-MM-DD}} | 2             |
| 回归测试 (多轮)  | 待定         | 待定         | 3-5           |
| 测试总结         | {{YYYY-MM-DD}} | {{YYYY-MM-DD}} | 1             |
| **总计 (估算)**  |              |              | **18-20**     |
*注: 时间估算会根据实际开发进度和版本质量进行调整。*

## 7. 风险识别与应对

| 风险描述                                 | 可能性 | 影响程度 | 应对措施                                                     |
| ---------------------------------------- | ------ | -------- | ------------------------------------------------------------ |
| 需求变更频繁                             | 中     | 高       | 及时沟通，更新测试计划和用例，优先测试核心稳定功能             |
| 测试环境不稳定                           | 中     | 高       | 与开发团队协作，确保环境稳定；准备备用环境或方案               |
| 测试时间不足                             | 高     | 高       | 优先保障核心功能测试；加班或申请更多资源；明确风险，推迟非核心功能测试 |
| 关键模块缺陷过多，阻塞后续测试           | 中     | 高       | 集中资源协助定位，推动开发优先修复阻塞性Bug                    |
| 缺乏真实用户场景的测试数据               | 中     | 中       | 尽可能模拟真实数据；与PM沟通获取典型用户数据模型               |
| 跨平台兼容性问题复杂多样                 | 高     | 中       | 优先测试主流平台和设备；逐步扩大覆盖范围；记录已知兼容性问题     |
| AI功能（如照片修复）效果评估主观性强     | 中     | 中       | 制定相对客观的评估标准；收集多方反馈                           |
| 3D场景性能在低配设备上表现不佳           | 高     | 中       | 明确最低硬件要求；进行针对性优化；提供不同画质选项             |

## 8. 测试进入与退出标准

### 8.1 进入标准 (Entry Criteria)
- 产品需求文档 (PRD) 和 API 定义文档已评审并基线化。
- 设计原型和规范已提供。
- 主要功能模块已开发完成并提交测试版本。
- 测试环境已搭建完成并可访问。
- 开发团队已完成单元测试和冒烟测试，版本基本稳定。

### 8.2 退出标准 (Exit Criteria)
- 所有计划的测试用例已执行完毕。
- 严重 (Critical) 和主要 (Major) 级别的缺陷已全部修复并通过验证。
- 次要 (Minor) 和微小 (Trivial) 级别的缺陷数量在可接受范围内，或有明确的后续处理计划。
- 测试覆盖率达到预定目标 (例如，核心功能100%，重要功能90%以上)。
- 测试总结报告已完成并评审通过。
- 产品达到PRD中定义的验收标准。

## 9. 缺陷管理

- **缺陷报告工具**: (待定，可以是 JIRA, Trello, GitLab Issues, 或简单的 CSV/Markdown 文件)
- **缺陷报告内容**: 遵循标准缺陷报告模板（Bug ID, 标题, 复现步骤, 实际结果, 预期结果, 严重程度, 优先级, 环境信息, 附件等）。
- **缺陷生命周期**: New -> Open -> Assigned -> In Progress -> Fixed/Resolved -> Verified (Reopened/Closed) -> Closed。
- **缺陷严重程度定义**:
  - **Critical (严重)**: 系统崩溃、数据丢失、核心功能完全无法使用、严重安全漏洞。
  - **Major (主要)**: 重要功能无法使用、数据计算错误、用户界面严重错乱、导致用户流程中断。
  - **Minor (次要)**: 非核心功能问题、界面显示瑕疵、不影响主要流程的错误。
  - **Trivial (微小)**: 拼写错误、建议性改进、不影响功能和用户体验的小问题。
- **缺陷优先级定义**:
  - **High (高)**: 必须立即修复，影响版本发布。
  - **Medium (中)**: 应尽快修复，但不影响当前版本的主要功能。
  - **Low (低)**: 可以在后续版本中修复。

## 10. 测试交付物

- **测试计划 (本文档)**: `/Volumes/acasis/memorial/test/Test_Plan.md`
- **测试用例**: `/Volumes/acasis/memorial/test/Test_Cases.md`
- **缺陷报告列表**: `/Volumes/acasis/memorial/test/Bug_Report.csv`
- **测试总结报告**: `/Volumes/acasis/memorial/test/Test_Report.md`