#!/bin/bash

# 启动 Memorial 项目服务器脚本
# 使用方法: ./start_server.sh

set -e  # 遇到错误时退出

# 切换到项目根目录
cd "$(dirname "$0")"

echo "🚀 启动 Memorial 项目服务器..."

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)/backend"
export DATABASE_URL="postgresql://memorial:memorial@localhost/memorial"
export SECRET_KEY="memorial-dev-secret-key-2025"
export JWT_SECRET_KEY="memorial-jwt-secret-key-2025"
export REPLICATE_API_TOKEN="${REPLICATE_API_TOKEN:-demo-token}"

# 后端端口配置 (使用8008端口避免冲突)
BACKEND_PORT=8008
FRONTEND_PORT=4001

echo "🔧 环境配置:"
echo "  - 后端端口: ${BACKEND_PORT}"
echo "  - 前端端口: ${FRONTEND_PORT}"
echo "  - 数据库: ${DATABASE_URL}"

# 检查并激活 conda 环境
if command -v conda &> /dev/null; then
    echo "📦 激活 conda 环境: memorial"
    source "$(conda info --base)/etc/profile.d/conda.sh"
    conda activate memorial || echo "⚠️  conda环境激活失败，继续使用系统Python"
else
    echo "⚠️  未找到 conda，使用系统 Python 环境"
fi

# 检查Python和Node.js环境
echo "🔍 检查环境依赖..."
if ! command -v python &> /dev/null && ! command -v python3 &> /dev/null; then
    echo "❌ 未找到 Python，请先安装 Python 3.11+"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ 未找到 npm，请先安装 Node.js 18+"
    exit 1
fi

# 启动后端服务器
echo "🔧 启动后端 FastAPI 服务器 (端口: ${BACKEND_PORT})..."
cd backend

# 检查依赖是否已安装
if ! python -c "import fastapi" &> /dev/null 2>&1; then
    echo "📥 安装后端依赖..."
    pip install -r requirements.txt
fi

# 检查数据库连接将在后端启动后进行

# 启动 FastAPI 服务器
echo "✅ 后端服务器启动中..."
uvicorn app.main:app --host 0.0.0.0 --port ${BACKEND_PORT} --reload --log-level info &
BACKEND_PID=$!

# 等待后端启动并检查健康状态
echo "⏳ 等待后端服务启动..."
for i in {1..30}; do
    if curl -s http://localhost:${BACKEND_PORT}/health > /dev/null 2>&1; then
        echo "✅ 后端服务启动成功"
        break
    fi
    if [ $i -eq 30 ]; then
        echo "❌ 后端服务启动超时"
        kill $BACKEND_PID 2>/dev/null
        exit 1
    fi
    sleep 1
done

# 启动前端服务器
echo "🎨 启动前端 Vite 服务器 (端口: ${FRONTEND_PORT})..."
cd ../frontend

# 检查依赖是否已安装
if [ ! -d "node_modules" ] || [ ! -f "node_modules/.package-lock.json" ]; then
    echo "📥 安装前端依赖..."
    npm install
fi

# 设置前端环境变量
export VITE_APP_API_BASE_URL="http://localhost:${BACKEND_PORT}/api/v1"
export VITE_APP_BACKEND_URL="http://localhost:${BACKEND_PORT}"

# 启动前端服务器
echo "✅ 前端服务器启动中..."
npm run dev -- --port ${FRONTEND_PORT} &
FRONTEND_PID=$!

# 等待前端启动
echo "⏳ 等待前端服务启动..."
for i in {1..20}; do
    if curl -s http://localhost:${FRONTEND_PORT} > /dev/null 2>&1; then
        echo "✅ 前端服务启动成功"
        break
    fi
    if [ $i -eq 20 ]; then
        echo "❌ 前端服务启动超时"
        kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
        exit 1
    fi
    sleep 1
done

echo ""
echo "🎉 Memorial 项目启动完成！"
echo ""
echo "📍 服务地址:"
echo "  🌐 前端应用: http://localhost:${FRONTEND_PORT}"
echo "  🔧 后端 API: http://localhost:${BACKEND_PORT}"
echo "  📚 API 文档: http://localhost:${BACKEND_PORT}/docs"
echo "  🔍 健康检查: http://localhost:${BACKEND_PORT}/health"
echo ""
echo "🛠️  开发工具:"
echo "  📊 数据库管理: http://localhost:${BACKEND_PORT}/db-admin (如果可用)"
echo "  🔧 API 测试: http://localhost:${BACKEND_PORT}/redoc"
echo ""
echo "💡 按 Ctrl+C 停止所有服务器"

# 优雅关闭处理
cleanup() {
    echo ""
    echo "🛑 正在停止服务器..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null && echo "  ✅ 后端服务已停止"
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null && echo "  ✅ 前端服务已停止"
    fi
    echo "🎯 所有服务已停止，再见！"
    exit 0
}

trap cleanup INT TERM

# 保持脚本运行，监控子进程
while true; do
    # 检查后端进程是否还在运行
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        echo "❌ 后端服务意外停止"
        kill $FRONTEND_PID 2>/dev/null
        exit 1
    fi
    
    # 检查前端进程是否还在运行
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "❌ 前端服务意外停止"
        kill $BACKEND_PID 2>/dev/null
        exit 1
    fi
    
    sleep 5
done