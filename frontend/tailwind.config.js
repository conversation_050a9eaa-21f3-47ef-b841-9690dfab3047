/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./public/**/*.html",
  ],
  safelist: [
    'text-amber-500',
    'bg-gray-900',
    'focus:ring-amber-500',
    'border-amber-500',
    'hover:bg-amber-500',
    'ring-offset-gray-900',
    'focus-within:ring-amber-500',
    'ring-amber-500',
    'hover:shadow-amber-500/20',
    'focus:ring-offset-gray-900',
    // 动态构建的类名
    'text-red-500',
    'text-green-500',
    'text-red-600',
    'text-green-600',
    'border-red-500',
    'border-green-500',
    'bg-red-600',
    'bg-green-600',
    'hover:bg-red-500',
    'hover:bg-green-500',
    // 按钮相关的动态类
    'ring-2',
    'ring-amber-500',
    'opacity-50',
    'cursor-not-allowed',
    'cursor-wait',
    'opacity-100',
    // 图标相关的动态类
    'fa-solid',
    'fa-star',
    'fa-check',
    'fa-edit',
    'fa-chevron-up',
    'fa-chevron-down',
    'fa-volume-mute',
    'fa-volume-up',
    'fa-volume-down',
    'fas',
    // 祭品系统相关的动态类
    'offering-incense',
    'offering-flower',
    'offering-food',
    'offering-candle',
    // 状态相关的动态类
    'completed',
    'active',
    'muted',
    'small',
    // 颜色相关的动态类
    'text-amber-500',
    'text-green-400',
    'text-gray-300',
    'text-white',
    'text-gray-900',
    'border-amber-500',
    'border-green-400',
    'border-gray-500',
    'border-opacity-50',
    'border-opacity-30',
    'bg-amber-500',
    'bg-green-400',
    'bg-gray-500',
    'bg-gray-900',
    'bg-opacity-20',
    // 尺寸相关的动态类
    'w-1/4',
    'w-40',
    'w-48',
    'text-sm',
    'text-xs',
    'text-3xl',
    'text-2xl',
    // 其他可能动态生成的类
    'group-hover:opacity-100',
    'transition-opacity',
    'duration-200',
    'duration-300',
    'cursor-default',
    'shadow-md',
    'rounded-md',
    'focus:ring-2',
    'focus:ring-amber-500',
    'focus:border-transparent',
    'outline-none',
    'transition',
    'duration-150',
    'ease-in-out',
    'rounded-lg',
    'rounded-full',
    'border-2',
    'w-8',
    'h-8',
    'flex',
    'items-center',
    'justify-center',
    'mt-2',
    'font-medium',
    'pl-10',
    'pr-3',
    'py-3',
    'p-3',
    'mr-2',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: [
          '-apple-system',
          'BlinkMacSystemFont',
          '"Segoe UI"',
          'Roboto',
          '"Helvetica Neue"',
          'Arial',
          'sans-serif'
        ],
        // 中文字体系统
        'chinese': [
          '"PingFang SC"',
          '"Hiragino Sans GB"',
          '"Source Han Sans CN"',
          '"Noto Sans CJK SC"',
          '"Microsoft YaHei"',
          'sans-serif'
        ],
        'mono': [
          '"SF Mono"',
          'Monaco',
          '"Cascadia Code"',
          '"Roboto Mono"',
          'Consolas',
          'monospace'
        ],
      },
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
        '5xl': ['3rem', { lineHeight: '1' }],
        '6xl': ['3.75rem', { lineHeight: '1' }],
        '7xl': ['4.5rem', { lineHeight: '1' }],
        '8xl': ['6rem', { lineHeight: '1' }],
        '9xl': ['8rem', { lineHeight: '1' }],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
        '144': '36rem',
      },
      borderRadius: {
        '4xl': '2rem',
        '5xl': '2.5rem',
      },
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'strong': '0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1)',
        'glow': '0 0 20px rgba(74, 144, 226, 0.3)',
        'glow-warm': '0 0 20px rgba(245, 166, 35, 0.3)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'fade-out': 'fadeOut 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'bounce-gentle': 'bounceGentle 0.6s ease-in-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'spin-slow': 'spin 3s linear infinite',
        'float': 'float 3s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeOut: {
          '0%': { opacity: '1' },
          '100%': { opacity: '0' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        bounceGentle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
      },
      backdropBlur: {
        xs: '2px',
      },
      screens: {
        'xs': '475px',
        '3xl': '1600px',
      },
      colors: {
        // 品牌主色调 - 优化对比度
        'primary': {
          DEFAULT: '#1a5e5a', // 深灰绿 - 提高对比度
          50: '#f0faf9',
          100: '#d1f2ef',
          200: '#a3e5df',
          300: '#75d8cf',
          400: '#47cbbf',
          500: '#1a5e5a', // 主色 - 更深的绿色提高对比度
          600: '#164e4a',
          700: '#123e3a',
          800: '#0e2e2a',
          900: '#0a1e1a',
        },
        'secondary': {
          DEFAULT: '#FFA631', // 杏黄 - 匹配设计要求
          50: '#fff8e1',
          100: '#ffecb3',
          200: '#ffe082',
          300: '#ffd54f',
          400: '#ffca28',
          500: '#FFA631', // 主色 - 杏黄
          600: '#e6941a', // 杏黄悬停
          700: '#cc8400',
          800: '#b37400',
          900: '#996400',
        },
        // 添加更好的文本颜色
        'text': {
          'primary': '#1f2937', // 深灰色文本
          'secondary': '#6b7280', // 中等灰色文本
          'light': '#9ca3af', // 浅灰色文本
          'inverse': '#ffffff', // 白色文本（用于深色背景）
        },
        // 添加更好的背景颜色
        'background': {
          'primary': '#ffffff',
          'secondary': '#f9fafb',
          'tertiary': '#f3f4f6',
        },
      },
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      },
      transitionDuration: {
        '400': '400ms',
        '600': '600ms',
      },
      aspectRatio: {
        '4/3': '4 / 3',
        '3/2': '3 / 2',
        '2/3': '2 / 3',
        '9/16': '9 / 16',
      },
    },
  },
  plugins: [
    // 可以添加官方插件
    // require('@tailwindcss/forms'),
    // require('@tailwindcss/typography'),
    // require('@tailwindcss/aspect-ratio'),
    // require('@tailwindcss/container-queries'),
  ],
  // 优化配置
  corePlugins: {
    // 如果不需要某些功能可以禁用以减小文件大小
    // preflight: false,
  },
  // 实验性功能
  future: {
    hoverOnlyWhenSupported: true,
  },
}