import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import "./App.css";
import { AuthProvider } from "./contexts/AuthContext";
import ProtectedRoute from "./components/ProtectedRoute";
import PublicRoute from "./components/PublicRoute";
import Home from "./components/Home";
import MemorialScene from "./scenes/MemorialScene";
import DemoNavigator from "./scenes/DemoNavigator";
import AdminPanel from "./components/AdminPanel";
import BabylonScene from "./components/BabylonScene";
import ModelViewer from "./components/ModelViewer";
import BuddhistTemple from "./scenes/BuddhistTemple";
import PhysicsDemo from "./scenes/PhysicsDemo";
import BabylonMobileOptimizationDemo from "./scenes/BabylonMobileOptimizationDemo";
import BabylonPerformanceDemo from "./scenes/BabylonPerformanceDemo";
import AIFeaturesPage from "./pages/AIFeaturesPage";
import LoginPage from "./pages/LoginPage"; // 导入登录页面组件
import RegisterPage from "./pages/RegisterPage"; // 导入注册页面组件
import ForgotPasswordPage from "./pages/ForgotPasswordPage"; // 导入忘记密码页面组件
import ResetPasswordPage from "./pages/ResetPasswordPage"; // 导入重置密码页面组件
import VerifyEmailPage from "./pages/VerifyEmailPage"; // 导入邮箱验证页面组件
import NotificationContainer from "./components/NotificationContainer"; // 导入通知容器组件
import CreateMemorialPage from "./pages/CreateMemorialPage"; // 导入创建纪念空间页面组件
import SceneSelectionPage from "./pages/SceneSelectionPage"; // 导入选择场景页面组件
import CompletionPage from "./pages/CompletionPage"; // 导入完成创建页面组件
import DashboardPage from "./pages/DashboardPage"; // 导入仪表盘页面组件
import FamilyTreePage from "./pages/FamilyTreePage"; // 导入家族树页面组件
import AIRepairPage from "./pages/AIRepairPage"; // 导入AI照片修复页面组件
import StorePage from "./pages/StorePage"; // 导入祭品商城页面组件
import SettingsPage from "./pages/SettingsPage"; // 导入账户设置页面组件
import WorshipPage from "./pages/WorshipPage"; // 导入祭拜互动页面组件
import NotFoundPage from "./pages/NotFoundPage"; // 导入未找到页面组件
import ComponentTest from "./pages/ComponentTest"; // 导入组件测试页面
import DatabaseAdminPage from "./pages/DatabaseAdminPage"; // 导入数据库管理页面
import HeroesMemorialPage from "./pages/HeroesMemorialPage"; // 导入英雄纪念馆页面
import HeroMemorialDetailPage from "./pages/HeroMemorialDetailPage"; // 导入英雄个人纪念空间页面

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <NotificationContainer />
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/memorial/:style" element={<MemorialScene />} />
            <Route path="/admin" element={<AdminPanel />} />
            <Route path="/demo" element={<DemoNavigator />} />

            {/* Babylon.js 路由 */}
            <Route path="/babylon" element={<BabylonScene />} />
            <Route
              path="/model-viewer"
              element={<ModelViewer modelPath="/models/buddhist-temple.glb" />}
            />
            <Route
              path="/memorial/buddhist-temple"
              element={<BuddhistTemple />}
            />
            <Route path="/demo/babylon-physics" element={<PhysicsDemo />} />
            <Route
              path="/demo/babylon-mobile"
              element={<BabylonMobileOptimizationDemo />}
            />
            <Route
              path="/demo/babylon-performance"
              element={<BabylonPerformanceDemo />}
            />

            {/* AI 功能路由 */}
            <Route path="/ai-features" element={<AIFeaturesPage />} />

            {/* 公共路由（未登录用户可访问） */}
            <Route
              path="/login"
              element={
                <PublicRoute>
                  <LoginPage />
                </PublicRoute>
              }
            />
            <Route
              path="/register"
              element={
                <PublicRoute>
                  <RegisterPage />
                </PublicRoute>
              }
            />
            <Route
              path="/forgot-password"
              element={
                <PublicRoute>
                  <ForgotPasswordPage />
                </PublicRoute>
              }
            />
            <Route
              path="/reset-password"
              element={
                <PublicRoute>
                  <ResetPasswordPage />
                </PublicRoute>
              }
            />
            <Route
              path="/verify-email"
              element={
                <PublicRoute>
                  <VerifyEmailPage />
                </PublicRoute>
              }
            />

            {/* 受保护的路由（需要登录） */}
            <Route
              path="/create-memorial"
              element={
                <ProtectedRoute>
                  <CreateMemorialPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/scene-selection"
              element={
                <ProtectedRoute>
                  <SceneSelectionPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/completion"
              element={
                <ProtectedRoute>
                  <CompletionPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <DashboardPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/family-tree"
              element={
                <ProtectedRoute>
                  <FamilyTreePage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/ai-repair"
              element={
                <ProtectedRoute>
                  <AIRepairPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/store"
              element={
                <ProtectedRoute>
                  <StorePage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/settings"
              element={
                <ProtectedRoute>
                  <SettingsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/worship"
              element={
                <ProtectedRoute>
                  <WorshipPage />
                </ProtectedRoute>
              }
            />

            {/* 组件测试页面路由 */}
            <Route path="/component-test" element={<ComponentTest />} />
            
            {/* 数据库管理页面路由（需要管理员权限） */}
            <Route
              path="/db-admin"
              element={
                <ProtectedRoute>
                  <DatabaseAdminPage />
                </ProtectedRoute>
              }
            />

            {/* 英雄纪念馆路由 */}
            <Route path="/heroes" element={<HeroesMemorialPage />} />
            <Route path="/memorial/hero/:heroId" element={<HeroMemorialDetailPage />} />

            {/* 通配符路由，匹配所有未定义的路径 */}
            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
