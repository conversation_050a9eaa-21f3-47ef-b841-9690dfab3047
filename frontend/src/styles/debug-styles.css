/**
 * 样式调试工具
 * 用于开发阶段快速定位和调试样式问题
 * 使用方法：在需要调试时导入此文件
 */

/* ==========================================================================
   调试边框 - 快速查看元素边界
   ========================================================================== */

/* 调试模式开关 */
.debug-mode [class] {
  outline: 1px solid rgba(var(--color-red-rgb), 0.3) !important;
}

.debug-mode [class]:hover {
  outline: 2px solid rgba(var(--color-red-rgb), 0.8) !important;
  background-color: rgba(var(--color-red-rgb), 0.1) !important;
}

/* 布局调试 */
.debug-layout {
  position: relative;
}

.debug-layout::before {
  content: attr(class);
  position: absolute;
  top: -20px;
  left: 0;
  background: rgba(var(--color-gray-900-rgb), 0.8);
  color: white;
  padding: 2px 6px;
  font-size: 10px;
  font-family: monospace;
  border-radius: 2px;
  z-index: 9999;
  pointer-events: none;
}

/* Flexbox 调试 */
.debug-flex {
  background: repeating-linear-gradient(
    45deg,
    rgba(var(--color-red-rgb), 0.1),
    rgba(var(--color-red-rgb), 0.1) 10px,
    rgba(var(--color-green-rgb), 0.1) 10px,
    rgba(var(--color-green-rgb), 0.1) 20px
  ) !important;
}

.debug-flex > [class] {
  outline: 1px dashed rgba(var(--color-blue-rgb), 0.5) !important;
  background: rgba(var(--color-blue-rgb), 0.1) !important;
}

/* Grid 调试 */
.debug-grid {
  background:
    repeating-linear-gradient(
      0deg,
      rgba(var(--color-red-rgb), 0.1),
      rgba(var(--color-red-rgb), 0.1) 1px,
      transparent 1px,
      transparent 20px
    ),
    repeating-linear-gradient(
      90deg,
      rgba(var(--color-red-rgb), 0.1),
      rgba(var(--color-red-rgb), 0.1) 1px,
      transparent 1px,
      transparent 20px
    ) !important;
}

.debug-grid > [class] {
  outline: 1px solid rgba(var(--color-orange-rgb), 0.7) !important;
  background: rgba(var(--color-orange-rgb), 0.1) !important;
}

/* ==========================================================================
   样式冲突检测
   ========================================================================== */

/* 检测重复定义的样式 */
.debug-conflicts {
  position: relative;
}

.debug-conflicts::after {
  content: "⚠️ 可能存在样式冲突";
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(var(--color-orange-rgb), 0.9);
  color: white;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: bold;
  border-radius: 0 0 0 4px;
  z-index: 9999;
  pointer-events: none;
}

/* 检测硬编码颜色 */
.debug-hardcoded-colors {
  position: relative;
}

.debug-hardcoded-colors::before {
  content: "🎨 使用硬编码颜色";
  position: absolute;
  bottom: 0;
  left: 0;
  background: rgba(var(--color-red-rgb), 0.9);
  color: white;
  padding: 2px 6px;
  font-size: 10px;
  border-radius: 0 4px 0 0;
  z-index: 9999;
  pointer-events: none;
}

/* 检测未使用CSS变量 */
.debug-no-variables {
  position: relative;
}

.debug-no-variables::after {
  content: "🔧 建议使用CSS变量";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(var(--color-blue-rgb), 0.9);
  color: white;
  padding: 4px 8px;
  font-size: 11px;
  border-radius: 4px;
  z-index: 9999;
  pointer-events: none;
  white-space: nowrap;
}

/* ==========================================================================
   响应式调试
   ========================================================================== */

/* 断点指示器 */
.debug-breakpoints::before {
  content: "📱 XS";
  position: fixed;
  top: 10px;
  right: 10px;
  background: rgba(var(--color-gray-900-rgb), 0.8);
  color: white;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: bold;
  border-radius: 4px;
  z-index: var(--z-toast);
  font-family: monospace;
}

@media (min-width: 640px) {
  .debug-breakpoints::before {
    content: "📱 SM (≥640px)";
    background: rgba(var(--color-green-rgb), 0.8);
  }
}

@media (min-width: 768px) {
  .debug-breakpoints::before {
    content: "💻 MD (≥768px)";
    background: rgba(var(--color-blue-rgb), 0.8);
  }
}

@media (min-width: 1024px) {
  .debug-breakpoints::before {
    content: "🖥️ LG (≥1024px)";
    background: rgba(var(--color-purple-rgb), 0.8);
  }
}

@media (min-width: 1280px) {
  .debug-breakpoints::before {
    content: "🖥️ XL (≥1280px)";
    background: rgba(var(--color-red-rgb), 0.8);
  }
}

@media (min-width: 1536px) {
  .debug-breakpoints::before {
    content: "🖥️ 2XL (≥1536px)";
    background: rgba(var(--color-red-rgb), 0.8);
  }
}

/* ==========================================================================
   性能调试
   ========================================================================== */

/* 检测昂贵的CSS属性 */
.debug-performance {
  position: relative;
}

/* 检测box-shadow过多 */
.debug-performance[style*="box-shadow"]::before {
  content: "⚡ 注意box-shadow性能";
  position: absolute;
  top: -25px;
  left: 0;
  background: rgba(var(--color-orange-rgb), 0.9);
  color: white;
  padding: 2px 6px;
  font-size: 10px;
  border-radius: 2px;
  z-index: 9999;
  pointer-events: none;
}

/* 检测transform过多 */
.debug-performance[style*="transform"]::after {
  content: "🔄 Transform使用";
  position: absolute;
  bottom: -25px;
  right: 0;
  background: rgba(var(--color-green-rgb), 0.9);
  color: white;
  padding: 2px 6px;
  font-size: 10px;
  border-radius: 2px;
  z-index: 9999;
  pointer-events: none;
}

/* ==========================================================================
   可访问性调试
   ========================================================================== */

/* 检测缺少alt属性的图片 */
img:not([alt]) {
  outline: 3px solid red !important;
}

img:not([alt])::after {
  content: "❌ 缺少alt属性";
  position: absolute;
  background: red;
  color: white;
  padding: 4px;
  font-size: 12px;
  z-index: 9999;
}

/* 检测缺少label的输入框 */
input:not([aria-label]):not([aria-labelledby]) {
  outline: 3px solid orange !important;
}

/* 检测对比度不足 */
.debug-contrast-low {
  background: repeating-linear-gradient(
    45deg,
    rgba(var(--color-yellow-rgb), 0.3),
    rgba(var(--color-yellow-rgb), 0.3) 5px,
    transparent 5px,
    transparent 10px
  ) !important;
}

.debug-contrast-low::before {
  content: "⚠️ 对比度可能不足";
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(var(--color-yellow-rgb), 0.9);
  color: black;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: bold;
  z-index: 9999;
  pointer-events: none;
}

/* ==========================================================================
   Z-index 调试
   ========================================================================== */

/* Z-index 可视化 */
.debug-z-index {
  position: relative;
}

.debug-z-index::before {
  content: "z: " attr(style);
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(var(--color-purple-rgb), 0.9);
  color: white;
  padding: 2px 6px;
  font-size: 10px;
  font-family: monospace;
  border-radius: 0 0 0 4px;
  z-index: 10001;
  pointer-events: none;
}

/* 高z-index警告 */
[style*="z-index: 999"],
[style*="z-index:999"] {
  outline: 3px solid purple !important;
}

[style*="z-index: 999"]::after,
[style*="z-index:999"]::after {
  content: "🚨 Z-index过高";
  position: absolute;
  bottom: 0;
  right: 0;
  background: purple;
  color: white;
  padding: 4px;
  font-size: 11px;
  font-weight: bold;
  z-index: 10002;
  pointer-events: none;
}

/* ==========================================================================
   CSS变量调试
   ========================================================================== */

/* 显示CSS变量值 */
.debug-css-vars {
  position: relative;
}

.debug-css-vars::before {
  content: "CSS变量: " attr(style);
  position: absolute;
  bottom: 100%;
  left: 0;
  background: rgba(var(--color-teal-rgb), 0.9);
  color: white;
  padding: 4px 8px;
  font-size: 10px;
  font-family: monospace;
  border-radius: 4px 4px 0 0;
  z-index: 9999;
  pointer-events: none;
  white-space: nowrap;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* ==========================================================================
   调试工具栏
   ========================================================================== */

.debug-toolbar {
  position: fixed;
  bottom: 20px;
  left: 20px;
  background: rgba(var(--color-gray-900-rgb), 0.9);
  color: white;
  padding: 12px;
  border-radius: 8px;
  font-family: monospace;
  font-size: 12px;
  z-index: var(--z-toast);
  box-shadow: 0 4px 12px rgba(var(--color-gray-900-rgb), 0.3);
}

.debug-toolbar h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--color-accent);
}

.debug-toolbar button {
  background: rgba(var(--color-surface-rgb), 0.1);
  border: 1px solid rgba(var(--color-surface-rgb), 0.3);
  color: white;
  padding: 4px 8px;
  margin: 2px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
}

.debug-toolbar button:hover {
  background: rgba(var(--color-surface-rgb), 0.2);
}

.debug-toolbar button.active {
  background: rgba(var(--color-green-rgb), 0.3);
  border-color: var(--color-accent);
}

/* ==========================================================================
   打印样式调试
   ========================================================================== */

@media print {
  .debug-print {
    outline: 2px solid red !important;
  }

  .debug-print::before {
    content: "打印时可见";
    background: red;
    color: white;
    padding: 2px 4px;
    font-size: 10px;
  }
}

/* ==========================================================================
   暗色模式调试
   ========================================================================== */

@media (prefers-color-scheme: dark) {
  .debug-dark-mode::before {
    content: "🌙 暗色模式";
    position: fixed;
    top: 50px;
    right: 10px;
    background: rgba(var(--color-gray-900-rgb), 0.8);
    color: white;
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 4px;
    z-index: var(--z-toast);
  }
}

/* ==========================================================================
   动画调试
   ========================================================================== */

/* 减慢所有动画用于调试 */
.debug-slow-animations [class] {
  animation-duration: 3s !important;
  animation-delay: 0.5s !important;
  transition-duration: 1s !important;
}

/* 显示动画边界 */
.debug-animations [style*="animation"],
.debug-animations [class*="animate"] {
  outline: 2px dashed rgba(var(--color-magenta-rgb), 0.8) !important;
}

.debug-animations [style*="animation"]::before,
.debug-animations [class*="animate"]::before {
  content: "🎬 动画元素";
  position: absolute;
  top: -20px;
  left: 0;
  background: rgba(var(--color-magenta-rgb), 0.9);
  color: white;
  padding: 2px 6px;
  font-size: 10px;
  border-radius: 2px;
  z-index: 9999;
  pointer-events: none;
}
