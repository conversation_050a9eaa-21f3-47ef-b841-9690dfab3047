/* 全局基础样式 - 从 unified-styles.css 移动到此处以避免样式冲突 */

/* CSS变量系统 - 统一颜色和尺寸管理 */
:root {
  /* 主色调系统 - 三色配色方案 */
  --color-primary: #185a56; /* 深灰绿 */
  --color-primary-hover: #134a44; /* 深灰绿悬停 */
  --color-primary-light: #2a7a71; /* 深灰绿浅色 */
  --color-primary-dark: #0f3e3b; /* 深灰绿深色 */
  --color-primary-rgb: 24, 90, 86; /* 深灰绿 RGB */

  --color-secondary: #ffa631; /* 杏黄 */
  --color-secondary-hover: #e6941a; /* 杏黄悬停 */
  --color-secondary-light: #ffb85c; /* 杏黄浅色 */
  --color-secondary-dark: #cc8428; /* 杏黄深色 */
  --color-secondary-rgb: 255, 166, 49; /* 杏黄 RGB */

  /* 白色系统 */
  --color-white: #ffffff; /* 纯白 */
  --color-white-rgb: 255, 255, 255; /* 白色 RGB */

  /* 中性色系统 - 基于三色配色的灰度变化 */
  --color-gray-50: #f8f8f8; /* 接近白色 */
  --color-gray-100: #f5f5f5;
  --color-gray-200: #eeeeee;
  --color-gray-300: #e0e0e0;
  --color-gray-400: #bdbdbd;
  --color-gray-500: #9e9e9e;
  --color-gray-600: #757575;
  --color-gray-700: #616161;
  --color-gray-800: #424242;
  --color-gray-900: #333333; /* 深灰 */

  /* RGB 变量用于 rgba() 函数 */
  --color-gray-50-rgb: 248, 248, 248;
  --color-gray-100-rgb: 245, 245, 245;
  --color-gray-200-rgb: 238, 238, 238;
  --color-gray-300-rgb: 224, 224, 224;
  --color-gray-400-rgb: 189, 189, 189;
  --color-gray-500-rgb: 158, 158, 158;
  --color-gray-600-rgb: 117, 117, 117;
  --color-gray-700-rgb: 97, 97, 97;
  --color-gray-800-rgb: 66, 66, 66;
  --color-gray-900-rgb: 51, 51, 51;
  --color-surface-rgb: 255, 255, 255;

  /* 语义化颜色 - 基于三色配色方案 */
  --color-background: var(--color-white); /* 白色背景 */
  --color-background-secondary: var(--color-gray-50); /* 次要背景 */
  --color-surface: var(--color-white); /* 白色表面 */
  --color-text-primary: var(--color-primary); /* 深灰绿文字 */
  --color-text-secondary: var(--color-gray-700); /* 灰色次要文字 */
  --color-text-muted: var(--color-gray-500); /* 灰色弱化文字 */
  --color-text-inverse: var(--color-white); /* 白色反色文字 */
  --color-text-link: var(--color-secondary); /* 杏黄链接 */
  --color-text-link-hover: var(--color-secondary-hover); /* 杏黄链接悬停 */
  --color-border: var(--color-gray-300); /* 灰色边框 */
  --color-border-light: var(--color-gray-200); /* 浅灰边框 */

  /* 状态颜色 - 使用三色配色的变体 */
  --color-success: var(--color-primary); /* 成功状态使用深灰绿 */
  --color-warning: var(--color-secondary); /* 警告状态使用杏黄 */
  --color-error: var(--color-primary-dark); /* 错误状态使用深灰绿深色 */

  /* 间距系统 */
  --spacing-xs: 0.25rem; /* 4px */
  --spacing-sm: 0.5rem; /* 8px */
  --spacing-md: 1rem; /* 16px */
  --spacing-lg: 1.5rem; /* 24px */
  --spacing-xl: 2rem; /* 32px */
  --spacing-2xl: 3rem; /* 48px */
  --spacing-3xl: 4rem; /* 64px */

  /* 字体系统 */
  --font-family-primary:
    "Source Han Sans CN", "PingFang SC", "SF Pro Display", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-family-mono:
    "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New",
    monospace;

  --font-size-xs: 0.75rem; /* 12px */
  --font-size-sm: 0.875rem; /* 14px */
  --font-size-base: 1rem; /* 16px */
  --font-size-lg: 1.125rem; /* 18px */
  --font-size-xl: 1.25rem; /* 20px */
  --font-size-2xl: 1.5rem; /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem; /* 36px */
  --font-size-5xl: 3rem; /* 48px */

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgba(var(--color-gray-900-rgb), 0.05);
  --shadow-md:
    0 4px 6px -1px rgba(var(--color-gray-900-rgb), 0.1),
    0 2px 4px -1px rgba(var(--color-gray-900-rgb), 0.06);
  --shadow-lg:
    0 10px 15px -3px rgba(var(--color-gray-900-rgb), 0.1),
    0 4px 6px -2px rgba(var(--color-gray-900-rgb), 0.05);
  --shadow-xl:
    0 20px 25px -5px rgba(var(--color-gray-900-rgb), 0.1),
    0 10px 10px -5px rgba(var(--color-gray-900-rgb), 0.04);

  /* 圆角系统 */
  --radius-sm: 0.25rem; /* 4px */
  --radius-md: 0.375rem; /* 6px */
  --radius-lg: 0.5rem; /* 8px */
  --radius-xl: 0.75rem; /* 12px */
  --radius-2xl: 1rem; /* 16px */
  --radius-full: 9999px;

  /* 过渡动画系统 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* 断点系统 */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* 层级系统 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* 全局盒模型设置 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* 修复Tailwind slate颜色 */
.text-slate-900 {
  color: rgb(15, 23, 42) !important;
}

.text-slate-600 {
  color: rgb(71, 85, 105) !important;
}

/* HTML 根元素设置 */
html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Body 基础样式 */
body {
  margin: 0;
  padding: 0;
  font-family:
    "Source Han Sans CN",
    "PingFang SC",
    "SF Pro Display",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    "Helvetica Neue",
    Arial,
    sans-serif;
  font-weight: 400;
  color: var(--color-text-primary);
  background-color: var(--color-background);
  min-height: 100vh;
}
