/**
 * 样式工具类
 * 提供常用的布局、间距、动画等工具类
 */

/* ==========================================================================
   布局工具类
   ========================================================================== */

/* Flexbox 工具 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-col-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

/* Grid 工具 */
.grid-center {
  display: grid;
  place-items: center;
}

.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-md);
}

.grid-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing-md);
}

/* 定位工具 */
.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.absolute-full {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.fixed-full {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* ==========================================================================
   间距工具类
   ========================================================================== */

/* 外边距 */
.m-0 {
  margin: 0;
}
.m-xs {
  margin: var(--spacing-xs);
}
.m-sm {
  margin: var(--spacing-sm);
}
.m-md {
  margin: var(--spacing-md);
}
.m-lg {
  margin: var(--spacing-lg);
}
.m-xl {
  margin: var(--spacing-xl);
}
.m-2xl {
  margin: var(--spacing-2xl);
}

/* 垂直外边距 */
.my-0 {
  margin-top: 0;
  margin-bottom: 0;
}
.my-xs {
  margin-top: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}
.my-sm {
  margin-top: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}
.my-md {
  margin-top: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}
.my-lg {
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}
.my-xl {
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

/* 水平外边距 */
.mx-0 {
  margin-left: 0;
  margin-right: 0;
}
.mx-xs {
  margin-left: var(--spacing-xs);
  margin-right: var(--spacing-xs);
}
.mx-sm {
  margin-left: var(--spacing-sm);
  margin-right: var(--spacing-sm);
}
.mx-md {
  margin-left: var(--spacing-md);
  margin-right: var(--spacing-md);
}
.mx-lg {
  margin-left: var(--spacing-lg);
  margin-right: var(--spacing-lg);
}
.mx-xl {
  margin-left: var(--spacing-xl);
  margin-right: var(--spacing-xl);
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

/* 内边距 */
.p-0 {
  padding: 0;
}
.p-xs {
  padding: var(--spacing-xs);
}
.p-sm {
  padding: var(--spacing-sm);
}
.p-md {
  padding: var(--spacing-md);
}
.p-lg {
  padding: var(--spacing-lg);
}
.p-xl {
  padding: var(--spacing-xl);
}
.p-2xl {
  padding: var(--spacing-2xl);
}

/* 垂直内边距 */
.py-0 {
  padding-top: 0;
  padding-bottom: 0;
}
.py-xs {
  padding-top: var(--spacing-xs);
  padding-bottom: var(--spacing-xs);
}
.py-sm {
  padding-top: var(--spacing-sm);
  padding-bottom: var(--spacing-sm);
}
.py-md {
  padding-top: var(--spacing-md);
  padding-bottom: var(--spacing-md);
}
.py-lg {
  padding-top: var(--spacing-lg);
  padding-bottom: var(--spacing-lg);
}
.py-xl {
  padding-top: var(--spacing-xl);
  padding-bottom: var(--spacing-xl);
}

/* 水平内边距 */
.px-0 {
  padding-left: 0;
  padding-right: 0;
}
.px-xs {
  padding-left: var(--spacing-xs);
  padding-right: var(--spacing-xs);
}
.px-sm {
  padding-left: var(--spacing-sm);
  padding-right: var(--spacing-sm);
}
.px-md {
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}
.px-lg {
  padding-left: var(--spacing-lg);
  padding-right: var(--spacing-lg);
}
.px-xl {
  padding-left: var(--spacing-xl);
  padding-right: var(--spacing-xl);
}

/* ==========================================================================
   文本工具类
   ========================================================================== */

/* 文本对齐 */
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-justify {
  text-align: justify;
}

/* 文本大小 */
.text-xs {
  font-size: var(--font-size-xs);
}
.text-sm {
  font-size: var(--font-size-sm);
}
.text-base {
  font-size: var(--font-size-base);
}
.text-lg {
  font-size: var(--font-size-lg);
}
.text-xl {
  font-size: var(--font-size-xl);
}
.text-2xl {
  font-size: var(--font-size-2xl);
}
.text-3xl {
  font-size: var(--font-size-3xl);
}

/* 文本粗细 */
.font-light {
  font-weight: var(--font-weight-light);
}
.font-normal {
  font-weight: var(--font-weight-normal);
}
.font-medium {
  font-weight: var(--font-weight-medium);
}
.font-semibold {
  font-weight: var(--font-weight-semibold);
}
.font-bold {
  font-weight: var(--font-weight-bold);
}

/* 文本颜色 */
.text-primary {
  color: var(--color-primary);
}
.text-secondary {
  color: var(--color-secondary);
}
.text-accent {
  color: var(--color-success);
}
.text-muted {
  color: var(--color-text-muted);
}
.text-error {
  color: var(--color-error);
}
.text-warning {
  color: var(--color-warning);
}
.text-success {
  color: var(--color-success);
}
.text-info {
  color: var(--color-info);
}

/* 文本装饰 */
.no-underline {
  text-decoration: none;
}
.underline {
  text-decoration: underline;
}
.line-through {
  text-decoration: line-through;
}

/* 文本溢出 */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* ==========================================================================
   背景工具类
   ========================================================================== */

/* 背景颜色 */
.bg-primary {
  background-color: var(--color-primary);
}
.bg-secondary {
  background-color: var(--color-secondary);
}
.bg-accent {
  background-color: var(--color-success);
}
.bg-surface {
  background-color: var(--color-surface);
}
.bg-surface-variant {
  background-color: var(--color-surface-variant);
}
.bg-error {
  background-color: var(--color-error);
}
.bg-warning {
  background-color: var(--color-warning);
}
.bg-success {
  background-color: var(--color-success);
}
.bg-info {
  background-color: var(--color-info);
}
.bg-transparent {
  background-color: transparent;
}

/* 背景渐变 */
.bg-gradient-primary {
  background: linear-gradient(
    135deg,
    var(--color-primary),
    var(--color-primary-dark)
  );
}

.bg-gradient-secondary {
  background: linear-gradient(
    135deg,
    var(--color-secondary),
    var(--color-secondary-dark)
  );
}

.bg-gradient-accent {
  background: linear-gradient(
    135deg,
    var(--color-success),
    var(--color-primary-dark)
  );
}

/* ==========================================================================
   边框工具类
   ========================================================================== */

/* 边框样式 */
.border {
  border: 1px solid var(--color-border);
}
.border-0 {
  border: none;
}
.border-t {
  border-top: 1px solid var(--color-border);
}
.border-r {
  border-right: 1px solid var(--color-border);
}
.border-b {
  border-bottom: 1px solid var(--color-border);
}
.border-l {
  border-left: 1px solid var(--color-border);
}

/* 边框颜色 */
.border-primary {
  border-color: var(--color-primary);
}
.border-secondary {
  border-color: var(--color-secondary);
}
.border-accent {
  border-color: var(--color-success);
}
.border-error {
  border-color: var(--color-error);
}
.border-warning {
  border-color: var(--color-warning);
}
.border-success {
  border-color: var(--color-success);
}
.border-transparent {
  border-color: transparent;
}

/* 圆角 */
.rounded-none {
  border-radius: 0;
}
.rounded-sm {
  border-radius: var(--border-radius-sm);
}
.rounded {
  border-radius: var(--border-radius-md);
}
.rounded-md {
  border-radius: var(--border-radius-md);
}
.rounded-lg {
  border-radius: var(--border-radius-lg);
}
.rounded-xl {
  border-radius: var(--border-radius-xl);
}
.rounded-full {
  border-radius: 50%;
}

/* ==========================================================================
   阴影工具类
   ========================================================================== */

.shadow-none {
  box-shadow: none;
}
.shadow-sm {
  box-shadow: var(--shadow-sm);
}
.shadow {
  box-shadow: var(--shadow-md);
}
.shadow-md {
  box-shadow: var(--shadow-md);
}
.shadow-lg {
  box-shadow: var(--shadow-lg);
}
.shadow-xl {
  box-shadow: var(--shadow-xl);
}
.shadow-2xl {
  box-shadow: var(--shadow-2xl);
}

/* ==========================================================================
   动画工具类
   ========================================================================== */

/* 过渡动画 */
.transition {
  transition-property: all;
  transition-duration: var(--transition-duration);
  transition-timing-function: var(--transition-timing);
}

.transition-colors {
  transition-property: color, background-color, border-color;
  transition-duration: var(--transition-duration);
  transition-timing-function: var(--transition-timing);
}

.transition-transform {
  transition-property: transform;
  transition-duration: var(--transition-duration);
  transition-timing-function: var(--transition-timing);
}

.transition-opacity {
  transition-property: opacity;
  transition-duration: var(--transition-duration);
  transition-timing-function: var(--transition-timing);
}

/* 变换 */
.scale-95 {
  transform: scale(0.95);
}
.scale-100 {
  transform: scale(1);
}
.scale-105 {
  transform: scale(1.05);
}
.scale-110 {
  transform: scale(1.1);
}

.rotate-90 {
  transform: rotate(90deg);
}
.rotate-180 {
  transform: rotate(180deg);
}
.rotate-270 {
  transform: rotate(270deg);
}

/* 透明度 */
.opacity-0 {
  opacity: 0;
}
.opacity-25 {
  opacity: 0.25;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-100 {
  opacity: 1;
}

/* ==========================================================================
   交互状态工具类
   ========================================================================== */

/* 悬停效果 */
/* .hover-lift 类已移至 tailwind-components.css 以避免重复定义 */

/* .hover-scale 类已移至 tailwind-components.css 以避免重复定义 */

.hover-shadow {
  transition: box-shadow var(--transition-duration) var(--transition-timing);
}

.hover-shadow:hover {
  box-shadow: var(--shadow-lg);
}

/* 焦点效果 */
.focus-ring:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.focus-ring-inset:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: -2px;
}

/* 禁用状态 */
.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* ==========================================================================
   响应式工具类
   ========================================================================== */

/* 显示/隐藏 */
.hidden {
  display: none;
}
.block {
  display: block;
}
.inline {
  display: inline;
}
.inline-block {
  display: inline-block;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.grid {
  display: grid;
}
.inline-grid {
  display: inline-grid;
}

/* 响应式断点 */
@media (max-width: 640px) {
  .sm\:hidden {
    display: none;
  }
  .sm\:block {
    display: block;
  }
  .sm\:flex {
    display: flex;
  }
  .sm\:grid {
    display: grid;
  }
}

@media (max-width: 768px) {
  .md\:hidden {
    display: none;
  }
  .md\:block {
    display: block;
  }
  .md\:flex {
    display: flex;
  }
  .md\:grid {
    display: grid;
  }
}

@media (max-width: 1024px) {
  .lg\:hidden {
    display: none;
  }
  .lg\:block {
    display: block;
  }
  .lg\:flex {
    display: flex;
  }
  .lg\:grid {
    display: grid;
  }
}

/* ==========================================================================
   特殊工具类
   ========================================================================== */

/* 清除浮动 */
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

/* 屏幕阅读器专用 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: var(--color-surface-variant);
  border-radius: var(--border-radius-sm);
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--border-radius-sm);
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-muted);
}

/* 选择文本样式 */
.select-none {
  user-select: none;
}

.select-text {
  user-select: text;
}

.select-all {
  user-select: all;
}

/* 指针事件 */
.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

/* 光标样式 */
.cursor-pointer {
  cursor: pointer;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-wait {
  cursor: wait;
}

.cursor-move {
  cursor: move;
}

.cursor-grab {
  cursor: grab;
}

.cursor-grabbing {
  cursor: grabbing;
}
