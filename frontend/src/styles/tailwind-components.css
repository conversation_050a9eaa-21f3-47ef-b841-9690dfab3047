/* Tailwind CSS v4.0 组件库 */
@import "tailwindcss";

/* 
所有自定义组件已移除，请直接使用 Tailwind CSS v4.0 原生类实现样式

推荐的 Tailwind CSS v4.0 原生类替代方案：

卡片组件：
- 基础卡片：bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden
- 悬停效果：hover:shadow-md hover:-translate-y-1 transition-all duration-200
- 交互式卡片：cursor-pointer hover:border-blue-300 dark:hover:border-blue-600

按钮组件：
- 主要按钮：bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors
- 次要按钮：bg-gray-100 hover:bg-gray-200 text-gray-900 px-4 py-2 rounded-lg font-medium transition-colors
- 危险按钮：bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors

表单组件：
- 输入框：w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500
- 标签：block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2
- 错误提示：text-sm text-red-600 dark:text-red-400 mt-1

导航组件：
- 导航栏：flex space-x-8
- 导航项：text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium transition-colors
- 活跃项：text-blue-600 bg-blue-50 dark:bg-blue-900/20

徽章组件：
- 基础徽章：inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
- 主要徽章：bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400
- 成功徽章：bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400
- 警告徽章：bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400
- 错误徽章：bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400

模态框组件：
- 背景遮罩：fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4
- 模态框：bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden
- 头部：px-6 py-4 border-b border-gray-200 dark:border-gray-700
- 内容：px-6 py-4
- 底部：px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3

下拉菜单：
- 容器：absolute z-50 mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 min-w-[160px]
- 菜单项：block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700

警告框：
- 成功：bg-green-50 border border-green-200 text-green-800 p-4 rounded-lg
- 警告：bg-yellow-50 border border-yellow-200 text-yellow-800 p-4 rounded-lg
- 错误：bg-red-50 border border-red-200 text-red-800 p-4 rounded-lg
- 信息：bg-blue-50 border border-blue-200 text-blue-800 p-4 rounded-lg

加载组件：
- 旋转器：inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin
- 大旋转器：w-8 h-8

进度条：
- 容器：w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2
- 进度：h-full bg-blue-600 rounded-full transition-all duration-300

分页：
- 容器：flex items-center space-x-1
- 页码：px-3 py-2 text-sm text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md transition-colors
- 当前页：px-3 py-2 text-sm text-white bg-blue-600 rounded-md

面包屑：
- 容器：flex items-center space-x-2 text-sm
- 项目：text-gray-500 hover:text-gray-700 transition-colors
- 当前项：text-gray-900 dark:text-white font-medium

标签页：
- 容器：border-b border-gray-200 dark:border-gray-700
- 标签：inline-flex items-center px-4 py-2 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-all
- 活跃标签：text-blue-600 border-blue-500

手风琴：
- 容器：border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden
- 头部：w-full px-4 py-3 text-left text-sm font-medium bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 focus:ring-2 focus:ring-blue-500
- 内容：px-4 py-3 text-sm

阴影：
- 软阴影：shadow-sm
- 中等阴影：shadow-md
- 强阴影：shadow-lg
- 超强阴影：shadow-xl
- 2XL阴影：shadow-2xl

动画：
- 淡入：animate-fade-in (需要自定义)
- 旋转：animate-spin
- 脉冲：animate-pulse
- 弹跳：animate-bounce

使用 Tailwind CSS v4.0 原生类的优势：
1. 更好的性能 - 只加载使用的样式
2. 更小的包体积 - 无需额外的自定义CSS
3. 更好的可维护性 - 标准化的类名
4. 更好的开发体验 - IDE支持和自动补全
5. 更好的一致性 - 遵循Tailwind设计系统
*/
