/* ==========================================================================
   导入工具类
   ========================================================================== */

@import "./style-utils.css";

/* 导入 Tailwind 组件库 */
@import "./tailwind-components.css";

/* ==========================================================================
   统一样式系统 - 解决CSS冲突，提供精准的样式控制
   ========================================================================== */

/* 1. CSS变量系统 - 统一颜色和尺寸管理 */
/* :root 选择器已移动到 base-styles.css 以避免全局选择器冲突 */

/* 2. 深色主题变量 */
html.dark {
  --color-background: var(--color-gray-900);
  --color-surface: var(--color-gray-800);
  --color-text-primary: var(--color-gray-50);
  --color-text-secondary: var(--color-gray-300);
  --color-text-muted: var(--color-gray-400);
  --color-border: var(--color-gray-700);
  --color-border-light: var(--color-gray-600);

  --color-primary-light: var(--color-primary-light);
}

/* 注意：全局选择器已移动到 base-styles.css 以避免样式冲突 */
/* 如需修改全局样式，请编辑 base-styles.css 文件 */

/* 4. 布局容器系统 */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}

.container-sm {
  max-width: 640px;
}
.container-md {
  max-width: 768px;
}
.container-lg {
  max-width: 1024px;
}
.container-xl {
  max-width: 1280px;
}
.container-2xl {
  max-width: 1536px;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/* 5. 通用组件样式 */
/* 按钮样式已移至 tailwind-components.css */

/* 6. 卡片组件 */
/* 卡片样式已移至 tailwind-components.css */

/* 7. 表单组件 */
/* 表单样式已移至 tailwind-components.css */

/* 8. 工具类已移至 style-utils.css */

/* 圆角、阴影、间距工具类已移至 style-utils.css */

/* 所有间距工具类已移至 style-utils.css */

/* 剩余间距工具类已移至 style-utils.css */

/* 10. 响应式工具类已移至 style-utils.css */

/* 11. 动画和过渡 */
/* .transition 类已移至 style-utils.css 以避免重复定义 */

.transition-fast {
  transition: all var(--transition-fast);
}

.transition-slow {
  transition: all var(--transition-slow);
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 12. 特殊组件样式 */
/* .loading-spinner 类已移至 LoadingScreen.css 以避免重复定义 */

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(var(--color-gray-900-rgb), 0.5);
  z-index: var(--z-modal-backdrop);
}

/* Modal styles moved to tailwind-components.css */

/* 13. 移动端优化工具类 */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

.touch-manipulation {
  touch-action: manipulation;
}

/* 14. 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }
}
