import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";

// 导入翻译文件
import translationEN from "./locales/en/translation.json";
import translationZH from "./locales/zh/translation.json";

// 资源对象
const resources = {
  en: {
    translation: translationEN,
  },
  zh: {
    translation: translationZH,
  },
};

i18n
  // 检测用户语言
  .use(LanguageDetector)
  // 将i18n实例传递给react-i18next
  .use(initReactI18next)
  // 初始化i18next
  .init({
    resources,
    fallbackLng: "zh", // 默认语言
    debug: import.meta.env.MODE === "development",
    interpolation: {
      escapeValue: false, // 不转义HTML
    },
  });

export default i18n;
