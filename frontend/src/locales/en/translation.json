{"siteName": "Memorial Website", "home": "Home", "about": "About Us", "contact": "Contact Us", "welcome": "Remembrance & Commemoration", "chooseEnvironment": "Please select an environment to begin your memorial journey", "categories": {"eastern": "Eastern Traditional Environments", "western": "Western Religious Environments", "modern": "Modern/Neutral Environments"}, "environments": {"chineseTemple": "Chinese Traditional Temple", "buddhistTemple": "Buddhist Temple", "taoistShrine": "Taoist Shrine", "christianChurch": "Christian Church", "jewishMemorial": "Jewish Memorial Space", "modernSpace": "Modern Minimalist Space", "nature": "Natural Ecological Environment", "cosmos": "Cosmic Space Environment"}, "controls": {"title": "Memorial Controls", "back": "Back", "incense": "Light Incense", "offerings": "Present Offerings", "music": "Play Music", "prayer": "Offer Prayers"}, "loading": {"detectingDevice": "Detecting device performance...", "model": "Loading model...", "connecting": "Connecting to server..."}, "quality": {"auto": "Auto", "high": "High Quality", "medium": "Medium Quality", "low": "Low Quality", "server": "Server Rendering", "image": "Image Mode", "highPerformance": "High Performance Device", "lowPerformance": "Low Performance Device"}, "renderer": {"dragToRotate": "Drag to rotate", "scrollToZoom": "Scroll to zoom", "serverError": "Cannot connect to render server, switching to image mode", "loadingServer": "Connecting to render server..."}, "login": "<PERSON><PERSON>", "logout": "Logout", "email": "Email", "password": "Password", "loggingIn": "Logging in...", "profile": "Profile", "backToHome": "Back to Home", "active": "Active", "inactive": "Inactive", "edit": "Edit", "delete": "Delete", "name": "Name", "type": "Type", "username": "Username", "role": "Role", "status": "Status", "actions": "Actions", "admin": {"dashboard": "Admin Dashboard", "users": "User Management", "environments": "Environment Management", "totalUsers": "Total Users", "totalEnvironments": "Total Environments", "userManagement": "User Management", "environmentManagement": "Environment Management"}}