import React, { useState, useEffect, ChangeEvent, FormEvent } from "react";
import { usePara<PERSON>, useNavigate, Link } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCheck,
  faCloudUploadAlt,
  faImages,
  faPlus,
  faTrashAlt,
  faVideo,
  faFilm,
  faSpinner,
  faArrowLeft,
  faArrowRight,
} from "@fortawesome/free-solid-svg-icons";
// Assuming Navbar and Sidebar are correctly imported if they are separate components
// import Navbar from '../components/Navbar';
// import Sidebar from '../components/Sidebar';

interface TimelineEvent {
  id?: string; // Optional: only present for existing events from backend
  year: string;
  title: string;
  description: string;
  // Add other relevant fields if necessary, e.g., event_date from API
}

interface MemorialAsset {
  id: string;
  url: string;
  asset_type: "life_photo" | "video" | "music"; // Example asset types
  // Add other relevant fields, e.g., created_at, file_name
}

interface MemorialSpaceData {
  id: string;
  deceased_name: string;
  birth_date?: string | null;
  death_date?: string | null;
  bio?: string | null;
  main_photo_url?: string | null;
  privacy_level: "public" | "password" | "family" | "private";
  access_password?: string | null;
  relationship?: string | null; // Added from editableMemorialSpaceData
  // Add other fields as necessary from your backend model
}

const MemorialSpacePage: React.FC = () => {
  const { spaceId } = useParams<{ spaceId: string }>();
  const navigate = useNavigate();

  const [memorialSpace, setMemorialSpace] = useState<MemorialSpaceData | null>(
    null,
  );
  const [editableMemorialSpaceData, setEditableMemorialSpaceData] = useState<
    Partial<MemorialSpaceData>
  >({});
  const [timelineEvents, setTimelineEvents] = useState<TimelineEvent[]>([]);
  const [lifePhotoAssets, setLifePhotoAssets] = useState<MemorialAsset[]>([]);
  const [videoAssets, setVideoAssets] = useState<MemorialAsset[]>([]);
  // const [musicAssets, setMusicAssets] = useState<MemorialAsset[]>([]); // If music is handled here

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Main Photo States
  const [mainPhotoPreview, setMainPhotoPreview] = useState<string | null>(null);
  const [isUploadingMainPhoto, setIsUploadingMainPhoto] =
    useState<boolean>(false);
  const [mainPhotoUploadError, setMainPhotoUploadError] = useState<
    string | null
  >(null);

  // Life Photos States
  // const [lifePhotosPreview, setLifePhotosPreview] = useState<string[]>([]); // Preview might be complex for multiple files
  const [isUploadingLifePhotos, setIsUploadingLifePhotos] =
    useState<boolean>(false);
  const [uploadLifePhotosError, setUploadLifePhotosError] = useState<
    string | null
  >(null);
  const [isDeletingLifePhoto, setIsDeletingLifePhoto] = useState<string | null>(
    null,
  ); // Store ID of photo being deleted
  const [deleteLifePhotoError, setDeleteLifePhotoError] = useState<
    string | null
  >(null);

  // Video States
  const [isUploadingVideo, setIsUploadingVideo] = useState<boolean>(false);
  const [uploadVideoError, setUploadVideoError] = useState<string | null>(null);
  const [isDeletingVideoAsset, setIsDeletingVideoAsset] = useState<
    string | null
  >(null);
  const [deleteVideoAssetError, setDeleteVideoAssetError] = useState<
    string | null
  >(null);
  const [deleteVideoAssetSuccess, setDeleteVideoAssetSuccess] = useState<
    string | null
  >(null);

  // Timeline Event States
  const [showAddEventModal, setShowAddEventModal] = useState<boolean>(false);
  const [newEvent, setNewEvent] = useState<Omit<TimelineEvent, "id">>({
    year: "",
    title: "",
    description: "",
  });
  const [isAddingEvent, setIsAddingEvent] = useState<boolean>(false);
  const [addEventError, setAddEventError] = useState<string | null>(null);
  const [isDeletingEvent, setIsDeletingEvent] = useState<string | null>(null); // Store ID of event being deleted
  const [deleteEventError, setDeleteEventError] = useState<string | null>(null);

  // Save Memorial Space Details States
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState<string | null>(null);

  // Example additional field state (if not part of MemorialSpaceData initially)
  const [hometown, setHometown] = useState<string>("");

  const API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL;

  // Fetch initial data
  useEffect(() => {
    if (!spaceId) {
      setError("Memorial space ID is missing.");
      setIsLoading(false);
      return;
    }
    const token = localStorage.getItem("authToken");
    if (!token) {
      navigate(
        "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
      );
      return;
    }

    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch memorial space details
        const spaceResponse = await fetch(
          `${API_BASE_URL}/memorial-spaces/${spaceId}`,
          {
            headers: { Authorization: `Bearer ${token}` },
          },
        );
        if (!spaceResponse.ok) {
          if (spaceResponse.status === 401)
            throw new Error("Session expired. Please login again.");
          throw new Error(
            `Failed to fetch memorial space data: ${spaceResponse.statusText}`,
          );
        }
        const spaceData: MemorialSpaceData = await spaceResponse.json();
        setMemorialSpace(spaceData);
        setEditableMemorialSpaceData({
          deceased_name: spaceData.deceased_name,
          birth_date: spaceData.birth_date,
          death_date: spaceData.death_date,
          bio: spaceData.bio,
          privacy_level: spaceData.privacy_level,
          access_password: spaceData.access_password,
          relationship: spaceData.relationship,
        });
        if (spaceData.main_photo_url)
          setMainPhotoPreview(spaceData.main_photo_url);

        // Fetch timeline events
        const eventsResponse = await fetch(
          `${API_BASE_URL}/memorial-spaces/${spaceId}/events`,
          {
            headers: { Authorization: `Bearer ${token}` },
          },
        );
        if (eventsResponse.ok) {
          const eventsData = await eventsResponse.json();
          setTimelineEvents(
            eventsData.map(
              (event: {
                event_date?: string;
                year?: string;
                [key: string]: unknown;
              }) => ({ ...event, year: event.event_date || event.year }),
            ), // Adapt based on API response
          );
        }

        // Fetch life photos
        const lifePhotosResponse = await fetch(
          `${API_BASE_URL}/memorial-spaces/${spaceId}/assets?asset_type=life_photo`,
          {
            headers: { Authorization: `Bearer ${token}` },
          },
        );
        if (lifePhotosResponse.ok) {
          const lifePhotosData = await lifePhotosResponse.json();
          setLifePhotoAssets(lifePhotosData);
        }

        // Fetch video assets
        const videoAssetsResponse = await fetch(
          `${API_BASE_URL}/memorial-spaces/${spaceId}/assets?asset_type=video`,
          {
            headers: { Authorization: `Bearer ${token}` },
          },
        );
        if (videoAssetsResponse.ok) {
          const videoAssetsData = await videoAssetsResponse.json();
          setVideoAssets(videoAssetsData);
        }
      } catch (err: unknown) {
        const errorMessage =
          err instanceof Error ? err.message : "发生未知错误";
        setError(errorMessage);
        if (errorMessage.includes("Session expired")) {
          localStorage.removeItem("authToken");
          navigate(
            "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
          );
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [spaceId, navigate, API_BASE_URL]);

  const handleMemorialSpaceInputChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target;
    setEditableMemorialSpaceData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSaveMemorialSpaceDetails = async () => {
    if (!spaceId) {
      setSaveError("Memorial space ID is missing.");
      return;
    }
    const token = localStorage.getItem("authToken");
    if (!token) {
      navigate(
        "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
      );
      return;
    }

    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(null);

    try {
      const response = await fetch(
        `${API_BASE_URL}/memorial-spaces/${spaceId}`,
        {
          method: "PATCH", // Or PUT, depending on your API design
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(editableMemorialSpaceData),
        },
      );

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("authToken");
          navigate(
            "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
          );
          return;
        }
        const errorData = await response.json();
        throw new Error(
          errorData.detail || "Failed to save memorial space details.",
        );
      }

      const updatedSpaceData: MemorialSpaceData = await response.json();
      setMemorialSpace(updatedSpaceData); // Update the main memorialSpace state
      setEditableMemorialSpaceData((prev) => ({
        // Keep editable form data in sync, or reset if needed
        ...prev, // Or just use parts of updatedSpaceData if form doesn't cover all fields
        deceased_name: updatedSpaceData.deceased_name,
        birth_date: updatedSpaceData.birth_date,
        death_date: updatedSpaceData.death_date,
        bio: updatedSpaceData.bio,
        privacy_level: updatedSpaceData.privacy_level,
        access_password: updatedSpaceData.access_password,
        relationship: updatedSpaceData.relationship,
      }));
      setSaveSuccess("Memorial space information saved successfully!");
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "An unknown error occurred while saving.";
      setSaveError(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  const handleMainPhotoUpload = async (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setMainPhotoPreview(URL.createObjectURL(file));
      setMainPhotoUploadError(null);

      if (!spaceId) {
        setMainPhotoUploadError("Memorial space ID is missing.");
        return;
      }
      const token = localStorage.getItem("authToken");
      if (!token) {
        navigate(
          "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
        );
        return;
      }

      const formData = new FormData();
      formData.append("file", file);

      setIsUploadingMainPhoto(true);
      try {
        const response = await fetch(
          `${API_BASE_URL}/memorial-spaces/${spaceId}/upload-main-photo`,
          {
            method: "POST",
            headers: { Authorization: `Bearer ${token}` },
            body: formData,
          },
        );
        if (!response.ok) {
          if (response.status === 401) {
            localStorage.removeItem("authToken");
            navigate(
              "/login?sessionExpired=true&redirectTo=/memorial-space/" +
                spaceId,
            );
            return;
          }
          const errorData = await response.json();
          throw new Error(errorData.detail || "Main photo upload failed.");
        }
        const result = await response.json();
        setMemorialSpace((prev) =>
          prev ? { ...prev, main_photo_url: result.file_url } : null,
        );
        setMainPhotoPreview(result.file_url); // Update with server URL
        // Optionally show a success message
      } catch (err: unknown) {
        const errorMessage =
          err instanceof Error ? err.message : "An unknown error occurred.";
        setMainPhotoUploadError(errorMessage);
        setMainPhotoPreview(memorialSpace?.main_photo_url || null); // Revert to original if upload fails
      } finally {
        setIsUploadingMainPhoto(false);
      }
    }
  };

  const handleLifePhotosUpload = async (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const files = Array.from(e.target.files);
      // You might want to limit the number of files or total size here
      setUploadLifePhotosError(null);

      if (!spaceId) {
        setUploadLifePhotosError("Memorial space ID is missing.");
        return;
      }
      const token = localStorage.getItem("authToken");
      if (!token) {
        navigate(
          "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
        );
        return;
      }

      setIsUploadingLifePhotos(true);
      const uploadedAssets: MemorialAsset[] = [];
      for (const file of files) {
        const formData = new FormData();
        formData.append("file", file);
        formData.append("asset_type", "life_photo");

        try {
          const response = await fetch(
            `${API_BASE_URL}/memorial-spaces/${spaceId}/assets`,
            {
              method: "POST",
              headers: { Authorization: `Bearer ${token}` },
              body: formData,
            },
          );
          if (!response.ok) {
            if (response.status === 401) {
              localStorage.removeItem("authToken");
              navigate(
                "/login?sessionExpired=true&redirectTo=/memorial-space/" +
                  spaceId,
              );
              setIsUploadingLifePhotos(false); // Stop further uploads
              return;
            }
            const errorData = await response.json();
            // Accumulate errors or handle them per file
            setUploadLifePhotosError(
              `Error uploading ${file.name}: ${errorData.detail || "Upload failed."}`,
            );
            continue; // Skip to next file
          }
          const newAsset: MemorialAsset = await response.json();
          uploadedAssets.push(newAsset);
        } catch (err: unknown) {
          const errorMessage =
            err instanceof Error ? err.message : "Unknown error.";
          setUploadLifePhotosError(
            `Error uploading ${file.name}: ${errorMessage}`,
          );
        }
      }
      setLifePhotoAssets((prev) => [...prev, ...uploadedAssets]);
      setIsUploadingLifePhotos(false);
      if (uploadedAssets.length === files.length && files.length > 0) {
        // Optionally show a success message for all files
      } else if (files.length > 0) {
        // Optionally show a partial success/error message
      }
    }
  };

  const handleDeleteLifePhoto = async (assetId: string) => {
    if (!spaceId) {
      setDeleteLifePhotoError("Cannot delete: Memorial space ID missing.");
      return;
    }
    const confirmDelete = window.confirm(
      "Are you sure you want to delete this life photo? This action cannot be undone.",
    );
    if (!confirmDelete) return;

    const token = localStorage.getItem("authToken");
    if (!token) {
      navigate(
        "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
      );
      return;
    }

    setIsDeletingLifePhoto(assetId);
    setDeleteLifePhotoError(null);
    try {
      const response = await fetch(
        `${API_BASE_URL}/memorial-spaces/${spaceId}/assets/${assetId}`,
        {
          method: "DELETE",
          headers: { Authorization: `Bearer ${token}` },
        },
      );
      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("authToken");
          navigate(
            "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
          );
          return;
        }
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to delete life photo.");
      }
      setLifePhotoAssets((prev) =>
        prev.filter((asset) => asset.id !== assetId),
      );
      // Optionally show success message
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred.";
      setDeleteLifePhotoError(errorMessage);
    } finally {
      setIsDeletingLifePhoto(null);
    }
  };

  const handleVideoUpload = async (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setUploadVideoError(null);

      if (!spaceId) {
        setUploadVideoError("Memorial space ID is missing.");
        return;
      }
      const token = localStorage.getItem("authToken");
      if (!token) {
        navigate(
          "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
        );
        return;
      }

      const formData = new FormData();
      formData.append("file", file);
      formData.append("asset_type", "video");

      setIsUploadingVideo(true);
      try {
        const response = await fetch(
          `${API_BASE_URL}/memorial-spaces/${spaceId}/assets`,
          {
            method: "POST",
            headers: { Authorization: `Bearer ${token}` },
            body: formData,
          },
        );
        if (!response.ok) {
          if (response.status === 401) {
            localStorage.removeItem("authToken");
            navigate(
              "/login?sessionExpired=true&redirectTo=/memorial-space/" +
                spaceId,
            );
            return;
          }
          const errorData = await response.json();
          throw new Error(errorData.detail || "Video upload failed.");
        }
        const newAsset: MemorialAsset = await response.json();
        setVideoAssets((prev) => [...prev, newAsset]);
        // Optionally show success message
      } catch (err: unknown) {
        const errorMessage =
          err instanceof Error ? err.message : "An unknown error occurred.";
        setUploadVideoError(errorMessage);
      } finally {
        setIsUploadingVideo(false);
      }
    }
  };

  const handleDeleteVideoAsset = async (assetId: string) => {
    if (!spaceId) {
      setDeleteVideoAssetError("Cannot delete: Memorial space ID missing.");
      return;
    }
    const confirmDelete = window.confirm(
      "Are you sure you want to delete this video? This action cannot be undone.",
    );
    if (!confirmDelete) return;

    const token = localStorage.getItem("authToken");
    if (!token) {
      navigate(
        "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
      );
      return;
    }

    setIsDeletingVideoAsset(assetId);
    setDeleteVideoAssetError(null);
    setDeleteVideoAssetSuccess(null);
    try {
      const response = await fetch(
        `${API_BASE_URL}/memorial-spaces/${spaceId}/assets/${assetId}`,
        {
          method: "DELETE",
          headers: { Authorization: `Bearer ${token}` },
        },
      );
      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("authToken");
          navigate(
            "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
          );
          return;
        }
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to delete video asset.");
      }
      setVideoAssets((prev) => prev.filter((asset) => asset.id !== assetId));
      setDeleteVideoAssetSuccess("Video deleted successfully.");
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred.";
      setDeleteVideoAssetError(errorMessage);
    } finally {
      setIsDeletingVideoAsset(null);
    }
  };

  const handleAddTimelineEvent = async () => {
    if (!spaceId) {
      setAddEventError("Memorial space ID is missing.");
      return;
    }
    const token = localStorage.getItem("authToken");
    if (!token) {
      navigate(
        "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
      );
      return;
    }

    // Basic validation
    if (!newEvent.year.trim() || !newEvent.title.trim()) {
      setAddEventError("Year and Title are required for a timeline event.");
      return;
    }

    setIsAddingEvent(true);
    setAddEventError(null);

    const eventPayload = {
      event_date: newEvent.year, // Assuming API expects event_date
      title: newEvent.title,
      description: newEvent.description,
    };

    try {
      const response = await fetch(
        `${API_BASE_URL}/memorial-spaces/${spaceId}/events`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(eventPayload),
        },
      );

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("authToken");
          navigate(
            "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
          );
          return;
        }
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to add timeline event.");
      }

      const createdEvent: TimelineEvent = await response.json();
      const newTimelineEntry: TimelineEvent = {
        ...createdEvent, // Spread the response from API
        year: createdEvent.year || eventPayload.event_date, // Ensure 'year' field is populated
      };

      setTimelineEvents([...timelineEvents, newTimelineEntry]);
      setNewEvent({ year: "", title: "", description: "" }); // Reset form
      setShowAddEventModal(false); // Close modal
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "An unknown error occurred while adding event.";
      setAddEventError(errorMessage);
    } finally {
      setIsAddingEvent(false);
    }
  };

  const handleDeleteTimelineEvent = async (eventId: string) => {
    if (!spaceId || !eventId) {
      setDeleteEventError("Cannot delete event: Missing space ID or event ID.");
      return;
    }

    const confirmDelete = window.confirm(
      "Are you sure you want to delete this timeline event? This action cannot be undone.",
    );
    if (!confirmDelete) return;

    const token = localStorage.getItem("authToken");
    if (!token) {
      navigate(
        "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
      );
      return;
    }

    setIsDeletingEvent(eventId);
    setDeleteEventError(null);

    try {
      const response = await fetch(
        `${API_BASE_URL}/memorial-spaces/${spaceId}/events/${eventId}`,
        {
          method: "DELETE",
          headers: { Authorization: `Bearer ${token}` },
        },
      );

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem("authToken");
          navigate(
            "/login?sessionExpired=true&redirectTo=/memorial-space/" + spaceId,
          );
          return;
        }
        const errorData = await response.json();
        throw new Error(errorData.detail || "Failed to delete timeline event.");
      }

      setTimelineEvents((events) =>
        events.filter((event) => event.id !== eventId),
      );
      // Optionally show a success message
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : "An unknown error occurred while deleting event.";
      setDeleteEventError(errorMessage);
    } finally {
      setIsDeletingEvent(null);
    }
  };

  // Tailwind classes (ensure these match your project's Tailwind config and desired style)
  // 已删除 formInputClasses 和 uploadAreaClasses 样式常量，直接使用 Tailwind 原生类
  // 使用Tailwind原生类替代自定义按钮样式常量

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-900 text-white">
        <FontAwesomeIcon icon={faSpinner} spin size="3x" />{" "}
        <p className="ml-4 text-xl">Loading memorial space...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-gray-900 text-red-400 p-4">
        <p className="text-xl mb-4">Error: {error}</p>
        <button
          onClick={() => navigate("/")}
          className="bg-amber-500 hover:bg-yellow-300 text-gray-900 font-semibold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50"
        >
          Go to Homepage
        </button>
      </div>
    );
  }

  if (!memorialSpace) {
    return (
      <div className="flex flex-col justify-center items-center min-h-screen bg-gray-900 text-white p-4">
        <p className="text-xl mb-4">Memorial space not found.</p>
        <button
          onClick={() => navigate("/")}
          className="bg-amber-500 hover:bg-yellow-300 text-gray-900 font-semibold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50"
        >
          Go to Homepage
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-900 text-white">
      {/* <Navbar /> */}
      <div className="flex flex-1">
        {/* <Sidebar /> */}
        <main className="flex-1 p-6">
          <div className="mb-8">
            <h1 className="text-2xl font-bold mb-2">上传资料</h1>
            <p className="text-white">
              上传逝者照片、生平事迹等资料，完善纪念空间
            </p>
          </div>

          {/* Steps Indicator - Placeholder styling, ensure it matches your design */}
          <div className="flex justify-between mb-10 px-0 md:px-10">
            {[
              { title: "基本信息", completed: true },
              { title: "选择场景", completed: true },
              { title: "上传资料", active: true },
              { title: "完成创建" },
            ].map((step, index) => (
              <div
                key={index}
                className={`step-item flex flex-col items-center w-1/4 ${step.completed ? "completed" : ""} ${step.active ? "active" : ""}`}
              >
                <div
                  className={`step-circle w-8 h-8 rounded-full flex items-center justify-center border-2 ${step.active ? "border-amber-500 bg-amber-500 text-gray-900" : step.completed ? "border-green-400 bg-green-400 text-white" : "border-gray-500 text-gray-300"}`}
                >
                  {step.completed ? (
                    <FontAwesomeIcon icon={faCheck} />
                  ) : (
                    index + 1
                  )}
                </div>
                <div
                  className={`mt-2 text-sm font-medium ${step.active ? "text-amber-500" : step.completed ? "text-green-400" : "text-gray-300"}`}
                >
                  {step.title}
                </div>
              </div>
            ))}
          </div>

          <div className="bg-gray-900 border border-amber-500 border-opacity-30 rounded-xl shadow-md p-6 mb-6">
            {/* Photo Upload Section */}
            <section className="mb-8">
              <h2 className="text-xl font-semibold mb-4">上传照片</h2>
              <p className="text-white mb-4">
                {memorialSpace.deceased_name}
                的照片，将作为纪念空间的主要展示图片。
              </p>
              {mainPhotoUploadError && (
                <p className="text-red-400 text-xs mb-2">
                  Error: {mainPhotoUploadError}
                </p>
              )}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    主照片（必选）
                  </label>
                  <div className="border-2 border-dashed border-amber-500 rounded-lg p-6 text-center bg-gray-800 hover:bg-gray-700 transition-colors">
                    <input
                      type="file"
                      accept="image/jpeg, image/png"
                      onChange={handleMainPhotoUpload}
                      className="hidden"
                      id="main-photo-upload"
                      disabled={isUploadingMainPhoto}
                    />
                    <label
                      htmlFor="main-photo-upload"
                      className={`cursor-pointer ${isUploadingMainPhoto ? "opacity-50 cursor-not-allowed" : ""}`}
                    >
                      {isUploadingMainPhoto ? (
                        <FontAwesomeIcon
                          icon={faSpinner}
                          spin
                          className="h-8 w-8 text-amber-500 mb-2"
                        />
                      ) : (
                        <FontAwesomeIcon
                          icon={faCloudUploadAlt}
                          className="text-4xl text-amber-500 mb-2"
                        />
                      )}
                      <p className="text-amber-500">
                        {isUploadingMainPhoto
                          ? "上传中..."
                          : "点击或拖拽上传照片"}
                      </p>
                      <p className="text-xs text-gray-300 mt-1">
                        支持 JPG、PNG 格式
                      </p>
                    </label>
                  </div>
                  {mainPhotoPreview && (
                    <img
                      src={mainPhotoPreview}
                      alt="主照片预览"
                      className="mt-2 h-32 w-auto object-cover rounded-md"
                    />
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">
                    生活照（可选，最多5张）
                  </label>
                  {uploadLifePhotosError && (
                    <p className="text-red-400 text-xs mb-2">
                      Error: {uploadLifePhotosError}
                    </p>
                  )}
                  {deleteLifePhotoError && (
                    <div
                      className="mb-2 p-2 bg-red-500 bg-opacity-20 text-red-400 border border-red-500 rounded-lg text-xs"
                      role="alert"
                    >
                      删除生活照片失败: {deleteLifePhotoError}
                      <button
                        onClick={() => setDeleteLifePhotoError(null)}
                        className="ml-2 text-xs underline"
                      >
                        关闭
                      </button>
                    </div>
                  )}
                  <div className="border-2 border-dashed border-amber-500 rounded-lg p-6 text-center bg-gray-800 hover:bg-gray-700 transition-colors">
                    <input
                      type="file"
                      accept="image/jpeg, image/png"
                      multiple
                      onChange={handleLifePhotosUpload}
                      className="hidden"
                      id="life-photos-upload"
                      disabled={isUploadingLifePhotos}
                    />
                    <label
                      htmlFor="life-photos-upload"
                      className={`cursor-pointer ${isUploadingLifePhotos ? "opacity-50 cursor-not-allowed" : ""}`}
                    >
                      {isUploadingLifePhotos ? (
                        <FontAwesomeIcon
                          icon={faSpinner}
                          spin
                          className="h-8 w-8 text-amber-500 mb-2"
                        />
                      ) : (
                        <FontAwesomeIcon
                          icon={faImages}
                          className="text-4xl text-amber-500 mb-2"
                        />
                      )}
                      <p className="text-amber-500">
                        {isUploadingLifePhotos
                          ? "上传中..."
                          : "点击或拖拽上传多张照片"}
                      </p>
                      <p className="text-xs text-gray-300 mt-1">
                        单张不超过5MB
                      </p>
                    </label>
                  </div>
                  {lifePhotoAssets.length > 0 && (
                    <div className="mt-2 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                      {lifePhotoAssets.map((asset) => (
                        <div key={asset.id} className="relative group">
                          <img
                            src={asset.url}
                            alt={`生活照 ${asset.id}`}
                            className="h-28 w-full object-cover rounded-md shadow-md"
                          />
                          <button
                            onClick={() => handleDeleteLifePhoto(asset.id)}
                            disabled={isDeletingLifePhoto === asset.id}
                            className={`absolute top-1 right-1 bg-red-600 hover:bg-red-700 text-white p-1 rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${isDeletingLifePhoto === asset.id ? "opacity-100 cursor-wait" : ""}`}
                            title="删除照片"
                          >
                            {isDeletingLifePhoto === asset.id ? (
                              <FontAwesomeIcon
                                icon={faSpinner}
                                spin
                                className="h-3 w-3"
                              />
                            ) : (
                              <FontAwesomeIcon
                                icon={faTrashAlt}
                                className="h-3 w-3"
                              />
                            )}
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </section>

            {/* Basic Info Section */}
            <section className="mb-8">
              <h2 className="text-xl font-semibold mb-4">基本资料</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label
                    htmlFor="birth_date_edit"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    出生日期
                  </label>
                  <input
                    type="date"
                    id="birth_date_edit"
                    name="birth_date"
                    value={
                      editableMemorialSpaceData.birth_date?.split("T")[0] || ""
                    }
                    onChange={handleMemorialSpaceInputChange}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label
                    htmlFor="death_date_edit"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    逝世日期
                  </label>
                  <input
                    type="date"
                    id="death_date_edit"
                    name="death_date"
                    value={
                      editableMemorialSpaceData.death_date?.split("T")[0] || ""
                    }
                    onChange={handleMemorialSpaceInputChange}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  />
                </div>
                <div className="md:col-span-2">
                  <label
                    htmlFor="relationship_edit"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    与创建者关系
                  </label>
                  <input
                    type="text"
                    id="relationship_edit"
                    name="relationship"
                    value={editableMemorialSpaceData.relationship || ""}
                    onChange={handleMemorialSpaceInputChange}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                    placeholder="例如：父亲、母亲"
                  />
                </div>
                <div className="md:col-span-2">
                  <label
                    htmlFor="hometown"
                    className="block text-sm font-medium mb-1"
                  >
                    籍贯 (示例字段)
                  </label>
                  <input
                    type="text"
                    id="hometown"
                    placeholder="例如：江苏省南京市"
                    value={hometown}
                    onChange={(e) => setHometown(e.target.value)}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  />
                </div>
              </div>
            </section>

            {/* Biography Section */}
            <section className="mb-8">
              <h2 className="text-xl font-semibold mb-4">生平简介</h2>
              <div className="mb-4">
                <label
                  htmlFor="bio_edit"
                  className="block text-sm font-medium text-white mb-1"
                >
                  简介内容
                </label>
                <textarea
                  id="bio_edit"
                  name="bio"
                  rows={5}
                  placeholder="请输入逝者生平简介..."
                  value={editableMemorialSpaceData.bio || ""}
                  onChange={handleMemorialSpaceInputChange}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent resize-none"
                ></textarea>
              </div>
              <div>
                <div className="flex justify-between items-center mb-4">
                  <label className="block text-sm font-medium text-white">
                    生平大事记（可选）
                  </label>
                  <button
                    type="button"
                    onClick={() => setShowAddEventModal(true)}
                    className="text-amber-500 hover:text-yellow-300 text-sm font-medium flex items-center"
                  >
                    <FontAwesomeIcon icon={faPlus} className="mr-1" /> 添加事件
                  </button>
                </div>
                {addEventError && (
                  <div
                    className="mb-2 p-2 bg-red-500 bg-opacity-20 text-red-400 border border-red-500 rounded-lg text-xs"
                    role="alert"
                  >
                    添加事件失败: {addEventError}
                    <button
                      onClick={() => setAddEventError(null)}
                      className="ml-2 text-xs underline"
                    >
                      关闭
                    </button>
                  </div>
                )}
                <div className="space-y-4">
                  {timelineEvents.map((event, index) => (
                    <div
                      key={event.id || index}
                      className="p-4 border border-amber-500 border-opacity-30 rounded-lg bg-gray-900 bg-opacity-20"
                    >
                      <div className="flex justify-between items-start mb-2">
                        <div className="flex items-center">
                          <span className="font-semibold text-amber-500 mr-3">
                            {event.year}
                          </span>
                          <h4 className="font-medium text-white">
                            {event.title}
                          </h4>
                        </div>
                        {event.id && (
                          <button
                            onClick={() => handleDeleteTimelineEvent(event.id!)}
                            className={`text-red-400 hover:text-red-300 text-sm ${isDeletingEvent === event.id ? "opacity-50 cursor-not-allowed" : ""}`}
                            disabled={isDeletingEvent === event.id}
                            title="删除事件"
                          >
                            {isDeletingEvent === event.id ? (
                              <FontAwesomeIcon
                                icon={faSpinner}
                                spin
                                className="h-4 w-4 mr-1 inline"
                              />
                            ) : (
                              <FontAwesomeIcon
                                icon={faTrashAlt}
                                className="mr-1"
                              />
                            )}
                            {isDeletingEvent === event.id
                              ? "删除中..."
                              : "删除"}
                          </button>
                        )}
                      </div>
                      <p className="text-white text-sm">{event.description}</p>
                    </div>
                  ))}
                  {timelineEvents.length === 0 && (
                    <p className="text-gray-300 text-center py-4">
                      暂无生平大事记。点击“添加事件”开始记录。
                    </p>
                  )}
                </div>
                {deleteEventError && (
                  <div
                    className="mt-2 p-2 bg-red-500 bg-opacity-20 text-red-400 border border-red-500 rounded-lg text-xs text-center"
                    role="alert"
                  >
                    删除事件失败: {deleteEventError}
                    <button
                      onClick={() => setDeleteEventError(null)}
                      className="ml-2 text-xs underline"
                    >
                      关闭
                    </button>
                  </div>
                )}
              </div>
            </section>

            {/* Video Upload Section */}
            <section className="mb-8">
              <h2 className="text-xl font-semibold mb-4">
                上传追忆视频 (可选)
              </h2>
              {uploadVideoError && (
                <p className="text-red-400 text-xs mb-2">
                  Error: {uploadVideoError}
                </p>
              )}
              {deleteVideoAssetError && (
                <div
                  className="mb-2 p-2 bg-red-500 bg-opacity-20 text-red-400 border border-red-500 rounded-lg text-xs"
                  role="alert"
                >
                  视频删除失败: {deleteVideoAssetError}
                  <button
                    onClick={() => setDeleteVideoAssetError(null)}
                    className="ml-2 text-xs underline"
                  >
                    关闭
                  </button>
                </div>
              )}
              {deleteVideoAssetSuccess && (
                <div
                  className="mb-2 p-2 bg-green-500 bg-opacity-20 text-green-400 border border-green-500 rounded-lg text-xs"
                  role="alert"
                >
                  {deleteVideoAssetSuccess}
                  <button
                    onClick={() => setDeleteVideoAssetSuccess(null)}
                    className="ml-2 text-xs underline"
                  >
                    关闭
                  </button>
                </div>
              )}
              <div className="border-2 border-dashed border-amber-500 rounded-lg p-6 text-center bg-gray-800 hover:bg-gray-700 transition-colors">
                <input
                  type="file"
                  accept="video/mp4,video/quicktime,video/x-msvideo,video/x-flv,video/webm"
                  onChange={handleVideoUpload}
                  className="hidden"
                  id="video-upload"
                  disabled={isUploadingVideo}
                />
                <label
                  htmlFor="video-upload"
                  className={`cursor-pointer ${isUploadingVideo ? "opacity-50 cursor-not-allowed" : ""}`}
                >
                  {isUploadingVideo ? (
                    <FontAwesomeIcon
                      icon={faSpinner}
                      spin
                      className="h-8 w-8 text-amber-500 mb-2"
                    />
                  ) : (
                    <FontAwesomeIcon
                      icon={faVideo}
                      className="text-4xl text-amber-500 mb-2"
                    />
                  )}
                  <p className="text-amber-500">
                    {isUploadingVideo ? "上传中..." : "点击或拖拽上传视频"}
                  </p>
                  <p className="text-xs text-gray-300 mt-1">
                    支持 MP4, MOV 等常见格式
                  </p>
                </label>
              </div>
              {videoAssets.length > 0 && (
                <div className="mt-4 space-y-3">
                  <h3 className="text-md font-semibold text-white">
                    已上传视频:
                  </h3>
                  {videoAssets.map((video) => (
                    <div
                      key={video.id}
                      className="relative group p-3 border border-amber-500 border-opacity-30 rounded-md bg-gray-900 bg-opacity-20 flex items-center justify-between"
                    >
                      <div className="flex items-center">
                        <FontAwesomeIcon
                          icon={faFilm}
                          className="text-amber-500 mr-3 text-lg"
                        />
                        <a
                          href={video.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-amber-500 hover:underline truncate"
                          title={video.url}
                        >
                          {video.url.substring(
                            video.url.lastIndexOf("/") + 1,
                          ) || video.id}{" "}
                          (点击播放/下载)
                        </a>
                      </div>
                      <button
                        onClick={() => handleDeleteVideoAsset(video.id)}
                        disabled={isDeletingVideoAsset === video.id}
                        className={`ml-4 text-red-400 hover:text-red-300 text-xs ${isDeletingVideoAsset === video.id ? "opacity-50 cursor-wait" : ""}`}
                        title="删除视频"
                      >
                        {isDeletingVideoAsset === video.id ? (
                          <FontAwesomeIcon
                            icon={faSpinner}
                            spin
                            className="h-4 w-4"
                          />
                        ) : (
                          <FontAwesomeIcon icon={faTrashAlt} />
                        )}
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </section>

            {/* Access Permission Settings Section */}
            <section className="mb-8">
              <h2 className="text-xl font-semibold mb-4">访问权限设置</h2>
              <div className="space-y-3">
                {["public", "password", "family", "private"].map((perm) => (
                  <div key={perm} className="flex items-center">
                    <input
                      type="radio"
                      id={`privacy_level_${perm}`}
                      name="privacy_level" // Ensure name is consistent for radio group
                      value={perm}
                      checked={editableMemorialSpaceData.privacy_level === perm}
                      onChange={handleMemorialSpaceInputChange}
                      className="h-4 w-4 text-amber-500 focus:ring-amber-500 border-amber-500 border-opacity-50 bg-transparent"
                    />
                    <label
                      htmlFor={`privacy_level_${perm}`}
                      className="ml-2 block text-sm text-white"
                    >
                      <span className="font-medium capitalize">
                        {perm === "public" && "公开 (任何人可见)"}
                        {perm === "password" && "密码保护 (凭密码访问)"}
                        {perm === "family" && "仅家族成员 (需登录)"}
                        {perm === "private" && "私密 (仅自己和协作者可见)"}
                      </span>
                      <p className="text-gray-300 text-xs mt-1">
                        {perm === "public" &&
                          "空间内容对所有人开放，可能被搜索引擎索引。"}
                        {perm === "password" &&
                          "访问者需要输入您设置的密码才能查看空间。"}
                        {perm === "family" &&
                          "仅限通过验证的家族成员登录后可见。"}
                        {perm === "private" &&
                          "仅限您自己以及您授权的协作者可以访问和编辑。"}
                      </p>
                    </label>
                  </div>
                ))}
              </div>
              {editableMemorialSpaceData.privacy_level === "password" && (
                <div className="mt-4">
                  <label
                    htmlFor="access_password_edit"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    访问密码
                  </label>
                  <input
                    type="password"
                    id="access_password_edit"
                    name="access_password"
                    value={editableMemorialSpaceData.access_password || ""}
                    onChange={handleMemorialSpaceInputChange}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                    placeholder="请输入至少6位密码"
                  />
                </div>
              )}
            </section>

            {/* Save Button for Memorial Details */}
            <section className="my-8 pt-6 border-t border-amber-500 border-opacity-50">
              <button
                type="button"
                onClick={handleSaveMemorialSpaceDetails}
                className="bg-amber-500 hover:bg-yellow-300 text-gray-900 font-semibold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50 w-full md:w-auto"
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <FontAwesomeIcon
                      icon={faSpinner}
                      spin
                      className="-ml-1 mr-2 h-5 w-5 inline"
                    />
                    正在保存...
                  </>
                ) : (
                  "保存纪念空间信息"
                )}
              </button>
              {saveSuccess && (
                <p className="mt-2 text-sm text-green-400">{saveSuccess}</p>
              )}
              {saveError && (
                <p className="mt-2 text-sm text-red-400">
                  保存失败: {saveError}
                </p>
              )}
            </section>

            {/* Bottom Navigation Buttons */}
            <div className="flex justify-between mt-10 border-t border-amber-500 border-opacity-50 pt-6">
              <Link
                to={`/memorial-creation/${spaceId}/scene-selection`}
                className="bg-transparent hover:bg-amber-500 hover:bg-opacity-20 text-amber-500 border border-amber-500 border-opacity-50 font-semibold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50"
              >
                {" "}
                {/* Adjust route as needed */}
                <FontAwesomeIcon icon={faArrowLeft} className="mr-2" /> 上一步
              </Link>
              <Link
                to={`/memorial-space/${spaceId}/completion`}
                className="bg-amber-500 hover:bg-yellow-300 text-gray-900 font-semibold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50"
              >
                {" "}
                {/* Adjust route as needed */}
                下一步：完成创建{" "}
                <FontAwesomeIcon icon={faArrowRight} className="ml-2" />
              </Link>
            </div>
          </div>
        </main>
      </div>

      {/* Add Timeline Event Modal */}
      {showAddEventModal && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-900 border border-amber-500 border-opacity-30 p-6 rounded-lg shadow-xl w-full max-w-md">
            <h3 className="text-lg font-medium mb-4 text-amber-500">
              添加生平大事记
            </h3>
            <form
              onSubmit={(e: FormEvent) => {
                e.preventDefault();
                handleAddTimelineEvent();
              }}
            >
              <div className="space-y-4">
                <div>
                  <label
                    htmlFor="eventYearModal"
                    className="block text-sm font-medium text-white"
                  >
                    年份/日期
                  </label>
                  <input
                    type="text"
                    name="year"
                    id="eventYearModal"
                    value={newEvent.year}
                    onChange={(e) =>
                      setNewEvent({ ...newEvent, year: e.target.value })
                    }
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent mt-1"
                    placeholder="例如：1990年 或 1990-01-01"
                    required
                  />
                </div>
                <div>
                  <label
                    htmlFor="eventTitleModal"
                    className="block text-sm font-medium text-white"
                  >
                    事件标题
                  </label>
                  <input
                    type="text"
                    name="title"
                    id="eventTitleModal"
                    value={newEvent.title}
                    onChange={(e) =>
                      setNewEvent({ ...newEvent, title: e.target.value })
                    }
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent mt-1"
                    placeholder="例如：大学毕业"
                    required
                  />
                </div>
                <div>
                  <label
                    htmlFor="eventDescriptionModal"
                    className="block text-sm font-medium text-white"
                  >
                    事件描述
                  </label>
                  <textarea
                    name="description"
                    id="eventDescriptionModal"
                    value={newEvent.description}
                    onChange={(e) =>
                      setNewEvent({ ...newEvent, description: e.target.value })
                    }
                    rows={3}
                    className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent mt-1 resize-none"
                    placeholder="详细描述事件内容..."
                  ></textarea>
                </div>
              </div>
              {addEventError && (
                <p className="text-xs text-red-500 mt-3 mb-3">
                  {addEventError}
                </p>
              )}
              <div className="mt-6 flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddEventModal(false);
                    setAddEventError(null);
                    setNewEvent({ year: "", title: "", description: "" });
                  }}
                  className="bg-transparent hover:bg-amber-500 hover:bg-opacity-20 text-amber-500 border border-amber-500 border-opacity-50 font-semibold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50"
                  disabled={isAddingEvent}
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="bg-amber-500 hover:bg-yellow-300 text-gray-900 font-semibold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-opacity-50"
                  disabled={isAddingEvent}
                >
                  {isAddingEvent ? (
                    <>
                      <FontAwesomeIcon icon={faSpinner} spin className="mr-2" />
                      正在添加...
                    </>
                  ) : (
                    "确认添加"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default MemorialSpacePage;
