import React, { useState } from "react";
import { Link } from "react-router-dom";

// Define interfaces for family members (can be expanded)
interface FamilyMember {
  id: string;
  name: string;
  birthYear?: number;
  deathYear?: number;
  relation?: string; // e.g., 'Father', 'Mother', 'Spouse', 'Child'
  parentId?: string | null; // ID of the parent node for tree structure
  spouseId?: string | null; // ID of the spouse
  childrenIds?: string[];
  // Add more properties as needed: photoUrl, bio, etc.
}

// Placeholder data for the family tree
// In a real application, this would come from an API and be processed to form a tree structure.
const familyMembersData: FamilyMember[] = [
  {
    id: "1",
    name: "王大锤",
    birthYear: 1890,
    deathYear: 1965,
    relation: "曾祖父",
  },
  {
    id: "2",
    name: "王建国",
    birthYear: 1920,
    deathYear: 1990,
    relation: "祖父",
    parentId: "1",
  },
  {
    id: "3",
    name: "王秀英",
    birthYear: 1925,
    deathYear: 2005,
    relation: "祖母",
    spouseId: "2",
  },
  {
    id: "4",
    name: "王强",
    birthYear: 1950,
    deathYear: 2020,
    relation: "父亲",
    parentId: "2",
  },
  // Add more members as needed
];

const FamilyTreePage: React.FC = () => {
  const members = useState<FamilyMember[]>(familyMembersData)[0];
  const [isEditMode, setIsEditMode] = useState(false);

  // 使用Tailwind原生类替代自定义按钮样式常量
  // 已删除 memberNodeClasses 样式常量，直接使用 Tailwind 原生类

  // Placeholder for actual tree rendering logic
  // A proper family tree visualization would require a dedicated library (e.g., D3.js, react-flow, etc.)
  // or significant custom SVG/CSS work.
  const renderSimplifiedTree = () => (
    <div className="space-y-6 p-4 bg-gray-900 bg-opacity-10 border border-[#185a56] border-opacity-30 rounded-lg">
      {members
        .filter((m) => !m.parentId)
        .map((rootMember) => (
          <div key={rootMember.id} className="flex flex-col items-center">
            <div className="member-node bg-gray-900 bg-opacity-20 border border-[#185a56] border-opacity-50 rounded-md p-3 text-center shadow-sm hover:shadow-md transition-shadow duration-200 w-48">
              <p className="font-bold text-white">
                {rootMember.name} ({rootMember.relation})
              </p>
              {(rootMember.birthYear || rootMember.deathYear) && (
                <p className="text-sm text-gray-300">
                  {rootMember.birthYear} - {rootMember.deathYear || "Present"}
                </p>
              )}
            </div>
            {/* Render children recursively or iteratively - simplified here */}
            {members.filter((child) => child.parentId === rootMember.id)
              .length > 0 && (
              <div className="mt-4 flex justify-center space-x-6">
                {members
                  .filter((child) => child.parentId === rootMember.id)
                  .map((childNode) => (
                    <div
                      key={childNode.id}
                      className="flex flex-col items-center"
                    >
                      <div className="h-6 w-px bg-[#185a56] bg-opacity-50"></div>{" "}
                      {/* Connector line */}
                      <div className="member-node bg-gray-900 bg-opacity-20 border border-[#185a56] border-opacity-50 rounded-md p-3 text-center shadow-sm hover:shadow-md transition-shadow duration-200 w-40">
                        <p className="font-bold text-white">
                          {childNode.name} ({childNode.relation})
                        </p>
                        {(childNode.birthYear || childNode.deathYear) && (
                          <p className="text-sm text-gray-300">
                            {childNode.birthYear} -{" "}
                            {childNode.deathYear || "Present"}
                          </p>
                        )}
                      </div>
                      {/* Further nesting for grandchildren etc. would go here */}
                    </div>
                  ))}
              </div>
            )}
          </div>
        ))}
    </div>
  );

  return (
    <div className="flex flex-col min-h-screen bg-gray-900 text-white">
      {/* <Navbar /> */}
      {/* <Sidebar /> */}
      {/* Assuming Navbar and Sidebar might be part of a layout wrapper for this page type */}
      {/* For now, a simplified header similar to the prototype */}
      <header className="bg-gray-900 border-b border-[#185a56] border-opacity-30 shadow-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-3 flex justify-between items-center">
          <div className="flex items-center">
            <i className="fas fa-sitemap text-[#185a56] text-2xl mr-3"></i>
            <span className="text-xl font-bold text-[#185a56]">归处家族树</span>
          </div>
          {/* Add navigation or user profile items if needed, similar to other pages */}
          <div>
            <Link
              to="/dashboard"
              className="bg-transparent hover:bg-[#185a56] hover:bg-opacity-20 text-[#185a56] border border-[#185a56] border-opacity-50 font-semibold py-2 px-4 rounded-lg transition duration-300 focus:outline-none focus:ring-2 focus:ring-[#185a56] focus:ring-opacity-50 text-sm"
            >
              返回仪表盘
            </Link>
          </div>
        </div>
      </header>

      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
            家族脉络传承
          </h1>
          <p className="text-lg text-gray-300">
            记录家族历史，传承家族记忆，一目了然的家族树。
          </p>
        </div>

        <div className="bg-gray-900 border border-[#185a56] border-opacity-30 p-6 sm:p-8 rounded-lg shadow-xl mb-8">
          <div className="flex flex-col sm:flex-row justify-between items-center mb-6">
            <h2 className="text-2xl font-semibold text-white mb-4 sm:mb-0">
              王氏家族树
            </h2>
            <div className="flex space-x-3">
              <button className="bg-[#185a56] hover:bg-[#FFA631] text-gray-900 font-semibold py-2 px-4 rounded-lg transition duration-300 focus:outline-none focus:ring-2 focus:ring-[#185a56] focus:ring-opacity-50 text-sm">
                <i className="fas fa-plus mr-2"></i>添加成员
              </button>
              <button
                onClick={() => setIsEditMode(!isEditMode)}
                className={`bg-transparent hover:bg-[#185a56] hover:bg-opacity-20 text-[#185a56] border border-[#185a56] border-opacity-50 font-semibold py-2 px-4 rounded-lg transition duration-300 focus:outline-none focus:ring-2 focus:ring-[#185a56] focus:ring-opacity-50 text-sm ${isEditMode ? "ring-2 ring-[#185a56]" : ""}`}
              >
                <i
                  className={`fas ${isEditMode ? "fa-check" : "fa-edit"} mr-2`}
                ></i>
                {isEditMode ? "完成编辑" : "编辑模式"}
              </button>
            </div>
          </div>

          <div className="tree-container bg-gray-900 bg-opacity-20 border border-[#185a56] border-opacity-30 rounded-lg min-h-[400px] flex flex-col items-center justify-center p-6">
            {/* Actual tree rendering would go here */}
            {members.length > 0 ? (
              renderSimplifiedTree()
            ) : (
              <p className="text-gray-300 text-center py-10">
                <i className="fas fa-sitemap text-[#185a56] text-4xl mb-4"></i>
                <br />
                家族树为空。请开始添加家族成员。
              </p>
            )}
            <p className="mt-6 text-xs text-gray-400 text-center">
              提示：这是一个简化的家族树展示。完整的交互式家族树通常需要专门的图表库。
            </p>
          </div>
        </div>
      </main>

      <footer className="bg-gray-900 bg-opacity-50 text-gray-300 py-8 text-center mt-auto">
        <div className="container mx-auto">
          <p>
            &copy; {new Date().getFullYear()} 归处在线祭祀平台. 保留所有权利.
          </p>
          <p className="text-sm mt-2">用心连接思念，科技传承记忆。</p>
        </div>
      </footer>
    </div>
  );
};

export default FamilyTreePage;

// Notes:
// - This is a basic representation. A full family tree requires a robust tree-drawing library or complex SVG/Canvas logic.
// - Assumes Font Awesome for icons.
// - Placeholder data is used. Real data would come from an API.
// - Edit/Add functionality is not implemented, only UI placeholders.
// - Navbar/Sidebar structure is simplified based on the prototype's header.
//   Integrate with your project's actual Navbar/Sidebar/Layout components as needed.
// - Dark mode compatibility is considered with dark: prefixes.
