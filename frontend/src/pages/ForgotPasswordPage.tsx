import React, { useState } from "react";
import { Link } from "react-router-dom";

const ForgotPasswordPage: React.FC = () => {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fieldError, setFieldError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const validateForm = (): boolean => {
    setFieldError(null);
    if (!email) {
      setFieldError("请输入您的注册邮箱地址。");
      return false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      setFieldError("请输入有效的邮箱地址。");
      return false;
    }
    return true;
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setSuccessMessage(null);

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL;
      const response = await fetch(`${API_BASE_URL}/auth/forgot-password`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "发送重置邮件失败，请重试。");
      }

      const data = await response.json();
      setSuccessMessage(data.message || "密码重置邮件已发送，请检查您的邮箱。");
      setEmail(""); // Clear the input field on success
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error ? err.message : "发生未知错误，请稍后再试。";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-gray-900 text-white flex items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md bg-gray-900 border border-[#185a56] border-opacity-30 shadow-2xl rounded-xl p-8 md:p-12">
        <div className="text-center mb-8">
          <i className="fas fa-key text-[#185a56] text-5xl mb-3"></i>
          <h1 className="text-3xl font-bold text-white">找回密码</h1>
          <p className="text-gray-300 mt-2">
            我们将向您的邮箱发送重置密码的链接
          </p>
        </div>

        {successMessage && (
          <div className="mb-4 p-3 bg-green-500 bg-opacity-20 text-green-300 border border-green-500 rounded-lg text-sm">
            {successMessage}
          </div>
        )}
        {error && (
          <div className="mb-4 p-3 bg-red-500 bg-opacity-20 text-red-400 border border-red-500 rounded-lg text-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              注册邮箱地址
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i className="fas fa-envelope text-gray-400"></i>
              </div>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={`w-full pl-10 pr-3 py-3 bg-gray-900 bg-opacity-20 border ${fieldError ? "border-red-500" : "border-[#185a56] border-opacity-50"} text-white rounded-lg focus:ring-2 focus:ring-[#185a56] focus:border-transparent outline-none transition duration-150 ease-in-out`}
                placeholder="<EMAIL>"
              />
            </div>
            {fieldError && (
              <p className="mt-1 text-xs text-red-400">{fieldError}</p>
            )}
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-gray-900 bg-[#185a56] hover:bg-[#ffa631] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-[#185a56] transition duration-150 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  发送中...
                </>
              ) : (
                <>
                  <i className="fas fa-paper-plane mr-2"></i> 发送重置链接
                </>
              )}
            </button>
          </div>
        </form>

        <p className="mt-8 text-center text-sm text-gray-300">
          记起密码了?{" "}
          <Link
            to="/login"
            className="font-medium text-[#185a56] hover:underline"
          >
            返回登录
          </Link>
        </p>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;

// Tailwind CSS color mapping (from prototype's tailwind.config.js and body style):
// bg-app-bg-dark: 'var(--color-gray-900)'
// text-text-dark-primary: 'var(--color-gray-50)'
// bg-card-bg-dark: 'var(--color-gray-800)'
// text-serene-blue: 'var(--color-primary)'
// text-moonlight-white: 'var(--color-gray-50)'
// text-text-dark-secondary: 'var(--color-gray-500)'
// bg-input-bg-dark: 'var(--color-gray-700)'
// border-gray-600
// text-gray-400
// focus:ring-serene-blue
// focus:border-serene-blue
// hover:bg-opacity-80 (for serene-blue button)
// focus:ring-offset-app-bg-dark

// It's assumed that these Tailwind custom colors are defined in the global Tailwind configuration.
// Font Awesome classes (fas fa-*) require Font Awesome to be set up in the project.
