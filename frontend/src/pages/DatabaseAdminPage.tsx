import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import DatabaseAdminPanel from '../components/DatabaseAdminPanel';

const DatabaseAdminPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 检查用户是否登录且具有管理员权限
    if (!isAuthenticated) {
      navigate('/login', { replace: true });
      return;
    }

    // 检查是否为管理员用户（这里假设管理员用户名为 'admin' 或有特殊标识）
    if (user && !user.is_superuser) {
      // 如果不是管理员，重定向到首页
      navigate('/', { replace: true });
      return;
    }

    setLoading(false);
  }, [isAuthenticated, user, navigate]);

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        fontSize: '18px',
        color: '#64748b'
      }}>
        正在验证权限...
      </div>
    );
  }

  if (!isAuthenticated || !user?.is_superuser) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <h2 style={{ color: '#dc2626', margin: 0 }}>访问被拒绝</h2>
        <p style={{ color: '#64748b', margin: 0 }}>您需要管理员权限才能访问此页面</p>
        <button 
          onClick={() => navigate('/')}
          style={{
            padding: '12px 24px',
            background: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer'
          }}
        >
          返回首页
        </button>
      </div>
    );
  }

  return <DatabaseAdminPanel />;
};

export default DatabaseAdminPage;