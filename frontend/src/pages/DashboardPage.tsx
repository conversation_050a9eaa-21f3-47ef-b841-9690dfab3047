import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import Navbar from "../components/Navbar";
import Sidebar from "../components/Sidebar"; // 取消注释，Sidebar 组件已存在

// Interfaces (Consider moving to a separate types file if they grow)
interface MemorialSpace {
  id: string;
  deceased_name: string; // 统一使用 API 返回的字段名
  birth_date?: string;
  death_date: string;
  cover_image_url?: string; // 封面图片 URL
  scene_id?: string; // 场景 ID
  created_at: string; // 创建时间
}

interface StatCardData {
  title: string;
  value: string | number;
  icon: string; // Font Awesome class, e.g., 'fas fa-landmark'
  colorClass: string; // Tailwind CSS color class for icon bg/text
}

interface QuickActionData {
  title: string;
  description: string;
  icon: string;
  link: string;
  colorClass: string;
}

// Placeholder Data (Replace with API calls in a real application)
// 移除未使用的 initialMemorials 常量声明

const statsData: StatCardData[] = [
  {
    title: "我的纪念馆",
    value: 5,
    icon: "fas fa-landmark",
    colorClass: "text-amber-500 bg-amber-500/20",
  },
  {
    title: "家族空间",
    value: 3,
    icon: "fas fa-users",
    colorClass: "text-amber-500 bg-amber-500/20",
  }, // Changed from warm-gold
  {
    title: "总祭拜次数",
    value: 128,
    icon: "fas fa-hand-holding-heart",
    colorClass: "text-green-400 bg-green-400/20",
  }, // Kept green semantic
  {
    title: "近期纪念日",
    value: 2,
    icon: "fas fa-calendar-alt",
    colorClass: "text-red-400 bg-red-400/20",
  }, // Kept red semantic
];

const quickActionsData: QuickActionData[] = [
  {
    title: "创建纪念馆",
    description: "开始新的数字传承",
    icon: "fas fa-plus-circle",
    link: "/create-memorial",
    colorClass: "text-amber-500",
  },
  {
    title: "家族管理",
    description: "管理家族成员与族谱",
    icon: "fas fa-users",
    link: "/family-management",
    colorClass: "text-amber-500",
  }, // Adjust link
  {
    title: "AI照片修复",
    description: "修复珍贵的老照片",
    icon: "fas fa-magic",
    link: "/ai-services/photo-repair",
    colorClass: "text-amber-500",
  }, // Adjust link, changed from warm-gold
  {
    title: "纪念日提醒",
    description: "查看重要纪念日",
    icon: "fas fa-calendar-check",
    link: "/events",
    colorClass: "text-green-400",
  }, // Adjust link, kept green semantic
  {
    title: "账户设置",
    description: "管理您的账户信息",
    icon: "fas fa-cog",
    link: "/settings",
    colorClass: "text-amber-500",
  }, // Adjust link, changed to apricot
];

const DashboardPage: React.FC = () => {
  const [memorials, setMemorials] = useState<MemorialSpace[]>([]);
  const [isLoadingMemorials, setIsLoadingMemorials] = useState<boolean>(true);
  const [memorialsError, setMemorialsError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchMemorials = async () => {
      setIsLoadingMemorials(true);
      setMemorialsError(null);
      const token = localStorage.getItem("authToken");

      if (!token) {
        setMemorialsError("用户未认证，请先登录。");
        setIsLoadingMemorials(false);
        navigate("/login");
        return;
      }

      try {
        const API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL;
        const response = await fetch(`${API_BASE_URL}/memorial-spaces`, {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          if (response.status === 401) {
            localStorage.removeItem("authToken");
            navigate("/login?sessionExpired=true");
            return;
          }
          const errorData = await response.json();
          throw new Error(errorData.detail || "获取纪念馆列表失败。");
        }

        const data = await response.json();
        // Assuming API returns { items: MemorialSpace[] } based on MemorialSpaceListResponse
        setMemorials(data.items || []);
      } catch (err: unknown) {
        const errorMessage =
          err instanceof Error ? err.message : "加载纪念馆时发生未知错误。";
        setMemorialsError(errorMessage);
      } finally {
        setIsLoadingMemorials(false);
      }
    };

    fetchMemorials();
  }, [navigate]);

  const StatCard: React.FC<StatCardData> = ({
    title,
    value,
    icon,
    colorClass,
  }) => (
    <div className="stat-card bg-gray-900 border border-amber-500 border-opacity-30 p-6 rounded-xl shadow-lg transition-all duration-300 ease-in-out hover:shadow-xl hover:shadow-amber-500/20">
      <div className="flex items-center">
        <div
          className={`p-3 rounded-full ${colorClass.includes("/") ? colorClass : colorClass + " bg-opacity-20"}`}
        >
          {" "}
          {/* Ensure bg opacity for icon backgrounds */}
          <i className={`${icon} fa-2x ${colorClass.split(" ")[0]}`}></i>{" "}
          {/* Ensure icon text color is applied */}
        </div>
        <div className="ml-4">
          <p className="text-3xl font-bold text-white">{value}</p>
          <p className="text-sm text-gray-300">{title}</p>
        </div>
      </div>
    </div>
  );

  const MemorialCard: React.FC<MemorialSpace> = ({
    id,
    deceased_name,
    death_date,
    cover_image_url,
    created_at,
  }) => (
    <div className="bg-gray-900 border border-amber-500 border-opacity-30 rounded-xl shadow-lg overflow-hidden transform hover:scale-105 transition-transform duration-300">
      <img
        className="h-48 w-full object-cover bg-gray-900 bg-opacity-50"
        src={
          cover_image_url || (() => {
            const svgContent = `
              <svg xmlns="http://www.w3.org/2000/svg" width="400" height="200" viewBox="0 0 400 200">
                <rect width="400" height="200" fill="#f3f4f6"/>
                <text x="200" y="100" font-family="Arial, sans-serif" font-size="20" fill="#6b7280" text-anchor="middle" dominant-baseline="middle">
                  暂无封面
                </text>
              </svg>
            `;
            return `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`;
          })()
        }
        alt={`${deceased_name} cover`}
      />
      <div className="p-6">
        <h3 className="text-lg font-semibold text-white mb-1">
          {deceased_name}
        </h3>
        <p className="text-sm text-gray-300 mb-3">
          逝于:{" "}
          {death_date ? new Date(death_date).toLocaleDateString() : "日期未知"}
        </p>
        <p className="text-xs text-gray-400 mb-4">
          创建于:{" "}
          {created_at ? new Date(created_at).toLocaleDateString() : "日期未知"}
        </p>
        <div className="flex justify-between items-center">
          <Link
            to={`/memorial-space/${id}`}
            className="text-sm text-amber-500 hover:text-yellow-300 hover:underline"
          >
            进入空间 <i className="fas fa-arrow-right ml-1"></i>
          </Link>
          <div className="flex space-x-3">
            <button className="text-gray-400 hover:text-amber-500">
              <i className="fas fa-edit"></i>
            </button>
            <button className="text-gray-400 hover:text-red-400">
              <i className="fas fa-trash"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const QuickActionCard: React.FC<QuickActionData> = ({
    title,
    description,
    icon,
    link,
    colorClass,
  }) => (
    <Link
      to={link}
      className="bg-gray-900 border border-amber-500 border-opacity-30 p-6 rounded-xl shadow-lg flex flex-col items-center text-center hover:bg-gray-900 hover:bg-opacity-70 transition-colors duration-300"
    >
      <i className={`${icon} text-3xl ${colorClass} mb-3`}></i>
      <h4 className="font-semibold text-white">{title}</h4>
      <p className="text-xs text-gray-300">{description}</p>
    </Link>
  );

  return (
    <div className="flex min-h-screen bg-gray-900 text-white">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        {/* Main content area that will scroll */}
        <main className="flex-1 overflow-y-auto">
          <header className="bg-gray-900 border-b border-amber-500 border-opacity-30 shadow-md sticky top-0 z-40">
            {" "}
            {/* Ensure Navbar is on top if it's also sticky */}
            <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex flex-col sm:flex-row justify-between items-center gap-4">
              <h1 className="text-3xl font-bold text-amber-500">仪表盘</h1>
              <Link
                to="/create-memorial"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-gray-900 bg-amber-500 hover:bg-yellow-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-amber-500"
              >
                <i className="fas fa-plus mr-2"></i> 创建新的纪念空间
              </Link>
            </div>
          </header>

          <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
            {/* Statistics Section */}
            <section className="mb-8">
              <h2 className="text-xl font-semibold text-gray-300 mb-4">概览</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {statsData.map((stat) => (
                  <StatCard key={stat.title} {...stat} />
                ))}
              </div>
            </section>

            {/* Recent Memorials Section */}
            <section id="memorials" className="mb-8">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold text-gray-300">
                  我的纪念馆
                </h2>
                {memorials.length > 0 && (
                  <Link
                    to="/my-memorials"
                    className="text-sm text-amber-500 hover:text-yellow-300 hover:underline"
                  >
                    查看全部
                  </Link>
                )}
              </div>
              {isLoadingMemorials && (
                <div className="text-center py-8">
                  <i className="fas fa-spinner fa-spin text-amber-500 text-3xl"></i>
                  <p className="mt-2 text-gray-300">正在加载纪念馆...</p>
                </div>
              )}
              {memorialsError && (
                <div className="p-4 bg-red-500 bg-opacity-20 text-red-400 border border-red-500 rounded-lg text-sm">
                  {memorialsError}
                </div>
              )}
              {!isLoadingMemorials && !memorialsError && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {memorials.slice(0, 2).map((memorial) => (
                    <MemorialCard key={memorial.id} {...memorial} />
                  ))}
                  {/* Always show create button, or conditionally if list is short */}
                  <Link
                    to="/create-memorial"
                    className="bg-gray-900 border-2 border-dashed border-amber-500 border-opacity-50 rounded-xl shadow-lg flex flex-col items-center justify-center text-center p-6 hover:border-amber-500 hover:bg-gray-900 hover:bg-opacity-70 transition-all duration-300 transform hover:scale-105 min-h-[270px] md:min-h-[290px]"
                  >
                    <i className="fas fa-plus-circle text-amber-500 text-4xl mb-3"></i>
                    <h4 className="font-semibold text-white">创建新的纪念馆</h4>
                    <p className="text-xs text-gray-300 mt-1">
                      为逝去的亲人建立一个永恒的数字空间。
                    </p>
                  </Link>
                  {memorials.length === 0 && (
                    <div className="md:col-span-2 lg:col-span-3 text-center py-8 px-4 bg-gray-900 border border-amber-500 border-opacity-30 rounded-xl shadow-lg">
                      <i className="fas fa-folder-open text-amber-500 text-4xl mb-3"></i>
                      <h3 className="text-xl font-semibold text-white mb-2">
                        您还没有创建任何纪念馆
                      </h3>
                      <p className="text-gray-300 mb-4">
                        点击上方的“创建新的纪念馆”按钮，开始您的第一个数字传承吧。
                      </p>
                    </div>
                  )}
                </div>
              )}
            </section>

            {/* Quick Actions Section */}
            <section id="quick-actions" className="mb-8">
              <h2 className="text-xl font-semibold text-gray-300 mb-4">
                快捷操作
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
                {quickActionsData.map((action) => (
                  <QuickActionCard key={action.title} {...action} />
                ))}
              </div>
            </section>
          </div>
        </main>
      </div>
    </div>
  );
};

export default DashboardPage;

// Notes:
// - This page is based on dashboard.html and adapted for React/Tailwind.
// - Assumes Font Awesome for icons.
// - Placeholder data is used. In a real app, fetch data via API.
// - Navbar and Sidebar components are assumed to exist. If your project structure is different (e.g., Sidebar is part of a layout), adjust accordingly.
// - The color palette from dashboard.html (serene-blue, warm-gold, etc.) has been mapped to Tailwind utility classes or inline styles where appropriate.
//   You might want to define these custom colors in your tailwind.config.js for easier use.
// - Mobile responsiveness for Navbar is simplified. Implement full toggle functionality if needed.
// - Links for navigation (e.g., /my-memorials, /family-management) are placeholders.
// - Dark mode is implemented using dark: prefixes from Tailwind CSS.
