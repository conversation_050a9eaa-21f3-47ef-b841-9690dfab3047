import React from "react";
// import Navbar from '../components/Navbar'; // Assuming a Navbar component exists

const WorshipPage: React.FC = () => {
  const interactiveItems = [
    {
      title: "敬献鲜花",
      description: "选择一束美丽的鲜花，表达您的思念之情。",
      icon: "fas fa-spa",
      buttonText: "选择鲜花",
      buttonColor: "bg-indigo-500 hover:bg-indigo-600",
      imgSrc:
        "https://images.unsplash.com/photo-1527066575791-0965fed54658?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Zmxvd2VycyUyMGJvdXF1ZXR8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=300&q=60",
      altText: "鲜花",
    },
    {
      title: "点燃香烛",
      description: "点燃一炷清香或一对明烛，祈愿逝者安息。",
      icon: "fas fa-fire",
      buttonText: "点燃香烛",
      buttonColor: "bg-[#185a56] hover:bg-[#FFA631]",
      imgSrc:
        "https://images.unsplash.com/photo-1588670938099-58e0ff015a5b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8Y2FuZGxlc3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=300&q=60",
      altText: "香烛",
    },
    {
      title: "敬奉供品",
      description: "选择水果、点心等供品，表达您的敬意。",
      icon: "fas fa-apple-alt",
      buttonText: "选择供品",
      buttonColor: "bg-lime-500 hover:bg-lime-600",
      imgSrc:
        "https://images.unsplash.com/photo-1598023003006-9668c6ff7f1a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTB8fGZydWl0JTIwYmFza2V0fGVufDB8fDB8fHww&auto=format&fit=crop&w=300&q=60",
      altText: "供品",
    },
    {
      title: "留言祈福",
      description: "写下您的思念与祝福，让心意永存。",
      icon: "fas fa-pencil-alt",
      buttonText: "写下留言",
      buttonColor: "bg-sky-500 hover:bg-sky-600",
      imgSrc:
        "https://images.unsplash.com/photo-1516467508483-a7212fe43291?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8bm90ZXBhZCUyMGFuZCUyMHBlbnxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=300&q=60",
      altText: "留言",
    },
    {
      title: "播放追思音乐",
      description: "选择一首宁静的音乐，在旋律中缅怀。",
      icon: "fas fa-music",
      buttonText: "选择音乐",
      buttonColor: "bg-purple-500 hover:bg-purple-600",
      imgSrc:
        "https://images.unsplash.com/photo-1511379938547-c1f69419868d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8bXVzaWN8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=300&q=60",
      altText: "音乐",
    },
    {
      title: "进入3D纪念堂",
      description: "沉浸式体验，身临其境进行虚拟祭扫。",
      icon: "fas fa-vr-cardboard",
      buttonText: "进入场景",
      buttonColor: "bg-teal-500 hover:bg-teal-600",
      imgSrc:
        "https://images.unsplash.com/photo-1612287230202-95aaa7e84e8f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8M2QlMjBzY2VuZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=300&q=60",
      altText: "3D祭扫",
      className: "md:col-span-1 lg:col-span-1", // Special class for this item
    },
  ];

  return (
    <div className="flex flex-col min-h-screen bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
      {/* <Navbar /> */}
      {/* Placeholder for Top Navigation - assuming it's handled globally or in a layout component */}
      {/* <header className="bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-3 flex justify-between items-center">
          <div className="flex items-center">
            <i className="fas fa-pray text-indigo-600 text-2xl mr-3"></i>
            <span className="text-xl font-bold text-indigo-800 dark:text-indigo-400">归处</span>
          </div>
          <nav className="hidden md:flex space-x-4">
            <Link to="/" className="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">首页</Link>
            <Link to="/dashboard" className="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">我的纪念馆</Link>
            <Link to="/worship" className="text-indigo-600 dark:text-indigo-400 font-semibold">祭拜互动</Link>
            <Link to="/store" className="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">祭品商城</Link>
            <Link to="/family-tree" className="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">家族树</Link>
          </nav>
          <div className="flex items-center space-x-4">
            <button id="theme-toggle-button" className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
              <i className="fas fa-sun"></i> 
            </button>
            <div className="relative">
              <button className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200">
                <i className="fas fa-bell text-xl"></i>
              </button>
            </div>
            <Link to="/login" className="text-gray-600 dark:text-gray-300 hover:text-indigo-600 dark:hover:text-indigo-400">登录</Link>
            <Link to="/register" className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md text-sm font-medium">注册</Link>
          </div>
        </div>
      </header> */}

      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 dark:text-white mb-4">
            祭拜互动体验
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            选择一种方式，寄托您的哀思与敬意。
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {interactiveItems.map((item, index) => (
            <div
              key={index}
              className={`interactive-item bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center transform transition-transform duration-200 ease-in-out hover:-translate-y-1 hover:shadow-xl ${item.className || ""}`}
            >
              <img
                src={item.imgSrc}
                alt={item.altText}
                className="w-32 h-32 mx-auto rounded-full mb-4 object-cover"
              />
              <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-2">
                {item.title}
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                {item.description}
              </p>
              <button
                className={`${item.buttonColor} text-white font-semibold py-2 px-4 rounded-lg transition duration-300`}
              >
                <i className={`${item.icon} mr-2`}></i>
                {item.buttonText}
              </button>
            </div>
          ))}
        </div>
      </main>

      <footer className="bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400 py-8 text-center mt-auto">
        <div className="container mx-auto">
          <p>
            &copy; {new Date().getFullYear()} 归处在线祭祀平台. 保留所有权利.
          </p>
          <p className="text-sm mt-2">用心连接思念，科技传承记忆。</p>
        </div>
      </footer>
    </div>
  );
};

export default WorshipPage;
