import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";

const RegisterPage: React.FC = () => {
  const [username, setUsername] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null); // For API errors
  const [fieldErrors, setFieldErrors] = useState<{
    username?: string;
    email?: string;
    password?: string;
    confirmPassword?: string;
    terms?: string;
  }>({});
  const navigate = useNavigate();

  const validateForm = (): boolean => {
    const newFieldErrors: typeof fieldErrors = {};
    let isValid = true;

    if (!username) {
      newFieldErrors.username = "请输入用户名。";
      isValid = false;
    }

    if (!email) {
      newFieldErrors.email = "请输入邮箱地址。";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newFieldErrors.email = "请输入有效的邮箱地址。";
      isValid = false;
    }

    if (!password) {
      newFieldErrors.password = "请输入密码。";
      isValid = false;
    } else if (password.length < 8) {
      newFieldErrors.password = "密码长度至少为8位。";
      isValid = false;
    }

    if (!confirmPassword) {
      newFieldErrors.confirmPassword = "请再次输入密码以确认。";
      isValid = false;
    } else if (password !== confirmPassword) {
      newFieldErrors.confirmPassword = "两次输入的密码不一致。";
      isValid = false;
    }

    if (!termsAccepted) {
      newFieldErrors.terms = "您必须同意服务条款和隐私政策。";
      isValid = false;
    }

    setFieldErrors(newFieldErrors);
    return isValid;
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null);
    setFieldErrors({});

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // 使用Vite代理，相对路径会自动转发到后端
      const API_ENDPOINT = "/api/v1/auth/register";
      const requestBody = { username, email, password };
      
      const response = await fetch(API_ENDPOINT, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        let errorMessage = "注册失败，请稍后再试。";
        try {
          const errorData = await response.json();
          if (errorData.detail) {
            if (errorData.detail.includes("already exists")) {
              errorMessage = errorData.detail.includes("email") 
                ? "该邮箱已被注册，请使用其他邮箱。"
                : "该用户名已被注册，请使用其他用户名。";
            } else {
              errorMessage = errorData.detail;
            }
          }
        } catch (jsonError) {
          // 如果响应不是JSON格式，使用状态码信息
          const errorText = await response.text();
          if (response.status === 500) {
            errorMessage = "服务器内部错误，请稍后重试。";
          } else {
            errorMessage = `服务器错误 (${response.status})`;
          }
        }
        throw new Error(errorMessage);
      }

      await response.json();
      
      // 注册成功后显示邮箱验证提示
      navigate("/login", {
        state: { 
          message: "注册成功！请使用您的用户名或邮箱登录。",
          email: email,
          type: "success"
        }
      });
    } catch (err: unknown) {
      let errorMessage = "发生未知错误，请稍后再试。";
      
      if (err instanceof Error) {
        if (err.message.includes("Failed to fetch")) {
          errorMessage = "网络连接失败，请检查网络连接或稍后重试。";
        } else if (err.message.includes("CORS")) {
          errorMessage = "服务器配置错误，请联系管理员。";
        } else {
          errorMessage = err.message;
        }
      }
      
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-gray-900 text-white flex items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md bg-gray-900 border border-amber-500 border-opacity-30 shadow-2xl rounded-xl p-8 md:p-12">
        <div className="text-center mb-8">
          {/* <i className="fas fa-user-plus text-amber-500 text-5xl mb-3"></i> */}
          <h1 className="text-3xl font-bold text-white">创建归处账户</h1>
          <p className="text-white mt-2">开启您的数字纪念之旅</p>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-500 bg-opacity-20 text-red-400 border border-red-500 rounded-lg text-sm">
            {error}
          </div>
        )}

        {/* 显示表单验证错误的总结 */}
        {Object.keys(fieldErrors).length > 0 && (
          <div className="mb-4 p-3 bg-yellow-500 bg-opacity-20 text-yellow-400 border border-yellow-500 rounded-lg text-sm">
            <div className="font-medium mb-1">请检查以下信息：</div>
            <ul className="list-disc list-inside space-y-1">
              {Object.entries(fieldErrors).map(([field, message]) => (
                <li key={field}>{message}</li>
              ))}
            </ul>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label
              htmlFor="username"
              className="block text-sm font-medium text-white mb-1"
            >
              用户名
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                {/* <i className="fas fa-user text-amber-500 opacity-75"></i> */}
              </div>
              <input
                id="username"
                name="username"
                type="text"
                autoComplete="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className={`w-full pl-10 pr-3 py-3 bg-gray-900 bg-opacity-20 border ${fieldErrors.username ? "border-red-500" : "border-amber-500 border-opacity-50"} text-white rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent outline-none transition duration-150 ease-in-out`}
                placeholder="设置您的用户名"
              />
            </div>
            {fieldErrors.username && (
              <p className="mt-1 text-xs text-red-400">
                {fieldErrors.username}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-white mb-1"
            >
              邮箱地址
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                {/* <i className="fas fa-envelope text-amber-500 opacity-75"></i> */}
              </div>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={`w-full pl-10 pr-3 py-3 bg-gray-900 bg-opacity-20 border ${fieldErrors.email ? "border-red-500" : "border-amber-500 border-opacity-50"} text-white rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent outline-none transition duration-150 ease-in-out`}
                placeholder="<EMAIL>"
              />
            </div>
            {fieldErrors.email && (
              <p className="mt-1 text-xs text-red-400">{fieldErrors.email}</p>
            )}
          </div>

          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-white mb-1"
            >
              密码
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                {/* <i className="fas fa-lock text-amber-500 opacity-75"></i> */}
              </div>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="new-password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className={`w-full pl-10 pr-3 py-3 bg-gray-900 bg-opacity-20 border ${fieldErrors.password ? "border-red-500" : "border-amber-500 border-opacity-50"} text-white rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent outline-none transition duration-150 ease-in-out`}
                placeholder="请输入至少8位密码"
              />
            </div>
            {fieldErrors.password && (
              <p className="mt-1 text-xs text-red-400">
                {fieldErrors.password}
              </p>
            )}
          </div>

          <div>
            <label
              htmlFor="confirm-password"
              className="block text-sm font-medium text-white mb-1"
            >
              确认密码
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                {/* <i className="fas fa-check-circle text-amber-500 opacity-75"></i> */}
              </div>
              <input
                id="confirm-password"
                name="confirm-password"
                type="password"
                autoComplete="new-password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className={`w-full pl-10 pr-3 py-3 bg-gray-900 bg-opacity-20 border ${fieldErrors.confirmPassword ? "border-red-500" : "border-amber-500 border-opacity-50"} text-white rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent outline-none transition duration-150 ease-in-out`}
                placeholder="再次输入密码"
              />
            </div>
            {fieldErrors.confirmPassword && (
              <p className="mt-1 text-xs text-red-400">
                {fieldErrors.confirmPassword}
              </p>
            )}
          </div>

          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id="terms"
                name="terms"
                type="checkbox"
                checked={termsAccepted}
                onChange={(e) => setTermsAccepted(e.target.checked)}
                className="h-4 w-4 text-amber-500 bg-gray-900 bg-opacity-50 border-amber-500 border-opacity-50 rounded focus:ring-amber-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="terms" className="text-white">
                我同意归处的
                <a
                  href="/terms-of-service"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-medium text-amber-500 hover:underline"
                >
                  服务条款
                </a>
                和
                <a
                  href="/privacy-policy"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-medium text-amber-500 hover:underline"
                >
                  隐私政策
                </a>
                。
              </label>
            </div>
          </div>
          {fieldErrors.terms && (
            <p className="mt-1 text-xs text-red-400">{fieldErrors.terms}</p>
          )}

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-gray-900 bg-amber-500 hover:bg-yellow-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-amber-500 transition duration-150 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  创建中...
                </>
              ) : (
                <>
                  <i className="fas fa-check mr-2"></i> 创建账户
                </>
              )}
            </button>
          </div>
        </form>

        <p className="mt-8 text-center text-sm text-white">
          已经有账户了?
          <Link
            to="/login"
            className="font-medium text-amber-500 hover:underline"
          >
            在此登录
          </Link>
        </p>
      </div>
    </div>
  );
};

export default RegisterPage;

// Tailwind CSS color mapping (from prototype's tailwind.config.js and body style):
// bg-app-bg-dark: 'var(--color-gray-900)'
// text-text-dark-primary: 'var(--color-gray-50)'
// bg-card-bg-dark: 'var(--color-gray-800)'
// text-serene-blue: 'var(--color-primary)'
// text-moonlight-white: 'var(--color-gray-50)'
// text-text-dark-secondary: 'var(--color-gray-500)'
// bg-input-bg-dark: 'var(--color-gray-700)'
// border-gray-600
// text-gray-400
// focus:ring-serene-blue
// focus:border-serene-blue
// hover:bg-opacity-80 (for serene-blue button)
// focus:ring-offset-app-bg-dark

// It's assumed that these Tailwind custom colors (app-bg-dark, card-bg-dark, etc.)
// are defined in the global Tailwind configuration (tailwind.config.js) of the Next.js project.
// If not, they would need to be added there, or inline styles/CSS variables would be an alternative.
// The Font Awesome classes (fas fa-*) require Font Awesome to be set up in the project.
