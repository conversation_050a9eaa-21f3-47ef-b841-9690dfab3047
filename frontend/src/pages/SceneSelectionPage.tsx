import React, { useState } from "react";
import { Link } from "react-router-dom";
// import Navbar from '../components/Navbar'; // Assuming a Navbar component exists
import Sidebar from "../components/Sidebar"; // Assuming a Sidebar component exists

interface Scene {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  tags: string[];
}

const scenesData: Scene[] = [
  {
    id: "scene1",
    name: "静谧园林",
    description: "传统中式园林，宁静祥和，适合缅怀先人。",
    imageUrl: "/img/scene_garden.jpg", // Placeholder image
    tags: ["中式", "自然", "宁静"],
  },
  {
    id: "scene2",
    name: "庄严殿堂",
    description: "宏伟的殿堂式建筑，庄严肃穆，适合大型祭奠活动。",
    imageUrl: "/img/scene_hall.jpg", // Placeholder image
    tags: ["殿堂", "庄重", "传统"],
  },
  {
    id: "scene3",
    name: "简约现代",
    description: "现代简约风格，线条流畅，适合追求简洁的用户。",
    imageUrl: "/img/scene_modern.jpg", // Placeholder image
    tags: ["现代", "简约", "清新"],
  },
  {
    id: "scene4",
    name: "海天一色",
    description: "广阔海景，心旷神怡，寄托哀思于远方。",
    imageUrl: "/img/scene_sea.jpg", // Placeholder image
    tags: ["海景", "开阔", "自然"],
  },
  {
    id: "scene5",
    name: "星空追忆",
    description: "仰望璀璨星空，思念如星光般永恒。",
    imageUrl: "/img/scene_starry.jpg", // Placeholder image
    tags: ["星空", "浪漫", "梦幻"],
  },
  {
    id: "scene6",
    name: "雪域净土",
    description: "纯洁雪山，象征灵魂的升华与宁静。",
    imageUrl: "/img/scene_snow.jpg", // Placeholder image
    tags: ["雪山", "纯洁", "神圣"],
  },
];

const SceneSelectionPage: React.FC = () => {
  const [selectedScene, setSelectedScene] = useState<string | null>(
    scenesData[0].id,
  ); // Default to first scene
  const [searchTerm, setSearchTerm] = useState("");
  const [filterTag, setFilterTag] = useState<string>("all");

  const allTags = [
    "all",
    ...new Set(scenesData.flatMap((scene) => scene.tags)),
  ];

  const filteredScenes = scenesData.filter((scene) => {
    const matchesSearchTerm =
      scene.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      scene.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesTag = filterTag === "all" || scene.tags.includes(filterTag);
    return matchesSearchTerm && matchesTag;
  });

  // 使用Tailwind原生类替代自定义按钮样式常量

  return (
    <div className="flex flex-col min-h-screen bg-gray-900 text-white">
      <div className="flex flex-1">
        <Sidebar />
        <main className="flex-1 p-6">
          <div className="mb-8">
            <h1 className="text-2xl font-bold mb-2 text-[#185a56]">
              选择3D场景
            </h1>
            <p className="text-white">
              为您的纪念空间选择一个合适的3D场景，营造庄重肃穆的氛围
            </p>
          </div>

          {/* Steps Indicator - Copied from prototype, ensure styling matches */}
          <div className="flex justify-between mb-10 px-0 md:px-10">
            <div className="step-item completed flex flex-col items-center w-1/4">
              <div className="step-circle">
                <i className="fas fa-check"></i>
              </div>
              <div className="mt-2 text-sm font-medium text-[#185a56]">
                基本信息
              </div>
            </div>
            <div className="step-item active flex flex-col items-center w-1/4">
              <div className="step-circle">2</div>
              <div className="mt-2 text-sm font-medium text-[#185a56]">
                选择场景
              </div>
            </div>
            <div className="step-item flex flex-col items-center w-1/4">
              <div className="step-circle">3</div>
              <div className="mt-2 text-sm font-medium text-gray-300">
                上传资料
              </div>
            </div>
            <div className="step-item flex flex-col items-center w-1/4">
              <div className="step-circle">4</div>
              <div className="mt-2 text-sm font-medium text-gray-300">
                完成创建
              </div>
            </div>
          </div>

          <div className="bg-gray-900 border border-[#185a56] border-opacity-30 rounded-xl shadow-md p-6 mb-6">
            {/* Filters and Search */}
            <div className="mb-6 flex flex-col md:flex-row justify-between items-center gap-4">
              <div className="relative w-full md:w-1/2 lg:w-1/3">
                <input
                  type="text"
                  placeholder="搜索场景名称或描述..."
                  className="form-input w-full px-4 py-2 border border-[#185a56] border-opacity-50 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#185a56] bg-gray-900 text-white placeholder-gray-400 pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-[#185a56]"></i>
              </div>
              <div className="flex items-center space-x-2 overflow-x-auto pb-2 md:pb-0">
                {allTags.map((tag) => (
                  <button
                    key={tag}
                    onClick={() => setFilterTag(tag)}
                    className={`px-4 py-1.5 rounded-full text-sm font-medium whitespace-nowrap transition-colors
                      ${
                        filterTag === tag
                          ? "bg-[#185a56] text-gray-900"
                          : "bg-gray-900 text-white hover:bg-[#185a56] hover:text-gray-900 hover:bg-opacity-80 border border-[#185a56] border-opacity-30"
                      }`}
                  >
                    {tag === "all" ? "全部" : tag}
                  </button>
                ))}
              </div>
            </div>

            {/* Scene Cards */}
            {filteredScenes.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredScenes.map((scene) => (
                  <div
                    key={scene.id}
                    className={`scene-card bg-gray-900 border-2 rounded-lg shadow-md overflow-hidden transition-all duration-300 ease-in-out hover:shadow-xl hover:border-[#185a56]
                      ${selectedScene === scene.id ? "border-[#185a56] ring-2 ring-[#185a56]" : "border-[#185a56] border-opacity-30"}`}
                    onClick={() => setSelectedScene(scene.id)}
                  >
                    <img
                      src={scene.imageUrl}
                      alt={scene.name}
                      className="w-full h-48 object-cover"
                    />
                    <div className="p-4">
                      <h3 className="text-lg font-semibold mb-1 text-white">
                        {scene.name}
                      </h3>
                      <p className="text-sm text-white mb-2 h-16 overflow-hidden">
                        {scene.description}
                      </p>
                      <div className="flex flex-wrap gap-2 mb-3">
                        {scene.tags.map((tag) => (
                          <span
                            key={tag}
                            className="bg-[#185a56] bg-opacity-20 text-[#185a56] text-xs font-medium px-2 py-0.5 rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                      <button
                        className={`w-full text-sm py-2 rounded-md transition-colors 
                          ${
                            selectedScene === scene.id
                              ? "bg-[#185a56] text-gray-900 cursor-default"
                              : "bg-transparent text-[#185a56] hover:bg-[#185a56] hover:text-gray-900 border border-[#185a56]"
                          }`}
                        onClick={() => setSelectedScene(scene.id)}
                        disabled={selectedScene === scene.id}
                      >
                        {selectedScene === scene.id ? "已选择" : "选择此场景"}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <i className="fas fa-search-minus text-5xl text-[#185a56] mb-4"></i>
                <p className="text-xl text-white">未找到匹配的场景</p>
                <p className="text-sm text-gray-300 mt-2">
                  请尝试调整搜索词或筛选条件。
                </p>
              </div>
            )}
          </div>

          {/* Bottom Buttons */}
          <div className="flex justify-between mt-10 border-t border-[#185a56] border-opacity-30 pt-6">
            <Link
              to="/create-memorial"
              className="bg-transparent hover:bg-amber-500/20 text-amber-500 border border-amber-500 font-medium py-2 px-4 rounded-lg transition duration-300"
            >
              <i className="fas fa-arrow-left mr-2"></i> 上一步
            </Link>
            <Link
              to="/memorial-space"
              className="bg-amber-500 hover:bg-yellow-300 text-gray-900 font-medium py-2 px-4 rounded-lg transition duration-300"
            >
              下一步：上传资料 <i className="fas fa-arrow-right ml-2"></i>
            </Link>
          </div>
        </main>
      </div>
    </div>
  );
};

export default SceneSelectionPage;

// Notes:
// - Placeholder images are used for scenes. Replace with actual image paths.
// - Assumes Font Awesome for icons.
// - Styling for steps indicator (.step-item, .step-circle) needs to be in global CSS or a CSS module if not already present.
// - The routes for '上一步' and '下一步' (e.g., /create-memorial, /memorial-space) are placeholders and should match your routing setup.
// - Ensure Sidebar and Navbar components are correctly imported and styled.
// - Dark mode compatibility is considered with dark: prefixes.
