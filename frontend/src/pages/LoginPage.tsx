import React, { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null); // For API errors
  const [fieldErrors, setFieldErrors] = useState<{
    email?: string;
    password?: string;
  }>({});
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // 处理来自注册页面的消息
    if (location.state?.message) {
      setSuccessMessage(location.state.message);
      if (location.state.email) {
        setEmail(location.state.email);
      }
      // 清除state以避免刷新时重复显示
      navigate(location.pathname, { replace: true });
    }

    // 处理查询参数
    const queryParams = new URLSearchParams(location.search);
    if (queryParams.get("registrationSuccess") === "true") {
      setSuccessMessage("注册成功！现在您可以登录了。");
      navigate(location.pathname, { replace: true }); // Clear query param
    }

    const rememberedEmail = localStorage.getItem("rememberedEmail");
    if (rememberedEmail && !email) {
      setEmail(rememberedEmail);
      setRememberMe(true);
    }
  }, [location, navigate, email]);

  const validateForm = (): boolean => {
    const newFieldErrors: { email?: string; password?: string } = {};
    let isValid = true;

    // Email validation
    if (!email) {
      newFieldErrors.email = "请输入邮箱地址。";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newFieldErrors.email = "请输入有效的邮箱地址。";
      isValid = false;
    }

    // Password validation
    if (!password) {
      newFieldErrors.password = "请输入密码。";
      isValid = false;
    } else if (password.length < 8) {
      newFieldErrors.password = "密码长度至少为8位。";
      isValid = false;
    }

    setFieldErrors(newFieldErrors);
    return isValid;
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setError(null); // Clear previous API errors
    setFieldErrors({}); // Clear previous field errors

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL;
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ login_identifier: email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || "登录失败，请检查您的凭据。");
      }

      const data = await response.json();
      console.log("登录成功:", data);
      localStorage.setItem("authToken", data.access_token);

      if (rememberMe) {
        localStorage.setItem("rememberedEmail", email);
      } else {
        localStorage.removeItem("rememberedEmail");
      }

      navigate("/dashboard");
    } catch (err: unknown) {
      const errorMessage =
        err instanceof Error ? err.message : "发生未知错误，请稍后再试。";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-gray-900 text-white flex items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md bg-gray-900 border border-primary border-opacity-30 shadow-2xl rounded-xl p-8 md:p-12">
        <div className="text-center mb-8">
          {/* 添加杏黄色装饰元素 */}
          <div className="w-16 h-1 bg-secondary mx-auto mb-4 rounded-full"></div>
          <h1 className="text-3xl font-bold text-white">登录归处</h1>
          <p className="text-gray-300 mt-2">继续您的数字传承之旅</p>
        </div>

        {successMessage && (
          <div className="mb-4 p-3 bg-secondary bg-opacity-20 text-secondary border border-secondary rounded-lg text-sm">
            {successMessage}
          </div>
        )}

        {error && (
          <div className="mb-4 p-3 bg-red-500 bg-opacity-20 text-red-400 border border-red-500 rounded-lg text-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-300 mb-1"
            >
              邮箱地址
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i className="fas fa-envelope text-gray-400"></i>
              </div>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={`w-full pl-10 pr-3 py-3 bg-gray-900 bg-opacity-20 border ${fieldErrors.email ? "border-red-500" : "border-primary border-opacity-50"} text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition duration-150 ease-in-out`}
                placeholder="<EMAIL>"
              />
            </div>
            {fieldErrors.email && (
              <p className="mt-1 text-xs text-red-400">{fieldErrors.email}</p>
            )}
          </div>

          <div>
            <div className="flex items-center justify-between mb-1">
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-300"
              >
                密码
              </label>
              <Link
                to="/forgot-password"
                className="text-sm text-primary hover:underline"
              >
                忘记密码?
              </Link>
            </div>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i className="fas fa-lock text-gray-400"></i>
              </div>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className={`w-full pl-10 pr-3 py-3 bg-gray-900 bg-opacity-20 border ${fieldErrors.password ? "border-red-500" : "border-primary border-opacity-50"} text-white rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent outline-none transition duration-150 ease-in-out`}
                placeholder="请输入密码"
              />
            </div>
            {fieldErrors.password && (
              <p className="mt-1 text-xs text-red-400">
                {fieldErrors.password}
              </p>
            )}
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="h-4 w-4 text-primary bg-gray-900 bg-opacity-50 border-primary border-opacity-50 rounded focus:ring-primary"
              />
              <label
                htmlFor="remember-me"
                className="ml-2 block text-sm text-gray-300"
              >
                记住我
              </label>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-primary transition duration-150 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  处理中...
                </>
              ) : (
                <>
                  <i className="fas fa-sign-in-alt mr-2"></i> 登录
                </>
              )}
            </button>
          </div>
        </form>

        <div className="mt-8 text-center text-sm text-gray-300 space-y-2">
          <p>
            还没有账户?{" "}
            <Link
              to="/register"
              className="font-medium text-primary hover:underline"
            >
              立即注册
            </Link>
          </p>
          <p>
            <Link
              to="/forgot-password"
              className="font-medium text-primary hover:underline"
            >
              忘记密码？
            </Link>
          </p>
        </div>

        <div className="mt-6">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-600"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-card-bg-dark text-text-dark-secondary">
                或通过以下方式登录
              </span>
            </div>
          </div>

          <div className="mt-4 grid grid-cols-1 gap-3">
            <div>
              <a
                href="#"
                className="w-full inline-flex justify-center py-2.5 px-4 border border-secondary border-opacity-50 rounded-lg shadow-sm bg-input-bg-dark text-sm font-medium text-text-dark-secondary hover:bg-secondary hover:bg-opacity-10 hover:text-secondary transition duration-150 ease-in-out"
              >
                <i className="fab fa-weixin mr-2 text-xl text-secondary"></i>{" "}
                使用微信登录
              </a>
            </div>
          </div>
        </div>

        {/* 底部杏黄色装饰条 */}
        <div className="mt-6">
          <div className="w-full h-1 bg-gradient-to-r from-primary via-secondary to-primary rounded-full"></div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
