// 纪念空间详情页面
import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useAuth } from "../hooks/useAuth";
import MemorialScene3D from "../components/MemorialScene3D";
import EnhancedTributeSystem from "../components/EnhancedTributeSystem";
import { 
  PhotoRestoreComponent, 
  PhotoEnhanceComponent, 
  PhotoRemoveBgComponent 
} from "../components/ai";

// 接口定义
interface MemorialSpace {
  id: string;
  deceased_name: string;
  deceased_gender?: string;
  birth_date?: string;
  death_date?: string;
  bio?: string;
  cover_image_url?: string;
  privacy_level: string;
  visit_count: number;
  created_at: string;
  creator_relationship_to_deceased?: string;
  scene?: {
    id: string;
    name: string;
    category: string;
    model_url: string;
    lighting_config?: any;
    camera_config?: any;
  };
  assets?: Array<{
    id: string;
    asset_type: string;
    file_url: string;
    title?: string;
    description?: string;
    thumbnail_url?: string;
  }>;
}

interface TributeRecord {
  id: string;
  tribute_type: string;
  content?: string;
  created_at: string;
  user_name?: string;
}

interface MemorialMessage {
  id: string;
  content: string;
  created_at: string;
  author_name?: string;
}

const MemorialSpaceDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();

  // 状态管理
  const [memorial, setMemorial] = useState<MemorialSpace | null>(null);
  const [, setTributes] = useState<TributeRecord[]>([]); // 暂时不使用，但保留以便后续功能
  const [messages, setMessages] = useState<MemorialMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<"3d" | "photos" | "biography" | "tributes" | "messages" | "ai-tools">("3d");
  
  // 交互状态
  const [isLeavingMessage, setIsLeavingMessage] = useState(false);
  const [newMessage, setNewMessage] = useState("");
  const [selectedOffering, setSelectedOffering] = useState<string | null>(null);

  // 获取纪念空间详情
  const fetchMemorialSpace = useCallback(async () => {
    if (!id) return;

    try {
      setIsLoading(true);
      const token = localStorage.getItem("authToken");
      
      const response = await fetch(`/api/v1/memorial-spaces/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error("获取纪念空间失败");
      }

      const data = await response.json();
      setMemorial(data);

      // 获取相关的祭拜记录和留言
      await Promise.all([
        fetchTributes(id),
        fetchMessages(id),
      ]);
    } catch (err) {
      setError(err instanceof Error ? err.message : "未知错误");
    } finally {
      setIsLoading(false);
    }
  }, [id]);

  // 获取祭拜记录
  const fetchTributes = async (memorialId: string) => {
    try {
      const token = localStorage.getItem("authToken");
      const response = await fetch(`/api/v1/memorial-spaces/${memorialId}/tributes`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTributes(data);
      }
    } catch (err) {
      console.error("获取祭拜记录失败:", err);
    }
  };

  // 获取留言
  const fetchMessages = async (memorialId: string) => {
    try {
      const token = localStorage.getItem("authToken");
      const response = await fetch(`/api/v1/memorial-spaces/${memorialId}/messages`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setMessages(data);
      }
    } catch (err) {
      console.error("获取留言失败:", err);
    }
  };

  // 处理3D场景交互
  const handle3DInteraction = useCallback(async (type: string, data?: any) => {
    if (!memorial || !user) return;

    try {
      const token = localStorage.getItem("authToken");
      
      switch (type) {
        case "candle_light":
          // 记录点蜡烛行为
          await fetch(`/api/v1/memorial-spaces/${memorial.id}/tributes`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            body: JSON.stringify({
              tribute_type: "candle",
              content: `点燃了一支蜡烛 (${data?.position || "center"})`,
            }),
          });
          
          // 刷新祭拜记录
          await fetchTributes(memorial.id);
          break;

        case "add_offering":
          // 显示供品选择
          setSelectedOffering("flower");
          break;

        case "photo_view":
          // 处理照片查看
          console.log("查看照片:", data);
          break;

        default:
          console.log("未知交互类型:", type, data);
      }
    } catch (err) {
      console.error("处理交互失败:", err);
    }
  }, [memorial, user]);

  // 提交祭拜供品
  const submitOffering = async (offeringType: string) => {
    if (!memorial || !user) return;

    try {
      const token = localStorage.getItem("authToken");
      
      await fetch(`/api/v1/memorial-spaces/${memorial.id}/tributes`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          tribute_type: "offering",
          content: `献上了${offeringType}`,
        }),
      });

      setSelectedOffering(null);
      await fetchTributes(memorial.id);
    } catch (err) {
      console.error("提交供品失败:", err);
    }
  };

  // 提交留言
  const submitMessage = async () => {
    if (!memorial || !user || !newMessage.trim()) return;

    try {
      const token = localStorage.getItem("authToken");
      
      const response = await fetch(`/api/v1/memorial-spaces/${memorial.id}/messages`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          content: newMessage.trim(),
          is_anonymous: false,
        }),
      });

      if (response.ok) {
        // 留言成功提交
        setNewMessage("");
        setIsLeavingMessage(false);
        await fetchMessages(memorial.id);
        
        // 显示成功消息
        alert("留言提交成功！");
      } else if (response.status === 202) {
        // 留言需要审核
        const result = await response.json();
        setNewMessage("");
        setIsLeavingMessage(false);
        alert(`留言已提交审核：${result.detail}`);
      } else if (response.status === 400) {
        // 留言被拒绝
        const result = await response.json();
        alert(`留言被拒绝：${result.detail}`);
      } else {
        throw new Error("提交失败");
      }
    } catch (err) {
      console.error("提交留言失败:", err);
      alert("提交留言失败，请重试");
    }
  };

  useEffect(() => {
    fetchMemorialSpace();
  }, [fetchMemorialSpace]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-white text-xl">正在加载纪念空间...</div>
      </div>
    );
  }

  if (error || !memorial) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">无法加载纪念空间</h2>
          <p className="text-gray-300 mb-6">{error || "纪念空间不存在"}</p>
          <button
            onClick={() => navigate("/dashboard")}
            className="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg"
          >
            返回首页
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* 顶部导航 */}
      <div className="bg-black bg-opacity-50 p-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <button
            onClick={() => navigate("/dashboard")}
            className="text-white hover:text-gray-300 flex items-center gap-2"
          >
            ← 返回
          </button>
          
          <div className="flex items-center gap-4">
            <span className="text-white text-sm">访问次数: {memorial.visit_count}</span>
            
            {user && (
              <button
                onClick={() => setIsLeavingMessage(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm"
              >
                留言悼念
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto p-4">
        {/* 标签页导航 - 移动端优化 */}
        <div className="mb-6">
          <div className="flex overflow-x-auto space-x-2 sm:space-x-4 border-b border-gray-700 scrollbar-hide">
            {[
              { key: "3d", label: "3D空间", shortLabel: "3D" },
              { key: "photos", label: "照片回忆", shortLabel: "照片" },
              { key: "biography", label: "生平简介", shortLabel: "生平" },
              { key: "tributes", label: "祭拜记录", shortLabel: "祭拜" },
              { key: "messages", label: "留言追思", shortLabel: "留言" },
              { key: "ai-tools", label: "AI照片工具", shortLabel: "AI工具" },
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`py-3 px-3 sm:px-6 text-xs sm:text-sm font-medium border-b-2 transition-colors whitespace-nowrap flex-shrink-0 ${
                  activeTab === tab.key
                    ? "text-blue-400 border-blue-400"
                    : "text-gray-400 border-transparent hover:text-white"
                }`}
              >
                <span className="sm:hidden">{tab.shortLabel}</span>
                <span className="hidden sm:inline">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 主要内容 */}
          <div className="lg:col-span-2">
            {activeTab === "3d" && memorial.scene && (
              <div className="bg-gray-800 rounded-lg overflow-hidden h-64 sm:h-96 lg:h-[600px]">
                <MemorialScene3D
                  sceneConfig={memorial.scene}
                  memorialData={memorial}
                  onInteraction={handle3DInteraction}
                  isInteractive={!!user}
                  performanceLevel="medium"
                />
              </div>
            )}
            
            {activeTab === "3d" && !memorial.scene && (
              <div className="bg-gray-800 rounded-lg p-6 text-center">
                <div className="text-gray-400">
                  <i className="fas fa-cube text-4xl mb-4"></i>
                  <h3 className="text-lg font-semibold text-white mb-2">暂无3D场景</h3>
                  <p className="text-sm">此纪念空间尚未设置3D场景，请联系创建者添加场景配置。</p>
                </div>
              </div>
            )}

            {activeTab === "photos" && (
              <div className="bg-gray-800 rounded-lg p-4 sm:p-6">
                <h3 className="text-white text-lg sm:text-xl font-bold mb-4">照片回忆</h3>
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4">
                  {memorial.cover_image_url && (
                    <div className="aspect-square rounded-lg overflow-hidden">
                      <img
                        src={memorial.cover_image_url}
                        alt="封面照片"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  {memorial.assets?.filter(asset => asset.asset_type === "image").map(asset => (
                    <div key={asset.id} className="aspect-square rounded-lg overflow-hidden">
                      <img
                        src={asset.file_url}
                        alt={asset.title || "纪念照片"}
                        className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === "biography" && (
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-white text-xl font-bold mb-4">生平简介</h3>
                <div className="text-gray-300 space-y-4">
                  {memorial.birth_date && memorial.death_date && (
                    <div>
                      <strong>生卒年月：</strong>
                      {memorial.birth_date} - {memorial.death_date}
                    </div>
                  )}
                  {memorial.creator_relationship_to_deceased && (
                    <div>
                      <strong>建立者关系：</strong>
                      {memorial.creator_relationship_to_deceased}
                    </div>
                  )}
                  {memorial.bio && (
                    <div>
                      <strong>生平介绍：</strong>
                      <p className="mt-2 leading-relaxed">{memorial.bio}</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === "tributes" && (
              <EnhancedTributeSystem
                memorialSpaceId={memorial.id}
                isInteractive={!!user}
                className="bg-gray-800 rounded-lg p-6"
                enableSound={true}
                enableAnimations={true}
                onTributeComplete={(type, message) => {
                  console.log(`祭拜完成: ${type}`, message);
                  // 可以在这里添加额外的处理逻辑
                }}
              />
            )}

            {activeTab === "messages" && (
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-white text-xl font-bold mb-4">留言追思</h3>
                <div className="space-y-4">
                  {messages.map(message => (
                    <div key={message.id} className="bg-gray-700 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-blue-400 font-medium">
                          {message.author_name || "匿名用户"}
                        </span>
                        <span className="text-gray-400 text-sm">
                          {new Date(message.created_at).toLocaleString()}
                        </span>
                      </div>
                      <p className="text-gray-300 leading-relaxed">{message.content}</p>
                    </div>
                  ))}
                  {messages.length === 0 && (
                    <p className="text-gray-400 text-center py-8">暂无留言</p>
                  )}
                </div>
              </div>
            )}

            {activeTab === "ai-tools" && (
              <div className="space-y-6">
                <div className="bg-gray-800 rounded-lg p-6">
                  <h3 className="text-white text-xl font-bold mb-4">🤖 AI照片工具</h3>
                  <p className="text-gray-400 mb-6">
                    使用AI技术增强和修复纪念照片，让珍贵的回忆更加清晰动人。
                  </p>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="text-white font-semibold">📸 照片修复</h4>
                      <PhotoRestoreComponent className="border-0" />
                    </div>
                    
                    <div className="space-y-4">
                      <h4 className="text-white font-semibold">🔍 照片增强</h4>
                      <PhotoEnhanceComponent className="border-0" />
                    </div>
                  </div>
                  
                  <div className="mt-6">
                    <h4 className="text-white font-semibold mb-4">✂️ 背景处理</h4>
                    <PhotoRemoveBgComponent className="border-0" />
                  </div>
                  
                  <div className="mt-6 p-4 bg-gray-700 rounded-lg">
                    <h5 className="text-yellow-400 font-medium mb-2">💡 使用提示</h5>
                    <ul className="text-gray-300 text-sm space-y-1">
                      <li>• 照片修复：适用于有损坏、划痕或模糊的老照片</li>
                      <li>• 照片增强：提高照片分辨率和清晰度，放大倍数可选择2-8倍</li>
                      <li>• 背景移除：生成透明背景，方便与纪念空间场景合成</li>
                      <li>• 处理后的照片可直接下载，也可添加到纪念空间相册中</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 侧边栏信息 */}
          <div className="space-y-6">
            {/* 基本信息卡片 */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-white text-lg font-bold mb-4">纪念信息</h3>
              <div className="space-y-3 text-sm">
                <div>
                  <span className="text-gray-400">姓名：</span>
                  <span className="text-white">{memorial.deceased_name}</span>
                </div>
                {memorial.deceased_gender && (
                  <div>
                    <span className="text-gray-400">性别：</span>
                    <span className="text-white">{memorial.deceased_gender === 'male' ? '男' : '女'}</span>
                  </div>
                )}
                <div>
                  <span className="text-gray-400">隐私设置：</span>
                  <span className="text-white">
                    {memorial.privacy_level === 'public' ? '公开' : '私密'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-400">创建时间：</span>
                  <span className="text-white">
                    {new Date(memorial.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>

            {/* 快捷操作 */}
            {user && (
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-white text-lg font-bold mb-4">祭拜操作</h3>
                <div className="space-y-3">
                  <button
                    onClick={() => handle3DInteraction("candle_light")}
                    className="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg text-sm"
                  >
                    🕯️ 点燃蜡烛
                  </button>
                  <button
                    onClick={() => setSelectedOffering("flower")}
                    className="w-full bg-pink-600 hover:bg-pink-700 text-white py-2 px-4 rounded-lg text-sm"
                  >
                    🌸 献花
                  </button>
                  <button
                    onClick={() => setIsLeavingMessage(true)}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm"
                  >
                    💬 留言
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 供品选择模态框 */}
      {selectedOffering && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-white text-lg font-bold mb-4">选择供品</h3>
            <div className="grid grid-cols-3 gap-3 mb-6">
              {["🌸 鲜花", "🍎 水果", "🕯️ 蜡烛", "🍰 糕点", "🍵 茶水", "💐 花束"].map(offering => (
                <button
                  key={offering}
                  onClick={() => submitOffering(offering)}
                  className="bg-gray-700 hover:bg-gray-600 text-white py-3 px-2 rounded-lg text-sm"
                >
                  {offering}
                </button>
              ))}
            </div>
            <button
              onClick={() => setSelectedOffering(null)}
              className="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 rounded-lg"
            >
              取消
            </button>
          </div>
        </div>
      )}

      {/* 留言模态框 */}
      {isLeavingMessage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-white text-lg font-bold mb-4">留言悼念</h3>
            <textarea
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="请输入您的留言..."
              className="w-full bg-gray-700 text-white rounded-lg p-3 min-h-[120px] resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <div className="flex gap-3 mt-4">
              <button
                onClick={submitMessage}
                disabled={!newMessage.trim()}
                className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white py-2 rounded-lg"
              >
                提交留言
              </button>
              <button
                onClick={() => {
                  setIsLeavingMessage(false);
                  setNewMessage("");
                }}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 rounded-lg"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MemorialSpaceDetailPage;