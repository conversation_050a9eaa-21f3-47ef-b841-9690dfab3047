import React, { useState } from "react";
import Button from "../components/Button";
import Card from "../components/Card";

/**
 * 组件演示页面
 * 展示使用 CSS Modules 实现的组件
 */
const ComponentDemo: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [selectedCard, setSelectedCard] = useState<number | null>(null);

  const handleButtonClick = () => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  };

  const demoCards = [
    {
      id: 1,
      title: "纪念卡片 1",
      subtitle: "永恒的回忆",
      content:
        "这是一个使用 CSS Modules 创建的卡片组件，具有完全的样式作用域隔离。",
      image:
        "https://images.unsplash.com/photo-1518623001395-125242310d0c?w=400&h=200&fit=crop",
    },
    {
      id: 2,
      title: "纪念卡片 2",
      subtitle: "温暖的思念",
      content: "每个组件的样式都是独立的，不会与其他组件产生冲突。",
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=200&fit=crop",
    },
    {
      id: 3,
      title: "纪念卡片 3",
      subtitle: "美好的记忆",
      content: "结合 Tailwind CSS 和 CSS Modules，我们获得了最佳的开发体验。",
      image:
        "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=200&fit=crop",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            CSS Modules 组件演示
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            展示使用 CSS Modules 实现样式作用域隔离的组件库
          </p>
        </div>

        {/* 按钮演示区域 */}
        <section className="mb-16">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">
            按钮组件演示
          </h2>
          <div className="bg-white rounded-lg p-8 shadow-sm border">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* 基础按钮 */}
              <div className="space-y-4">
                <h3 className="font-medium text-gray-900">基础变体</h3>
                <div className="space-y-3">
                  <Button variant="primary">主要按钮</Button>
                  <Button variant="secondary">次要按钮</Button>
                  <Button variant="outline">轮廓按钮</Button>
                  <Button variant="ghost">幽灵按钮</Button>
                  <Button variant="danger">危险按钮</Button>
                </div>
              </div>

              {/* 尺寸变体 */}
              <div className="space-y-4">
                <h3 className="font-medium text-gray-900">尺寸变体</h3>
                <div className="space-y-3">
                  <Button size="small" variant="primary">
                    小按钮
                  </Button>
                  <Button size="medium" variant="primary">
                    中按钮
                  </Button>
                  <Button size="large" variant="primary">
                    大按钮
                  </Button>
                </div>
              </div>

              {/* 特殊状态 */}
              <div className="space-y-4">
                <h3 className="font-medium text-gray-900">特殊状态</h3>
                <div className="space-y-3">
                  <Button
                    variant="primary"
                    loading={loading}
                    onClick={handleButtonClick}
                  >
                    {loading ? "加载中..." : "点击加载"}
                  </Button>
                  <Button variant="primary" disabled>
                    禁用按钮
                  </Button>
                  <Button variant="primary" fullWidth>
                    全宽按钮
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* 卡片演示区域 */}
        <section className="mb-16">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">
            卡片组件演示
          </h2>

          {/* 基础卡片变体 */}
          <div className="mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">基础变体</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card variant="default">
                <Card.Content>
                  <p>默认卡片</p>
                </Card.Content>
              </Card>

              <Card variant="elevated">
                <Card.Content>
                  <p>提升卡片</p>
                </Card.Content>
              </Card>

              <Card variant="outlined">
                <Card.Content>
                  <p>轮廓卡片</p>
                </Card.Content>
              </Card>

              <Card variant="flat">
                <Card.Content>
                  <p>扁平卡片</p>
                </Card.Content>
              </Card>
            </div>
          </div>

          {/* 复杂卡片示例 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              复杂卡片示例
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {demoCards.map((card) => (
                <Card
                  key={card.id}
                  variant="elevated"
                  clickable
                  selected={selectedCard === card.id}
                  onClick={() =>
                    setSelectedCard(selectedCard === card.id ? null : card.id)
                  }
                  className="cursor-pointer"
                >
                  <Card.Media src={card.image} alt={card.title} />

                  <Card.Header title={card.title} subtitle={card.subtitle} />

                  <Card.Content>
                    <p className="text-gray-600">{card.content}</p>
                  </Card.Content>

                  <Card.Actions>
                    <Button
                      size="small"
                      variant={selectedCard === card.id ? "primary" : "outline"}
                    >
                      {selectedCard === card.id ? "已选择" : "选择"}
                    </Button>
                    <Button size="small" variant="ghost">
                      详情
                    </Button>
                  </Card.Actions>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* 技术说明 */}
        <section className="bg-white rounded-lg p-8 shadow-sm border">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">
            技术特性
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                🎯 样式作用域隔离
              </h3>
              <ul className="space-y-2 text-gray-600">
                <li>• 每个组件的样式完全独立</li>
                <li>• 自动生成唯一的类名</li>
                <li>• 避免样式冲突和污染</li>
                <li>• 支持组件级样式覆盖</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                🚀 开发体验优化
              </h3>
              <ul className="space-y-2 text-gray-600">
                <li>• TypeScript 类型支持</li>
                <li>• 智能代码提示</li>
                <li>• 热重载支持</li>
                <li>• 构建时优化</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                🎨 Tailwind CSS 集成
              </h3>
              <ul className="space-y-2 text-gray-600">
                <li>• 使用 @apply 指令复用样式</li>
                <li>• 保持设计系统一致性</li>
                <li>• 响应式设计支持</li>
                <li>• 暗色模式兼容</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                📦 组件化架构
              </h3>
              <ul className="space-y-2 text-gray-600">
                <li>• 可复用的组件库</li>
                <li>• 一致的 API 设计</li>
                <li>• 灵活的组合方式</li>
                <li>• 易于维护和扩展</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default ComponentDemo;
