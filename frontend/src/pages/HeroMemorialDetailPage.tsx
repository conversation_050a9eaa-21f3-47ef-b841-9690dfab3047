// 英雄纪念空间详情页面
import React, { useState, useCallback, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useAuth } from "../hooks/useAuth";
import MemorialScene3D from "../components/MemorialScene3D";
import EnhancedTributeSystem from "../components/EnhancedTributeSystem";
import MessageSystem from "../components/MessageSystem";
import { 
  PhotoRestoreComponent, 
  PhotoEnhanceComponent, 
  PhotoRemoveBgComponent 
} from "../components/ai";

// 英雄数据
const heroesData: Record<string, any> = {
  'yuan-longping': {
    id: 'yuan-longping',
    name: '袁隆平',
    nameEn: 'Yuan Longping',
    lifeSpan: '1930-2021',
    title: '杂交水稻之父',
    titleEn: 'Father of Hybrid Rice',
    description: '中国农学家，被誉为"杂交水稻之父"。他的研究成果大大提高了水稻产量，解决了中国乃至世界的粮食问题，让数亿人免于饥饿。',
    detailedBio: `袁隆平院士，1930年9月7日出生于北京，是世界著名的杂交水稻育种专家，中国工程院院士。

主要成就：
• 1973年，成功培育出世界上第一个实用高产杂交水稻品种"南优2号"
• 推动中国水稻产量从每亩300公斤提高到500公斤以上
• 其研究成果在全球推广，解决了数亿人的温饱问题
• 获得国家最高科学技术奖、世界粮食奖等众多荣誉

袁隆平院士一生致力于杂交水稻研究，直到91岁高龄仍在田间工作。他的梦想是"让所有人远离饥饿"，被誉为"当代神农"。2021年5月22日，袁隆平院士在长沙逝世，全国人民深切悼念这位为人类作出巨大贡献的科学家。`,
    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
    category: 'science',
    nationality: '中国',
    scene: {
      category: 'nature',
      name: '稻田丰收',
      description: '金黄的稻田象征着袁隆平院士一生的奉献'
    },
    quotes: [
      "我有两个梦，一个是禾下乘凉梦，一个是杂交水稻覆盖全球梦。",
      "人就像种子，要做一粒好种子。",
      "我觉得人就像一粒种子。要做一粒好的种子，身体、精神、情感都要健康。"
    ]
  },
  'zhong-nanshan': {
    id: 'zhong-nanshan',
    name: '钟南山',
    nameEn: 'Zhong Nanshan',
    lifeSpan: '1936-',
    title: '抗疫英雄',
    titleEn: 'Anti-epidemic Hero',
    description: '中国工程院院士，呼吸病学专家。在2003年抗击SARS和2020年抗击新冠疫情中做出杰出贡献，被誉为"国士无双"。',
    detailedBio: `钟南山院士，1936年10月出生于南京，中国工程院院士、著名呼吸病学专家。

主要成就：
• 2003年SARS疫情期间，勇敢质疑权威，提出正确的治疗方案
• 2020年新冠疫情爆发时，84岁高龄逆行武汉，成为抗疫的定海神针
• 建立了呼吸疾病国家重点实验室
• 在慢性咳嗽、哮喘、慢阻肺等领域取得重大研究成果

钟南山院士始终坚持"医者仁心"的理念，在关键时刻总是挺身而出。他的专业精神和高尚医德激励着一代又一代医务工作者。`,
    imageUrl: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=400&fit=crop&crop=face',
    category: 'medical',
    nationality: '中国',
    scene: {
      category: 'modern',
      name: '医者仁心',
      description: '现代医院环境，象征着医学的进步与希望'
    },
    quotes: [
      "医生看的不是病，而是病人。",
      "选择医学可能是偶然，但你一旦选择了，就必须用一生的忠诚和热情去对待它。",
      "健康所系，性命相托。"
    ]
  },
  'lei-feng': {
    id: 'lei-feng',
    name: '雷锋',
    nameEn: 'Lei Feng',
    lifeSpan: '1940-1962',
    title: '助人为乐的楷模',
    titleEn: 'Model of Selflessness',
    description: '中国人民解放军战士，以其无私奉献、助人为乐的精神成为中国道德楷模。"雷锋精神"激励了几代中国人。',
    detailedBio: `雷锋，原名雷正兴，1940年12月18日出生于湖南省望城县。1962年8月15日因公殉职，年仅22岁。

主要事迹：
• 热心帮助战友和群众，做了数不清的好事
• 勤俭节约，将积蓄全部捐给灾区和需要帮助的人
• 刻苦学习，写下了近20万字的日记
• 爱岗敬业，在平凡的岗位上做出不平凡的贡献

雷锋精神的核心是全心全意为人民服务。他的名言"人的生命是有限的，可是为人民服务是无限的"激励着一代又一代中国人。`,
    imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
    category: 'military',
    nationality: '中国',
    scene: {
      category: 'chinese-traditional',
      name: '英雄纪念堂',
      description: '庄严肃穆的纪念空间'
    },
    quotes: [
      "人的生命是有限的，可是为人民服务是无限的。",
      "一滴水只有放进大海里才永远不会干涸。",
      "我愿做一颗永不生锈的螺丝钉。"
    ]
  },
  'qiu-shaoyun': {
    id: 'qiu-shaoyun',
    name: '邱少云',
    nameEn: 'Qiu Shaoyun',
    lifeSpan: '1926-1952',
    title: '抗美援朝英雄',
    titleEn: 'Korean War Hero',
    description: '中国人民志愿军战士，在朝鲜战争中为了不暴露部队位置，在烈火中坚持潜伏，直至牺牲，展现了崇高的革命精神。',
    detailedBio: `邱少云，1926年出生于重庆市铜梁县，中国人民志愿军战士。

英雄事迹：
• 1952年10月，参加朝鲜平康前线391高地战斗
• 在潜伏中被敌人燃烧弹击中，全身着火
• 为了不暴露部队，强忍剧痛，一动不动
• 直至壮烈牺牲，保证了整个战斗的胜利

邱少云烈士用生命诠释了什么是纪律，什么是忠诚。他的英勇事迹成为中国军人的楷模。`,
    imageUrl: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&h=400&fit=crop&crop=face',
    category: 'military',
    nationality: '中国',
    scene: {
      category: 'chinese-traditional',
      name: '烈士陵园',
      description: '青松翠柏，英魂永存'
    },
    quotes: [
      "纪律重于生命。",
      "为了胜利，向我开炮！"
    ]
  },
  'martin-luther-king': {
    id: 'martin-luther-king',
    name: '马丁·路德·金',
    nameEn: 'Martin Luther King Jr.',
    lifeSpan: '1929-1968',
    title: '民权运动领袖',
    titleEn: 'Civil Rights Leader',
    description: '美国民权运动领袖，以非暴力抗争争取种族平等。他的"我有一个梦想"演讲激励了全世界追求平等和正义的人们。',
    detailedBio: `马丁·路德·金，1929年1月15日出生于美国佐治亚州亚特兰大。

主要成就：
• 领导蒙哥马利公交车抵制运动
• 1963年组织华盛顿大游行，发表著名的"我有一个梦想"演讲
• 1964年获得诺贝尔和平奖，时年35岁
• 推动美国通过《民权法案》和《投票权法案》

1968年4月4日，马丁·路德·金在田纳西州孟菲斯遇刺身亡。他的非暴力抗争理念影响了全世界的民权运动。`,
    imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/05/Martin_Luther_King%2C_Jr..jpg/220px-Martin_Luther_King%2C_Jr..jpg',
    category: 'civil',
    nationality: '美国',
    scene: {
      category: 'christian',
      name: '和平教堂',
      description: '象征着信仰与正义的力量'
    },
    quotes: [
      "I have a dream that one day this nation will rise up and live out the true meaning of its creed.",
      "Injustice anywhere is a threat to justice everywhere.",
      "The time is always right to do what is right."
    ]
  },
  'nelson-mandela': {
    id: 'nelson-mandela',
    name: '纳尔逊·曼德拉',
    nameEn: 'Nelson Mandela',
    lifeSpan: '1918-2013',
    title: '反种族隔离斗士',
    titleEn: 'Anti-Apartheid Revolutionary',
    description: '南非反种族隔离革命家、政治家。历经27年监禁后，成为南非首位黑人总统，致力于种族和解与民主建设。',
    detailedBio: `纳尔逊·曼德拉，1918年7月18日出生于南非特兰斯凯。

主要成就：
• 创建非洲人国民大会青年联盟
• 因反种族隔离活动被监禁27年
• 1990年获释后推动和平过渡
• 1994年当选南非首位黑人总统
• 建立真相与和解委员会，促进种族和解

曼德拉用宽容和智慧引领南非走向民主，成为全世界和平与正义的象征。2013年12月5日在约翰内斯堡去世。`,
    imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/02/Nelson_Mandela_1994.jpg/220px-Nelson_Mandela_1994.jpg',
    category: 'civil',
    nationality: '南非',
    scene: {
      category: 'nature',
      name: '自由之地',
      description: '广袤的非洲大地，象征着自由与希望'
    },
    quotes: [
      "It always seems impossible until it's done.",
      "Education is the most powerful weapon which you can use to change the world.",
      "For to be free is not merely to cast off one's chains, but to live in a way that respects and enhances the freedom of others."
    ]
  },
  'marie-curie': {
    id: 'marie-curie',
    name: '玛丽·居里',
    nameEn: 'Marie Curie',
    lifeSpan: '1867-1934',
    title: '诺贝尔奖获得者',
    titleEn: 'Nobel Prize Winner',
    description: '波兰裔法国科学家，首位获得诺贝尔奖的女性，也是唯一在两个不同科学领域获得诺贝尔奖的人。她的研究开创了放射性理论。',
    detailedBio: `玛丽·居里，1867年11月7日出生于波兰华沙。

主要成就：
• 发现钋和镭两种放射性元素
• 1903年获诺贝尔物理学奖（与丈夫皮埃尔·居里共同获得）
• 1911年获诺贝尔化学奖
• 创立居里研究所
• 第一次世界大战期间，推动X射线在医疗中的应用

居里夫人不仅是杰出的科学家，更是女性追求科学事业的先驱。1934年7月4日因长期接触放射性物质导致的恶性贫血去世。`,
    imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/7e/Marie_Curie_c1920.jpg/220px-Marie_Curie_c1920.jpg',
    category: 'science',
    nationality: '波兰/法国',
    scene: {
      category: 'modern',
      name: '科学殿堂',
      description: '实验室环境，象征着科学探索的精神'
    },
    quotes: [
      "Nothing in life is to be feared, it is only to be understood.",
      "Be less curious about people and more curious about ideas.",
      "Life is not easy for any of us. But what of that? We must have perseverance."
    ]
  },
  'mahatma-gandhi': {
    id: 'mahatma-gandhi',
    name: '圣雄甘地',
    nameEn: 'Mahatma Gandhi',
    lifeSpan: '1869-1948',
    title: '非暴力抵抗先驱',
    titleEn: 'Pioneer of Nonviolent Resistance',
    description: '印度独立运动领袖，通过非暴力不合作运动领导印度获得独立。他的理念影响了全球的民权和自由运动。',
    detailedBio: `莫罕达斯·卡拉姆昌德·甘地，1869年10月2日出生于印度波尔班达尔。

主要成就：
• 创立"非暴力不合作"运动理念
• 领导印度独立运动
• 多次绝食抗议，推动社会改革
• 倡导宗教和谐与种姓平等
• 影响了全世界的民权运动

1948年1月30日，甘地在新德里被刺杀。他的非暴力理念成为世界和平运动的重要思想基础。`,
    imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/7a/Mahatma-Gandhi%2C_studio%2C_1931.jpg/220px-Mahatma-Gandhi%2C_studio%2C_1931.jpg',
    category: 'civil',
    nationality: '印度',
    scene: {
      category: 'buddhist',
      name: '和平圣地',
      description: '宁静祥和的精神空间'
    },
    quotes: [
      "Be the change you wish to see in the world.",
      "An eye for an eye will only make the whole world blind.",
      "The weak can never forgive. Forgiveness is the attribute of the strong."
    ]
  }
};

// 默认场景配置
const defaultSceneConfigs: Record<string, any> = {
  'nature': {
    id: 'nature-scene',
    name: '自然生态环境',
    category: 'nature',
    model_url: '/models/nature_scene.glb',
    lighting_config: {
      ambientIntensity: 0.7,
      directionalIntensity: 0.8
    },
    camera_config: {
      position: [0, 5, 10],
      target: [0, 0, 0]
    }
  },
  'modern': {
    id: 'modern-scene',
    name: '现代纪念空间',
    category: 'modern',
    model_url: '/models/modern_memorial.glb',
    lighting_config: {
      ambientIntensity: 0.8,
      directionalIntensity: 0.6
    }
  },
  'chinese-traditional': {
    id: 'chinese-temple-scene',
    name: '中国传统祠堂',
    category: 'chinese-traditional',
    model_url: '/models/chinese_temple.glb',
    lighting_config: {
      ambientIntensity: 0.6,
      directionalIntensity: 0.7
    }
  },
  'christian': {
    id: 'christian-church-scene',
    name: '基督教堂',
    category: 'christian',
    model_url: '/models/christian_church.glb',
    lighting_config: {
      ambientIntensity: 0.5,
      directionalIntensity: 0.8
    }
  },
  'buddhist': {
    id: 'buddhist-temple-scene',
    name: '佛教寺庙',
    category: 'buddhist',
    model_url: '/models/buddhist_temple.glb',
    lighting_config: {
      ambientIntensity: 0.6,
      directionalIntensity: 0.7
    }
  }
};

const HeroMemorialDetailPage: React.FC = () => {
  const { heroId } = useParams<{ heroId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();

  // 获取英雄数据
  const hero = heroId ? heroesData[heroId] : null;

  // 状态管理
  const [activeTab, setActiveTab] = useState<"3d" | "biography" | "quotes" | "tributes" | "messages" | "ai-tools">("3d");
  const [isLeavingMessage, setIsLeavingMessage] = useState(false);
  const [newMessage, setNewMessage] = useState("");
  const [visitCount] = useState(Math.floor(Math.random() * 10000) + 5000); // 模拟访问量
  
  // 获取场景配置
  const sceneConfig = hero?.scene ? defaultSceneConfigs[hero.scene.category] : null;

  // 处理3D场景交互
  const handle3DInteraction = useCallback(async (type: string, data?: any) => {
    console.log("3D交互:", type, data);
    // 英雄纪念空间的交互可以记录到本地或发送到服务器
  }, []);

  // 提交留言
  const submitMessage = () => {
    if (!newMessage.trim()) return;
    
    console.log("提交留言:", newMessage);
    alert("感谢您的留言！");
    setNewMessage("");
    setIsLeavingMessage(false);
  };

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  if (!hero) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">英雄不存在</h2>
          <button
            onClick={() => navigate("/heroes")}
            className="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg"
          >
            返回英雄纪念馆
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* 顶部导航 */}
      <div className="bg-black bg-opacity-50 p-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <button
            onClick={() => navigate("/heroes")}
            className="text-white hover:text-gray-300 flex items-center gap-2"
          >
            ← 返回英雄纪念馆
          </button>
          
          <div className="flex items-center gap-4">
            <span className="text-white text-sm">访问次数: {visitCount.toLocaleString()}</span>
            
            {user && (
              <button
                onClick={() => setIsLeavingMessage(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm"
              >
                留言致敬
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 英雄信息头部 */}
      <div className="bg-gradient-to-b from-gray-800 to-gray-900 py-8">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center gap-8">
            <div className="w-32 h-32 md:w-48 md:h-48 rounded-full overflow-hidden bg-gray-700">
              <img
                src={hero.imageUrl}
                alt={hero.name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const img = e.target as HTMLImageElement;
                  // Prevent infinite loop by only setting fallback once
                  if (!img.src.includes('data:image')) {
                    const svgContent = `
                      <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
                        <rect width="200" height="200" fill="#f3f4f6"/>
                        <text x="100" y="100" font-family="Arial, sans-serif" font-size="18" fill="#6b7280" text-anchor="middle" dominant-baseline="middle">
                          ${hero.name}
                        </text>
                      </svg>
                    `;
                    // Use URL encoding instead of btoa to handle Chinese characters
                    img.src = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`;
                  }
                }}
              />
            </div>
            <div className="text-center md:text-left">
              <h1 className="text-4xl font-bold text-white mb-2">{hero.name}</h1>
              {hero.nameEn && <p className="text-2xl text-gray-300 mb-2">{hero.nameEn}</p>}
              <p className="text-xl text-gray-400 mb-2">{hero.lifeSpan}</p>
              <p className="text-2xl text-yellow-400 font-semibold mb-4">{hero.title}</p>
              <div className="flex flex-wrap gap-2 justify-center md:justify-start">
                <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm">
                  {hero.nationality}
                </span>
                <span className="bg-green-600 text-white px-3 py-1 rounded-full text-sm">
                  {hero.category === 'science' ? '科学' : 
                   hero.category === 'medical' ? '医学' :
                   hero.category === 'military' ? '军事' :
                   hero.category === 'civil' ? '民权' : '其他'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto p-4">
        {/* 标签页导航 */}
        <div className="mb-6">
          <div className="flex overflow-x-auto space-x-2 sm:space-x-4 border-b border-gray-700 scrollbar-hide">
            {[
              { key: "3d", label: "3D纪念空间", shortLabel: "3D空间" },
              { key: "biography", label: "生平事迹", shortLabel: "生平" },
              { key: "quotes", label: "名言语录", shortLabel: "语录" },
              { key: "tributes", label: "祭拜致敬", shortLabel: "祭拜" },
              { key: "messages", label: "留言追思", shortLabel: "留言" },
              { key: "ai-tools", label: "AI照片工具", shortLabel: "AI工具" },
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`py-3 px-3 sm:px-6 text-xs sm:text-sm font-medium border-b-2 transition-colors whitespace-nowrap flex-shrink-0 ${
                  activeTab === tab.key
                    ? "text-blue-400 border-blue-400"
                    : "text-gray-400 border-transparent hover:text-white"
                }`}
              >
                <span className="sm:hidden">{tab.shortLabel}</span>
                <span className="hidden sm:inline">{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 主要内容 */}
          <div className="lg:col-span-2">
            {activeTab === "3d" && sceneConfig && (
              <div className="bg-gray-800 rounded-lg overflow-hidden h-64 sm:h-96 lg:h-[600px]">
                <MemorialScene3D
                  sceneConfig={sceneConfig}
                  memorialData={{
                    id: hero.id,
                    deceased_name: hero.name,
                    bio: hero.description,
                    cover_image_url: hero.imageUrl
                  }}
                  onInteraction={handle3DInteraction}
                  isInteractive={!!user}
                  performanceLevel="medium"
                />
              </div>
            )}

            {activeTab === "biography" && (
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-white text-xl font-bold mb-4">生平事迹</h3>
                <div className="text-gray-300 space-y-4 whitespace-pre-line leading-relaxed">
                  {hero.detailedBio}
                </div>
              </div>
            )}

            {activeTab === "quotes" && (
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-white text-xl font-bold mb-4">名言语录</h3>
                <div className="space-y-4">
                  {hero.quotes.map((quote: string, index: number) => (
                    <div key={index} className="bg-gray-700 rounded-lg p-4 border-l-4 border-yellow-400">
                      <p className="text-gray-300 text-lg italic">"{quote}"</p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === "tributes" && (
              <EnhancedTributeSystem
                memorialSpaceId={hero.id}
                isInteractive={!!user}
                className="bg-gray-800 rounded-lg p-6"
                enableSound={true}
                enableAnimations={true}
                onTributeComplete={(type, message) => {
                  console.log(`祭拜完成: ${type}`, message);
                }}
              />
            )}

            {activeTab === "messages" && (
              <div className="bg-gray-800 rounded-lg p-6">
                <MessageSystem
                  memorialSpaceId={hero.id}
                />
              </div>
            )}

            {activeTab === "ai-tools" && (
              <div className="space-y-6">
                <div className="bg-gray-800 rounded-lg p-6">
                  <h3 className="text-white text-xl font-bold mb-4">🤖 AI照片工具</h3>
                  <p className="text-gray-400 mb-6">
                    使用AI技术处理英雄的历史照片，让珍贵的影像更加清晰。
                  </p>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h4 className="text-white font-semibold">📸 照片修复</h4>
                      <PhotoRestoreComponent className="border-0" />
                    </div>
                    
                    <div className="space-y-4">
                      <h4 className="text-white font-semibold">🔍 照片增强</h4>
                      <PhotoEnhanceComponent className="border-0" />
                    </div>
                  </div>
                  
                  <div className="mt-6">
                    <h4 className="text-white font-semibold mb-4">✂️ 背景处理</h4>
                    <PhotoRemoveBgComponent className="border-0" />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 侧边栏信息 */}
          <div className="space-y-6">
            {/* 英雄简介卡片 */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-white text-lg font-bold mb-4">英雄信息</h3>
              <div className="space-y-3 text-sm">
                <div>
                  <span className="text-gray-400">姓名：</span>
                  <span className="text-white">{hero.name}</span>
                </div>
                <div>
                  <span className="text-gray-400">生卒：</span>
                  <span className="text-white">{hero.lifeSpan}</span>
                </div>
                <div>
                  <span className="text-gray-400">称号：</span>
                  <span className="text-white">{hero.title}</span>
                </div>
                <div>
                  <span className="text-gray-400">国籍：</span>
                  <span className="text-white">{hero.nationality}</span>
                </div>
                <div className="pt-3 border-t border-gray-700">
                  <p className="text-gray-300 text-sm leading-relaxed">
                    {hero.description}
                  </p>
                </div>
              </div>
            </div>

            {/* 快捷操作 */}
            {user && (
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-white text-lg font-bold mb-4">致敬英雄</h3>
                <div className="space-y-3">
                  <button
                    onClick={() => setActiveTab("tributes")}
                    className="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-lg text-sm"
                  >
                    🕯️ 点燃蜡烛
                  </button>
                  <button
                    onClick={() => setActiveTab("tributes")}
                    className="w-full bg-pink-600 hover:bg-pink-700 text-white py-2 px-4 rounded-lg text-sm"
                  >
                    🌸 献花致敬
                  </button>
                  <button
                    onClick={() => setIsLeavingMessage(true)}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm"
                  >
                    💬 留言追思
                  </button>
                </div>
              </div>
            )}

            {/* 相关链接 */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h3 className="text-white text-lg font-bold mb-4">了解更多</h3>
              <div className="space-y-2">
                <a
                  href={`https://zh.wikipedia.org/wiki/${encodeURIComponent(hero.name)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block text-blue-400 hover:text-blue-300 text-sm"
                >
                  📖 维基百科
                </a>
                <a
                  href={`https://www.baidu.com/s?wd=${encodeURIComponent(hero.name)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block text-blue-400 hover:text-blue-300 text-sm"
                >
                  🔍 百度搜索
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 留言模态框 */}
      {isLeavingMessage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-white text-lg font-bold mb-4">向{hero.name}致敬</h3>
            <textarea
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="请输入您的留言..."
              className="w-full bg-gray-700 text-white rounded-lg p-3 min-h-[120px] resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <div className="flex gap-3 mt-4">
              <button
                onClick={submitMessage}
                disabled={!newMessage.trim()}
                className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white py-2 rounded-lg"
              >
                提交留言
              </button>
              <button
                onClick={() => {
                  setIsLeavingMessage(false);
                  setNewMessage("");
                }}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 rounded-lg"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HeroMemorialDetailPage;