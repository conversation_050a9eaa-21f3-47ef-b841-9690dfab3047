import React, { useState } from "react";
import { Link } from "react-router-dom";
// import Navbar from '../components/Navbar'; // Assuming a Navbar component exists
// import Sidebar from '../components/Sidebar'; // Assuming a Sidebar component exists

interface Product {
  id: string;
  name: string;
  description: string;
  price: string;
  imageUrl: string;
  category: string;
  soldCount?: number;
}

const productsData: Product[] = [
  {
    id: "prod1",
    name: "素雅白菊",
    description: "寄托哀思，纯洁高雅",
    price: "¥28.00",
    imageUrl:
      "https://images.unsplash.com/photo-1541160097335-7d8c00d15f99?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8d2hpdGUlMjBjaHJ5c2FudGhlbXVtfGVufDB8fDB8fHww&auto=format&fit=crop&w=300&q=60",
    category: "flowers",
    soldCount: 120,
  },
  {
    id: "prod2",
    name: "电子长明灯",
    description: "环保安全，心意永续",
    price: "¥45.00",
    imageUrl:
      "https://images.unsplash.com/photo-1604706511593-3994790f0e79?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8ZWxlY3Ryb25pYyUyMGNhbmRsZXxlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=300&q=60",
    category: "candles",
    soldCount: 80,
  },
  {
    id: "prod3",
    name: "时令鲜果篮",
    description: "新鲜饱满，敬奉佳品",
    price: "¥88.00",
    imageUrl:
      "https://images.unsplash.com/photo-1557844352-761f2565b576?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8ZnJ1aXQlMjBvZmZlcmluZ3xlbnwwfHwwfHx8MA%3D%3D&auto=format&fit=crop&w=300&q=60",
    category: "offerings",
    soldCount: 200,
  },
  {
    id: "prod4",
    name: "环保纸钱元宝",
    description: "传统习俗，现代环保",
    price: "¥18.00",
    imageUrl:
      "https://images.unsplash.com/photo-1599791090080-0003b7ac1c47?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8am9zcyUyMHBhcGVyfGVufDB8fDB8fHww&auto=format&fit=crop&w=300&q=60",
    category: "candles", // Or a new category like 'jossPaper'
    soldCount: 500,
  },
  // Add more products as needed
];

const StorePage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const productsPerPage = 8; // Adjust as needed

  const categories = [
    { value: "all", label: "所有分类" },
    { value: "flowers", label: "鲜花花束" },
    { value: "candles", label: "香烛纸钱" },
    { value: "offerings", label: "供品果点" },
    { value: "virtual", label: "虚拟祭品" },
  ];

  const filteredProducts = productsData
    .filter(
      (product) =>
        selectedCategory === "all" || product.category === selectedCategory,
    )
    .filter(
      (product) =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase()),
    );

  const indexOfLastProduct = currentPage * productsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage;
  const currentProducts = filteredProducts.slice(
    indexOfFirstProduct,
    indexOfLastProduct,
  );
  const totalPages = Math.ceil(filteredProducts.length / productsPerPage);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  const ProductCard: React.FC<Product> = ({
    name,
    description,
    price,
    imageUrl,
    soldCount,
  }) => (
    <div className="product-card bg-gray-900 rounded-lg shadow-lg overflow-hidden border border-amber-500 border-opacity-30 transition-all duration-300 hover:shadow-xl hover:shadow-amber-500/20 hover:-translate-y-1">
      <img src={imageUrl} alt={name} className="w-full h-48 object-cover" />
      <div className="p-4">
        <h3
          className="text-lg font-semibold text-white mb-1 truncate"
          title={name}
        >
          {name}
        </h3>
        <p className="text-sm text-gray-300 mb-2 h-10 overflow-hidden">
          {description}
        </p>
        <div className="flex justify-between items-center mb-3">
          <span className="text-xl font-bold text-amber-500">{price}</span>
          {soldCount && (
            <span className="text-xs text-gray-400">已售 {soldCount}+</span>
          )}
        </div>
        <button className="w-full bg-amber-500 hover:bg-yellow-300 text-gray-900 font-medium py-2 px-4 rounded-md text-sm transition duration-300">
          <i className="fas fa-cart-plus mr-2"></i>加入购物车
        </button>
      </div>
    </div>
  );

  // 使用Tailwind原生类替代自定义按钮样式常量

  return (
    <div className="flex flex-col min-h-screen bg-gray-900 text-white">
      <header className="bg-gray-900 border-b border-amber-500 border-opacity-30 shadow-md sticky top-0 z-50">
        <div className="container mx-auto px-4 py-3 flex justify-between items-center">
          <div className="flex items-center">
            <i className="fas fa-store text-amber-500 text-2xl mr-3"></i>
            <span className="text-xl font-bold text-amber-500">
              归处祭品商城
            </span>
          </div>
          <div>
            <Link
              to="/dashboard"
              className="bg-transparent hover:bg-amber-500/20 text-amber-500 border border-amber-500 font-medium py-2 px-4 rounded-lg transition duration-300 text-sm"
            >
              返回仪表盘
            </Link>
          </div>
        </div>
      </header>

      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-amber-500 mb-4">
            归处祭品商城
          </h1>
          <p className="text-lg text-gray-300">
            为逝者选购一份心意，寄托您的哀思。
          </p>
        </div>

        <div className="mb-8 p-4 bg-gray-900 border border-amber-500 border-opacity-30 rounded-lg shadow-lg">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center space-x-2">
              <label
                htmlFor="category-filter"
                className="text-sm font-medium text-gray-300"
              >
                分类:
              </label>
              <select
                id="category-filter"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="rounded-md border-amber-500 border-opacity-50 bg-gray-900 text-white shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-500 focus:ring-opacity-50 text-sm py-1.5 px-2"
              >
                {categories.map((cat) => (
                  <option key={cat.value} value={cat.value}>
                    {cat.label}
                  </option>
                ))}
              </select>
            </div>
            <div className="relative w-full sm:w-auto max-w-xs">
              <input
                type="text"
                placeholder="搜索商品..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full rounded-md border-amber-500 border-opacity-50 bg-gray-900 text-white placeholder-gray-400 shadow-sm focus:border-amber-500 focus:ring focus:ring-amber-500 focus:ring-opacity-50 text-sm py-1.5 px-3 pr-10"
              />
              <i className="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-amber-500"></i>
            </div>
          </div>
        </div>

        {currentProducts.length > 0 ? (
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {currentProducts.map((product) => (
              <ProductCard key={product.id} {...product} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <i className="fas fa-store-slash text-5xl text-amber-500 mb-4"></i>
            <p className="text-xl text-gray-300">未找到相关商品</p>
            <p className="text-sm text-gray-400 mt-2">
              请尝试调整搜索词或筛选条件。
            </p>
          </div>
        )}

        {totalPages > 1 && (
          <div className="mt-12 flex justify-center">
            <nav aria-label="Page navigation">
              <ul className="inline-flex items-center -space-x-px">
                <li>
                  <button
                    onClick={() => paginate(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="py-2 px-3 ml-0 leading-tight text-gray-300 bg-gray-900 rounded-l-lg border border-amber-500 border-opacity-50 hover:bg-amber-500 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    上一页
                  </button>
                </li>
                {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                  (number) => (
                    <li key={number}>
                      <button
                        onClick={() => paginate(number)}
                        className={`py-2 px-3 leading-tight border border-amber-500 border-opacity-50 ${
                          currentPage === number
                            ? "bg-amber-500 text-gray-900"
                            : "text-gray-300 bg-gray-900 hover:bg-amber-500 hover:text-gray-900"
                        }`}
                      >
                        {number}
                      </button>
                    </li>
                  ),
                )}
                <li>
                  <button
                    onClick={() => paginate(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="py-2 px-3 leading-tight text-gray-300 bg-gray-900 rounded-r-lg border border-amber-500 border-opacity-50 hover:bg-amber-500 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    下一页
                  </button>
                </li>
              </ul>
            </nav>
          </div>
        )}
      </main>

      <footer className="bg-gray-100 dark:bg-gray-900/50 text-gray-600 dark:text-gray-400 py-8 text-center mt-auto">
        <div className="container mx-auto">
          <p>
            &copy; {new Date().getFullYear()} 归处在线祭祀平台. 保留所有权利.
          </p>
          <p className="text-sm mt-2">用心连接思念，科技传承记忆。</p>
        </div>
      </footer>
    </div>
  );
};

export default StorePage;

// Notes:
// - Assumes Font Awesome for icons.
// - Placeholder data for products. Real data would come from an API.
// - Cart functionality is not implemented.
// - Navbar/Sidebar are commented out, assuming they might be part of a global layout.
// - Dark mode compatibility is considered with dark: prefixes.
// - Pagination is basic; more advanced features like '...' for many pages could be added.
