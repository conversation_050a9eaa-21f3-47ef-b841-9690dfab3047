import React, { useState, ChangeEvent, useRef } from "react";
import { Link } from "react-router-dom";
// import Navbar from '../components/Navbar'; // Assuming a Navbar component exists
// import Sidebar from '../components/Sidebar'; // Assuming a Sidebar component exists

const AIRepairPage: React.FC = () => {
  const [originalImage, setOriginalImage] = useState<string | null>(null);
  const [restoredImage, setRestoredImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [fileName, setFileName] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFileName(file.name);
      const reader = new FileReader();
      reader.onload = (e) => {
        setOriginalImage(e.target?.result as string);
        // Simulate AI processing
        setIsProcessing(true);
        setRestoredImage(null); // Clear previous restored image
        setTimeout(() => {
          // In a real app, this would be the result from an AI service
          // For demo, we'll use a placeholder or slightly modified original
          setRestoredImage((e.target?.result as string) + "?repaired=true"); // Placeholder
          setIsProcessing(false);
        }, 3000);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUploadAnother = () => {
    setOriginalImage(null);
    setRestoredImage(null);
    setIsProcessing(false);
    setFileName(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = ""; // Reset file input
    }
  };

  // 已删除 uploadAreaClasses 样式常量，直接使用 Tailwind 原生类

  return (
    <div className="flex flex-col min-h-screen bg-gray-900 text-white">
      {/* Header similar to prototype */}
      <header className="bg-gray-900 shadow-sm sticky top-0 z-50 border-b border-[#185a56] border-opacity-50">
        <div className="container mx-auto px-4 py-3 flex justify-between items-center">
          <div className="flex items-center">
            <i className="fas fa-microchip text-[#185a56] text-2xl mr-3"></i>
            <span className="text-xl font-bold text-white">归处AI照片修复</span>
          </div>
          <div>
            <Link
              to="/dashboard"
              className="bg-transparent hover:bg-[#185a56] hover:bg-opacity-20 text-[#185a56] border border-[#185a56] border-opacity-50 font-semibold py-2 px-4 rounded-lg transition duration-300 focus:outline-none focus:ring-2 focus:ring-[#185a56] focus:ring-opacity-50 text-sm"
            >
              返回仪表盘
            </Link>
          </div>
        </div>
      </header>

      <main className="flex-grow container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
            AI 智能照片修复
          </h1>
          <p className="text-lg text-white">
            上传您珍贵的老照片，让AI赋予它们新生。
          </p>
        </div>

        <div className="max-w-3xl mx-auto bg-gray-900 p-6 sm:p-8 rounded-lg shadow-xl border border-[#185a56] border-opacity-30">
          {!originalImage && !isProcessing && (
            <div id="upload-section">
              <label
                htmlFor="photo-upload"
                className="border-2 border-dashed border-[#185a56] rounded-lg p-6 text-center bg-gray-800 hover:bg-gray-700 transition-colors"
              >
                <i className="fas fa-cloud-upload-alt text-4xl text-[#185a56] mb-4"></i>
                <p className="text-white font-semibold">
                  点击此处或拖拽照片到这里上传
                </p>
                <p className="text-sm text-gray-300 mt-1">
                  支持 JPG, PNG, WEBP 格式，最大 10MB
                </p>
              </label>
              <input
                type="file"
                id="photo-upload"
                ref={fileInputRef}
                className="hidden"
                accept="image/jpeg, image/png, image/webp"
                onChange={handleImageUpload}
              />
            </div>
          )}

          {isProcessing && (
            <div id="processing-section" className="text-center py-10">
              <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-[#185a56] mx-auto mb-4"></div>
              <p className="text-xl font-semibold text-white">
                正在智能修复中，请稍候...
              </p>
              {fileName && (
                <p className="text-sm text-gray-300 mt-2">
                  处理文件: {fileName}
                </p>
              )}
            </div>
          )}

          {originalImage && restoredImage && !isProcessing && (
            <div id="result-section">
              <h2 className="text-2xl font-semibold text-white mb-6 text-center">
                修复效果对比
              </h2>
              <div className="grid md:grid-cols-2 gap-6 items-start">
                <div>
                  <h3 className="text-lg font-medium text-white mb-2 text-center">
                    原始照片
                  </h3>
                  <img
                    src={originalImage}
                    alt="原始照片"
                    className="preview-image w-full rounded-lg shadow-md border border-[#185a56] border-opacity-50 max-h-96 object-contain"
                  />
                </div>
                <div>
                  <h3 className="text-lg font-medium text-white mb-2 text-center">
                    修复后照片
                  </h3>
                  <img
                    src={restoredImage}
                    alt="修复后照片"
                    className="preview-image w-full rounded-lg shadow-md border border-[#185a56] border-opacity-50 max-h-96 object-contain"
                  />
                </div>
              </div>
              <div className="mt-8 text-center space-y-3 sm:space-y-0 sm:space-x-4">
                <a
                  href={restoredImage}
                  download={`repaired-${fileName || "photo.jpg"}`}
                  className="bg-[#185a56] hover:bg-[#FFA631] text-gray-900 font-semibold py-2 px-4 rounded-lg transition duration-300 inline-block w-full sm:w-auto"
                >
                  <i className="fas fa-download mr-2"></i>下载修复照片
                </a>
                <button
                  onClick={handleUploadAnother}
                  className="bg-transparent hover:bg-[#185a56] hover:bg-opacity-20 text-[#185a56] border border-[#185a56] border-opacity-50 font-semibold py-2 px-4 rounded-lg transition duration-300 focus:outline-none focus:ring-2 focus:ring-[#185a56] focus:ring-opacity-50 inline-block w-full sm:w-auto"
                >
                  <i className="fas fa-redo mr-2"></i>上传另一张
                </button>
              </div>
            </div>
          )}
        </div>
      </main>

      <footer className="bg-gray-900 text-white py-8 text-center mt-auto border-t border-[#185a56] border-opacity-50">
        <div className="container mx-auto">
          <p>
            &copy; {new Date().getFullYear()} 归处在线祭祀平台. 保留所有权利.
          </p>
          <p className="text-sm mt-2">用心连接思念，科技传承记忆。</p>
        </div>
      </footer>
    </div>
  );
};

export default AIRepairPage;

// Notes:
// - Assumes Font Awesome for icons.
// - This component simulates the AI repair process with a timeout.
// - Actual AI integration would involve API calls to a backend service.
// - Navbar/Sidebar are commented out, assuming they might be part of a global layout.
//   If they are page-specific, uncomment and import them.
// - Dark mode compatibility is considered with dark: prefixes.
