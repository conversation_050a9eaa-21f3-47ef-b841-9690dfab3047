import React, { useContext, useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import AuthContext from '../contexts/AuthContext';
import AdminDashboard from '../components/AdminDashboard';

const AdminPage: React.FC = () => {
  const authContext = useContext(AuthContext);
  const [isAdminVerified, setIsAdminVerified] = useState<boolean | null>(null);

  if (!authContext) {
    return <Navigate to="/login" replace />;
  }

  const { user, isAuthenticated } = authContext;

  useEffect(() => {
    // 验证管理员权限
    const verifyAdminRole = async () => {
      if (!isAuthenticated || !user) {
        setIsAdminVerified(false);
        return;
      }

      try {
        const token = localStorage.getItem('authToken');
        const response = await fetch('/api/v1/admin/verify', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const result = await response.json();
          setIsAdminVerified(result.is_admin || false);
        } else {
          setIsAdminVerified(false);
        }
      } catch (error) {
        console.error('管理员权限验证失败:', error);
        setIsAdminVerified(false);
      }
    };

    verifyAdminRole();
  }, [isAuthenticated, user]);

  // 如果未登录，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // 如果正在验证管理员权限，显示加载状态
  if (isAdminVerified === null) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">正在验证管理员权限...</p>
        </div>
      </div>
    );
  }

  // 如果不是管理员，显示权限不足页面
  if (!isAdminVerified) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="text-red-500 text-6xl mb-4">
              <i className="fas fa-shield-alt"></i>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">权限不足</h1>
            <p className="text-gray-600 mb-6">
              抱歉，您没有访问管理后台的权限。如果您认为这是错误，请联系系统管理员。
            </p>
            <div className="space-y-3">
              <button
                onClick={() => window.history.back()}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
              >
                返回上一页
              </button>
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors"
              >
                返回首页
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 如果是管理员，显示管理后台
  return <AdminDashboard />;
};

export default AdminPage;