import React from "react";
import { Link } from "react-router-dom";

const NotFoundPage: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-900 text-white p-4">
      <div className="text-center">
        <h1 className="text-6xl font-bold text-amber-500 mb-4">404</h1>
        <h2 className="text-3xl font-semibold text-white mb-3">页面未找到</h2>
        <p className="text-gray-300 mb-8 max-w-md">
          抱歉，您访问的页面不存在。它可能已被移动、删除或您输入的地址有误。
        </p>
        <Link
          to="/"
          className="px-6 py-3 bg-amber-500 text-gray-900 font-medium rounded-lg hover:bg-yellow-300 transition duration-150 ease-in-out"
        >
          返回首页
        </Link>
      </div>
    </div>
  );
};

export default NotFoundPage;
