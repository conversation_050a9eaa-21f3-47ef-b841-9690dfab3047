import React, { useState } from "react";
import { Container, Row, Col, Nav, Tab, Alert } from "react-bootstrap";
import { useTranslation } from "react-i18next";
import {
  PhotoRestoreComponent,
  PhotoColorizeComponent,
  PhotoTo3DComponent,
  VoiceCloneComponent,
  PhotoEnhanceComponent,
  PhotoRemoveBgComponent,
} from "../components/ai";

const AIFeaturesPage: React.FC = () => {
  const { t } = useTranslation();
  const [activeKey, setActiveKey] = useState("photo-restore");

  return (
    <Container className="py-5 text-white">
      <h1 className="mb-4 text-3xl font-semibold text-white">
        {t("ai.pageTitle")}
      </h1>
      <p className="lead mb-5 text-white">{t("ai.pageDescription")}</p>

      <Alert
        variant="info"
        className="mb-4 bg-[#185a56] text-gray-900 border-[#185a56]"
      >
        <Alert.Heading>{t("ai.betaNotice.title")}</Alert.Heading>
        <p>{t("ai.betaNotice.description")}</p>
      </Alert>

      <Tab.Container
        id="ai-features-tabs"
        activeKey={activeKey}
        onSelect={(key) => key && setActiveKey(key)}
      >
        <Row>
          <Col md={3} className="mb-4">
            <Nav variant="pills" className="flex-column">
              <Nav.Item>
                <Nav.Link
                  eventKey="photo-restore"
                  className={
                    activeKey === "photo-restore"
                      ? "bg-[#185a56] text-gray-900 rounded-md"
                      : "text-white hover:bg-[#185a56] hover:text-gray-900 hover:bg-opacity-80 rounded-md"
                  }
                >
                  {t("ai.tabs.photoRestore")}
                </Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link
                  eventKey="photo-colorize"
                  className={
                    activeKey === "photo-colorize"
                      ? "bg-[#185a56] text-gray-900 rounded-md"
                      : "text-white hover:bg-[#185a56] hover:text-gray-900 hover:bg-opacity-80 rounded-md"
                  }
                >
                  {t("ai.tabs.photoColorize")}
                </Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link
                  eventKey="photo-to-3d"
                  className={
                    activeKey === "photo-to-3d"
                      ? "bg-[#185a56] text-gray-900 rounded-md"
                      : "text-white hover:bg-[#185a56] hover:text-gray-900 hover:bg-opacity-80 rounded-md"
                  }
                >
                  {t("ai.tabs.photoTo3D")}
                </Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link
                  eventKey="photo-enhance"
                  className={
                    activeKey === "photo-enhance"
                      ? "bg-[#185a56] text-gray-900 rounded-md"
                      : "text-white hover:bg-[#185a56] hover:text-gray-900 hover:bg-opacity-80 rounded-md"
                  }
                >
                  照片增强
                </Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link
                  eventKey="photo-remove-bg"
                  className={
                    activeKey === "photo-remove-bg"
                      ? "bg-[#185a56] text-gray-900 rounded-md"
                      : "text-white hover:bg-[#185a56] hover:text-gray-900 hover:bg-opacity-80 rounded-md"
                  }
                >
                  背景移除
                </Nav.Link>
              </Nav.Item>
              <Nav.Item>
                <Nav.Link
                  eventKey="voice-clone"
                  className={
                    activeKey === "voice-clone"
                      ? "bg-[#185a56] text-gray-900 rounded-md"
                      : "text-white hover:bg-[#185a56] hover:text-gray-900 hover:bg-opacity-80 rounded-md"
                  }
                >
                  {t("ai.tabs.voiceClone")}
                </Nav.Link>
              </Nav.Item>
            </Nav>
          </Col>
          <Col md={9}>
            <Tab.Content>
              <Tab.Pane eventKey="photo-restore">
                <PhotoRestoreComponent />
              </Tab.Pane>
              <Tab.Pane eventKey="photo-colorize">
                <PhotoColorizeComponent />
              </Tab.Pane>
              <Tab.Pane eventKey="photo-to-3d">
                <PhotoTo3DComponent />
              </Tab.Pane>
              <Tab.Pane eventKey="photo-enhance">
                <PhotoEnhanceComponent />
              </Tab.Pane>
              <Tab.Pane eventKey="photo-remove-bg">
                <PhotoRemoveBgComponent />
              </Tab.Pane>
              <Tab.Pane eventKey="voice-clone">
                <VoiceCloneComponent />
              </Tab.Pane>
            </Tab.Content>
          </Col>
        </Row>
      </Tab.Container>
    </Container>
  );
};

export default AIFeaturesPage;
