import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from '../components/Navbar';
import '../styles/tailwind-components.css';

interface Hero {
  id: string;
  name: string;
  nameEn?: string;
  lifeSpan: string;
  title: string;
  titleEn?: string;
  description: string;
  descriptionEn?: string;
  imageUrl: string;
  category: 'military' | 'revolution' | 'science' | 'medical' | 'civil';
  nationality: string;
}

const heroes: Hero[] = [
  // Chinese Heroes
  {
    id: 'yuan-longping',
    name: '袁隆平',
    nameEn: 'Yuan Longping',
    lifeSpan: '1930-2021',
    title: '杂交水稻之父',
    titleEn: 'Father of Hybrid Rice',
    description: '中国农学家，被誉为"杂交水稻之父"。他的研究成果大大提高了水稻产量，解决了中国乃至世界的粮食问题，让数亿人免于饥饿。',
    descriptionEn: 'Chinese agronomist known as the "Father of Hybrid Rice". His research greatly increased rice yields, solving food problems in China and worldwide, saving hundreds of millions from hunger.',
    imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
    category: 'science',
    nationality: '中国'
  },
  {
    id: 'zhong-nanshan',
    name: '钟南山',
    nameEn: 'Zhong Nanshan',
    lifeSpan: '1936-',
    title: '抗疫英雄',
    titleEn: 'Anti-epidemic Hero',
    description: '中国工程院院士，呼吸病学专家。在2003年抗击SARS和2020年抗击新冠疫情中做出杰出贡献，被誉为"国士无双"。',
    descriptionEn: 'Chinese epidemiologist and pulmonologist. Made outstanding contributions in fighting SARS in 2003 and COVID-19 in 2020, hailed as a national hero.',
    imageUrl: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=400&fit=crop&crop=face',
    category: 'medical',
    nationality: '中国'
  },
  {
    id: 'lei-feng',
    name: '雷锋',
    nameEn: 'Lei Feng',
    lifeSpan: '1940-1962',
    title: '助人为乐的楷模',
    titleEn: 'Model of Selflessness',
    description: '中国人民解放军战士，以其无私奉献、助人为乐的精神成为中国道德楷模。"雷锋精神"激励了几代中国人。',
    descriptionEn: 'PLA soldier who became a moral model in China for his selflessness and dedication to helping others. The "Lei Feng Spirit" has inspired generations.',
    imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
    category: 'military',
    nationality: '中国'
  },
  {
    id: 'qiu-shaoyun',
    name: '邱少云',
    nameEn: 'Qiu Shaoyun',
    lifeSpan: '1926-1952',
    title: '抗美援朝英雄',
    titleEn: 'Korean War Hero',
    description: '中国人民志愿军战士，在朝鲜战争中为了不暴露部队位置，在烈火中坚持潜伏，直至牺牲，展现了崇高的革命精神。',
    descriptionEn: 'Chinese volunteer soldier who sacrificed himself in the Korean War by remaining motionless while engulfed in flames to avoid exposing his unit\'s position.',
    imageUrl: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&h=400&fit=crop&crop=face',
    category: 'military',
    nationality: '中国'
  },
  
  // International Heroes
  {
    id: 'martin-luther-king',
    name: '马丁·路德·金',
    nameEn: 'Martin Luther King Jr.',
    lifeSpan: '1929-1968',
    title: '民权运动领袖',
    titleEn: 'Civil Rights Leader',
    description: '美国民权运动领袖，以非暴力抗争争取种族平等。他的"我有一个梦想"演讲激励了全世界追求平等和正义的人们。',
    descriptionEn: 'American civil rights leader who fought for racial equality through nonviolent resistance. His "I Have a Dream" speech inspired people worldwide.',
    imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/05/Martin_Luther_King%2C_Jr..jpg/220px-Martin_Luther_King%2C_Jr..jpg',
    category: 'civil',
    nationality: '美国'
  },
  {
    id: 'nelson-mandela',
    name: '纳尔逊·曼德拉',
    nameEn: 'Nelson Mandela',
    lifeSpan: '1918-2013',
    title: '反种族隔离斗士',
    titleEn: 'Anti-Apartheid Revolutionary',
    description: '南非反种族隔离革命家、政治家。历经27年监禁后，成为南非首位黑人总统，致力于种族和解与民主建设。',
    descriptionEn: 'South African anti-apartheid revolutionary and politician. After 27 years in prison, became South Africa\'s first Black president, dedicated to reconciliation.',
    imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/0/02/Nelson_Mandela_1994.jpg/220px-Nelson_Mandela_1994.jpg',
    category: 'civil',
    nationality: '南非'
  },
  {
    id: 'marie-curie',
    name: '玛丽·居里',
    nameEn: 'Marie Curie',
    lifeSpan: '1867-1934',
    title: '诺贝尔奖获得者',
    titleEn: 'Nobel Prize Winner',
    description: '波兰裔法国科学家，首位获得诺贝尔奖的女性，也是唯一在两个不同科学领域获得诺贝尔奖的人。她的研究开创了放射性理论。',
    descriptionEn: 'Polish-French scientist, first woman to win a Nobel Prize and only person to win Nobel Prizes in two different sciences. Pioneered radioactivity research.',
    imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/7e/Marie_Curie_c1920.jpg/220px-Marie_Curie_c1920.jpg',
    category: 'science',
    nationality: '波兰/法国'
  },
  {
    id: 'mahatma-gandhi',
    name: '圣雄甘地',
    nameEn: 'Mahatma Gandhi',
    lifeSpan: '1869-1948',
    title: '非暴力抵抗先驱',
    titleEn: 'Pioneer of Nonviolent Resistance',
    description: '印度独立运动领袖，通过非暴力不合作运动领导印度获得独立。他的理念影响了全球的民权和自由运动。',
    descriptionEn: 'Leader of Indian independence movement who led India to independence through nonviolent civil disobedience. His philosophy influenced civil rights movements worldwide.',
    imageUrl: 'https://upload.wikimedia.org/wikipedia/commons/thumb/7/7a/Mahatma-Gandhi%2C_studio%2C_1931.jpg/220px-Mahatma-Gandhi%2C_studio%2C_1931.jpg',
    category: 'civil',
    nationality: '印度'
  }
];

const HeroesMemorialPage: React.FC = () => {
  const navigate = useNavigate();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedNationality, setSelectedNationality] = useState<string>('all');

  const categories = [
    { value: 'all', label: '全部类别', labelEn: 'All Categories' },
    { value: 'military', label: '军事英雄', labelEn: 'Military Heroes' },
    { value: 'revolution', label: '革命先驱', labelEn: 'Revolutionary Pioneers' },
    { value: 'science', label: '科学巨匠', labelEn: 'Scientific Giants' },
    { value: 'medical', label: '医学先锋', labelEn: 'Medical Pioneers' },
    { value: 'civil', label: '民权斗士', labelEn: 'Civil Rights Fighters' }
  ];

  const nationalities = [
    { value: 'all', label: '全部国家' },
    { value: '中国', label: '中国' },
    { value: '美国', label: '美国' },
    { value: '南非', label: '南非' },
    { value: '波兰/法国', label: '波兰/法国' },
    { value: '印度', label: '印度' }
  ];

  const filteredHeroes = heroes.filter(hero => {
    const categoryMatch = selectedCategory === 'all' || hero.category === selectedCategory;
    const nationalityMatch = selectedNationality === 'all' || hero.nationality === selectedNationality;
    return categoryMatch && nationalityMatch;
  });

  const handleViewMemorial = (heroId: string) => {
    // Navigate to the hero's memorial space
    navigate(`/memorial/hero/${heroId}`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">英雄纪念馆</h1>
          <p className="text-xl text-gray-600">Heroes Memorial Hall</p>
          <p className="mt-4 text-gray-600 max-w-3xl mx-auto">
            纪念那些为人类进步、社会公正、科学发展做出卓越贡献的英雄们。他们的精神永远激励着我们前行。
          </p>
        </div>

        {/* Filters */}
        <div className="mb-8 flex flex-wrap gap-4 justify-center">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {categories.map(cat => (
              <option key={cat.value} value={cat.value}>
                {cat.label}
              </option>
            ))}
          </select>

          <select
            value={selectedNationality}
            onChange={(e) => setSelectedNationality(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {nationalities.map(nat => (
              <option key={nat.value} value={nat.value}>
                {nat.label}
              </option>
            ))}
          </select>
        </div>

        {/* Heroes Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredHeroes.map(hero => (
            <div
              key={hero.id}
              className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
            >
              <div className="relative h-64 bg-gray-200">
                <img
                  src={hero.imageUrl}
                  alt={hero.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const img = e.target as HTMLImageElement;
                    // Prevent infinite loop by only setting fallback once
                    if (!img.src.includes('data:image')) {
                      const svgContent = `
                        <svg xmlns="http://www.w3.org/2000/svg" width="400" height="300" viewBox="0 0 400 300">
                          <rect width="400" height="300" fill="#f3f4f6"/>
                          <text x="200" y="150" font-family="Arial, sans-serif" font-size="24" fill="#6b7280" text-anchor="middle" dominant-baseline="middle">
                            ${hero.name}
                          </text>
                        </svg>
                      `;
                      // Use URL encoding instead of btoa to handle Chinese characters
                      img.src = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`;
                    }
                  }}
                />
                <div className="absolute top-4 right-4 bg-white/90 px-3 py-1 rounded-full text-sm font-medium">
                  {hero.nationality}
                </div>
              </div>
              
              <div className="p-6">
                <div className="mb-2">
                  <h3 className="text-2xl font-bold text-gray-800">{hero.name}</h3>
                  {hero.nameEn && (
                    <p className="text-lg text-gray-600">{hero.nameEn}</p>
                  )}
                  <p className="text-sm text-gray-500 mt-1">{hero.lifeSpan}</p>
                </div>
                
                <div className="mb-4">
                  <p className="text-lg font-semibold text-blue-600">{hero.title}</p>
                  {hero.titleEn && (
                    <p className="text-sm text-gray-600">{hero.titleEn}</p>
                  )}
                </div>
                
                <p className="text-gray-700 mb-6 line-clamp-4">
                  {hero.description}
                </p>
                
                <button
                  onClick={() => handleViewMemorial(hero.id)}
                  className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium"
                >
                  进入纪念空间
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredHeroes.length === 0 && (
          <div className="text-center py-12">
            <p className="text-xl text-gray-600">没有找到符合条件的英雄</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default HeroesMemorialPage;