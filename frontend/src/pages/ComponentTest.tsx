import React from "react";
import { Button } from "../components/Button/Button";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  CardFooter,
  CardActions,
} from "../components/Card/Card";
import LanguageSwitcher from "../components/LanguageSwitcher";

const ComponentTest: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">组件测试页面</h1>

        {/* 语言切换器测试 */}
        <section className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">语言切换器</h2>
          <LanguageSwitcher />
        </section>

        {/* 按钮组件测试 */}
        <section className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">按钮组件</h2>
          <div className="space-y-4">
            <div className="flex gap-4 flex-wrap">
              <Button variant="primary">主要按钮</Button>
              <Button variant="secondary">次要按钮</Button>
              <Button variant="outline">轮廓按钮</Button>
              <Button variant="ghost">幽灵按钮</Button>
              <Button variant="danger">危险按钮</Button>
            </div>

            <div className="flex gap-4 flex-wrap">
              <Button size="small">小按钮</Button>
              <Button size="medium">中等按钮</Button>
              <Button size="large">大按钮</Button>
            </div>

            <div className="flex gap-4 flex-wrap">
              <Button loading>加载中</Button>
              <Button disabled>禁用按钮</Button>
              <Button fullWidth>全宽按钮</Button>
            </div>
          </div>
        </section>

        {/* 卡片组件测试 */}
        <section className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">卡片组件</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 默认卡片 */}
            <Card variant="default">
              <CardHeader title="默认卡片" subtitle="这是一个默认样式的卡片" />
              <CardContent>
                <p className="text-gray-600">
                  这里是卡片的主要内容区域。可以放置任何内容，比如文本、图片、表单等。
                </p>
              </CardContent>
              <CardActions>
                <Button variant="outline" size="small">
                  取消
                </Button>
                <Button variant="primary" size="small">
                  确认
                </Button>
              </CardActions>
            </Card>

            {/* 高级卡片 */}
            <Card variant="elevated">
              <CardHeader title="高级卡片" subtitle="带有阴影效果的卡片" />
              <CardContent>
                <p className="text-gray-600">
                  这是一个带有更明显阴影效果的卡片，适合用于重要内容的展示。
                </p>
              </CardContent>
              <CardFooter>
                <p className="text-sm text-gray-500">最后更新：2024年1月</p>
              </CardFooter>
            </Card>

            {/* 轮廓卡片 */}
            <Card variant="outlined">
              <CardHeader title="轮廓卡片" subtitle="带有边框的卡片" />
              <CardContent>
                <p className="text-gray-600">
                  这是一个带有明显边框的卡片，没有阴影效果。
                </p>
              </CardContent>
            </Card>

            {/* 可点击卡片 */}
            <Card variant="default" clickable>
              <CardHeader title="可点击卡片" subtitle="鼠标悬停时会有效果" />
              <CardContent>
                <p className="text-gray-600">
                  这是一个可点击的卡片，鼠标悬停时会显示阴影效果。
                </p>
              </CardContent>
            </Card>

            {/* 选中状态卡片 */}
            <Card variant="default" selected>
              <CardHeader title="选中状态卡片" subtitle="当前处于选中状态" />
              <CardContent>
                <p className="text-gray-600">
                  这是一个处于选中状态的卡片，带有高亮边框。
                </p>
              </CardContent>
            </Card>

            {/* 紧凑卡片 */}
            <Card variant="default" compact>
              <CardContent>
                <h3 className="font-semibold text-gray-900">紧凑卡片</h3>
                <p className="text-gray-600 text-sm mt-1">
                  这是一个紧凑样式的卡片，内边距较小。
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* 组合使用示例 */}
        <section className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">组合使用示例</h2>
          <Card variant="elevated" className="max-w-md">
            <CardHeader title="用户设置" subtitle="管理您的账户偏好" />
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    语言设置
                  </label>
                  <LanguageSwitcher />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    通知设置
                  </label>
                  <div className="flex gap-2">
                    <Button variant="outline" size="small">
                      邮件
                    </Button>
                    <Button variant="outline" size="small">
                      短信
                    </Button>
                    <Button variant="primary" size="small">
                      推送
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardActions>
              <Button variant="outline">重置</Button>
              <Button variant="primary">保存设置</Button>
            </CardActions>
          </Card>
        </section>
      </div>
    </div>
  );
};

export default ComponentTest;
