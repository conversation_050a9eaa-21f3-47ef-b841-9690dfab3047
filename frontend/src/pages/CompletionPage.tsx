import React from "react";
import { Link } from "react-router-dom";
// 暂时注释掉 Navbar 导入，直到组件被使用
// import Navbar from '../components/Navbar';
// 暂时注释掉 Sidebar 导入，直到组件创建完成
// import Sidebar from '../components/Sidebar';

const CompletionPage: React.FC = () => {
  // 使用Tailwind原生类替代自定义按钮样式常量
  // 已删除 featureCardClasses 样式常量，直接使用 Tailwind 原生类

  // Placeholder data - replace with actual memorial space data if available
  const memorialName = "李明先生纪念空间";
  const memorialLink = "/memorial/li-ming"; // Example link

  return (
    <div className="flex flex-col min-h-screen bg-gray-900 text-white">
      {/* <Navbar /> */}
      <div className="flex flex-1">
        {/* 暂时注释掉 Sidebar 组件，等待组件创建完成后再启用 */}
        {/* <Sidebar /> */}
        <main className="flex-1 p-6">
          <div className="mb-8">
            <h1 className="text-2xl font-bold mb-2 text-white">创建完成</h1>
            <p className="text-white">您的纪念空间已成功创建，可以开始使用了</p>
          </div>

          {/* Steps Indicator */}
          <div className="flex justify-between mb-10 px-0 md:px-10">
            <div className="step-item completed flex flex-col items-center w-1/4">
              <div className="step-circle">
                <i className="fas fa-check"></i>
              </div>
              <div className="mt-2 text-sm font-medium text-[#185a56]">
                基本信息
              </div>
            </div>
            <div className="step-item completed flex flex-col items-center w-1/4">
              <div className="step-circle">
                <i className="fas fa-check"></i>
              </div>
              <div className="mt-2 text-sm font-medium text-[#185a56]">
                选择场景
              </div>
            </div>
            <div className="step-item completed flex flex-col items-center w-1/4">
              <div className="step-circle">
                <i className="fas fa-check"></i>
              </div>
              <div className="mt-2 text-sm font-medium text-[#185a56]">
                上传资料
              </div>
            </div>
            <div className="step-item active flex flex-col items-center w-1/4">
              <div className="step-circle">4</div>
              <div className="mt-2 text-sm font-medium text-[#185a56]">
                完成创建
              </div>
            </div>
          </div>

          <div className="bg-gray-900 border border-[#185a56] border-opacity-30 rounded-xl shadow-md p-8 text-center">
            <div className="success-animation mb-6">
              <i className="fas fa-check-circle text-7xl text-[#185a56]"></i>
            </div>
            <h2 className="text-3xl font-semibold mb-3 text-white">
              恭喜您，{memorialName} 创建成功！
            </h2>
            <p className="text-white mb-8 max-w-xl mx-auto">
              您的专属纪念空间已经准备就绪。您可以立即前往查看，或通过以下方式进一步完善和分享您的纪念空间。
            </p>

            <div className="flex flex-col sm:flex-row justify-center items-center gap-4 mb-10">
              <Link
                to={memorialLink}
                className="bg-[#185a56] hover:bg-[#FFA631] text-gray-900 font-medium py-2 px-4 rounded-lg transition duration-300 w-full sm:w-auto"
              >
                <i className="fas fa-eye mr-2"></i> 前往纪念空间
              </Link>
              <Link
                to="/dashboard"
                className="bg-transparent hover:bg-[#185a56] hover:bg-opacity-20 text-[#185a56] border border-[#185a56] border-opacity-50 font-medium py-2 px-4 rounded-lg transition duration-300 focus:outline-none focus:ring-2 focus:ring-[#185a56] focus:ring-opacity-50 w-full sm:w-auto"
              >
                {" "}
                {/* Adjust to your dashboard route */}
                <i className="fas fa-tachometer-alt mr-2"></i> 返回管理后台
              </Link>
            </div>

            <div className="border-t border-[#185a56] border-opacity-30 pt-8">
              <h3 className="text-xl font-semibold mb-6 text-white">
                接下来您可以：
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="feature-card bg-gray-900 border border-[#185a56] border-opacity-30 p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 ease-in-out flex flex-col items-center text-center text-white hover:bg-[#185a56] hover:text-gray-900 hover:bg-opacity-10">
                  <i className="fas fa-users text-3xl text-[#185a56] mb-3"></i>
                  <h4 className="text-lg font-medium mb-1 text-white">
                    邀请亲友
                  </h4>
                  <p className="text-sm text-white mb-3">
                    邀请家族成员和亲朋好友共同参与悼念和缅怀。
                  </p>
                  <button className="bg-transparent hover:bg-[#185a56] hover:bg-opacity-20 text-[#185a56] border border-[#185a56] border-opacity-50 font-medium py-2 px-4 rounded-lg transition duration-300 focus:outline-none focus:ring-2 focus:ring-[#185a56] focus:ring-opacity-50 text-sm w-full">
                    去邀请
                  </button>
                </div>
                <div className="feature-card bg-gray-900 border border-[#185a56] border-opacity-30 p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 ease-in-out flex flex-col items-center text-center text-white hover:bg-[#185a56] hover:text-gray-900 hover:bg-opacity-10">
                  <i className="fas fa-share-alt text-3xl text-[#185a56] mb-3"></i>
                  <h4 className="text-lg font-medium mb-1 text-white">
                    分享空间
                  </h4>
                  <p className="text-sm text-white mb-3">
                    将纪念空间分享到社交媒体，让更多人了解逝者生平。
                  </p>
                  <button className="bg-transparent hover:bg-[#185a56] hover:bg-opacity-20 text-[#185a56] border border-[#185a56] border-opacity-50 font-medium py-2 px-4 rounded-lg transition duration-300 focus:outline-none focus:ring-2 focus:ring-[#185a56] focus:ring-opacity-50 text-sm w-full">
                    去分享
                  </button>
                </div>
                <div className="feature-card bg-gray-900 border border-[#185a56] border-opacity-30 p-6 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 ease-in-out flex flex-col items-center text-center text-white hover:bg-[#185a56] hover:text-gray-900 hover:bg-opacity-10">
                  <i className="fas fa-edit text-3xl text-[#185a56] mb-3"></i>
                  <h4 className="text-lg font-medium mb-1 text-white">
                    继续完善
                  </h4>
                  <p className="text-sm text-white mb-3">
                    随时可以补充更多照片、视频或生平事迹。
                  </p>
                  <button className="bg-transparent hover:bg-[#185a56] hover:bg-opacity-20 text-[#185a56] border border-[#185a56] border-opacity-50 font-medium py-2 px-4 rounded-lg transition duration-300 focus:outline-none focus:ring-2 focus:ring-[#185a56] focus:ring-opacity-50 text-sm w-full">
                    去完善
                  </button>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default CompletionPage;

// Notes:
// - Assumes Font Awesome for icons.
// - Styling for steps indicator (.step-item, .step-circle) and success animation (.success-animation) needs to be in global CSS or a CSS module if not already present.
// - Placeholder for memorial name and link, should be dynamic based on actual data.
// - Routes for buttons (e.g., /dashboard, /memorial/li-ming) are placeholders.
// - Ensure Sidebar and Navbar components are correctly imported and styled.
// - Dark mode compatibility is considered with dark: prefixes.
