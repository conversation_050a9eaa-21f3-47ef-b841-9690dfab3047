import React, { useState, ChangeEvent, FormEvent, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../hooks/useAuth";
import { FileUploadComponent } from "../components/FileUpload";
import { UploadResponse } from "../services/fileUploadService";

// 场景接口定义
interface Scene {
  id: string;
  name: string;
  description?: string;
  category: string;
  thumbnail_url?: string;
  is_premium: boolean;
  supports_mobile: boolean;
  min_performance_level: string;
}

// 表单数据接口
interface MemorialFormData {
  deceased_name: string;
  gender: string;
  birth_date: string;
  death_date: string;
  relationship: string;
  bio: string;
  privacy_level: string;
  access_password?: string;
  scene_id: string;
  cover_image?: File;
}

const CreateMemorialPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  // 表单状态
  const [formData, setFormData] = useState<MemorialFormData>({
    deceased_name: "",
    gender: "male",
    birth_date: "",
    death_date: "",
    relationship: "",
    bio: "",
    privacy_level: "public",
    access_password: "",
    scene_id: "",
  });

  // UI状态
  const [coverImageFile, setCoverImageFile] = useState<File | null>(null);
  const [coverImagePreview, setCoverImagePreview] = useState<string | null>(null);
  const [availableScenes, setAvailableScenes] = useState<Scene[]>([]);
  const [scenesLoading, setScenesLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [createdMemorialId, setCreatedMemorialId] = useState<string | null>(null);
  const [uploadedAssets, setUploadedAssets] = useState<UploadResponse[]>([]);

  // 检查用户认证状态
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login?redirect=/create-memorial");
      return;
    }
  }, [isAuthenticated, navigate]);

  // 获取可用场景
  const fetchScenes = useCallback(async () => {
    try {
      setScenesLoading(true);
      const token = localStorage.getItem("authToken");
      
      const response = await fetch("/api/v1/scenes", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const scenes = await response.json();
        setAvailableScenes(scenes);
        // 默认选择第一个免费场景
        const defaultScene = scenes.find((scene: Scene) => !scene.is_premium);
        if (defaultScene && !formData.scene_id) {
          setFormData(prev => ({ ...prev, scene_id: defaultScene.id }));
        }
      } else {
        throw new Error("获取场景列表失败");
      }
    } catch (error) {
      setApiError(error instanceof Error ? error.message : "获取场景失败");
    } finally {
      setScenesLoading(false);
    }
  }, [formData.scene_id]);

  useEffect(() => {
    if (isAuthenticated) {
      fetchScenes();
    }
  }, [isAuthenticated, fetchScenes]);

  // 表单验证
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.deceased_name.trim()) {
      errors.deceased_name = "请输入逝者姓名";
    }

    if (!formData.birth_date) {
      errors.birth_date = "请选择出生日期";
    }

    if (!formData.death_date) {
      errors.death_date = "请选择逝世日期";
    }

    if (formData.birth_date && formData.death_date && new Date(formData.birth_date) >= new Date(formData.death_date)) {
      errors.death_date = "逝世日期必须晚于出生日期";
    }

    if (!formData.relationship.trim()) {
      errors.relationship = "请输入与逝者的关系";
    }

    if (!formData.scene_id) {
      errors.scene_id = "请选择纪念场景";
    }

    if (formData.privacy_level === "password" && !formData.access_password) {
      errors.access_password = "请设置访问密码";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // 处理输入变化
  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除对应字段的错误
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  // 处理图片上传
  const handleImageUpload = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // 验证文件类型
      if (!file.type.startsWith("image/")) {
        setApiError("请选择有效的图片文件");
        return;
      }

      // 验证文件大小 (5MB)
      if (file.size > 5 * 1024 * 1024) {
        setApiError("图片文件大小不能超过5MB");
        return;
      }

      setCoverImageFile(file);
      
      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        setCoverImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // 提交表单
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setApiError(null);

    try {
      const token = localStorage.getItem("authToken");
      
      // 创建FormData以支持文件上传
      const submitData = new FormData();
      submitData.append("deceased_name", formData.deceased_name);
      submitData.append("gender", formData.gender);
      submitData.append("birth_date", formData.birth_date);
      submitData.append("death_date", formData.death_date);
      submitData.append("relationship", formData.relationship);
      submitData.append("bio", formData.bio);
      submitData.append("privacy_level", formData.privacy_level);
      submitData.append("scene_id", formData.scene_id);
      
      if (formData.access_password) {
        submitData.append("access_password", formData.access_password);
      }
      
      if (coverImageFile) {
        submitData.append("cover_image", coverImageFile);
      }

      const response = await fetch("/api/v1/memorial-spaces", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: submitData,
      });

      if (response.ok) {
        const memorial = await response.json();
        setCreatedMemorialId(memorial.id);
        setCurrentStep(4); // 进入文件上传步骤
      } else {
        const errorData = await response.json();
        throw new Error(errorData.detail || "创建纪念空间失败");
      }
    } catch (error) {
      setApiError(error instanceof Error ? error.message : "创建失败");
    } finally {
      setIsLoading(false);
    }
  };


  // 步骤导航
  const nextStep = () => {
    if (currentStep === 1) {
      // 验证基本信息
      const basicErrors: Record<string, string> = {};
      if (!formData.deceased_name.trim()) basicErrors.deceased_name = "请输入逝者姓名";
      if (!formData.birth_date) basicErrors.birth_date = "请选择出生日期";
      if (!formData.death_date) basicErrors.death_date = "请选择逝世日期";
      if (!formData.relationship.trim()) basicErrors.relationship = "请输入关系";
      
      if (Object.keys(basicErrors).length > 0) {
        setFormErrors(basicErrors);
        return;
      }
    }
    
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>正在验证身份...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">创建纪念空间</h1>
          <p className="text-gray-400">为您的挚爱创建一个永恒的数字纪念空间</p>
        </div>

        {/* 步骤指示器 - 移动端优化 */}
        <div className="flex justify-center mb-6 sm:mb-8 px-4">
          <div className="flex items-center space-x-2 sm:space-x-4 overflow-x-auto">
            {[
              { step: 1, label: "基本信息" },
              { step: 2, label: "生平与照片" },
              { step: 3, label: "场景设置" },
              { step: 4, label: "上传资料" }
            ].map((item, index) => (
              <div key={item.step} className="flex items-center flex-shrink-0">
                <div className="flex flex-col items-center">
                  <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center font-medium text-sm ${
                    currentStep >= item.step 
                      ? "bg-blue-500 text-white" 
                      : "bg-gray-700 text-gray-400"
                  }`}>
                    {item.step}
                  </div>
                  <span className="text-xs text-gray-400 mt-1 text-center whitespace-nowrap">{item.label}</span>
                </div>
                {index < 3 && (
                  <div className={`w-8 sm:w-16 h-0.5 mx-1 sm:mx-0 ${
                    currentStep > item.step ? "bg-blue-500" : "bg-gray-700"
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 错误提示 */}
        {apiError && (
          <div className="mb-6 p-4 bg-red-900 border border-red-700 rounded-lg">
            <p className="text-red-200">{apiError}</p>
          </div>
        )}

        {/* 表单 */}
        <form onSubmit={handleSubmit} className="bg-gray-800 rounded-lg p-8">
          {/* 步骤1: 基本信息 */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-white mb-6">基本信息</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    逝者姓名 *
                  </label>
                  <input
                    type="text"
                    name="deceased_name"
                    value={formData.deceased_name}
                    onChange={handleInputChange}
                    className={`w-full bg-gray-700 border rounded-lg px-3 py-2 text-white ${
                      formErrors.deceased_name ? "border-red-500" : "border-gray-600"
                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    placeholder="请输入逝者姓名"
                  />
                  {formErrors.deceased_name && (
                    <p className="text-red-400 text-sm mt-1">{formErrors.deceased_name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    性别
                  </label>
                  <select
                    name="gender"
                    value={formData.gender}
                    onChange={handleInputChange}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="male">男</option>
                    <option value="female">女</option>
                    <option value="other">其他</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    出生日期 *
                  </label>
                  <input
                    type="date"
                    name="birth_date"
                    value={formData.birth_date}
                    onChange={handleInputChange}
                    className={`w-full bg-gray-700 border rounded-lg px-3 py-2 text-white ${
                      formErrors.birth_date ? "border-red-500" : "border-gray-600"
                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  />
                  {formErrors.birth_date && (
                    <p className="text-red-400 text-sm mt-1">{formErrors.birth_date}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    逝世日期 *
                  </label>
                  <input
                    type="date"
                    name="death_date"
                    value={formData.death_date}
                    onChange={handleInputChange}
                    className={`w-full bg-gray-700 border rounded-lg px-3 py-2 text-white ${
                      formErrors.death_date ? "border-red-500" : "border-gray-600"
                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  />
                  {formErrors.death_date && (
                    <p className="text-red-400 text-sm mt-1">{formErrors.death_date}</p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    与逝者的关系 *
                  </label>
                  <input
                    type="text"
                    name="relationship"
                    value={formData.relationship}
                    onChange={handleInputChange}
                    className={`w-full bg-gray-700 border rounded-lg px-3 py-2 text-white ${
                      formErrors.relationship ? "border-red-500" : "border-gray-600"
                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    placeholder="如：父亲、母亲、朋友、老师等"
                  />
                  {formErrors.relationship && (
                    <p className="text-red-400 text-sm mt-1">{formErrors.relationship}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 步骤2: 生平与照片 */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-white mb-6">生平介绍与照片</h2>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  生平介绍
                </label>
                <textarea
                  name="bio"
                  value={formData.bio}
                  onChange={handleInputChange}
                  rows={6}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请简要介绍逝者的生平事迹、兴趣爱好、重要成就等..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  封面照片
                </label>
                <div className="border-2 border-dashed border-gray-600 rounded-lg p-6 text-center">
                  {coverImagePreview ? (
                    <div className="space-y-4">
                      <img
                        src={coverImagePreview}
                        alt="封面预览"
                        className="mx-auto h-32 w-32 object-cover rounded-lg"
                      />
                      <div>
                        <button
                          type="button"
                          onClick={() => {
                            setCoverImagePreview(null);
                            setCoverImageFile(null);
                          }}
                          className="text-red-400 hover:text-red-300 text-sm"
                        >
                          移除照片
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="text-4xl text-gray-400">📷</div>
                      <div>
                        <label className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-600 rounded-lg text-sm font-medium text-gray-300 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                          <input
                            type="file"
                            accept="image/*"
                            onChange={handleImageUpload}
                            className="hidden"
                          />
                          选择照片
                        </label>
                      </div>
                      <p className="text-gray-400 text-sm">
                        支持 JPG、PNG 格式，文件大小不超过 5MB
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* 步骤3: 场景与隐私设置 */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-white mb-6">场景与隐私设置</h2>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-4">
                  选择纪念场景 *
                </label>
                {scenesLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                    <p className="text-gray-400 mt-2">加载场景中...</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {availableScenes.map((scene) => (
                      <div
                        key={scene.id}
                        className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all ${
                          formData.scene_id === scene.id
                            ? "border-blue-500 bg-blue-900/20"
                            : "border-gray-600 hover:border-gray-500"
                        }`}
                        onClick={() => setFormData(prev => ({ ...prev, scene_id: scene.id }))}
                      >
                        {scene.thumbnail_url && (
                          <img
                            src={scene.thumbnail_url}
                            alt={scene.name}
                            className="w-full h-24 object-cover rounded mb-2"
                          />
                        )}
                        <h3 className="text-white font-medium text-sm">{scene.name}</h3>
                        {scene.description && (
                          <p className="text-gray-400 text-xs mt-1">{scene.description}</p>
                        )}
                        {scene.is_premium && (
                          <span className="absolute top-2 right-2 bg-yellow-500 text-black text-xs px-2 py-1 rounded">
                            付费
                          </span>
                        )}
                      </div>
                    ))}
                  </div>
                )}
                {formErrors.scene_id && (
                  <p className="text-red-400 text-sm mt-2">{formErrors.scene_id}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  隐私设置
                </label>
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="privacy_level"
                      value="public"
                      checked={formData.privacy_level === "public"}
                      onChange={handleInputChange}
                      className="mr-3"
                    />
                    <span className="text-white">公开 - 任何人都可以查看</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="privacy_level"
                      value="private"
                      checked={formData.privacy_level === "private"}
                      onChange={handleInputChange}
                      className="mr-3"
                    />
                    <span className="text-white">私密 - 仅自己可见</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="privacy_level"
                      value="password"
                      checked={formData.privacy_level === "password"}
                      onChange={handleInputChange}
                      className="mr-3"
                    />
                    <span className="text-white">密码保护 - 需要密码访问</span>
                  </label>
                </div>

                {formData.privacy_level === "password" && (
                  <div className="mt-4">
                    <input
                      type="password"
                      name="access_password"
                      value={formData.access_password}
                      onChange={handleInputChange}
                      className={`w-full bg-gray-700 border rounded-lg px-3 py-2 text-white ${
                        formErrors.access_password ? "border-red-500" : "border-gray-600"
                      } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      placeholder="设置访问密码"
                    />
                    {formErrors.access_password && (
                      <p className="text-red-400 text-sm mt-1">{formErrors.access_password}</p>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 步骤4: 上传纪念资料 */}
          {currentStep === 4 && createdMemorialId && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <div className="text-4xl text-green-500 mb-4">✅</div>
                <h2 className="text-xl font-semibold text-white mb-2">纪念空间创建成功！</h2>
                <p className="text-gray-400">现在您可以上传照片、视频等珍贵资料来丰富纪念空间内容</p>
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-white mb-4">上传纪念资料</h3>
                <FileUploadComponent
                  memorialSpaceId={createdMemorialId}
                  multiple={true}
                  acceptedFileTypes="image/*,video/*,audio/*,.pdf"
                  maxFileSize={50 * 1024 * 1024} // 50MB
                  uploadOptions={{
                    asset_type: 'life_photo',
                    title: `${formData.deceased_name}的珍贵回忆`
                  }}
                  onUploadSuccess={(response) => {
                    setUploadedAssets(prev => [...prev, response]);
                  }}
                  onUploadError={(error) => {
                    console.error('上传失败:', error);
                    setApiError(error);
                  }}
                  className="mb-6"
                />
                
                {uploadedAssets.length > 0 && (
                  <div className="mt-6">
                    <h4 className="text-md font-medium text-white mb-3">已上传资料 ({uploadedAssets.length})</h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {uploadedAssets.map((asset) => (
                        <div key={asset.id} className="bg-gray-800 rounded-lg p-3">
                          {asset.file_url && asset.file_url.match(/\.(jpg|jpeg|png|gif)$/i) && (
                            <img
                              src={asset.file_url}
                              alt={asset.title || '纪念照片'}
                              className="w-full h-20 object-cover rounded mb-2"
                            />
                          )}
                          <p className="text-white text-xs truncate">{asset.title || asset.original_filename}</p>
                          <p className="text-gray-400 text-xs">{(asset.file_size / 1024 / 1024).toFixed(1)} MB</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              <div className="bg-gray-800 rounded-lg p-4">
                <h4 className="text-white font-medium mb-2">💡 温馨提示</h4>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• 您可以随时跳过此步骤，稍后在纪念空间中添加更多内容</li>
                  <li>• 支持上传照片、视频、音频和文档等多种格式</li>
                  <li>• 上传的内容将丰富纪念空间的展示效果</li>
                  <li>• 所有上传的文件都会被安全保存</li>
                </ul>
              </div>
            </div>
          )}

          {/* 导航按钮 */}
          <div className="flex justify-between mt-8 pt-6 border-t border-gray-700">
            <div>
              {currentStep > 1 && currentStep < 4 && (
                <button
                  type="button"
                  onClick={prevStep}
                  className="px-6 py-2 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  上一步
                </button>
              )}
            </div>
            
            <div className="space-x-4">
              <button
                type="button"
                onClick={() => navigate("/dashboard")}
                className="px-6 py-2 text-gray-400 hover:text-gray-300"
              >
                取消
              </button>
              
              {currentStep < 3 ? (
                <button
                  type="button"
                  onClick={nextStep}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  下一步
                </button>
              ) : currentStep === 3 ? (
                <button
                  type="submit"
                  disabled={isLoading}
                  className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? "创建中..." : "创建纪念空间"}
                </button>
              ) : (
                <div className="space-x-3">
                  <button
                    type="button"
                    onClick={() => navigate(`/memorial/${createdMemorialId}`)}
                    className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
                  >
                    稍后上传
                  </button>
                  <button
                    type="button"
                    onClick={() => navigate(`/memorial/${createdMemorialId}`, { 
                      state: { message: `纪念空间创建成功！已上传 ${uploadedAssets.length} 个文件。` }
                    })}
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    完成并查看
                  </button>
                </div>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateMemorialPage;