import React, { useState } from "react";
import Sidebar from "../components/Sidebar";
import Navbar from "../components/Navbar";
import UserProfileManager from "../components/UserProfileManager";

const SettingsPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState("profile");
  const [showUserProfile, setShowUserProfile] = useState(false);

  // 已删除 formInputClasses 样式常量，直接使用 Tailwind 原生类
  // 已删除 formLabelClasses、tabButtonBaseClasses、tabContentClasses 等样式常量，直接使用 Tailwind 原生类
  const tabButtonActiveClass = "bg-amber-500 text-gray-900 shadow-md";
  const tabButtonInactiveClass =
    "text-white hover:bg-amber-500 hover:text-gray-900 hover:bg-opacity-80";
  // 使用Tailwind原生类替代自定义按钮样式常量

  const renderTabContent = () => {
    switch (activeTab) {
      case "profile":
        return (
          <div
            id="profile-content"
            className="bg-gray-900 border border-amber-500 border-opacity-30 rounded-b-lg md:rounded-r-lg md:rounded-bl-none shadow-lg"
          >
            {!showUserProfile ? (
              <div className="p-6">
                <h2 className="text-2xl font-semibold text-white mb-6">个人资料管理</h2>
                <p className="text-white mb-4">
                  使用新的个人中心管理您的详细信息、安全设置和偏好配置。
                </p>
                <button
                  onClick={() => setShowUserProfile(true)}
                  className="bg-amber-500 hover:bg-yellow-300 text-gray-900 font-semibold py-2 px-4 rounded-md transition duration-300"
                >
                  打开个人中心
                </button>
              </div>
            ) : (
              <UserProfileManager onClose={() => setShowUserProfile(false)} />
            )}
          </div>
        );
      case "security":
        return (
          <div
            id="security-content"
            className="p-6 bg-gray-900 border border-amber-500 border-opacity-30 rounded-b-lg md:rounded-r-lg md:rounded-bl-none shadow-lg"
          >
            <h2 className="text-2xl font-semibold text-white mb-6">安全设置</h2>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-white">修改密码</h3>
                <form className="mt-2 space-y-4">
                  <div>
                    <label
                      htmlFor="current-password"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      当前密码
                    </label>
                    <input
                      type="password"
                      id="current-password"
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="new-password"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      新密码
                    </label>
                    <input
                      type="password"
                      id="new-password"
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="confirm-password"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      确认新密码
                    </label>
                    <input
                      type="password"
                      id="confirm-password"
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                    />
                  </div>
                  <button
                    type="submit"
                    className="bg-amber-500 hover:bg-yellow-300 text-gray-900 font-semibold py-2 px-4 rounded-md transition duration-300"
                  >
                    更新密码
                  </button>
                </form>
              </div>
              <hr className="border-amber-500 border-opacity-30" />
              <div>
                <h3 className="text-lg font-medium text-white">
                  双因素认证 (2FA)
                </h3>
                <p className="mt-1 text-sm text-white">
                  启用双因素认证以增强账户安全。
                </p>
                <button
                  type="button"
                  className="mt-3 bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-md transition duration-300"
                >
                  设置2FA
                </button>
              </div>
            </div>
          </div>
        );
      case "notifications":
        return (
          <div
            id="notifications-content"
            className="p-6 bg-gray-900 border border-amber-500 border-opacity-30 rounded-b-lg md:rounded-r-lg md:rounded-bl-none shadow-lg"
          >
            <h2 className="text-2xl font-semibold text-white mb-6">通知偏好</h2>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-white">邮件通知</h3>
                <div className="mt-2 space-y-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox h-5 w-5 text-amber-500 bg-gray-900 border-amber-500 border-opacity-50 rounded focus:ring-amber-500"
                      defaultChecked
                    />
                    <span className="ml-2 text-white">新功能和更新</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox h-5 w-5 text-amber-500 bg-gray-900 border-amber-500 border-opacity-50 rounded focus:ring-amber-500"
                    />
                    <span className="ml-2 text-white">纪念日提醒</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox h-5 w-5 text-amber-500 bg-gray-900 border-amber-500 border-opacity-50 rounded focus:ring-amber-500"
                      defaultChecked
                    />
                    <span className="ml-2 text-white">账户安全警报</span>
                  </label>
                </div>
              </div>
              <hr className="border-amber-500 border-opacity-30" />
              <div>
                <h3 className="text-lg font-medium text-white">站内通知</h3>
                <div className="mt-2 space-y-3">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox h-5 w-5 text-amber-500 bg-gray-900 border-amber-500 border-opacity-50 rounded focus:ring-amber-500"
                      defaultChecked
                    />
                    <span className="ml-2 text-white">亲友互动提醒</span>
                  </label>
                </div>
              </div>
              <button
                type="submit"
                className="bg-amber-500 hover:bg-yellow-300 text-gray-900 font-semibold py-2 px-4 rounded-md transition duration-300"
              >
                保存偏好
              </button>
            </div>
          </div>
        );
      case "privacy":
        return (
          <div
            id="privacy-content"
            className="p-6 bg-gray-900 border border-amber-500 border-opacity-30 rounded-b-lg md:rounded-r-lg md:rounded-bl-none shadow-lg"
          >
            <h2 className="text-2xl font-semibold text-white mb-6">隐私选项</h2>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-white">
                  个人资料可见性
                </h3>
                <p className="mt-1 text-sm text-white">
                  控制谁可以看到您的个人资料信息。
                </p>
                <select className="mt-2 w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent max-w-xs">
                  <option>所有人可见</option>
                  <option selected>仅好友可见</option>
                  <option>仅自己可见</option>
                </select>
              </div>
              <hr className="border-amber-500 border-opacity-30" />
              <div>
                <h3 className="text-lg font-medium text-white">
                  纪念馆访问权限
                </h3>
                <p className="mt-1 text-sm text-white">
                  设置谁可以访问您创建的纪念馆。
                </p>
                <select className="mt-2 w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent max-w-xs">
                  <option>公开（任何人可搜索和访问）</option>
                  <option selected>凭密码访问</option>
                  <option>仅限邀请</option>
                </select>
              </div>
              <hr className="border-amber-500 border-opacity-30" />
              <div>
                <h3 className="text-lg font-medium text-white">数据管理</h3>
                <button
                  type="button"
                  className="mt-3 mr-2 bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-md transition duration-300"
                >
                  {" "}
                  {/* 保留蓝色用于下载按钮，符合常见约定 */}
                  下载我的数据
                </button>
                <button
                  type="button"
                  className="mt-3 bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-md transition duration-300"
                >
                  {" "}
                  {/* Keep red for delete as it's a common convention */}
                  删除我的账户
                </button>
              </div>
              <button
                type="submit"
                className="bg-amber-500 hover:bg-yellow-300 text-gray-900 font-semibold py-2 px-4 rounded-md transition duration-300"
              >
                保存隐私设置
              </button>
            </div>
          </div>
        );
      case "memorials":
        return (
          <div
            id="memorials-content"
            className="p-6 bg-gray-900 border border-amber-500 border-opacity-30 rounded-b-lg md:rounded-r-lg md:rounded-bl-none shadow-lg"
          >
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-semibold text-white">我的纪念空间</h2>
              <button
                type="button"
                className="bg-amber-500 hover:bg-yellow-300 text-gray-900 font-semibold py-2 px-4 rounded-md transition duration-300"
                onClick={() => window.location.href = '/create-memorial'}
              >
                创建新纪念空间
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* 示例纪念空间卡片 */}
              <div className="bg-gray-800 border border-amber-500 border-opacity-30 rounded-lg overflow-hidden hover:border-opacity-60 transition-all duration-300">
                <div className="h-32 bg-gradient-to-r from-amber-500 to-yellow-600 flex items-center justify-center">
                  <i className="fas fa-monument text-white text-3xl"></i>
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-semibold text-white mb-2">爷爷的纪念空间</h3>
                  <p className="text-gray-300 text-sm mb-3">创建于 2024-01-15</p>
                  <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                    <span>访问次数: 156</span>
                    <span>最近访问: 2天前</span>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm transition duration-300"
                      onClick={() => window.location.href = '/memorial-space/1'}
                    >
                      访问
                    </button>
                    <button
                      type="button"
                      className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-1 px-3 rounded text-sm transition duration-300"
                    >
                      编辑
                    </button>
                    <button
                      type="button"
                      className="bg-red-600 hover:bg-red-700 text-white py-1 px-3 rounded text-sm transition duration-300"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>

              {/* 另一个示例纪念空间 */}
              <div className="bg-gray-800 border border-amber-500 border-opacity-30 rounded-lg overflow-hidden hover:border-opacity-60 transition-all duration-300">
                <div className="h-32 bg-gradient-to-r from-purple-500 to-pink-600 flex items-center justify-center">
                  <i className="fas fa-heart text-white text-3xl"></i>
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-semibold text-white mb-2">奶奶的纪念空间</h3>
                  <p className="text-gray-300 text-sm mb-3">创建于 2024-02-20</p>
                  <div className="flex items-center justify-between text-sm text-gray-400 mb-4">
                    <span>访问次数: 89</span>
                    <span>最近访问: 1周前</span>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm transition duration-300"
                      onClick={() => window.location.href = '/memorial-space/2'}
                    >
                      访问
                    </button>
                    <button
                      type="button"
                      className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-1 px-3 rounded text-sm transition duration-300"
                    >
                      编辑
                    </button>
                    <button
                      type="button"
                      className="bg-red-600 hover:bg-red-700 text-white py-1 px-3 rounded text-sm transition duration-300"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>

              {/* 空状态提示 */}
              <div className="bg-gray-800 border-2 border-dashed border-amber-500 border-opacity-30 rounded-lg p-6 flex flex-col items-center justify-center text-center hover:border-opacity-60 transition-all duration-300">
                <i className="fas fa-plus-circle text-amber-500 text-4xl mb-4"></i>
                <h3 className="text-lg font-semibold text-white mb-2">创建新纪念空间</h3>
                <p className="text-gray-400 text-sm mb-4">为您的亲人创建一个永恒的纪念空间</p>
                <button
                  type="button"
                  className="bg-amber-500 hover:bg-yellow-300 text-gray-900 font-semibold py-2 px-4 rounded-md transition duration-300"
                  onClick={() => window.location.href = '/create-memorial'}
                >
                  开始创建
                </button>
              </div>
            </div>

            {/* 统计信息 */}
            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gray-800 border border-amber-500 border-opacity-30 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-amber-500 mb-1">2</div>
                <div className="text-sm text-gray-300">纪念空间总数</div>
              </div>
              <div className="bg-gray-800 border border-amber-500 border-opacity-30 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-amber-500 mb-1">245</div>
                <div className="text-sm text-gray-300">总访问次数</div>
              </div>
              <div className="bg-gray-800 border border-amber-500 border-opacity-30 rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-amber-500 mb-1">18</div>
                <div className="text-sm text-gray-300">本月祭拜次数</div>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    // Assuming a similar layout structure with Navbar and potentially a main content wrapper
    // For now, focusing on the settings page content itself
    <div className="flex min-h-screen bg-gray-900 text-white">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Navbar />
        <main className="flex-grow container mx-auto px-4 py-8 overflow-y-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-amber-500 mb-4">账户设置</h1>
            <p className="text-lg text-white">
              管理您的个人信息、安全设置和偏好。
            </p>
          </div>

          <div className="max-w-4xl mx-auto md:flex">
            {/* Left Navigation Tabs */}
            <div className="md:w-1/4 mb-6 md:mb-0 md:mr-6">
              <div className="bg-gray-900 border border-amber-500 border-opacity-30 rounded-lg shadow-lg p-2">
                <nav className="flex flex-col space-y-1">
                  <button
                    onClick={() => setActiveTab("profile")}
                    className={`px-4 py-2 font-medium text-sm rounded-lg transition-colors duration-150 flex items-center w-full ${activeTab === "profile" ? tabButtonActiveClass : tabButtonInactiveClass}`}
                  >
                    <i className="fas fa-user-edit w-5 mr-3"></i>个人资料
                  </button>
                  <button
                    onClick={() => setActiveTab("security")}
                    className={`px-4 py-2 font-medium text-sm rounded-lg transition-colors duration-150 flex items-center w-full ${activeTab === "security" ? tabButtonActiveClass : tabButtonInactiveClass}`}
                  >
                    <i className="fas fa-shield-alt w-5 mr-3"></i>安全设置
                  </button>
                  <button
                    onClick={() => setActiveTab("notifications")}
                    className={`px-4 py-2 font-medium text-sm rounded-lg transition-colors duration-150 flex items-center w-full ${activeTab === "notifications" ? tabButtonActiveClass : tabButtonInactiveClass}`}
                  >
                    <i className="fas fa-bell w-5 mr-3"></i>通知偏好
                  </button>
                  <button
                    onClick={() => setActiveTab("privacy")}
                    className={`px-4 py-2 font-medium text-sm rounded-lg transition-colors duration-150 flex items-center w-full ${activeTab === "privacy" ? tabButtonActiveClass : tabButtonInactiveClass}`}
                  >
                    <i className="fas fa-user-secret w-5 mr-3"></i>隐私选项
                  </button>
                  <button
                    onClick={() => setActiveTab("memorials")}
                    className={`px-4 py-2 font-medium text-sm rounded-lg transition-colors duration-150 flex items-center w-full ${activeTab === "memorials" ? tabButtonActiveClass : tabButtonInactiveClass}`}
                  >
                    <i className="fas fa-monument w-5 mr-3"></i>我的纪念空间
                  </button>
                </nav>
              </div>
            </div>

            {/* Right Content Area */}
            <div className="md:w-3/4">{renderTabContent()}</div>
          </div>
        </main>
        {/* Footer can be added here or at App level */}
        <footer className="bg-gray-900 text-gray-300 py-8 text-center mt-auto border-t border-amber-500 border-opacity-30">
          <div className="container mx-auto">
            <p>
              &copy; {new Date().getFullYear()} 归处在线祭祀平台. 保留所有权利.
            </p>
            <p className="text-sm mt-2">用心连接思念，科技传承记忆。</p>
          </div>
        </footer>
      </div>{" "}
      {/* Closes <div className="flex-1 flex flex-col"> */}
    </div>
  );
};

export default SettingsPage;
