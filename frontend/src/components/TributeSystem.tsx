// 祭拜系统组件
import React, { useState, useEffect, useCallback } from "react";

// 祭拜类型配置
const TRIBUTE_TYPES = {
  candle: {
    name: "点燃蜡烛",
    icon: "🕯️",
    description: "为逝者点亮心灯，表达思念",
    color: "bg-yellow-600 hover:bg-yellow-700"
  },
  flower: {
    name: "献花",
    icon: "🌸",
    description: "献上鲜花，表达敬意",
    color: "bg-pink-600 hover:bg-pink-700"
  },
  incense: {
    name: "上香",
    icon: "🪔",
    description: "点燃心香，祈福安息",
    color: "bg-purple-600 hover:bg-purple-700"
  },
  bow: {
    name: "鞠躬",
    icon: "🙏",
    description: "深深鞠躬，表达敬意",
    color: "bg-gray-600 hover:bg-gray-700"
  },
  food: {
    name: "供奉食物",
    icon: "🍎",
    description: "献上供品，寄托哀思",
    color: "bg-orange-600 hover:bg-orange-700"
  },
  offering: {
    name: "其他供品",
    icon: "🎁",
    description: "献上其他纪念品",
    color: "bg-blue-600 hover:bg-blue-700"
  }
};

interface TributeRecord {
  id: string;
  tribute_type: string;
  message?: string;
  is_anonymous: boolean;
  created_at: string;
  user_name?: string;
}

interface TributeStats {
  total_tributes: number;
  tribute_types: Record<string, number>;
  recent_tributes: TributeRecord[];
}

interface TributeSystemProps {
  memorialSpaceId: string;
  isInteractive?: boolean;
  className?: string;
}

const TributeSystem: React.FC<TributeSystemProps> = ({
  memorialSpaceId,
  isInteractive = true,
  className = "",
}) => {
  const [tributes, setTributes] = useState<TributeRecord[]>([]);
  const [stats, setStats] = useState<TributeStats | null>(null);
  const [selectedTributeType, setSelectedTributeType] = useState<string | null>(null);
  const [tributeMessage, setTributeMessage] = useState("");
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showTributeModal, setShowTributeModal] = useState(false);
  const [activeTab, setActiveTab] = useState<"tribute" | "history" | "stats">("tribute");

  // 获取祭拜记录
  const fetchTributes = useCallback(async () => {
    try {
      const token = localStorage.getItem("authToken");
      const response = await fetch(`/api/v1/memorial-spaces/${memorialSpaceId}/tributes`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTributes(data.items || []);
      }
    } catch (err) {
      console.error("获取祭拜记录失败:", err);
    }
  }, [memorialSpaceId]);

  // 获取祭拜统计
  const fetchStats = useCallback(async () => {
    try {
      const token = localStorage.getItem("authToken");
      const response = await fetch(`/api/v1/memorial-spaces/${memorialSpaceId}/tributes/stats`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (err) {
      console.error("获取祭拜统计失败:", err);
    }
  }, [memorialSpaceId]);

  // 提交祭拜
  const submitTribute = async () => {
    if (!selectedTributeType) return;

    try {
      setIsSubmitting(true);
      const token = localStorage.getItem("authToken");
      
      const response = await fetch(`/api/v1/memorial-spaces/${memorialSpaceId}/tributes`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          tribute_type: selectedTributeType,
          message: tributeMessage.trim() || null,
          is_anonymous: isAnonymous,
        }),
      });

      if (response.ok) {
        // 重置表单
        setSelectedTributeType(null);
        setTributeMessage("");
        setIsAnonymous(false);
        setShowTributeModal(false);
        
        // 刷新数据
        await Promise.all([fetchTributes(), fetchStats()]);
      } else {
        throw new Error("祭拜提交失败");
      }
    } catch (err) {
      console.error("祭拜提交失败:", err);
      alert("祭拜提交失败，请稍后重试");
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    fetchTributes();
    fetchStats();
  }, [fetchTributes, fetchStats]);

  return (
    <div className={`tribute-system ${className}`}>
      {/* 标签页导航 */}
      <div className="flex space-x-4 border-b border-gray-700 mb-6">
        {[
          { key: "tribute", label: "祭拜", icon: "🙏" },
          { key: "history", label: "祭拜记录", icon: "📜" },
          { key: "stats", label: "统计", icon: "📊" },
        ].map(tab => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`py-3 px-6 text-sm font-medium border-b-2 transition-colors flex items-center gap-2 ${
              activeTab === tab.key
                ? "text-blue-400 border-blue-400"
                : "text-gray-400 border-transparent hover:text-white"
            }`}
          >
            <span>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* 祭拜操作 */}
      {activeTab === "tribute" && (
        <div className="space-y-6">
          <div className="text-center mb-6">
            <h3 className="text-xl font-bold text-white mb-2">献上心意</h3>
            <p className="text-gray-400">选择一种方式，表达您对逝者的思念与敬意</p>
          </div>

          {/* 祭拜类型选择 */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {Object.entries(TRIBUTE_TYPES).map(([type, config]) => (
              <button
                key={type}
                onClick={() => {
                  setSelectedTributeType(type);
                  setShowTributeModal(true);
                }}
                disabled={!isInteractive}
                className={`p-4 rounded-lg border-2 transition-all text-center ${
                  isInteractive
                    ? `${config.color} border-transparent text-white hover:scale-105`
                    : "bg-gray-700 border-gray-600 text-gray-400 cursor-not-allowed"
                }`}
              >
                <div className="text-2xl mb-2">{config.icon}</div>
                <div className="font-medium mb-1">{config.name}</div>
                <div className="text-xs opacity-80">{config.description}</div>
              </button>
            ))}
          </div>

          {/* 祭拜统计简览 */}
          {stats && (
            <div className="bg-gray-800 rounded-lg p-4">
              <h4 className="text-white font-medium mb-3">祭拜统计</h4>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-400">{stats.total_tributes}</div>
                  <div className="text-sm text-gray-400">总祭拜次数</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-400">{stats.recent_tributes.length}</div>
                  <div className="text-sm text-gray-400">最近活动</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-400">{Object.keys(stats.tribute_types).length}</div>
                  <div className="text-sm text-gray-400">祭拜类型</div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 祭拜历史 */}
      {activeTab === "history" && (
        <div className="space-y-4">
          <h3 className="text-xl font-bold text-white mb-4">祭拜记录</h3>
          {tributes.length > 0 ? (
            <div className="space-y-3">
              {tributes.map(tribute => (
                <div key={tribute.id} className="bg-gray-800 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <span className="text-xl">
                        {TRIBUTE_TYPES[tribute.tribute_type as keyof typeof TRIBUTE_TYPES]?.icon || "🙏"}
                      </span>
                      <div>
                        <span className="text-blue-400 font-medium">
                          {tribute.is_anonymous ? "匿名用户" : (tribute.user_name || "用户")}
                        </span>
                        <span className="text-gray-400 text-sm ml-2">
                          {TRIBUTE_TYPES[tribute.tribute_type as keyof typeof TRIBUTE_TYPES]?.name || tribute.tribute_type}
                        </span>
                      </div>
                    </div>
                    <span className="text-gray-400 text-sm">
                      {new Date(tribute.created_at).toLocaleString()}
                    </span>
                  </div>
                  {tribute.message && (
                    <p className="text-gray-300 text-sm">{tribute.message}</p>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-400">
              暂无祭拜记录
            </div>
          )}
        </div>
      )}

      {/* 统计信息 */}
      {activeTab === "stats" && stats && (
        <div className="space-y-6">
          <h3 className="text-xl font-bold text-white mb-4">祭拜统计</h3>
          
          {/* 总体统计 */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h4 className="text-white font-medium mb-4">总体数据</h4>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-400 mb-2">{stats.total_tributes}</div>
              <div className="text-gray-400">累计祭拜次数</div>
            </div>
          </div>

          {/* 类型统计 */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h4 className="text-white font-medium mb-4">祭拜类型分布</h4>
            <div className="space-y-3">
              {Object.entries(stats.tribute_types).map(([type, count]) => {
                const config = TRIBUTE_TYPES[type as keyof typeof TRIBUTE_TYPES];
                const percentage = stats.total_tributes > 0 ? (count / stats.total_tributes * 100) : 0;
                
                return (
                  <div key={type} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-xl">{config?.icon || "🙏"}</span>
                      <span className="text-white">{config?.name || type}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-20 bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                      <span className="text-gray-400 text-sm w-12 text-right">{count}次</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* 祭拜确认模态框 */}
      {showTributeModal && selectedTributeType && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <div className="text-center mb-6">
              <div className="text-4xl mb-3">
                {TRIBUTE_TYPES[selectedTributeType as keyof typeof TRIBUTE_TYPES]?.icon}
              </div>
              <h3 className="text-xl font-bold text-white mb-2">
                {TRIBUTE_TYPES[selectedTributeType as keyof typeof TRIBUTE_TYPES]?.name}
              </h3>
              <p className="text-gray-400 text-sm">
                {TRIBUTE_TYPES[selectedTributeType as keyof typeof TRIBUTE_TYPES]?.description}
              </p>
            </div>

            {/* 祭拜留言 */}
            <div className="mb-4">
              <label className="block text-white text-sm font-medium mb-2">
                祭拜留言（可选）
              </label>
              <textarea
                value={tributeMessage}
                onChange={(e) => setTributeMessage(e.target.value)}
                placeholder="表达您的思念与敬意..."
                className="w-full bg-gray-700 text-white rounded-lg p-3 min-h-[80px] resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                maxLength={200}
              />
              <div className="text-right text-gray-400 text-xs mt-1">
                {tributeMessage.length}/200
              </div>
            </div>

            {/* 匿名选项 */}
            <div className="mb-6">
              <label className="flex items-center text-white text-sm">
                <input
                  type="checkbox"
                  checked={isAnonymous}
                  onChange={(e) => setIsAnonymous(e.target.checked)}
                  className="mr-2 rounded"
                />
                匿名祭拜
              </label>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-3">
              <button
                onClick={() => {
                  setShowTributeModal(false);
                  setSelectedTributeType(null);
                  setTributeMessage("");
                  setIsAnonymous(false);
                }}
                className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 rounded-lg"
              >
                取消
              </button>
              <button
                onClick={submitTribute}
                disabled={isSubmitting}
                className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white py-2 rounded-lg"
              >
                {isSubmitting ? "祭拜中..." : "确认祭拜"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TributeSystem;