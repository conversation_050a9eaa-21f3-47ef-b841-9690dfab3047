import React, { useEffect, useState, useRef } from "react";
import {
  Scene,
  Engine,
  SceneOptimizer,
  SceneOptimizerOptions,
  SceneInstrumentation,
} from "@babylonjs/core";
import "@babylonjs/inspector";

interface BabylonPerformanceMonitorProps {
  scene?: Scene;
  engine?: Engine;
  showInspector?: boolean;
  enableOptimizer?: boolean;
  optimizerOptions?: {
    targetFrameRate?: number;
    trackerDuration?: number;
  };
  position?: "top-left" | "top-right" | "bottom-left" | "bottom-right";
  showFps?: boolean;
  showDrawCalls?: boolean;
  showTriangles?: boolean; // Corresponds to totalVerticesCounter or activeIndicesCounter/3
  showTextures?: boolean; // Corresponds to engine.getTextureCount() or similar
  showActiveMeshes?: boolean;
  showRenderTime?: boolean;
  // Removed a large number of non-standard showTotal... props
}

/**
 * Babylon.js性能监视器组件
 * 用于显示场景性能指标和调试信息
 */
const BabylonPerformanceMonitor: React.FC<BabylonPerformanceMonitorProps> = ({
  scene,
  engine,
  showInspector = false,
  enableOptimizer = false,
  optimizerOptions = {
    targetFrameRate: 60,
    trackerDuration: 2000,
  },
  position = "bottom-right",
  showFps = true,
  showDrawCalls = true,
  showTriangles = true,
  showTextures = false, // Defaulting to false as it might need specific engine/scene calls
  showActiveMeshes = true,
  showRenderTime = true,
  // Corresponding destructuring for removed props is also implicitly removed
}) => {
  const [fps, setFps] = useState<number>(0);
  const [drawCalls, setDrawCalls] = useState<number>(0);
  const [triangles, setTriangles] = useState<number>(0);
  const [textures, setTextures] = useState<number>(0);
  const [activeMeshes, setActiveMeshes] = useState<number>(0);
  const [renderTime, setRenderTime] = useState<number>(0);

  const instrumentation = useRef<SceneInstrumentation | null>(null);
  const optimizer = useRef<SceneOptimizer | null>(null);
  const intervalRef = useRef<number | null>(null);

  useEffect(() => {
    if (!scene || !engine) {
      return;
    }

    // 初始化场景检测工具
    instrumentation.current = new SceneInstrumentation(scene);
    instrumentation.current.captureFrameTime = true;
    instrumentation.current.captureActiveMeshesEvaluationTime = true;
    instrumentation.current.captureRenderTargetsRenderTime = true;
    instrumentation.current.captureInterFrameTime = true;
    instrumentation.current.captureRenderTime = true;
    instrumentation.current.capturePhysicsTime = true;
    instrumentation.current.captureParticlesRenderTime = true;
    instrumentation.current.captureSpritesRenderTime = true;
    instrumentation.current.captureAnimationsTime = true;

    // 显示检查器
    if (showInspector) {
      if (!scene.debugLayer.isVisible()) {
        scene.debugLayer.show({
          embedMode: true,
          overlay: true,
        });
      }
    }

    // 启用场景优化器
    if (enableOptimizer) {
      const options = new SceneOptimizerOptions(
        optimizerOptions.targetFrameRate || 60,
        optimizerOptions.trackerDuration || 2000,
      );

      // 使用SceneOptimizer的标准优化选项
      // 注意：在Babylon.js 8.x中，优化器API可能已更改
      // 这里使用标准优化级别
      SceneOptimizer.OptimizeAsync(scene);

      optimizer.current = new SceneOptimizer(scene, options);
      optimizer.current.start();
    }

    // 更新性能数据
    const updatePerformanceData = () => {
      if (engine && scene) {
        setFps(engine.getFps());
        // 使用SceneInstrumentation的drawCallsCounter获取drawCalls
        if (instrumentation.current) {
          setDrawCalls(instrumentation.current.drawCallsCounter.current);
          setRenderTime(instrumentation.current.renderTimeCounter.current);
        } else {
          setDrawCalls(0);
          setRenderTime(0);
        }
        setTriangles(scene.getActiveIndices() / 3);
        setTextures(scene.textures.length);
        setActiveMeshes(scene.getActiveMeshes().length);
      }
    };

    // 设置定时器定期更新性能数据
    intervalRef.current = window.setInterval(updatePerformanceData, 1000);

    return () => {
      // 清理
      if (intervalRef.current) {
        window.clearInterval(intervalRef.current);
      }

      if (optimizer.current) {
        optimizer.current.stop();
        optimizer.current.dispose();
      }

      if (showInspector && scene.debugLayer.isVisible()) {
        scene.debugLayer.hide();
      }
    };
  }, [scene, engine, showInspector, enableOptimizer, optimizerOptions]);

  // 根据位置设置样式
  const getPositionStyle = () => {
    switch (position) {
      case "top-left":
        return { top: "10px", left: "10px" };
      case "top-right":
        return { top: "10px", right: "10px" };
      case "bottom-left":
        return { bottom: "10px", left: "10px" };
      case "bottom-right":
      default:
        return { bottom: "10px", right: "10px" };
    }
  };

  return (
    <div
      style={{
        position: "absolute",
        padding: "10px",
        backgroundColor: "rgba(var(--color-gray-900-rgb), 0.7)",
        color: "white",
        fontFamily: "monospace",
        fontSize: "12px",
        borderRadius: "5px",
        zIndex: 1000,
        ...getPositionStyle(),
      }}
    >
      <div style={{ fontWeight: "bold", marginBottom: "5px" }}>
        Babylon.js Performance Monitor
      </div>
      {showFps && <div>FPS: {fps.toFixed(2)}</div>}
      {showDrawCalls && <div>Draw Calls: {drawCalls}</div>}
      {showTriangles && <div>Triangles: {triangles}</div>}
      {showTextures && <div>Textures: {textures}</div>}
      {showActiveMeshes && <div>Active Meshes: {activeMeshes}</div>}
      {showRenderTime && <div>Render Time: {renderTime.toFixed(2)} ms</div>}
    </div>
  );
};

export default BabylonPerformanceMonitor;
