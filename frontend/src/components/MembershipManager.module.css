/* 会员权益管理组件样式 */
.membershipManager {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f0f0;
}

.header h2 {
  margin: 0;
  color: #1a1a1a;
  font-size: 28px;
  font-weight: 600;
}

.currentTier {
  display: flex;
  align-items: center;
}

.tierBadge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
}

/* 标签页 */
.tabs {
  display: flex;
  margin-bottom: 24px;
  border-bottom: 1px solid #e0e0e0;
}

.tab {
  background: none;
  border: none;
  padding: 12px 24px;
  cursor: pointer;
  font-size: 16px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.tab:hover {
  color: #667eea;
}

.tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
  font-weight: 600;
}

/* 加载和错误状态 */
.loading, .error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retryButton {
  background: #667eea;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 16px;
  transition: background-color 0.3s ease;
}

.retryButton:hover {
  background: #5a6fd8;
}

/* 概览页面 */
.overview {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.featuresGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.featureCard {
  background: #f8f9fa;
  padding: 24px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.featureCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.featureCard h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.featureValue {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 8px;
}

.featureDescription {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 升级建议 */
.upgradeRecommendations {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 24px;
}

.upgradeRecommendations h4 {
  margin: 0 0 16px 0;
  color: #856404;
  font-size: 18px;
}

.recommendationCard {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 12px;
  border: 1px solid #f0c040;
}

.recommendationCard:last-child {
  margin-bottom: 0;
}

.recommendationContent h5 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
}

.recommendationContent p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.recommendedTier {
  color: #667eea;
  font-weight: 600;
  margin-left: 8px;
}

.upgradeButton {
  background: #ffc107;
  color: #212529;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;
  white-space: nowrap;
}

.upgradeButton:hover {
  background: #e0a800;
}

/* 使用情况页面 */
.usage h4 {
  margin: 0 0 24px 0;
  color: #333;
  font-size: 20px;
}

.usageList {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 32px;
}

.usageItem {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.usageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.featureName {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.usagePercentage {
  font-weight: 700;
  font-size: 16px;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

.usageDetails {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.usageDetails h5 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
}

.usageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.usageGrid div {
  font-size: 14px;
  color: #666;
}

/* 等级对比页面 */
.tiers h4 {
  margin: 0 0 24px 0;
  color: #333;
  font-size: 20px;
}

.tiersGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.tierCard {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 24px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
}

.tierCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.1);
}

.tierCard.currentTier {
  border-color: #667eea;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
}

.tierHeader {
  text-align: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.tierHeader h5 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 20px;
  font-weight: 700;
}

.tierHeader p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.tierFeatures {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.tierFeature {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
}

.tierFeature:last-child {
  border-bottom: none;
}

.tierFeature span:first-child {
  color: #666;
}

.tierFeature span:last-child {
  color: #333;
  font-weight: 600;
}

.selectTierButton {
  width: 100%;
  background: #667eea;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.selectTierButton:hover {
  background: #5a6fd8;
}

.currentTierBadge {
  width: 100%;
  background: #28a745;
  color: white;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .membershipManager {
    padding: 16px;
  }
  
  .header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .header h2 {
    font-size: 24px;
  }
  
  .tabs {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .tab {
    flex: 1;
    min-width: 80px;
    padding: 12px 16px;
    font-size: 14px;
  }
  
  .featuresGrid {
    grid-template-columns: 1fr;
  }
  
  .recommendationCard {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .tiersGrid {
    grid-template-columns: 1fr;
  }
  
  .usageGrid {
    grid-template-columns: 1fr;
  }
}

/* 辅助类 */
.success {
  color: #28a745;
}

.warning {
  color: #ffc107;
}

.error {
  color: #dc3545;
}

.info {
  color: #17a2b8;
}