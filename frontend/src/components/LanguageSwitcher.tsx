import React from "react";
import { useTranslation } from "react-i18next";
import { cn } from "../utils/cn";
import { motion } from "framer-motion";

const LanguageSwitcher: React.FC = () => {
  const { i18n } = useTranslation();

  const changeLanguage = (lng: string) => {
    i18n.changeLanguage(lng);
  };

  return (
    <motion.div
      className="flex gap-2.5"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <button
        onClick={() => changeLanguage("zh")}
        className={cn(
          "bg-gray-900 text-white border-none px-3 py-2 rounded cursor-pointer transition-colors duration-300",
          "hover:bg-gray-700 text-sm md:text-base",
          {
            "bg-amber-500 hover:bg-amber-600": i18n.language === "zh",
          },
        )}
      >
        中文
      </button>
      <button
        onClick={() => changeLanguage("en")}
        className={cn(
          "bg-gray-900 text-white border-none px-3 py-2 rounded cursor-pointer transition-colors duration-300",
          "hover:bg-gray-700 text-sm md:text-base",
          {
            "bg-amber-500 hover:bg-amber-600": i18n.language === "en",
          },
        )}
      >
        English
      </button>
    </motion.div>
  );
};

export default LanguageSwitcher;
