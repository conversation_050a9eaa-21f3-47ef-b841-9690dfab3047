import React, { useRef, useState } from "react";
import { Engine, Scene } from "react-babylonjs";
import { Vector3, Color3, Color4, Mesh } from "@babylonjs/core";
// 导入Babylon.js的事件系统
import { ActionManager, ExecuteCodeAction } from "@babylonjs/core";
import "@babylonjs/loaders";

interface BoxProps {
  position: Vector3;
  color?: Color3;
  hoveredColor?: Color3;
  onBoxClick?: () => void;
}

// 旋转盒子组件
const RotatingBox: React.FC<BoxProps> = (props) => {
  const boxRef = useRef<Mesh | null>(null);
  const [hovered, setHovered] = useState(false);

  // 使用onCreated回调设置动画和事件
  const handleBoxCreated = (box: Mesh) => {
    if (box) {
      // 存储引用
      boxRef.current = box;

      // 设置动画
      box.getScene().registerBeforeRender(() => {
        if (box && box.rotation) {
          box.rotation.x += 0.01;
          box.rotation.y += 0.01;
        }
      });

      // 设置交互事件
      box.actionManager = new ActionManager(box.getScene());

      // 点击事件
      box.actionManager.registerAction(
        new ExecuteCodeAction(ActionManager.OnPickTrigger, () => {
          if (props.onBoxClick) {
            props.onBoxClick();
          }
        }),
      );

      // 悬停事件
      box.actionManager.registerAction(
        new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, () =>
          setHovered(true),
        ),
      );

      box.actionManager.registerAction(
        new ExecuteCodeAction(ActionManager.OnPointerOutTrigger, () =>
          setHovered(false),
        ),
      );
    }
  };

  const color = hovered
    ? props.hoveredColor || Color3.Red()
    : props.color || Color3.Green();

  return (
    <box
      name="box"
      size={2}
      position={props.position}
      onCreated={handleBoxCreated}
    >
      <standardMaterial
        name="boxMaterial"
        diffuseColor={color}
        specularColor={Color3.Black()}
      />
    </box>
  );
};

// 地面组件
const Ground: React.FC = () => {
  return (
    <ground
      name="ground"
      width={10}
      height={10}
      subdivisions={2}
      receiveShadows={true}
    >
      <standardMaterial
        name="groundMaterial"
        diffuseColor={Color3.FromHexString("var(--color-gray-400)")}
        specularColor={Color3.Black()}
      />
    </ground>
  );
};

// 主场景组件
const BabylonScene: React.FC = () => {
  const [clicked, setClicked] = useState(false);

  const handleBoxClick = () => {
    setClicked(!clicked);
    console.log("Box clicked!");
  };

  return (
    <div style={{ width: "100%", height: "100vh" }}>
      <Engine antialias adaptToDeviceRatio canvasId="babylonJS">
        <Scene clearColor={new Color4(0.2, 0.2, 0.3, 1)}>
          {/* 相机 */}
          <arcRotateCamera
            name="camera"
            target={Vector3.Zero()}
            alpha={-Math.PI / 2}
            beta={Math.PI / 3}
            radius={8}
            minZ={0.001}
            wheelPrecision={50}
            lowerRadiusLimit={3}
            upperRadiusLimit={20}
          />

          {/* 光源 */}
          <hemisphericLight
            key="hemiLight"
            name="hemiLight"
            intensity={0.7}
            direction={new Vector3(0, 1, 0)}
          />
          <pointLight
            key="pointLight"
            name="pointLight"
            position={new Vector3(0, 4, 0)}
            intensity={0.5}
          />

          {/* 3D对象 */}
          <RotatingBox
            position={new Vector3(0, 1, 0)}
            color={clicked ? Color3.Yellow() : Color3.Green()}
            hoveredColor={Color3.Red()}
            onBoxClick={handleBoxClick}
          />
          <Ground />
        </Scene>
      </Engine>
    </div>
  );
};

export default BabylonScene;
