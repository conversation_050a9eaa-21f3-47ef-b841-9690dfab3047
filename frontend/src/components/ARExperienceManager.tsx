import React, { useState, useEffect, useRef } from 'react';
import styles from './ARExperienceManager.module.css';

interface ARExperienceManagerProps {
  memorialSpaceId: string;
  virtualProducts: VirtualProduct[];
  onProductPlace?: (productId: string, position: XRPosition, rotation: XRRotation) => void;
  onExitAR?: () => void;
}

interface VirtualProduct {
  id: string;
  name: string;
  modelUrl: string;
  type: 'incense' | 'flower' | 'candle' | 'lantern' | 'fruit' | 'tea' | 'custom';
  scale: number;
  category: 'traditional' | 'modern' | 'religious' | 'seasonal';
  cultural: 'chinese' | 'buddhist' | 'christian' | 'universal';
  price: number;
  isOwned: boolean;
}

interface XRPosition {
  x: number;
  y: number;
  z: number;
}

interface XRRotation {
  x: number;
  y: number;
  z: number;
  w: number;
}

interface ARSessionState {
  isSupported: boolean;
  isActive: boolean;
  error: string | null;
  placedObjects: PlacedObject[];
  selectedProduct: VirtualProduct | null;
}

interface PlacedObject {
  id: string;
  productId: string;
  position: XRPosition;
  rotation: XRRotation;
  timestamp: number;
}

interface WebXRFeatureDetection {
  domOverlay: boolean;
  planeDetection: boolean;
  handTracking: boolean;
  hitTest: boolean;
  localFloor: boolean;
}

export const ARExperienceManager: React.FC<ARExperienceManagerProps> = ({
  virtualProducts,
  onProductPlace,
  onExitAR,
}) => {
  const [arState, setArState] = useState<ARSessionState>({
    isSupported: false,
    isActive: false,
    error: null,
    placedObjects: [],
    selectedProduct: null,
  });

  const [features, setFeatures] = useState<WebXRFeatureDetection>({
    domOverlay: false,
    planeDetection: false,
    handTracking: false,
    hitTest: false,
    localFloor: false,
  });

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const sessionRef = useRef<XRSession | null>(null);
  const rendererRef = useRef<any>(null);
  const sceneRef = useRef<any>(null);
  const cameraRef = useRef<any>(null);
  const hitTestSourceRef = useRef<XRHitTestSource | null>(null);
  const reticleRef = useRef<any>(null);

  useEffect(() => {
    checkARSupport();
    return () => {
      if (sessionRef.current) {
        endARSession();
      }
    };
  }, []);

  /// 检查AR支持
  const checkARSupport = async () => {
    try {
      if (!navigator.xr) {
        setArState(prev => ({
          ...prev,
          error: 'WebXR不被当前浏览器支持',
        }));
        return;
      }

      const isSupported = await navigator.xr.isSessionSupported('immersive-ar');
      
      if (isSupported) {
        // 检测具体功能支持
        const featureDetection = await detectWebXRFeatures();
        setFeatures(featureDetection);
        
        setArState(prev => ({
          ...prev,
          isSupported: true,
        }));
      } else {
        setArState(prev => ({
          ...prev,
          error: '设备不支持沉浸式AR体验',
        }));
      }
    } catch (error) {
      console.error('AR支持检查失败:', error);
      setArState(prev => ({
        ...prev,
        error: 'AR功能检查失败',
      }));
    }
  };

  /// 检测WebXR功能支持
  const detectWebXRFeatures = async (): Promise<WebXRFeatureDetection> => {
    const features: WebXRFeatureDetection = {
      domOverlay: false,
      planeDetection: false,
      handTracking: false,
      hitTest: false,
      localFloor: false,
    };

    try {
      // 检测DOM覆盖支持
      const session = await navigator.xr?.requestSession('immersive-ar', {
        requiredFeatures: ['dom-overlay'],
        domOverlay: { root: document.body },
      });
      if (session) {
        features.domOverlay = true;
        await session.end();
      }
    } catch {
      // DOM覆盖不支持
    }

    try {
      // 检测平面检测
      const session = await navigator.xr?.requestSession('immersive-ar', {
        requiredFeatures: ['plane-detection'],
      });
      if (session) {
        features.planeDetection = true;
        await session.end();
      }
    } catch {
      // 平面检测不支持
    }

    try {
      // 检测命中测试
      const session = await navigator.xr?.requestSession('immersive-ar', {
        requiredFeatures: ['hit-test'],
      });
      if (session) {
        features.hitTest = true;
        await session.end();
      }
    } catch {
      // 命中测试不支持
    }

    return features;
  };

  /// 启动AR会话
  const startARSession = async () => {
    if (!arState.isSupported || !navigator.xr) {
      return;
    }

    try {
      setArState(prev => ({ ...prev, error: null }));

      // 配置AR会话选项
      const sessionInit: XRSessionInit = {
        requiredFeatures: ['hit-test', 'local-floor'],
        optionalFeatures: ['dom-overlay', 'plane-detection'],
        domOverlay: { root: document.getElementById('ar-overlay')! },
      };

      const session = await navigator.xr.requestSession('immersive-ar', sessionInit);
      sessionRef.current = session;

      // 初始化Three.js渲染器和场景
      await initializeThreeJS(session);

      // 设置命中测试源
      await setupHitTestSource(session);

      // 开始渲染循环
      session.requestAnimationFrame(onXRFrame);

      setArState(prev => ({
        ...prev,
        isActive: true,
      }));

      // 会话结束时的清理
      session.addEventListener('end', () => {
        setArState(prev => ({
          ...prev,
          isActive: false,
        }));
        cleanup();
      });

    } catch (error) {
      console.error('AR会话启动失败:', error);
      setArState(prev => ({
        ...prev,
        error: 'AR会话启动失败，请检查设备权限',
      }));
    }
  };

  /// 初始化Three.js
  const initializeThreeJS = async (_session: XRSession) => {
    // const canvas = canvasRef.current!;
    
    // 这里使用Three.js的伪代码，实际需要导入Three.js
    // const THREE = await import('three');
    // const { GLTFLoader } = await import('three/examples/jsm/loaders/GLTFLoader');
    
    // 创建场景
    // sceneRef.current = new THREE.Scene();
    
    // 创建相机
    // cameraRef.current = new THREE.PerspectiveCamera();
    
    // 创建渲染器
    // rendererRef.current = new THREE.WebGLRenderer({
    //   canvas: canvas,
    //   context: await canvas.getContext('webgl2'),
    //   alpha: true,
    // });
    
    // 设置XR
    // rendererRef.current.xr.enabled = true;
    // rendererRef.current.xr.setSession(session);

    // 创建瞄准器
    // createReticle();
  };

  /// 设置命中测试源
  const setupHitTestSource = async (session: XRSession) => {
    const referenceSpace = await session.requestReferenceSpace('viewer');
    hitTestSourceRef.current = await session.requestHitTestSource!({ space: referenceSpace });
  };

  /// XR帧渲染
  const onXRFrame = (_time: number, frame: XRFrame) => {
    const session = frame.session;
    session.requestAnimationFrame(onXRFrame);

    if (hitTestSourceRef.current) {
      const hitTestResults = frame.getHitTestResults(hitTestSourceRef.current);
      
      if (hitTestResults.length > 0) {
        // const hit = hitTestResults[0];
        // const pose = hit.getPose(session.renderState.baseSpace!);
        
        // if (pose && reticleRef.current) {
        //   // 更新瞄准器位置
        //   // reticleRef.current.position.setFromMatrixPosition(pose.transform.matrix);
        //   // reticleRef.current.visible = true;
        // }
      } else if (reticleRef.current) {
        // reticleRef.current.visible = false;
      }
    }

    // 渲染场景
    // rendererRef.current?.render(sceneRef.current, cameraRef.current);
  };

  /// 创建瞄准器 (暂时禁用)
  // const createReticle = () => {
  //   // 使用Three.js创建瞄准器几何体
  //   // const geometry = new THREE.RingGeometry(0.15, 0.2, 32).rotateX(-Math.PI / 2);
  //   // const material = new THREE.MeshBasicMaterial();
  //   // reticleRef.current = new THREE.Mesh(geometry, material);
  //   // reticleRef.current.matrixAutoUpdate = false;
  //   // reticleRef.current.visible = false;
  //   // sceneRef.current?.add(reticleRef.current);
  // };

  /// 放置虚拟物品
  const placeVirtualProduct = (product: VirtualProduct) => {
    if (!sessionRef.current || !reticleRef.current || !reticleRef.current.visible) {
      return;
    }

    const position: XRPosition = {
      x: reticleRef.current.position.x,
      y: reticleRef.current.position.y,
      z: reticleRef.current.position.z,
    };

    const rotation: XRRotation = {
      x: 0,
      y: Math.random() * Math.PI * 2, // 随机旋转
      z: 0,
      w: 1,
    };

    const placedObject: PlacedObject = {
      id: generateUniqueId(),
      productId: product.id,
      position,
      rotation,
      timestamp: Date.now(),
    };

    // 加载并添加3D模型到场景
    loadAndPlaceModel(product, position, rotation);

    setArState(prev => ({
      ...prev,
      placedObjects: [...prev.placedObjects, placedObject],
    }));

    onProductPlace?.(product.id, position, rotation);
  };

  /// 加载并放置3D模型
  const loadAndPlaceModel = async (_product: VirtualProduct, _position: XRPosition, _rotation: XRRotation) => {
    try {
      // 使用GLTF加载器加载模型
      // const loader = new GLTFLoader();
      // const gltf = await loader.loadAsync(product.modelUrl);
      
      // const model = gltf.scene;
      // model.position.set(position.x, position.y, position.z);
      // model.quaternion.set(rotation.x, rotation.y, rotation.z, rotation.w);
      // model.scale.setScalar(product.scale);
      
      // sceneRef.current?.add(model);
    } catch (error) {
      console.error('模型加载失败:', error);
    }
  };

  /// 结束AR会话
  const endARSession = async () => {
    if (sessionRef.current) {
      await sessionRef.current.end();
      sessionRef.current = null;
    }
    onExitAR?.();
  };

  /// 清理资源
  const cleanup = () => {
    hitTestSourceRef.current = null;
    rendererRef.current = null;
    sceneRef.current = null;
    cameraRef.current = null;
    reticleRef.current = null;
  };

  /// 生成唯一ID
  const generateUniqueId = (): string => {
    return `ar_object_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  /// 产品选择处理
  const handleProductSelect = (product: VirtualProduct) => {
    if (!product.isOwned) {
      // 跳转到购买页面
      window.location.href = `/store/product/${product.id}`;
      return;
    }

    setArState(prev => ({
      ...prev,
      selectedProduct: product,
    }));
  };

  /// 清除所有放置的物品
  const clearAllPlacedObjects = () => {
    setArState(prev => ({
      ...prev,
      placedObjects: [],
    }));

    // 从场景中移除所有放置的物品
    // sceneRef.current?.children.forEach(child => {
    //   if (child.userData.isPlacedObject) {
    //     sceneRef.current?.remove(child);
    //   }
    // });
  };

  if (!arState.isSupported) {
    return (
      <div className={styles.arUnsupported}>
        <div className={styles.errorIcon}>📱</div>
        <h3>AR体验不可用</h3>
        <p>{arState.error || '您的设备不支持AR功能'}</p>
        <div className={styles.requirements}>
          <h4>AR体验要求：</h4>
          <ul>
            <li>支持WebXR的现代浏览器</li>
            <li>具备AR功能的移动设备</li>
            <li>稳定的网络连接</li>
            <li>良好的光照条件</li>
          </ul>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.arExperience}>
      {!arState.isActive ? (
        <div className={styles.arStart}>
          <div className={styles.arPreview}>
            <div className={styles.arIcon}>🔮</div>
            <h2>增强现实纪念体验</h2>
            <p>在真实环境中放置虚拟祭品，创造沉浸式纪念体验</p>
            
            <div className={styles.featureSupport}>
              <h3>设备功能支持</h3>
              <div className={styles.featureList}>
                <div className={`${styles.feature} ${features.hitTest ? styles.supported : styles.unsupported}`}>
                  <span className={styles.featureIcon}>
                    {features.hitTest ? '✅' : '❌'}
                  </span>
                  <span>空间定位</span>
                </div>
                <div className={`${styles.feature} ${features.planeDetection ? styles.supported : styles.unsupported}`}>
                  <span className={styles.featureIcon}>
                    {features.planeDetection ? '✅' : '❌'}
                  </span>
                  <span>平面检测</span>
                </div>
                <div className={`${styles.feature} ${features.domOverlay ? styles.supported : styles.unsupported}`}>
                  <span className={styles.featureIcon}>
                    {features.domOverlay ? '✅' : '❌'}
                  </span>
                  <span>界面覆盖</span>
                </div>
              </div>
            </div>

            <button 
              className={styles.startARButton}
              onClick={startARSession}
              disabled={!!arState.error}
            >
              开始AR体验
            </button>

            {arState.error && (
              <div className={styles.error}>
                <p>{arState.error}</p>
              </div>
            )}
          </div>

          <div className={styles.productSelection}>
            <h3>选择虚拟祭品</h3>
            <div className={styles.productGrid}>
              {virtualProducts.map((product) => (
                <div 
                  key={product.id}
                  className={`${styles.productCard} ${!product.isOwned ? styles.locked : ''}`}
                  onClick={() => handleProductSelect(product)}
                >
                  <div className={styles.productImage}>
                    <div className={styles.productType}>
                      {getProductIcon(product.type)}
                    </div>
                    {!product.isOwned && (
                      <div className={styles.lockOverlay}>
                        <span>🔒</span>
                      </div>
                    )}
                  </div>
                  <div className={styles.productInfo}>
                    <h4>{product.name}</h4>
                    <div className={styles.productMeta}>
                      <span className={styles.category}>
                        {getCategoryName(product.category)}
                      </span>
                      <span className={styles.cultural}>
                        {getCulturalName(product.cultural)}
                      </span>
                    </div>
                    {!product.isOwned && (
                      <div className={styles.price}>¥{product.price}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className={styles.arActive}>
          {/* AR会话激活时的界面 */}
          <canvas 
            ref={canvasRef}
            className={styles.arCanvas}
          />

          {/* AR覆盖界面 */}
          <div id="ar-overlay" className={styles.arOverlay}>
            <div className={styles.arControls}>
              <button 
                className={styles.exitButton}
                onClick={endARSession}
              >
                退出AR
              </button>
              
              <div className={styles.arStats}>
                <span>已放置: {arState.placedObjects.length}</span>
              </div>

              <button 
                className={styles.clearButton}
                onClick={clearAllPlacedObjects}
                disabled={arState.placedObjects.length === 0}
              >
                清除全部
              </button>
            </div>

            {arState.selectedProduct && (
              <div className={styles.productPlacement}>
                <div className={styles.selectedProduct}>
                  <span>{arState.selectedProduct.name}</span>
                  <button 
                    onClick={() => placeVirtualProduct(arState.selectedProduct!)}
                  >
                    放置
                  </button>
                </div>
              </div>
            )}

            <div className={styles.arInstructions}>
              <p>移动设备寻找平面，点击"放置"按钮添加虚拟祭品</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// 辅助函数
const getProductIcon = (type: VirtualProduct['type']): string => {
  const icons = {
    incense: '🕯️',
    flower: '🌸',
    candle: '🕯️',
    lantern: '🏮',
    fruit: '🍊',
    tea: '🍵',
    custom: '🎁',
  };
  return icons[type] || '🎁';
};

const getCategoryName = (category: VirtualProduct['category']): string => {
  const names = {
    traditional: '传统',
    modern: '现代',
    religious: '宗教',
    seasonal: '节日',
  };
  return names[category] || category;
};

const getCulturalName = (cultural: VirtualProduct['cultural']): string => {
  const names = {
    chinese: '中式',
    buddhist: '佛教',
    christian: '基督教',
    universal: '通用',
  };
  return names[cultural] || cultural;
};

export default ARExperienceManager;