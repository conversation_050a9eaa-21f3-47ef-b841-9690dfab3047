import React, { useState, useEffect } from 'react';
import { authService } from '../services/authService';
import styles from './DatabaseAdminPanel.module.css';

// 类型定义
interface DatabaseTable {
  name: string;
  column_count: number;
  row_count: number;
  primary_keys: string[];
  foreign_keys_count: number;
  size_mb?: number; // 表大小，可选字段
}

interface DatabaseStats {
  database_info: {
    name: string;
    version: string;
    total_tables: number;
    total_rows: number;
    size_bytes: number;
    size_mb: number;
  };
  table_statistics: DatabaseTable[];
  summary: {
    largest_table: DatabaseTable | null;
    total_size_mb: number;
    avg_rows_per_table: number;
  };
}

interface TableColumn {
  name: string;
  type: string;
  nullable: boolean;
  default: string | null;
  autoincrement: boolean;
  comment: string;
}

interface TableSchema {
  table_name: string;
  columns: TableColumn[];
  primary_keys: { constrained_columns: string[] };
  foreign_keys: any[];
  indexes: any[];
  unique_constraints: any[];
  check_constraints: any[];
}

interface TableData {
  table_name: string;
  columns: string[];
  rows: Record<string, any>[];
  pagination: {
    page: number;
    page_size: number;
    total_count: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  filters: {
    search: string | null;
    order_by: string | null;
    order_direction: string;
  };
}

interface HealthCheck {
  status: string;
  connection: string;
  active_connections?: number;
  database_start_time?: string;
  timestamp: string;
  error?: string;
}

const DatabaseAdminPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'tables' | 'data' | 'health'>('overview');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // 数据状态
  const [tables, setTables] = useState<DatabaseTable[]>([]);
  const [stats, setStats] = useState<DatabaseStats | null>(null);
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [tableSchema, setTableSchema] = useState<TableSchema | null>(null);
  const [tableData, setTableData] = useState<TableData | null>(null);
  const [healthStatus, setHealthStatus] = useState<HealthCheck | null>(null);
  
  // 筛选和分页状态
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [sortColumn, setSortColumn] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // API 调用函数
  const callAPI = async (endpoint: string, options?: RequestInit) => {
    try {
      // 使用httpClient直接调用，它已经包含了认证处理
      const response = await fetch(`${import.meta.env.VITE_APP_API_BASE_URL}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${await authService.getStoredToken()}`,
          ...options?.headers,
        },
        ...options,
      });
      
      if (!response.ok) {
        throw new Error(`API 调用失败: ${response.status}`);
      }
      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || '操作失败');
      }
      return data.data;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : '网络错误');
    }
  };

  // 获取数据库表列表
  const fetchTables = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await callAPI('/api/v1/db-admin/tables');
      setTables(data.tables);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取表列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取数据库统计信息
  const fetchStats = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await callAPI('/api/v1/db-admin/statistics');
      setStats(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取统计信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取表结构
  const fetchTableSchema = async (tableName: string) => {
    setLoading(true);
    setError(null);
    try {
      const data = await callAPI(`/api/v1/db-admin/tables/${tableName}/schema`);
      setTableSchema(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取表结构失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取表数据
  const fetchTableData = async (tableName: string, page = 1, search = '', orderBy = '', orderDir: 'asc' | 'desc' = 'asc') => {
    setLoading(true);
    setError(null);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        page_size: pageSize.toString(),
        ...(search && { search }),
        ...(orderBy && { order_by: orderBy }),
        order_direction: orderDir,
      });
      
      const data = await callAPI(`/api/v1/db-admin/tables/${tableName}/data?${params}`);
      setTableData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取表数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取健康检查
  const fetchHealthCheck = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await callAPI('/api/v1/db-admin/health');
      setHealthStatus(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : '健康检查失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    if (activeTab === 'overview') {
      fetchStats();
    } else if (activeTab === 'tables') {
      fetchTables();
    } else if (activeTab === 'health') {
      fetchHealthCheck();
    }
  }, [activeTab]);

  // 表选择处理
  const handleTableSelect = (tableName: string) => {
    setSelectedTable(tableName);
    setActiveTab('data');
    fetchTableSchema(tableName);
    fetchTableData(tableName, 1, searchTerm, sortColumn, sortDirection);
  };

  // 搜索处理
  const handleSearch = () => {
    if (selectedTable) {
      setCurrentPage(1);
      fetchTableData(selectedTable, 1, searchTerm, sortColumn, sortDirection);
    }
  };

  // 排序处理
  const handleSort = (column: string) => {
    const newDirection = sortColumn === column && sortDirection === 'asc' ? 'desc' : 'asc';
    setSortColumn(column);
    setSortDirection(newDirection);
    if (selectedTable) {
      fetchTableData(selectedTable, currentPage, searchTerm, column, newDirection);
    }
  };

  // 分页处理
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    if (selectedTable) {
      fetchTableData(selectedTable, page, searchTerm, sortColumn, sortDirection);
    }
  };

  // 渲染导航标签
  const renderTabs = () => (
    <div className={styles.navTabs}>
      <button
        className={`${styles.navTab} ${activeTab === 'overview' ? styles.active : ''}`}
        onClick={() => setActiveTab('overview')}
      >
        概览
      </button>
      <button
        className={`${styles.navTab} ${activeTab === 'tables' ? styles.active : ''}`}
        onClick={() => setActiveTab('tables')}
      >
        表结构
      </button>
      <button
        className={`${styles.navTab} ${activeTab === 'data' ? styles.active : ''}`}
        onClick={() => setActiveTab('data')}
      >
        数据浏览
      </button>
      <button
        className={`${styles.navTab} ${activeTab === 'health' ? styles.active : ''}`}
        onClick={() => setActiveTab('health')}
      >
        健康检查
      </button>
    </div>
  );

  // 渲染统计概览
  const renderOverview = () => (
    <div className={styles.contentSection}>
      <div className={styles.sectionHeader}>
        <h3 className={styles.sectionTitle}>数据库统计概览</h3>
      </div>
      <div className={styles.sectionBody}>
        {stats && (
          <>
            <div className={styles.statsGrid}>
              <div className={styles.statCard}>
                <div className={styles.statLabel}>数据库名称</div>
                <div className={styles.statValue}>{stats.database_info.name}</div>
              </div>
              <div className={styles.statCard}>
                <div className={styles.statLabel}>表总数</div>
                <div className={styles.statValue}>{stats.database_info.total_tables}</div>
              </div>
              <div className={styles.statCard}>
                <div className={styles.statLabel}>记录总数</div>
                <div className={styles.statValue}>{stats.database_info.total_rows.toLocaleString()}</div>
              </div>
              <div className={styles.statCard}>
                <div className={styles.statLabel}>数据库大小</div>
                <div className={styles.statValue}>{stats.database_info.size_mb} MB</div>
              </div>
            </div>
            
            <div className={styles.tableContainer}>
              <table className={styles.dataTable}>
                <thead>
                  <tr>
                    <th>表名</th>
                    <th>记录数</th>
                    <th>列数</th>
                    <th>大小 (MB)</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  {stats.table_statistics.map((table) => (
                    <tr key={table.name}>
                      <td>{table.name}</td>
                      <td>{table.row_count.toLocaleString()}</td>
                      <td>{table.column_count}</td>
                      <td>{table.size_mb ? `${table.size_mb.toFixed(2)}` : '未知'}</td>
                      <td>
                        <button
                          className={styles.filterButton}
                          onClick={() => handleTableSelect(table.name)}
                        >
                          查看数据
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </>
        )}
      </div>
    </div>
  );

  // 渲染表列表
  const renderTables = () => (
    <div className={styles.contentSection}>
      <div className={styles.sectionHeader}>
        <h3 className={styles.sectionTitle}>数据库表结构</h3>
      </div>
      <div className={styles.sectionBody}>
        <div className={styles.tableContainer}>
          <table className={styles.dataTable}>
            <thead>
              <tr>
                <th>表名</th>
                <th>列数</th>
                <th>记录数</th>
                <th>主键</th>
                <th>外键数</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {tables.map((table) => (
                <tr key={table.name}>
                  <td>{table.name}</td>
                  <td>{table.column_count}</td>
                  <td>{table.row_count.toLocaleString()}</td>
                  <td>{table.primary_keys.join(', ') || '无'}</td>
                  <td>{table.foreign_keys_count}</td>
                  <td>
                    <button
                      className={styles.filterButton}
                      onClick={() => handleTableSelect(table.name)}
                    >
                      查看详情
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  // 渲染数据浏览
  const renderDataBrowser = () => (
    <div className={styles.contentSection}>
      <div className={styles.sectionHeader}>
        <h3 className={styles.sectionTitle}>
          数据浏览 {selectedTable && `- ${selectedTable}`}
        </h3>
      </div>
      <div className={styles.sectionBody}>
        {!selectedTable ? (
          <p style={{ textAlign: 'center', color: '#64748b', padding: '40px' }}>
            请从概览或表结构页面选择一个表来查看数据
          </p>
        ) : (
          <>
            {/* 搜索和筛选 */}
            <div className={styles.filters}>
              <input
                type="text"
                placeholder="搜索..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={styles.searchInput}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <button className={styles.filterButton} onClick={handleSearch}>
                搜索
              </button>
              <select
                value={pageSize}
                onChange={(e) => setPageSize(Number(e.target.value))}
                className={styles.selectInput}
              >
                <option value={10}>10条/页</option>
                <option value={20}>20条/页</option>
                <option value={50}>50条/页</option>
                <option value={100}>100条/页</option>
              </select>
            </div>

            {/* 表结构信息 */}
            {tableSchema && (
              <div className={styles.constraintSection}>
                <h4 className={styles.constraintTitle}>表结构信息</h4>
                <div className={styles.tableContainer}>
                  <table className={styles.dataTable}>
                    <thead>
                      <tr>
                        <th>列名</th>
                        <th>类型</th>
                        <th>可空</th>
                        <th>默认值</th>
                        <th>自增</th>
                        <th>备注</th>
                      </tr>
                    </thead>
                    <tbody>
                      {tableSchema.columns.map((column) => (
                        <tr key={column.name}>
                          <td>{column.name}</td>
                          <td>{column.type}</td>
                          <td>{column.nullable ? '是' : '否'}</td>
                          <td>{column.default || '无'}</td>
                          <td>{column.autoincrement ? '是' : '否'}</td>
                          <td>{column.comment || '无'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* 数据表格 */}
            {tableData && (
              <>
                <div className={styles.tableContainer}>
                  <table className={styles.dataTable}>
                    <thead>
                      <tr>
                        {tableData.columns.map((column) => (
                          <th key={column}>
                            <button
                              style={{ background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }}
                              onClick={() => handleSort(column)}
                            >
                              {column}
                              {sortColumn === column && (
                                <span>{sortDirection === 'asc' ? ' ↑' : ' ↓'}</span>
                              )}
                            </button>
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {tableData.rows.map((row, index) => (
                        <tr key={index}>
                          {tableData.columns.map((column) => (
                            <td key={column}>
                              {row[column] !== null ? String(row[column]) : '(null)'}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* 分页 */}
                <div className={styles.pagination}>
                  <div className={styles.paginationInfo}>
                    显示第 {((tableData.pagination.page - 1) * tableData.pagination.page_size) + 1} - {Math.min(tableData.pagination.page * tableData.pagination.page_size, tableData.pagination.total_count)} 条，
                    共 {tableData.pagination.total_count} 条记录
                  </div>
                  <div className={styles.paginationControls}>
                    <button
                      className={styles.paginationButton}
                      disabled={!tableData.pagination.has_prev}
                      onClick={() => handlePageChange(currentPage - 1)}
                    >
                      上一页
                    </button>
                    <span className={`${styles.paginationButton} ${styles.active}`}>
                      {tableData.pagination.page} / {tableData.pagination.total_pages}
                    </span>
                    <button
                      className={styles.paginationButton}
                      disabled={!tableData.pagination.has_next}
                      onClick={() => handlePageChange(currentPage + 1)}
                    >
                      下一页
                    </button>
                  </div>
                </div>
              </>
            )}
          </>
        )}
      </div>
    </div>
  );

  // 渲染健康检查
  const renderHealthCheck = () => (
    <div className={styles.contentSection}>
      <div className={styles.sectionHeader}>
        <h3 className={styles.sectionTitle}>数据库健康检查</h3>
      </div>
      <div className={styles.sectionBody}>
        {healthStatus && (
          <div className={styles.statsGrid}>
            <div className={styles.statCard}>
              <div className={styles.statLabel}>连接状态</div>
              <div className={`${styles.healthStatus} ${healthStatus.status === 'healthy' ? styles.healthy : styles.unhealthy}`}>
                <div className={styles.healthIndicator}></div>
                {healthStatus.connection}
              </div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statLabel}>活跃连接数</div>
              <div className={styles.statValue}>{healthStatus.active_connections || '未知'}</div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statLabel}>数据库启动时间</div>
              <div className={styles.statValue}>
                {healthStatus.database_start_time ? new Date(healthStatus.database_start_time).toLocaleString() : '未知'}
              </div>
            </div>
            <div className={styles.statCard}>
              <div className={styles.statLabel}>检查时间</div>
              <div className={styles.statValue}>{new Date(healthStatus.timestamp).toLocaleString()}</div>
            </div>
          </div>
        )}
        <button className={styles.filterButton} onClick={fetchHealthCheck}>
          重新检查
        </button>
      </div>
    </div>
  );

  return (
    <div className={styles.adminPanel}>
      <div className={styles.header}>
        <h1 className={styles.title}>数据库管理面板</h1>
        <p className={styles.subtitle}>查看和管理 Memorial 平台数据库</p>
      </div>

      {renderTabs()}

      {error && (
        <div className={styles.error}>
          错误: {error}
        </div>
      )}

      {loading && (
        <div className={styles.loading}>
          <div className={styles.loadingSpinner}></div>
          加载中...
        </div>
      )}

      {!loading && (
        <>
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'tables' && renderTables()}
          {activeTab === 'data' && renderDataBrowser()}
          {activeTab === 'health' && renderHealthCheck()}
        </>
      )}
    </div>
  );
};

export default DatabaseAdminPanel;