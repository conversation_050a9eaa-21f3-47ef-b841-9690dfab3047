// 公共路由组件（未登录用户可访问）
import { ReactNode } from "react";
import ProtectedRoute from "./ProtectedRoute";

interface PublicRouteProps {
  children: ReactNode;
  redirectIfAuthenticated?: boolean; // 已登录时是否重定向
  redirectTo?: string; // 重定向路径
}

function PublicRoute({
  children,
  redirectIfAuthenticated = true,
  redirectTo = "/dashboard",
}: PublicRouteProps) {
  return (
    <ProtectedRoute
      requireAuth={false}
      redirectTo={redirectIfAuthenticated ? redirectTo : undefined}
    >
      {children}
    </ProtectedRoute>
  );
}

export default PublicRoute;
