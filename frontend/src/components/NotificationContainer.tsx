import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import NotificationService, { Notification, NotificationType } from '../utils/NotificationService';

// 单个通知组件
interface NotificationItemProps {
  notification: Notification;
  onClose: (id: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({ notification, onClose }) => {
  const getIcon = () => {
    switch (notification.type) {
      case NotificationType.SUCCESS:
        return (
          <svg className="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case NotificationType.ERROR:
        return (
          <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
        );
      case NotificationType.WARNING:
        return (
          <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case NotificationType.INFO:
      default:
        return (
          <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  const getBackgroundColor = () => {
    switch (notification.type) {
      case NotificationType.SUCCESS:
        return 'bg-green-50 border-green-200';
      case NotificationType.ERROR:
        return 'bg-red-50 border-red-200';
      case NotificationType.WARNING:
        return 'bg-yellow-50 border-yellow-200';
      case NotificationType.INFO:
      default:
        return 'bg-blue-50 border-blue-200';
    }
  };

  const getTitleColor = () => {
    switch (notification.type) {
      case NotificationType.SUCCESS:
        return 'text-green-800';
      case NotificationType.ERROR:
        return 'text-red-800';
      case NotificationType.WARNING:
        return 'text-yellow-800';
      case NotificationType.INFO:
      default:
        return 'text-blue-800';
    }
  };

  const getMessageColor = () => {
    switch (notification.type) {
      case NotificationType.SUCCESS:
        return 'text-green-700';
      case NotificationType.ERROR:
        return 'text-red-700';
      case NotificationType.WARNING:
        return 'text-yellow-700';
      case NotificationType.INFO:
      default:
        return 'text-blue-700';
    }
  };

  const getButtonStyle = (style?: 'primary' | 'secondary') => {
    const baseStyle = "inline-flex items-center px-3 py-1.5 border text-xs font-medium rounded transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2";
    
    switch (notification.type) {
      case NotificationType.SUCCESS:
        return style === 'primary' 
          ? `${baseStyle} border-transparent text-white bg-green-600 hover:bg-green-700 focus:ring-green-500`
          : `${baseStyle} border-green-300 text-green-700 bg-green-100 hover:bg-green-200 focus:ring-green-500`;
      case NotificationType.ERROR:
        return style === 'primary'
          ? `${baseStyle} border-transparent text-white bg-red-600 hover:bg-red-700 focus:ring-red-500`
          : `${baseStyle} border-red-300 text-red-700 bg-red-100 hover:bg-red-200 focus:ring-red-500`;
      case NotificationType.WARNING:
        return style === 'primary'
          ? `${baseStyle} border-transparent text-white bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500`
          : `${baseStyle} border-yellow-300 text-yellow-700 bg-yellow-100 hover:bg-yellow-200 focus:ring-yellow-500`;
      case NotificationType.INFO:
      default:
        return style === 'primary'
          ? `${baseStyle} border-transparent text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500`
          : `${baseStyle} border-blue-300 text-blue-700 bg-blue-100 hover:bg-blue-200 focus:ring-blue-500`;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -50, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -50, scale: 0.95 }}
      className={`max-w-sm w-full ${getBackgroundColor()} shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden`}
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            {getIcon()}
          </div>
          <div className="ml-3 w-0 flex-1 pt-0.5">
            <p className={`text-sm font-medium ${getTitleColor()}`}>
              {notification.title}
            </p>
            <p className={`mt-1 text-sm ${getMessageColor()}`}>
              {notification.message}
            </p>
            {notification.actions && notification.actions.length > 0 && (
              <div className="mt-3 flex space-x-2">
                {notification.actions.map((action, index) => (
                  <button
                    key={index}
                    onClick={action.action}
                    className={getButtonStyle(action.style)}
                  >
                    {action.label}
                  </button>
                ))}
              </div>
            )}
          </div>
          <div className="ml-4 flex-shrink-0 flex">
            <button
              onClick={() => onClose(notification.id)}
              className={`rounded-md inline-flex ${getTitleColor()} hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`}
            >
              <span className="sr-only">关闭</span>
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// 通知容器组件
const NotificationContainer: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  useEffect(() => {
    // 订阅通知更新
    const unsubscribe = NotificationService.addListener((updatedNotifications) => {
      setNotifications(updatedNotifications);
    });

    // 获取当前通知
    setNotifications(NotificationService.getNotifications());

    return unsubscribe;
  }, []);

  const handleClose = (id: string) => {
    NotificationService.removeNotification(id);
  };

  return (
    <div 
      className="fixed inset-0 flex items-end justify-center px-4 py-6 pointer-events-none sm:p-6 sm:items-start sm:justify-end z-50"
      style={{ zIndex: 9999 }}
    >
      <div className="w-full flex flex-col items-center space-y-4 sm:items-end">
        <AnimatePresence>
          {notifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onClose={handleClose}
            />
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default NotificationContainer;