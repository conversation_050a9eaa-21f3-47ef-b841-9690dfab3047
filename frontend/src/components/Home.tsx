import React from "react";
import { Link } from "react-router-dom"; // 使用 react-router-dom进行导航
// import { useTranslation } from 'react-i18next'; // 暂时不使用国际化
// import { motion } from 'framer-motion'; // 暂时不使用动画
import { cn } from "../utils/cn";

// 假设 Navbar 组件已创建或后续会创建
// import Navbar from './Navbar';

const Home: React.FC = () => {
  // const { t } = useTranslation(); // 暂时不使用国际化

  // 从 home.html 提取的数据结构，可以根据需要进行调整或国际化
  // 根据截图调整核心价值内容
  const coreValues = [
    {
      icon: "fas fa-clock", // 图标待确认，截图不清晰，暂用原图标
      title: "不受时空限制",
      description:
        "随时随地，通过互联网便可祭拜追思，不再受限于传统祭祀的时间和地点约束。",
    },
    {
      icon: "fas fa-cubes", // 图标待确认
      title: "3D沉浸体验",
      description:
        "采用最新3D技术，提供逼真的虚拟纪念场景和互动体验，增强情感连接。",
    },
    {
      icon: "fas fa-brain", // 图标待确认
      title: "AI赋能服务",
      description:
        "利用AI技术修复老照片、克隆声音，让先人的音容笑貌更加生动鲜活。",
    },
    {
      icon: "fas fa-users", // 图标待确认
      title: "家族共享传承",
      description: "支持家族成员共同管理和参与，建立数字化族谱，传承家族记忆。",
    },
  ];

  // 根据截图调整主要功能内容和图片
  const features = [
    {
      title: "创建个性化纪念空间",
      description:
        "为逝者创建专属的在线纪念馆，上传照片、视频、音频资料，撰写生平事迹，选择合适的3D场景，打造独一无二的纪念空间。",
      points: [
        "多种3D场景模板选择",
        "照片墙与生平事迹编辑",
        "个性化装饰与背景音乐",
      ],
      imageSrc: "/images/flutter/feature_card_bg_1.png",
      imageAlt: "创建纪念空间",
    },
    {
      title: "在线祭拜与互动",
      description:
        "在3D纪念空间中进行虚拟祭拜，献花、点烛、上香，发表追思留言，与其他亲友共同缅怀。",
      points: ["虚拟祭品选择与摆放", "祭拜动作模拟", "留言祈福板"],
      imageSrc: "/images/flutter/feature_card_bg_2.png",
      imageAlt: "在线祭拜",
      reverse: true,
    },
    {
      title: "AI照片修复与声音克隆",
      description:
        "利用先进的AI技术，修复老旧照片，为黑白照片上色，甚至可以克隆逝者的声音，让记忆更加生动。",
      points: ["老照片修复与上色", "照片3D效果生成", "声音克隆技术"],
      imageSrc: "/images/flutter/feature_card_bg_3.png",
      imageAlt: "AI照片修复与声音克隆",
    },
    {
      title: "智能搜索与发现",
      description:
        "通过强大的搜索功能，快速找到特定的纪念空间、人物或事件，支持多种搜索条件和智能推荐。",
      points: ["多维度搜索筛选", "智能推荐算法", "历史搜索记录"],
      imageSrc: "/images/flutter/feature_card_bg_4.png",
      imageAlt: "智能搜索",
    },
    {
      title: "家族族谱管理",
      description:
        "构建完整的数字化家族族谱，记录家族成员关系，传承家族历史文化，让后代了解家族传统。",
      points: ["可视化族谱图谱", "家族成员关系管理", "家族历史文档存储"],
      imageSrc: "/images/flutter/feature_card_bg_5.png",
      imageAlt: "家族族谱管理",
      reverse: true,
    },
    {
      title: "个人中心与历史",
      description:
        "管理个人账户信息，查看访问历史记录，设置隐私权限，统计纪念空间的访问数据和互动情况。",
      points: ["个人资料管理", "访问历史记录", "数据统计分析"],
      imageSrc: "/images/flutter/feature_card_bg_6.png",
      imageAlt: "个人中心",
    },
  ];

  // 根据截图调整用户评价内容
  const testimonials = [
    {
      avatar: "/images/flutter/avatar_1.svg",
      name: "李明", // 姓名与截图一致
      quote:
        "因为工作原因常年在外，无法经常回家祭拜父亲。归处让我能随时随地表达思念，AI修复的老照片让我重新看到了父亲年轻时的样子，非常感动。", // 内容与截图一致
    },
    {
      avatar: "/images/flutter/avatar_2.svg",
      name: "王芳", // 姓名与截图一致
      quote:
        "奶奶去世后，我们全家都很难过。通过归处平台，我们为奶奶建了一个数字纪念馆，上传了很多珍贵的照片和视频，感觉奶奶从未离开。", // 内容与截图一致
    },
    {
      avatar: "/images/flutter/avatar_3.svg",
      name: "张伟", // 姓名与截图一致
      quote:
        "归处的3D场景做得很逼真，在线上香、献花的感觉很庄重。家族成员可以一起管理，对于传承家族记忆非常有意义。", // 内容与截图一致
    },
  ];

  return (
    <div className={cn("min-h-screen bg-background-primary")}>
      {/* Navbar 组件已集成在下方，此处注释可移除 */}
      {/* 导航栏 - 对应 home.html nav */}
      {/* 导航栏 - 样式根据截图调整 */}
      <nav className={cn("bg-primary-dark shadow-md sticky top-0 z-50")}>
        <div
          className={cn(
            "container mx-auto px-4 py-3 flex justify-between items-center",
          )}
        >
          <div className={cn("flex items-center")}>
            {/* Placeholder for custom logo icon, using Font Awesome for now */}
            <i className="fa-solid fa-spa text-secondary text-3xl mr-2" />
            <span className={cn("text-2xl font-bold text-text-inverse")}>
              归处
            </span>
          </div>
          <div className={cn("hidden md:flex space-x-6 lg:space-x-8")}>
            <Link
              to="/"
              className={cn(
                "text-gray-300 hover:text-text-inverse font-medium transition-colors duration-200",
              )}
            >
              首页
            </Link>
            <a
              href="#core-values-section"
              className={cn(
                "text-gray-300 hover:text-text-inverse font-medium transition-colors duration-200",
              )}
            >
              功能介绍
            </a>
            <Link
              to="/heroes"
              className={cn(
                "text-gray-300 hover:text-text-inverse font-medium transition-colors duration-200",
              )}
            >
              英雄纪念馆
            </Link>
            <Link
              to="/about"
              className={cn(
                "text-gray-300 hover:text-text-inverse font-medium transition-colors duration-200",
              )}
            >
              关于我们
            </Link>
          </div>
          <div className={cn("flex items-center space-x-4")}>
            <Link
              to="/login"
              className={cn(
                "text-gray-300 hover:text-text-inverse font-medium transition-colors duration-200",
              )}
            >
              登录
            </Link>
            <Link
              to="/register"
              className={cn(
                "bg-secondary hover:bg-secondary-dark text-text-inverse px-5 py-2.5 rounded-md",
                "transition-all duration-300 text-sm font-medium transform hover:scale-105",
              )}
            >
              注册
            </Link>
          </div>
        </div>
      </nav>

      {/* 英雄区域 - 对应 home.html hero-section */}
      {/* 英雄区域 - 背景图和文字内容根据截图调整 */}
      <section
        className={cn(
          "relative flex items-center justify-center text-center text-text-inverse min-h-[600px] p-16 overflow-hidden",
          "bg-cover bg-center bg-no-repeat",
        )}
        style={{
          backgroundImage: `url("/images/flutter/hero_background.jpg")`,
        }}
      >
        {/* 背景渐变叠加层 */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary-dark/80 via-primary/60 to-secondary/40" />

        {/* 装饰性几何图形 */}
        <div className="absolute top-10 left-10 w-32 h-32 bg-secondary/20 rounded-full blur-xl" />
        <div className="absolute bottom-20 right-20 w-48 h-48 bg-primary/30 rounded-full blur-2xl" />
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-white/10 rounded-full blur-lg" />

        <div className={cn("relative z-10 container mx-auto px-4 text-center")}>
          <h1
            className={cn(
              "text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 text-white",
              "drop-shadow-2xl",
            )}
          >
            跨越时空的思念
          </h1>
          <p
            className={cn(
              "text-lg sm:text-xl md:text-2xl mb-12 max-w-4xl mx-auto text-white",
              "p-6",
            )}
          >
            归处为您提供一个不受时间、地点限制的个性化在线纪念空间，让思念永不消逝
          </p>
          <div
            className={cn(
              "flex flex-col sm:flex-row justify-center items-center gap-6",
            )}
          >
            <Link
              to="/register"
              className={cn(
                "group relative bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600",
                "text-black px-10 py-5 rounded-2xl transition-all duration-500 text-lg font-bold",
                "transform hover:scale-110 hover:-translate-y-1 shadow-2xl hover:shadow-yellow-500/50",
                "border-2 border-yellow-400/30",
                "before:absolute before:inset-0 before:rounded-2xl before:bg-gradient-to-r before:from-white/20 before:to-transparent before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
              )}
            >
              <span className="relative z-10 flex items-center gap-2">
                <i className="fas fa-plus-circle group-hover:rotate-90 transition-transform duration-300" />
                开始创建纪念空间
              </span>
            </Link>
            <a
              href="#core-values-section"
              className={cn(
                "group relative border-2 border-white/50 text-white hover:bg-white/10",
                "px-10 py-5 rounded-2xl transition-all duration-500 text-lg font-bold",
                "transform hover:scale-110 hover:-translate-y-1 shadow-2xl hover:shadow-white/30",
                "backdrop-blur-md bg-white/5",
                "before:absolute before:inset-0 before:rounded-2xl before:bg-gradient-to-r before:from-primary/20 before:to-secondary/20 before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
              )}
            >
              <span className="relative z-10 flex items-center gap-2">
                <i className="fas fa-heart group-hover:text-secondary transition-colors duration-300" />
                了解更多
              </span>
            </a>
          </div>
        </div>
      </section>

      {/* 核心价值区域 - 对应 home.html core values section */}
      <section
        id="core-values-section"
        className="py-16 bg-background-secondary"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-text-primary mb-4">
              核心价值
            </h2>
            <p className="text-lg text-text-secondary max-w-3xl mx-auto">
              我们致力于为每一个生命创造永恒的数字纪念空间
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {coreValues.map((value, index) => (
              <div
                key={index}
                className={cn(
                  "group relative bg-gradient-to-br from-white to-gray-50/50 p-8 rounded-2xl",
                  "shadow-lg hover:shadow-xl transition-all duration-300 border-2",
                  "hover:-translate-y-1",
                  "before:absolute before:inset-0 before:rounded-2xl before:bg-gradient-to-br before:from-primary/5 before:to-secondary/5",
                  "before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
                )}
                style={{ borderColor: "#185a56" }}
              >
                <div className="relative z-10 text-center">
                  <div
                    className={cn(
                      "inline-flex items-center justify-center w-16 h-16 mb-6 rounded-full",
                      "bg-gradient-to-br from-secondary/20 to-primary/20 group-hover:from-secondary/30 group-hover:to-primary/30",
                      "transition-all duration-300",
                    )}
                  >
                    <i
                      className={`${value.icon} text-2xl text-primary group-hover:text-secondary transition-colors duration-300`}
                    ></i>
                  </div>
                  <h3 className="text-xl font-bold text-text-primary mb-4 group-hover:text-primary transition-colors duration-300">
                    {value.title}
                  </h3>
                  <p className="text-text-secondary leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                    {value.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 主要功能 - 双列网格布局 */}
      <section
        className={cn(
          "py-20 bg-gradient-to-br from-primary-light to-secondary-light",
        )}
        id="main-features-section"
      >
        <div className={cn("container mx-auto px-4")}>
          <h2
            className={cn(
              "text-3xl md:text-4xl font-bold text-center mb-16 text-text-primary",
            )}
          >
            主要功能
          </h2>
          <div
            className={cn(
              "grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-7xl mx-auto",
            )}
          >
            {features.map((feature, index) => (
              <div
                key={index}
                className={cn(
                  "group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300",
                  "border-2 border-secondary overflow-hidden hover:-translate-y-1",
                )}
              >
                <div className={cn("relative overflow-hidden")}>
                  <div
                    className={cn(
                      "relative h-64 overflow-hidden",
                      "before:absolute before:inset-0 before:bg-gradient-to-br before:from-primary/10 before:to-secondary/10",
                      "before:opacity-0 group-hover:before:opacity-100 before:transition-opacity before:duration-300",
                      "after:absolute after:inset-0 after:bg-gradient-to-t after:from-black/20 after:to-transparent",
                    )}
                  >
                    <img
                      src={feature.imageSrc}
                      alt={feature.imageAlt}
                      className={cn(
                        "w-full h-full object-cover transition-all duration-700",
                        "group-hover:scale-110 filter group-hover:brightness-110 group-hover:contrast-105",
                      )}
                    />
                    <div
                      className={cn(
                        "absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent",
                        "opacity-0 group-hover:opacity-100 transition-opacity duration-300",
                      )}
                    >
                      <div
                        className={cn(
                          "absolute bottom-6 left-6 right-6 text-white transform translate-y-4",
                          "group-hover:translate-y-0 transition-transform duration-300",
                        )}
                      >
                        <h4 className="text-xl font-bold mb-2">
                          {feature.title}
                        </h4>
                        <p className="text-sm opacity-90 line-clamp-2">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div className={cn("p-8")}>
                  <h3
                    className={cn(
                      "text-xl md:text-2xl font-semibold mb-4 text-text-primary group-hover:text-primary transition-colors duration-300",
                    )}
                  >
                    {feature.title}
                  </h3>
                  <p
                    className={cn(
                      "text-text-secondary mb-6 text-base leading-relaxed",
                    )}
                  >
                    {feature.description}
                  </p>
                  <ul
                    className={cn("space-y-3 text-left w-full list-none pl-0")}
                  >
                    {feature.points.map((point, pIndex) => (
                      <li
                        key={pIndex}
                        className={cn("flex items-start text-text-secondary")}
                      >
                        <i className="fa-solid fa-check-circle text-secondary mr-3 mt-1 flex-shrink-0" />
                        <span className="text-sm">{point}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 用户评价 - 样式和内容根据截图调整 */}
      <section className={cn("py-12 md:py-16 bg-background-secondary")}>
        <div className={cn("container mx-auto px-4")}>
          <h2
            className={cn(
              "text-3xl md:text-4xl font-bold text-center mb-8 md:mb-10 text-text-primary",
            )}
          >
            用户评价
          </h2>
          <div className={cn("grid grid-cols-1 gap-6 max-w-4xl mx-auto")}>
            {testimonials.map((testimonial, index) => {
              // 卡通头像图片数组 - 使用更可靠的头像源
              const avatarImages = [
                `data:image/svg+xml;base64,${btoa(`
                   <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64">
                     <circle cx="32" cy="32" r="32" fill="#e3f2fd"/>
                     <circle cx="32" cy="24" r="8" fill="#1976d2"/>
                     <path d="M16 56c0-8.8 7.2-16 16-16s16 7.2 16 16" fill="#1976d2"/>
                   </svg>
                 `)}`,
                `data:image/svg+xml;base64,${btoa(`
                   <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64">
                     <circle cx="32" cy="32" r="32" fill="#fce4ec"/>
                     <circle cx="32" cy="24" r="8" fill="#e91e63"/>
                     <path d="M16 56c0-8.8 7.2-16 16-16s16 7.2 16 16" fill="#e91e63"/>
                   </svg>
                 `)}`,
                `data:image/svg+xml;base64,${btoa(`
                   <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64">
                     <circle cx="32" cy="32" r="32" fill="#e8f5e8"/>
                     <circle cx="32" cy="24" r="8" fill="#4caf50"/>
                     <path d="M16 56c0-8.8 7.2-16 16-16s16 7.2 16 16" fill="#4caf50"/>
                   </svg>
                 `)}`,
              ];

              return (
                <div
                  key={index}
                  className={cn(
                    "bg-gradient-to-br from-white to-gray-50 p-4 md:p-6 rounded-2xl shadow-lg flex flex-col items-start text-left",
                    "border-2 border-gray-100 hover:border-secondary/30 hover:shadow-xl transition-all duration-300",
                    "transform hover:-translate-y-1",
                  )}
                >
                  <div className={cn("flex items-center mb-3 w-full")}>
                    <img
                      src={avatarImages[index % avatarImages.length]}
                      alt={`${testimonial.name} 头像`}
                      className={cn(
                        "w-12 h-12 rounded-full mr-3 object-cover flex-shrink-0",
                        "border-2 border-secondary/30 shadow-md",
                      )}
                    />
                    <div className={cn("flex-grow")}>
                      <h4 className={cn("font-bold text-text-primary text-lg")}>
                        {testimonial.name}
                      </h4>
                      <div
                        className={cn("text-secondary flex items-center mt-1")}
                      >
                        {[...Array(5)].map((_, i) => (
                          <i key={i} className="fa-solid fa-star text-sm" />
                        ))}
                      </div>
                    </div>
                  </div>
                  <div
                    className={cn(
                      "bg-gray-50 rounded-xl p-3 border-l-4 border-secondary/50 w-full",
                    )}
                  >
                    <p
                      className={cn(
                        "text-text-secondary text-sm md:text-base leading-relaxed italic",
                      )}
                    >
                      "{testimonial.quote}"
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* 新增：开始您的在线纪念之旅 CTA Section */}
      <section className={cn("py-16 md:py-24 bg-primary text-white")}>
        <div className={cn("container mx-auto px-4 text-center")}>
          <h2 className={cn("text-3xl md:text-4xl font-bold mb-4 text-white")}>
            开始您的在线纪念之旅
          </h2>
          <p
            className={cn(
              "text-lg md:text-xl mb-8 max-w-2xl mx-auto text-white/90",
            )}
          >
            创建一个永恒的纪念空间，让思念与记忆永不消逝。
          </p>
          <Link
            to="/register"
            className={cn(
              "bg-secondary text-white font-semibold py-3 px-10 rounded-lg text-lg",
              "hover:bg-secondary-600 transition duration-300 shadow-md transform hover:scale-105",
            )}
          >
            立即注册
          </Link>
        </div>
      </section>

      {/* 页脚 - 样式和内容根据截图调整 */}
      <footer className={cn("bg-primary text-white py-6 md:py-8")}>
        <div className={cn("container mx-auto px-4")}>
          <div
            className={cn(
              "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4 text-left",
            )}
          >
            <div>
              <div className={cn("flex items-center mb-4")}>
                <i className="fa-solid fa-spa text-secondary text-2xl mr-2" />
                <h5 className={cn("text-xl font-bold text-white")}>归处</h5>
              </div>
              <p className={cn("text-sm text-white")}>
                致力于提供一个充满关怀与尊重的在线纪念平台，让思念跨越时空，永存心间。
              </p>
            </div>
            <div>
              <h5 className={cn("text-lg font-semibold mb-4 text-white")}>
                快速链接
              </h5>
              <ul className={cn("space-y-2 text-sm")}>
                <li>
                  <a
                    href="#main-features-section"
                    className={cn(
                      "text-white hover:text-secondary transition-colors",
                    )}
                  >
                    功能介绍
                  </a>
                </li>
                <li>
                  <Link
                    to="/heroes"
                    className={cn(
                      "text-white hover:text-secondary transition-colors",
                    )}
                  >
                    英雄纪念馆
                  </Link>
                </li>
                <li>
                  <Link
                    to="/about"
                    className={cn(
                      "text-white hover:text-secondary transition-colors",
                    )}
                  >
                    关于我们
                  </Link>
                </li>
                <li>
                  <Link
                    to="/contact"
                    className={cn(
                      "text-white hover:text-secondary transition-colors",
                    )}
                  >
                    联系方式
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h5 className={cn("text-lg font-semibold mb-4 text-white")}>
                帮助与支持
              </h5>
              <ul className={cn("space-y-2 text-sm")}>
                <li>
                  <Link
                    to="/faq"
                    className={cn(
                      "text-white hover:text-secondary transition-colors",
                    )}
                  >
                    常见问题
                  </Link>
                </li>
                <li>
                  <Link
                    to="/contact"
                    className={cn(
                      "text-white hover:text-secondary transition-colors",
                    )}
                  >
                    联系我们
                  </Link>
                </li>
                <li>
                  <Link
                    to="/privacy"
                    className={cn(
                      "text-white hover:text-secondary transition-colors",
                    )}
                  >
                    隐私政策
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h5 className={cn("text-lg font-semibold mb-4 text-white")}>
                关注我们
              </h5>
              <div className={cn("flex space-x-4")}>
                <a
                  href="#"
                  aria-label="微信公众号"
                  className={cn(
                    "text-white hover:text-secondary transition-colors",
                  )}
                >
                  <i className="fab fa-weixin text-2xl" />
                </a>
                <a
                  href="#"
                  aria-label="微博"
                  className={cn(
                    "text-white hover:text-secondary transition-colors",
                  )}
                >
                  <i className="fab fa-weibo text-2xl" />
                </a>
                <a
                  href="#"
                  aria-label="抖音"
                  className={cn(
                    "text-white hover:text-secondary transition-colors",
                  )}
                >
                  <i className="fab fa-tiktok text-2xl" />
                </a>
              </div>
              <p className={cn("text-sm mt-4 text-white")}>
                客服热线: 400-889-0919
              </p>
            </div>
          </div>
          <div
            className={cn(
              "border-t border-gray-600 pt-3 text-center text-sm text-white",
            )}
          >
            <p className="!text-white text-center">
              &copy; {new Date().getFullYear()} 归处 |
              海南长小养智能科技有限责任公司 版权所有
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;
