import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import styles from './MembershipBenefitsManager.module.css';

interface UserPlan {
  id: string;
  name: string;
  tier: 'basic' | 'premium' | 'family' | 'enterprise';
  status: 'active' | 'cancelled' | 'expired' | 'trial';
  startDate: string;
  endDate: string;
  autoRenew: boolean;
  usage: UsageData;
  features: PlanFeature[];
}

interface UsageData {
  memorialSpaces: { used: number; limit: number };
  aiEnhancements: { used: number; limit: number };
  storage: { used: number; limit: number; unit: 'MB' | 'GB' };
  virtualProducts: { used: number; limit: number };
  familyMembers: { used: number; limit: number };
  customizations: { used: number; limit: number };
}

interface PlanFeature {
  id: string;
  name: string;
  description: string;
  available: boolean;
  isNew?: boolean;
  isPremium?: boolean;
  usageLimit?: number;
  currentUsage?: number;
}

interface UpgradeRecommendation {
  targetPlan: string;
  reason: string;
  benefits: string[];
  urgency: 'low' | 'medium' | 'high';
  discount?: number;
}

export const MembershipBenefitsManager: React.FC = () => {
  const { } = useAuth();
  const [currentPlan, setCurrentPlan] = useState<UserPlan | null>(null);
  const [availablePlans, setAvailablePlans] = useState<UserPlan[]>([]);
  const [upgradeRecommendations, setUpgradeRecommendations] = useState<UpgradeRecommendation[]>([]);
  const [loading, setLoading] = useState(true);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [selectedUpgradePlan, setSelectedUpgradePlan] = useState<string | null>(null);

  useEffect(() => {
    loadMembershipData();
  }, []);

  const loadMembershipData = async () => {
    setLoading(true);
    try {
      // 获取当前用户的订阅计划
      const planData = await getMembershipPlan();
      setCurrentPlan(planData);

      // 获取可用的升级计划
      const plans = await getAvailablePlans();
      setAvailablePlans(plans);

      // 获取智能升级建议
      const recommendations = await getUpgradeRecommendations(planData);
      setUpgradeRecommendations(recommendations);
    } catch (error) {
      console.error('Failed to load membership data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getMembershipPlan = async (): Promise<UserPlan> => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800));
    
    return {
      id: 'plan_premium_001',
      name: '高级版',
      tier: 'premium',
      status: 'active',
      startDate: '2024-01-15',
      endDate: '2025-01-15',
      autoRenew: true,
      usage: {
        memorialSpaces: { used: 3, limit: 10 },
        aiEnhancements: { used: 15, limit: 50 },
        storage: { used: 1.2, limit: 5, unit: 'GB' },
        virtualProducts: { used: 8, limit: 100 },
        familyMembers: { used: 5, limit: 20 },
        customizations: { used: 12, limit: -1 }, // -1 表示无限制
      },
      features: [
        {
          id: 'memorial_spaces',
          name: '纪念空间',
          description: '创建和管理多个个性化纪念空间',
          available: true,
          usageLimit: 10,
          currentUsage: 3,
        },
        {
          id: 'ai_photo_restoration',
          name: 'AI照片修复',
          description: '智能修复老照片，提升图片质量',
          available: true,
          usageLimit: 50,
          currentUsage: 15,
        },
        {
          id: 'virtual_products',
          name: '虚拟祭品',
          description: '购买和使用3D虚拟祭品',
          available: true,
          usageLimit: 100,
          currentUsage: 8,
        },
        {
          id: 'family_sharing',
          name: '家族共享',
          description: '邀请家族成员共同管理纪念空间',
          available: true,
          usageLimit: 20,
          currentUsage: 5,
        },
        {
          id: 'priority_support',
          name: '优先客服',
          description: '享受24/7优先技术支持服务',
          available: true,
          isPremium: true,
        },
        {
          id: 'voice_clone',
          name: '语音克隆',
          description: '生成逝者声音的AI语音克隆',
          available: false,
          isPremium: true,
          isNew: true,
        },
        {
          id: 'ar_experience',
          name: 'AR体验',
          description: '增强现实纪念空间体验',
          available: false,
          isPremium: true,
          isNew: true,
        },
      ],
    };
  };

  const getAvailablePlans = async (): Promise<UserPlan[]> => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return [
      {
        id: 'plan_family_001',
        name: '家族版',
        tier: 'family',
        status: 'active',
        startDate: '',
        endDate: '',
        autoRenew: false,
        usage: {
          memorialSpaces: { used: 0, limit: 50 },
          aiEnhancements: { used: 0, limit: 200 },
          storage: { used: 0, limit: 20, unit: 'GB' },
          virtualProducts: { used: 0, limit: 500 },
          familyMembers: { used: 0, limit: 100 },
          customizations: { used: 0, limit: -1 },
        },
        features: [],
      },
      {
        id: 'plan_enterprise_001',
        name: '企业版',
        tier: 'enterprise',
        status: 'active',
        startDate: '',
        endDate: '',
        autoRenew: false,
        usage: {
          memorialSpaces: { used: 0, limit: -1 },
          aiEnhancements: { used: 0, limit: -1 },
          storage: { used: 0, limit: 100, unit: 'GB' },
          virtualProducts: { used: 0, limit: -1 },
          familyMembers: { used: 0, limit: -1 },
          customizations: { used: 0, limit: -1 },
        },
        features: [],
      },
    ];
  };

  const getUpgradeRecommendations = async (plan: UserPlan): Promise<UpgradeRecommendation[]> => {
    // 基于使用情况生成智能升级建议
    const recommendations: UpgradeRecommendation[] = [];

    // 分析存储使用情况
    const storageUsagePercent = (plan.usage.storage.used / plan.usage.storage.limit) * 100;
    if (storageUsagePercent > 80) {
      recommendations.push({
        targetPlan: 'family',
        reason: '存储空间即将用完',
        benefits: ['20GB存储空间', '更多纪念空间', '支持更多家族成员'],
        urgency: 'high',
        discount: 20,
      });
    }

    // 分析AI使用情况
    const aiUsagePercent = (plan.usage.aiEnhancements.used / plan.usage.aiEnhancements.limit) * 100;
    if (aiUsagePercent > 70) {
      recommendations.push({
        targetPlan: 'family',
        reason: 'AI增强服务使用频繁',
        benefits: ['200次AI增强', '语音克隆功能', 'AR体验'],
        urgency: 'medium',
      });
    }

    // 分析家族成员数量
    const familyUsagePercent = (plan.usage.familyMembers.used / plan.usage.familyMembers.limit) * 100;
    if (familyUsagePercent > 60) {
      recommendations.push({
        targetPlan: 'family',
        reason: '家族成员较多',
        benefits: ['支持100个家族成员', '协作管理功能', '批量邀请工具'],
        urgency: 'medium',
      });
    }

    return recommendations;
  };

  const handleUpgrade = (planId: string) => {
    setSelectedUpgradePlan(planId);
    setShowUpgradeModal(true);
  };

  const confirmUpgrade = async () => {
    if (!selectedUpgradePlan) return;

    try {
      // 调用升级API
      await upgradePlan(selectedUpgradePlan);
      
      // 重新加载数据
      await loadMembershipData();
      
      setShowUpgradeModal(false);
      setSelectedUpgradePlan(null);
    } catch (error) {
      console.error('Failed to upgrade plan:', error);
    }
  };

  const upgradePlan = async (_planId: string) => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
  };

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>正在加载会员信息...</p>
      </div>
    );
  }

  if (!currentPlan) {
    return (
      <div className={styles.error}>
        <p>无法加载会员信息</p>
        <button onClick={loadMembershipData}>重试</button>
      </div>
    );
  }

  return (
    <div className={styles.membershipManager}>
      {/* 当前计划概览 */}
      <div className={styles.currentPlan}>
        <div className={styles.planHeader}>
          <div className={styles.planInfo}>
            <h2>{currentPlan.name}</h2>
            <span className={`${styles.status} ${styles[currentPlan.status]}`}>
              {currentPlan.status === 'active' ? '正常' : 
               currentPlan.status === 'trial' ? '试用中' :
               currentPlan.status === 'expired' ? '已过期' : '已取消'}
            </span>
          </div>
          <div className={styles.planDates}>
            <p>有效期: {currentPlan.startDate} - {currentPlan.endDate}</p>
            <p className={styles.autoRenew}>
              自动续费: {currentPlan.autoRenew ? '已开启' : '已关闭'}
            </p>
          </div>
        </div>

        {/* 使用情况仪表板 */}
        <div className={styles.usageDashboard}>
          <h3>使用情况</h3>
          <div className={styles.usageGrid}>
            <UsageCard
              title="纪念空间"
              used={currentPlan.usage.memorialSpaces.used}
              limit={currentPlan.usage.memorialSpaces.limit}
              unit="个"
            />
            <UsageCard
              title="AI增强"
              used={currentPlan.usage.aiEnhancements.used}
              limit={currentPlan.usage.aiEnhancements.limit}
              unit="次"
            />
            <UsageCard
              title="存储空间"
              used={currentPlan.usage.storage.used}
              limit={currentPlan.usage.storage.limit}
              unit={currentPlan.usage.storage.unit}
            />
            <UsageCard
              title="虚拟祭品"
              used={currentPlan.usage.virtualProducts.used}
              limit={currentPlan.usage.virtualProducts.limit}
              unit="个"
            />
            <UsageCard
              title="家族成员"
              used={currentPlan.usage.familyMembers.used}
              limit={currentPlan.usage.familyMembers.limit}
              unit="人"
            />
            <UsageCard
              title="定制服务"
              used={currentPlan.usage.customizations.used}
              limit={currentPlan.usage.customizations.limit}
              unit="次"
              unlimited={currentPlan.usage.customizations.limit === -1}
            />
          </div>
        </div>
      </div>

      {/* 功能权限列表 */}
      <div className={styles.features}>
        <h3>功能权限</h3>
        <div className={styles.featureGrid}>
          {currentPlan.features.map(feature => (
            <FeatureCard
              key={feature.id}
              feature={feature}
              onUpgrade={() => handleUpgrade('family')}
            />
          ))}
        </div>
      </div>

      {/* 升级建议 */}
      {upgradeRecommendations.length > 0 && (
        <div className={styles.recommendations}>
          <h3>升级建议</h3>
          <div className={styles.recommendationList}>
            {upgradeRecommendations.map((rec, index) => (
              <RecommendationCard
                key={index}
                recommendation={rec}
                onUpgrade={() => handleUpgrade(rec.targetPlan)}
              />
            ))}
          </div>
        </div>
      )}

      {/* 可升级计划 */}
      <div className={styles.availablePlans}>
        <h3>升级选项</h3>
        <div className={styles.planGrid}>
          {availablePlans.map(plan => (
            <PlanCard
              key={plan.id}
              plan={plan}
              currentTier={currentPlan.tier}
              onUpgrade={() => handleUpgrade(plan.id)}
            />
          ))}
        </div>
      </div>

      {/* 升级确认弹窗 */}
      {showUpgradeModal && (
        <UpgradeModal
          planName={availablePlans.find(p => p.id === selectedUpgradePlan)?.name || ''}
          onConfirm={confirmUpgrade}
          onCancel={() => {
            setShowUpgradeModal(false);
            setSelectedUpgradePlan(null);
          }}
        />
      )}
    </div>
  );
};

// 使用情况卡片组件
interface UsageCardProps {
  title: string;
  used: number;
  limit: number;
  unit: string;
  unlimited?: boolean;
}

const UsageCard: React.FC<UsageCardProps> = ({ title, used, limit, unit, unlimited = false }) => {
  const percentage = unlimited ? 0 : (used / limit) * 100;
  const isNearLimit = percentage > 80;
  const isOverLimit = percentage > 100;

  return (
    <div className={`${styles.usageCard} ${isNearLimit ? styles.warning : ''} ${isOverLimit ? styles.danger : ''}`}>
      <div className={styles.usageHeader}>
        <h4>{title}</h4>
        <span className={styles.usageText}>
          {unlimited ? `${used} ${unit}` : `${used} / ${limit} ${unit}`}
        </span>
      </div>
      
      {!unlimited && (
        <div className={styles.progressBar}>
          <div 
            className={styles.progress}
            style={{ width: `${Math.min(percentage, 100)}%` }}
          />
        </div>
      )}
      
      {unlimited && (
        <div className={styles.unlimited}>无限制</div>
      )}
      
      {isNearLimit && !isOverLimit && (
        <div className={styles.warningText}>即将达到限制</div>
      )}
      
      {isOverLimit && (
        <div className={styles.dangerText}>已超出限制</div>
      )}
    </div>
  );
};

// 功能卡片组件
interface FeatureCardProps {
  feature: PlanFeature;
  onUpgrade: () => void;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ feature, onUpgrade }) => {
  return (
    <div className={`${styles.featureCard} ${feature.available ? styles.available : styles.unavailable}`}>
      <div className={styles.featureHeader}>
        <h4>{feature.name}</h4>
        <div className={styles.featureBadges}>
          {feature.isNew && <span className={styles.newBadge}>新功能</span>}
          {feature.isPremium && <span className={styles.premiumBadge}>高级</span>}
        </div>
      </div>
      
      <p className={styles.featureDescription}>{feature.description}</p>
      
      {feature.usageLimit && feature.currentUsage !== undefined && (
        <div className={styles.featureUsage}>
          <span>使用: {feature.currentUsage} / {feature.usageLimit}</span>
          <div className={styles.featureProgress}>
            <div 
              className={styles.progress}
              style={{ width: `${(feature.currentUsage / feature.usageLimit) * 100}%` }}
            />
          </div>
        </div>
      )}
      
      {!feature.available && (
        <button className={styles.upgradeButton} onClick={onUpgrade}>
          升级解锁
        </button>
      )}
    </div>
  );
};

// 升级建议卡片组件
interface RecommendationCardProps {
  recommendation: UpgradeRecommendation;
  onUpgrade: () => void;
}

const RecommendationCard: React.FC<RecommendationCardProps> = ({ recommendation, onUpgrade }) => {
  return (
    <div className={`${styles.recommendationCard} ${styles[recommendation.urgency]}`}>
      <div className={styles.recommendationHeader}>
        <h4>{recommendation.reason}</h4>
        <span className={styles.urgencyBadge}>
          {recommendation.urgency === 'high' ? '紧急' :
           recommendation.urgency === 'medium' ? '建议' : '可选'}
        </span>
      </div>
      
      <div className={styles.benefits}>
        <p>升级到{recommendation.targetPlan}版可获得:</p>
        <ul>
          {recommendation.benefits.map((benefit, index) => (
            <li key={index}>{benefit}</li>
          ))}
        </ul>
      </div>
      
      <div className={styles.recommendationFooter}>
        {recommendation.discount && (
          <span className={styles.discount}>限时优惠 {recommendation.discount}% OFF</span>
        )}
        <button className={styles.upgradeButton} onClick={onUpgrade}>
          立即升级
        </button>
      </div>
    </div>
  );
};

// 计划卡片组件
interface PlanCardProps {
  plan: UserPlan;
  currentTier: string;
  onUpgrade: () => void;
}

const PlanCard: React.FC<PlanCardProps> = ({ plan, currentTier, onUpgrade }) => {
  const isUpgrade = getTierLevel(plan.tier) > getTierLevel(currentTier);
  
  return (
    <div className={styles.planCard}>
      <div className={styles.planCardHeader}>
        <h4>{plan.name}</h4>
        <span className={styles.tier}>{plan.tier.toUpperCase()}</span>
      </div>
      
      <div className={styles.planFeatures}>
        <div className={styles.featureItem}>
          <span>纪念空间: </span>
          <span>{plan.usage.memorialSpaces.limit === -1 ? '无限制' : `${plan.usage.memorialSpaces.limit}个`}</span>
        </div>
        <div className={styles.featureItem}>
          <span>AI增强: </span>
          <span>{plan.usage.aiEnhancements.limit === -1 ? '无限制' : `${plan.usage.aiEnhancements.limit}次`}</span>
        </div>
        <div className={styles.featureItem}>
          <span>存储空间: </span>
          <span>{plan.usage.storage.limit === -1 ? '无限制' : `${plan.usage.storage.limit}${plan.usage.storage.unit}`}</span>
        </div>
        <div className={styles.featureItem}>
          <span>家族成员: </span>
          <span>{plan.usage.familyMembers.limit === -1 ? '无限制' : `${plan.usage.familyMembers.limit}人`}</span>
        </div>
      </div>
      
      {isUpgrade && (
        <button className={styles.upgradeButton} onClick={onUpgrade}>
          升级到{plan.name}
        </button>
      )}
    </div>
  );
};

// 升级确认弹窗组件
interface UpgradeModalProps {
  planName: string;
  onConfirm: () => void;
  onCancel: () => void;
}

const UpgradeModal: React.FC<UpgradeModalProps> = ({ planName, onConfirm, onCancel }) => {
  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modal}>
        <div className={styles.modalHeader}>
          <h3>确认升级</h3>
        </div>
        
        <div className={styles.modalContent}>
          <p>您确定要升级到 <strong>{planName}</strong> 吗？</p>
          <p>升级后将立即生效，费用将按比例计算。</p>
        </div>
        
        <div className={styles.modalActions}>
          <button className={styles.cancelButton} onClick={onCancel}>
            取消
          </button>
          <button className={styles.confirmButton} onClick={onConfirm}>
            确认升级
          </button>
        </div>
      </div>
    </div>
  );
};

// 辅助函数
const getTierLevel = (tier: string): number => {
  const levels = {
    'basic': 1,
    'premium': 2,
    'family': 3,
    'enterprise': 4,
  };
  return levels[tier as keyof typeof levels] || 0;
};

export default MembershipBenefitsManager;