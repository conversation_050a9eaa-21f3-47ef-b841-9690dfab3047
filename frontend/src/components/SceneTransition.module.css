/* CSS Modules 样式文件 */
/* 使用 .module.css 后缀启用 CSS Modules */

/* 注意: 检测到可能的全局样式，请考虑使用 :global() 包装或移至全局样式文件 */
.scene-transition-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  perspective: 1200px;
}

.scene-transition-content {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  will-change: transform, opacity, filter;
}

/* 特殊效果的额外样式 */
.scene-transition-blur {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.scene-transition-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(var(--color-gray-900-rgb), 0.5);
  z-index: var(--z-modal);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.scene-transition-overlay.active {
  opacity: 1;
}
