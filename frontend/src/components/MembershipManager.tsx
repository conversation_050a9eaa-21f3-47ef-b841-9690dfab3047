import React, { useState, useEffect } from 'react';
import { membershipService } from '../services/membershipService';
import { MembershipSummary, TierInfo } from '../types/membership';
import styles from './MembershipManager.module.css';

interface MembershipManagerProps {
  onUpgradeClick?: (tier: string) => void;
  showUpgradePrompt?: boolean;
}

const MembershipManager: React.FC<MembershipManagerProps> = ({
  onUpgradeClick,
  showUpgradePrompt = true
}) => {
  const [summary, setSummary] = useState<MembershipSummary | null>(null);
  const [allTiers, setAllTiers] = useState<TierInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'usage' | 'tiers'>('overview');

  useEffect(() => {
    loadMembershipData();
  }, []);

  const loadMembershipData = async () => {
    try {
      setLoading(true);
      const [summaryData, tiersData] = await Promise.all([
        membershipService.getMembershipSummary(),
        membershipService.getAllTiers()
      ]);
      setSummary(summaryData);
      setAllTiers(tiersData);
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载会员信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = (tier: string) => {
    if (onUpgradeClick) {
      onUpgradeClick(tier);
    }
  };

  const getUsageColor = (percentage: number | null): string => {
    if (percentage === null) return 'var(--color-text-secondary)';
    if (percentage >= 90) return 'var(--color-error)';
    if (percentage >= 70) return 'var(--color-warning)';
    return 'var(--color-success)';
  };

  const formatLimit = (limit: any): string => {
    if (limit === -1) return '无限制';
    if (typeof limit === 'boolean') return limit ? '支持' : '不支持';
    return limit.toString();
  };

  const formatUsage = (used: number, limit: any): string => {
    if (limit === -1) return used.toString();
    return `${used} / ${limit}`;
  };

  if (loading) {
    return (
      <div className={styles.membershipManager}>
        <div className={styles.loading}>
          <div className={styles.spinner}></div>
          <p>加载会员信息中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.membershipManager}>
        <div className={styles.error}>
          <h3>加载失败</h3>
          <p>{error}</p>
          <button onClick={loadMembershipData} className={styles.retryButton}>
            重试
          </button>
        </div>
      </div>
    );
  }

  if (!summary) {
    return (
      <div className={styles.membershipManager}>
        <div className={styles.error}>
          <p>无法获取会员信息</p>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.membershipManager}>
      <div className={styles.header}>
        <h2>会员权益管理</h2>
        <div className={styles.currentTier}>
          <span className={styles.tierBadge}>
            {summary.tier_display_name}
          </span>
        </div>
      </div>

      <div className={styles.tabs}>
        <button
          className={`${styles.tab} ${activeTab === 'overview' ? styles.active : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          概览
        </button>
        <button
          className={`${styles.tab} ${activeTab === 'usage' ? styles.active : ''}`}
          onClick={() => setActiveTab('usage')}
        >
          使用情况
        </button>
        <button
          className={`${styles.tab} ${activeTab === 'tiers' ? styles.active : ''}`}
          onClick={() => setActiveTab('tiers')}
        >
          等级对比
        </button>
      </div>

      <div className={styles.content}>
        {activeTab === 'overview' && (
          <div className={styles.overview}>
            {/* 功能概览 */}
            <div className={styles.featuresGrid}>
              <div className={styles.featureCard}>
                <h4>纪念空间</h4>
                <div className={styles.featureValue}>
                  {formatUsage(
                    summary.usage.memorial_spaces_used || 0,
                    summary.limits.memorial_spaces
                  )}
                </div>
                <div className={styles.featureDescription}>
                  可创建的纪念空间数量
                </div>
              </div>

              <div className={styles.featureCard}>
                <h4>存储空间</h4>
                <div className={styles.featureValue}>
                  {formatUsage(
                    summary.usage.storage_used_gb || 0,
                    summary.limits.storage_gb
                  )} GB
                </div>
                <div className={styles.featureDescription}>
                  文件存储空间容量
                </div>
              </div>

              <div className={styles.featureCard}>
                <h4>AI服务</h4>
                <div className={styles.featureValue}>
                  {formatUsage(
                    summary.usage.ai_minutes_used || 0,
                    summary.limits.ai_minutes
                  )} 分钟
                </div>
                <div className={styles.featureDescription}>
                  每月AI服务使用时长
                </div>
              </div>

              <div className={styles.featureCard}>
                <h4>优先支持</h4>
                <div className={styles.featureValue}>
                  {formatLimit(summary.limits.priority_support)}
                </div>
                <div className={styles.featureDescription}>
                  客服优先响应服务
                </div>
              </div>
            </div>

            {/* 升级建议 */}
            {showUpgradePrompt && summary.upgrade_recommendations.length > 0 && (
              <div className={styles.upgradeRecommendations}>
                <h4>升级建议</h4>
                {summary.upgrade_recommendations.map((rec, index) => (
                  <div key={index} className={styles.recommendationCard}>
                    <div className={styles.recommendationContent}>
                      <h5>{rec.feature}</h5>
                      <p>{rec.reason}</p>
                      <p>
                        推荐升级到：
                        <span className={styles.recommendedTier}>
                          {rec.recommended_tier}
                        </span>
                      </p>
                    </div>
                    <button
                      className={styles.upgradeButton}
                      onClick={() => handleUpgrade(rec.recommended_tier)}
                    >
                      立即升级
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'usage' && (
          <div className={styles.usage}>
            <h4>本月使用情况</h4>
            <div className={styles.usageList}>
              {Object.entries(summary.usage_percentages).map(([feature, percentage]) => {
                if (percentage === null) return null;
                
                const featureNames: Record<string, string> = {
                  memorial_spaces: '纪念空间',
                  storage_gb: '存储空间',
                  ai_minutes: 'AI服务',
                  api_calls: 'API调用',
                  custom_domains: '自定义域名'
                };

                return (
                  <div key={feature} className={styles.usageItem}>
                    <div className={styles.usageHeader}>
                      <span className={styles.featureName}>
                        {featureNames[feature] || feature}
                      </span>
                      <span 
                        className={styles.usagePercentage}
                        style={{ color: getUsageColor(percentage) }}
                      >
                        {percentage.toFixed(1)}%
                      </span>
                    </div>
                    <div className={styles.progressBar}>
                      <div 
                        className={styles.progressFill}
                        style={{ 
                          width: `${Math.min(percentage, 100)}%`,
                          backgroundColor: getUsageColor(percentage)
                        }}
                      />
                    </div>
                  </div>
                );
              })}
            </div>

            <div className={styles.usageDetails}>
              <h5>详细信息</h5>
              <div className={styles.usageGrid}>
                <div>
                  <strong>计费周期：</strong>
                  {new Date(summary.usage.period_start).toLocaleDateString()} - 
                  {new Date(summary.usage.period_end).toLocaleDateString()}
                </div>
                <div>
                  <strong>上次重置：</strong>
                  {new Date(summary.usage.last_reset_date).toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'tiers' && (
          <div className={styles.tiers}>
            <h4>等级对比</h4>
            <div className={styles.tiersGrid}>
              {allTiers.map((tier) => (
                <div 
                  key={tier.tier} 
                  className={`${styles.tierCard} ${
                    tier.tier === summary.current_tier ? styles.currentTier : ''
                  }`}
                >
                  <div className={styles.tierHeader}>
                    <h5>{tier.name}</h5>
                    <p>{tier.description}</p>
                  </div>
                  
                  <div className={styles.tierFeatures}>
                    <div className={styles.tierFeature}>
                      <span>纪念空间：</span>
                      <span>{formatLimit(tier.limits.memorial_spaces)}</span>
                    </div>
                    <div className={styles.tierFeature}>
                      <span>存储空间：</span>
                      <span>{formatLimit(tier.limits.storage_gb)} GB</span>
                    </div>
                    <div className={styles.tierFeature}>
                      <span>AI服务：</span>
                      <span>{formatLimit(tier.limits.ai_minutes)} 分钟/月</span>
                    </div>
                    <div className={styles.tierFeature}>
                      <span>优先支持：</span>
                      <span>{formatLimit(tier.limits.priority_support)}</span>
                    </div>
                    <div className={styles.tierFeature}>
                      <span>高级分析：</span>
                      <span>{formatLimit(tier.limits.advanced_analytics)}</span>
                    </div>
                  </div>

                  {tier.tier !== summary.current_tier && (
                    <button
                      className={styles.selectTierButton}
                      onClick={() => handleUpgrade(tier.tier)}
                    >
                      {tier.tier === 'free' ? '降级到此版本' : '升级到此版本'}
                    </button>
                  )}
                  
                  {tier.tier === summary.current_tier && (
                    <div className={styles.currentTierBadge}>
                      当前等级
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MembershipManager;