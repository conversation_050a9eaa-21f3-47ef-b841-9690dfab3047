.audio-controls {
  background-color: rgba(var(--color-gray-900-rgb), 0.7);
  border-radius: 8px;
  padding: 10px;
  color: white;
  width: 300px;
  max-width: 100%;
  box-shadow: 0 4px 8px rgba(var(--color-gray-900-rgb), 0.2);
}

.audio-controls-header {
  display: flex;
  align-items: center;
  gap: 10px;
}

.audio-toggle-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.audio-toggle-btn:hover {
  background-color: rgba(var(--color-surface-rgb), 0.1);
}

.audio-toggle-btn.muted {
  color: var(--color-error);
}

.audio-toggle-btn.small {
  font-size: 16px;
  width: 24px;
  height: 24px;
}

.master-volume-slider {
  flex: 1;
}

.expand-btn {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.expand-btn:hover {
  background-color: rgba(var(--color-surface-rgb), 0.1);
}

.audio-controls-details {
  margin-top: 15px;
  overflow: hidden;
}

.audio-control-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.audio-control-label {
  width: 120px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.audio-control-slider {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

input[type="range"] {
  -webkit-appearance: none;
  width: 100%;
  height: 6px;
  background: var(--color-gray-800);
  border-radius: 3px;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: var(--color-primary);
  border-radius: 50%;
  cursor: pointer;
}

input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: var(--color-primary);
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

input[type="range"]:disabled {
  opacity: 0.5;
}

.test-sound-btn {
  background: none;
  border: none;
  color: white;
  font-size: 12px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: var(--color-primary);
  transition: background-color 0.2s;
}

.test-sound-btn:hover {
  background-color: var(--color-primary-hover);
}

.test-sound-btn:disabled {
  background-color: var(--color-gray-800);
  cursor: not-allowed;
}
