import React, { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { cn } from "../utils/cn";

// 供品类型
export type OfferingType = "incense" | "flower" | "food" | "candle";

// 供品项
interface OfferingItem {
  id: string;
  type: OfferingType;
  position: { x: number; y: number };
  rotation: number;
  scale: number;
  createdAt: number;
  burning?: boolean;
  smoke?: boolean;
}

interface OfferingSystemProps {
  onOfferingPlaced?: (offering: OfferingItem) => void;
  maxOfferings?: number;
}

// 获取供品样式的辅助函数
const getOfferingStyles = (type: OfferingType) => {
  const baseStyles =
    "absolute transform-gpu transition-transform duration-300 ease-out";

  switch (type) {
    case "incense":
      return cn(
        baseStyles,
        "w-1 h-20 bg-gradient-to-b from-amber-800 to-amber-600 rounded-sm",
      );
    case "flower":
      return cn(
        baseStyles,
        "w-8 h-8 bg-gradient-to-br from-pink-400 to-red-500 rounded-full",
      );
    case "food":
      return cn(
        baseStyles,
        "w-6 h-6 bg-gradient-to-br from-orange-400 to-yellow-500 rounded",
      );
    case "candle":
      return cn(
        baseStyles,
        "w-3 h-16 bg-gradient-to-b from-yellow-200 to-yellow-600 rounded-sm",
      );
    default:
      return baseStyles;
  }
};

const OfferingSystem: React.FC<OfferingSystemProps> = ({
  onOfferingPlaced,
  maxOfferings = 20,
}) => {
  const { t } = useTranslation();
  const [offerings, setOfferings] = useState<OfferingItem[]>([]);
  const [selectedType, setSelectedType] = useState<OfferingType>("incense");
  const [isDragging, setIsDragging] = useState(false);
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // 添加供品
  const addOffering = (position: { x: number; y: number }) => {
    if (offerings.length >= maxOfferings) {
      // 如果超过最大数量，移除最早的供品
      setOfferings((prev) => prev.slice(1));
    }

    const newOffering: OfferingItem = {
      id: `offering-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: selectedType,
      position,
      rotation: Math.random() * 30 - 15, // 随机旋转 -15 到 15 度
      scale: 0.8 + Math.random() * 0.4, // 随机大小 0.8 到 1.2
      createdAt: Date.now(),
      burning: selectedType === "incense" || selectedType === "candle",
      smoke: selectedType === "incense",
    };

    setOfferings((prev) => [...prev, newOffering]);

    if (onOfferingPlaced) {
      onOfferingPlaced(newOffering);
    }
  };

  // 处理鼠标/触摸事件
  const handleMouseDown = (e: React.MouseEvent | React.TouchEvent) => {
    if (!containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const clientX = "touches" in e ? e.touches[0].clientX : e.clientX;
    const clientY = "touches" in e ? e.touches[0].clientY : e.clientY;

    setIsDragging(true);
    setDragPosition({
      x: clientX - rect.left,
      y: clientY - rect.top,
    });
  };

  const handleMouseMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (!isDragging || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const clientX = "touches" in e ? e.touches[0].clientX : e.clientX;
    const clientY = "touches" in e ? e.touches[0].clientY : e.clientY;

    setDragPosition({
      x: clientX - rect.left,
      y: clientY - rect.top,
    });
  };

  const handleMouseUp = () => {
    if (isDragging) {
      addOffering(dragPosition);
      setIsDragging(false);
    }
  };

  // 添加事件监听器
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      setIsDragging(false);
    };

    window.addEventListener("mouseup", handleGlobalMouseUp);
    window.addEventListener("touchend", handleGlobalMouseUp);

    return () => {
      window.removeEventListener("mouseup", handleGlobalMouseUp);
      window.removeEventListener("touchend", handleGlobalMouseUp);
    };
  }, []);

  // 清除所有供品
  const clearAllOfferings = () => {
    setOfferings([]);
  };

  // 渲染供品
  const renderOffering = (offering: OfferingItem) => {
    const style = {
      transform: `translate(${offering.position.x}px, ${offering.position.y}px) rotate(${offering.rotation}deg) scale(${offering.scale})`,
      transformOrigin: "bottom center",
    };

    return (
      <div
        key={offering.id}
        className={getOfferingStyles(offering.type)}
        style={style}
      >
        {offering.smoke && (
          <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-2 h-8 bg-gradient-to-t from-gray-400 to-transparent opacity-60 animate-pulse" />
        )}
        {offering.burning && (
          <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-1 h-2 bg-gradient-to-t from-orange-500 to-yellow-300 rounded-full animate-pulse" />
        )}
      </div>
    );
  };

  return (
    <div
      className={cn(
        "absolute bottom-0 left-0 right-0 z-50 pointer-events-none",
      )}
    >
      <div
        className={cn(
          "relative w-full pointer-events-auto",
          "h-[calc(100%-80px)]",
        )}
        ref={containerRef}
        onMouseDown={handleMouseDown}
        onTouchStart={handleMouseDown}
        onMouseMove={handleMouseMove}
        onTouchMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onTouchEnd={handleMouseUp}
      >
        {offerings.map(renderOffering)}

        {isDragging && (
          <div
            className={cn(
              "absolute pointer-events-none opacity-70",
              getOfferingStyles(selectedType),
            )}
            style={{
              transform: `translate(${dragPosition.x}px, ${dragPosition.y}px)`,
            }}
          />
        )}
      </div>

      <div
        className={cn(
          "flex justify-center items-center p-3 pointer-events-auto",
          "bg-gray-900/70 rounded-t-lg",
        )}
      >
        <div className={cn("flex gap-3 mr-5")}>
          <button
            className={cn(
              "px-3 py-2 rounded transition-all duration-200",
              "border border-gray-600 text-sm font-medium",
              "focus:outline-none focus:ring-2 focus:ring-amber-500",
              selectedType === "incense"
                ? "bg-amber-600 text-white border-amber-500 hover:bg-amber-700"
                : "bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white",
            )}
            onClick={() => setSelectedType("incense")}
          >
            {t("offerings.incense", "香")}
          </button>
          <button
            className={cn(
              "px-3 py-2 rounded transition-all duration-200",
              "border border-gray-600 text-sm font-medium",
              "focus:outline-none focus:ring-2 focus:ring-amber-500",
              selectedType === "flower"
                ? "bg-pink-600 text-white border-pink-500 hover:bg-pink-700"
                : "bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white",
            )}
            onClick={() => setSelectedType("flower")}
          >
            {t("offerings.flower", "花")}
          </button>
          <button
            className={cn(
              "px-3 py-2 rounded transition-all duration-200",
              "border border-gray-600 text-sm font-medium",
              "focus:outline-none focus:ring-2 focus:ring-amber-500",
              selectedType === "food"
                ? "bg-orange-600 text-white border-orange-500 hover:bg-orange-700"
                : "bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white",
            )}
            onClick={() => setSelectedType("food")}
          >
            {t("offerings.food", "食品")}
          </button>
          <button
            className={cn(
              "px-3 py-2 rounded transition-all duration-200",
              "border border-gray-600 text-sm font-medium",
              "focus:outline-none focus:ring-2 focus:ring-amber-500",
              selectedType === "candle"
                ? "bg-yellow-600 text-white border-yellow-500 hover:bg-yellow-700"
                : "bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white",
            )}
            onClick={() => setSelectedType("candle")}
          >
            {t("offerings.candle", "蜡烛")}
          </button>
        </div>

        <button
          className={cn(
            "px-4 py-2 rounded transition-all duration-200",
            "bg-red-600 text-white border border-red-500",
            "hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500",
            "text-sm font-medium",
          )}
          onClick={clearAllOfferings}
        >
          {t("offerings.clear", "清除供品")}
        </button>
      </div>
    </div>
  );
};

export default OfferingSystem;
