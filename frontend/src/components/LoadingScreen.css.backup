.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(var(--color-gray-900-rgb), 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--z-modal);
  color: white;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.loading-content {
  text-align: center;
  max-width: 80%;
}

.loading-progress-container {
  width: 300px;
  height: 10px;
  background-color: var(--color-gray-900);
  border-radius: 5px;
  margin: 20px auto;
  overflow: hidden;
}

.loading-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-hover));
  border-radius: 5px;
  transition: width 0.3s ease-out;
}

.loading-progress-text {
  font-size: 18px;
  margin-bottom: 10px;
}

.loading-message {
  font-size: 14px;
  color: var(--color-gray-500);
  margin-top: 20px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* 加载动画 */
.loading-spinner {
  width: 50px;
  height: 50px;
  margin: 0 auto 20px;
  border: 5px solid rgba(var(--color-surface-rgb), 0.3);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
