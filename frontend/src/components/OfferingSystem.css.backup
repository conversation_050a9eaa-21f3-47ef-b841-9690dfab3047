.offering-system {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--z-dropdown);
  pointer-events: none;
}

.offering-container {
  position: relative;
  width: 100%;
  height: calc(100% - 80px);
  pointer-events: auto;
}

.offering-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  background-color: rgba(var(--color-gray-900-rgb), 0.7);
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  pointer-events: auto;
}

.offering-types {
  display: flex;
  gap: 10px;
  margin-right: 20px;
}

.offering-type-btn {
  background-color: var(--color-gray-800);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.offering-type-btn.active {
  background-color: var(--color-primary);
}

.offering-type-btn:hover {
  background-color: var(--color-gray-700);
}

.offering-type-btn.active:hover {
  background-color: var(--color-primary-hover);
}

.offering-clear-btn {
  background-color: var(--color-error);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.offering-clear-btn:hover {
  background-color: var(--color-error);
}

.offering-item {
  position: absolute;
  width: 30px;
  height: 100px;
  transform-origin: bottom center;
  transition: transform 0.3s ease-out;
}

.offering-preview {
  position: absolute;
  width: 30px;
  height: 100px;
  transform-origin: bottom center;
  pointer-events: none;
}

/* 香 */
.offering-incense {
  background-image: linear-gradient(to bottom, var(--color-brown), var(--color-brown-light));
  width: 4px;
  height: 80px;
  border-radius: 2px;
}

.offering-incense::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: -3px;
  width: 10px;
  height: 10px;
  background-color: var(--color-brown-lighter);
  border-radius: 50%;
}

.offering-smoke {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 30px;
  background: linear-gradient(
    to top,
    rgba(var(--color-gray-400-rgb), 0.8),
    rgba(var(--color-gray-400-rgb), 0)
  );
  border-radius: 50%;
  animation: smoke 3s infinite;
}

@keyframes smoke {
  0% {
    opacity: 0.8;
    transform: translateX(-50%) translateY(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-30px) scale(3);
  }
}

/* 花 */
.offering-flower {
  width: 30px;
  height: 40px;
}

.offering-flower::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  background-color: var(--color-pink);
  border-radius: 50%;
  box-shadow:
    -10px -5px 0 var(--color-pink-dark),
    10px -5px 0 var(--color-pink-dark),
    -5px 5px 0 var(--color-pink-dark),
    5px 5px 0 var(--color-pink-dark);
}

.offering-flower::after {
  content: "";
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 20px;
  background-color: var(--color-green);
}

/* 食品 */
.offering-food {
  width: 40px;
  height: 20px;
  background-color: var(--color-gold);
  border-radius: 5px;
}

.offering-food::before {
  content: "";
  position: absolute;
  top: -10px;
  left: 5px;
  width: 30px;
  height: 10px;
  background-color: var(--color-orange);
  border-radius: 5px 5px 0 0;
}

/* 蜡烛 */
.offering-candle {
  width: 15px;
  height: 50px;
  background-color: var(--color-gray-100);
  border-radius: 2px;
}

.offering-candle::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: -5px;
  width: 25px;
  height: 5px;
  background-color: var(--color-brown-lighter);
  border-radius: 2px;
}

.offering-flame {
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  width: 10px;
  height: 15px;
  background: radial-gradient(ellipse at center, var(--color-surface)f00 0%, var(--color-error) 100%);
  border-radius: 50% 50% 20% 20%;
  animation: flame 0.5s infinite alternate;
}

@keyframes flame {
  0% {
    transform: translateX(-50%) scaleY(1) scaleX(1);
  }
  100% {
    transform: translateX(-50%) scaleY(1.1) scaleX(0.9);
  }
}
