import React, { useEffect, useState, useCallback } from 'react';
import { Scene, Engine } from '@babylonjs/core';
import DeviceDetector from '../utils/DeviceDetector';
import ScenePerformanceManager from '../utils/ScenePerformanceManager';
import { PerformanceMetrics } from '../utils/PerformanceMonitor';

// 移动端优化器属性
interface MobilePerformanceOptimizerProps {
  scene: Scene | null;
  engine: Engine | null;
  onOptimizationChange?: (level: string, reason: string) => void;
  showDebugInfo?: boolean;
}

// 移动端特定优化设置
interface MobileOptimizationSettings {
  lowMemoryMode: boolean;
  batteryOptimization: boolean;
  reducedParticles: boolean;
  simplifiedShaders: boolean;
  compressedTextures: boolean;
}

// 移动端性能优化器组件
const MobilePerformanceOptimizer: React.FC<MobilePerformanceOptimizerProps> = ({
  scene,
  engine,
  onOptimizationChange,
  showDebugInfo = false
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [deviceInfo, setDeviceInfo] = useState<any>(null);
  const [currentOptimizations, setCurrentOptimizations] = useState<MobileOptimizationSettings>({
    lowMemoryMode: false,
    batteryOptimization: false,
    reducedParticles: false,
    simplifiedShaders: false,
    compressedTextures: false,
  });
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null);
  const [optimizationHistory, setOptimizationHistory] = useState<string[]>([]);

  // 检测设备类型和性能
  useEffect(() => {
    const device = DeviceDetector.getDeviceInfo();
    setDeviceInfo(device);
    setIsMobile(device.isMobile);

    if (device.isMobile) {
      console.log('[MobileOptimizer] Mobile device detected, applying optimizations');
      applyMobileOptimizations(device);
    }
  }, []);

  // 初始化性能管理器
  useEffect(() => {
    if (!scene || !engine) return;

    const performanceManager = ScenePerformanceManager.getInstance();
    
    // 为移动设备设置更激进的优化
    if (isMobile) {
      performanceManager.setAutoOptimization(true);
      
      // 设置移动端回调
      performanceManager.setCallbacks({
        onQualityChanged: (level: string) => {
          const reason = `Mobile optimization: quality changed to ${level}`;
          addOptimizationLog(reason);
          if (onOptimizationChange) {
            onOptimizationChange(level, reason);
          }
        },
        onPerformanceUpdate: (metrics: PerformanceMetrics) => {
          setPerformanceMetrics(metrics);
          checkMobilePerformance(metrics);
        }
      });
    }
  }, [scene, engine, isMobile, onOptimizationChange]);

  // 应用移动端优化
  const applyMobileOptimizations = useCallback((device: any) => {
    if (!scene || !engine) return;

    const optimizations: MobileOptimizationSettings = {
      lowMemoryMode: false,
      batteryOptimization: false,
      reducedParticles: false,
      simplifiedShaders: false,
      compressedTextures: false,
    };

    // 根据设备性能等级应用不同优化
    if (device.performanceLevel === 'low' || device.memoryGB < 4) {
      optimizations.lowMemoryMode = true;
      optimizations.reducedParticles = true;
      optimizations.simplifiedShaders = true;
      optimizations.compressedTextures = true;
      applyLowMemoryOptimizations();
    }

    // 电池优化（所有移动设备）
    optimizations.batteryOptimization = true;
    applyBatteryOptimizations();

    // 减少粒子效果
    if (device.performanceLevel !== 'high') {
      optimizations.reducedParticles = true;
      reduceParticleEffects();
    }

    setCurrentOptimizations(optimizations);
    addOptimizationLog(`Applied mobile optimizations for ${device.performanceLevel} performance device`);
  }, [scene, engine]);

  // 应用低内存优化
  const applyLowMemoryOptimizations = useCallback(() => {
    if (!scene || !engine) return;

    // 降低渲染分辨率
    engine.setHardwareScalingLevel(0.7);

    // 减少纹理质量
    scene.textures.forEach(texture => {
      if ((texture as any).updateSamplingMode) {
        (texture as any).updateSamplingMode(0); // 使用最低质量采样
      }
    });

    // 限制活动网格数量
    const maxActiveMeshes = 50;
    if (scene.getActiveMeshes().length > maxActiveMeshes) {
      scene.freezeActiveMeshes();
    }

    addOptimizationLog('Applied low memory optimizations');
  }, [scene, engine]);

  // 应用电池优化
  const applyBatteryOptimizations = useCallback(() => {
    if (!scene || !engine) return;

    // 降低目标帧率
    engine.setHardwareScalingLevel(0.8);

    // 减少光源数量
    const lights = scene.lights;
    lights.forEach((light, index) => {
      if (index > 2) { // 只保留前3个光源
        light.setEnabled(false);
      }
    });

    // 禁用不必要的后处理
    scene.postProcesses.forEach(postProcess => {
      if (postProcess.name.includes('glow') || postProcess.name.includes('bloom')) {
        (postProcess as any).setEnabled(false);
      }
    });

    addOptimizationLog('Applied battery optimization');
  }, [scene, engine]);

  // 减少粒子效果
  const reduceParticleEffects = useCallback(() => {
    if (!scene) return;

    scene.particleSystems.forEach(system => {
      const currentCapacity = system.getCapacity();
      system.manualEmitCount = Math.min(25, Math.floor(currentCapacity * 0.3));
    });

    addOptimizationLog('Reduced particle effects for mobile');
  }, [scene]);

  // 检查移动端性能
  const checkMobilePerformance = useCallback((metrics: PerformanceMetrics) => {
    if (!isMobile) return;

    // 如果FPS过低，应用更激进的优化
    if (metrics.fps < 25) {
      applyAggressiveOptimizations();
    }

    // 如果内存使用过高，清理资源  
    if (metrics.memoryUsage.used > 150) { // MB
      performMemoryCleanup();
    }

    // 温度管理（如果可用）
    if ((metrics as any).temperature && (metrics as any).temperature > 80) {
      applyThermalOptimizations();
    }
  }, [isMobile]);

  // 应用激进优化
  const applyAggressiveOptimizations = useCallback(() => {
    if (!scene || !engine) return;

    // 进一步降低渲染质量
    engine.setHardwareScalingLevel(0.5);

    // 禁用所有阴影
    scene.lights.forEach(light => {
      if ((light as any).shadowEnabled) {
        (light as any).shadowEnabled = false;
      }
    });

    // 冻结不动的网格
    scene.meshes.forEach(mesh => {
      if (mesh.animations.length === 0) {
        mesh.freezeWorldMatrix();
      }
    });

    addOptimizationLog('Applied aggressive optimizations due to low FPS');
  }, [scene, engine]);

  // 执行内存清理
  const performMemoryCleanup = useCallback(() => {
    if (!scene || !engine) return;

    // 清理未使用的纹理
    scene.textures.forEach(texture => {
      if ((texture as any).isReady && (texture as any).getSize().width > 512) {
        // 降低大纹理的分辨率
        (texture as any).updateSamplingMode(0);
      }
    });

    // 强制垃圾回收（如果可用）
    if (window.gc) {
      window.gc();
    }

    addOptimizationLog('Performed memory cleanup');
  }, [scene, engine]);

  // 应用热管理优化
  const applyThermalOptimizations = useCallback(() => {
    if (!scene || !engine) return;

    // 临时降低渲染质量
    engine.setHardwareScalingLevel(0.6);

    // 减少动画和粒子
    scene.animationGroups.forEach(group => {
      group.pause();
    });

    scene.particleSystems.forEach(system => {
      system.stop();
    });

    // 设置定时器恢复（30秒后）
    setTimeout(() => {
      if (scene && engine) {
        engine.setHardwareScalingLevel(0.8);
        scene.animationGroups.forEach(group => {
          group.play();
        });
      }
    }, 30000);

    addOptimizationLog('Applied thermal management optimizations');
  }, [scene, engine]);

  // 添加优化日志
  const addOptimizationLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `${timestamp}: ${message}`;
    
    setOptimizationHistory(prev => {
      const newHistory = [logEntry, ...prev].slice(0, 10); // 保留最近10条
      return newHistory;
    });

    console.log('[MobileOptimizer]', message);
  }, []);

  // 手动触发优化
  const triggerManualOptimization = useCallback(() => {
    if (deviceInfo) {
      applyMobileOptimizations(deviceInfo);
    }
  }, [deviceInfo, applyMobileOptimizations]);

  // 如果不是移动设备，不渲染组件
  if (!isMobile && !showDebugInfo) {
    return null;
  }

  return (
    <div className="mobile-performance-optimizer">
      {showDebugInfo && (
        <div className="fixed top-4 left-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-sm z-50 max-w-xs">
          <h3 className="font-bold mb-2">移动端优化状态</h3>
          
          {/* 设备信息 */}
          <div className="mb-2">
            <div className="font-semibold">设备信息:</div>
            <div>类型: {deviceInfo?.deviceType}</div>
            <div>性能: {deviceInfo?.performanceLevel}</div>
            <div>内存: {deviceInfo?.memoryGB}GB</div>
          </div>

          {/* 性能指标 */}
          {performanceMetrics && (
            <div className="mb-2">
              <div className="font-semibold">性能指标:</div>
              <div>FPS: {performanceMetrics.fps.toFixed(1)}</div>
              <div>内存: {performanceMetrics.memoryUsage.used.toFixed(1)}MB</div>
              <div>网格: {performanceMetrics.drawCalls}</div>
            </div>
          )}

          {/* 当前优化 */}
          <div className="mb-2">
            <div className="font-semibold">启用的优化:</div>
            {Object.entries(currentOptimizations).map(([key, enabled]) => (
              <div key={key} className="flex items-center">
                <span className={`w-2 h-2 rounded-full mr-2 ${enabled ? 'bg-green-500' : 'bg-gray-500'}`} />
                <span className="text-xs">{key}</span>
              </div>
            ))}
          </div>

          {/* 优化历史 */}
          <div className="mb-2">
            <div className="font-semibold">优化记录:</div>
            <div className="max-h-20 overflow-y-auto text-xs">
              {optimizationHistory.map((log, index) => (
                <div key={index} className="text-gray-300">
                  {log}
                </div>
              ))}
            </div>
          </div>

          {/* 手动操作 */}
          <button 
            onClick={triggerManualOptimization}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs"
          >
            手动优化
          </button>
        </div>
      )}
    </div>
  );
};

export default MobilePerformanceOptimizer;
export type { MobileOptimizationSettings };