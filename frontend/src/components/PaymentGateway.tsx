import React, { useState, useEffect } from 'react';
import { 
  paymentService, 
  PaymentProvider, 
  PaymentMethod, 
  PaymentRequest, 
  PaymentResult 
} from '../services/paymentService';
import styles from './PaymentGateway.module.css';

interface PaymentGatewayProps {
  orderId: string;
  amount: number;
  currency?: string;
  description: string;
  onSuccess: (result: PaymentResult) => void;
  onCancel: () => void;
  onError: (error: string) => void;
}

interface PaymentState {
  step: 'method_selection' | 'processing' | 'completed' | 'failed';
  providers: PaymentProvider[];
  selectedProvider?: PaymentProvider;
  selectedMethod?: PaymentMethod;
  paymentResult?: PaymentResult;
  loading: boolean;
  error?: string;
}

export const PaymentGateway: React.FC<PaymentGatewayProps> = ({
  orderId,
  amount,
  currency = 'CNY',
  description,
  onSuccess,
  onCancel,
  onError,
}) => {
  const [state, setState] = useState<PaymentState>({
    step: 'method_selection',
    providers: [],
    loading: true,
  });

  // 初始化支付提供商
  useEffect(() => {
    loadPaymentProviders();
  }, []);

  const loadPaymentProviders = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: undefined }));
      const providers = await paymentService.getAvailableProviders();
      setState(prev => ({ 
        ...prev, 
        providers: providers.filter(p => p.enabled),
        loading: false 
      }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: '无法加载支付方式' 
      }));
    }
  };

  const selectPaymentMethod = async (provider: PaymentProvider, method: PaymentMethod) => {
    // 验证支付金额
    if (!paymentService.validatePaymentAmount(amount, method)) {
      onError(`支付金额必须在 ${method.minimumAmount} - ${method.maximumAmount} 元之间`);
      return;
    }

    setState(prev => ({
      ...prev,
      selectedProvider: provider,
      selectedMethod: method,
    }));
  };

  const processPayment = async () => {
    if (!state.selectedProvider || !state.selectedMethod) return;

    setState(prev => ({ ...prev, step: 'processing', loading: true }));

    try {
      const paymentRequest: PaymentRequest = {
        orderId,
        amount,
        currency,
        description,
        providerId: state.selectedProvider.id,
        methodId: state.selectedMethod.id,
        returnUrl: `${window.location.origin}/payment/success`,
        cancelUrl: `${window.location.origin}/payment/cancel`,
        metadata: {
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
        },
      };

      const result = await paymentService.createPayment(paymentRequest);

      if (result.success) {
        if (result.redirectUrl) {
          // 重定向到支付页面
          window.location.href = result.redirectUrl;
        } else {
          // 开始轮询支付状态
          startPaymentPolling(result.paymentId!);
        }
      } else {
        setState(prev => ({ 
          ...prev, 
          step: 'failed', 
          loading: false,
          error: result.message 
        }));
        onError(result.message || '支付创建失败');
      }
    } catch (error: any) {
      setState(prev => ({ 
        ...prev, 
        step: 'failed', 
        loading: false,
        error: error.message 
      }));
      onError(error.message || '支付处理异常');
    }
  };

  const startPaymentPolling = (paymentId: string) => {
    paymentService.pollPaymentStatus(
      paymentId,
      (result) => {
        setState(prev => ({ ...prev, paymentResult: result }));

        if (result.status === 'completed') {
          setState(prev => ({ ...prev, step: 'completed', loading: false }));
          onSuccess(result);
        } else if (result.status === 'failed') {
          setState(prev => ({ ...prev, step: 'failed', loading: false }));
          onError(result.message || '支付失败');
        }
      },
      30, // 最大轮询次数
      2000 // 轮询间隔
    );
  };

  const renderMethodSelection = () => (
    <div className={styles.methodSelection}>
      <div className={styles.header}>
        <h3>选择支付方式</h3>
        <p className={styles.amount}>
          支付金额: {paymentService.formatPaymentAmount(amount, currency)}
        </p>
        <p className={styles.description}>{description}</p>
      </div>

      <div className={styles.providersContainer}>
        {state.providers.map(provider => (
          <ProviderSection
            key={provider.id}
            provider={provider}
            onMethodSelect={(method) => selectPaymentMethod(provider, method)}
            selectedMethod={state.selectedMethod}
          />
        ))}
      </div>

      <div className={styles.actions}>
        <button 
          className={styles.cancelButton}
          onClick={onCancel}
        >
          取消支付
        </button>
        <button 
          className={styles.payButton}
          onClick={processPayment}
          disabled={!state.selectedMethod || state.loading}
        >
          {state.loading ? '处理中...' : '确认支付'}
        </button>
      </div>
    </div>
  );

  const renderProcessing = () => (
    <div className={styles.processing}>
      <div className={styles.spinner}></div>
      <h3>正在处理支付...</h3>
      <p>请不要关闭页面，支付完成后会自动跳转</p>
      
      {state.selectedMethod && (
        <div className={styles.methodInfo}>
          <span className={styles.methodIcon}>
            {paymentService.getPaymentMethodIcon(state.selectedMethod.id)}
          </span>
          <span>{state.selectedMethod.name}</span>
        </div>
      )}

      <button 
        className={styles.cancelButton}
        onClick={onCancel}
      >
        取消支付
      </button>
    </div>
  );

  const renderCompleted = () => (
    <div className={styles.completed}>
      <div className={styles.successIcon}>✅</div>
      <h3>支付成功</h3>
      <p>您的支付已完成，感谢您的购买！</p>
      
      {state.paymentResult?.transactionId && (
        <p className={styles.transactionId}>
          交易号: {state.paymentResult.transactionId}
        </p>
      )}
    </div>
  );

  const renderFailed = () => (
    <div className={styles.failed}>
      <div className={styles.errorIcon}>❌</div>
      <h3>支付失败</h3>
      <p>{state.error || '支付过程中发生错误，请重试'}</p>
      
      <div className={styles.actions}>
        <button 
          className={styles.retryButton}
          onClick={() => setState(prev => ({ 
            ...prev, 
            step: 'method_selection',
            error: undefined 
          }))}
        >
          重新支付
        </button>
        <button 
          className={styles.cancelButton}
          onClick={onCancel}
        >
          取消
        </button>
      </div>
    </div>
  );

  if (state.loading && state.step === 'method_selection') {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>正在加载支付方式...</p>
      </div>
    );
  }

  return (
    <div className={styles.paymentGateway}>
      {state.step === 'method_selection' && renderMethodSelection()}
      {state.step === 'processing' && renderProcessing()}
      {state.step === 'completed' && renderCompleted()}
      {state.step === 'failed' && renderFailed()}
    </div>
  );
};

// 支付提供商区域组件
interface ProviderSectionProps {
  provider: PaymentProvider;
  onMethodSelect: (method: PaymentMethod) => void;
  selectedMethod?: PaymentMethod;
}

const ProviderSection: React.FC<ProviderSectionProps> = ({
  provider,
  onMethodSelect,
  selectedMethod,
}) => {
  const [methods, setMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadMethods();
  }, [provider.id]);

  const loadMethods = async () => {
    try {
      setLoading(true);
      const providerMethods = await paymentService.getPaymentMethods(provider.id);
      setMethods(providerMethods);
    } catch (error) {
      console.error('加载支付方式失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={styles.providerSection}>
        <h4>{provider.name}</h4>
        <div className={styles.loadingMethods}>加载中...</div>
      </div>
    );
  }

  return (
    <div className={styles.providerSection}>
      <h4 className={styles.providerName}>{provider.name}</h4>
      <div className={styles.methodsGrid}>
        {methods.map(method => (
          <button
            key={method.id}
            className={`${styles.methodButton} ${
              selectedMethod?.id === method.id ? styles.selected : ''
            }`}
            onClick={() => onMethodSelect(method)}
          >
            <span className={styles.methodIcon}>
              {paymentService.getPaymentMethodIcon(method.id)}
            </span>
            <div className={styles.methodInfo}>
              <span className={styles.methodName}>{method.name}</span>
              <span className={styles.methodDescription}>{method.description}</span>
            </div>
          </button>
        ))}
      </div>
    </div>
  );
};

export default PaymentGateway;