import React, { useState, useEffect } from 'react';
import { PaymentGateway } from './PaymentGateway';
import styles from './VirtualProductPurchase.module.css';

interface VirtualProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  category: 'offering' | 'decoration' | 'scene' | 'effect';
  type: 'virtual_offering' | 'scene_decoration' | 'premium_effect';
  imageUrl: string;
  modelUrl?: string;
  customizations?: ProductCustomization[];
  religiousCompatibility: string[];
  duration?: number; // 持续时间(天)，null表示永久
  metadata?: Record<string, any>;
}

interface ProductCustomization {
  id: string;
  name: string;
  type: 'text' | 'image' | 'color' | 'size';
  required: boolean;
  options?: string[];
  maxLength?: number;
  price?: number;
}

interface ShoppingCartItem {
  product: VirtualProduct;
  quantity: number;
  customizations: Record<string, any>;
  totalPrice: number;
}

interface PurchaseState {
  step: 'selection' | 'customization' | 'cart' | 'payment' | 'completed';
  selectedProduct?: VirtualProduct;
  cart: ShoppingCartItem[];
  customizations: Record<string, any>;
  orderId?: string;
  loading: boolean;
  error?: string;
}

export const VirtualProductPurchase: React.FC = () => {
  const [state, setState] = useState<PurchaseState>({
    step: 'selection',
    cart: [],
    customizations: {},
    loading: false,
  });

  const [products, setProducts] = useState<VirtualProduct[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      // 模拟加载虚拟商品数据
      const mockProducts: VirtualProduct[] = [
        {
          id: 'incense_premium',
          name: '高级檀香',
          description: '珍贵檀香木制作，香气持久，适合各种祭拜场合',
          price: 9.99,
          currency: 'CNY',
          category: 'offering',
          type: 'virtual_offering',
          imageUrl: '/images/products/incense_premium.jpg',
          modelUrl: '/models/incense_premium.glb',
          religiousCompatibility: ['buddhist', 'taoist', 'general'],
          duration: 7,
          customizations: [
            {
              id: 'inscription',
              name: '刻字内容',
              type: 'text',
              required: false,
              maxLength: 20,
              price: 2.00
            }
          ]
        },
        {
          id: 'lotus_flower',
          name: '莲花祭品',
          description: '纯净莲花，象征着纯洁与重生',
          price: 15.99,
          currency: 'CNY',
          category: 'offering',
          type: 'virtual_offering',
          imageUrl: '/images/products/lotus_flower.jpg',
          modelUrl: '/models/lotus_flower.glb',
          religiousCompatibility: ['buddhist', 'general'],
          duration: 30,
          customizations: [
            {
              id: 'color',
              name: '花色选择',
              type: 'color',
              required: true,
              options: ['白色', '粉色', '金色']
            }
          ]
        },
        {
          id: 'candle_set',
          name: '祈福蜡烛套装',
          description: '三支装祈福蜡烛，寓意三生三世的思念',
          price: 12.99,
          currency: 'CNY',
          category: 'offering',
          type: 'virtual_offering',
          imageUrl: '/images/products/candle_set.jpg',
          modelUrl: '/models/candle_set.glb',
          religiousCompatibility: ['christian', 'general'],
          duration: 14
        },
        {
          id: 'cherry_blossom_effect',
          name: '樱花飘落特效',
          description: '浪漫的樱花飘落动画效果，营造温馨纪念氛围',
          price: 29.99,
          currency: 'CNY',
          category: 'effect',
          type: 'premium_effect',
          imageUrl: '/images/products/cherry_blossom_effect.jpg',
          religiousCompatibility: ['general'],
          duration: 90
        },
        {
          id: 'golden_frame',
          name: '黄金相框',
          description: '典雅的黄金相框，让珍贵回忆更加闪耀',
          price: 39.99,
          currency: 'CNY',
          category: 'decoration',
          type: 'scene_decoration',
          imageUrl: '/images/products/golden_frame.jpg',
          modelUrl: '/models/golden_frame.glb',
          religiousCompatibility: ['general'],
          customizations: [
            {
              id: 'size',
              name: '尺寸规格',
              type: 'size',
              required: true,
              options: ['标准版', '加大版', '豪华版'],
              price: 10.00
            },
            {
              id: 'photo',
              name: '上传照片',
              type: 'image',
              required: true
            }
          ]
        }
      ];

      setProducts(mockProducts);
      setCategories(['all', 'offering', 'decoration', 'effect']);
      setState(prev => ({ ...prev, loading: false }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: '加载商品失败' 
      }));
    }
  };

  const filteredProducts = selectedCategory === 'all' 
    ? products 
    : products.filter(p => p.category === selectedCategory);

  const addToCart = (product: VirtualProduct, customizations: Record<string, any> = {}) => {
    const customizationPrice = calculateCustomizationPrice(product, customizations);
    const totalPrice = product.price + customizationPrice;
    
    const cartItem: ShoppingCartItem = {
      product,
      quantity: 1,
      customizations,
      totalPrice,
    };

    setState(prev => ({
      ...prev,
      cart: [...prev.cart, cartItem],
      step: 'cart'
    }));
  };

  const calculateCustomizationPrice = (product: VirtualProduct, customizations: Record<string, any>): number => {
    if (!product.customizations) return 0;
    
    return product.customizations.reduce((total, customization) => {
      if (customizations[customization.id] && customization.price) {
        return total + customization.price;
      }
      return total;
    }, 0);
  };

  const getTotalCartPrice = (): number => {
    return state.cart.reduce((total, item) => total + item.totalPrice, 0);
  };

  const proceedToPayment = () => {
    const orderId = `ORDER_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    setState(prev => ({
      ...prev,
      step: 'payment',
      orderId
    }));
  };

  const handlePaymentSuccess = (_paymentResult: any) => {
    setState(prev => ({
      ...prev,
      step: 'completed'
    }));

    // 这里应该调用API将虚拟商品添加到用户的纪念空间
    deliverVirtualProducts();
  };

  const deliverVirtualProducts = async () => {
    try {
      // 实际项目中应该调用后端API
      for (const item of state.cart) {
        await deliverVirtualProduct(item);
      }
    } catch (error) {
      console.error('虚拟商品交付失败:', error);
    }
  };

  const deliverVirtualProduct = async (item: ShoppingCartItem) => {
    // 模拟虚拟商品交付逻辑
    const deliveryData = {
      productId: item.product.id,
      customizations: item.customizations,
      duration: item.product.duration,
      deliveredAt: new Date().toISOString(),
    };
    
    console.log('交付虚拟商品:', deliveryData);
    // 实际应该调用API: await api.deliverVirtualProduct(deliveryData);
  };

  return (
    <div className={styles.virtualProductPurchase}>
      {state.step === 'selection' && (
        <ProductSelection
          products={filteredProducts}
          categories={categories}
          selectedCategory={selectedCategory}
          onCategoryChange={setSelectedCategory}
          onProductSelect={(product) => setState(prev => ({ 
            ...prev, 
            selectedProduct: product, 
            step: product.customizations?.length ? 'customization' : 'cart'
          }))}
          onAddToCart={addToCart}
          loading={state.loading}
          error={state.error}
        />
      )}

      {state.step === 'customization' && state.selectedProduct && (
        <ProductCustomization
          product={state.selectedProduct}
          customizations={state.customizations}
          onCustomizationChange={(customizations) => 
            setState(prev => ({ ...prev, customizations }))
          }
          onAddToCart={() => {
            if (state.selectedProduct) {
              addToCart(state.selectedProduct, state.customizations);
            }
          }}
          onBack={() => setState(prev => ({ ...prev, step: 'selection' }))}
        />
      )}

      {state.step === 'cart' && (
        <ShoppingCart
          items={state.cart}
          totalPrice={getTotalCartPrice()}
          onUpdateCart={(cart) => setState(prev => ({ ...prev, cart }))}
          onContinueShopping={() => setState(prev => ({ ...prev, step: 'selection' }))}
          onProceedToPayment={proceedToPayment}
        />
      )}

      {state.step === 'payment' && state.orderId && (
        <PaymentGateway
          orderId={state.orderId}
          amount={getTotalCartPrice()}
          description={`虚拟商品购买 - ${state.cart.length}件商品`}
          onSuccess={handlePaymentSuccess}
          onCancel={() => setState(prev => ({ ...prev, step: 'cart' }))}
          onError={(error) => setState(prev => ({ ...prev, error }))}
        />
      )}

      {state.step === 'completed' && (
        <PurchaseCompleted
          cart={state.cart}
          orderId={state.orderId!}
          onContinueShopping={() => setState(prev => ({ 
            ...prev, 
            step: 'selection',
            cart: [],
            selectedProduct: undefined,
            customizations: {}
          }))}
        />
      )}
    </div>
  );
};

// 商品选择组件
interface ProductSelectionProps {
  products: VirtualProduct[];
  categories: string[];
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  onProductSelect: (product: VirtualProduct) => void;
  onAddToCart: (product: VirtualProduct) => void;
  loading: boolean;
  error?: string;
}

const ProductSelection: React.FC<ProductSelectionProps> = ({
  products,
  categories,
  selectedCategory,
  onCategoryChange,
  onProductSelect,
  onAddToCart,
  loading,
  error
}) => {
  const getCategoryName = (category: string): string => {
    const names: Record<string, string> = {
      'all': '全部',
      'offering': '祭品',
      'decoration': '装饰',
      'effect': '特效'
    };
    return names[category] || category;
  };

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>正在加载商品...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.error}>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>重新加载</button>
      </div>
    );
  }

  return (
    <div className={styles.productSelection}>
      <div className={styles.header}>
        <h2>虚拟商品商城</h2>
        <p>为您的纪念空间添加特殊意义的虚拟物品</p>
      </div>

      <div className={styles.categoryFilter}>
        {categories.map(category => (
          <button
            key={category}
            className={`${styles.categoryButton} ${
              selectedCategory === category ? styles.active : ''
            }`}
            onClick={() => onCategoryChange(category)}
          >
            {getCategoryName(category)}
          </button>
        ))}
      </div>

      <div className={styles.productGrid}>
        {products.map(product => (
          <ProductCard
            key={product.id}
            product={product}
            onSelect={() => onProductSelect(product)}
            onAddToCart={() => onAddToCart(product)}
          />
        ))}
      </div>

      {products.length === 0 && (
        <div className={styles.empty}>
          <p>该分类下暂无商品</p>
        </div>
      )}
    </div>
  );
};

// 商品卡片组件
interface ProductCardProps {
  product: VirtualProduct;
  onSelect: () => void;
  onAddToCart: () => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onSelect, onAddToCart }) => {
  return (
    <div className={styles.productCard}>
      <div className={styles.productImage}>
        <img src={product.imageUrl} alt={product.name} />
        {product.duration && (
          <div className={styles.durationBadge}>
            {product.duration}天
          </div>
        )}
      </div>

      <div className={styles.productInfo}>
        <h3>{product.name}</h3>
        <p className={styles.description}>{product.description}</p>
        
        <div className={styles.religiousCompatibility}>
          {product.religiousCompatibility.map(religion => (
            <span key={religion} className={styles.religionTag}>
              {religion}
            </span>
          ))}
        </div>

        <div className={styles.priceSection}>
          <span className={styles.price}>¥{product.price.toFixed(2)}</span>
          {product.customizations && product.customizations.length > 0 && (
            <span className={styles.customizable}>可定制</span>
          )}
        </div>

        <div className={styles.actions}>
          <button className={styles.detailButton} onClick={onSelect}>
            查看详情
          </button>
          <button className={styles.addButton} onClick={onAddToCart}>
            加入购物车
          </button>
        </div>
      </div>
    </div>
  );
};

// 商品定制组件
interface ProductCustomizationProps {
  product: VirtualProduct;
  customizations: Record<string, any>;
  onCustomizationChange: (customizations: Record<string, any>) => void;
  onAddToCart: () => void;
  onBack: () => void;
}

const ProductCustomization: React.FC<ProductCustomizationProps> = ({
  product,
  customizations,
  onCustomizationChange,
  onAddToCart,
  onBack
}) => {
  const handleCustomizationChange = (customizationId: string, value: any) => {
    const newCustomizations = {
      ...customizations,
      [customizationId]: value
    };
    onCustomizationChange(newCustomizations);
  };

  const calculateTotalPrice = (): number => {
    let total = product.price;
    if (product.customizations) {
      product.customizations.forEach(customization => {
        if (customizations[customization.id] && customization.price) {
          total += customization.price;
        }
      });
    }
    return total;
  };

  const isValid = (): boolean => {
    if (!product.customizations) return true;
    
    return product.customizations.every(customization => {
      if (customization.required) {
        return customizations[customization.id] != null;
      }
      return true;
    });
  };

  return (
    <div className={styles.productCustomization}>
      <div className={styles.header}>
        <button className={styles.backButton} onClick={onBack}>
          ← 返回
        </button>
        <h2>定制 {product.name}</h2>
      </div>

      <div className={styles.productPreview}>
        <img src={product.imageUrl} alt={product.name} />
        <div className={styles.productBasicInfo}>
          <h3>{product.name}</h3>
          <p>{product.description}</p>
          <span className={styles.basePrice}>基础价格: ¥{product.price.toFixed(2)}</span>
        </div>
      </div>

      <div className={styles.customizationOptions}>
        <h3>定制选项</h3>
        {product.customizations?.map(customization => (
          <CustomizationOption
            key={customization.id}
            customization={customization}
            value={customizations[customization.id]}
            onChange={(value) => handleCustomizationChange(customization.id, value)}
          />
        ))}
      </div>

      <div className={styles.summary}>
        <div className={styles.totalPrice}>
          总价: ¥{calculateTotalPrice().toFixed(2)}
        </div>
        <button 
          className={styles.addToCartButton}
          onClick={onAddToCart}
          disabled={!isValid()}
        >
          加入购物车
        </button>
      </div>
    </div>
  );
};

// 定制选项组件
interface CustomizationOptionProps {
  customization: ProductCustomization;
  value: any;
  onChange: (value: any) => void;
}

const CustomizationOption: React.FC<CustomizationOptionProps> = ({
  customization,
  value,
  onChange
}) => {
  const renderOption = () => {
    switch (customization.type) {
      case 'text':
        return (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            maxLength={customization.maxLength}
            placeholder={`请输入${customization.name}`}
            className={styles.textInput}
          />
        );
      
      case 'color':
        return (
          <div className={styles.colorOptions}>
            {customization.options?.map(option => (
              <button
                key={option}
                className={`${styles.colorOption} ${
                  value === option ? styles.selected : ''
                }`}
                onClick={() => onChange(option)}
              >
                {option}
              </button>
            ))}
          </div>
        );
      
      case 'size':
        return (
          <select
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className={styles.sizeSelect}
          >
            <option value="">请选择尺寸</option>
            {customization.options?.map(option => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
        );
      
      case 'image':
        return (
          <div className={styles.imageUpload}>
            <input
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  onChange(file);
                }
              }}
              className={styles.fileInput}
            />
            {value && (
              <div className={styles.preview}>
                已选择: {value.name || '图片文件'}
              </div>
            )}
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className={styles.customizationOption}>
      <div className={styles.optionHeader}>
        <label className={styles.optionLabel}>
          {customization.name}
          {customization.required && <span className={styles.required}>*</span>}
        </label>
        {customization.price && customization.price > 0 && (
          <span className={styles.optionPrice}>+¥{customization.price.toFixed(2)}</span>
        )}
      </div>
      {renderOption()}
    </div>
  );
};

// 购物车组件
interface ShoppingCartProps {
  items: ShoppingCartItem[];
  totalPrice: number;
  onUpdateCart: (cart: ShoppingCartItem[]) => void;
  onContinueShopping: () => void;
  onProceedToPayment: () => void;
}

const ShoppingCart: React.FC<ShoppingCartProps> = ({
  items,
  totalPrice,
  onUpdateCart,
  onContinueShopping,
  onProceedToPayment
}) => {
  const removeItem = (index: number) => {
    const newCart = items.filter((_, i) => i !== index);
    onUpdateCart(newCart);
  };

  if (items.length === 0) {
    return (
      <div className={styles.emptyCart}>
        <h2>购物车是空的</h2>
        <p>去选择一些虚拟商品吧！</p>
        <button onClick={onContinueShopping}>
          继续购物
        </button>
      </div>
    );
  }

  return (
    <div className={styles.shoppingCart}>
      <div className={styles.header}>
        <h2>购物车</h2>
        <button className={styles.continueButton} onClick={onContinueShopping}>
          继续购物
        </button>
      </div>

      <div className={styles.cartItems}>
        {items.map((item, index) => (
          <div key={index} className={styles.cartItem}>
            <img src={item.product.imageUrl} alt={item.product.name} />
            <div className={styles.itemInfo}>
              <h3>{item.product.name}</h3>
              <p>{item.product.description}</p>
              
              {Object.keys(item.customizations).length > 0 && (
                <div className={styles.customizations}>
                  <h4>定制选项:</h4>
                  {Object.entries(item.customizations).map(([key, value]) => (
                    <span key={key} className={styles.customization}>
                      {key}: {typeof value === 'object' ? value.name : value}
                    </span>
                  ))}
                </div>
              )}
              
              <div className={styles.itemPrice}>
                ¥{item.totalPrice.toFixed(2)}
              </div>
            </div>
            <button 
              className={styles.removeButton}
              onClick={() => removeItem(index)}
            >
              移除
            </button>
          </div>
        ))}
      </div>

      <div className={styles.cartSummary}>
        <div className={styles.total}>
          总计: ¥{totalPrice.toFixed(2)}
        </div>
        <button 
          className={styles.checkoutButton}
          onClick={onProceedToPayment}
        >
          去支付
        </button>
      </div>
    </div>
  );
};

// 购买完成组件
interface PurchaseCompletedProps {
  cart: ShoppingCartItem[];
  orderId: string;
  onContinueShopping: () => void;
}

const PurchaseCompleted: React.FC<PurchaseCompletedProps> = ({
  cart,
  orderId,
  onContinueShopping
}) => {
  return (
    <div className={styles.purchaseCompleted}>
      <div className={styles.successIcon}>✅</div>
      <h2>购买成功！</h2>
      <p>您的虚拟商品已成功添加到纪念空间</p>
      
      <div className={styles.orderInfo}>
        <h3>订单信息</h3>
        <p>订单号: {orderId}</p>
        <div className={styles.purchasedItems}>
          {cart.map((item, index) => (
            <div key={index} className={styles.purchasedItem}>
              <span>{item.product.name}</span>
              <span>¥{item.totalPrice.toFixed(2)}</span>
            </div>
          ))}
        </div>
      </div>

      <div className={styles.actions}>
        <button className={styles.continueButton} onClick={onContinueShopping}>
          继续购物
        </button>
        <button className={styles.viewSpaceButton}>
          查看纪念空间
        </button>
      </div>
    </div>
  );
};

export default VirtualProductPurchase;