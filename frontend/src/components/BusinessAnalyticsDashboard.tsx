import React, { useState, useEffect } from 'react';
import styles from './BusinessAnalyticsDashboard.module.css';

interface AnalyticsData {
  revenue: RevenueMetrics;
  users: UserMetrics;
  conversion: ConversionMetrics;
  retention: RetentionMetrics;
  products: ProductMetrics;
  growth: GrowthMetrics;
}

interface RevenueMetrics {
  mrr: number; // Monthly Recurring Revenue
  arr: number; // Annual Recurring Revenue
  totalRevenue: number;
  revenueGrowth: number;
  avgRevenuePerUser: number;
  bySource: RevenueBySource[];
  byPlan: RevenueByPlan[];
  trends: RevenueTrend[];
}

interface RevenueBySource {
  source: 'subscription' | 'virtual_products' | 'premium_services' | 'customization';
  amount: number;
  percentage: number;
  growth: number;
}

interface RevenueByPlan {
  plan: 'basic' | 'premium' | 'family' | 'enterprise';
  amount: number;
  users: number;
  arpu: number; // Average Revenue Per User
}

interface RevenueTrend {
  date: string;
  amount: number;
  subscriptions: number;
  virtualProducts: number;
  services: number;
}

interface UserMetrics {
  totalUsers: number;
  activeUsers: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  newUsers: number;
  userGrowthRate: number;
  churnRate: number;
  ltv: number; // Lifetime Value
  cac: number; // Customer Acquisition Cost
  payingUsers: number;
  conversionRate: number;
}

interface ConversionMetrics {
  funnelSteps: FunnelStep[];
  bySource: ConversionBySource[];
  timeToConvert: number;
  dropoffPoints: DropoffPoint[];
}

interface FunnelStep {
  step: string;
  users: number;
  conversionRate: number;
  dropoff: number;
}

interface ConversionBySource {
  source: string;
  visitors: number;
  conversions: number;
  rate: number;
}

interface DropoffPoint {
  point: string;
  users: number;
  reason: string;
}

interface RetentionMetrics {
  cohortAnalysis: CohortData[];
  retentionRates: {
    day1: number;
    day7: number;
    day30: number;
    day90: number;
  };
  engagementScore: number;
  stickiness: number;
}

interface CohortData {
  month: string;
  users: number;
  retention: number[];
}

interface ProductMetrics {
  virtualProducts: VirtualProductStats[];
  subscriptionPlans: SubscriptionStats[];
  premiumFeatures: FeatureUsageStats[];
  customerSatisfaction: number;
}

interface VirtualProductStats {
  productId: string;
  name: string;
  sales: number;
  revenue: number;
  popularity: number;
  rating: number;
}

interface SubscriptionStats {
  plan: string;
  subscribers: number;
  churnRate: number;
  avgLifetime: number;
  upgradeRate: number;
}

interface FeatureUsageStats {
  feature: string;
  usage: number;
  conversionImpact: number;
  satisfaction: number;
}

interface GrowthMetrics {
  nps: number; // Net Promoter Score
  referralRate: number;
  viralCoefficient: number;
  organicGrowth: number;
  paidGrowth: number;
  marketingROI: number;
}

export const BusinessAnalyticsDashboard: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [activeTab, setActiveTab] = useState<'overview' | 'revenue' | 'users' | 'conversion' | 'products'>('overview');

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedTimeRange]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      const data = await fetchAnalyticsData(selectedTimeRange);
      setAnalyticsData(data);
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAnalyticsData = async (_timeRange: string): Promise<AnalyticsData> => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      revenue: {
        mrr: 125000,
        arr: 1500000,
        totalRevenue: 2150000,
        revenueGrowth: 28.5,
        avgRevenuePerUser: 89.50,
        bySource: [
          { source: 'subscription', amount: 875000, percentage: 40.7, growth: 25.3 },
          { source: 'virtual_products', amount: 645000, percentage: 30.0, growth: 35.8 },
          { source: 'premium_services', amount: 430000, percentage: 20.0, growth: 18.2 },
          { source: 'customization', amount: 200000, percentage: 9.3, growth: 42.1 },
        ],
        byPlan: [
          { plan: 'basic', amount: 120000, users: 8000, arpu: 15.00 },
          { plan: 'premium', amount: 350000, users: 5000, arpu: 70.00 },
          { plan: 'family', amount: 720000, users: 4000, arpu: 180.00 },
          { plan: 'enterprise', amount: 960000, users: 800, arpu: 1200.00 },
        ],
        trends: [
          { date: '2024-01', amount: 185000, subscriptions: 125000, virtualProducts: 45000, services: 15000 },
          { date: '2024-02', amount: 198000, subscriptions: 132000, virtualProducts: 48000, services: 18000 },
          { date: '2024-03', amount: 215000, subscriptions: 145000, virtualProducts: 52000, services: 18000 },
          // ... more trend data
        ],
      },
      users: {
        totalUsers: 24500,
        activeUsers: {
          daily: 8420,
          weekly: 15680,
          monthly: 21200,
        },
        newUsers: 1250,
        userGrowthRate: 5.8,
        churnRate: 3.2,
        ltv: 450.00,
        cac: 85.50,
        payingUsers: 12800,
        conversionRate: 52.2,
      },
      conversion: {
        funnelSteps: [
          { step: '访问网站', users: 50000, conversionRate: 100, dropoff: 0 },
          { step: '注册账号', users: 8500, conversionRate: 17, dropoff: 83 },
          { step: '创建纪念空间', users: 6200, conversionRate: 72.9, dropoff: 27.1 },
          { step: '购买服务', users: 3100, conversionRate: 50, dropoff: 50 },
          { step: '成为付费用户', users: 2800, conversionRate: 90.3, dropoff: 9.7 },
        ],
        bySource: [
          { source: '搜索引擎', visitors: 25000, conversions: 4500, rate: 18 },
          { source: '社交媒体', visitors: 15000, conversions: 2100, rate: 14 },
          { source: '直接访问', visitors: 8000, conversions: 1600, rate: 20 },
          { source: '推荐链接', visitors: 2000, conversions: 300, rate: 15 },
        ],
        timeToConvert: 7.5,
        dropoffPoints: [
          { point: '注册流程', users: 41500, reason: '注册步骤过多' },
          { point: '首次使用', users: 2300, reason: '功能复杂度高' },
          { point: '付费转化', users: 300, reason: '价格敏感' },
        ],
      },
      retention: {
        cohortAnalysis: [
          { month: '2024-01', users: 1200, retention: [100, 85, 72, 68, 65, 62] },
          { month: '2024-02', users: 1350, retention: [100, 88, 75, 71, 68] },
          { month: '2024-03', users: 1500, retention: [100, 90, 78, 74] },
          { month: '2024-04', users: 1680, retention: [100, 92, 80] },
          { month: '2024-05', users: 1820, retention: [100, 94] },
          { month: '2024-06', users: 2000, retention: [100] },
        ],
        retentionRates: {
          day1: 94,
          day7: 78,
          day30: 65,
          day90: 52,
        },
        engagementScore: 8.2,
        stickiness: 0.68,
      },
      products: {
        virtualProducts: [
          { productId: 'incense_premium', name: '高级檀香', sales: 1250, revenue: 12475, popularity: 89, rating: 4.8 },
          { productId: 'lotus_flower', name: '莲花祭品', sales: 980, revenue: 15682, popularity: 76, rating: 4.9 },
          { productId: 'candle_set', name: '祈福蜡烛套装', sales: 750, revenue: 9742, popularity: 65, rating: 4.6 },
          { productId: 'cherry_blossom_effect', name: '樱花飘落特效', sales: 420, revenue: 12598, popularity: 92, rating: 4.9 },
        ],
        subscriptionPlans: [
          { plan: '基础版', subscribers: 8000, churnRate: 5.2, avgLifetime: 18, upgradeRate: 15.5 },
          { plan: '高级版', subscribers: 5000, churnRate: 3.1, avgLifetime: 28, upgradeRate: 8.2 },
          { plan: '家族版', subscribers: 4000, churnRate: 2.1, avgLifetime: 36, upgradeRate: 4.1 },
          { plan: '企业版', subscribers: 800, churnRate: 1.2, avgLifetime: 48, upgradeRate: 0 },
        ],
        premiumFeatures: [
          { feature: 'AI照片修复', usage: 85, conversionImpact: 32, satisfaction: 9.1 },
          { feature: '3D纪念空间', usage: 78, conversionImpact: 28, satisfaction: 8.8 },
          { feature: '语音克隆', usage: 45, conversionImpact: 45, satisfaction: 9.3 },
          { feature: 'AR体验', usage: 23, conversionImpact: 52, satisfaction: 9.5 },
        ],
        customerSatisfaction: 8.7,
      },
      growth: {
        nps: 68,
        referralRate: 12.5,
        viralCoefficient: 1.35,
        organicGrowth: 65,
        paidGrowth: 35,
        marketingROI: 4.2,
      },
    };
  };

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>正在加载分析数据...</p>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className={styles.error}>
        <p>无法加载分析数据</p>
        <button onClick={loadAnalyticsData}>重试</button>
      </div>
    );
  }

  return (
    <div className={styles.analyticsDashboard}>
      {/* 头部控制栏 */}
      <div className={styles.header}>
        <div className={styles.headerTitle}>
          <h1>商业化数据分析</h1>
          <p>实时监控业务指标和用户行为</p>
        </div>
        
        <div className={styles.controls}>
          <select 
            value={selectedTimeRange} 
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className={styles.timeRangeSelector}
          >
            <option value="7d">近7天</option>
            <option value="30d">近30天</option>
            <option value="90d">近90天</option>
            <option value="1y">近1年</option>
          </select>
        </div>
      </div>

      {/* 标签导航 */}
      <div className={styles.tabNavigation}>
        {[
          { key: 'overview', label: '概览' },
          { key: 'revenue', label: '收入分析' },
          { key: 'users', label: '用户分析' },
          { key: 'conversion', label: '转化分析' },
          { key: 'products', label: '产品分析' },
        ].map((tab) => (
          <button
            key={tab.key}
            className={`${styles.tabButton} ${activeTab === tab.key ? styles.active : ''}`}
            onClick={() => setActiveTab(tab.key as any)}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* 内容区域 */}
      <div className={styles.content}>
        {activeTab === 'overview' && <OverviewTab data={analyticsData} />}
        {activeTab === 'revenue' && <RevenueTab data={analyticsData.revenue} />}
        {activeTab === 'users' && <UsersTab data={analyticsData.users} />}
        {activeTab === 'conversion' && <ConversionTab data={analyticsData.conversion} />}
        {activeTab === 'products' && <ProductsTab data={analyticsData.products} />}
      </div>
    </div>
  );
};

// 概览标签页
const OverviewTab: React.FC<{ data: AnalyticsData }> = ({ data }) => {
  return (
    <div className={styles.overviewTab}>
      {/* 关键指标卡片 */}
      <div className={styles.kpiCards}>
        <KPICard 
          title="月度经常性收入" 
          value={`¥${(data.revenue.mrr / 1000).toFixed(0)}K`}
          change={data.revenue.revenueGrowth}
          format="currency"
        />
        <KPICard 
          title="活跃用户数" 
          value={data.users.activeUsers.monthly.toLocaleString()}
          change={data.users.userGrowthRate}
          format="number"
        />
        <KPICard 
          title="付费转化率" 
          value={`${data.users.conversionRate.toFixed(1)}%`}
          change={5.2}
          format="percentage"
        />
        <KPICard 
          title="用户留存率" 
          value={`${data.retention.retentionRates.day30}%`}
          change={2.8}
          format="percentage"
        />
      </div>

      {/* 收入趋势图 */}
      <div className={styles.chartSection}>
        <h3>收入趋势</h3>
        <RevenueTrendChart trends={data.revenue.trends} />
      </div>

      {/* 用户活跃度图 */}
      <div className={styles.chartSection}>
        <h3>用户活跃度</h3>
        <UserActivityChart data={data.users} />
      </div>
    </div>
  );
};

// 收入分析标签页
const RevenueTab: React.FC<{ data: RevenueMetrics }> = ({ data }) => {
  return (
    <div className={styles.revenueTab}>
      <div className={styles.revenueGrid}>
        {/* 收入概况 */}
        <div className={styles.revenueOverview}>
          <h3>收入概况</h3>
          <div className={styles.revenueStats}>
            <div className={styles.revenueStat}>
              <span className={styles.label}>总收入</span>
              <span className={styles.value}>¥{(data.totalRevenue / 1000000).toFixed(2)}M</span>
            </div>
            <div className={styles.revenueStat}>
              <span className={styles.label}>月度经常性收入</span>
              <span className={styles.value}>¥{(data.mrr / 1000).toFixed(0)}K</span>
            </div>
            <div className={styles.revenueStat}>
              <span className={styles.label}>年度经常性收入</span>
              <span className={styles.value}>¥{(data.arr / 1000000).toFixed(1)}M</span>
            </div>
            <div className={styles.revenueStat}>
              <span className={styles.label}>平均每用户收入</span>
              <span className={styles.value}>¥{data.avgRevenuePerUser.toFixed(2)}</span>
            </div>
          </div>
        </div>

        {/* 收入来源分布 */}
        <div className={styles.revenueBySource}>
          <h3>收入来源分布</h3>
          <div className={styles.sourceList}>
            {data.bySource.map((source, index) => (
              <div key={index} className={styles.sourceItem}>
                <div className={styles.sourceInfo}>
                  <span className={styles.sourceName}>
                    {getSourceName(source.source)}
                  </span>
                  <span className={styles.sourceGrowth}>
                    +{source.growth.toFixed(1)}%
                  </span>
                </div>
                <div className={styles.sourceValue}>
                  <span>¥{(source.amount / 1000).toFixed(0)}K</span>
                  <span className={styles.percentage}>({source.percentage.toFixed(1)}%)</span>
                </div>
                <div className={styles.progressBar}>
                  <div 
                    className={styles.progress}
                    style={{ width: `${source.percentage}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 按计划收入分析 */}
        <div className={styles.revenueByPlan}>
          <h3>按订阅计划收入</h3>
          <div className={styles.planList}>
            {data.byPlan.map((plan, index) => (
              <div key={index} className={styles.planItem}>
                <div className={styles.planHeader}>
                  <span className={styles.planName}>{getPlanName(plan.plan)}</span>
                  <span className={styles.planRevenue}>¥{(plan.amount / 1000).toFixed(0)}K</span>
                </div>
                <div className={styles.planStats}>
                  <span>用户数: {plan.users.toLocaleString()}</span>
                  <span>ARPU: ¥{plan.arpu.toFixed(2)}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// 用户分析标签页
const UsersTab: React.FC<{ data: UserMetrics }> = ({ data }) => {
  return (
    <div className={styles.usersTab}>
      <div className={styles.userMetricsGrid}>
        {/* 用户概况 */}
        <div className={styles.userOverview}>
          <h3>用户概况</h3>
          <div className={styles.userStats}>
            <div className={styles.userStat}>
              <span className={styles.value}>{data.totalUsers.toLocaleString()}</span>
              <span className={styles.label}>总用户数</span>
            </div>
            <div className={styles.userStat}>
              <span className={styles.value}>{data.payingUsers.toLocaleString()}</span>
              <span className={styles.label}>付费用户</span>
            </div>
            <div className={styles.userStat}>
              <span className={styles.value}>{data.newUsers.toLocaleString()}</span>
              <span className={styles.label}>新增用户</span>
            </div>
          </div>
        </div>

        {/* 活跃用户 */}
        <div className={styles.activeUsers}>
          <h3>活跃用户</h3>
          <div className={styles.activeUserStats}>
            <div className={styles.activeUserStat}>
              <span className={styles.label}>日活跃</span>
              <span className={styles.value}>{data.activeUsers.daily.toLocaleString()}</span>
            </div>
            <div className={styles.activeUserStat}>
              <span className={styles.label}>周活跃</span>
              <span className={styles.value}>{data.activeUsers.weekly.toLocaleString()}</span>
            </div>
            <div className={styles.activeUserStat}>
              <span className={styles.label}>月活跃</span>
              <span className={styles.value}>{data.activeUsers.monthly.toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* 关键指标 */}
        <div className={styles.keyMetrics}>
          <h3>关键指标</h3>
          <div className={styles.metricsList}>
            <div className={styles.metric}>
              <span className={styles.label}>转化率</span>
              <span className={styles.value}>{data.conversionRate.toFixed(1)}%</span>
            </div>
            <div className={styles.metric}>
              <span className={styles.label}>流失率</span>
              <span className={styles.value}>{data.churnRate.toFixed(1)}%</span>
            </div>
            <div className={styles.metric}>
              <span className={styles.label}>用户生命周期价值</span>
              <span className={styles.value}>¥{data.ltv.toFixed(2)}</span>
            </div>
            <div className={styles.metric}>
              <span className={styles.label}>获客成本</span>
              <span className={styles.value}>¥{data.cac.toFixed(2)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// 转化分析标签页
const ConversionTab: React.FC<{ data: ConversionMetrics }> = ({ data }) => {
  return (
    <div className={styles.conversionTab}>
      {/* 转化漏斗 */}
      <div className={styles.conversionFunnel}>
        <h3>转化漏斗</h3>
        <div className={styles.funnelSteps}>
          {data.funnelSteps.map((step, index) => (
            <div key={index} className={styles.funnelStep}>
              <div className={styles.stepInfo}>
                <span className={styles.stepName}>{step.step}</span>
                <span className={styles.stepUsers}>{step.users.toLocaleString()} 用户</span>
              </div>
              <div className={styles.stepMetrics}>
                <span className={styles.conversionRate}>
                  转化率: {step.conversionRate.toFixed(1)}%
                </span>
                {index > 0 && (
                  <span className={styles.dropoff}>
                    流失: {step.dropoff.toFixed(1)}%
                  </span>
                )}
              </div>
              <div 
                className={styles.funnelBar}
                style={{ width: `${(step.users / data.funnelSteps[0].users) * 100}%` }}
              />
            </div>
          ))}
        </div>
      </div>

      {/* 按来源转化 */}
      <div className={styles.conversionBySource}>
        <h3>按来源转化率</h3>
        <div className={styles.sourceConversions}>
          {data.bySource.map((source, index) => (
            <div key={index} className={styles.sourceConversion}>
              <div className={styles.sourceInfo}>
                <span className={styles.sourceName}>{source.source}</span>
                <span className={styles.sourceRate}>{source.rate}%</span>
              </div>
              <div className={styles.sourceNumbers}>
                <span>访客: {source.visitors.toLocaleString()}</span>
                <span>转化: {source.conversions.toLocaleString()}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// 产品分析标签页
const ProductsTab: React.FC<{ data: ProductMetrics }> = ({ data }) => {
  return (
    <div className={styles.productsTab}>
      {/* 虚拟商品表现 */}
      <div className={styles.virtualProducts}>
        <h3>虚拟商品表现</h3>
        <div className={styles.productList}>
          {data.virtualProducts.map((product, index) => (
            <div key={index} className={styles.productItem}>
              <div className={styles.productInfo}>
                <span className={styles.productName}>{product.name}</span>
                <span className={styles.productRating}>⭐ {product.rating}</span>
              </div>
              <div className={styles.productMetrics}>
                <div className={styles.metric}>
                  <span className={styles.label}>销量</span>
                  <span className={styles.value}>{product.sales}</span>
                </div>
                <div className={styles.metric}>
                  <span className={styles.label}>收入</span>
                  <span className={styles.value}>¥{product.revenue.toLocaleString()}</span>
                </div>
                <div className={styles.metric}>
                  <span className={styles.label}>热度</span>
                  <span className={styles.value}>{product.popularity}%</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 订阅计划表现 */}
      <div className={styles.subscriptionPlans}>
        <h3>订阅计划表现</h3>
        <div className={styles.plansList}>
          {data.subscriptionPlans.map((plan, index) => (
            <div key={index} className={styles.planItem}>
              <div className={styles.planInfo}>
                <span className={styles.planName}>{plan.plan}</span>
                <span className={styles.subscribers}>{plan.subscribers} 订阅者</span>
              </div>
              <div className={styles.planMetrics}>
                <div className={styles.metric}>
                  <span className={styles.label}>流失率</span>
                  <span className={styles.value}>{plan.churnRate}%</span>
                </div>
                <div className={styles.metric}>
                  <span className={styles.label}>平均寿命</span>
                  <span className={styles.value}>{plan.avgLifetime}月</span>
                </div>
                <div className={styles.metric}>
                  <span className={styles.label}>升级率</span>
                  <span className={styles.value}>{plan.upgradeRate}%</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

// KPI卡片组件
interface KPICardProps {
  title: string;
  value: string;
  change: number;
  format: 'currency' | 'number' | 'percentage';
}

const KPICard: React.FC<KPICardProps> = ({ title, value, change }) => {
  const isPositive = change > 0;
  
  return (
    <div className={styles.kpiCard}>
      <div className={styles.kpiHeader}>
        <h4>{title}</h4>
        <span className={`${styles.changeIndicator} ${isPositive ? styles.positive : styles.negative}`}>
          {isPositive ? '↑' : '↓'} {Math.abs(change).toFixed(1)}%
        </span>
      </div>
      <div className={styles.kpiValue}>{value}</div>
    </div>
  );
};

// 收入趋势图组件
const RevenueTrendChart: React.FC<{ trends: RevenueTrend[] }> = ({ }) => {
  return (
    <div className={styles.chartContainer}>
      <div className={styles.trendChart}>
        {/* 这里应该使用真实的图表库如Chart.js或D3.js */}
        <div className={styles.chartPlaceholder}>
          <p>收入趋势图表</p>
          <p>显示订阅、虚拟商品、服务收入的时间序列数据</p>
        </div>
      </div>
    </div>
  );
};

// 用户活跃度图组件
const UserActivityChart: React.FC<{ data: UserMetrics }> = ({ }) => {
  return (
    <div className={styles.chartContainer}>
      <div className={styles.activityChart}>
        <div className={styles.chartPlaceholder}>
          <p>用户活跃度图表</p>
          <p>显示日活、周活、月活用户的趋势变化</p>
        </div>
      </div>
    </div>
  );
};

// 辅助函数
const getSourceName = (source: string): string => {
  const names: Record<string, string> = {
    'subscription': '订阅收入',
    'virtual_products': '虚拟商品',
    'premium_services': '增值服务',
    'customization': '定制服务',
  };
  return names[source] || source;
};

const getPlanName = (plan: string): string => {
  const names: Record<string, string> = {
    'basic': '基础版',
    'premium': '高级版',
    'family': '家族版',
    'enterprise': '企业版',
  };
  return names[plan] || plan;
};

export default BusinessAnalyticsDashboard;