.language-switcher {
  display: flex;
  gap: 10px;
}

.language-button {
  background: var(--color-gray-900);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.language-button:hover {
  background: var(--color-gray-900);
}

.language-button.active {
  background: var(--color-primary);
}

@media (max-width: 768px) {
  .language-switcher {
    gap: 5px;
  }

  .language-button {
    padding: 6px 10px;
    font-size: 0.9rem;
  }
}
