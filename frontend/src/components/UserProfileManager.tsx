import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import AuthContext from '../contexts/AuthContext';
import NotificationService from '../utils/NotificationService';

// 用户信息接口
interface UserProfile {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  bio?: string;
  phone?: string;
  location?: string;
  birth_date?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_say';
  is_email_verified: boolean;
  created_at: string;
  updated_at: string;
  preferences?: UserPreferences;
}

// 用户偏好设置接口
interface UserPreferences {
  language: 'zh' | 'en';
  theme: 'light' | 'dark' | 'auto';
  email_notifications: boolean;
  privacy_level: 'public' | 'friends' | 'private';
  auto_save: boolean;
  quality_preference: 'auto' | 'high' | 'medium' | 'low';
}

// 密码修改接口
interface PasswordChangeData {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

interface UserProfileManagerProps {
  onClose?: () => void;
}

const UserProfileManager: React.FC<UserProfileManagerProps> = ({ onClose }) => {
  const authContext = useContext(AuthContext);
  
  if (!authContext) {
    throw new Error('UserProfileManager must be used within an AuthProvider');
  }
  
  const { user: _user, logout } = authContext;
  const [activeTab, setActiveTab] = useState<'profile' | 'security' | 'preferences' | 'memorials'>('profile');
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // 表单数据状态
  const [profileForm, setProfileForm] = useState<Partial<UserProfile>>({});
  const [passwordForm, setPasswordForm] = useState<PasswordChangeData>({
    current_password: '',
    new_password: '',
    confirm_password: ''
  });
  const [preferencesForm, setPreferencesForm] = useState<UserPreferences>({
    language: 'zh',
    theme: 'auto',
    email_notifications: true,
    privacy_level: 'friends',
    auto_save: true,
    quality_preference: 'auto'
  });

  // 获取用户信息
  useEffect(() => {
    fetchUserProfile();
  }, []);

  const fetchUserProfile = async () => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/v1/users/me', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const profile = await response.json();
        setUserProfile(profile);
        setProfileForm(profile);
        if (profile.preferences) {
          setPreferencesForm(profile.preferences);
        }
      } else {
        throw new Error('获取用户信息失败');
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      NotificationService.error('获取失败', '无法获取用户信息，请刷新页面重试');
    } finally {
      setLoading(false);
    }
  };

  // 更新个人信息
  const updateProfile = async () => {
    setSaving(true);
    setErrors({});

    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/v1/users/me', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(profileForm),
      });

      if (response.ok) {
        const updatedProfile = await response.json();
        setUserProfile(updatedProfile);
        NotificationService.success('更新成功', '个人信息已成功更新');
      } else {
        const errorData = await response.json();
        throw new Error(errorData.detail || '更新失败');
      }
    } catch (error) {
      console.error('更新个人信息失败:', error);
      NotificationService.error('更新失败', error instanceof Error ? error.message : '更新个人信息失败');
    } finally {
      setSaving(false);
    }
  };

  // 修改密码
  const changePassword = async () => {
    if (passwordForm.new_password !== passwordForm.confirm_password) {
      setErrors({ confirm_password: '两次输入的密码不一致' });
      return;
    }

    if (passwordForm.new_password.length < 8) {
      setErrors({ new_password: '密码长度至少为8位' });
      return;
    }

    setSaving(true);
    setErrors({});

    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/v1/users/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          current_password: passwordForm.current_password,
          new_password: passwordForm.new_password,
        }),
      });

      if (response.ok) {
        setPasswordForm({ current_password: '', new_password: '', confirm_password: '' });
        NotificationService.success('密码修改成功', '请使用新密码重新登录');
        // 3秒后自动登出
        setTimeout(() => {
          logout();
        }, 3000);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.detail || '密码修改失败');
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      NotificationService.error('修改失败', error instanceof Error ? error.message : '密码修改失败');
    } finally {
      setSaving(false);
    }
  };

  // 更新偏好设置
  const updatePreferences = async () => {
    setSaving(true);

    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/v1/users/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(preferencesForm),
      });

      if (response.ok) {
        NotificationService.success('设置已保存', '偏好设置已成功更新');
      } else {
        throw new Error('保存设置失败');
      }
    } catch (error) {
      console.error('更新偏好设置失败:', error);
      NotificationService.error('保存失败', '偏好设置保存失败');
    } finally {
      setSaving(false);
    }
  };

  // 渲染个人资料标签页
  const renderProfileTab = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            用户名
          </label>
          <input
            type="text"
            value={profileForm.username || ''}
            onChange={(e) => setProfileForm(prev => ({ ...prev, username: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="请输入用户名"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            全名
          </label>
          <input
            type="text"
            value={profileForm.full_name || ''}
            onChange={(e) => setProfileForm(prev => ({ ...prev, full_name: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="请输入全名"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            邮箱
          </label>
          <div className="relative">
            <input
              type="email"
              value={profileForm.email || ''}
              onChange={(e) => setProfileForm(prev => ({ ...prev, email: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入邮箱地址"
            />
            {userProfile?.is_email_verified ? (
              <span className="absolute right-2 top-2 text-green-500 text-sm">✓ 已验证</span>
            ) : (
              <span className="absolute right-2 top-2 text-orange-500 text-sm">未验证</span>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            手机号
          </label>
          <input
            type="tel"
            value={profileForm.phone || ''}
            onChange={(e) => setProfileForm(prev => ({ ...prev, phone: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="请输入手机号"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            性别
          </label>
          <select
            value={profileForm.gender || ''}
            onChange={(e) => setProfileForm(prev => ({ ...prev, gender: e.target.value as any }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">请选择</option>
            <option value="male">男</option>
            <option value="female">女</option>
            <option value="other">其他</option>
            <option value="prefer_not_say">不愿透露</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            出生日期
          </label>
          <input
            type="date"
            value={profileForm.birth_date || ''}
            onChange={(e) => setProfileForm(prev => ({ ...prev, birth_date: e.target.value }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          个人简介
        </label>
        <textarea
          value={profileForm.bio || ''}
          onChange={(e) => setProfileForm(prev => ({ ...prev, bio: e.target.value }))}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="介绍一下自己..."
        />
      </div>

      <div className="flex justify-end">
        <button
          onClick={updateProfile}
          disabled={saving}
          className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {saving ? '保存中...' : '保存更改'}
        </button>
      </div>
    </div>
  );

  // 渲染安全设置标签页
  const renderSecurityTab = () => (
    <div className="space-y-6">
      <div className="max-w-md">
        <h3 className="text-lg font-medium text-gray-900 mb-4">修改密码</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              当前密码
            </label>
            <input
              type="password"
              value={passwordForm.current_password}
              onChange={(e) => setPasswordForm(prev => ({ ...prev, current_password: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入当前密码"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              新密码
            </label>
            <input
              type="password"
              value={passwordForm.new_password}
              onChange={(e) => setPasswordForm(prev => ({ ...prev, new_password: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入新密码（至少8位）"
            />
            {errors.new_password && (
              <p className="mt-1 text-sm text-red-600">{errors.new_password}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              确认新密码
            </label>
            <input
              type="password"
              value={passwordForm.confirm_password}
              onChange={(e) => setPasswordForm(prev => ({ ...prev, confirm_password: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请再次输入新密码"
            />
            {errors.confirm_password && (
              <p className="mt-1 text-sm text-red-600">{errors.confirm_password}</p>
            )}
          </div>

          <button
            onClick={changePassword}
            disabled={saving || !passwordForm.current_password || !passwordForm.new_password}
            className="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {saving ? '修改中...' : '修改密码'}
          </button>
        </div>
      </div>
    </div>
  );

  // 渲染偏好设置标签页
  const renderPreferencesTab = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            语言设置
          </label>
          <select
            value={preferencesForm.language}
            onChange={(e) => setPreferencesForm(prev => ({ ...prev, language: e.target.value as any }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="zh">中文</option>
            <option value="en">English</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            主题设置
          </label>
          <select
            value={preferencesForm.theme}
            onChange={(e) => setPreferencesForm(prev => ({ ...prev, theme: e.target.value as any }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="auto">跟随系统</option>
            <option value="light">浅色主题</option>
            <option value="dark">深色主题</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            隐私级别
          </label>
          <select
            value={preferencesForm.privacy_level}
            onChange={(e) => setPreferencesForm(prev => ({ ...prev, privacy_level: e.target.value as any }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="public">公开</option>
            <option value="friends">仅朋友</option>
            <option value="private">私密</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            性能偏好
          </label>
          <select
            value={preferencesForm.quality_preference}
            onChange={(e) => setPreferencesForm(prev => ({ ...prev, quality_preference: e.target.value as any }))}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="auto">自动调节</option>
            <option value="high">高质量</option>
            <option value="medium">中等质量</option>
            <option value="low">低质量</option>
          </select>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="email_notifications"
            checked={preferencesForm.email_notifications}
            onChange={(e) => setPreferencesForm(prev => ({ ...prev, email_notifications: e.target.checked }))}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="email_notifications" className="ml-2 block text-sm text-gray-900">
            接收邮件通知
          </label>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="auto_save"
            checked={preferencesForm.auto_save}
            onChange={(e) => setPreferencesForm(prev => ({ ...prev, auto_save: e.target.checked }))}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="auto_save" className="ml-2 block text-sm text-gray-900">
            自动保存
          </label>
        </div>
      </div>

      <div className="flex justify-end">
        <button
          onClick={updatePreferences}
          disabled={saving}
          className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {saving ? '保存中...' : '保存设置'}
        </button>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg">
      {/* 头部 */}
      <div className="flex items-center justify-between p-6 border-b">
        <div className="flex items-center space-x-4">
          <div className="h-12 w-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
            {userProfile?.username?.[0]?.toUpperCase() || 'U'}
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">{userProfile?.username || '用户'}</h2>
            <p className="text-sm text-gray-500">{userProfile?.email}</p>
          </div>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* 标签导航 */}
      <div className="border-b">
        <nav className="flex space-x-8 px-6">
          {[
            { key: 'profile', label: '个人资料' },
            { key: 'security', label: '安全设置' },
            { key: 'preferences', label: '偏好设置' },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* 标签内容 */}
      <div className="p-6">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            {activeTab === 'profile' && renderProfileTab()}
            {activeTab === 'security' && renderSecurityTab()}
            {activeTab === 'preferences' && renderPreferencesTab()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default UserProfileManager;