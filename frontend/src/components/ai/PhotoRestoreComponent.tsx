import React, { useState } from "react";
import { Card, Form, Image, Spinner, Alert } from "react-bootstrap";
import axios from "axios";

interface PhotoRestoreProps {
  onComplete?: (originalUrl: string, resultUrl: string) => void;
  className?: string;
}

const PhotoRestoreComponent: React.FC<PhotoRestoreProps> = ({
  onComplete,
  className,
}) => {
  // TODO: 实现国际化
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [result, setResult] = useState<{
    original_url: string;
    restored_url: string;
  } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);

      // 创建预览
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setPreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(selectedFile);

      // 重置状态
      setResult(null);
      setError(null);
      setSuccess(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      setError("请选择一个图片文件");
      return;
    }

    setLoading(true);
    setError(null);

    const formData = new FormData();
    formData.append("file", file);

    try {
      const token = localStorage.getItem("authToken");
      const response = await axios.post("/api/v1/ai-enhanced/photo-restore", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
          "Authorization": `Bearer ${token}`,
        },
      });

      if (response.data.success) {
        setResult({
          original_url: response.data.original_url,
          restored_url: response.data.result_url,
        });
        setSuccess(true);

        if (onComplete) {
          onComplete(response.data.original_url, response.data.result_url);
        }
      } else {
        setError(response.data.message || "照片修复失败");
      }
    } catch (err) {
      console.error("照片修复失败:", err);
      let errorMessage = "照片修复失败";
      if (
        axios.isAxiosError(err) &&
        err.response &&
        err.response.data &&
        err.response.data.message
      ) {
        errorMessage = err.response.data.message;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className={className}>
      <Card.Header>
        <h5>🔧 AI照片修复</h5>
      </Card.Header>
      <Card.Body>
        <p className="text-muted">
          使用AI技术修复老照片中的噪点、划痕和模糊，让珍贵回忆重新焕发光彩。
        </p>

        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>选择照片</Form.Label>
            <Form.Control
              type="file"
              onChange={handleFileChange}
              accept="image/*"
              disabled={loading}
            />
          </Form.Group>

          {preview && (
            <div className="mb-3">
              <p>预览</p>
              <Image
                src={preview}
                alt="预览"
                thumbnail
                style={{ maxHeight: "200px" }}
              />
            </div>
          )}

          <button
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            type="submit"
            disabled={!file || loading}
          >
            {loading && (
              <Spinner
                as="span"
                animation="border"
                size="sm"
                role="status"
                aria-hidden="true"
                className="me-2"
              />
            )}
            {loading ? "修复中..." : "开始修复"}
          </button>
        </Form>

        {error && (
          <Alert variant="danger" className="mt-3">
            {error}
          </Alert>
        )}

        {success && result && (
          <div className="mt-4">
            <h6>修复结果</h6>
            <div className="d-flex flex-wrap">
              <div className="me-3 mb-3">
                <p>原始照片</p>
                <Image
                  src={result.original_url}
                  alt="原始照片"
                  thumbnail
                  style={{ maxHeight: "200px" }}
                />
              </div>
              <div>
                <p>修复后照片</p>
                <Image
                  src={result.restored_url}
                  alt="修复后照片"
                  thumbnail
                  style={{ maxHeight: "200px" }}
                />
                <a
                  href={result.restored_url}
                  download="restored_photo.png"
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200 mt-2 block text-center no-underline"
                >
                  下载修复后的照片
                </a>
              </div>
            </div>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default PhotoRestoreComponent;
