import React, { useState } from "react";
import axios from "axios";

interface PhotoRemoveBgProps {
  onComplete?: (originalUrl: string, resultUrl: string) => void;
  className?: string;
}

const PhotoRemoveBgComponent: React.FC<PhotoRemoveBgProps> = ({
  onComplete,
  className,
}) => {
  // TODO: 实现国际化
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [result, setResult] = useState<{
    original_url: string;
    no_bg_url: string;
    options_used?: any;
  } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  // 模型选项
  const [model, setModel] = useState<string>("u2net");

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);

      // 创建预览
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setPreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(selectedFile);

      // 重置状态
      setResult(null);
      setError(null);
      setSuccess(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      setError("请选择一个图片文件");
      return;
    }

    setLoading(true);
    setError(null);

    const formData = new FormData();
    formData.append("file", file);
    formData.append("model", model);

    try {
      const response = await axios.post("/api/ai/photo-remove-bg", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response.data.success) {
        setResult({
          original_url: response.data.original_url,
          no_bg_url: response.data.result_url,
          options_used: response.data.options_used,
        });
        setSuccess(true);

        if (onComplete) {
          onComplete(response.data.original_url, response.data.result_url);
        }
      } else {
        setError(response.data.message || "背景移除失败");
      }
    } catch (err) {
      console.error("背景移除失败:", err);
      let errorMessage = "背景移除失败";
      if (
        axios.isAxiosError(err) &&
        err.response &&
        err.response.data &&
        err.response.data.detail
      ) {
        errorMessage = err.response.data.detail;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`bg-gray-800 rounded-lg border border-gray-700 ${className}`}>
      <div className="px-4 py-3 border-b border-gray-700">
        <h5 className="text-lg font-semibold text-white m-0">✂️ AI背景移除</h5>
      </div>
      <div className="p-4">
        <p className="text-gray-400 mb-4">
          使用AI技术智能移除照片背景，生成透明背景的图像，方便后续编辑和合成。
        </p>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">
              选择照片
            </label>
            <input
              type="file"
              onChange={handleFileChange}
              accept="image/*"
              disabled={loading}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
            />
          </div>

          {/* 模型选择 */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">
              AI模型
            </label>
            <select
              value={model}
              onChange={(e) => setModel(e.target.value)}
              disabled={loading}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <option value="u2net">U2-Net (通用模型，适合大多数图片)</option>
              <option value="silueta">Silueta (人物模型，更适合人像)</option>
            </select>
            <p className="text-sm text-gray-400">
              选择适合您图片类型的AI模型以获得最佳效果
            </p>
          </div>

          {preview && (
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-300">预览</p>
              <div className="flex justify-center">
                <img
                  src={preview}
                  alt="预览"
                  className="max-h-48 rounded-lg border border-gray-600"
                />
              </div>
            </div>
          )}

          <button
            className="w-full sm:w-auto px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            type="submit"
            disabled={!file || loading}
          >
            {loading && (
              <div className="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
            )}
            {loading ? "处理中..." : "移除背景"}
          </button>
        </form>

        {error && (
          <div className="mt-4 p-4 bg-red-900/50 border border-red-700 rounded-lg">
            <p className="text-red-200 m-0">{error}</p>
          </div>
        )}

        {success && result && (
          <div className="mt-6">
            <h6 className="text-lg font-semibold text-white mb-4">处理结果</h6>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-300 mb-2">原始照片</p>
                <div className="bg-gray-100 p-2 rounded-lg">
                  <img
                    src={result.original_url}
                    alt="原始照片"
                    className="w-full h-48 object-contain rounded border border-gray-300"
                  />
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-300 mb-2">移除背景后 (透明背景)</p>
                <div 
                  className="p-2 rounded-lg"
                  style={{
                    background: "repeating-conic-gradient(#808080 0% 25%, transparent 0% 50%) 50% / 20px 20px"
                  }}
                >
                  <img
                    src={result.no_bg_url}
                    alt="移除背景后"
                    className="w-full h-48 object-contain rounded"
                  />
                </div>
                <div className="mt-3">
                  <a
                    href={result.no_bg_url}
                    download="no_background_photo.png"
                    className="w-full inline-block px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors duration-200 text-center no-underline"
                  >
                    下载透明背景图
                  </a>
                </div>
              </div>
            </div>
            
            {result.options_used && (
              <div className="mt-4 p-3 bg-gray-700 rounded-lg">
                <p className="text-sm text-gray-300 m-0">
                  使用模型: {result.options_used.model === "u2net" ? "U2-Net (通用)" : "Silueta (人像)"}
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PhotoRemoveBgComponent;