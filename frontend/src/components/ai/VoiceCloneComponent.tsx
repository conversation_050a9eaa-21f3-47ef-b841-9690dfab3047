import React, { useState } from "react";
import { <PERSON>, Form, Spinner, Alert } from "react-bootstrap";
import axios from "axios";
import { useTranslation } from "react-i18next";

interface VoiceCloneProps {
  onComplete?: (resultUrl: string) => void;
  className?: string;
}

const VoiceCloneComponent: React.FC<VoiceCloneProps> = ({
  onComplete,
  className,
}) => {
  const { t } = useTranslation();
  const [file, setFile] = useState<File | null>(null);
  const [text, setText] = useState("");
  const [originalAudio, setOriginalAudio] = useState<string | null>(null);
  const [resultAudio, setResultAudio] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);

      // 创建音频预览
      const audioUrl = URL.createObjectURL(selectedFile);
      setOriginalAudio(audioUrl);

      // 重置状态
      setResultAudio(null);
      setError(null);
      setSuccess(false);
    }
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      setError(t("ai.voiceClone.noFileError"));
      return;
    }

    if (!text.trim()) {
      setError(t("ai.voiceClone.noTextError"));
      return;
    }

    setLoading(true);
    setError(null);

    const formData = new FormData();
    formData.append("file", file);
    formData.append("text", text);
    formData.append("language", "zh");

    try {
      const response = await axios.post("/api/ai/clone-voice", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response.data.success) {
        setResultAudio(response.data.result_url);
        setSuccess(true);

        if (onComplete) {
          onComplete(response.data.result_url);
        }
      } else {
        setError(response.data.message || t("ai.voiceClone.genericError"));
      }
    } catch (err) {
      console.error("声音克隆失败:", err);
      setError(
        err instanceof Error ? err.message : t("ai.voiceClone.genericError"),
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className={className}>
      <Card.Header>
        <h5>{t("ai.voiceClone.title")}</h5>
      </Card.Header>
      <Card.Body>
        <p>{t("ai.voiceClone.description")}</p>

        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>{t("ai.voiceClone.selectVoice")}</Form.Label>
            <Form.Control
              type="file"
              onChange={handleFileChange}
              accept="audio/*"
              disabled={loading}
            />
            <Form.Text className="text-muted">
              {t("ai.voiceClone.voiceHint")}
            </Form.Text>
          </Form.Group>

          {originalAudio && (
            <div className="mb-3">
              <p>{t("ai.voiceClone.originalVoice")}</p>
              <audio controls src={originalAudio} className="w-100" />
            </div>
          )}

          <Form.Group className="mb-3">
            <Form.Label>{t("ai.voiceClone.enterText")}</Form.Label>
            <Form.Control
              as="textarea"
              rows={3}
              value={text}
              onChange={handleTextChange}
              placeholder={t("ai.voiceClone.textPlaceholder")}
              disabled={loading}
            />
          </Form.Group>

          <button
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            type="submit"
            disabled={!file || !text.trim() || loading}
          >
            {loading && (
              <>
                <Spinner
                  as="span"
                  animation="border"
                  size="sm"
                  role="status"
                  aria-hidden="true"
                  className="me-2"
                />
                {t("ai.voiceClone.processing")}
              </>
            )}
            {!loading && t("ai.voiceClone.startButton")}
          </button>
        </Form>

        {error && (
          <Alert variant="danger" className="mt-3">
            {error}
          </Alert>
        )}

        {success && resultAudio && (
          <div className="mt-4">
            <h6>{t("ai.voiceClone.result")}</h6>
            <p>{t("ai.voiceClone.clonedVoice")}</p>
            <audio controls src={resultAudio} className="w-100" />
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default VoiceCloneComponent;
