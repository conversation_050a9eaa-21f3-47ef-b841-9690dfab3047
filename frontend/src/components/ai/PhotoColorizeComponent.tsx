import React, { useState } from "react";
import { Card, Form, Image, Spinner, Alert } from "react-bootstrap";
import axios from "axios";
import { useTranslation } from "react-i18next";

interface PhotoColorizeProps {
  onComplete?: (originalUrl: string, resultUrl: string) => void;
  className?: string;
}

const PhotoColorizeComponent: React.FC<PhotoColorizeProps> = ({
  onComplete,
  className,
}) => {
  const { t } = useTranslation();
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [result, setResult] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);

      // 创建预览
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setPreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(selectedFile);

      // 重置状态
      setResult(null);
      setError(null);
      setSuccess(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      setError(t("ai.photoColorize.noFileError"));
      return;
    }

    setLoading(true);
    setError(null);

    const formData = new FormData();
    formData.append("file", file);

    try {
      const response = await axios.post("/api/ai/photo-colorize", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response.data.success) {
        setResult(response.data.result_url);
        setSuccess(true);

        if (onComplete) {
          onComplete(response.data.original_url, response.data.result_url);
        }
      } else {
        setError(response.data.message || t("ai.photoColorize.genericError"));
      }
    } catch (err) {
      console.error("照片上色失败:", err);
      setError(
        err instanceof Error ? err.message : t("ai.photoColorize.genericError"),
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className={className}>
      <Card.Header>
        <h5>{t("ai.photoColorize.title")}</h5>
      </Card.Header>
      <Card.Body>
        <p>{t("ai.photoColorize.description")}</p>

        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>{t("ai.photoColorize.selectPhoto")}</Form.Label>
            <Form.Control
              type="file"
              onChange={handleFileChange}
              accept="image/*"
              disabled={loading}
            />
          </Form.Group>

          {preview && (
            <div className="mb-3">
              <p>{t("ai.photoColorize.preview")}</p>
              <Image
                src={preview}
                alt={t("ai.photoColorize.previewAlt")}
                thumbnail
                style={{ maxHeight: "200px" }}
              />
            </div>
          )}

          <button
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            type="submit"
            disabled={!file || loading}
          >
            {loading && (
              <>
                <Spinner
                  as="span"
                  animation="border"
                  size="sm"
                  role="status"
                  aria-hidden="true"
                  className="me-2"
                />
                {t("ai.photoColorize.processing")}
              </>
            )}
            {!loading && t("ai.photoColorize.startButton")}
          </button>
        </Form>

        {error && (
          <Alert variant="danger" className="mt-3">
            {error}
          </Alert>
        )}

        {success && result && (
          <div className="mt-4">
            <h6>{t("ai.photoColorize.result")}</h6>
            <div className="d-flex flex-wrap">
              <div className="me-3 mb-3">
                <p>{t("ai.photoColorize.original")}</p>
                <Image
                  src={preview || ""}
                  alt={t("ai.photoColorize.originalAlt")}
                  thumbnail
                  style={{ maxHeight: "200px" }}
                />
              </div>
              <div>
                <p>{t("ai.photoColorize.colorized")}</p>
                <Image
                  src={result}
                  alt={t("ai.photoColorize.colorizedAlt")}
                  thumbnail
                  style={{ maxHeight: "200px" }}
                />
              </div>
            </div>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default PhotoColorizeComponent;
