import React, { useState } from "react";
import axios from "axios";

interface PhotoEnhanceProps {
  onComplete?: (originalUrl: string, resultUrl: string) => void;
  className?: string;
}

const PhotoEnhanceComponent: React.FC<PhotoEnhanceProps> = ({
  onComplete,
  className,
}) => {
  // TODO: 实现国际化
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const [result, setResult] = useState<{
    original_url: string;
    enhanced_url: string;
    options_used?: any;
  } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  
  // 增强选项
  const [scale, setScale] = useState<number>(4);
  const [faceEnhance, setFaceEnhance] = useState<boolean>(true);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);

      // 创建预览
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setPreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(selectedFile);

      // 重置状态
      setResult(null);
      setError(null);
      setSuccess(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      setError("请选择一个图片文件");
      return;
    }

    setLoading(true);
    setError(null);

    const formData = new FormData();
    formData.append("file", file);
    formData.append("scale", scale.toString());
    formData.append("face_enhance", faceEnhance.toString());

    try {
      const response = await axios.post("/api/ai/photo-enhance", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      if (response.data.success) {
        setResult({
          original_url: response.data.original_url,
          enhanced_url: response.data.result_url,
          options_used: response.data.options_used,
        });
        setSuccess(true);

        if (onComplete) {
          onComplete(response.data.original_url, response.data.result_url);
        }
      } else {
        setError(response.data.message || "照片增强失败");
      }
    } catch (err) {
      console.error("照片增强失败:", err);
      let errorMessage = "照片增强失败";
      if (
        axios.isAxiosError(err) &&
        err.response &&
        err.response.data &&
        err.response.data.detail
      ) {
        errorMessage = err.response.data.detail;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`bg-gray-800 rounded-lg border border-gray-700 ${className}`}>
      <div className="px-4 py-3 border-b border-gray-700">
        <h5 className="text-lg font-semibold text-white m-0">🔍 AI照片增强</h5>
      </div>
      <div className="p-4">
        <p className="text-gray-400 mb-4">
          使用AI技术增强照片分辨率和质量，让模糊的老照片重新清晰起来。
        </p>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-300">
              选择照片
            </label>
            <input
              type="file"
              onChange={handleFileChange}
              accept="image/*"
              disabled={loading}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            />
          </div>

          {/* 增强选项 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-300">
                放大倍数: {scale}x
              </label>
              <input
                type="range"
                min={2}
                max={8}
                value={scale}
                onChange={(e) => setScale(parseInt(e.target.value))}
                disabled={loading}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
              />
              <p className="text-sm text-gray-400">
                较高的倍数可能需要更长处理时间
              </p>
            </div>
            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={faceEnhance}
                  onChange={(e) => setFaceEnhance(e.target.checked)}
                  disabled={loading}
                  className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed"
                />
                <span className="text-sm font-medium text-gray-300">面部增强</span>
              </label>
              <p className="text-sm text-gray-400">
                专门优化人脸区域的清晰度
              </p>
            </div>
          </div>

          {preview && (
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-300">预览</p>
              <div className="flex justify-center">
                <img
                  src={preview}
                  alt="预览"
                  className="max-h-48 rounded-lg border border-gray-600"
                />
              </div>
            </div>
          )}

          <button
            className="w-full sm:w-auto px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            type="submit"
            disabled={!file || loading}
          >
            {loading && (
              <div className="inline-block w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
            )}
            {loading ? "增强中..." : "开始增强"}
          </button>
        </form>

        {error && (
          <div className="mt-4 p-4 bg-red-900/50 border border-red-700 rounded-lg">
            <p className="text-red-200 m-0">{error}</p>
          </div>
        )}

        {success && result && (
          <div className="mt-6">
            <h6 className="text-lg font-semibold text-white mb-4">增强结果</h6>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-300 mb-2">原始照片</p>
                <img
                  src={result.original_url}
                  alt="原始照片"
                  className="w-full h-48 object-contain rounded-lg border border-gray-600"
                />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-300 mb-2">
                  增强后照片 ({result.options_used?.scale || scale}x 增强)
                </p>
                <img
                  src={result.enhanced_url}
                  alt="增强后照片"
                  className="w-full h-48 object-contain rounded-lg border border-gray-600"
                />
                <div className="mt-3">
                  <a
                    href={result.enhanced_url}
                    download="enhanced_photo.png"
                    className="w-full inline-block px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-gray-800 transition-colors duration-200 text-center no-underline"
                  >
                    下载增强照片
                  </a>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PhotoEnhanceComponent;