import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { TransitionType, TransitionDirection } from "./TransitionTypes";

interface SceneTransitionProps {
  children: React.ReactNode;
  type?: TransitionType;
  direction?: TransitionDirection;
  duration?: number;
  isVisible?: boolean;
  onExitComplete?: () => void;
}

const SceneTransition: React.FC<SceneTransitionProps> = ({
  children,
  type = TransitionType.FADE,
  direction = TransitionDirection.RIGHT,
  duration = 0.5,
  isVisible = true,
  onExitComplete,
}) => {
  const [key, setKey] = useState<number>(0);

  // 当子组件变化时更新key，触发动画
  useEffect(() => {
    setKey((prev) => prev + 1);
  }, [children]);

  // 根据转场类型和方向获取动画变体
  const getVariants = () => {
    switch (type) {
      case TransitionType.SLIDE:
        return {
          initial: {
            x:
              direction === TransitionDirection.LEFT
                ? "100%"
                : direction === TransitionDirection.RIGHT
                  ? "-100%"
                  : 0,
            y:
              direction === TransitionDirection.UP
                ? "100%"
                : direction === TransitionDirection.DOWN
                  ? "-100%"
                  : 0,
          },
          animate: { x: 0, y: 0 },
          exit: {
            x:
              direction === TransitionDirection.LEFT
                ? "-100%"
                : direction === TransitionDirection.RIGHT
                  ? "100%"
                  : 0,
            y:
              direction === TransitionDirection.UP
                ? "-100%"
                : direction === TransitionDirection.DOWN
                  ? "100%"
                  : 0,
          },
        };

      case TransitionType.ZOOM:
        return {
          initial: {
            scale: direction === TransitionDirection.IN ? 0.8 : 1.2,
            opacity: 0,
          },
          animate: { scale: 1, opacity: 1 },
          exit: {
            scale: direction === TransitionDirection.IN ? 1.2 : 0.8,
            opacity: 0,
          },
        };

      case TransitionType.FLIP:
        return {
          initial: {
            rotateY:
              direction === TransitionDirection.LEFT
                ? 90
                : direction === TransitionDirection.RIGHT
                  ? -90
                  : 0,
            rotateX:
              direction === TransitionDirection.UP
                ? 90
                : direction === TransitionDirection.DOWN
                  ? -90
                  : 0,
            opacity: 0,
          },
          animate: { rotateX: 0, rotateY: 0, opacity: 1 },
          exit: {
            rotateY:
              direction === TransitionDirection.LEFT
                ? -90
                : direction === TransitionDirection.RIGHT
                  ? 90
                  : 0,
            rotateX:
              direction === TransitionDirection.UP
                ? -90
                : direction === TransitionDirection.DOWN
                  ? 90
                  : 0,
            opacity: 0,
          },
        };

      case TransitionType.BLUR:
        return {
          initial: { filter: "blur(12px)", opacity: 0 },
          animate: { filter: "blur(0px)", opacity: 1 },
          exit: { filter: "blur(12px)", opacity: 0 },
        };

      case TransitionType.NONE:
        return {
          initial: {},
          animate: {},
          exit: {},
        };

      case TransitionType.FADE:
      default:
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 },
        };
    }
  };

  const variants = getVariants();

  return (
    <div className="scene-transition-container">
      <AnimatePresence mode="wait" onExitComplete={onExitComplete}>
        {isVisible && (
          <motion.div
            key={key}
            className="scene-transition-content"
            initial="initial"
            animate="animate"
            exit="exit"
            variants={variants}
            transition={{ duration }}
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SceneTransition;
