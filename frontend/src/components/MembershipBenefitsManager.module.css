/* Memorial 会员权益管理系统样式 */

.membershipManager {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 加载和错误状态 */
.loading, .error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: var(--text-secondary, #666);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-light, #e0e0e0);
  border-top: 4px solid var(--primary, #007AFF);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 当前计划概览 */
.currentPlan {
  background: linear-gradient(135deg, var(--primary, #007AFF), var(--primary-dark, #0056cc));
  color: white;
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 122, 255, 0.2);
}

.planHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.planInfo h2 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
}

.status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status.active {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.status.trial {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status.expired, .status.cancelled {
  background: rgba(220, 53, 69, 0.2);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.planDates {
  text-align: right;
  opacity: 0.9;
}

.planDates p {
  margin: 4px 0;
  font-size: 14px;
}

.autoRenew {
  font-weight: 500;
}

/* 使用情况仪表板 */
.usageDashboard {
  margin-top: 24px;
}

.usageDashboard h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  opacity: 0.95;
}

.usageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.usageCard {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 20px;
  transition: all 0.3s ease;
}

.usageCard:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.usageCard.warning {
  border-color: rgba(255, 193, 7, 0.5);
  background: rgba(255, 193, 7, 0.1);
}

.usageCard.danger {
  border-color: rgba(220, 53, 69, 0.5);
  background: rgba(220, 53, 69, 0.1);
}

.usageHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.usageHeader h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.usageText {
  font-size: 14px;
  font-weight: 500;
  opacity: 0.9;
}

.progressBar {
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
  margin-top: 8px;
}

.progress {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.usageCard.warning .progress {
  background: #ffc107;
}

.usageCard.danger .progress {
  background: #dc3545;
}

.unlimited {
  text-align: center;
  padding: 8px;
  background: rgba(40, 167, 69, 0.2);
  border-radius: 8px;
  color: #28a745;
  font-size: 12px;
  font-weight: 600;
  margin-top: 8px;
}

.warningText, .dangerText {
  font-size: 12px;
  text-align: center;
  margin-top: 8px;
  font-weight: 500;
}

.warningText {
  color: #ffc107;
}

.dangerText {
  color: #dc3545;
}

/* 功能权限区域 */
.features {
  margin-bottom: 32px;
}

.features h3 {
  margin: 0 0 20px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.featureGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.featureCard {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.featureCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.featureCard.unavailable {
  background: var(--bg-secondary, #f8f9fa);
  border: 2px dashed var(--border-light, #e8e8e8);
  opacity: 0.7;
}

.featureHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.featureHeader h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.featureBadges {
  display: flex;
  gap: 6px;
}

.newBadge, .premiumBadge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.newBadge {
  background: #e8f5e8;
  color: #28a745;
}

.premiumBadge {
  background: #fff3cd;
  color: #856404;
}

.featureDescription {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: var(--text-secondary, #666);
  line-height: 1.5;
}

.featureUsage {
  margin: 16px 0;
}

.featureUsage span {
  font-size: 12px;
  color: var(--text-tertiary, #888);
}

.featureProgress {
  height: 4px;
  background: var(--bg-tertiary, #f0f0f0);
  border-radius: 2px;
  margin-top: 6px;
  overflow: hidden;
}

.featureProgress .progress {
  height: 100%;
  background: var(--primary, #007AFF);
  border-radius: 2px;
}

.upgradeButton {
  width: 100%;
  padding: 10px 16px;
  background: var(--primary, #007AFF);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upgradeButton:hover {
  background: var(--primary-dark, #0056cc);
  transform: translateY(-1px);
}

.upgradeButton:active {
  transform: translateY(0);
}

/* 升级建议区域 */
.recommendations {
  margin-bottom: 32px;
}

.recommendations h3 {
  margin: 0 0 20px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.recommendationList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendationCard {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-left: 4px solid var(--border-light, #e8e8e8);
}

.recommendationCard.high {
  border-left-color: #dc3545;
  background: linear-gradient(135deg, #fff, #fff5f5);
}

.recommendationCard.medium {
  border-left-color: #ffc107;
  background: linear-gradient(135deg, #fff, #fffbf0);
}

.recommendationCard.low {
  border-left-color: #28a745;
  background: linear-gradient(135deg, #fff, #f8fff8);
}

.recommendationHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.recommendationHeader h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.urgencyBadge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.recommendationCard.high .urgencyBadge {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.recommendationCard.medium .urgencyBadge {
  background: rgba(255, 193, 7, 0.1);
  color: #856404;
}

.recommendationCard.low .urgencyBadge {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.benefits {
  margin-bottom: 20px;
}

.benefits p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: var(--text-secondary, #666);
}

.benefits ul {
  margin: 0;
  padding-left: 20px;
  list-style-type: none;
}

.benefits li {
  position: relative;
  padding: 4px 0;
  font-size: 14px;
  color: var(--text-primary, #333);
}

.benefits li::before {
  content: '✓';
  position: absolute;
  left: -20px;
  color: var(--success, #28a745);
  font-weight: bold;
}

.recommendationFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.discount {
  padding: 6px 12px;
  background: #e8f5e8;
  color: #28a745;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

/* 可升级计划区域 */
.availablePlans {
  margin-bottom: 32px;
}

.availablePlans h3 {
  margin: 0 0 20px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.planGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.planCard {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.planCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.planCardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light, #e8e8e8);
}

.planCardHeader h4 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.tier {
  padding: 4px 8px;
  background: var(--primary-light, #e3f2fd);
  color: var(--primary, #007AFF);
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
}

.planFeatures {
  margin-bottom: 20px;
}

.featureItem {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  font-size: 14px;
  border-bottom: 1px solid var(--border-light, #f0f0f0);
}

.featureItem:last-child {
  border-bottom: none;
}

.featureItem span:first-child {
  color: var(--text-secondary, #666);
}

.featureItem span:last-child {
  font-weight: 500;
  color: var(--text-primary, #333);
}

/* 弹窗样式 */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal {
  background: white;
  border-radius: 16px;
  padding: 32px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

.modalHeader h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.modalContent {
  margin-bottom: 24px;
}

.modalContent p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: var(--text-secondary, #666);
  line-height: 1.5;
}

.modalActions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.cancelButton, .confirmButton {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton {
  background: var(--bg-secondary, #f8f9fa);
  color: var(--text-secondary, #666);
  border: 1px solid var(--border-light, #e8e8e8);
}

.cancelButton:hover {
  background: var(--bg-hover, #e8e8e8);
}

.confirmButton {
  background: var(--primary, #007AFF);
  color: white;
  border: none;
}

.confirmButton:hover {
  background: var(--primary-dark, #0056cc);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .membershipManager {
    padding: 16px;
  }

  .currentPlan {
    padding: 20px;
  }

  .planHeader {
    flex-direction: column;
    gap: 16px;
  }

  .planDates {
    text-align: left;
  }

  .usageGrid {
    grid-template-columns: 1fr;
  }

  .featureGrid {
    grid-template-columns: 1fr;
  }

  .planGrid {
    grid-template-columns: 1fr;
  }

  .recommendationHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .recommendationFooter {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .modal {
    margin: 20px;
    padding: 24px;
  }

  .modalActions {
    flex-direction: column;
  }
}