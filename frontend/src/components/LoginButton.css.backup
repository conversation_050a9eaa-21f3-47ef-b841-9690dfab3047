.login-container {
  position: relative;
}

.login-button {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.login-button:hover {
  background-color: var(--color-primary-hover);
}

/* .modal-overlay 类已移至 tailwind-components.css 以避免重复定义 */

.login-modal {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  width: 100%;
  max-width: 400px;
  position: relative;
  box-shadow: 0 4px 16px rgba(var(--color-gray-900-rgb), 0.2);
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--color-gray-600);
}

/* 表单样式已移至 tailwind-components.css，使用统一的表单组件 */

.submit-button {
  width: 100%;
  padding: 12px;
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.submit-button:hover {
  background-color: var(--color-primary-hover);
}

.submit-button:disabled {
  background-color: var(--color-primary-light);
  cursor: not-allowed;
}

.error-message {
  background-color: var(--color-error-light);
  color: var(--color-error);
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 16px;
}

.user-menu {
  position: relative;
  display: inline-block;
}

.username {
  background-color: var(--color-primary);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(var(--color-gray-900-rgb), 0.1);
  min-width: 150px;
  display: none;
  z-index: var(--z-modal);
}

.user-menu:hover .dropdown-menu {
  display: block;
}

/* .dropdown-item 类已移至 tailwind-components.css 以避免重复定义 */
