import React, { useState } from "react";
import { useScene, useBeforeRender, Model } from "react-babylonjs";
import { Vector3 } from "@babylonjs/core";
import "@babylonjs/loaders";

interface AdaptiveLODProps {
  modelPath: string;
  levels?: {
    distance: number;
    path: string;
  }[];
  position?: [number, number, number];
  rotation?: [number, number, number];
  scale?: number | [number, number, number];
  visible?: boolean;
  receiveShadows?: boolean;
}

/**
 * 自适应LOD（细节级别）组件
 * 根据相机距离自动切换不同细节级别的模型
 */
const AdaptiveLOD: React.FC<AdaptiveLODProps> = ({
  modelPath,
  levels = [],
  position = [0, 0, 0],
  rotation = [0, 0, 0],
  scale = 1,
  visible = true,
  receiveShadows = true,
}) => {
  const scene = useScene();
  const [currentLevel, setCurrentLevel] = useState<number>(0);

  // 确保有默认级别
  const allLevels = [{ distance: 0, path: modelPath }, ...levels].sort(
    (a, b) => a.distance - b.distance,
  );

  // 更新LOD可见性
  useBeforeRender(() => {
    if (!scene || !scene.activeCamera) return;

    // 获取相机
    const camera = scene.activeCamera;

    // 计算相机到模型位置的距离
    const modelPosition = new Vector3(position[0], position[1], position[2]);
    const distance = camera.globalPosition.subtract(modelPosition).length();

    // 手动确定当前LOD级别
    let newLevelIndex = 0;
    for (let i = 1; i < allLevels.length; i++) {
      if (distance >= allLevels[i].distance) {
        newLevelIndex = i;
      } else {
        break;
      }
    }

    if (newLevelIndex !== currentLevel) {
      setCurrentLevel(newLevelIndex);
    }
  });

  // 获取当前应该显示的模型路径
  const currentModelPath = allLevels[currentLevel]?.path || modelPath;

  // 解析路径获取rootUrl和文件名
  const lastSlashIndex = currentModelPath.lastIndexOf("/");
  const rootUrl = currentModelPath.substring(0, lastSlashIndex + 1);
  const fileName = currentModelPath.substring(lastSlashIndex + 1);

  // 计算缩放值
  const scaleVector = Array.isArray(scale)
    ? new Vector3(scale[0], scale[1], scale[2])
    : new Vector3(scale, scale, scale);

  return (
    <transformNode
      name="LODRootNode"
      position={new Vector3(position[0], position[1], position[2])}
      rotation={new Vector3(rotation[0], rotation[1], rotation[2])}
      scaling={scaleVector}
      setEnabled={visible}
    >
      {/* 使用react-babylonjs的Model组件加载当前LOD级别的模型 */}
      <Model
        name={`model-lod-${currentLevel}`}
        rootUrl={rootUrl}
        sceneFilename={fileName}
        receiveShadows={receiveShadows}
      />
    </transformNode>
  );
};

export default AdaptiveLOD;
