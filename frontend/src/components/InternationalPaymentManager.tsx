import React, { useState, useEffect, useRef } from 'react';
import { StripeCardElement } from '@stripe/stripe-js';
import {
  InternationalPaymentService,
  PaymentMethod,
  PaymentRequest,
  PaymentResult,
  createInternationalPaymentService,
} from '../services/internationalPaymentService';
import styles from './InternationalPaymentManager.module.css';

interface InternationalPaymentManagerProps {
  amount: number;
  currency: string;
  description: string;
  orderId: string;
  userId: string;
  productId?: string;
  subscriptionId?: string;
  onPaymentSuccess?: (result: PaymentResult) => void;
  onPaymentFailure?: (result: PaymentResult) => void;
  onCancel?: () => void;
}

export const InternationalPaymentManager: React.FC<InternationalPaymentManagerProps> = ({
  amount,
  currency,
  description,
  orderId,
  userId,
  productId,
  subscriptionId,
  onPaymentSuccess,
  onPaymentFailure,
  onCancel,
}) => {
  const [paymentService, setPaymentService] = useState<InternationalPaymentService | null>(null);
  const [availableMethods, setAvailableMethods] = useState<PaymentMethod[]>([]);
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userRegion, setUserRegion] = useState<string>('US');
  const [convertedAmount, setConvertedAmount] = useState<number>(amount);
  const [displayCurrency, setDisplayCurrency] = useState<string>(currency);

  const stripeCardRef = useRef<StripeCardElement | null>(null);
  const paypalContainerRef = useRef<HTMLDivElement>(null);
  const alipayQRRef = useRef<HTMLDivElement>(null);
  const wechatQRRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    initializePaymentService();
  }, []);

  useEffect(() => {
    if (paymentService) {
      const region = paymentService.getUserRegion();
      setUserRegion(region);
      
      const methods = paymentService.getSupportedPaymentMethods(currency, region);
      setAvailableMethods(methods);
      
      const recommended = paymentService.getRecommendedPaymentMethod(currency, region);
      if (recommended) {
        setSelectedMethod(recommended);
      }
    }
  }, [paymentService, currency]);

  useEffect(() => {
    if (selectedMethod && paymentService) {
      handleCurrencyConversion();
    }
  }, [selectedMethod, amount, currency]);

  /// 初始化支付服务
  const initializePaymentService = async () => {
    try {
      const config = {
        stripe: {
          publicKey: process.env.REACT_APP_STRIPE_PUBLIC_KEY || '',
          secretKey: process.env.REACT_APP_STRIPE_SECRET_KEY || '',
          webhookSecret: process.env.REACT_APP_STRIPE_WEBHOOK_SECRET || '',
          apiVersion: '2023-10-16',
        },
        paypal: {
          clientId: process.env.REACT_APP_PAYPAL_CLIENT_ID || '',
          clientSecret: process.env.REACT_APP_PAYPAL_CLIENT_SECRET || '',
          sandbox: process.env.NODE_ENV !== 'production',
          webhookId: process.env.REACT_APP_PAYPAL_WEBHOOK_ID || '',
        },
        supportedCurrencies: ['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'HKD', 'TWD', 'CAD', 'AUD'],
        defaultCurrency: 'USD',
        exchangeRateProvider: 'fixer.io',
      };

      const service = createInternationalPaymentService(config);
      setPaymentService(service);
    } catch (error) {
      console.error('Failed to initialize payment service:', error);
      setError('支付系统初始化失败');
    }
  };

  /// 处理货币转换
  const handleCurrencyConversion = async () => {
    if (!paymentService || !selectedMethod) return;

    try {
      // 根据支付方式推荐货币
      const preferredCurrency = getPreferredCurrency(selectedMethod, userRegion);
      
      if (preferredCurrency !== currency) {
        const converted = await paymentService.convertCurrency(amount, currency, preferredCurrency);
        setConvertedAmount(converted);
        setDisplayCurrency(preferredCurrency);
      } else {
        setConvertedAmount(amount);
        setDisplayCurrency(currency);
      }
    } catch (error) {
      console.error('Currency conversion failed:', error);
      // 使用原始金额
      setConvertedAmount(amount);
      setDisplayCurrency(currency);
    }
  };

  /// 获取推荐货币
  const getPreferredCurrency = (method: PaymentMethod, region: string): string => {
    if (method.type === 'alipay' || method.type === 'wechat') {
      return 'CNY';
    }
    
    if (method.type === 'paypal') {
      switch (region) {
        case 'EU': return 'EUR';
        case 'UK': return 'GBP';
        case 'JP': return 'JPY';
        case 'CA': return 'CAD';
        case 'AU': return 'AUD';
        default: return 'USD';
      }
    }

    return currency;
  };

  /// 选择支付方式
  const handleMethodSelect = (method: PaymentMethod) => {
    setSelectedMethod(method);
    setError(null);
    
    // 为特定支付方式初始化UI元素
    setTimeout(() => {
      if (method.type === 'stripe') {
        initializeStripeCard();
      } else if (method.type === 'paypal') {
        initializePayPalButtons();
      }
    }, 100);
  };

  /// 初始化Stripe卡片元素
  const initializeStripeCard = () => {
    if (!paymentService) return;

    const cardElement = paymentService.createStripeCardElement('stripe-card-element');
    stripeCardRef.current = cardElement;
  };

  /// 初始化PayPal按钮
  const initializePayPalButtons = () => {
    if (paypalContainerRef.current) {
      paypalContainerRef.current.innerHTML = '';
      paypalContainerRef.current.id = 'paypal-button-container';
    }
  };

  /// 处理支付
  const handlePayment = async () => {
    if (!paymentService || !selectedMethod) {
      setError('请选择支付方式');
      return;
    }

    setIsProcessing(true);
    setError(null);

    const paymentRequest: PaymentRequest = {
      amount: convertedAmount,
      currency: displayCurrency,
      description,
      orderId,
      userId,
      productId,
      subscriptionId,
      metadata: {
        originalAmount: amount,
        originalCurrency: currency,
        userRegion,
        paymentMethod: selectedMethod.type,
      },
    };

    try {
      let result: PaymentResult;

      switch (selectedMethod.type) {
        case 'stripe':
          result = await handleStripePayment(paymentRequest);
          break;
        case 'paypal':
          result = await paymentService.processPayPalPayment(paymentRequest);
          break;
        case 'alipay':
          result = await paymentService.processAlipayPayment(paymentRequest);
          break;
        case 'wechat':
          result = await paymentService.processWeChatPayment(paymentRequest);
          break;
        default:
          throw new Error('不支持的支付方式');
      }

      if (result.success) {
        onPaymentSuccess?.(result);
      } else {
        setError(result.message || '支付失败');
        onPaymentFailure?.(result);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '支付处理失败';
      setError(errorMessage);
      onPaymentFailure?.({
        success: false,
        status: 'failed',
        message: errorMessage,
      });
    } finally {
      setIsProcessing(false);
    }
  };

  /// 处理Stripe支付
  const handleStripePayment = async (request: PaymentRequest): Promise<PaymentResult> => {
    if (!stripeCardRef.current || !paymentService) {
      throw new Error('Stripe未正确初始化');
    }

    const billingDetails = {
      name: 'Memorial User', // 实际应用中应该从用户信息获取
      email: '<EMAIL>',
    };

    return await paymentService.processStripePayment(
      request,
      stripeCardRef.current,
      billingDetails
    );
  };

  /// 格式化货币显示
  const formatCurrency = (amount: number, currency: string): string => {
    if (!paymentService) {
      return `${currency} ${amount.toFixed(2)}`;
    }
    return paymentService.formatCurrency(amount, currency);
  };

  /// 获取支付方式图标
  const getMethodIcon = (method: PaymentMethod): string => {
    const icons = {
      'stripe': '💳',
      'paypal': '🅿️',
      'alipay': '💙',
      'wechat': '💚',
    };
    return icons[method.type] || '💳';
  };

  if (!paymentService) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>正在初始化支付系统...</p>
      </div>
    );
  }

  return (
    <div className={styles.paymentManager}>
      <div className={styles.header}>
        <h2>国际支付</h2>
        <p>安全便捷的多币种支付体验</p>
      </div>

      {/* 支付金额显示 */}
      <div className={styles.amountDisplay}>
        <div className={styles.originalAmount}>
          <span className={styles.label}>订单金额:</span>
          <span className={styles.amount}>{formatCurrency(amount, currency)}</span>
        </div>
        
        {displayCurrency !== currency && (
          <div className={styles.convertedAmount}>
            <span className={styles.label}>支付金额:</span>
            <span className={styles.amount}>{formatCurrency(convertedAmount, displayCurrency)}</span>
            <span className={styles.conversionNote}>
              (按当前汇率转换)
            </span>
          </div>
        )}
      </div>

      {/* 支付方式选择 */}
      <div className={styles.methodSelection}>
        <h3>选择支付方式</h3>
        <div className={styles.methodGrid}>
          {availableMethods.map((method) => (
            <div
              key={method.id}
              className={`${styles.methodCard} ${
                selectedMethod?.id === method.id ? styles.selected : ''
              } ${!method.isActive ? styles.disabled : ''}`}
              onClick={() => method.isActive && handleMethodSelect(method)}
            >
              <div className={styles.methodIcon}>
                {getMethodIcon(method)}
              </div>
              <div className={styles.methodInfo}>
                <h4>{method.name}</h4>
                <div className={styles.methodMeta}>
                  <span className={styles.regions}>
                    {method.regions.slice(0, 3).join(', ')}
                  </span>
                  {!method.isActive && (
                    <span className={styles.unavailable}>不可用</span>
                  )}
                </div>
              </div>
              {selectedMethod?.id === method.id && (
                <div className={styles.selectedIndicator}>✓</div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 支付表单 */}
      {selectedMethod && (
        <div className={styles.paymentForm}>
          <h3>支付信息</h3>
          
          {selectedMethod.type === 'stripe' && (
            <div className={styles.stripeForm}>
              <div className={styles.cardElementContainer}>
                <label htmlFor="stripe-card-element">
                  信用卡/借记卡信息
                </label>
                <div id="stripe-card-element" className={styles.cardElement}>
                  {/* Stripe卡片元素将在这里渲染 */}
                </div>
              </div>
            </div>
          )}

          {selectedMethod.type === 'paypal' && (
            <div className={styles.paypalForm}>
              <p>点击下方PayPal按钮完成支付</p>
              <div ref={paypalContainerRef} id="paypal-button-container">
                {/* PayPal按钮将在这里渲染 */}
              </div>
            </div>
          )}

          {selectedMethod.type === 'alipay' && (
            <div className={styles.alipayForm}>
              <div className={styles.qrCodeContainer}>
                <div ref={alipayQRRef} className={styles.qrCode}>
                  <div className={styles.qrPlaceholder}>
                    <div className={styles.qrIcon}>📱</div>
                    <p>支付宝二维码将在此显示</p>
                  </div>
                </div>
                <p>请使用支付宝扫码支付</p>
              </div>
            </div>
          )}

          {selectedMethod.type === 'wechat' && (
            <div className={styles.wechatForm}>
              <div className={styles.qrCodeContainer}>
                <div ref={wechatQRRef} className={styles.qrCode}>
                  <div className={styles.qrPlaceholder}>
                    <div className={styles.qrIcon}>📱</div>
                    <p>微信支付二维码将在此显示</p>
                  </div>
                </div>
                <p>请使用微信扫码支付</p>
              </div>
            </div>
          )}

          {/* 错误信息 */}
          {error && (
            <div className={styles.error}>
              <div className={styles.errorIcon}>⚠️</div>
              <span>{error}</span>
            </div>
          )}

          {/* 支付按钮 */}
          <div className={styles.paymentActions}>
            <button
              className={styles.cancelButton}
              onClick={onCancel}
              disabled={isProcessing}
            >
              取消
            </button>
            
            {selectedMethod.type !== 'paypal' && (
              <button
                className={styles.payButton}
                onClick={handlePayment}
                disabled={isProcessing || !selectedMethod.isActive}
              >
                {isProcessing ? (
                  <>
                    <div className={styles.spinner}></div>
                    <span>处理中...</span>
                  </>
                ) : (
                  <>
                    <span>确认支付</span>
                    <span className={styles.paymentAmount}>
                      {formatCurrency(convertedAmount, displayCurrency)}
                    </span>
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      )}

      {/* 安全提示 */}
      <div className={styles.securityNotice}>
        <div className={styles.securityIcon}>🔒</div>
        <div className={styles.securityText}>
          <h4>安全支付保障</h4>
          <ul>
            <li>采用SSL加密传输，保护您的支付信息</li>
            <li>支持多种国际主流支付方式</li>
            <li>实时汇率转换，透明无隐藏费用</li>
            <li>24/7客服支持，解决支付问题</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default InternationalPaymentManager;