import React, { useState } from "react";
import { Engine, Scene } from "react-babylonjs";
import { Vector3, Color3, Color4, AbstractMesh } from "@babylonjs/core";
import "@babylonjs/loaders";

interface ModelViewerProps {
  modelPath: string;
  modelName?: string;
  modelExtension?: string;
  scale?: number;
  position?: Vector3;
  rotation?: Vector3;
  onModelLoaded?: (meshes: AbstractMesh[]) => void;
}

// 模型查看器组件
const ModelViewer: React.FC<ModelViewerProps> = ({
  modelPath,
  modelName = "",
  modelExtension = ".glb",
  scale = 1,
  position = Vector3.Zero(),
  rotation = Vector3.Zero(),
  onModelLoaded,
}) => {
  const [modelReady, setModelReady] = useState(false);

  // 处理模型加载完成事件
  const onModelLoaderReady = (meshes: AbstractMesh[]) => {
    setModelReady(true);

    // 应用缩放
    meshes.forEach((mesh) => {
      mesh.scaling = new Vector3(scale, scale, scale);
    });

    // 调用外部回调
    if (onModelLoaded) {
      onModelLoaded(meshes);
    }
  };

  // 确定模型文件路径
  const rootUrl = modelPath.substring(0, modelPath.lastIndexOf("/") + 1);
  const fileName =
    modelName || modelPath.substring(modelPath.lastIndexOf("/") + 1);
  const fileNameWithExt = fileName.includes(".")
    ? fileName
    : `${fileName}${modelExtension}`;

  return (
    <div style={{ width: "100%", height: "100vh" }}>
      <Engine antialias adaptToDeviceRatio canvasId="modelViewer">
        <Scene clearColor={new Color4(0.2, 0.2, 0.3, 1)}>
          {/* 相机 */}
          <arcRotateCamera
            name="camera"
            target={Vector3.Zero()}
            alpha={-Math.PI / 2}
            beta={Math.PI / 3}
            radius={10}
            minZ={0.001}
            wheelPrecision={50}
            lowerRadiusLimit={3}
            upperRadiusLimit={20}
          />

          {/* 光源 */}
          <hemisphericLight
            key="hemiLight"
            name="hemiLight"
            intensity={0.7}
            direction={new Vector3(0, 1, 0)}
          />
          <pointLight
            key="pointLight"
            name="pointLight"
            position={new Vector3(5, 10, 5)}
            intensity={0.8}
          />

          {/* 模型加载 */}
          {/* @ts-expect-error react-babylonjs Model component might have typing issues or need specific import */}
          <Model
            rootUrl={rootUrl}
            sceneFilename={fileNameWithExt}
            position={position}
            rotation={rotation}
            onModelLoaded={onModelLoaderReady}
            scaling={new Vector3(scale, scale, scale)}
          />

          {/* 地面 */}
          <ground
            name="ground"
            width={20}
            height={20}
            subdivisions={2}
            receiveShadows={true}
          >
            <standardMaterial
              name="groundMaterial"
              diffuseColor={Color3.FromHexString("var(--color-gray-400)")}
              specularColor={Color3.Black()}
            />
          </ground>
        </Scene>
      </Engine>

      {/* 加载状态指示器 */}
      {!modelReady && (
        <div
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            color: "white",
            background: "rgba(var(--color-gray-900-rgb), 0.5)",
            padding: "10px 20px",
            borderRadius: "5px",
          }}
        >
          加载模型中...
        </div>
      )}
    </div>
  );
};

export default ModelViewer;
