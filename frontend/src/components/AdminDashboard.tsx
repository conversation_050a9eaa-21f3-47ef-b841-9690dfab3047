import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

// 管理员仪表板接口定义
interface AdminStats {
  total_users: number;
  active_users: number;
  total_memorials: number;
  pending_content: number;
  reports_count: number;
  system_health: 'healthy' | 'warning' | 'critical';
}

interface User {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  last_login_at?: string;
}

interface Memorial {
  id: string;
  title: string;
  creator_username: string;
  created_at: string;
  is_public: boolean;
  visit_count: number;
  status: 'active' | 'suspended' | 'pending_review';
}

interface ContentReport {
  id: string;
  content_type: 'memorial' | 'message' | 'tribute';
  content_id: string;
  reporter_username: string;
  reason: string;
  status: 'pending' | 'resolved' | 'dismissed';
  created_at: string;
}

const AdminDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'users' | 'memorials' | 'content' | 'reports' | 'system'>('overview');
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [memorials, setMemorials] = useState<Memorial[]>([]);
  const [reports, setReports] = useState<ContentReport[]>([]);

  // 模拟数据加载
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟统计数据
      setStats({
        total_users: 1248,
        active_users: 892,
        total_memorials: 2156,
        pending_content: 23,
        reports_count: 8,
        system_health: 'healthy'
      });

      // 模拟用户数据
      setUsers([
        {
          id: '1',
          username: 'user001',
          email: '<EMAIL>',
          full_name: '张三',
          is_active: true,
          is_verified: true,
          created_at: '2024-01-15T10:30:00Z',
          last_login_at: '2024-06-10T08:45:00Z'
        },
        {
          id: '2',
          username: 'user002',
          email: '<EMAIL>',
          full_name: '李四',
          is_active: false,
          is_verified: true,
          created_at: '2024-02-20T14:15:00Z',
          last_login_at: '2024-06-08T16:22:00Z'
        }
      ]);

      // 模拟纪念空间数据
      setMemorials([
        {
          id: '1',
          title: '爷爷的纪念空间',
          creator_username: 'user001',
          created_at: '2024-01-20T09:00:00Z',
          is_public: true,
          visit_count: 156,
          status: 'active'
        },
        {
          id: '2',
          title: '奶奶的纪念空间',
          creator_username: 'user002',
          created_at: '2024-02-25T11:30:00Z',
          is_public: false,
          visit_count: 89,
          status: 'pending_review'
        }
      ]);

      // 模拟举报数据
      setReports([
        {
          id: '1',
          content_type: 'message',
          content_id: 'msg_001',
          reporter_username: 'user003',
          reason: '不当言论',
          status: 'pending',
          created_at: '2024-06-09T15:20:00Z'
        },
        {
          id: '2',
          content_type: 'memorial',
          content_id: 'mem_002',
          reporter_username: 'user004',
          reason: '涉嫌虚假信息',
          status: 'resolved',
          created_at: '2024-06-08T13:45:00Z'
        }
      ]);

      setLoading(false);
    };

    loadData();
  }, []);

  // 渲染概览标签页
  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-blue-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100">总用户数</p>
              <p className="text-3xl font-bold">{stats?.total_users}</p>
            </div>
            <i className="fas fa-users text-blue-200 text-4xl"></i>
          </div>
        </div>

        <div className="bg-green-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100">活跃用户</p>
              <p className="text-3xl font-bold">{stats?.active_users}</p>
            </div>
            <i className="fas fa-user-check text-green-200 text-4xl"></i>
          </div>
        </div>

        <div className="bg-purple-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100">纪念空间</p>
              <p className="text-3xl font-bold">{stats?.total_memorials}</p>
            </div>
            <i className="fas fa-monument text-purple-200 text-4xl"></i>
          </div>
        </div>

        <div className="bg-yellow-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-yellow-100">待审核内容</p>
              <p className="text-3xl font-bold">{stats?.pending_content}</p>
            </div>
            <i className="fas fa-clock text-yellow-200 text-4xl"></i>
          </div>
        </div>

        <div className="bg-red-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-red-100">举报数量</p>
              <p className="text-3xl font-bold">{stats?.reports_count}</p>
            </div>
            <i className="fas fa-flag text-red-200 text-4xl"></i>
          </div>
        </div>

        <div className={`rounded-lg p-6 text-white ${
          stats?.system_health === 'healthy' ? 'bg-green-600' :
          stats?.system_health === 'warning' ? 'bg-yellow-600' : 'bg-red-600'
        }`}>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white opacity-90">系统状态</p>
              <p className="text-lg font-bold">
                {stats?.system_health === 'healthy' ? '健康' :
                 stats?.system_health === 'warning' ? '警告' : '异常'}
              </p>
            </div>
            <i className={`text-4xl ${
              stats?.system_health === 'healthy' ? 'fas fa-check-circle' :
              stats?.system_health === 'warning' ? 'fas fa-exclamation-triangle' : 'fas fa-times-circle'
            }`}></i>
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button
            onClick={() => setActiveTab('users')}
            className="p-4 bg-blue-50 hover:bg-blue-100 rounded-lg text-center transition-colors"
          >
            <i className="fas fa-users text-blue-600 text-2xl mb-2 block"></i>
            <span className="text-blue-800 font-medium">用户管理</span>
          </button>
          <button
            onClick={() => setActiveTab('memorials')}
            className="p-4 bg-purple-50 hover:bg-purple-100 rounded-lg text-center transition-colors"
          >
            <i className="fas fa-monument text-purple-600 text-2xl mb-2 block"></i>
            <span className="text-purple-800 font-medium">纪念空间</span>
          </button>
          <button
            onClick={() => setActiveTab('content')}
            className="p-4 bg-yellow-50 hover:bg-yellow-100 rounded-lg text-center transition-colors"
          >
            <i className="fas fa-filter text-yellow-600 text-2xl mb-2 block"></i>
            <span className="text-yellow-800 font-medium">内容审核</span>
          </button>
          <button
            onClick={() => setActiveTab('reports')}
            className="p-4 bg-red-50 hover:bg-red-100 rounded-lg text-center transition-colors"
          >
            <i className="fas fa-flag text-red-600 text-2xl mb-2 block"></i>
            <span className="text-red-800 font-medium">举报处理</span>
          </button>
        </div>
      </div>
    </div>
  );

  // 渲染用户管理标签页
  const renderUsersTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">用户管理</h3>
        <div className="flex space-x-3">
          <input
            type="text"
            placeholder="搜索用户..."
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <select className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="">所有状态</option>
            <option value="active">活跃</option>
            <option value="inactive">非活跃</option>
            <option value="verified">已验证</option>
            <option value="unverified">未验证</option>
          </select>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">邮箱</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">注册时间</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后登录</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {users.map((user) => (
              <tr key={user.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="h-10 w-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                      {user.username[0].toUpperCase()}
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{user.username}</div>
                      <div className="text-sm text-gray-500">{user.full_name}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.email}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex space-x-2">
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                      user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {user.is_active ? '活跃' : '非活跃'}
                    </span>
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                      user.is_verified ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {user.is_verified ? '已验证' : '未验证'}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(user.created_at).toLocaleDateString('zh-CN')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {user.last_login_at ? new Date(user.last_login_at).toLocaleDateString('zh-CN') : '从未登录'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button className="text-blue-600 hover:text-blue-900">编辑</button>
                  <button className={`${user.is_active ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}>
                    {user.is_active ? '禁用' : '启用'}
                  </button>
                  <button className="text-gray-600 hover:text-gray-900">详情</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  // 渲染纪念空间管理标签页
  const renderMemorialsTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">纪念空间管理</h3>
        <div className="flex space-x-3">
          <input
            type="text"
            placeholder="搜索纪念空间..."
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <select className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="">所有状态</option>
            <option value="active">正常</option>
            <option value="pending_review">待审核</option>
            <option value="suspended">已暂停</option>
          </select>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">纪念空间</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建者</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">可见性</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">访问量</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {memorials.map((memorial) => (
              <tr key={memorial.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{memorial.title}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{memorial.creator_username}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                    memorial.is_public ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {memorial.is_public ? '公开' : '私密'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{memorial.visit_count}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                    memorial.status === 'active' ? 'bg-green-100 text-green-800' :
                    memorial.status === 'pending_review' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {memorial.status === 'active' ? '正常' :
                     memorial.status === 'pending_review' ? '待审核' : '已暂停'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(memorial.created_at).toLocaleDateString('zh-CN')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button className="text-blue-600 hover:text-blue-900">查看</button>
                  <button className="text-green-600 hover:text-green-900">审核</button>
                  <button className="text-red-600 hover:text-red-900">暂停</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  // 渲染内容审核标签页
  const renderContentTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">内容审核</h3>
        <div className="flex space-x-3">
          <select className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="">所有类型</option>
            <option value="memorials">纪念空间</option>
            <option value="messages">留言</option>
            <option value="tributes">祭拜记录</option>
          </select>
          <select className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="">所有状态</option>
            <option value="pending">待审核</option>
            <option value="approved">已通过</option>
            <option value="rejected">已拒绝</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <i className="fas fa-clock text-yellow-600 mr-2"></i>
            <h4 className="font-medium text-yellow-800">待审核内容</h4>
          </div>
          <p className="text-2xl font-bold text-yellow-900">{stats?.pending_content}</p>
          <p className="text-sm text-yellow-700 mt-1">需要人工审核</p>
        </div>

        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <i className="fas fa-check-circle text-green-600 mr-2"></i>
            <h4 className="font-medium text-green-800">今日已审核</h4>
          </div>
          <p className="text-2xl font-bold text-green-900">156</p>
          <p className="text-sm text-green-700 mt-1">通过率 94%</p>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <i className="fas fa-times-circle text-red-600 mr-2"></i>
            <h4 className="font-medium text-red-800">今日拒绝</h4>
          </div>
          <p className="text-2xl font-bold text-red-900">10</p>
          <p className="text-sm text-red-700 mt-1">主要违规: 不当言论</p>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h4 className="font-medium text-gray-900 mb-4">快速审核工具</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors">
            <i className="fas fa-robot text-blue-600 text-2xl mb-2 block"></i>
            <span className="text-gray-700 font-medium">AI自动审核</span>
            <p className="text-sm text-gray-500 mt-1">启用智能内容审核</p>
          </button>
          <button className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-400 hover:bg-green-50 transition-colors">
            <i className="fas fa-batch text-green-600 text-2xl mb-2 block"></i>
            <span className="text-gray-700 font-medium">批量审核</span>
            <p className="text-sm text-gray-500 mt-1">批量处理待审核内容</p>
          </button>
        </div>
      </div>
    </div>
  );

  // 渲染举报处理标签页
  const renderReportsTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">举报处理</h3>
        <div className="flex space-x-3">
          <select className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="">所有类型</option>
            <option value="memorial">纪念空间</option>
            <option value="message">留言</option>
            <option value="tribute">祭拜记录</option>
          </select>
          <select className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
            <option value="">所有状态</option>
            <option value="pending">待处理</option>
            <option value="resolved">已解决</option>
            <option value="dismissed">已驳回</option>
          </select>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">举报内容</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">举报人</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">举报原因</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">举报时间</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {reports.map((report) => (
              <tr key={report.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {report.content_type === 'memorial' ? '纪念空间' :
                     report.content_type === 'message' ? '留言' : '祭拜记录'}
                  </div>
                  <div className="text-sm text-gray-500">ID: {report.content_id}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{report.reporter_username}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{report.reason}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                    report.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    report.status === 'resolved' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {report.status === 'pending' ? '待处理' :
                     report.status === 'resolved' ? '已解决' : '已驳回'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(report.created_at).toLocaleDateString('zh-CN')}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button className="text-blue-600 hover:text-blue-900">查看</button>
                  {report.status === 'pending' && (
                    <>
                      <button className="text-green-600 hover:text-green-900">通过</button>
                      <button className="text-red-600 hover:text-red-900">拒绝</button>
                    </>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  // 渲染系统监控标签页
  const renderSystemTab = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">系统监控</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="font-medium text-gray-900 mb-4">服务器状态</h4>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">CPU使用率</span>
              <span className="text-sm font-medium text-green-600">23%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-green-600 h-2 rounded-full" style={{ width: '23%' }}></div>
            </div>
          </div>
          
          <div className="space-y-3 mt-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">内存使用率</span>
              <span className="text-sm font-medium text-blue-600">67%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-600 h-2 rounded-full" style={{ width: '67%' }}></div>
            </div>
          </div>

          <div className="space-y-3 mt-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">磁盘使用率</span>
              <span className="text-sm font-medium text-yellow-600">45%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '45%' }}></div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="font-medium text-gray-900 mb-4">实时数据</h4>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">在线用户</span>
              <span className="text-lg font-bold text-green-600">234</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">今日访问量</span>
              <span className="text-lg font-bold text-blue-600">1,567</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">平均响应时间</span>
              <span className="text-lg font-bold text-purple-600">124ms</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">错误率</span>
              <span className="text-lg font-bold text-red-600">0.02%</span>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h4 className="font-medium text-gray-900 mb-4">最近日志</h4>
        <div className="space-y-2 max-h-64 overflow-y-auto">
          <div className="flex items-center space-x-2 text-sm">
            <span className="text-gray-500">2024-06-10 15:30:22</span>
            <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">INFO</span>
            <span className="text-gray-700">用户登录成功: user001</span>
          </div>
          <div className="flex items-center space-x-2 text-sm">
            <span className="text-gray-500">2024-06-10 15:28:15</span>
            <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">DEBUG</span>
            <span className="text-gray-700">纪念空间访问: memorial_001</span>
          </div>
          <div className="flex items-center space-x-2 text-sm">
            <span className="text-gray-500">2024-06-10 15:25:33</span>
            <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">WARN</span>
            <span className="text-gray-700">检测到可疑内容，已自动标记审核</span>
          </div>
          <div className="flex items-center space-x-2 text-sm">
            <span className="text-gray-500">2024-06-10 15:20:45</span>
            <span className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">ERROR</span>
            <span className="text-gray-700">邮件发送失败: 连接超时</span>
          </div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* 头部导航 */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <i className="fas fa-shield-alt text-blue-600 text-2xl mr-3"></i>
              <h1 className="text-xl font-bold text-gray-900">管理后台</h1>
            </div>
            <div className="flex items-center space-x-4">
              <button className="text-gray-500 hover:text-gray-700">
                <i className="fas fa-bell text-lg"></i>
              </button>
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  A
                </div>
                <span className="text-sm font-medium text-gray-700">管理员</span>
              </div>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 标签导航 */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="flex space-x-8">
            {[
              { key: 'overview', label: '概览', icon: 'fas fa-tachometer-alt' },
              { key: 'users', label: '用户管理', icon: 'fas fa-users' },
              { key: 'memorials', label: '纪念空间', icon: 'fas fa-monument' },
              { key: 'content', label: '内容审核', icon: 'fas fa-filter' },
              { key: 'reports', label: '举报处理', icon: 'fas fa-flag' },
              { key: 'system', label: '系统监控', icon: 'fas fa-server' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <i className={tab.icon}></i>
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* 标签内容 */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'overview' && renderOverviewTab()}
          {activeTab === 'users' && renderUsersTab()}
          {activeTab === 'memorials' && renderMemorialsTab()}
          {activeTab === 'content' && renderContentTab()}
          {activeTab === 'reports' && renderReportsTab()}
          {activeTab === 'system' && renderSystemTab()}
        </motion.div>
      </div>
    </div>
  );
};

export default AdminDashboard;