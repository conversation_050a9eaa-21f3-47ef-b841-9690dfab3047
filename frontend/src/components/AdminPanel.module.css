/* CSS Modules 样式文件 */
/* 使用 .module.css 后缀启用 CSS Modules */

/* 注意: 检测到可能的全局样式，请考虑使用 :global() 包装或移至全局样式文件 */
.admin-panel {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.admin-back-button {
  background-color: var(--color-gray-100);
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.admin-back-button:hover {
  background-color: var(--color-gray-300);
}

.admin-error {
  background-color: var(--color-gray-100);
  color: var(--color-error);
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  border-left: 4px solid var(--color-error);
}

.admin-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.2rem;
}

.admin-tabs {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--color-gray-300);
}

.admin-tabs button {
  background: none;
  border: none;
  padding: 1rem 2rem;
  font-size: 1rem;
  cursor: pointer;
  position: relative;
}

.admin-tabs button.active {
  font-weight: bold;
}

.admin-tabs button.active::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: var(--color-primary);
}

.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

.stat-card {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 10px rgba(var(--color-gray-900-rgb), 0.1);
}

.stat-value {
  font-size: 2.5rem;
  font-weight: bold;
  margin: 0.5rem 0;
  color: var(--color-primary);
}

.admin-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.admin-table th,
.admin-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--color-gray-300);
}

.admin-table th {
  background-color: var(--color-gray-100);
  font-weight: 500;
}

.admin-table tr:hover {
  background-color: var(--color-gray-100);
}

.action-button {
  background: none;
  border: none;
  cursor: pointer;
  margin-right: 0.5rem;
  padding: 5px 10px;
  border-radius: 4px;
}

.action-button.edit {
  color: var(--color-primary);
}

.action-button.edit:hover {
  background-color: var(--color-primary-light);
}

.action-button.delete {
  color: var(--color-error);
}

.action-button.delete:hover {
  background-color: var(--color-gray-100);
}
