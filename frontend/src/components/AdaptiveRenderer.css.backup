.adaptive-renderer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
  background-color: rgba(var(--color-gray-900-rgb), 0.7);
}

.adaptive-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid rgba(var(--color-surface-rgb), 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
  background-color: rgba(var(--color-gray-900-rgb), 0.7);
  padding: 20px;
}

.image-viewer {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: var(--color-gray-900);
  cursor: grab;
}

.image-viewer:active {
  cursor: grabbing;
}

.image-viewer img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  user-select: none;
  -webkit-user-drag: none;
}

.viewer-controls {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  text-align: center;
  color: white;
  background-color: rgba(var(--color-gray-900-rgb), 0.5);
  padding: 10px;
  font-size: 14px;
}

.server-rendered-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: var(--color-gray-900);
}

.server-rendered-container img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  user-select: none;
  -webkit-user-drag: none;
}

.control-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  cursor: grab;
  z-index: 10;
}

.control-overlay:active {
  cursor: grabbing;
}

.server-render-controls {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 20;
  background-color: rgba(var(--color-gray-900-rgb), 0.7);
  border-radius: 8px;
  padding: 10px;
  color: white;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.render-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.reset-view-btn {
  background-color: var(--color-primary);
  border: none;
  border-radius: 4px;
  padding: 5px 10px;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.reset-view-btn:hover {
  background-color: var(--color-primary-hover);
}

.gpu-badge {
  display: inline-block;
  background-color: var(--color-error);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  margin-top: 5px;
}

.quality-controls {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: var(--z-dropdown);
}

.quality-selector {
  padding: 8px 12px;
  border-radius: 4px;
  border: none;
  background-color: rgba(var(--color-gray-900-rgb), 0.7);
  color: white;
  font-size: 14px;
  cursor: pointer;
}

.quality-selector:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--color-surface-rgb), 0.5);
}
