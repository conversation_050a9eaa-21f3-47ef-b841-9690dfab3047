import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
// TODO: 更新类名使用方式
// 从: className="button primary"
// 到: className={clsx(styles.button, styles.primary)}

const AdminPanel: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [users, setUsers] = useState<
    {
      id: number;
      username: string;
      email: string;
      role: string;
      is_active: boolean;
    }[]
  >([]);
  const [environments, setEnvironments] = useState<
    {
      id: number;
      name: string;
      style_type: string;
    }[]
  >([]);
  const [activeTab, setActiveTab] = useState("dashboard");

  const fetchUsers = React.useCallback(async () => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch("/api/admin/users", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
      } else {
        throw new Error("Failed to fetch users");
      }
    } catch (err) {
      console.error("Error fetching users:", err);
      // 使用模拟数据进行演示
      setUsers([
        {
          id: 1,
          username: "admin",
          email: "<EMAIL>",
          role: "admin",
          is_active: true,
        },
        {
          id: 2,
          username: "test",
          email: "<EMAIL>",
          role: "user",
          is_active: true,
        },
      ]);
    }
  }, [setUsers]);

  const fetchEnvironments = React.useCallback(async () => {
    try {
      const response = await fetch("/api/main/environments");

      if (response.ok) {
        const data = await response.json();
        setEnvironments(data.environments || []);
      } else {
        throw new Error("Failed to fetch environments");
      }
    } catch (err) {
      console.error("Error fetching environments:", err);
      // 使用模拟数据进行演示
      setEnvironments([
        { id: 1, name: "佛教寺庙", style_type: "buddhist-temple" },
        { id: 2, name: "道教道观", style_type: "taoist-shrine" },
        { id: 3, name: "中国寺庙", style_type: "chinese-temple" },
      ]);
    }
  }, [setEnvironments]);

  const loadData = React.useCallback(async () => {
    setLoading(true);
    setError("");

    try {
      // 获取用户列表
      await fetchUsers();

      // 获取环境列表
      await fetchEnvironments();
    } catch (err) {
      console.error("Error loading admin data:", err);
      setError("加载数据时出错，请稍后再试");
    } finally {
      setLoading(false);
    }
  }, [fetchUsers, fetchEnvironments, setLoading, setError]);

  // 在组件挂载时加载数据
  useEffect(() => {
    // 检查用户是否已登录且是管理员
    const token = localStorage.getItem("token");
    const user = JSON.parse(localStorage.getItem("user") || "{}");

    if (!token || user.role !== "admin") {
      navigate("/");
      return;
    }

    // 加载初始数据
    loadData();
  }, [navigate, loadData]);

  if (loading) {
    return <div className="admin-loading">{t("loading")}...</div>;
  }

  return (
    <div className="admin-panel">
      <header className="admin-header">
        <h1>{t("admin.dashboard")}</h1>
        <button onClick={() => navigate("/")} className="admin-back-button">
          {t("backToHome")}
        </button>
      </header>

      {error && <div className="admin-error">{error}</div>}

      <div className="admin-tabs">
        <button
          className={activeTab === "dashboard" ? "active" : ""}
          onClick={() => setActiveTab("dashboard")}
        >
          {t("admin.dashboard")}
        </button>
        <button
          className={activeTab === "users" ? "active" : ""}
          onClick={() => setActiveTab("users")}
        >
          {t("admin.users")}
        </button>
        <button
          className={activeTab === "environments" ? "active" : ""}
          onClick={() => setActiveTab("environments")}
        >
          {t("admin.environments")}
        </button>
      </div>

      <div className="admin-content">
        {activeTab === "dashboard" && (
          <div className="dashboard-stats">
            <div className="stat-card">
              <h3>{t("admin.totalUsers")}</h3>
              <p className="stat-value">{users.length}</p>
            </div>
            <div className="stat-card">
              <h3>{t("admin.totalEnvironments")}</h3>
              <p className="stat-value">{environments.length}</p>
            </div>
          </div>
        )}

        {activeTab === "users" && (
          <div className="users-table-container">
            <h2>{t("admin.userManagement")}</h2>
            <table className="admin-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>{t("username")}</th>
                  <th>{t("email")}</th>
                  <th>{t("role")}</th>
                  <th>{t("status")}</th>
                  <th>{t("actions")}</th>
                </tr>
              </thead>
              <tbody>
                {users.map((user) => (
                  <tr key={user.id}>
                    <td>{user.id}</td>
                    <td>{user.username}</td>
                    <td>{user.email}</td>
                    <td>{user.role}</td>
                    <td>{user.is_active ? t("active") : t("inactive")}</td>
                    <td>
                      <button className="action-button edit">
                        {t("edit")}
                      </button>
                      <button className="action-button delete">
                        {t("delete")}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {activeTab === "environments" && (
          <div className="environments-table-container">
            <h2>{t("admin.environmentManagement")}</h2>
            <table className="admin-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>{t("name")}</th>
                  <th>{t("type")}</th>
                  <th>{t("actions")}</th>
                </tr>
              </thead>
              <tbody>
                {environments.map((env) => (
                  <tr key={env.id}>
                    <td>{env.id}</td>
                    <td>{env.name}</td>
                    <td>{env.style_type}</td>
                    <td>
                      <button className="action-button edit">
                        {t("edit")}
                      </button>
                      <button className="action-button delete">
                        {t("delete")}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminPanel;
