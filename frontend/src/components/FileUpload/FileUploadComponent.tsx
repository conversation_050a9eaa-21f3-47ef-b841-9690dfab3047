// 文件上传组件
import React, { useState, useRef, useCallback } from 'react';
import { fileUploadService, FileUploadOptions, UploadProgress, UploadResponse } from '../../services/fileUploadService';

interface FileUploadComponentProps {
  memorialSpaceId?: string;
  onUploadSuccess?: (response: UploadResponse) => void;
  onUploadError?: (error: string) => void;
  uploadOptions?: Partial<FileUploadOptions>;
  className?: string;
  disabled?: boolean;
  multiple?: boolean;
  acceptedFileTypes?: string;
  maxFileSize?: number; // in bytes
  showPreview?: boolean;
}

interface FileUploadItem {
  id: string;
  file: File;
  preview?: string;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
  response?: UploadResponse;
}

const FileUploadComponent: React.FC<FileUploadComponentProps> = ({
  memorialSpaceId,
  onUploadSuccess,
  onUploadError,
  uploadOptions = {},
  className = '',
  disabled = false,
  multiple = false,
  acceptedFileTypes = 'image/*,video/*,audio/*,.pdf',
  maxFileSize = 10 * 1024 * 1024, // 10MB
  showPreview = true,
}) => {
  const [uploadItems, setUploadItems] = useState<FileUploadItem[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理文件选择
  const handleFileSelect = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const newItems: FileUploadItem[] = [];

    fileArray.forEach((file) => {
      // 文件验证
      const validation = fileUploadService.validateFile(file, {
        maxSize: maxFileSize,
        allowedTypes: acceptedFileTypes.split(','),
      });

      if (!validation.valid) {
        if (onUploadError) {
          onUploadError(validation.error || '文件验证失败');
        }
        return;
      }

      const item: FileUploadItem = {
        id: Math.random().toString(36).substr(2, 9),
        file,
        progress: 0,
        status: 'pending',
      };

      // 生成预览（仅图片）
      if (showPreview && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const preview = e.target?.result as string;
          setUploadItems(prev => 
            prev.map(prevItem => 
              prevItem.id === item.id ? { ...prevItem, preview } : prevItem
            )
          );
        };
        reader.readAsDataURL(file);
      }

      newItems.push(item);
    });

    setUploadItems(prev => multiple ? [...prev, ...newItems] : newItems);
  }, [maxFileSize, acceptedFileTypes, multiple, showPreview, onUploadError]);

  // 处理文件上传
  const uploadFile = useCallback(async (item: FileUploadItem) => {
    if (!memorialSpaceId) {
      if (onUploadError) {
        onUploadError('纪念空间ID未提供');
      }
      return;
    }

    setUploadItems(prev =>
      prev.map(prevItem =>
        prevItem.id === item.id 
          ? { ...prevItem, status: 'uploading', progress: 0 }
          : prevItem
      )
    );

    const options: FileUploadOptions = {
      asset_type: 'life_photo',
      ...uploadOptions,
      onProgress: (progress: UploadProgress) => {
        setUploadItems(prev =>
          prev.map(prevItem =>
            prevItem.id === item.id 
              ? { ...prevItem, progress: progress.percentage }
              : prevItem
          )
        );
      },
    };

    try {
      const response = await fileUploadService.uploadToMemorialSpace(
        memorialSpaceId,
        item.file,
        options
      );

      setUploadItems(prev =>
        prev.map(prevItem =>
          prevItem.id === item.id 
            ? { ...prevItem, status: 'success', progress: 100, response }
            : prevItem
        )
      );

      if (onUploadSuccess) {
        onUploadSuccess(response);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '上传失败';
      
      setUploadItems(prev =>
        prev.map(prevItem =>
          prevItem.id === item.id 
            ? { ...prevItem, status: 'error', error: errorMessage }
            : prevItem
        )
      );

      if (onUploadError) {
        onUploadError(errorMessage);
      }
    }
  }, [memorialSpaceId, uploadOptions, onUploadSuccess, onUploadError]);

  // 自动上传所有待上传文件
  const uploadAllPending = useCallback(() => {
    uploadItems.filter(item => item.status === 'pending').forEach(uploadFile);
  }, [uploadItems, uploadFile]);

  // 移除文件
  const removeFile = useCallback((itemId: string) => {
    setUploadItems(prev => prev.filter(item => item.id !== itemId));
  }, []);

  // 拖拽处理
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragging(true);
    }
  }, [disabled]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [disabled, handleFileSelect]);

  // 点击上传
  const handleClick = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [disabled]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files);
    }
    // 清空input以允许重复选择同一文件
    e.target.value = '';
  }, [handleFileSelect]);

  return (
    <div className={`file-upload-component ${className}`}>
      {/* 拖拽上传区域 */}
      <div
        className={`
          border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer
          ${isDragging 
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          className="hidden"
          multiple={multiple}
          accept={acceptedFileTypes}
          onChange={handleInputChange}
          disabled={disabled}
        />
        
        <div className="text-4xl text-gray-400 mb-4">📁</div>
        <p className="text-gray-600 dark:text-gray-300 mb-2">
          {isDragging ? '放下文件以上传' : '点击选择文件或拖拽到此处'}
        </p>
        <p className="text-sm text-gray-400">
          支持格式：{acceptedFileTypes} | 最大大小：{(maxFileSize / 1024 / 1024).toFixed(1)}MB
        </p>
      </div>

      {/* 文件列表 */}
      {uploadItems.length > 0 && (
        <div className="mt-6 space-y-3">
          <div className="flex justify-between items-center">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              上传文件 ({uploadItems.length})
            </h4>
            {uploadItems.some(item => item.status === 'pending') && (
              <button
                onClick={uploadAllPending}
                disabled={disabled}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg text-sm"
              >
                上传全部
              </button>
            )}
          </div>

          {uploadItems.map((item) => (
            <div
              key={item.id}
              className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 flex items-center space-x-4"
            >
              {/* 预览 */}
              {showPreview && item.preview && (
                <img
                  src={item.preview}
                  alt="预览"
                  className="w-12 h-12 object-cover rounded"
                />
              )}
              
              {/* 文件信息 */}
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {item.file.name}
                </p>
                <p className="text-xs text-gray-500">
                  {(item.file.size / 1024 / 1024).toFixed(2)} MB
                </p>
                
                {/* 进度条 */}
                {item.status === 'uploading' && (
                  <div className="mt-2">
                    <div className="bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${item.progress}%` }}
                      />
                    </div>
                    <p className="text-xs text-gray-500 mt-1">{item.progress}%</p>
                  </div>
                )}
                
                {/* 错误信息 */}
                {item.status === 'error' && item.error && (
                  <p className="text-xs text-red-500 mt-1">{item.error}</p>
                )}
              </div>

              {/* 状态和操作 */}
              <div className="flex items-center space-x-2">
                {item.status === 'pending' && (
                  <button
                    onClick={() => uploadFile(item)}
                    disabled={disabled}
                    className="text-blue-600 hover:text-blue-700 text-sm"
                  >
                    上传
                  </button>
                )}
                
                {item.status === 'success' && (
                  <span className="text-green-600 text-sm">✓ 成功</span>
                )}
                
                {item.status === 'error' && (
                  <button
                    onClick={() => uploadFile(item)}
                    disabled={disabled}
                    className="text-orange-600 hover:text-orange-700 text-sm"
                  >
                    重试
                  </button>
                )}
                
                <button
                  onClick={() => removeFile(item.id)}
                  disabled={disabled || item.status === 'uploading'}
                  className="text-red-600 hover:text-red-700 text-sm"
                >
                  移除
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FileUploadComponent;