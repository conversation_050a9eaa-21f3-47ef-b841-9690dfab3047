import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import AuthContext from '../contexts/AuthContext';
import NotificationService from '../utils/NotificationService';
import {
  StoreItem,
  StoreCategory,
  CartItem,
  ShoppingCart,
  StoreFilters
} from '../types/store';

interface VirtualStoreProps {
  onClose?: () => void;
  memorialSpaceId?: string; // 关联的纪念空间ID
}

const VirtualStore: React.FC<VirtualStoreProps> = ({ onClose, memorialSpaceId: _memorialSpaceId }) => {
  const authContext = useContext(AuthContext);
  const [activeTab, setActiveTab] = useState<'browse' | 'cart' | 'orders' | 'wishlist'>('browse');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  
  // 数据状态
  const [storeItems, setStoreItems] = useState<StoreItem[]>([]);
  const [categories, setCategories] = useState<StoreCategory[]>([]);
  const [cart, setCart] = useState<ShoppingCart | null>(null);
  const [_selectedItem, setSelectedItem] = useState<StoreItem | null>(null);
  const [_showItemDetail, setShowItemDetail] = useState(false);
  
  // 过滤器状态
  const [filters, setFilters] = useState<StoreFilters>({
    sort_by: 'popular'
  });

  if (!authContext) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-600">请先登录以访问虚拟商店</p>
      </div>
    );
  }

  // 模拟数据加载
  useEffect(() => {
    loadStoreData();
  }, []);

  const loadStoreData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟分类数据
      setCategories([
        {
          id: 'incense',
          name: '香烛类',
          description: '传统香烛、电子香烛',
          icon: 'fas fa-fire',
          sort_order: 1,
          is_active: true,
          item_count: 25,
          religious_tags: ['buddhist', 'taoist', 'universal']
        },
        {
          id: 'flowers',
          name: '鲜花类',
          description: '各式鲜花、花束',
          icon: 'fas fa-seedling',
          sort_order: 2,
          is_active: true,
          item_count: 18,
          religious_tags: ['universal', 'christian']
        },
        {
          id: 'offerings',
          name: '供品类',
          description: '水果、点心、酒水',
          icon: 'fas fa-apple-alt',
          sort_order: 3,
          is_active: true,
          item_count: 32,
          religious_tags: ['buddhist', 'taoist', 'universal']
        },
        {
          id: 'decorations',
          name: '装饰类',
          description: '场景装饰、特效',
          icon: 'fas fa-palette',
          sort_order: 4,
          is_active: true,
          item_count: 45,
          religious_tags: ['universal']
        }
      ]);

      // 模拟商品数据
      setStoreItems([
        {
          id: '1',
          name: '传统三支香',
          description: '经典三支檀香，寓意敬天、敬地、敬祖先',
          category: 'offering',
          type: 'incense',
          price: 299,
          currency: 'CNY',
          model_url: '/models/incense-three.glb',
          thumbnail_url: '/images/incense-three-thumb.jpg',
          preview_images: ['/images/incense-three-1.jpg', '/images/incense-three-2.jpg'],
          model_size: 245,
          polygon_count: 1200,
          is_animated: true,
          duration: 300,
          sound_effect: '/audio/incense-burning.mp3',
          religious_tags: ['buddhist', 'taoist', 'universal'],
          customization_options: [
            {
              id: 'inscription',
              name: '刻字定制',
              type: 'text',
              required: false,
              price_modifier: 100,
              max_length: 20
            }
          ],
          is_featured: true,
          is_new: false,
          discount_percentage: 10,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-06-01T00:00:00Z',
          download_count: 1567,
          rating_average: 4.8,
          rating_count: 234
        },
        {
          id: '2',
          name: '白玫瑰花束',
          description: '纯洁的白玫瑰花束，表达深深的思念',
          category: 'offering',
          type: 'flower',
          price: 599,
          currency: 'CNY',
          model_url: '/models/white-roses.glb',
          thumbnail_url: '/images/white-roses-thumb.jpg',
          preview_images: ['/images/white-roses-1.jpg'],
          model_size: 512,
          polygon_count: 2800,
          is_animated: true,
          duration: 60,
          particle_effect: 'petals_falling',
          religious_tags: ['universal', 'christian'],
          customization_options: [
            {
              id: 'quantity',
              name: '花朵数量',
              type: 'size',
              required: false,
              price_modifier: 0,
              size_options: [
                { name: '9朵', multiplier: 1 },
                { name: '19朵', multiplier: 2 },
                { name: '99朵', multiplier: 5 }
              ]
            }
          ],
          is_featured: false,
          is_new: true,
          created_at: '2024-05-01T00:00:00Z',
          updated_at: '2024-06-01T00:00:00Z',
          download_count: 892,
          rating_average: 4.6,
          rating_count: 156
        },
        {
          id: '3',
          name: '水果供盘',
          description: '精美水果拼盘，包含苹果、橙子、香蕉',
          category: 'offering',
          type: 'food',
          price: 899,
          currency: 'CNY',
          model_url: '/models/fruit-plate.glb',
          thumbnail_url: '/images/fruit-plate-thumb.jpg',
          preview_images: ['/images/fruit-plate-1.jpg', '/images/fruit-plate-2.jpg'],
          model_size: 1024,
          polygon_count: 4500,
          is_animated: false,
          religious_tags: ['buddhist', 'taoist', 'universal'],
          customization_options: [],
          is_featured: true,
          is_new: false,
          created_at: '2024-02-01T00:00:00Z',
          updated_at: '2024-06-01T00:00:00Z',
          download_count: 2341,
          rating_average: 4.9,
          rating_count: 445
        },
        {
          id: '4',
          name: '星空背景',
          description: '浩瀚星空背景，营造宁静祥和的氛围',
          category: 'scene',
          type: 'background',
          price: 1299,
          currency: 'CNY',
          model_url: '/models/starry-sky.glb',
          thumbnail_url: '/images/starry-sky-thumb.jpg',
          preview_images: ['/images/starry-sky-1.jpg'],
          model_size: 2048,
          polygon_count: 500,
          is_animated: true,
          duration: 600,
          sound_effect: '/audio/peaceful-ambience.mp3',
          religious_tags: ['universal'],
          customization_options: [
            {
              id: 'time_of_day',
              name: '时间设置',
              type: 'color',
              required: false,
              price_modifier: 0,
              color_palette: ['#1a1a2e', '#16213e', '#0f3460']
            }
          ],
          is_featured: true,
          is_new: true,
          created_at: '2024-06-01T00:00:00Z',
          updated_at: '2024-06-01T00:00:00Z',
          download_count: 678,
          rating_average: 4.7,
          rating_count: 89
        }
      ]);

      // 模拟购物车数据
      setCart({
        id: 'cart_001',
        user_id: authContext.user?.id || '',
        items: [],
        total_amount: 0,
        currency: 'CNY',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    } catch (error) {
      console.error('加载商店数据失败:', error);
      NotificationService.error('加载失败', '无法加载商店数据，请刷新重试');
    } finally {
      setLoading(false);
    }
  };

  // 添加到购物车
  const addToCart = (item: StoreItem, quantity: number = 1, customizations: any = {}) => {
    if (!cart) return;

    const existingItemIndex = cart.items.findIndex(
      cartItem => cartItem.store_item.id === item.id
    );

    let updatedItems = [...cart.items];
    const calculatedPrice = item.price * quantity;

    if (existingItemIndex >= 0) {
      updatedItems[existingItemIndex].quantity += quantity;
      updatedItems[existingItemIndex].calculated_price += calculatedPrice;
    } else {
      const newCartItem: CartItem = {
        id: `cart_item_${Date.now()}`,
        store_item: item,
        quantity,
        customizations,
        calculated_price: calculatedPrice,
        added_at: new Date().toISOString()
      };
      updatedItems.push(newCartItem);
    }

    const updatedCart = {
      ...cart,
      items: updatedItems,
      total_amount: updatedItems.reduce((sum, item) => sum + item.calculated_price, 0),
      updated_at: new Date().toISOString()
    };

    setCart(updatedCart);
    NotificationService.success('添加成功', `${item.name} 已添加到购物车`);
  };

  // 渲染商品卡片
  const renderItemCard = (item: StoreItem) => (
    <motion.div
      key={item.id}
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 cursor-pointer"
      onClick={() => {
        setSelectedItem(item);
        setShowItemDetail(true);
      }}
    >
      <div className="relative">
        <img
          src={item.thumbnail_url}
          alt={item.name}
          className="w-full h-48 object-cover"
        />
        {item.is_new && (
          <span className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-bold">
            新品
          </span>
        )}
        {item.is_featured && (
          <span className="absolute top-2 right-2 bg-amber-500 text-white px-2 py-1 rounded text-xs font-bold">
            精选
          </span>
        )}
        {item.discount_percentage && (
          <span className="absolute bottom-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-bold">
            -{item.discount_percentage}%
          </span>
        )}
      </div>
      
      <div className="p-4">
        <h3 className="font-bold text-gray-900 mb-2 line-clamp-2">{item.name}</h3>
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{item.description}</p>
        
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-1">
            <span className="text-yellow-500">★</span>
            <span className="text-sm text-gray-600">{item.rating_average}</span>
            <span className="text-sm text-gray-400">({item.rating_count})</span>
          </div>
          <div className="flex space-x-1">
            {item.religious_tags.slice(0, 2).map(tag => (
              <span key={tag} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                {tag === 'buddhist' ? '佛教' :
                 tag === 'christian' ? '基督教' :
                 tag === 'taoist' ? '道教' :
                 tag === 'islamic' ? '伊斯兰教' :
                 tag === 'universal' ? '通用' : '世俗'}
              </span>
            ))}
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {item.discount_percentage ? (
              <>
                <span className="text-xl font-bold text-red-600">
                  ¥{((item.price * (100 - item.discount_percentage)) / 10000).toFixed(2)}
                </span>
                <span className="text-sm text-gray-400 line-through">
                  ¥{(item.price / 100).toFixed(2)}
                </span>
              </>
            ) : (
              <span className="text-xl font-bold text-gray-900">
                ¥{(item.price / 100).toFixed(2)}
              </span>
            )}
          </div>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              addToCart(item);
            }}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            加入购物车
          </button>
        </div>

        {/* 商品特性图标 */}
        <div className="flex items-center space-x-2 mt-3 pt-3 border-t border-gray-100">
          {item.is_animated && (
            <span className="text-xs text-blue-600 flex items-center">
              <i className="fas fa-play mr-1"></i>动画
            </span>
          )}
          {item.sound_effect && (
            <span className="text-xs text-green-600 flex items-center">
              <i className="fas fa-volume-up mr-1"></i>音效
            </span>
          )}
          {item.particle_effect && (
            <span className="text-xs text-purple-600 flex items-center">
              <i className="fas fa-magic mr-1"></i>特效
            </span>
          )}
          {item.customization_options.length > 0 && (
            <span className="text-xs text-amber-600 flex items-center">
              <i className="fas fa-edit mr-1"></i>可定制
            </span>
          )}
        </div>
      </div>
    </motion.div>
  );

  // 渲染商品浏览页面
  const renderBrowsePage = () => (
    <div className="space-y-6">
      {/* 搜索和过滤器 */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="搜索商品..."
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">所有分类</option>
            {categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name} ({category.item_count})
              </option>
            ))}
          </select>
          
          <select
            value={filters.sort_by}
            onChange={(e) => setFilters(prev => ({ ...prev, sort_by: e.target.value as any }))}
            className="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="popular">热门推荐</option>
            <option value="newest">最新上架</option>
            <option value="price_low">价格从低到高</option>
            <option value="price_high">价格从高到低</option>
            <option value="rating">评分最高</option>
          </select>
        </div>
      </div>

      {/* 分类快速选择 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="font-bold text-gray-900 mb-4">商品分类</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {categories.map(category => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`p-4 rounded-lg border-2 text-center transition-all ${
                selectedCategory === category.id
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'
              }`}
            >
              <i className={`${category.icon} text-2xl mb-2 block`}></i>
              <div className="font-medium">{category.name}</div>
              <div className="text-sm text-gray-500">{category.item_count} 件商品</div>
            </button>
          ))}
        </div>
      </div>

      {/* 商品网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <AnimatePresence>
          {storeItems
            .filter(item => 
              selectedCategory === 'all' || 
              categories.find(cat => cat.id === selectedCategory)?.name.includes(item.type) ||
              item.category === selectedCategory
            )
            .filter(item =>
              searchQuery === '' ||
              item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
              item.description.toLowerCase().includes(searchQuery.toLowerCase())
            )
            .map(renderItemCard)}
        </AnimatePresence>
      </div>
    </div>
  );

  // 渲染购物车页面
  const renderCartPage = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-bold text-gray-900 mb-4">购物车</h3>
        
        {!cart || cart.items.length === 0 ? (
          <div className="text-center py-12">
            <i className="fas fa-shopping-cart text-gray-400 text-6xl mb-4"></i>
            <p className="text-gray-600 text-lg mb-4">购物车是空的</p>
            <button
              onClick={() => setActiveTab('browse')}
              className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              去购物
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {cart.items.map(item => (
              <div key={item.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                <img
                  src={item.store_item.thumbnail_url}
                  alt={item.store_item.name}
                  className="w-16 h-16 object-cover rounded"
                />
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{item.store_item.name}</h4>
                  <p className="text-sm text-gray-600">{item.store_item.description}</p>
                  <div className="flex items-center space-x-4 mt-2">
                    <span className="text-sm text-gray-500">数量: {item.quantity}</span>
                    <span className="text-sm font-medium text-gray-900">
                      ¥{(item.calculated_price / 100).toFixed(2)}
                    </span>
                  </div>
                </div>
                <button className="text-red-600 hover:text-red-800">
                  <i className="fas fa-trash"></i>
                </button>
              </div>
            ))}
            
            <div className="border-t pt-4">
              <div className="flex justify-between items-center text-lg font-bold">
                <span>总计:</span>
                <span>¥{(cart.total_amount / 100).toFixed(2)}</span>
              </div>
              <button className="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-colors mt-4">
                立即结算
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* 头部 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <i className="fas fa-store text-blue-600 text-2xl"></i>
              <h1 className="text-xl font-bold text-gray-900">虚拟商店</h1>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setActiveTab('cart')}
                className="relative text-gray-600 hover:text-gray-900"
              >
                <i className="fas fa-shopping-cart text-xl"></i>
                {cart && cart.items.length > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                    {cart.items.length}
                  </span>
                )}
              </button>
              
              {onClose && (
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <i className="fas fa-times text-xl"></i>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 标签导航 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {[
              { key: 'browse', label: '浏览商品', icon: 'fas fa-th-large' },
              { key: 'cart', label: '购物车', icon: 'fas fa-shopping-cart' },
              { key: 'orders', label: '我的订单', icon: 'fas fa-receipt' },
              { key: 'wishlist', label: '心愿单', icon: 'fas fa-heart' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <i className={tab.icon}></i>
                <span>{tab.label}</span>
                {tab.key === 'cart' && cart && cart.items.length > 0 && (
                  <span className="bg-red-500 text-white rounded-full px-2 py-1 text-xs">
                    {cart.items.length}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'browse' && renderBrowsePage()}
          {activeTab === 'cart' && renderCartPage()}
          {activeTab === 'orders' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">我的订单</h3>
              <p className="text-gray-600">订单历史功能开发中...</p>
            </div>
          )}
          {activeTab === 'wishlist' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">心愿单</h3>
              <p className="text-gray-600">心愿单功能开发中...</p>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default VirtualStore;