import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import { AudioType } from "../utils/BabylonAudioManager";
import BabylonAudioManager from "../utils/BabylonAudioManager";
import { cn } from "../utils/cn";

interface AudioControlsProps {
  showDetails?: boolean;
}

const AudioControls: React.FC<AudioControlsProps> = ({
  showDetails = false,
}) => {
  const { t } = useTranslation();
  const audioManager = BabylonAudioManager.getInstance();

  const [masterVolume, setMasterVolume] = useState(
    audioManager.getMasterVolume(),
  );
  const [musicVolume, setMusicVolume] = useState(
    audioManager.getVolumeByType(AudioType.MUSIC),
  );
  const [sfxVolume, setSfxVolume] = useState(
    audioManager.getVolumeByType(AudioType.SOUND_EFFECT),
  );
  const [ambientVolume, setAmbientVolume] = useState(
    audioManager.getVolumeByType(AudioType.AMBIENT),
  );
  const [voiceVolume, setVoiceVolume] = useState(
    audioManager.getVolumeByType(AudioType.VOICE),
  );

  const [muted, setMuted] = useState(audioManager.isMuted());
  const [musicMuted, setMusicMuted] = useState(
    audioManager.isMutedByType(AudioType.MUSIC),
  );
  const [sfxMuted, setSfxMuted] = useState(
    audioManager.isMutedByType(AudioType.SOUND_EFFECT),
  );
  const [ambientMuted, setAmbientMuted] = useState(
    audioManager.isMutedByType(AudioType.AMBIENT),
  );
  const [voiceMuted, setVoiceMuted] = useState(
    audioManager.isMutedByType(AudioType.VOICE),
  );

  const [expanded, setExpanded] = useState(showDetails);

  // 处理主音量变化
  const handleMasterVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    setMasterVolume(value);
    audioManager.setMasterVolume(value);
  };

  // 处理音乐音量变化
  const handleMusicVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    setMusicVolume(value);
    audioManager.setVolumeByType(AudioType.MUSIC, value);
  };

  // 处理音效音量变化
  const handleSfxVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    setSfxVolume(value);
    audioManager.setVolumeByType(AudioType.SOUND_EFFECT, value);
  };

  // 处理环境音量变化
  const handleAmbientVolumeChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const value = parseFloat(e.target.value);
    setAmbientVolume(value);
    audioManager.setVolumeByType(AudioType.AMBIENT, value);
  };

  // 处理语音音量变化
  const handleVoiceVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value);
    setVoiceVolume(value);
    audioManager.setVolumeByType(AudioType.VOICE, value);
  };

  // 处理主静音切换
  const handleMutedToggle = () => {
    const newMuted = !muted;
    setMuted(newMuted);
    audioManager.setMuted(newMuted);
  };

  // 处理音乐静音切换
  const handleMusicMutedToggle = () => {
    const newMuted = !musicMuted;
    setMusicMuted(newMuted);
    audioManager.setMutedByType(AudioType.MUSIC, newMuted);
  };

  // 处理音效静音切换
  const handleSfxMutedToggle = () => {
    const newMuted = !sfxMuted;
    setSfxMuted(newMuted);
    audioManager.setMutedByType(AudioType.SOUND_EFFECT, newMuted);
  };

  // 处理环境静音切换
  const handleAmbientMutedToggle = () => {
    const newMuted = !ambientMuted;
    setAmbientMuted(newMuted);
    audioManager.setMutedByType(AudioType.AMBIENT, newMuted);
  };

  // 处理语音静音切换
  const handleVoiceMutedToggle = () => {
    const newMuted = !voiceMuted;
    setVoiceMuted(newMuted);
    audioManager.setMutedByType(AudioType.VOICE, newMuted);
  };

  // 播放测试音效
  const playTestSound = () => {
    // 加载并播放测试音效
    audioManager.loadAudio(
      "test-sound",
      "/audio/common/click.mp3",
      AudioType.SOUND_EFFECT,
      { autoplay: true, volume: 1.0 },
    );
  };

  return (
    <div
      className={cn(
        "bg-gray-800/90 backdrop-blur-sm rounded-lg p-4 shadow-lg border border-gray-700",
        "min-w-[280px] max-w-sm",
      )}
    >
      <div className={cn("flex items-center gap-3 mb-2")}>
        <button
          className={cn(
            "flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200",
            "hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-amber-500",
            muted
              ? "bg-red-600 text-white hover:bg-red-700"
              : "bg-amber-500 text-gray-900 hover:bg-amber-400",
          )}
          onClick={handleMutedToggle}
          aria-label={muted ? t("audio.unmute") : t("audio.mute")}
        >
          <i
            className={`fas fa-volume-${muted ? "mute" : masterVolume > 0.5 ? "up" : "down"} text-lg`}
          />
        </button>

        <div className={cn("flex-1 px-2")}>
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={masterVolume}
            onChange={handleMasterVolumeChange}
            disabled={muted}
            className={cn(
              "w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer",
              "slider-thumb:appearance-none slider-thumb:w-4 slider-thumb:h-4",
              "slider-thumb:rounded-full slider-thumb:bg-amber-500",
              "slider-thumb:cursor-pointer slider-thumb:shadow-lg",
              "disabled:opacity-50 disabled:cursor-not-allowed",
            )}
          />
        </div>

        <button
          className={cn(
            "flex items-center justify-center w-8 h-8 rounded-full",
            "text-gray-300 hover:text-white hover:bg-gray-700",
            "transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-amber-500",
          )}
          onClick={() => setExpanded(!expanded)}
          aria-label={expanded ? t("audio.collapse") : t("audio.expand")}
        >
          <i className={`fas fa-chevron-${expanded ? "up" : "down"} text-sm`} />
        </button>
      </div>

      {expanded && (
        <motion.div
          className={cn("space-y-3 pt-3 border-t border-gray-600")}
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: "auto", opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className={cn("flex items-center gap-3")}>
            <div className={cn("flex items-center gap-2 min-w-[100px]")}>
              <button
                className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full transition-all duration-200",
                  "focus:outline-none focus:ring-2 focus:ring-amber-500",
                  musicMuted
                    ? "bg-red-500 text-white hover:bg-red-600"
                    : "bg-blue-500 text-white hover:bg-blue-600",
                )}
                onClick={handleMusicMutedToggle}
              >
                <i className="fas fa-music text-sm" />
              </button>
              <span className="text-sm text-gray-300">{t("audio.music")}</span>
            </div>
            <div className="flex-1">
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={musicVolume}
                onChange={handleMusicVolumeChange}
                disabled={muted || musicMuted}
                className={cn(
                  "w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer",
                  "slider-thumb:appearance-none slider-thumb:w-3 slider-thumb:h-3",
                  "slider-thumb:rounded-full slider-thumb:bg-blue-500",
                  "slider-thumb:cursor-pointer slider-thumb:shadow-md",
                  "disabled:opacity-50 disabled:cursor-not-allowed",
                )}
              />
            </div>
          </div>

          <div className={cn("flex items-center gap-3")}>
            <div className={cn("flex items-center gap-2 min-w-[100px]")}>
              <button
                className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full transition-all duration-200",
                  "focus:outline-none focus:ring-2 focus:ring-amber-500",
                  sfxMuted
                    ? "bg-red-500 text-white hover:bg-red-600"
                    : "bg-green-500 text-white hover:bg-green-600",
                )}
                onClick={handleSfxMutedToggle}
              >
                <i className="fas fa-bell text-sm" />
              </button>
              <span className="text-sm text-gray-300">
                {t("audio.soundEffects")}
              </span>
            </div>
            <div className="flex-1 flex items-center gap-2">
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={sfxVolume}
                onChange={handleSfxVolumeChange}
                disabled={muted || sfxMuted}
                className={cn(
                  "flex-1 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer",
                  "slider-thumb:appearance-none slider-thumb:w-3 slider-thumb:h-3",
                  "slider-thumb:rounded-full slider-thumb:bg-green-500",
                  "slider-thumb:cursor-pointer slider-thumb:shadow-md",
                  "disabled:opacity-50 disabled:cursor-not-allowed",
                )}
              />
              <button
                className={cn(
                  "flex items-center justify-center w-6 h-6 rounded-full",
                  "bg-green-500 text-white hover:bg-green-600",
                  "transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500",
                  "disabled:opacity-50 disabled:cursor-not-allowed",
                )}
                onClick={playTestSound}
                disabled={muted || sfxMuted}
              >
                <i className="fas fa-play text-xs" />
              </button>
            </div>
          </div>

          <div className={cn("flex items-center gap-3")}>
            <div className={cn("flex items-center gap-2 min-w-[100px]")}>
              <button
                className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full transition-all duration-200",
                  "focus:outline-none focus:ring-2 focus:ring-amber-500",
                  ambientMuted
                    ? "bg-red-500 text-white hover:bg-red-600"
                    : "bg-teal-500 text-white hover:bg-teal-600",
                )}
                onClick={handleAmbientMutedToggle}
              >
                <i className="fas fa-wind text-sm" />
              </button>
              <span className="text-sm text-gray-300">
                {t("audio.ambient")}
              </span>
            </div>
            <div className="flex-1">
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={ambientVolume}
                onChange={handleAmbientVolumeChange}
                disabled={muted || ambientMuted}
                className={cn(
                  "w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer",
                  "slider-thumb:appearance-none slider-thumb:w-3 slider-thumb:h-3",
                  "slider-thumb:rounded-full slider-thumb:bg-teal-500",
                  "slider-thumb:cursor-pointer slider-thumb:shadow-md",
                  "disabled:opacity-50 disabled:cursor-not-allowed",
                )}
              />
            </div>
          </div>

          <div className={cn("flex items-center gap-3")}>
            <div className={cn("flex items-center gap-2 min-w-[100px]")}>
              <button
                className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full transition-all duration-200",
                  "focus:outline-none focus:ring-2 focus:ring-amber-500",
                  voiceMuted
                    ? "bg-red-500 text-white hover:bg-red-600"
                    : "bg-purple-500 text-white hover:bg-purple-600",
                )}
                onClick={handleVoiceMutedToggle}
              >
                <i className="fas fa-microphone text-sm" />
              </button>
              <span className="text-sm text-gray-300">{t("audio.voice")}</span>
            </div>
            <div className="flex-1">
              <input
                type="range"
                min="0"
                max="1"
                step="0.01"
                value={voiceVolume}
                onChange={handleVoiceVolumeChange}
                disabled={muted || voiceMuted}
                className={cn(
                  "w-full h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer",
                  "slider-thumb:appearance-none slider-thumb:w-3 slider-thumb:h-3",
                  "slider-thumb:rounded-full slider-thumb:bg-purple-500",
                  "slider-thumb:cursor-pointer slider-thumb:shadow-md",
                  "disabled:opacity-50 disabled:cursor-not-allowed",
                )}
              />
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default AudioControls;
