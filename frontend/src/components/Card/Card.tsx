import React from "react";
import { cn } from "../../utils/cn";

// 卡片变体类型定义
export type CardVariant = "default" | "elevated" | "outlined" | "flat";

// 卡片组件属性接口
export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  /** 卡片变体 */
  variant?: CardVariant;
  /** 是否为紧凑模式 */
  compact?: boolean;
  /** 是否可点击 */
  clickable?: boolean;
  /** 是否选中 */
  selected?: boolean;
  /** 子元素 */
  children?: React.ReactNode;
  /** 自定义类名 */
  className?: string;
}

// 卡片头部组件
export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  subtitle?: string;
  children?: React.ReactNode;
  className?: string;
}

export const CardHeader: React.FC<CardHeaderProps> = ({
  title,
  subtitle,
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={cn("px-6 py-4 border-b border-gray-200", className)}
      {...props}
    >
      {title && (
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
      )}
      {subtitle && <p className="text-sm text-gray-600 mt-1">{subtitle}</p>}
      {children}
    </div>
  );
};

// 卡片内容组件
export interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode;
  className?: string;
}

export const CardContent: React.FC<CardContentProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div className={cn("px-6 py-4", className)} {...props}>
      {children}
    </div>
  );
};

// 卡片底部组件
export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode;
  className?: string;
}

export const CardFooter: React.FC<CardFooterProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={cn("px-6 py-4 border-t border-gray-200 bg-gray-50", className)}
      {...props}
    >
      {children}
    </div>
  );
};

// 卡片媒体组件
export interface CardMediaProps
  extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  className?: string;
}

export const CardMedia: React.FC<CardMediaProps> = ({
  src,
  alt,
  className,
  ...props
}) => {
  return (
    <img
      src={src}
      alt={alt}
      className={cn("w-full h-48 object-cover", className)}
      {...props}
    />
  );
};

// 卡片操作区域组件
export interface CardActionsProps extends React.HTMLAttributes<HTMLDivElement> {
  children?: React.ReactNode;
  className?: string;
}

export const CardActions: React.FC<CardActionsProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={cn("px-6 py-4 flex gap-2 justify-end", className)}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * 通用卡片组件
 * 使用 Tailwind CSS 实现样式和响应式设计
 * 支持多种变体和组合使用
 */
export const Card: React.FC<CardProps> = ({
  variant = "default",
  compact = false,
  clickable = false,
  selected = false,
  children,
  className,
  ...props
}) => {
  // 基础卡片样式
  const baseClasses =
    "bg-white rounded-lg border border-gray-200 overflow-hidden";

  // 变体样式映射
  const variantClasses = {
    default: "shadow-sm",
    elevated: "shadow-lg",
    outlined: "border-2 border-gray-300 shadow-none",
    flat: "shadow-none border-none",
  };

  const cardClasses = cn(
    baseClasses,
    variantClasses[variant],
    {
      "p-4": compact,
      "cursor-pointer hover:shadow-md transition-shadow duration-200":
        clickable,
      "ring-2 ring-amber-500 border-amber-500": selected,
    },
    className,
  );

  return (
    <div className={cardClasses} {...props}>
      {children}
    </div>
  );
};

// 定义复合组件类型
type CardComponent = React.FC<CardProps> & {
  Header: typeof CardHeader;
  Content: typeof CardContent;
  Footer: typeof CardFooter;
  Media: typeof CardMedia;
  Actions: typeof CardActions;
};

// 复合组件导出
const CardWithSubComponents = Card as CardComponent;
CardWithSubComponents.Header = CardHeader;
CardWithSubComponents.Content = CardContent;
CardWithSubComponents.Footer = CardFooter;
CardWithSubComponents.Media = CardMedia;
CardWithSubComponents.Actions = CardActions;

export default CardWithSubComponents;
