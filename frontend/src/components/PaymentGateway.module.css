/* Memorial 支付网关样式 - 企业级支付体验设计 */

.paymentGateway {
  max-width: 600px;
  margin: 0 auto;
  background: var(--bg-primary, #ffffff);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  color: var(--text-secondary, #666666);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-light, #e0e0e0);
  border-top: 3px solid var(--primary, #007AFF);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 支付方式选择 */
.methodSelection {
  padding: 24px;
}

.header {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-light, #e8e8e8);
}

.header h3 {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary, #333333);
}

.amount {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: var(--primary, #007AFF);
}

.description {
  margin: 0;
  font-size: 14px;
  color: var(--text-secondary, #666666);
  line-height: 1.4;
}

/* 支付提供商区域 */
.providersContainer {
  margin-bottom: 24px;
}

.providerSection {
  margin-bottom: 24px;
}

.providerName {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #333333);
  display: flex;
  align-items: center;
}

.providerName:before {
  content: '';
  width: 4px;
  height: 16px;
  background: var(--primary, #007AFF);
  border-radius: 2px;
  margin-right: 8px;
}

.loadingMethods {
  padding: 16px;
  text-align: center;
  color: var(--text-secondary, #999999);
  font-size: 14px;
}

/* 支付方式网格 */
.methodsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.methodButton {
  display: flex;
  align-items: center;
  padding: 16px;
  background: var(--bg-secondary, #f8f9fa);
  border: 2px solid var(--border-light, #e8e8e8);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
}

.methodButton:hover {
  border-color: var(--primary-light, #66b3ff);
  background: var(--bg-hover, #f0f8ff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.15);
}

.methodButton.selected {
  border-color: var(--primary, #007AFF);
  background: var(--primary-lightest, #e6f3ff);
  box-shadow: 0 0 0 1px var(--primary, #007AFF);
}

.methodIcon {
  font-size: 24px;
  margin-right: 12px;
  min-width: 32px;
  text-align: center;
}

.methodInfo {
  flex: 1;
}

.methodName {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #333333);
  margin-bottom: 2px;
}

.methodDescription {
  display: block;
  font-size: 12px;
  color: var(--text-secondary, #666666);
  line-height: 1.3;
}

/* 操作按钮 */
.actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid var(--border-light, #e8e8e8);
}

.cancelButton {
  padding: 12px 24px;
  background: var(--bg-secondary, #f5f5f5);
  color: var(--text-secondary, #666666);
  border: 1px solid var(--border-light, #d0d0d0);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancelButton:hover {
  background: var(--bg-hover, #e8e8e8);
  border-color: var(--border-dark, #b0b0b0);
}

.payButton {
  padding: 12px 32px;
  background: var(--primary, #007AFF);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.payButton:hover:not(:disabled) {
  background: var(--primary-dark, #0056cc);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

.payButton:disabled {
  background: var(--disabled, #cccccc);
  cursor: not-allowed;
  transform: none;
}

.retryButton {
  padding: 12px 24px;
  background: var(--primary, #007AFF);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retryButton:hover {
  background: var(--primary-dark, #0056cc);
}

/* 处理中状态 */
.processing {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
}

.processing h3 {
  margin: 16px 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #333333);
}

.processing p {
  margin: 0 0 24px 0;
  color: var(--text-secondary, #666666);
  line-height: 1.5;
}

.methodInfo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 16px 0;
  padding: 12px 20px;
  background: var(--bg-secondary, #f8f9fa);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #333333);
}

/* 完成状态 */
.completed {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
}

.successIcon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: bounceIn 0.6s ease;
}

@keyframes bounceIn {
  0% { 
    transform: scale(0.3); 
    opacity: 0;
  }
  50% { 
    transform: scale(1.05); 
  }
  70% { 
    transform: scale(0.9); 
  }
  100% { 
    transform: scale(1); 
    opacity: 1;
  }
}

.completed h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--success, #28a745);
}

.completed p {
  margin: 0 0 16px 0;
  color: var(--text-secondary, #666666);
  line-height: 1.5;
}

.transactionId {
  margin: 16px 0 0 0 !important;
  padding: 8px 12px;
  background: var(--bg-secondary, #f8f9fa);
  border-radius: 6px;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', monospace;
  color: var(--text-tertiary, #888888);
}

/* 失败状态 */
.failed {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
}

.errorIcon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
  20%, 40%, 60%, 80% { transform: translateX(4px); }
}

.failed h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--error, #dc3545);
}

.failed p {
  margin: 0 0 24px 0;
  color: var(--text-secondary, #666666);
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .paymentGateway {
    margin: 0;
    border-radius: 0;
    min-height: 100vh;
  }

  .methodSelection {
    padding: 16px;
  }

  .methodsGrid {
    grid-template-columns: 1fr;
  }

  .actions {
    flex-direction: column-reverse;
  }

  .actions button {
    width: 100%;
    margin: 0;
  }

  .actions .cancelButton {
    margin-top: 8px;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .paymentGateway {
    background: var(--bg-primary-dark, #1a1a1a);
    color: var(--text-primary-dark, #ffffff);
  }

  .methodButton {
    background: var(--bg-secondary-dark, #2a2a2a);
    border-color: var(--border-dark, #404040);
  }

  .methodButton:hover {
    background: var(--bg-hover-dark, #333333);
  }

  .methodButton.selected {
    background: var(--primary-dark-bg, #0d2857);
  }
}