import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { cn } from "../utils/cn";

interface LoadingScreenProps {
  progress: number;
  message?: string;
  onComplete?: () => void;
  minDisplayTime?: number;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  progress,
  message,
  onComplete,
  minDisplayTime = 1000,
}) => {
  const { t } = useTranslation();
  const [displayProgress, setDisplayProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [startTime] = useState(Date.now());

  // 平滑过渡进度
  useEffect(() => {
    if (progress > displayProgress) {
      const interval = setInterval(() => {
        setDisplayProgress((prev) => {
          const next = Math.min(prev + 0.01, progress);
          if (next >= progress) {
            clearInterval(interval);
          }
          return next;
        });
      }, 16);

      return () => clearInterval(interval);
    }
  }, [progress, displayProgress]);

  // 处理加载完成
  useEffect(() => {
    if (progress >= 1) {
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

      const timer = setTimeout(() => {
        setIsVisible(false);
        if (onComplete) {
          onComplete();
        }
      }, remainingTime);

      return () => clearTimeout(timer);
    }
  }, [progress, minDisplayTime, startTime, onComplete]);

  if (!isVisible) {
    return null;
  }

  const progressPercent = Math.floor(displayProgress * 100);

  return (
    <div
      className={cn(
        "fixed inset-0 w-full h-full bg-gray-900/90 flex justify-center items-center z-50 text-white",
        "animate-in fade-in duration-300",
      )}
    >
      <div className={cn("text-center max-w-[80%] space-y-6")}>
        <h2 className="text-2xl font-semibold text-white">
          {t("loading.title", "加载中...")}
        </h2>

        <div
          className={cn(
            "w-80 h-2.5 bg-gray-800 rounded-full mx-auto overflow-hidden",
          )}
        >
          <div
            className={cn(
              "h-full bg-gradient-to-r from-amber-500 to-yellow-400 rounded-full",
              "transition-all duration-300 ease-out",
            )}
            style={{ width: `${progressPercent}%` }}
          />
        </div>

        <div className="text-lg font-medium text-amber-400">
          {progressPercent}%
        </div>

        {message && (
          <div className="text-sm text-gray-300 mt-4 animate-pulse">
            {message}
          </div>
        )}
      </div>
    </div>
  );
};

export default LoadingScreen;
