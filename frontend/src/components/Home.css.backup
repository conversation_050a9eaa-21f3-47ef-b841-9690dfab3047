/* Home.css - 使用统一样式系统 */
/* 移除body和container的重复定义，使用统一样式系统中的定义 */

/* Hero Section - Based on home.html and Design Spec */
.hero-section {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--color-moonlight-white); /* var(--color-gray-50) */
  /* background-image from home.html, with overlay */
  background-image:
    linear-gradient(rgba(var(--color-gray-900-rgb), 0.5), rgba(var(--color-gray-900-rgb), 0.7)),
    url("https://images.unsplash.com/photo-1518623001395-125242310d0c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2000&q=80");
  background-size: cover;
  background-position: center;
  min-height: 500px; /* Adjusted from home.html's height: 600px */
  padding: 4rem 1rem;
}

.hero-section h1 {
  font-size: 2.75rem; /* ~44px, between H1(28px) and larger display */
  font-weight: 700; /* Bold */
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  /* md */
  .hero-section h1 {
    font-size: 3.75rem; /* ~60px */
  }
}

.hero-section p {
  font-size: 1.125rem; /* ~18px, between Body and H3 */
  margin-bottom: 2.5rem;
  max-width: 48rem; /* max-w-3xl */
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 768px) {
  /* md */
  .hero-section p {
    font-size: 1.25rem; /* 20px */
  }
}

.hero-section .button-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  justify-content: center;
}

@media (min-width: 768px) {
  /* md */
  .hero-section .button-group {
    flex-direction: row;
  }
}

.hero-section .button-primary {
  background-color: var(--color-serene-blue); /* var(--color-primary) */
  color: var(--color-moonlight-white);
  font-weight: bold;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem; /* rounded-lg */
  font-size: 1.125rem; /* text-lg */
  transition:
    background-color 0.3s ease,
    transform 0.3s ease;
  text-decoration: none;
}

.hero-section .button-primary:hover {
  background-color: var(--color-primary-hover); /* Darker shade of Serene Blue */
  transform: scale(1.05);
}

.hero-section .button-secondary {
  background-color: transparent;
  border: 2px solid var(--color-moonlight-white);
  color: var(--color-moonlight-white);
  font-weight: bold;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem; /* rounded-lg */
  font-size: 1.125rem; /* text-lg */
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
  text-decoration: none;
}

.hero-section .button-secondary:hover {
  background-color: var(--color-moonlight-white);
  color: var(--color-serene-blue);
}

/* Core Values Section */
.core-values-section {
  padding-top: 4rem;
  padding-bottom: 4rem;
  background-color: var(
    --color-bg-secondary
  ); /* Light: white, Dark: #2C2C2C (slightly lighter than Dark Space Gray) */
}

.core-values-section h2 {
  font-size: 2rem; /* ~32px, between H1 and H2 spec */
  font-weight: 700; /* Bold */
  text-align: center;
  margin-bottom: 3rem; /* mb-12 md:mb-16 */
  color: var(
    --color-text-title
  ); /* Light: Serene Blue, Dark: Lighter Serene Blue */
}

@media (min-width: 768px) {
  /* md */
  .core-values-section h2 {
    font-size: 2.25rem; /* 36px */
    margin-bottom: 4rem;
  }
}

.core-values-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 2rem;
}

@media (min-width: 768px) {
  /* md */
  .core-values-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  /* lg */
  .core-values-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.value-card {
  background-color: var(--color-bg-card); /* Light: var(--color-gray-50), Dark: #3E3E3E */
  padding: 1.5rem;
  border-radius: 0.75rem; /* rounded-xl */
  box-shadow:
    0 4px 6px -1px rgba(var(--color-gray-900-rgb), 0.1),
    0 2px 4px -1px rgba(var(--color-gray-900-rgb), 0.06); /* shadow-lg */
  text-align: center;
  transition:
    box-shadow 0.3s ease,
    transform 0.3s ease;
}

.value-card:hover {
  box-shadow:
    0 10px 15px -3px rgba(var(--color-gray-900-rgb), 0.1),
    0 4px 6px -2px rgba(var(--color-gray-900-rgb), 0.05); /* shadow-xl */
  transform: translateY(-5px);
}

.value-card-icon-wrapper {
  background-color: var(
    --color-bg-icon-wrapper
  ); /* Light: #E0EAFC (Light Serene Blue), Dark: var(--color-primary-hover) (Darker Serene Blue) */
  width: 4rem; /* w-16 */
  height: 4rem; /* h-16 */
  border-radius: 9999px; /* rounded-full */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 1.5rem;
}

.value-card-icon-wrapper i {
  font-size: 1.5rem; /* text-2xl */
  color: var(--color-icon); /* Light: Serene Blue, Dark: Moonlight White */
}

.value-card h3 {
  font-size: 1.25rem; /* text-xl */
  font-weight: 700; /* Bold */
  margin-bottom: 0.75rem;
  color: var(
    --color-text-card-title
  ); /* Light: Darker Serene Blue, Dark: Lighter Serene Blue */
}

.value-card p {
  font-size: 1rem; /* text-base (16px) */
  color: var(--color-text-secondary); /* Light: var(--color-gray-600), Dark: var(--color-gray-400) */
}

/* Features Section */
.features-section {
  padding-top: 4rem;
  padding-bottom: 4rem;
  background-color: var(
    --color-bg-tertiary
  ); /* Light: #F8F9FA, Dark: #272727 */
}

.features-section h2 {
  font-size: 2rem; /* ~32px */
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem; /* mb-12 md:mb-16 */
  color: var(--color-text-title);
}

@media (min-width: 768px) {
  /* md */
  .features-section h2 {
    font-size: 2.25rem; /* 36px */
    margin-bottom: 4rem;
  }
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 4rem; /* mb-16 md:mb-20 */
}

@media (min-width: 768px) {
  /* md */
  .feature-item {
    flex-direction: row;
  }
  .feature-item.reverse {
    flex-direction: row-reverse;
  }
}

.feature-text-content {
  width: 100%;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  /* md */
  .feature-text-content {
    width: 50%;
    margin-bottom: 0;
    padding-left: 1.25rem; /* md:px-5 */
    padding-right: 1.25rem;
  }
  .feature-item.reverse .feature-text-content {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }
}

.feature-text-content h3 {
  font-size: 1.5rem; /* text-2xl */
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--color-text-card-title);
}

.feature-text-content p {
  font-size: 1.125rem; /* text-lg */
  color: var(--color-text-secondary);
  margin-bottom: 1.5rem;
}

.feature-points {
  list-style: none;
  padding: 0;
  margin: 0;
  space-y: 0.5rem;
}

.feature-points li {
  display: flex;
  align-items: center;
  color: var(--color-text-primary);
}

.feature-points i {
  color: var(--color-success); /* var(--color-accent) */
  margin-right: 0.5rem;
}

.feature-image-content {
  width: 100%;
}

@media (min-width: 768px) {
  /* md */
  .feature-image-content {
    width: 50%;
    padding-left: 1.25rem; /* md:px-5 */
    padding-right: 1.25rem;
  }
}

.feature-image-content img {
  border-radius: 0.5rem; /* rounded-lg */
  box-shadow:
    0 10px 15px -3px rgba(var(--color-gray-900-rgb), 0.1),
    0 4px 6px -2px rgba(var(--color-gray-900-rgb), 0.05); /* shadow-xl */
  width: 100%;
  height: auto;
  object-fit: cover;
}

/* Testimonials Section */
.testimonials-section {
  padding-top: 4rem;
  padding-bottom: 4rem;
  background-color: var(
    --color-bg-secondary
  ); /* Light: #E0EAFC (Indigo-50 like), Dark: #2C2C2C */
}

.testimonials-section h2 {
  font-size: 2rem; /* ~32px */
  font-weight: 700;
  text-align: center;
  margin-bottom: 3rem; /* mb-12 md:mb-16 */
  color: var(--color-text-title);
}

@media (min-width: 768px) {
  /* md */
  .testimonials-section h2 {
    font-size: 2.25rem; /* 36px */
    margin-bottom: 4rem;
  }
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 2rem;
}

@media (min-width: 768px) {
  /* md */
  .testimonials-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.testimonial-card {
  background-color: var(--color-bg-card-alt); /* Light: white, Dark: #3E3E3E */
  padding: 2rem;
  border-radius: 0.75rem; /* rounded-xl */
  box-shadow:
    0 4px 6px -1px rgba(var(--color-gray-900-rgb), 0.1),
    0 2px 4px -1px rgba(var(--color-gray-900-rgb), 0.06); /* shadow-lg */
  transition:
    box-shadow 0.3s ease,
    transform 0.3s ease;
}

.testimonial-card:hover {
  box-shadow:
    0 10px 15px -3px rgba(var(--color-gray-900-rgb), 0.1),
    0 4px 6px -2px rgba(var(--color-gray-900-rgb), 0.05); /* shadow-xl */
  transform: translateY(-5px);
}

.testimonial-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.testimonial-header img {
  width: 3rem; /* w-12 */
  height: 3rem; /* h-12 */
  border-radius: 9999px; /* rounded-full */
  margin-right: 1rem;
  object-fit: cover;
}

.testimonial-header h4 {
  font-weight: 700;
  color: var(--color-text-primary);
}

.testimonial-stars {
  color: var(--color-warm-gold); /* var(--color-secondary) */
}

.testimonial-card p {
  color: var(--color-text-secondary);
}

/* Footer Section */
.footer-section {
  background-color: var(--color-dark-space-gray); /* var(--color-gray-900) */
  color: var(--color-neutral-light-gray); /* var(--color-gray-400) */
  padding-top: 3rem;
  padding-bottom: 3rem;
}

@media (min-width: 768px) {
  /* md */
  .footer-section {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 2rem;
  margin-bottom: 2rem;
}

@media (min-width: 768px) {
  /* md */
  .footer-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.footer-section h5 {
  font-size: 1.25rem; /* text-xl */
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--color-moonlight-white);
}

.footer-section p,
.footer-section ul {
  font-size: 0.875rem; /* text-sm (14px) */
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
  space-y: 0.5rem;
}

.footer-section a {
  color: var(--color-neutral-light-gray);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: var(--color-moonlight-white);
}

.footer-social-links {
  display: flex;
  gap: 1rem;
}

.footer-contact-item {
  display: flex;
  align-items: center;
}

.footer-contact-item i {
  margin-right: 0.5rem;
}

.footer-bottom {
  border-top: 1px solid var(--color-neutral-dark-gray); /* var(--color-gray-600) */
  padding-top: 2rem;
  text-align: center;
  font-size: 0.875rem; /* text-sm */
}

/* 移除全局:root定义，使用统一样式文件中的变量 */

/* Ensure Navbar styles are compatible or defined elsewhere */
/* Example: if Navbar has its own CSS, ensure it respects dark mode variables or has its own toggle */
