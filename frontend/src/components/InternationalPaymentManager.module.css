/* Memorial 国际支付管理器样式 */

.paymentManager {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-primary, #ffffff);
}

/* 加载状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: var(--text-secondary, #666);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-light, #e0e0e0);
  border-top: 3px solid var(--primary, #007AFF);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 2px solid var(--border-light, #f0f0f0);
}

.header h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary, #333);
}

.header p {
  margin: 0;
  font-size: 16px;
  color: var(--text-secondary, #666);
}

/* 金额显示 */
.amountDisplay {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  text-align: center;
}

.originalAmount, .convertedAmount {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.convertedAmount:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 14px;
  opacity: 0.9;
}

.amount {
  font-size: 24px;
  font-weight: 700;
}

.conversionNote {
  font-size: 12px;
  opacity: 0.8;
  font-style: italic;
}

/* 支付方式选择 */
.methodSelection {
  margin-bottom: 32px;
}

.methodSelection h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.methodGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.methodCard {
  background: white;
  border: 2px solid var(--border-light, #e8e8e8);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.methodCard:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-color: var(--primary, #007AFF);
}

.methodCard.selected {
  border-color: var(--primary, #007AFF);
  background: linear-gradient(135deg, #f8fbff, #e3f2fd);
}

.methodCard.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--bg-secondary, #f8f9fa);
}

.methodIcon {
  font-size: 32px;
  margin-bottom: 12px;
}

.methodInfo h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.methodMeta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.regions {
  font-size: 12px;
  color: var(--text-secondary, #666);
}

.unavailable {
  font-size: 12px;
  color: var(--error, #dc3545);
  font-weight: 500;
}

.selectedIndicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: var(--primary, #007AFF);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* 支付表单 */
.paymentForm {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 32px;
}

.paymentForm h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

/* Stripe表单 */
.stripeForm {
  margin-bottom: 24px;
}

.cardElementContainer {
  margin-bottom: 20px;
}

.cardElementContainer label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #333);
}

.cardElement {
  padding: 12px 16px;
  border: 2px solid var(--border-light, #e8e8e8);
  border-radius: 8px;
  background: white;
  transition: border-color 0.2s ease;
}

.cardElement:focus-within {
  border-color: var(--primary, #007AFF);
  outline: none;
}

/* PayPal表单 */
.paypalForm {
  text-align: center;
}

.paypalForm p {
  margin: 0 0 20px 0;
  font-size: 14px;
  color: var(--text-secondary, #666);
}

/* 二维码表单 */
.alipayForm, .wechatForm {
  text-align: center;
}

.qrCodeContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.qrCode {
  width: 200px;
  height: 200px;
  border: 2px dashed var(--border-light, #e8e8e8);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary, #f8f9fa);
}

.qrPlaceholder {
  text-align: center;
  color: var(--text-secondary, #666);
}

.qrIcon {
  font-size: 48px;
  margin-bottom: 12px;
}

.qrPlaceholder p {
  margin: 0;
  font-size: 14px;
}

/* 错误信息 */
.error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.2);
  border-radius: 8px;
  color: var(--error, #dc3545);
  margin-bottom: 20px;
}

.errorIcon {
  font-size: 16px;
}

/* 支付操作 */
.paymentActions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 24px;
}

.cancelButton, .payButton {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cancelButton {
  background: var(--bg-secondary, #f8f9fa);
  color: var(--text-secondary, #666);
  border: 1px solid var(--border-light, #e8e8e8);
}

.cancelButton:hover:not(:disabled) {
  background: var(--bg-hover, #e8e8e8);
}

.payButton {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
}

.payButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 6px 24px rgba(40, 167, 69, 0.4);
}

.payButton:disabled {
  background: var(--bg-tertiary, #f0f0f0);
  color: var(--text-tertiary, #999);
  cursor: not-allowed;
  box-shadow: none;
}

.paymentAmount {
  font-weight: 700;
  padding-left: 8px;
  border-left: 1px solid rgba(255, 255, 255, 0.3);
}

/* 安全提示 */
.securityNotice {
  background: linear-gradient(135deg, #f8fff8, #e8f5e8);
  border: 1px solid rgba(40, 167, 69, 0.2);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  gap: 16px;
}

.securityIcon {
  font-size: 24px;
  color: var(--success, #28a745);
  flex-shrink: 0;
}

.securityText h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--success, #28a745);
}

.securityText ul {
  margin: 0;
  padding-left: 20px;
  list-style: none;
}

.securityText li {
  position: relative;
  padding: 2px 0;
  font-size: 14px;
  color: var(--text-secondary, #666);
  line-height: 1.5;
}

.securityText li::before {
  content: '✓';
  position: absolute;
  left: -20px;
  color: var(--success, #28a745);
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .paymentManager {
    padding: 16px;
  }

  .header {
    margin-bottom: 24px;
    padding-bottom: 20px;
  }

  .header h2 {
    font-size: 24px;
  }

  .amountDisplay {
    padding: 20px;
    margin-bottom: 24px;
  }

  .originalAmount, .convertedAmount {
    flex-direction: column;
    gap: 4px;
  }

  .amount {
    font-size: 20px;
  }

  .methodGrid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .methodCard {
    padding: 16px;
    flex-direction: row;
    text-align: left;
    align-items: center;
  }

  .methodIcon {
    font-size: 24px;
    margin-bottom: 0;
    margin-right: 12px;
  }

  .methodInfo {
    flex: 1;
  }

  .paymentForm {
    padding: 20px;
    margin-bottom: 24px;
  }

  .paymentActions {
    flex-direction: column;
    gap: 12px;
  }

  .cancelButton, .payButton {
    width: 100%;
    justify-content: center;
  }

  .qrCode {
    width: 160px;
    height: 160px;
  }

  .qrIcon {
    font-size: 36px;
  }

  .securityNotice {
    padding: 16px;
    flex-direction: column;
    text-align: center;
  }

  .securityText ul {
    text-align: left;
  }
}

@media (max-width: 480px) {
  .paymentManager {
    padding: 12px;
  }

  .header h2 {
    font-size: 20px;
  }

  .header p {
    font-size: 14px;
  }

  .amountDisplay {
    padding: 16px;
  }

  .amount {
    font-size: 18px;
  }

  .methodCard {
    padding: 12px;
  }

  .methodIcon {
    font-size: 20px;
  }

  .methodInfo h4 {
    font-size: 14px;
  }

  .regions, .unavailable {
    font-size: 11px;
  }

  .paymentForm {
    padding: 16px;
  }

  .qrCode {
    width: 140px;
    height: 140px;
  }

  .qrIcon {
    font-size: 32px;
  }

  .securityNotice {
    padding: 12px;
  }

  .securityText h4 {
    font-size: 14px;
  }

  .securityText li {
    font-size: 13px;
  }
}

/* 动画效果 */
.methodCard {
  animation: fadeInUp 0.5s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.paymentForm {
  animation: slideIn 0.3s ease forwards;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 脉冲动画 */
.payButton:not(:disabled) {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
  }
  50% {
    box-shadow: 0 4px 24px rgba(40, 167, 69, 0.5);
  }
  100% {
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.3);
  }
}