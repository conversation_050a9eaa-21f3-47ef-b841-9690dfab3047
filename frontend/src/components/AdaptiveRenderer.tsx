import React, { useState, useEffect, useRef } from "react";
import { Engine, Scene, Model } from "react-babylonjs";
import { Vector3, Color3, Color4 } from "@babylonjs/core";
import "@babylonjs/loaders";
import { useTranslation } from "react-i18next";
// TODO: 更新类名使用方式
// 从: className="button primary"
// 到: className={clsx(styles.button, styles.primary)}

// 低多边形模型组件
const LowPolyModel = ({
  rootUrl,
  fileName,
  position = Vector3.Zero(),
  scale = 1,
  onModelLoaded,
}: {
  rootUrl: string;
  fileName: string;
  position?: Vector3;
  scale?: number;
  onModelLoaded?: () => void;
}) => {
  return (
    <Model
      name="model"
      rootUrl={rootUrl}
      sceneFilename={fileName}
      position={position}
      scaling={new Vector3(scale, scale, scale)}
      onModelLoaded={() => {
        if (onModelLoaded) onModelLoaded();
      }}
    />
  );
};

// 图像查看器组件
const ImageViewer = ({ basePath }: { basePath: string }) => {
  const [angleIndex, setAngleIndex] = useState(0);
  const [elevIndex, setElevIndex] = useState(2); // 默认中间高度
  const [startX, setStartX] = useState<number | null>(null);
  const totalAngles = 8; // 简化版本，只有8个角度
  const totalElevations = 3; // 简化版本，只有3个高度

  const handleMouseDown = (e: React.MouseEvent | React.TouchEvent) => {
    const clientX = "touches" in e ? e.touches[0].clientX : e.clientX;
    setStartX(clientX);
  };

  const handleMouseMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (startX === null) return;

    const clientX = "touches" in e ? e.touches[0].clientX : e.clientX;
    const diff = clientX - startX;

    if (Math.abs(diff) > 10) {
      const direction = diff > 0 ? -1 : 1;
      setAngleIndex((prev) => {
        let newAngle = (prev + direction) % totalAngles;
        if (newAngle < 0) newAngle = totalAngles - 1;
        return newAngle;
      });
      setStartX(clientX);
    }
  };

  const handleMouseUp = () => {
    setStartX(null);
  };

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault();
    const direction = e.deltaY > 0 ? -1 : 1;
    setElevIndex((prev) =>
      Math.max(0, Math.min(totalElevations - 1, prev + direction)),
    );
  };

  useEffect(() => {
    window.addEventListener("mouseup", handleMouseUp);
    window.addEventListener("touchend", handleMouseUp);

    return () => {
      window.removeEventListener("mouseup", handleMouseUp);
      window.removeEventListener("touchend", handleMouseUp);
    };
  }, []);

  // 由于我们还没有实际的预渲染图像，这里使用一个占位图像
  const imageSrc = `${basePath}/placeholder_elev${elevIndex}_ang${angleIndex}.jpg`;

  return (
    <div
      className="image-viewer"
      onMouseDown={handleMouseDown}
      onTouchStart={handleMouseDown}
      onMouseMove={handleMouseMove}
      onTouchMove={handleMouseMove}
      onWheel={handleWheel}
    >
      <img
        src={imageSrc}
        alt="Buddhist Temple View"
        onError={(e) => {
          // 如果图像加载失败，使用备用图像
          (e.target as HTMLImageElement).src = "/models/vite.svg";
        }}
      />
      <div className="viewer-controls">
        <p>拖动旋转 | 滚轮调整高度</p>
      </div>
    </div>
  );
};

// 服务器渲染组件
const ServerRenderedView = ({ modelPath }: { modelPath: string }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [serverStatus, setServerStatus] = useState<{
    available: boolean;
    gpuRendering?: boolean;
  }>({ available: false });
  const [rotation, setRotation] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(5);
  const [startPos, setStartPos] = useState<{ x: number; y: number } | null>(
    null,
  );
  const controlOverlayRef = useRef<HTMLDivElement>(null);

  // 检查服务器渲染服务是否可用
  useEffect(() => {
    const checkServerStatus = async () => {
      try {
        const response = await fetch("/api/render-service/status");
        if (response.ok) {
          const status = await response.json();
          setServerStatus(status);
          setIsConnected(status.available);
          console.log("渲染服务状态:", status);
        } else {
          setIsConnected(false);
        }
      } catch (error) {
        console.error("检查渲染服务失败:", error);
        setIsConnected(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkServerStatus();
  }, []);

  // 初始化渲染会话
  useEffect(() => {
    if (!isConnected) return;

    const initSession = async () => {
      try {
        // 获取视口尺寸
        const width = window.innerWidth > 800 ? 800 : window.innerWidth;
        const height = window.innerHeight > 600 ? 600 : window.innerHeight;

        const response = await fetch("/api/render-service/init", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            modelPath,
            width,
            height,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          console.log("渲染会话初始化成功:", data);
        } else {
          console.error("初始化渲染会话失败");
          setIsConnected(false);
        }
      } catch (error) {
        console.error("初始化渲染会话异常:", error);
        setIsConnected(false);
      }
    };

    initSession();
  }, [isConnected, modelPath]);

  // 处理鼠标/触摸事件
  useEffect(() => {
    if (!controlOverlayRef.current || !isConnected) return;

    const overlay = controlOverlayRef.current;

    const handleMouseDown = (e: MouseEvent | TouchEvent) => {
      const clientX = "touches" in e ? e.touches[0].clientX : e.clientX;
      const clientY = "touches" in e ? e.touches[0].clientY : e.clientY;
      setStartPos({ x: clientX, y: clientY });
    };

    const handleMouseMove = (e: MouseEvent | TouchEvent) => {
      if (!startPos) return;

      const clientX = "touches" in e ? e.touches[0].clientX : e.clientX;
      const clientY = "touches" in e ? e.touches[0].clientY : e.clientY;

      const deltaX = clientX - startPos.x;
      const deltaY = clientY - startPos.y;

      if (Math.abs(deltaX) > 5 || Math.abs(deltaY) > 5) {
        // 发送旋转控制命令
        fetch("/api/render-control", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            type: "rotate",
            x: deltaX / overlay.clientWidth + 0.5,
            y: deltaY / overlay.clientHeight + 0.5,
          }),
        })
          .then((response) => response.json())
          .then((data) => {
            if (data.rotation) {
              setRotation(data.rotation);
            }
            if (data.zoom) {
              setZoom(data.zoom);
            }
          })
          .catch((error) => console.error("发送控制命令失败:", error));

        setStartPos({ x: clientX, y: clientY });
      }
    };

    const handleMouseUp = () => {
      setStartPos(null);
    };

    const handleWheel = (e: WheelEvent) => {
      e.preventDefault();

      // 发送缩放控制命令
      fetch("/api/render-control", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "zoom",
          delta: e.deltaY > 0 ? -1 : 1,
        }),
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.zoom) {
            setZoom(data.zoom);
          }
        })
        .catch((error) => console.error("发送缩放命令失败:", error));
    };

    // 添加事件监听器
    overlay.addEventListener("mousedown", handleMouseDown);
    overlay.addEventListener("touchstart", handleMouseDown);
    overlay.addEventListener("mousemove", handleMouseMove);
    overlay.addEventListener("touchmove", handleMouseMove);
    overlay.addEventListener("wheel", handleWheel);
    window.addEventListener("mouseup", handleMouseUp);
    window.addEventListener("touchend", handleMouseUp);

    return () => {
      // 移除事件监听器
      overlay.removeEventListener("mousedown", handleMouseDown);
      overlay.removeEventListener("touchstart", handleMouseDown);
      overlay.removeEventListener("mousemove", handleMouseMove);
      overlay.removeEventListener("touchmove", handleMouseMove);
      overlay.removeEventListener("wheel", handleWheel);
      window.removeEventListener("mouseup", handleMouseUp);
      window.removeEventListener("touchend", handleMouseUp);
    };
  }, [isConnected, startPos]);

  // 重置视图
  const resetView = () => {
    fetch("/api/render-control", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        type: "reset",
      }),
    })
      .then((response) => response.json())
      .then((data) => {
        if (data.rotation) {
          setRotation(data.rotation);
        }
        if (data.zoom) {
          setZoom(data.zoom);
        }
      })
      .catch((error) => console.error("重置视图失败:", error));
  };

  if (isLoading) {
    return <div className="loading-container">正在连接渲染服务器...</div>;
  }

  if (!isConnected) {
    return (
      <div className="error-container">
        <p>无法连接到渲染服务器，正在切换到图像模式</p>
        <ImageViewer basePath="/images/buddhist-temple" />
      </div>
    );
  }

  // 构建渲染流URL
  const streamUrl = `/api/render-stream?t=${Date.now()}`;

  return (
    <div className="server-rendered-container">
      <img src={streamUrl} alt="Server Rendered View" />
      <div
        className="control-overlay"
        ref={controlOverlayRef}
        style={{ cursor: startPos ? "grabbing" : "grab" }}
      ></div>
      <div className="server-render-controls">
        <div className="render-info">
          <span>
            旋转: X={rotation.x.toFixed(2)}, Y={rotation.y.toFixed(2)}
          </span>
          <span>缩放: {zoom.toFixed(1)}</span>
          {serverStatus?.gpuRendering && <span className="gpu-badge">GPU</span>}
        </div>
        <button className="reset-view-btn" onClick={resetView}>
          重置视图
        </button>
      </div>
    </div>
  );
};

// 检测设备GPU性能
const detectGPUPerformance = async (): Promise<number> => {
  return new Promise((resolve) => {
    // 创建临时canvas和WebGL上下文
    const canvas = document.createElement("canvas");
    const gl = canvas.getContext("webgl2") || canvas.getContext("webgl");

    if (!gl) {
      // WebGL不可用，返回最低性能级别
      resolve(0);
      return;
    }

    // 获取GPU信息
    const debugInfo = gl.getExtension("WEBGL_debug_renderer_info");
    const renderer = debugInfo
      ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL)
      : gl.getParameter(gl.RENDERER);

    // 检查是否为移动设备
    const isMobile =
      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent,
      );

    // 基于渲染器字符串和设备类型评估性能
    let performanceScore = 0;

    // 高端GPU关键词
    const highEndGPUs = [
      "RTX",
      "Radeon Pro",
      "Quadro",
      "Apple M1",
      "Apple M2",
      "Apple M3",
      "Apple M4",
    ];
    // 中端GPU关键词
    const midRangeGPUs = ["Intel Iris", "AMD Radeon", "NVIDIA GeForce"];

    // 检查GPU类型
    if (highEndGPUs.some((gpu) => renderer.includes(gpu))) {
      performanceScore = isMobile ? 1 : 2;
    } else if (midRangeGPUs.some((gpu) => renderer.includes(gpu))) {
      performanceScore = isMobile ? 0 : 1;
    }

    // 清理
    canvas.remove();

    resolve(performanceScore);
  });
};

// 自适应渲染器属性接口
interface AdaptiveRendererProps {
  modelPath: string;
  onSceneReady?: (scene: any, engine: any) => void;
}

// 主组件 - 自适应渲染
const AdaptiveRenderer = React.forwardRef<any, AdaptiveRendererProps>(({ 
  modelPath, 
  onSceneReady 
}, _ref) => {
  const { t } = useTranslation();
  const [renderMode, setRenderMode] = useState("auto");
  const [autoDetectedMode, setAutoDetectedMode] = useState("loading");
  const [isModelLoaded, setIsModelLoaded] = useState(false);

  // 检测设备性能并设置渲染模式
  useEffect(() => {
    const detectPerformance = async () => {
      const tier = await detectGPUPerformance();

      // 根据GPU性能设置渲染模式
      if (renderMode !== "auto") return;

      if (tier >= 2) {
        setAutoDetectedMode("high");
      } else if (tier === 1) {
        setAutoDetectedMode("medium");
      } else if (
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
          navigator.userAgent,
        )
      ) {
        // 移动设备低性能GPU，使用图像模式
        setAutoDetectedMode("image");
      } else {
        // 检查网络条件，决定使用服务器渲染还是图像模式
        checkNetworkCondition().then((isGoodNetwork) => {
          setAutoDetectedMode(isGoodNetwork ? "server" : "image");
        });
      }
    };

    detectPerformance();
  }, [renderMode]);

  // 检查网络条件
  const checkNetworkCondition = async () => {
    try {
      const startTime = Date.now();
      // 这里应该是一个轻量级的API调用
      const response = await fetch("/api/ping").catch(() => ({ ok: false }));
      const latency = Date.now() - startTime;
      return response.ok && latency < 150; // 延迟小于150ms认为网络良好
    } catch {
      return false;
    }
  };

  // 获取当前应该使用的渲染模式
  const currentMode = renderMode === "auto" ? autoDetectedMode : renderMode;

  // 根据模型路径确定低质量和中等质量模型的路径
  const getModelPathByQuality = (quality: string) => {
    const basePath = modelPath.replace(".glb", "");
    switch (quality) {
      case "low":
        return `${basePath}-low.glb`;
      case "medium":
        return `${basePath}-medium.glb`;
      default:
        return modelPath;
    }
  };

  // 处理模型加载完成事件
  const handleModelLoaded = () => {
    setIsModelLoaded(true);
  };

  // 处理场景就绪事件
  const handleSceneReady = React.useCallback((scene: any, engine: any) => {
    if (onSceneReady) {
      onSceneReady(scene, engine);
    }
  }, [onSceneReady]);

  // 渲染加载状态
  if (currentMode === "loading") {
    return (
      <div className="loading-container">
        <div className="adaptive-spinner"></div>
        <p>{t("loading.detectingDevice", "正在检测设备性能...")}</p>
      </div>
    );
  }

  // 根据渲染模式返回相应的组件
  const renderContent = () => {
    switch (currentMode) {
      case "high":
        return (
          <div style={{ width: "100%", height: "100%" }}>
            <Engine antialias adaptToDeviceRatio canvasId="babylonJS-high">
              <Scene 
                clearColor={new Color4(0.2, 0.2, 0.3, 1)}
                onSceneMount={({ scene }) => {
                  const engine = scene.getEngine();
                  handleSceneReady(scene, engine);
                }}
              >
                {/* 相机 */}
                <arcRotateCamera
                  name="camera"
                  target={Vector3.Zero()}
                  alpha={-Math.PI / 2}
                  beta={Math.PI / 3}
                  radius={8}
                  minZ={0.001}
                  wheelPrecision={50}
                  lowerRadiusLimit={3}
                  upperRadiusLimit={20}
                />

                {/* 光源 */}
                <hemisphericLight
                  key="hemiLight"
                  name="hemiLight"
                  intensity={0.7}
                  direction={new Vector3(0, 1, 0)}
                />
                <directionalLight
                  key="dirLight"
                  name="dirLight"
                  direction={new Vector3(-1, -2, -1)}
                  position={new Vector3(10, 10, 5)}
                  intensity={0.8}
                />

                {/* 模型 */}
                <LowPolyModel
                  rootUrl={modelPath.substring(
                    0,
                    modelPath.lastIndexOf("/") + 1,
                  )}
                  fileName={modelPath.substring(modelPath.lastIndexOf("/") + 1)}
                  onModelLoaded={handleModelLoaded}
                />

                {/* 天空盒 */}
                <box name="skyBox" size={1000} infiniteDistance={true}>
                  <standardMaterial
                    name="skyBoxMaterial"
                    backFaceCulling={false}
                    diffuseColor={Color3.Black()}
                    specularColor={Color3.Black()}
                  />
                </box>
              </Scene>
            </Engine>

            {!isModelLoaded && (
              <div className="loading-overlay">
                <div className="adaptive-spinner"></div>
                <p>{t("loading.model", "加载模型中...")}</p>
              </div>
            )}
          </div>
        );

      case "medium":
        return (
          <div style={{ width: "100%", height: "100%" }}>
            <Engine antialias adaptToDeviceRatio canvasId="babylonJS-medium">
              <Scene 
                clearColor={new Color4(0.2, 0.2, 0.3, 1)}
                onSceneMount={({ scene }) => {
                  const engine = scene.getEngine();
                  handleSceneReady(scene, engine);
                }}
              >
                {/* 相机 */}
                <arcRotateCamera
                  name="camera"
                  target={Vector3.Zero()}
                  alpha={-Math.PI / 2}
                  beta={Math.PI / 3}
                  radius={8}
                  minZ={0.001}
                  wheelPrecision={50}
                  lowerRadiusLimit={3}
                  upperRadiusLimit={20}
                />

                {/* 光源 */}
                <hemisphericLight
                  key="hemiLight2"
                  name="hemiLight"
                  intensity={0.7}
                  direction={new Vector3(0, 1, 0)}
                />

                {/* 模型 */}
                <LowPolyModel
                  rootUrl={getModelPathByQuality("medium").substring(
                    0,
                    getModelPathByQuality("medium").lastIndexOf("/") + 1,
                  )}
                  fileName={getModelPathByQuality("medium").substring(
                    getModelPathByQuality("medium").lastIndexOf("/") + 1,
                  )}
                  onModelLoaded={handleModelLoaded}
                />

                {/* 场景优化已移除 */}
              </Scene>
            </Engine>

            {!isModelLoaded && (
              <div className="loading-overlay">
                <div className="adaptive-spinner"></div>
                <p>{t("loading.model", "加载模型中...")}</p>
              </div>
            )}
          </div>
        );

      case "low":
        return (
          <div style={{ width: "100%", height: "100%" }}>
            <Engine
              antialias={false}
              adaptToDeviceRatio={false}
              canvasId="babylonJS-low"
            >
              <Scene 
                clearColor={new Color4(0.2, 0.2, 0.3, 1)}
                onSceneMount={({ scene }) => {
                  const engine = scene.getEngine();
                  handleSceneReady(scene, engine);
                }}
              >
                {/* 相机 */}
                <arcRotateCamera
                  name="camera"
                  target={Vector3.Zero()}
                  alpha={-Math.PI / 2}
                  beta={Math.PI / 3}
                  radius={8}
                  minZ={0.001}
                  wheelPrecision={50}
                  lowerRadiusLimit={3}
                  upperRadiusLimit={20}
                />

                {/* 光源 */}
                <hemisphericLight
                  key="hemiLight3"
                  name="hemiLight"
                  intensity={0.7}
                  direction={new Vector3(0, 1, 0)}
                />

                {/* 模型 */}
                <LowPolyModel
                  rootUrl={getModelPathByQuality("low").substring(
                    0,
                    getModelPathByQuality("low").lastIndexOf("/") + 1,
                  )}
                  fileName={getModelPathByQuality("low").substring(
                    getModelPathByQuality("low").lastIndexOf("/") + 1,
                  )}
                  onModelLoaded={handleModelLoaded}
                />

                {/* 场景优化已移除 */}
              </Scene>
            </Engine>

            {!isModelLoaded && (
              <div className="loading-overlay">
                <div className="adaptive-spinner"></div>
                <p>{t("loading.model", "加载模型中...")}</p>
              </div>
            )}
          </div>
        );

      case "server":
        return <ServerRenderedView modelPath={modelPath} />;

      case "image":
      default:
        return <ImageViewer basePath="/images/buddhist-temple" />;
    }
  };

  return (
    <div className="adaptive-renderer">
      {renderContent()}

      <div className="quality-controls">
        <select
          value={renderMode}
          onChange={(e) => setRenderMode(e.target.value)}
          className="quality-selector"
        >
          <option value="auto">
            {t("quality.auto", "自动")} ({autoDetectedMode})
          </option>
          <option value="high">
            {t("quality.high", "高质量")} (
            {t("quality.highPerformance", "高性能设备")})
          </option>
          <option value="medium">{t("quality.medium", "中等质量")}</option>
          <option value="low">{t("quality.low", "低质量")}</option>
          <option value="server">{t("quality.server", "服务器渲染")}</option>
          <option value="image">
            {t("quality.image", "图像模式")} (
            {t("quality.lowPerformance", "低性能设备")})
          </option>
        </select>
      </div>
    </div>
  );
});

AdaptiveRenderer.displayName = 'AdaptiveRenderer';

export default AdaptiveRenderer;
