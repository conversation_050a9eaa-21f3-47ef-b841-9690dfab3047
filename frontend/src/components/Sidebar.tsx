import React from "react";
import { Link, useLocation } from "react-router-dom";

interface NavItem {
  name: string;
  path: string;
  icon: string; // Font Awesome class
}

const navItems: NavItem[] = [
  { name: "仪表盘", path: "/dashboard", icon: "fas fa-tachometer-alt" },
  { name: "我的纪念馆", path: "/my-memorials", icon: "fas fa-landmark" },
  { name: "家族管理", path: "/family-management", icon: "fas fa-users" },
  { name: "AI 服务", path: "/ai-services", icon: "fas fa-magic" },
  { name: "在线商店", path: "/store", icon: "fas fa-store" },
  { name: "消息通知", path: "/notifications", icon: "fas fa-bell" },
  { name: "账户设置", path: "/settings", icon: "fas fa-cog" },
];

const Sidebar: React.FC = () => {
  const location = useLocation();

  return (
    <aside className="w-64 bg-gray-900 text-white p-4 space-y-2 flex flex-col shadow-lg h-screen sticky top-0">
      <div className="text-center py-4 mb-4 border-b border-amber-500 border-opacity-50">
        <Link to="/dashboard" className="flex items-center justify-center">
          <i className="fas fa-leaf text-amber-500 text-3xl"></i>
          <span className="ml-2 text-2xl font-semibold text-white">归处</span>
        </Link>
      </div>
      <nav className="flex-grow">
        <ul className="space-y-1">
          {navItems.map((item) => (
            <li key={item.name}>
              <Link
                to={item.path}
                className={`flex items-center px-3 py-2.5 rounded-lg transition-colors duration-150 ease-in-out 
                            ${
                              location.pathname.startsWith(item.path)
                                ? "bg-amber-500 text-gray-900 shadow-md"
                                : "text-gray-300 hover:bg-amber-500 hover:text-gray-900 hover:bg-opacity-80"
                            }`}
              >
                <i className={`${item.icon} w-5 h-5 mr-3`}></i>
                <span>{item.name}</span>
              </Link>
            </li>
          ))}
        </ul>
      </nav>
      <div className="mt-auto pt-4 border-t border-amber-500 border-opacity-50">
        <Link
          to="/logout" // Placeholder for logout functionality
          className="flex items-center px-3 py-2.5 rounded-lg text-gray-300 hover:bg-red-600 hover:text-white transition-colors duration-150 ease-in-out"
        >
          <i className="fas fa-sign-out-alt w-5 h-5 mr-3"></i>
          <span>退出登录</span>
        </Link>
      </div>
    </aside>
  );
};

export default Sidebar;
