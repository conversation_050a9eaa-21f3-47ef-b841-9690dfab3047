import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import ContentFilter from '../utils/ContentFilter';

// 留言接口
interface Message {
  id: string;
  memorial_space_id: string;
  author_id: string | null;
  content: string;
  is_anonymous: boolean;
  created_at: string;
  updated_at: string;
  author_name?: string;
  author_avatar?: string;
  is_moderated?: boolean;
  moderation_status?: 'pending' | 'approved' | 'rejected';
  likes_count?: number;
  user_liked?: boolean;
}

// 留言表单数据
interface MessageFormData {
  content: string;
  is_anonymous: boolean;
  author_name?: string;
}

// 留言系统组件属性
interface MessageSystemProps {
  memorialSpaceId: string;
  showMessageBoard?: boolean;
  onToggleMessages?: () => void;
  maxMessages?: number;
  allowAnonymous?: boolean;
  enableModeration?: boolean;
  enableLikes?: boolean;
}

// 留言系统组件
const MessageSystem: React.FC<MessageSystemProps> = ({
  memorialSpaceId,
  showMessageBoard = false,
  onToggleMessages,
  maxMessages = 50,
  allowAnonymous = true,
  enableModeration = true,
  enableLikes = true
}) => {
  const { t } = useTranslation();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState<MessageFormData>({
    content: '',
    is_anonymous: false,
    author_name: ''
  });
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [filteredMessages, setFilteredMessages] = useState<Message[]>([]);
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'likes'>('newest');
  const [showModerationInfo, setShowModerationInfo] = useState(false);

  // 获取留言列表
  const fetchMessages = useCallback(async () => {
    if (!memorialSpaceId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/v1/messages/space/${memorialSpaceId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken') || ''}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setMessages(data);
      } else {
        throw new Error('获取留言失败');
      }
    } catch (error) {
      console.error('获取留言失败:', error);
      setError('无法加载留言，请稍后重试');
    } finally {
      setLoading(false);
    }
  }, [memorialSpaceId]);

  // 提交留言
  const submitMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.content.trim()) {
      setError('请输入留言内容');
      return;
    }

    if (formData.is_anonymous && !formData.author_name?.trim()) {
      setError('匿名留言需要提供昵称');
      return;
    }

    // 内容过滤检查
    const contentCheck = ContentFilter.checkSensitiveContent(formData.content);
    if (!contentCheck.isSafe) {
      setError(contentCheck.reason || '内容包含不当信息，请修改后重试');
      return;
    }

    // 昵称检查（如果是匿名）
    if (formData.is_anonymous && formData.author_name) {
      const nicknameCheck = ContentFilter.checkNickname(formData.author_name);
      if (!nicknameCheck.isValid) {
        setError(`昵称${nicknameCheck.reason || '不符合规范'}`);
        return;
      }
    }

    setSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/v1/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token || ''}`,
        },
        body: JSON.stringify({
          memorial_space_id: memorialSpaceId,
          content: contentCheck.filteredContent.trim(),
          is_anonymous: formData.is_anonymous,
          author_name: formData.is_anonymous ? formData.author_name?.trim() : undefined,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess('留言提交成功' + (enableModeration ? '，等待审核' : ''));
        setFormData({ content: '', is_anonymous: false, author_name: '' });
        
        // 如果不需要审核，立即刷新留言列表
        if (!enableModeration) {
          await fetchMessages();
        }
      } else {
        throw new Error(data.detail || '提交留言失败');
      }
    } catch (error) {
      console.error('提交留言失败:', error);
      setError(error instanceof Error ? error.message : '提交失败，请稍后重试');
    } finally {
      setSubmitting(false);
    }
  };

  // 点赞留言
  const likeMessage = async (messageId: string) => {
    if (!enableLikes) return;

    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/v1/messages/${messageId}/like`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token || ''}`,
        },
      });

      if (response.ok) {
        // 更新本地状态
        setMessages(prev => prev.map(msg => {
          if (msg.id === messageId) {
            return {
              ...msg,
              likes_count: (msg.likes_count || 0) + (msg.user_liked ? -1 : 1),
              user_liked: !msg.user_liked
            };
          }
          return msg;
        }));
      }
    } catch (error) {
      console.error('点赞失败:', error);
    }
  };

  // 删除留言（管理员功能）
  const deleteMessage = async (messageId: string) => {
    if (!confirm('确定要删除这条留言吗？')) return;

    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/v1/messages/${messageId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token || ''}`,
        },
      });

      if (response.ok) {
        setMessages(prev => prev.filter(msg => msg.id !== messageId));
        setSuccess('留言已删除');
      } else {
        throw new Error('删除失败');
      }
    } catch (error) {
      console.error('删除留言失败:', error);
      setError('删除失败，请稍后重试');
    }
  };

  // 排序和过滤留言
  useEffect(() => {
    let filtered = [...messages];
    
    // 根据排序方式排序
    switch (sortBy) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
        break;
      case 'likes':
        filtered.sort((a, b) => (b.likes_count || 0) - (a.likes_count || 0));
        break;
    }

    // 限制显示数量
    if (maxMessages > 0) {
      filtered = filtered.slice(0, maxMessages);
    }

    setFilteredMessages(filtered);
  }, [messages, sortBy, maxMessages]);

  // 初始加载
  useEffect(() => {
    if (showMessageBoard && memorialSpaceId) {
      fetchMessages();
    }
  }, [showMessageBoard, memorialSpaceId, fetchMessages]);

  // 清除提示消息
  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(() => {
        setError(null);
        setSuccess(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, success]);

  if (!showMessageBoard) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={(e) => {
          if (e.target === e.currentTarget && onToggleMessages) {
            onToggleMessages();
          }
        }}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-lg shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col"
        >
          {/* 头部 */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-4 flex justify-between items-center">
            <div>
              <h2 className="text-xl font-bold">{t('messages.title', '留言板')}</h2>
              <p className="text-sm opacity-90">
                {messages.length} 条留言
                {enableModeration && (
                  <button
                    onClick={() => setShowModerationInfo(!showModerationInfo)}
                    className="ml-2 text-xs underline hover:no-underline"
                  >
                    审核说明
                  </button>
                )}
              </p>
            </div>
            <button
              onClick={onToggleMessages}
              className="text-white hover:bg-white hover:bg-opacity-20 p-2 rounded-full transition-colors"
            >
              ✕
            </button>
          </div>

          {/* 审核信息 */}
          {enableModeration && showModerationInfo && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="bg-blue-50 border-b p-3 text-sm text-blue-800"
            >
              <p className="font-medium mb-1">内容审核机制：</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li>所有留言需要通过审核后才会显示</li>
                <li>系统会自动过滤不当内容</li>
                <li>管理员会及时处理审核请求</li>
                <li>请遵守社区规范，文明留言</li>
              </ul>
            </motion.div>
          )}

          {/* 留言表单 */}
          <div className="p-4 border-b">
            <form onSubmit={submitMessage} className="space-y-3">
              <div>
                <textarea
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  placeholder={t('messages.placeholder', '写下您的留言...')}
                  className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  maxLength={500}
                  disabled={submitting}
                />
                <div className="text-right text-xs text-gray-500 mt-1">
                  {formData.content.length}/500
                </div>
              </div>

              {allowAnonymous && (
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={formData.is_anonymous}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        is_anonymous: e.target.checked,
                        author_name: e.target.checked ? prev.author_name : ''
                      }))}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm">匿名留言</span>
                  </label>

                  {formData.is_anonymous && (
                    <input
                      type="text"
                      value={formData.author_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, author_name: e.target.value }))}
                      placeholder="输入昵称"
                      className="flex-1 px-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      maxLength={20}
                    />
                  )}
                </div>
              )}

              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-red-600 text-sm bg-red-50 p-2 rounded"
                >
                  {error}
                </motion.div>
              )}

              {success && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-green-600 text-sm bg-green-50 p-2 rounded"
                >
                  {success}
                </motion.div>
              )}

              <button
                type="submit"
                disabled={submitting || !formData.content.trim()}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white py-2 rounded-lg transition-colors font-medium"
              >
                {submitting ? '提交中...' : '发表留言'}
              </button>
            </form>
          </div>

          {/* 留言列表 */}
          <div className="flex-1 overflow-hidden flex flex-col">
            {/* 排序控制 */}
            <div className="p-3 border-b bg-gray-50 flex justify-between items-center">
              <span className="text-sm text-gray-600">排序方式：</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="newest">最新</option>
                <option value="oldest">最早</option>
                {enableLikes && <option value="likes">最多赞</option>}
              </select>
            </div>

            <div className="flex-1 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : filteredMessages.length === 0 ? (
                <div className="flex items-center justify-center h-32 text-gray-500">
                  暂无留言，快来留下第一条留言吧！
                </div>
              ) : (
                <div className="space-y-3 p-4">
                  {filteredMessages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="bg-gray-50 rounded-lg p-3 hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            {message.is_anonymous 
                              ? (message.author_name?.[0] || '匿')
                              : (message.author_name?.[0] || 'U')
                            }
                          </div>
                          <div>
                            <div className="font-medium text-sm">
                              {message.is_anonymous 
                                ? (message.author_name || '匿名用户')
                                : (message.author_name || '用户')
                              }
                              {message.is_anonymous && (
                                <span className="ml-1 text-xs text-gray-500">(匿名)</span>
                              )}
                            </div>
                            <div className="text-xs text-gray-500">
                              {new Date(message.created_at).toLocaleString()}
                            </div>
                          </div>
                        </div>
                        
                        {enableModeration && (
                          <button
                            onClick={() => deleteMessage(message.id)}
                            className="text-red-500 hover:text-red-700 text-xs"
                          >
                            删除
                          </button>
                        )}
                      </div>

                      <p className="text-gray-800 text-sm mb-2 leading-relaxed">
                        {message.content}
                      </p>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {message.moderation_status && (
                            <span className={`text-xs px-2 py-1 rounded ${
                              message.moderation_status === 'approved' 
                                ? 'bg-green-100 text-green-700'
                                : message.moderation_status === 'pending'
                                ? 'bg-yellow-100 text-yellow-700'
                                : 'bg-red-100 text-red-700'
                            }`}>
                              {message.moderation_status === 'approved' && '已审核'}
                              {message.moderation_status === 'pending' && '审核中'}
                              {message.moderation_status === 'rejected' && '已拒绝'}
                            </span>
                          )}
                        </div>

                        {enableLikes && (
                          <button
                            onClick={() => likeMessage(message.id)}
                            className={`flex items-center space-x-1 text-xs transition-colors ${
                              message.user_liked 
                                ? 'text-red-500 hover:text-red-600' 
                                : 'text-gray-500 hover:text-red-500'
                            }`}
                          >
                            <span>{message.user_liked ? '❤️' : '🤍'}</span>
                            <span>{message.likes_count || 0}</span>
                          </button>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default MessageSystem;
export type { Message, MessageFormData, MessageSystemProps };