import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
// TODO: 更新类名使用方式
// 从: className="button primary"
// 到: className={clsx(styles.button, styles.primary)}

interface UserData {
  username?: string;
  email?: string;
  role?: string;
  id?: number;
}

interface LoginButtonProps {
  onLoginSuccess?: (userData: UserData) => void;
}

const LoginButton: React.FC<LoginButtonProps> = ({ onLoginSuccess }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(!!localStorage.getItem("token"));
  const [userData, setUserData] = useState<UserData>(
    JSON.parse(localStorage.getItem("user") || "{}"),
  );

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setLoading(true);

    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // 保存令牌和用户信息
        localStorage.setItem("token", data.access_token);
        localStorage.setItem("refreshToken", data.refresh_token);
        localStorage.setItem("user", JSON.stringify(data.user));

        setIsLoggedIn(true);
        setUserData(data.user);
        setShowModal(false);

        // 调用登录成功回调
        if (onLoginSuccess) {
          onLoginSuccess(data.user);
        }

        // 如果是管理员，导航到管理面板
        if (data.user.role === "admin") {
          navigate("/admin");
        }
      } else {
        setError(data.message || "登录失败，请检查您的凭据");
      }
    } catch (err) {
      setError("登录过程中出现错误，请稍后再试");
      console.error("Login error:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    // 清除本地存储的令牌和用户信息
    localStorage.removeItem("token");
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("user");
    setIsLoggedIn(false);
    setUserData({});

    // 如果在管理面板，返回首页
    if (window.location.pathname.startsWith("/admin")) {
      navigate("/");
    }
  };

  return (
    <div className="login-container">
      {isLoggedIn ? (
        <div className="user-menu">
          <span className="username">{userData.username || "用户"}</span>
          <div className="dropdown-menu">
            {userData.role === "admin" && (
              <button
                onClick={() => navigate("/admin")}
                className="dropdown-item"
              >
                {t("admin.dashboard")}
              </button>
            )}
            <button
              onClick={() => navigate("/profile")}
              className="dropdown-item"
            >
              {t("profile")}
            </button>
            <button onClick={handleLogout} className="dropdown-item">
              {t("logout")}
            </button>
          </div>
        </div>
      ) : (
        <button onClick={() => setShowModal(true)} className="login-button">
          {t("login")}
        </button>
      )}

      {showModal && (
        <div className="modal-overlay">
          <div className="login-modal">
            <button
              className="close-button"
              onClick={() => setShowModal(false)}
            >
              &times;
            </button>
            <h2>{t("login")}</h2>
            {error && <div className="error-message">{error}</div>}
            <form onSubmit={handleLogin}>
              <div className="form-group">
                <label htmlFor="email">{t("email")}</label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="password">{t("password")}</label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
              <button
                type="submit"
                className="submit-button"
                disabled={loading}
              >
                {loading ? t("loggingIn") : t("login")}
              </button>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default LoginButton;
