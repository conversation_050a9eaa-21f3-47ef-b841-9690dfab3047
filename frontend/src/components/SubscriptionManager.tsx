import React, { useState, useEffect, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import AuthContext from '../contexts/AuthContext';
import NotificationService from '../utils/NotificationService';
import {
  SubscriptionPlan,
  UserSubscription,
  PlanUsage,
  BillingCycle,
  SubscriptionInvoice
} from '../types/subscription';

interface SubscriptionManagerProps {
  onClose?: () => void;
}

const SubscriptionManager: React.FC<SubscriptionManagerProps> = ({ onClose }) => {
  const authContext = useContext(AuthContext);
  const [activeTab, setActiveTab] = useState<'plans' | 'current' | 'usage' | 'billing' | 'invoices'>('plans');
  const [loading, setLoading] = useState(true);
  
  // 数据状态
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscription | null>(null);
  const [usage, setUsage] = useState<PlanUsage | null>(null);
  const [invoices, setInvoices] = useState<SubscriptionInvoice[]>([]);
  
  // UI状态
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [selectedBillingCycle, setSelectedBillingCycle] = useState<BillingCycle>('monthly');
  const [showPlanModal, setShowPlanModal] = useState(false);
  const [couponCode, setCouponCode] = useState('');

  if (!authContext) {
    return (
      <div className="text-center p-8">
        <p className="text-gray-600">请先登录以管理订阅</p>
      </div>
    );
  }

  // 加载数据
  useEffect(() => {
    loadSubscriptionData();
  }, []);

  const loadSubscriptionData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 模拟订阅计划数据
      const mockPlans: SubscriptionPlan[] = [
        {
          id: 'free',
          name: 'free',
          display_name: '基础版',
          description: '适合个人用户的基础功能',
          plan_type: 'free',
          monthly_price: 0,
          yearly_price: 0,
          currency: 'CNY',
          features: [
            { id: 'memorial_spaces', name: '纪念空间', description: '最多2个纪念空间', category: 'basic', is_included: true, limit: 2, unit: '个' },
            { id: 'storage', name: '存储空间', description: '500MB存储空间', category: 'basic', is_included: true, limit: 500, unit: 'MB' },
            { id: 'basic_scenes', name: '基础3D场景', description: '基础3D场景模板', category: 'basic', is_included: true },
            { id: 'community_support', name: '社区支持', description: '社区论坛支持', category: 'basic', is_included: true }
          ],
          limits: {
            memorial_spaces: 2,
            storage_gb: 0.5,
            ai_minutes_per_month: 0,
            custom_domains: 0,
            api_calls_per_month: 100,
            priority_support: false,
            advanced_analytics: false,
            white_label: false,
            custom_branding: false,
            backup_retention_days: 7
          },
          is_popular: false,
          is_available: true,
          sort_order: 1,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-06-01T00:00:00Z'
        },
        {
          id: 'premium',
          name: 'premium',
          display_name: '高级版',
          description: '适合个人用户的增强功能',
          plan_type: 'premium',
          monthly_price: 1999,
          yearly_price: 19990,
          currency: 'CNY',
          discount_percentage: 17,
          features: [
            { id: 'memorial_spaces', name: '纪念空间', description: '最多10个纪念空间', category: 'basic', is_included: true, limit: 10, unit: '个' },
            { id: 'storage', name: '存储空间', description: '5GB存储空间', category: 'basic', is_included: true, limit: 5, unit: 'GB' },
            { id: 'premium_scenes', name: '高级3D场景', description: '高级3D场景与特效', category: 'advanced', is_included: true },
            { id: 'ai_voice_clone', name: 'AI声音克隆', description: '每月10分钟AI声音克隆', category: 'premium', is_included: true, limit: 10, unit: '分钟/月' },
            { id: 'hd_photo_repair', name: '高清照片修复', description: '高清照片修复服务', category: 'premium', is_included: true },
            { id: 'priority_support', name: '优先客服', description: '优先客服支持', category: 'advanced', is_included: true }
          ],
          limits: {
            memorial_spaces: 10,
            storage_gb: 5,
            ai_minutes_per_month: 10,
            custom_domains: 0,
            api_calls_per_month: 1000,
            priority_support: true,
            advanced_analytics: false,
            white_label: false,
            custom_branding: false,
            backup_retention_days: 30
          },
          is_popular: true,
          is_available: true,
          trial_days: 7,
          sort_order: 2,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-06-01T00:00:00Z'
        },
        {
          id: 'family',
          name: 'family',
          display_name: '家族版',
          description: '适合家族管理的完整功能',
          plan_type: 'family',
          monthly_price: 4999,
          yearly_price: 49990,
          currency: 'CNY',
          discount_percentage: 17,
          features: [
            { id: 'memorial_spaces', name: '纪念空间', description: '无限纪念空间', category: 'basic', is_included: true, limit: -1, unit: '个' },
            { id: 'storage', name: '存储空间', description: '50GB存储空间', category: 'basic', is_included: true, limit: 50, unit: 'GB' },
            { id: 'family_management', name: '家族群组管理', description: '创建和管理家族群组', category: 'premium', is_included: true },
            { id: 'family_tree', name: '族谱编辑器', description: '可视化族谱编辑工具', category: 'premium', is_included: true },
            { id: 'ai_content_gen', name: 'AI纪念文案', description: 'AI生成纪念文案', category: 'premium', is_included: true },
            { id: 'data_backup', name: '数据备份', description: '自动数据备份与同步', category: 'advanced', is_included: true }
          ],
          limits: {
            memorial_spaces: -1,
            storage_gb: 50,
            ai_minutes_per_month: 60,
            custom_domains: 1,
            api_calls_per_month: 10000,
            priority_support: true,
            advanced_analytics: true,
            white_label: false,
            custom_branding: true,
            backup_retention_days: 90
          },
          is_popular: false,
          is_available: true,
          trial_days: 14,
          sort_order: 3,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-06-01T00:00:00Z'
        },
        {
          id: 'enterprise',
          name: 'enterprise',
          display_name: '企业版',
          description: '企业级功能和定制服务',
          plan_type: 'enterprise',
          monthly_price: 19999,
          yearly_price: 199990,
          currency: 'CNY',
          discount_percentage: 17,
          features: [
            { id: 'unlimited_everything', name: '无限制访问', description: '所有功能无限制使用', category: 'enterprise', is_included: true },
            { id: 'private_deployment', name: '私有化部署', description: '支持私有化部署选项', category: 'enterprise', is_included: true },
            { id: 'custom_branding', name: '品牌定制', description: '完全自定义品牌服务', category: 'enterprise', is_included: true },
            { id: 'api_access', name: 'API接口开放', description: '完整API接口访问', category: 'enterprise', is_included: true },
            { id: 'dedicated_support', name: '专属技术支持', description: '7x24专属技术支持', category: 'enterprise', is_included: true },
            { id: 'data_security', name: '数据安全保障', description: '企业级数据安全保障', category: 'enterprise', is_included: true }
          ],
          limits: {
            memorial_spaces: -1,
            storage_gb: -1,
            ai_minutes_per_month: -1,
            custom_domains: -1,
            api_calls_per_month: -1,
            priority_support: true,
            advanced_analytics: true,
            white_label: true,
            custom_branding: true,
            backup_retention_days: 365
          },
          is_popular: false,
          is_available: true,
          trial_days: 30,
          sort_order: 4,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-06-01T00:00:00Z'
        }
      ];
      
      setPlans(mockPlans);

      // 模拟当前订阅（假设用户是免费版）
      setCurrentSubscription({
        id: 'sub_001',
        user_id: authContext.user?.id || '',
        plan_id: 'free',
        plan: mockPlans[0],
        status: 'active',
        billing_cycle: 'monthly',
        start_date: '2024-01-01T00:00:00Z',
        end_date: '2024-12-31T23:59:59Z',
        auto_renew: false,
        current_usage: {
          memorial_spaces_used: 1,
          storage_used_gb: 0.1,
          ai_minutes_used: 0,
          api_calls_used: 45,
          last_reset_date: '2024-06-01T00:00:00Z',
          period_start: '2024-06-01T00:00:00Z',
          period_end: '2024-06-30T23:59:59Z'
        },
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-06-01T00:00:00Z'
      });

      // 模拟使用情况
      setUsage({
        memorial_spaces_used: 1,
        storage_used_gb: 0.1,
        ai_minutes_used: 0,
        api_calls_used: 45,
        last_reset_date: '2024-06-01T00:00:00Z',
        period_start: '2024-06-01T00:00:00Z',
        period_end: '2024-06-30T23:59:59Z'
      });

      // 模拟发票数据（免费版用户通常没有发票）
      setInvoices([]);

    } catch (error) {
      console.error('加载订阅数据失败:', error);
      NotificationService.error('加载失败', '无法加载订阅数据，请刷新重试');
    } finally {
      setLoading(false);
    }
  };

  // 计算使用百分比
  const getUsagePercentage = (used: number, limit: number): number => {
    if (limit === -1) return 0; // 无限制
    if (limit === 0) return 100; // 不可用
    return Math.min((used / limit) * 100, 100);
  };

  // 格式化价格
  const formatPrice = (price: number, _currency: string = 'CNY'): string => {
    if (price === 0) return '免费';
    const formatted = (price / 100).toFixed(2);
    return `¥${formatted}`;
  };

  // 渲染订阅计划卡片
  const renderPlanCard = (plan: SubscriptionPlan) => {
    const isCurrentPlan = currentSubscription?.plan_id === plan.id;
    const monthlyPrice = formatPrice(plan.monthly_price);
    const yearlyMonthlyPrice = plan.yearly_price > 0 ? formatPrice(plan.yearly_price / 12) : '免费';

    return (
      <motion.div
        key={plan.id}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`relative bg-white rounded-lg shadow-lg p-6 ${
          plan.is_popular ? 'ring-2 ring-blue-500' : 'border border-gray-200'
        } ${isCurrentPlan ? 'ring-2 ring-green-500' : ''}`}
      >
        {plan.is_popular && (
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <span className="bg-blue-500 text-white px-4 py-1 rounded-full text-sm font-medium">
              最受欢迎
            </span>
          </div>
        )}
        
        {isCurrentPlan && (
          <div className="absolute -top-3 right-4">
            <span className="bg-green-500 text-white px-4 py-1 rounded-full text-sm font-medium">
              当前计划
            </span>
          </div>
        )}

        <div className="text-center mb-6">
          <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.display_name}</h3>
          <p className="text-gray-600 mb-4">{plan.description}</p>
          
          <div className="space-y-2">
            <div className="text-center">
              <span className="text-4xl font-bold text-gray-900">{monthlyPrice}</span>
              {plan.monthly_price > 0 && <span className="text-gray-500">/月</span>}
            </div>
            
            {plan.yearly_price > 0 && plan.discount_percentage && (
              <div className="text-center">
                <span className="text-sm text-gray-500">年付: </span>
                <span className="text-lg font-semibold text-green-600">{yearlyMonthlyPrice}/月</span>
                <span className="ml-2 bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                  省{plan.discount_percentage}%
                </span>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-3 mb-6">
          {plan.features.map(feature => (
            <div key={feature.id} className="flex items-start space-x-3">
              <i className="fas fa-check text-green-500 mt-0.5"></i>
              <div>
                <span className="text-gray-900 font-medium">{feature.name}</span>
                {feature.limit && feature.unit && (
                  <span className="ml-2 text-sm text-gray-500">
                    ({feature.limit === -1 ? '无限制' : `${feature.limit}${feature.unit}`})
                  </span>
                )}
                <p className="text-sm text-gray-600">{feature.description}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center">
          {isCurrentPlan ? (
            <button
              disabled
              className="w-full bg-gray-100 text-gray-500 py-3 rounded-lg font-medium cursor-not-allowed"
            >
              当前计划
            </button>
          ) : (
            <button
              onClick={() => {
                setSelectedPlan(plan);
                setShowPlanModal(true);
              }}
              className={`w-full py-3 rounded-lg font-medium transition-colors ${
                plan.is_popular
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-900 text-white hover:bg-gray-800'
              }`}
            >
              {plan.trial_days ? `开始${plan.trial_days}天免费试用` : '选择此计划'}
            </button>
          )}
        </div>

        {plan.trial_days && !isCurrentPlan && (
          <p className="text-center text-sm text-gray-500 mt-2">
            免费试用{plan.trial_days}天，无需信用卡
          </p>
        )}
      </motion.div>
    );
  };

  // 渲染使用情况页面
  const renderUsagePage = () => {
    if (!currentSubscription || !usage) {
      return (
        <div className="text-center py-12">
          <p className="text-gray-600">无法加载使用情况数据</p>
        </div>
      );
    }

    const { limits } = currentSubscription.plan;

    return (
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-bold text-gray-900 mb-6">当前使用情况</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 纪念空间使用情况 */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">纪念空间</span>
                <span className="text-sm text-gray-500">
                  {usage.memorial_spaces_used}/{limits.memorial_spaces === -1 ? '无限制' : limits.memorial_spaces}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${getUsagePercentage(usage.memorial_spaces_used, limits.memorial_spaces)}%` }}
                ></div>
              </div>
            </div>

            {/* 存储空间使用情况 */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">存储空间</span>
                <span className="text-sm text-gray-500">
                  {usage.storage_used_gb.toFixed(2)}GB/{limits.storage_gb === -1 ? '无限制' : `${limits.storage_gb}GB`}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${getUsagePercentage(usage.storage_used_gb, limits.storage_gb)}%` }}
                ></div>
              </div>
            </div>

            {/* AI服务使用情况 */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">AI服务 (本月)</span>
                <span className="text-sm text-gray-500">
                  {usage.ai_minutes_used}/{limits.ai_minutes_per_month === -1 ? '无限制' : `${limits.ai_minutes_per_month}分钟`}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${getUsagePercentage(usage.ai_minutes_used, limits.ai_minutes_per_month)}%` }}
                ></div>
              </div>
            </div>

            {/* API调用使用情况 */}
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700">API调用 (本月)</span>
                <span className="text-sm text-gray-500">
                  {usage.api_calls_used}/{limits.api_calls_per_month === -1 ? '无限制' : limits.api_calls_per_month}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-orange-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${getUsagePercentage(usage.api_calls_used, limits.api_calls_per_month)}%` }}
                ></div>
              </div>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center space-x-2 text-blue-800">
              <i className="fas fa-info-circle"></i>
              <span className="font-medium">计费周期信息</span>
            </div>
            <p className="text-sm text-blue-700 mt-2">
              当前计费周期: {new Date(usage.period_start).toLocaleDateString('zh-CN')} - {new Date(usage.period_end).toLocaleDateString('zh-CN')}
            </p>
            <p className="text-sm text-blue-700">
              下次重置时间: {new Date(usage.period_end).toLocaleDateString('zh-CN')}
            </p>
          </div>
        </div>

        {/* 功能限制提醒 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h4 className="font-medium text-gray-900 mb-4">当前计划限制</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex items-center space-x-3">
              <i className={`fas ${limits.priority_support ? 'fa-check text-green-500' : 'fa-times text-red-500'}`}></i>
              <span className="text-sm text-gray-700">优先客服支持</span>
            </div>
            <div className="flex items-center space-x-3">
              <i className={`fas ${limits.advanced_analytics ? 'fa-check text-green-500' : 'fa-times text-red-500'}`}></i>
              <span className="text-sm text-gray-700">高级数据分析</span>
            </div>
            <div className="flex items-center space-x-3">
              <i className={`fas ${limits.custom_branding ? 'fa-check text-green-500' : 'fa-times text-red-500'}`}></i>
              <span className="text-sm text-gray-700">自定义品牌</span>
            </div>
            <div className="flex items-center space-x-3">
              <i className={`fas ${limits.white_label ? 'fa-check text-green-500' : 'fa-times text-red-500'}`}></i>
              <span className="text-sm text-gray-700">白标服务</span>
            </div>
            <div className="flex items-center space-x-3">
              <i className="fas fa-clock text-blue-500"></i>
              <span className="text-sm text-gray-700">备份保留 {limits.backup_retention_days} 天</span>
            </div>
            <div className="flex items-center space-x-3">
              <i className="fas fa-globe text-blue-500"></i>
              <span className="text-sm text-gray-700">
                自定义域名 {limits.custom_domains === -1 ? '无限制' : limits.custom_domains === 0 ? '不支持' : `${limits.custom_domains}个`}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* 头部 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <i className="fas fa-crown text-amber-600 text-2xl"></i>
              <h1 className="text-xl font-bold text-gray-900">订阅管理</h1>
            </div>
            
            {onClose && (
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <i className="fas fa-times text-xl"></i>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 当前订阅概览 */}
      {currentSubscription && (
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold mb-2">当前订阅: {currentSubscription.plan.display_name}</h2>
                <p className="text-blue-100">
                  状态: <span className="capitalize">{currentSubscription.status === 'active' ? '活跃' : '已取消'}</span>
                  {currentSubscription.end_date && (
                    <span className="ml-4">
                      到期时间: {new Date(currentSubscription.end_date).toLocaleDateString('zh-CN')}
                    </span>
                  )}
                </p>
              </div>
              
              <div className="text-right">
                <div className="text-3xl font-bold">
                  {formatPrice(currentSubscription.plan.monthly_price)}
                </div>
                {currentSubscription.plan.monthly_price > 0 && (
                  <div className="text-blue-100">每月</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 标签导航 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {[
              { key: 'plans', label: '订阅计划', icon: 'fas fa-list' },
              { key: 'current', label: '当前订阅', icon: 'fas fa-crown' },
              { key: 'usage', label: '使用情况', icon: 'fas fa-chart-bar' },
              { key: 'billing', label: '计费设置', icon: 'fas fa-credit-card' },
              { key: 'invoices', label: '账单历史', icon: 'fas fa-receipt' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <i className={tab.icon}></i>
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {activeTab === 'plans' && (
            <div className="space-y-8">
              <div className="text-center">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">选择适合您的订阅计划</h2>
                <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                  无论您是个人用户还是家族群体，我们都有适合您需求的计划。
                  所有付费计划都提供免费试用期，让您无风险体验高级功能。
                </p>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
                {plans.map(renderPlanCard)}
              </div>
            </div>
          )}
          
          {activeTab === 'current' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">当前订阅详情</h3>
              {currentSubscription ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">订阅信息</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">计划名称:</span>
                          <span className="font-medium">{currentSubscription.plan.display_name}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">计费周期:</span>
                          <span className="font-medium">
                            {currentSubscription.billing_cycle === 'monthly' ? '按月计费' : '按年计费'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">状态:</span>
                          <span className={`font-medium ${
                            currentSubscription.status === 'active' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {currentSubscription.status === 'active' ? '活跃' : '已取消'}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">自动续费:</span>
                          <span className="font-medium">
                            {currentSubscription.auto_renew ? '开启' : '关闭'}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">重要日期</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">开始日期:</span>
                          <span className="font-medium">
                            {new Date(currentSubscription.start_date).toLocaleDateString('zh-CN')}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">到期日期:</span>
                          <span className="font-medium">
                            {new Date(currentSubscription.end_date).toLocaleDateString('zh-CN')}
                          </span>
                        </div>
                        {currentSubscription.next_billing_date && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">下次扣费:</span>
                            <span className="font-medium">
                              {new Date(currentSubscription.next_billing_date).toLocaleDateString('zh-CN')}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t border-gray-200">
                    <div className="flex space-x-4">
                      <button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                        升级计划
                      </button>
                      <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors">
                        更改计费周期
                      </button>
                      <button className="bg-red-100 text-red-700 px-4 py-2 rounded-md hover:bg-red-200 transition-colors">
                        取消订阅
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-gray-600">暂无有效订阅</p>
              )}
            </div>
          )}
          
          {activeTab === 'usage' && renderUsagePage()}
          
          {activeTab === 'billing' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">计费设置</h3>
              <p className="text-gray-600">计费设置功能开发中...</p>
            </div>
          )}
          
          {activeTab === 'invoices' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">账单历史</h3>
              {invoices.length === 0 ? (
                <div className="text-center py-12">
                  <i className="fas fa-receipt text-gray-400 text-6xl mb-4"></i>
                  <p className="text-gray-600 text-lg mb-4">暂无账单记录</p>
                  <p className="text-sm text-gray-500">
                    您的账单记录将在第一次付费后显示在这里
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {invoices.map(invoice => (
                    <div key={invoice.id} className="border border-gray-200 rounded-lg p-4">
                      {/* 发票内容 */}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </motion.div>
      </div>

      {/* 计划选择模态框 */}
      <AnimatePresence>
        {showPlanModal && selectedPlan && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setShowPlanModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-lg shadow-xl max-w-md w-full p-6"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center mb-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  订阅 {selectedPlan.display_name}
                </h3>
                <p className="text-gray-600">{selectedPlan.description}</p>
              </div>

              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    计费周期
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={() => setSelectedBillingCycle('monthly')}
                      className={`p-3 rounded-lg border text-center ${
                        selectedBillingCycle === 'monthly'
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <div className="font-medium">按月</div>
                      <div className="text-sm">{formatPrice(selectedPlan.monthly_price)}/月</div>
                    </button>
                    <button
                      onClick={() => setSelectedBillingCycle('yearly')}
                      className={`p-3 rounded-lg border text-center relative ${
                        selectedBillingCycle === 'yearly'
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <div className="font-medium">按年</div>
                      <div className="text-sm">{formatPrice(selectedPlan.yearly_price / 12)}/月</div>
                      {selectedPlan.discount_percentage && (
                        <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                          省{selectedPlan.discount_percentage}%
                        </div>
                      )}
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    优惠券代码 (可选)
                  </label>
                  <input
                    type="text"
                    value={couponCode}
                    onChange={(e) => setCouponCode(e.target.value)}
                    placeholder="输入优惠券代码"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowPlanModal(false)}
                  className="flex-1 bg-gray-100 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={() => {
                    // 处理订阅逻辑
                    setShowPlanModal(false);
                    NotificationService.success('订阅成功', `已订阅${selectedPlan.display_name}计划`);
                  }}
                  className="flex-1 bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                >
                  {selectedPlan.trial_days ? `开始${selectedPlan.trial_days}天试用` : '立即订阅'}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SubscriptionManager;