// 纪念空间3D场景组件
import React, { useRef, useState, useCallback } from "react";
import { Engine, Scene } from "react-babylonjs";
import {
  Vector3,
  Color3,
  Color4,
  Mesh,
  StandardMaterial,
  PBRMaterial,
  Texture,
} from "@babylonjs/core";
import { ActionManager, ExecuteCodeAction } from "@babylonjs/core";
import "@babylonjs/loaders/glTF";
import "@babylonjs/loaders/OBJ";

// 场景配置接口
interface SceneConfig {
  id: string;
  name: string;
  category: string;
  model_url: string;
  texture_urls?: string[];
  thumbnail_url?: string;
  lighting_config?: any;
  camera_config?: any;
  interaction_config?: any;
  audio_config?: any;
}

// 纪念空间数据接口
interface MemorialData {
  id: string;
  deceased_name: string;
  bio?: string;
  cover_image_url?: string;
  assets?: Array<{
    id: string;
    asset_type: string;
    file_url: string;
    title?: string;
    description?: string;
  }>;
}


// 组件属性接口
interface MemorialScene3DProps {
  sceneConfig: SceneConfig;
  memorialData: MemorialData;
  onInteraction?: (type: string, data?: any) => void;
  onSceneReady?: () => void;
  className?: string;
  isInteractive?: boolean;
  performanceLevel?: "low" | "medium" | "high";
  enableShadows?: boolean;
}

// 性能配置
const PERFORMANCE_CONFIGS = {
  low: {
    shadowsEnabled: false,
    maxLights: 2,
    renderScale: 0.8,
    postProcessingEnabled: false,
  },
  medium: {
    shadowsEnabled: true,
    maxLights: 4,
    renderScale: 1.0,
    postProcessingEnabled: false,
  },
  high: {
    shadowsEnabled: true,
    maxLights: 8,
    renderScale: 1.0,
    postProcessingEnabled: true,
  },
};

// 3D照片显示组件
const PhotoDisplay: React.FC<{
  position: Vector3;
  imageUrl: string;
  title?: string;
  onClick?: () => void;
}> = ({ position, imageUrl, title, onClick }) => {
  const handlePhotoCreated = useCallback((mesh: Mesh) => {
    if (mesh) {
      // 设置材质和贴图
      const material = new StandardMaterial(`photoMaterial_${Date.now()}`, mesh.getScene());
      
      if (imageUrl) {
        material.diffuseTexture = new Texture(imageUrl, mesh.getScene());
        material.emissiveColor = new Color3(0.1, 0.1, 0.1);
      } else {
        material.diffuseColor = new Color3(0.8, 0.8, 0.8);
      }
      
      mesh.material = material;

      // 设置交互
      if (onClick) {
        mesh.actionManager = new ActionManager(mesh.getScene());
        mesh.actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPickTrigger, onClick)
        );

        mesh.actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPointerOverTrigger, () => {
            material.emissiveColor = new Color3(0.2, 0.2, 0.2);
          })
        );

        mesh.actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPointerOutTrigger, () => {
            material.emissiveColor = new Color3(0.1, 0.1, 0.1);
          })
        );
      }
    }
  }, [imageUrl, onClick]);

  return (
    <plane
      name={`photoDisplay_${title || 'photo'}`}
      size={2}
      position={position}
      onCreated={handlePhotoCreated}
    />
  );
};

// 虚拟蜡烛组件
const VirtualCandle: React.FC<{
  position: Vector3;
  isLit?: boolean;
  onLight?: () => void;
}> = ({ position, isLit = false, onLight }) => {
  const [lit, setLit] = useState(isLit);

  const handleCandleCreated = useCallback((mesh: Mesh) => {
    if (mesh) {
      const material = new StandardMaterial(`candleMaterial_${Date.now()}`, mesh.getScene());
      material.diffuseColor = lit ? new Color3(1, 0.8, 0.2) : new Color3(0.8, 0.8, 0.8);
      
      if (lit) {
        material.emissiveColor = new Color3(0.5, 0.3, 0.1);
      }
      
      mesh.material = material;

      // 添加点击交互
      if (onLight) {
        mesh.actionManager = new ActionManager(mesh.getScene());
        mesh.actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPickTrigger, () => {
            setLit(!lit);
            if (onLight) onLight();
          })
        );
      }
    }
  }, [lit, onLight]);

  return (
    <cylinder
      name={`candle_${Date.now()}`}
      height={1}
      diameter={0.2}
      position={position}
      onCreated={handleCandleCreated}
    />
  );
};

// 供品台组件
const OfferingTable: React.FC<{
  position: Vector3;
  onOfferingPlace?: (offeringType: string) => void;
}> = ({ position, onOfferingPlace }) => {
  const handleTableCreated = useCallback((mesh: Mesh) => {
    if (mesh) {
      const material = new PBRMaterial(`tableMaterial_${Date.now()}`, mesh.getScene());
      material.albedoColor = new Color3(0.6, 0.4, 0.2); // 木质颜色
      material.metallic = 0.1;
      material.roughness = 0.8;
      
      mesh.material = material;

      if (onOfferingPlace) {
        mesh.actionManager = new ActionManager(mesh.getScene());
        mesh.actionManager.registerAction(
          new ExecuteCodeAction(ActionManager.OnPickTrigger, () => {
            if (onOfferingPlace) onOfferingPlace('general');
          })
        );
      }
    }
  }, [onOfferingPlace]);

  return (
    <box
      name={`offeringTable_${Date.now()}`}
      size={2}
      position={position}
      onCreated={handleTableCreated}
    />
  );
};

// 主场景组件
const MemorialScene3D: React.FC<MemorialScene3DProps> = ({
  sceneConfig,
  memorialData,
  onInteraction,
  onSceneReady,
  className = "",
  isInteractive = true,
  performanceLevel = "medium",
  enableShadows = true,
}) => {
  const sceneRef = useRef<any>(null);
  const [sceneReady, setSceneReady] = useState(false);

  // 获取性能配置
  const perfConfig = PERFORMANCE_CONFIGS[performanceLevel];

  // 场景挂载处理
  const handleSceneMount = useCallback((scene: any) => {
    if (scene && scene.scene) {
      sceneRef.current = scene.scene;
      
      // 设置场景性能配置
      scene.scene.skipPointerMovePicking = performanceLevel === "low";
      scene.scene.autoClear = true;
      scene.scene.autoClearDepthAndStencil = true;
      
      // 启用硬件缩放
      if (perfConfig.renderScale !== 1.0) {
        scene.scene.getEngine().setHardwareScalingLevel(1 / perfConfig.renderScale);
      }

      setSceneReady(true);
      if (onSceneReady) {
        onSceneReady();
      }
    }
  }, [performanceLevel, perfConfig.renderScale, onSceneReady]);

  // 创建灯光系统
  const createLighting = useCallback(() => {
    const lights = [];
    
    // 环境光
    lights.push(
      <hemisphericLight
        key="ambientLight"
        name="ambientLight"
        direction={Vector3.Up()}
        intensity={0.4}
        diffuse={new Color3(1, 1, 0.8)}
      />
    );

    // 主方向光
    if (perfConfig.maxLights > 1) {
      lights.push(
        <directionalLight
          key="mainLight"
          name="mainLight"
          direction={new Vector3(-1, -1, -1)}
          intensity={0.8}
          diffuse={new Color3(1, 0.95, 0.8)}
          shadowEnabled={enableShadows && perfConfig.shadowsEnabled}
        />
      );
    }

    // 补充点光源
    if (perfConfig.maxLights > 2) {
      lights.push(
        <pointLight
          key="fillLight"
          name="fillLight"
          position={new Vector3(5, 8, 5)}
          intensity={0.3}
          diffuse={new Color3(0.8, 0.9, 1)}
        />
      );
    }

    return lights;
  }, [perfConfig.maxLights, perfConfig.shadowsEnabled, enableShadows]);

  // 处理交互
  const handleInteraction = useCallback((type: string, data?: any) => {
    if (onInteraction && isInteractive) {
      onInteraction(type, data);
    }
  }, [onInteraction, isInteractive]);

  // 渲染照片展示
  const renderPhotos = useCallback(() => {
    if (!memorialData.assets) return null;
    
    const photoAssets = memorialData.assets.filter(asset => 
      asset.asset_type === 'photo' || asset.asset_type === 'image'
    );

    return photoAssets.slice(0, 3).map((asset, index) => (
      <PhotoDisplay
        key={asset.id}
        position={new Vector3(-3 + index * 3, 2, -2)}
        imageUrl={asset.file_url}
        title={asset.title}
        onClick={() => handleInteraction('photo_view', asset)}
      />
    ));
  }, [memorialData.assets, handleInteraction]);

  return (
    <div className={`memorial-scene-3d ${className}`} style={{ width: '100%', height: '500px' }}>
      <Engine
        antialias={performanceLevel !== "low"}
        adaptToDeviceRatio={true}
        canvasId="memorial-scene-canvas"
      >
        <Scene
          clearColor={new Color4(0.2, 0.2, 0.3, 1)}
          onSceneMount={handleSceneMount}
        >
          {/* 相机 */}
          <arcRotateCamera
            name="camera"
            target={Vector3.Zero()}
            alpha={-Math.PI / 2}
            beta={Math.PI / 2.5}
            radius={10}
            minZ={0.1}
            maxZ={100}
            setActiveOnSceneIfNoneActive={true}
          />

          {/* 灯光系统 */}
          {createLighting()}

          {/* 地面 */}
          <ground
            name="ground"
            width={20}
            height={20}
            subdivisions={performanceLevel === "low" ? 1 : 4}
          >
            <standardMaterial
              name="groundMaterial"
              diffuseColor={new Color3(0.4, 0.6, 0.4)}
              specularColor={new Color3(0.1, 0.1, 0.1)}
            />
          </ground>

          {/* 中央纪念台 */}
          <OfferingTable
            position={new Vector3(0, 0.5, 0)}
            onOfferingPlace={(type) => handleInteraction('offering', { type })}
          />

          {/* 照片展示 */}
          {renderPhotos()}

          {/* 蜡烛 */}
          <VirtualCandle
            position={new Vector3(-2, 1, 1)}
            onLight={() => handleInteraction('candle_light')}
          />
          <VirtualCandle
            position={new Vector3(2, 1, 1)}
            onLight={() => handleInteraction('candle_light')}
          />

          {/* 天空盒 */}
          {performanceLevel !== "low" && (
            <box name="skyBox" size={50} infiniteDistance={true}>
              <standardMaterial
                name="skyBoxMaterial"
                diffuseColor={new Color3(0, 0, 0)}
                specularColor={new Color3(0, 0, 0)}
                disableLighting={true}
                backFaceCulling={false}
              />
            </box>
          )}
        </Scene>
      </Engine>
      
      {/* 场景信息覆盖层 */}
      {sceneReady && (
        <div className="absolute top-4 left-4 bg-black bg-opacity-50 text-white p-3 rounded">
          <h3 className="text-lg font-bold">{memorialData.deceased_name}</h3>
          <p className="text-sm">{sceneConfig.name}</p>
          {performanceLevel === "low" && (
            <p className="text-xs text-yellow-300">低性能模式</p>
          )}
        </div>
      )}
    </div>
  );
};

export default MemorialScene3D;