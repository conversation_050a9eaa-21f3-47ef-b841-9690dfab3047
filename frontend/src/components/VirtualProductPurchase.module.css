/* Memorial 虚拟商品购买流程样式 */

.virtualProductPurchase {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 加载和错误状态 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: var(--text-secondary, #666);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-light, #e0e0e0);
  border-top: 4px solid var(--primary, #007AFF);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  color: var(--error, #dc3545);
}

/* 商品选择界面 */
.productSelection {
  width: 100%;
}

.header {
  text-align: center;
  margin-bottom: 32px;
}

.header h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--text-primary, #333);
}

.header p {
  margin: 0;
  font-size: 16px;
  color: var(--text-secondary, #666);
}

/* 分类过滤器 */
.categoryFilter {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 32px;
  flex-wrap: wrap;
}

.categoryButton {
  padding: 10px 20px;
  background: var(--bg-secondary, #f8f9fa);
  border: 2px solid var(--border-light, #e8e8e8);
  border-radius: 25px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary, #666);
  cursor: pointer;
  transition: all 0.2s ease;
}

.categoryButton:hover {
  border-color: var(--primary-light, #66b3ff);
  background: var(--bg-hover, #f0f8ff);
}

.categoryButton.active {
  background: var(--primary, #007AFF);
  border-color: var(--primary, #007AFF);
  color: white;
}

/* 商品网格 */
.productGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.productCard {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
}

.productCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.productImage {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.productImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.durationBadge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: var(--primary, #007AFF);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.productInfo {
  padding: 20px;
}

.productInfo h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.description {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: var(--text-secondary, #666);
  line-height: 1.4;
}

.religiousCompatibility {
  display: flex;
  gap: 6px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.religionTag {
  padding: 2px 8px;
  background: var(--bg-tertiary, #f0f0f0);
  border-radius: 10px;
  font-size: 12px;
  color: var(--text-tertiary, #888);
}

.priceSection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.price {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary, #007AFF);
}

.customizable {
  padding: 4px 8px;
  background: var(--success-light, #e8f5e8);
  color: var(--success, #28a745);
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
}

.actions {
  display: flex;
  gap: 8px;
}

.detailButton {
  flex: 1;
  padding: 10px 16px;
  background: var(--bg-secondary, #f8f9fa);
  border: 1px solid var(--border-light, #e8e8e8);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #333);
  cursor: pointer;
  transition: all 0.2s ease;
}

.detailButton:hover {
  background: var(--bg-hover, #e8e8e8);
}

.addButton {
  flex: 1;
  padding: 10px 16px;
  background: var(--primary, #007AFF);
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.addButton:hover {
  background: var(--primary-dark, #0056cc);
}

.empty {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary, #666);
}

/* 商品定制界面 */
.productCustomization {
  max-width: 800px;
  margin: 0 auto;
}

.backButton {
  background: none;
  border: none;
  font-size: 16px;
  color: var(--primary, #007AFF);
  cursor: pointer;
  margin-bottom: 20px;
}

.productPreview {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.productPreview img {
  width: 200px;
  height: 150px;
  object-fit: cover;
  border-radius: 12px;
}

.productBasicInfo h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.basePrice {
  color: var(--text-secondary, #666);
  font-size: 14px;
}

.customizationOptions {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
}

.customizationOptions h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
}

.customizationOption {
  margin-bottom: 24px;
}

.optionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.optionLabel {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary, #333);
}

.required {
  color: var(--error, #dc3545);
  margin-left: 4px;
}

.optionPrice {
  font-size: 14px;
  color: var(--success, #28a745);
  font-weight: 500;
}

.textInput {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-light, #e8e8e8);
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.textInput:focus {
  outline: none;
  border-color: var(--primary, #007AFF);
}

.colorOptions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.colorOption {
  padding: 8px 16px;
  background: var(--bg-secondary, #f8f9fa);
  border: 2px solid var(--border-light, #e8e8e8);
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.colorOption:hover {
  border-color: var(--primary-light, #66b3ff);
}

.colorOption.selected {
  background: var(--primary, #007AFF);
  border-color: var(--primary, #007AFF);
  color: white;
}

.sizeSelect {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-light, #e8e8e8);
  border-radius: 8px;
  font-size: 14px;
  background: white;
}

.imageUpload {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.fileInput {
  padding: 12px 16px;
  border: 2px dashed var(--border-light, #e8e8e8);
  border-radius: 8px;
  background: var(--bg-secondary, #f8f9fa);
  cursor: pointer;
}

.preview {
  padding: 8px 12px;
  background: var(--success-light, #e8f5e8);
  color: var(--success, #28a745);
  border-radius: 6px;
  font-size: 14px;
}

.summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.totalPrice {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary, #007AFF);
}

.addToCartButton {
  padding: 12px 32px;
  background: var(--primary, #007AFF);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.addToCartButton:hover:not(:disabled) {
  background: var(--primary-dark, #0056cc);
}

.addToCartButton:disabled {
  background: var(--disabled, #cccccc);
  cursor: not-allowed;
}

/* 购物车界面 */
.shoppingCart {
  max-width: 800px;
  margin: 0 auto;
}

.continueButton {
  background: none;
  border: none;
  color: var(--primary, #007AFF);
  font-size: 16px;
  cursor: pointer;
}

.cartItems {
  margin-bottom: 24px;
}

.cartItem {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-bottom: 16px;
}

.cartItem img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 8px;
}

.itemInfo {
  flex: 1;
}

.itemInfo h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.customizations {
  margin: 12px 0;
}

.customizations h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
}

.customization {
  display: inline-block;
  margin-right: 12px;
  padding: 4px 8px;
  background: var(--bg-tertiary, #f0f0f0);
  border-radius: 6px;
  font-size: 12px;
}

.itemPrice {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary, #007AFF);
  margin-top: 8px;
}

.removeButton {
  background: var(--error-light, #ffeaea);
  color: var(--error, #dc3545);
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  height: fit-content;
}

.cartSummary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.total {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary, #333);
}

.checkoutButton {
  padding: 12px 32px;
  background: var(--primary, #007AFF);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.checkoutButton:hover {
  background: var(--primary-dark, #0056cc);
}

.emptyCart {
  text-align: center;
  padding: 60px 20px;
}

.emptyCart h2 {
  margin: 0 0 16px 0;
  color: var(--text-primary, #333);
}

.emptyCart button {
  padding: 12px 24px;
  background: var(--primary, #007AFF);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
}

/* 购买完成界面 */
.purchaseCompleted {
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
  padding: 40px 20px;
}

.successIcon {
  font-size: 64px;
  margin-bottom: 24px;
  animation: bounceIn 0.6s ease;
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

.purchaseCompleted h2 {
  margin: 0 0 16px 0;
  font-size: 28px;
  font-weight: 700;
  color: var(--success, #28a745);
}

.orderInfo {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin: 24px 0;
  text-align: left;
}

.orderInfo h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
}

.purchasedItems {
  margin-top: 16px;
}

.purchasedItem {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid var(--border-light, #e8e8e8);
}

.actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 24px;
}

.continueButton, .viewSpaceButton {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.continueButton {
  background: var(--bg-secondary, #f8f9fa);
  color: var(--text-primary, #333);
  border: 1px solid var(--border-light, #e8e8e8);
}

.viewSpaceButton {
  background: var(--primary, #007AFF);
  color: white;
  border: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .virtualProductPurchase {
    padding: 16px;
  }

  .productGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .productPreview {
    flex-direction: column;
    text-align: center;
  }

  .productPreview img {
    width: 100%;
    height: 200px;
  }

  .cartItem {
    flex-direction: column;
  }

  .cartItem img {
    width: 100%;
    height: 150px;
  }

  .cartSummary {
    flex-direction: column;
    gap: 16px;
  }

  .actions {
    flex-direction: column;
  }
}