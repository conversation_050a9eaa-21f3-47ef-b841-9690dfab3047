.navbar {
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  box-shadow: var(--shadow-lg);
  border-bottom: 1px solid var(--color-border-light);
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  height: 70px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--color-text-primary);
}

.logo-image {
  height: 40px;
  margin-right: 10px;
}

.logo-text {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.navbar-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: var(--color-text-primary);
  text-decoration: none;
  font-size: var(--font-size-base);
  padding: var(--spacing-sm) 0;
  position: relative;
  transition: color var(--transition-normal);
}

.nav-link:hover {
  color: var(--color-primary);
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--color-primary);
  transition: width var(--transition-normal);
}

.nav-link:hover::after {
  width: 100%;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navbar-container {
    flex-direction: column;
    height: auto;
    padding: 1rem 2rem;
  }

  .navbar-logo {
    margin-bottom: 1rem;
  }

  .navbar-links {
    margin-bottom: 1rem;
  }

  .navbar-actions {
    width: 100%;
    justify-content: space-between;
  }
}
