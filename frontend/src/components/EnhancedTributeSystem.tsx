// 增强的祭拜系统组件
import React, { useState, useEffect, useCallback, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";

// 祭拜类型配置（增强版）
const ENHANCED_TRIBUTE_TYPES = {
  candle: {
    name: "点燃蜡烛",
    icon: "🕯️",
    description: "为逝者点亮心灯，表达思念",
    color: "bg-gradient-to-br from-yellow-500 to-orange-600",
    hoverColor: "from-yellow-400 to-orange-500",
    animation: "flame",
    sound: "/sounds/candle-light.mp3",
    particles: "fire",
    blessing: "愿这烛光为您带来温暖与安息"
  },
  flower: {
    name: "献花",
    icon: "🌸",
    description: "献上鲜花，表达敬意",
    color: "bg-gradient-to-br from-pink-500 to-rose-600",
    hoverColor: "from-pink-400 to-rose-500",
    animation: "bloom",
    sound: "/sounds/flower-place.mp3",
    particles: "petals",
    blessing: "愿这花香陪伴您，如您生前的美好"
  },
  incense: {
    name: "上香",
    icon: "🪔",
    description: "点燃心香，祈福安息",
    color: "bg-gradient-to-br from-purple-500 to-indigo-600",
    hoverColor: "from-purple-400 to-indigo-500",
    animation: "smoke",
    sound: "/sounds/incense-burn.mp3",
    particles: "smoke",
    blessing: "香烟袅袅，承载我们对您的思念"
  },
  bow: {
    name: "鞠躬",
    icon: "🙏",
    description: "深深鞠躬，表达敬意",
    color: "bg-gradient-to-br from-gray-500 to-slate-600",
    hoverColor: "from-gray-400 to-slate-500",
    animation: "bow",
    sound: "/sounds/respectful-bow.mp3",
    particles: "light",
    blessing: "此致敬礼，表达我们深深的敬意"
  },
  food: {
    name: "供奉食物",
    icon: "🍎",
    description: "献上供品，寄托哀思",
    color: "bg-gradient-to-br from-orange-500 to-red-600",
    hoverColor: "from-orange-400 to-red-500",
    animation: "place",
    sound: "/sounds/food-place.mp3",
    particles: "sparkle",
    blessing: "献上佳肴美食，愿您在另一世界安好"
  },
  prayer: {
    name: "祈祷",
    icon: "🤲",
    description: "虔诚祈祷，祝福安息",
    color: "bg-gradient-to-br from-blue-500 to-cyan-600",
    hoverColor: "from-blue-400 to-cyan-500",
    animation: "pray",
    sound: "/sounds/prayer-bell.mp3",
    particles: "divine",
    blessing: "愿您在天之灵得到永恒的安息"
  }
};

interface TributeRecord {
  id: string;
  tribute_type: string;
  message?: string;
  is_anonymous: boolean;
  created_at: string;
  user_name?: string;
}

interface TributeStats {
  total_tributes: number;
  tribute_types: Record<string, number>;
  recent_tributes: TributeRecord[];
}

interface EnhancedTributeSystemProps {
  memorialSpaceId: string;
  isInteractive?: boolean;
  className?: string;
  enableSound?: boolean;
  enableAnimations?: boolean;
  onTributeComplete?: (type: string, message?: string) => void;
}

const EnhancedTributeSystem: React.FC<EnhancedTributeSystemProps> = ({
  memorialSpaceId,
  isInteractive = true,
  className = "",
  enableSound = true,
  enableAnimations = true,
  onTributeComplete,
}) => {
  const [tributes, setTributes] = useState<TributeRecord[]>([]);
  const [stats, setStats] = useState<TributeStats | null>(null);
  const [selectedTributeType, setSelectedTributeType] = useState<string | null>(null);
  const [tributeMessage, setTributeMessage] = useState("");
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showTributeModal, setShowTributeModal] = useState(false);
  const [showSuccessAnimation, setShowSuccessAnimation] = useState(false);
  const [activeTab, setActiveTab] = useState<"tribute" | "history" | "stats" | "session">("tribute");
  const [currentSession, setCurrentSession] = useState<any>(null);
  const [sessionDuration, setSessionDuration] = useState(0);
  
  // 音频播放相关
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const sessionTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 播放声音效果
  const playSound = useCallback((soundUrl: string) => {
    if (!enableSound) return;
    
    try {
      if (audioRef.current) {
        audioRef.current.pause();
      }
      audioRef.current = new Audio(soundUrl);
      audioRef.current.volume = 0.3;
      audioRef.current.play().catch(e => console.log('音频播放失败:', e));
    } catch (error) {
      console.log('音频播放失败:', error);
    }
  }, [enableSound]);

  // 获取祭拜记录
  const fetchTributes = useCallback(async () => {
    try {
      const token = localStorage.getItem("authToken");
      if (!token) {
        // 没有token时，设置空数组并静默返回
        setTributes([]);
        return;
      }

      const response = await fetch(`/api/v1/memorial-spaces/${memorialSpaceId}/tributes`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTributes(data.items || []);
      } else if (response.status === 401 || response.status === 403) {
        // 认证失败时静默处理，设置空数组
        setTributes([]);
      } else {
        console.error("获取祭拜记录失败:", response.status);
      }
    } catch (err) {
      console.error("获取祭拜记录失败:", err);
      setTributes([]);
    }
  }, [memorialSpaceId]);

  // 获取祭拜统计
  const fetchStats = useCallback(async () => {
    try {
      const token = localStorage.getItem("authToken");
      if (!token) {
        // 没有token时，设置默认统计并静默返回
        setStats({ total_tributes: 0, tribute_types: {}, recent_tributes: [] });
        return;
      }

      const response = await fetch(`/api/v1/memorial-spaces/${memorialSpaceId}/tributes/stats`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      } else if (response.status === 401 || response.status === 403) {
        // 认证失败时静默处理，设置默认统计
        setStats({ total_tributes: 0, tribute_types: {}, recent_tributes: [] });
      } else {
        console.error("获取祭拜统计失败:", response.status);
      }
    } catch (err) {
      console.error("获取祭拜统计失败:", err);
      setStats({ total_tributes: 0, tribute_types: {}, recent_tributes: [] });
    }
  }, [memorialSpaceId]);

  // 开始祭拜会话
  const startSession = useCallback(async () => {
    try {
      const token = localStorage.getItem("authToken");
      const response = await fetch(`/api/v1/memorial-spaces/${memorialSpaceId}/worship-sessions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          session_type: "individual",
          participant_count: 1
        }),
      });

      if (response.ok) {
        const session = await response.json();
        setCurrentSession(session);
        setSessionDuration(0);
        
        // 开始计时
        sessionTimerRef.current = setInterval(() => {
          setSessionDuration(prev => prev + 1);
        }, 1000);
      }
    } catch (err) {
      console.error("开始祭拜会话失败:", err);
    }
  }, [memorialSpaceId]);

  // 结束祭拜会话
  const endSession = useCallback(async () => {
    if (!currentSession) return;

    try {
      const token = localStorage.getItem("authToken");
      await fetch(`/api/v1/memorial-spaces/${memorialSpaceId}/worship-sessions/${currentSession.id}/end`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      setCurrentSession(null);
      setSessionDuration(0);
      
      if (sessionTimerRef.current) {
        clearInterval(sessionTimerRef.current);
        sessionTimerRef.current = null;
      }
    } catch (err) {
      console.error("结束祭拜会话失败:", err);
    }
  }, [currentSession, memorialSpaceId]);

  // 提交祭拜
  const submitTribute = async () => {
    if (!selectedTributeType) return;

    try {
      setIsSubmitting(true);
      const token = localStorage.getItem("authToken");
      
      const response = await fetch(`/api/v1/memorial-spaces/${memorialSpaceId}/tributes`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          tribute_type: selectedTributeType,
          message: tributeMessage.trim() || null,
          is_anonymous: isAnonymous,
        }),
      });

      if (response.ok) {
        // 播放成功音效
        const config = ENHANCED_TRIBUTE_TYPES[selectedTributeType as keyof typeof ENHANCED_TRIBUTE_TYPES];
        if (config.sound) {
          playSound(config.sound);
        }

        // 显示成功动画
        setShowSuccessAnimation(true);
        setTimeout(() => setShowSuccessAnimation(false), 3000);

        // 回调函数
        if (onTributeComplete) {
          onTributeComplete(selectedTributeType, tributeMessage);
        }

        // 重置表单
        setSelectedTributeType(null);
        setTributeMessage("");
        setIsAnonymous(false);
        setShowTributeModal(false);
        
        // 刷新数据
        await Promise.all([fetchTributes(), fetchStats()]);
      } else {
        throw new Error("祭拜提交失败");
      }
    } catch (err) {
      console.error("祭拜提交失败:", err);
      alert("祭拜提交失败，请稍后重试");
    } finally {
      setIsSubmitting(false);
    }
  };

  // 粒子效果动画
  const ParticleAnimation: React.FC<{ type: string }> = ({ type }) => {
    const particles = Array.from({ length: 20 }, (_, i) => i);

    const getParticleStyle = (type: string) => {
      switch (type) {
        case "fire":
          return "bg-orange-400 opacity-80";
        case "petals":
          return "bg-pink-300 opacity-70";
        case "smoke":
          return "bg-gray-400 opacity-50";
        case "light":
          return "bg-yellow-200 opacity-60";
        case "sparkle":
          return "bg-blue-300 opacity-80";
        case "divine":
          return "bg-purple-200 opacity-70";
        default:
          return "bg-white opacity-60";
      }
    };

    return (
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {particles.map((_, i) => (
          <motion.div
            key={i}
            className={`absolute w-2 h-2 rounded-full ${getParticleStyle(type)}`}
            initial={{
              x: Math.random() * 300,
              y: 300,
              opacity: 0,
              scale: 0,
            }}
            animate={{
              y: -50,
              opacity: [0, 1, 0],
              scale: [0, 1, 0],
              x: Math.random() * 300,
            }}
            transition={{
              duration: 3,
              delay: i * 0.1,
              ease: "easeOut",
            }}
          />
        ))}
      </div>
    );
  };

  // 格式化时间
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  useEffect(() => {
    fetchTributes();
    fetchStats();

    return () => {
      if (sessionTimerRef.current) {
        clearInterval(sessionTimerRef.current);
      }
    };
  }, [fetchTributes, fetchStats]);

  return (
    <div className={`enhanced-tribute-system ${className}`}>
      {/* 成功动画覆盖层 */}
      <AnimatePresence>
        {showSuccessAnimation && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 pointer-events-none"
          >
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              exit={{ scale: 0, rotate: 180 }}
              className="bg-white rounded-full p-8 shadow-2xl"
            >
              <div className="text-6xl">✨</div>
            </motion.div>
            <ParticleAnimation type="divine" />
          </motion.div>
        )}
      </AnimatePresence>

      {/* 标签页导航 - 移动端优化 */}
      <div className="flex overflow-x-auto space-x-2 sm:space-x-4 border-b border-gray-700 mb-6 scrollbar-hide">
        {[
          { key: "tribute", label: "祭拜", icon: "🙏" },
          { key: "session", label: "会话", icon: "⏰" },
          { key: "history", label: "记录", icon: "📜" },
          { key: "stats", label: "统计", icon: "📊" },
        ].map(tab => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`py-3 px-3 sm:px-6 text-xs sm:text-sm font-medium border-b-2 transition-colors flex items-center gap-1 sm:gap-2 whitespace-nowrap flex-shrink-0 ${
              activeTab === tab.key
                ? "text-blue-400 border-blue-400"
                : "text-gray-400 border-transparent hover:text-white"
            }`}
          >
            <span>{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* 祭拜会话 */}
      {activeTab === "session" && (
        <div className="space-y-6">
          <div className="text-center mb-6">
            <h3 className="text-xl font-bold text-white mb-2">祭拜会话</h3>
            <p className="text-gray-400">开始一个专注的祭拜时间，记录您的心意历程</p>
          </div>

          {currentSession ? (
            <div className="bg-gray-800 rounded-lg p-6 text-center">
              <div className="text-4xl mb-4">⏰</div>
              <h4 className="text-xl text-white mb-2">祭拜进行中</h4>
              <div className="text-3xl font-mono text-blue-400 mb-4">
                {formatDuration(sessionDuration)}
              </div>
              <div className="space-y-3">
                <p className="text-gray-400">本次会话已进行 {sessionDuration} 秒</p>
                <button
                  onClick={endSession}
                  className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg"
                >
                  结束会话
                </button>
              </div>
            </div>
          ) : (
            <div className="bg-gray-800 rounded-lg p-6 text-center">
              <div className="text-4xl mb-4">🕯️</div>
              <h4 className="text-xl text-white mb-2">开始祭拜会话</h4>
              <p className="text-gray-400 mb-4">
                开始一个专注的祭拜时间，系统将记录您的祭拜历程和时长
              </p>
              <button
                onClick={startSession}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg"
              >
                开始祭拜会话
              </button>
            </div>
          )}
        </div>
      )}

      {/* 祭拜操作 */}
      {activeTab === "tribute" && (
        <div className="space-y-6">
          <div className="text-center mb-6">
            <h3 className="text-xl font-bold text-white mb-2">献上心意</h3>
            <p className="text-gray-400">选择一种方式，表达您对逝者的思念与敬意</p>
            {currentSession && (
              <div className="mt-2 text-blue-400 text-sm">
                会话进行中: {formatDuration(sessionDuration)}
              </div>
            )}
          </div>

          {/* 祭拜类型选择 - 移动端优化 */}
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-3 gap-3 sm:gap-4">
            {Object.entries(ENHANCED_TRIBUTE_TYPES).map(([type, config]) => (
              <motion.button
                key={type}
                whileHover={enableAnimations ? { scale: 1.05 } : {}}
                whileTap={enableAnimations ? { scale: 0.95 } : {}}
                onClick={() => {
                  setSelectedTributeType(type);
                  setShowTributeModal(true);
                }}
                disabled={!isInteractive}
                className={`p-4 sm:p-6 rounded-xl border-2 transition-all text-center relative overflow-hidden touch-manipulation ${
                  isInteractive
                    ? `${config.color} hover:${config.hoverColor} border-transparent text-white shadow-lg active:scale-95`
                    : "bg-gray-700 border-gray-600 text-gray-400 cursor-not-allowed"
                }`}
              >
                <div className="relative z-10">
                  <div className="text-2xl sm:text-3xl mb-2 sm:mb-3">{config.icon}</div>
                  <div className="font-semibold mb-1 sm:mb-2 text-sm sm:text-base">{config.name}</div>
                  <div className="text-xs sm:text-sm opacity-90 leading-tight">{config.description}</div>
                </div>
                
                {/* 背景渐变效果 */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent opacity-0 hover:opacity-100 transition-opacity" />
              </motion.button>
            ))}
          </div>

          {/* 祭拜统计简览 */}
          {stats && (
            <motion.div 
              initial={enableAnimations ? { opacity: 0, y: 20 } : {}}
              animate={enableAnimations ? { opacity: 1, y: 0 } : {}}
              className="bg-gray-800 rounded-lg p-6"
            >
              <h4 className="text-white font-semibold mb-4 flex items-center gap-2">
                <span>📊</span>
                祭拜统计
              </h4>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-400">{stats.total_tributes}</div>
                  <div className="text-sm text-gray-400">总祭拜次数</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-400">{stats.recent_tributes.length}</div>
                  <div className="text-sm text-gray-400">最近活动</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-400">{Object.keys(stats.tribute_types).length}</div>
                  <div className="text-sm text-gray-400">祭拜类型</div>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      )}

      {/* 祭拜历史 */}
      {activeTab === "history" && (
        <div className="space-y-4">
          <h3 className="text-xl font-bold text-white mb-4">祭拜记录</h3>
          {tributes.length > 0 ? (
            <div className="space-y-3">
              {tributes.map((tribute, index) => (
                <motion.div
                  key={tribute.id}
                  initial={enableAnimations ? { opacity: 0, x: -20 } : {}}
                  animate={enableAnimations ? { opacity: 1, x: 0 } : {}}
                  transition={enableAnimations ? { delay: index * 0.1 } : {}}
                  className="bg-gray-800 rounded-lg p-4 hover:bg-gray-750 transition-colors"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <span className="text-xl">
                        {ENHANCED_TRIBUTE_TYPES[tribute.tribute_type as keyof typeof ENHANCED_TRIBUTE_TYPES]?.icon || "🙏"}
                      </span>
                      <div>
                        <span className="text-blue-400 font-medium">
                          {tribute.is_anonymous ? "匿名用户" : (tribute.user_name || "用户")}
                        </span>
                        <span className="text-gray-400 text-sm ml-2">
                          {ENHANCED_TRIBUTE_TYPES[tribute.tribute_type as keyof typeof ENHANCED_TRIBUTE_TYPES]?.name || tribute.tribute_type}
                        </span>
                      </div>
                    </div>
                    <span className="text-gray-400 text-sm">
                      {new Date(tribute.created_at).toLocaleString()}
                    </span>
                  </div>
                  {tribute.message && (
                    <p className="text-gray-300 text-sm bg-gray-900 rounded-md p-3 mt-2">
                      "{tribute.message}"
                    </p>
                  )}
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-400">
              暂无祭拜记录
            </div>
          )}
        </div>
      )}

      {/* 统计信息 */}
      {activeTab === "stats" && stats && (
        <div className="space-y-6">
          <h3 className="text-xl font-bold text-white mb-4">祭拜统计</h3>
          
          {/* 总体统计 */}
          <motion.div 
            initial={enableAnimations ? { opacity: 0, scale: 0.9 } : {}}
            animate={enableAnimations ? { opacity: 1, scale: 1 } : {}}
            className="bg-gray-800 rounded-lg p-6"
          >
            <h4 className="text-white font-medium mb-4">总体数据</h4>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-400 mb-2">{stats.total_tributes}</div>
              <div className="text-gray-400">累计祭拜次数</div>
            </div>
          </motion.div>

          {/* 类型统计 */}
          <motion.div 
            initial={enableAnimations ? { opacity: 0, y: 20 } : {}}
            animate={enableAnimations ? { opacity: 1, y: 0 } : {}}
            transition={enableAnimations ? { delay: 0.2 } : {}}
            className="bg-gray-800 rounded-lg p-6"
          >
            <h4 className="text-white font-medium mb-4">祭拜类型分布</h4>
            <div className="space-y-4">
              {Object.entries(stats.tribute_types).map(([type, count]) => {
                const config = ENHANCED_TRIBUTE_TYPES[type as keyof typeof ENHANCED_TRIBUTE_TYPES];
                const percentage = stats.total_tributes > 0 ? (count / stats.total_tributes * 100) : 0;
                
                return (
                  <div key={type} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-xl">{config?.icon || "🙏"}</span>
                      <span className="text-white">{config?.name || type}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="w-24 bg-gray-700 rounded-full h-3 overflow-hidden">
                        <motion.div 
                          initial={enableAnimations ? { width: 0 } : { width: `${percentage}%` }}
                          animate={{ width: `${percentage}%` }}
                          transition={enableAnimations ? { duration: 1, delay: 0.5 } : {}}
                          className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full"
                        />
                      </div>
                      <span className="text-gray-400 text-sm w-12 text-right">{count}次</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </motion.div>
        </div>
      )}

      {/* 祭拜确认模态框 */}
      <AnimatePresence>
        {showTributeModal && selectedTributeType && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          >
            <motion.div
              initial={enableAnimations ? { scale: 0.8, opacity: 0 } : {}}
              animate={enableAnimations ? { scale: 1, opacity: 1 } : {}}
              exit={enableAnimations ? { scale: 0.8, opacity: 0 } : {}}
              className="bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4 relative overflow-hidden"
            >
              {/* 背景装饰 */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 to-purple-900/20" />
              
              <div className="relative z-10">
                <div className="text-center mb-6">
                  <div className="text-5xl mb-3">
                    {ENHANCED_TRIBUTE_TYPES[selectedTributeType as keyof typeof ENHANCED_TRIBUTE_TYPES]?.icon}
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">
                    {ENHANCED_TRIBUTE_TYPES[selectedTributeType as keyof typeof ENHANCED_TRIBUTE_TYPES]?.name}
                  </h3>
                  <p className="text-gray-400 text-sm">
                    {ENHANCED_TRIBUTE_TYPES[selectedTributeType as keyof typeof ENHANCED_TRIBUTE_TYPES]?.description}
                  </p>
                  <div className="mt-3 text-blue-300 text-sm font-medium">
                    {ENHANCED_TRIBUTE_TYPES[selectedTributeType as keyof typeof ENHANCED_TRIBUTE_TYPES]?.blessing}
                  </div>
                </div>

                {/* 祭拜留言 */}
                <div className="mb-4">
                  <label className="block text-white text-sm font-medium mb-2">
                    祭拜留言（可选）
                  </label>
                  <textarea
                    value={tributeMessage}
                    onChange={(e) => setTributeMessage(e.target.value)}
                    placeholder="表达您的思念与敬意..."
                    className="w-full bg-gray-700 text-white rounded-lg p-3 min-h-[80px] resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 border border-gray-600"
                    maxLength={200}
                  />
                  <div className="text-right text-gray-400 text-xs mt-1">
                    {tributeMessage.length}/200
                  </div>
                </div>

                {/* 匿名选项 */}
                <div className="mb-6">
                  <label className="flex items-center text-white text-sm">
                    <input
                      type="checkbox"
                      checked={isAnonymous}
                      onChange={(e) => setIsAnonymous(e.target.checked)}
                      className="mr-2 rounded"
                    />
                    匿名祭拜
                  </label>
                </div>

                {/* 操作按钮 */}
                <div className="flex gap-3">
                  <button
                    onClick={() => {
                      setShowTributeModal(false);
                      setSelectedTributeType(null);
                      setTributeMessage("");
                      setIsAnonymous(false);
                    }}
                    className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-3 rounded-lg transition-colors"
                  >
                    取消
                  </button>
                  <button
                    onClick={submitTribute}
                    disabled={isSubmitting}
                    className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-600 text-white py-3 rounded-lg transition-all"
                  >
                    {isSubmitting ? "祭拜中..." : "确认祭拜"}
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default EnhancedTributeSystem;