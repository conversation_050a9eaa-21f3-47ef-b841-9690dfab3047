import React from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import LanguageSwitcher from "./LanguageSwitcher";
// import LoginButton from "./LoginButton"; // Temporarily commented out or to be replaced
import { cn } from "../utils/cn";
import { Disclosure } from "@headlessui/react"; // For mobile menu
import { Bars3Icon, XMarkIcon } from "@heroicons/react/24/outline"; // For mobile menu icons

const Navbar: React.FC = () => {
  const { t } = useTranslation();

  const navigation = [
    { nameKey: "navbar.home", href: "/", current: true }, // Assuming 'home' key exists or maps to '首页'
    { nameKey: "navbar.features", href: "/#features", current: false }, // Link to section in home page
    { nameKey: "navbar.memorialHall", href: "/memorials", current: false }, // Example route
    { nameKey: "navbar.aboutUs", href: "/about", current: false }, // Assuming 'about' key exists or maps to '关于我们'
  ];

  return (
    <Disclosure
      as="nav"
      className={cn(
        "bg-gray-900 sticky top-0 z-50 shadow-lg border-b border-gray-700",
      )}
    >
      {({ open }) => (
        <>
          <div
            className={cn(
              "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-16 flex justify-between items-center",
            )}
          >
            <div className="flex items-center justify-between w-full">
              <div className="absolute inset-y-0 left-0 flex items-center md:hidden">
                {/* Mobile menu button*/}
                <Disclosure.Button className="relative inline-flex items-center justify-center rounded-md p-2 text-gray-300 hover:bg-amber-500 hover:bg-opacity-80 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-amber-500">
                  <span className="absolute -inset-0.5" />
                  <span className="sr-only">Open main menu</span>
                  {open ? (
                    <XMarkIcon className="block h-6 w-6" aria-hidden="true" />
                  ) : (
                    <Bars3Icon className="block h-6 w-6" aria-hidden="true" />
                  )}
                </Disclosure.Button>
              </div>
              <div className="flex flex-1 items-center justify-center md:items-stretch md:justify-start">
                <Link to="/" className="flex flex-shrink-0 items-center">
                  <i className="fas fa-pray text-amber-500 text-3xl mr-2"></i>
                  <span className="text-2xl font-bold text-white">
                    {t("appName", "归处")}
                  </span>
                </Link>
                <div className="hidden md:ml-6 md:flex md:space-x-8">
                  {navigation.map((item) => (
                    <Link
                      key={item.nameKey}
                      to={item.href}
                      className={cn(
                        "inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200",
                        item.current
                          ? "border-amber-500 text-white"
                          : "border-transparent text-gray-300 hover:border-amber-500 hover:text-white",
                      )}
                      aria-current={item.current ? "page" : undefined}
                    >
                      {t(item.nameKey)}
                    </Link>
                  ))}
                </div>
              </div>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 md:static md:inset-auto md:ml-6 md:pr-0">
                <LanguageSwitcher />
                <Link
                  to="/login"
                  className="ml-4 text-sm font-medium text-amber-500 hover:text-yellow-300 whitespace-nowrap"
                >
                  {t("navbar.login", "登录")}
                </Link>
                <Link
                  to="/register"
                  className="ml-4 whitespace-nowrap inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-gray-900 bg-amber-500 hover:bg-yellow-300"
                >
                  {t("navbar.register", "注册")}
                </Link>
                {/* LoginButton could be re-integrated here if needed, handling auth state */}
              </div>
            </div>
          </div>

          <Disclosure.Panel className="md:hidden">
            <div className="space-y-1 px-2 pb-3 pt-2 bg-gray-800">
              {navigation.map((item) => (
                <Disclosure.Button
                  key={item.nameKey}
                  as={Link}
                  to={item.href}
                  className={cn(
                    "block rounded-md py-2 px-3 text-base font-medium border-l-4 transition-colors duration-200",
                    item.current
                      ? "bg-amber-500 border-amber-500 text-gray-900"
                      : "border-transparent text-gray-300 hover:bg-amber-500 hover:bg-opacity-80 hover:border-amber-500 hover:text-gray-900",
                  )}
                  aria-current={item.current ? "page" : undefined}
                >
                  {t(item.nameKey)}
                </Disclosure.Button>
              ))}
            </div>
          </Disclosure.Panel>
        </>
      )}
    </Disclosure>
  );
};

export default Navbar;
