/* Memorial AR体验管理器样式 */

.arExperience {
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

/* AR不支持状态 */
.arUnsupported {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
  text-align: center;
}

.errorIcon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.8;
}

.arUnsupported h3 {
  margin: 0 0 16px 0;
  font-size: 28px;
  font-weight: 600;
}

.arUnsupported p {
  margin: 0 0 32px 0;
  font-size: 16px;
  opacity: 0.9;
  max-width: 500px;
  line-height: 1.6;
}

.requirements {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  max-width: 400px;
  text-align: left;
}

.requirements h4 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
}

.requirements ul {
  margin: 0;
  padding-left: 20px;
  list-style: none;
}

.requirements li {
  position: relative;
  padding: 4px 0;
  font-size: 14px;
  line-height: 1.5;
}

.requirements li::before {
  content: '•';
  position: absolute;
  left: -16px;
  color: #ffd700;
  font-weight: bold;
}

/* AR启动界面 */
.arStart {
  padding: 20px;
  height: 100vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.arPreview {
  text-align: center;
  padding: 40px 20px;
}

.arIcon {
  font-size: 72px;
  margin-bottom: 24px;
  display: inline-block;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.arPreview h2 {
  margin: 0 0 16px 0;
  font-size: 32px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.arPreview p {
  margin: 0 0 32px 0;
  font-size: 18px;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* 功能支持状态 */
.featureSupport {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 24px;
  margin: 32px 0;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.featureSupport h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
}

.featureList {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
}

.feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.feature.supported {
  background: rgba(40, 167, 69, 0.2);
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.feature.unsupported {
  background: rgba(220, 53, 69, 0.2);
  border: 1px solid rgba(220, 53, 69, 0.3);
}

.featureIcon {
  font-size: 24px;
  margin-bottom: 8px;
}

.feature span:last-child {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

/* AR启动按钮 */
.startARButton {
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 16px 48px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
  margin: 24px auto;
  display: block;
}

.startARButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 48px rgba(255, 107, 107, 0.4);
}

.startARButton:active {
  transform: translateY(0);
}

.startARButton:disabled {
  background: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
  box-shadow: none;
}

/* 产品选择区域 */
.productSelection {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.productSelection h3 {
  margin: 0 0 20px 0;
  font-size: 24px;
  font-weight: 600;
  text-align: center;
}

.productGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 16px;
}

.productCard {
  background: rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.productCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.productCard.locked {
  opacity: 0.7;
}

.productCard.locked:hover {
  transform: translateY(-2px);
}

.productImage {
  position: relative;
  text-align: center;
  margin-bottom: 12px;
}

.productType {
  font-size: 32px;
  margin-bottom: 8px;
}

.lockOverlay {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.productInfo h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
}

.productMeta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 10px;
  opacity: 0.8;
}

.category {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 8px;
}

.cultural {
  background: rgba(255, 193, 7, 0.3);
  color: #ffc107;
  padding: 2px 6px;
  border-radius: 8px;
}

.price {
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #ffd700;
}

/* AR激活状态 */
.arActive {
  position: relative;
  width: 100%;
  height: 100vh;
}

.arCanvas {
  width: 100%;
  height: 100%;
  display: block;
}

/* AR覆盖界面 */
.arOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1000;
}

.arOverlay > * {
  pointer-events: auto;
}

.arControls {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  padding: 12px 20px;
}

.exitButton, .clearButton {
  background: rgba(220, 53, 69, 0.9);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clearButton {
  background: rgba(255, 193, 7, 0.9);
  color: #333;
}

.exitButton:hover, .clearButton:hover:not(:disabled) {
  transform: scale(1.05);
}

.clearButton:disabled {
  background: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.6);
  cursor: not-allowed;
}

.arStats {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 8px 12px;
  font-size: 12px;
  font-weight: 500;
}

/* 产品放置界面 */
.productPlacement {
  position: absolute;
  bottom: 100px;
  left: 20px;
  right: 20px;
  display: flex;
  justify-content: center;
}

.selectedProduct {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.selectedProduct span {
  font-size: 16px;
  font-weight: 500;
  color: white;
}

.selectedProduct button {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.selectedProduct button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3);
}

/* AR使用说明 */
.arInstructions {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  text-align: center;
}

.arInstructions p {
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 12px 16px;
  margin: 0;
  font-size: 14px;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 错误状态 */
.error {
  background: rgba(220, 53, 69, 0.2);
  border: 1px solid rgba(220, 53, 69, 0.3);
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
  text-align: center;
}

.error p {
  margin: 0;
  font-size: 14px;
  color: #ff6b6b;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .arStart {
    padding: 16px;
    gap: 24px;
  }

  .arPreview {
    padding: 24px 16px;
  }

  .arIcon {
    font-size: 56px;
    margin-bottom: 16px;
  }

  .arPreview h2 {
    font-size: 24px;
  }

  .arPreview p {
    font-size: 16px;
    margin-bottom: 24px;
  }

  .featureSupport {
    padding: 20px;
    margin: 24px 0;
  }

  .featureList {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .feature {
    flex-direction: row;
    justify-content: flex-start;
    gap: 12px;
    padding: 12px 16px;
  }

  .featureIcon {
    margin-bottom: 0;
    font-size: 20px;
  }

  .productGrid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
  }

  .productCard {
    padding: 12px;
  }

  .productType {
    font-size: 24px;
  }

  .productInfo h4 {
    font-size: 12px;
  }

  .productMeta {
    font-size: 9px;
  }

  .arControls {
    flex-direction: column;
    gap: 8px;
    padding: 16px;
    top: 10px;
    left: 10px;
    right: 10px;
  }

  .selectedProduct {
    flex-direction: column;
    gap: 12px;
    bottom: 80px;
    left: 10px;
    right: 10px;
  }

  .arInstructions {
    bottom: 10px;
    left: 10px;
    right: 10px;
  }

  .arInstructions p {
    font-size: 12px;
    padding: 10px 12px;
  }
}

@media (max-width: 480px) {
  .productGrid {
    grid-template-columns: repeat(2, 1fr);
  }

  .startARButton {
    padding: 14px 32px;
    font-size: 16px;
  }

  .requirements {
    padding: 20px;
  }

  .requirements h4 {
    font-size: 16px;
  }

  .requirements li {
    font-size: 13px;
  }
}

/* 动画效果 */
.productCard {
  animation: fadeInUp 0.5s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature {
  animation: fadeInScale 0.3s ease forwards;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 脉冲动画 */
.startARButton:not(:disabled) {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);
  }
  50% {
    box-shadow: 0 8px 32px rgba(255, 107, 107, 0.5);
  }
  100% {
    box-shadow: 0 8px 32px rgba(255, 107, 107, 0.3);
  }
}