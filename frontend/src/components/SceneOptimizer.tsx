import React, { useState, useEffect, useRef } from "react";
import { Scene, Engine } from "@babylonjs/core";
import AdaptiveLODManager from "../utils/AdaptiveLODManager";
import PerformanceMonitor, { PerformanceMetrics, PerformanceStatus, OptimizationSuggestion } from "../utils/PerformanceMonitor";

// 性能优化器属性接口
interface SceneOptimizerProps {
  scene: Scene | null;
  engine: Engine | null;
  onPerformanceUpdate?: (metrics: PerformanceMetrics) => void;
  onOptimizationSuggestions?: (suggestions: OptimizationSuggestion[]) => void;
  showDebugPanel?: boolean;
  autoOptimization?: boolean;
}

// 优化器状态接口
interface OptimizerState {
  isInitialized: boolean;
  performanceMetrics: PerformanceMetrics | null;
  performanceStatus: PerformanceStatus;
  optimizationSuggestions: OptimizationSuggestion[];
  lodLevel: string;
  autoOptimizationEnabled: boolean;
}

// 场景优化器组件
const SceneOptimizer: React.FC<SceneOptimizerProps> = ({
  scene,
  engine,
  onPerformanceUpdate,
  onOptimizationSuggestions,
  showDebugPanel = false,
  autoOptimization = true,
}) => {
  const [state, setState] = useState<OptimizerState>({
    isInitialized: false,
    performanceMetrics: null,
    performanceStatus: "good",
    optimizationSuggestions: [],
    lodLevel: "medium",
    autoOptimizationEnabled: autoOptimization,
  });

  const lodManagerRef = useRef<AdaptiveLODManager | null>(null);
  const performanceMonitorRef = useRef<PerformanceMonitor | null>(null);
  const initializationTimeoutRef = useRef<number | null>(null);

  // 初始化优化器
  useEffect(() => {
    if (!scene || !engine || state.isInitialized) return;

    // 延迟初始化以确保场景完全加载
    initializationTimeoutRef.current = window.setTimeout(() => {
      initializeOptimizer();
    }, 1000);

    return () => {
      if (initializationTimeoutRef.current) {
        clearTimeout(initializationTimeoutRef.current);
      }
    };
  }, [scene, engine, state.isInitialized]);

  // 初始化优化器系统
  const initializeOptimizer = async (): Promise<void> => {
    if (!scene || !engine) return;

    try {
      console.log("[SceneOptimizer] Initializing performance optimization systems...");

      // 初始化LOD管理器
      lodManagerRef.current = AdaptiveLODManager.getInstance();
      lodManagerRef.current.initializeScene(scene);

      // 初始化性能监控器
      performanceMonitorRef.current = PerformanceMonitor.getInstance();
      performanceMonitorRef.current.initialize(scene, engine);

      // 设置性能监控回调
      performanceMonitorRef.current.addCallback(handlePerformanceUpdate);

      // 开始监控
      performanceMonitorRef.current.startMonitoring(1000); // 每秒更新一次

      // 应用初始优化设置
      await applyInitialOptimizations();

      setState(prev => ({
        ...prev,
        isInitialized: true,
        lodLevel: lodManagerRef.current?.getPerformanceStats().devicePerformanceLevel || "medium",
      }));

      console.log("[SceneOptimizer] Optimization systems initialized successfully");
    } catch (error) {
      console.error("[SceneOptimizer] Failed to initialize optimizer:", error);
    }
  };

  // 应用初始优化设置
  const applyInitialOptimizations = async (): Promise<void> => {
    if (!lodManagerRef.current || !scene || !engine) return;

    // 获取推荐的场景设置
    const recommendedSettings = lodManagerRef.current.getRecommendedSceneSettings();

    // 应用阴影设置
    if (!recommendedSettings.shadowsEnabled) {
      scene.lights.forEach(light => {
        if ((light as any).setShadowGenerator) {
          (light as any).setShadowGenerator(null);
        }
      });
    }

    // 应用抗锯齿设置
    if (!recommendedSettings.antialiasing) {
      const renderingPipeline = scene.postProcesses;
      renderingPipeline.forEach(pp => {
        if (pp.name.includes("FXAA") || pp.name.includes("MSAA")) {
          pp.dispose();
        }
      });
    }

    // 设置渲染目标大小
    const canvas = engine.getRenderingCanvas();
    if (canvas) {
      const devicePixelRatio = lodManagerRef.current.getPerformanceStats().devicePerformanceLevel === "mobile" ? 1 : window.devicePixelRatio;
      engine.setHardwareScalingLevel(1 / Math.min(devicePixelRatio, 2));
    }

    console.log("[SceneOptimizer] Applied initial optimizations", recommendedSettings);
  };

  // 处理性能更新
  const handlePerformanceUpdate = (metrics: PerformanceMetrics): void => {
    const status = performanceMonitorRef.current?.getPerformanceStatus() || "good";
    const suggestions = performanceMonitorRef.current?.getOptimizationSuggestions() || [];

    setState(prev => ({
      ...prev,
      performanceMetrics: metrics,
      performanceStatus: status,
      optimizationSuggestions: suggestions,
    }));

    // 调用外部回调
    onPerformanceUpdate?.(metrics);
    onOptimizationSuggestions?.(suggestions);

    // 自动优化
    if (state.autoOptimizationEnabled && (status === "poor" || status === "critical")) {
      applyAutoOptimizations(metrics, suggestions);
    }
  };

  // 应用自动优化  
  const applyAutoOptimizations = (_metrics: PerformanceMetrics, suggestions: OptimizationSuggestion[]): void => {
    if (!lodManagerRef.current || !scene) return;

    console.log("[SceneOptimizer] Applying auto optimizations...");

    // 按优先级处理优化建议
    const highPrioritySuggestions = suggestions.filter(s => s.priority === "high");
    
    highPrioritySuggestions.forEach(suggestion => {
      switch (suggestion.type) {
        case "mesh":
          // 降低LOD级别
          lodManagerRef.current?.setPerformanceLevel("low");
          break;
        
        case "texture":
          // 降低纹理质量
          reduceTextureQuality();
          break;
        
        case "lighting":
          // 禁用实时阴影
          disableRealtimeShadows();
          break;
        
        case "particles":
          // 减少粒子数量
          reduceParticleCount();
          break;
        
        case "postprocessing":
          // 禁用后处理效果
          disablePostProcessing();
          break;
      }
    });

    setState(prev => ({
      ...prev,
      lodLevel: lodManagerRef.current?.getPerformanceStats().devicePerformanceLevel || prev.lodLevel,
    }));
  };

  // 降低纹理质量
  const reduceTextureQuality = (): void => {
    if (!scene) return;

    scene.textures.forEach(texture => {
      if (texture.level > 0.5) {
        texture.level = Math.max(0.5, texture.level - 0.2);
      }
    });
  };

  // 禁用实时阴影
  const disableRealtimeShadows = (): void => {
    if (!scene) return;

    scene.lights.forEach(light => {
      if ((light as any).getShadowGenerator) {
        const shadowGenerator = (light as any).getShadowGenerator();
        if (shadowGenerator) {
          shadowGenerator.dispose();
        }
      }
    });
  };

  // 减少粒子数量
  const reduceParticleCount = (): void => {
    if (!scene) return;

    scene.particleSystems.forEach(system => {
      const currentCapacity = system.getCapacity();
      // Note: updateFunction is not a standard property - would need custom implementation
      system.manualEmitCount = Math.max(50, Math.floor(currentCapacity * 0.5));
    });
  };

  // 禁用后处理效果
  const disablePostProcessing = (): void => {
    if (!scene) return;

    scene.postProcesses.forEach(postProcess => {
      if (!postProcess.name.includes("final")) {
        postProcess.dispose();
      }
    });
  };

  // 手动调整LOD级别
  const handleLODLevelChange = (level: "high" | "medium" | "low" | "mobile"): void => {
    if (!lodManagerRef.current) return;

    lodManagerRef.current.setPerformanceLevel(level);
    setState(prev => ({ ...prev, lodLevel: level }));
  };

  // 切换自动优化
  const toggleAutoOptimization = (): void => {
    setState(prev => ({
      ...prev,
      autoOptimizationEnabled: !prev.autoOptimizationEnabled,
    }));
  };

  // 导出性能报告
  const exportPerformanceReport = (): void => {
    if (!performanceMonitorRef.current) return;

    const report = performanceMonitorRef.current.exportPerformanceReport();
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement("a");
    link.href = url;
    link.download = `performance-report-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  };

  // 清理资源
  useEffect(() => {
    return () => {
      if (performanceMonitorRef.current) {
        performanceMonitorRef.current.dispose();
      }
      if (lodManagerRef.current) {
        lodManagerRef.current.dispose();
      }
      if (initializationTimeoutRef.current) {
        clearTimeout(initializationTimeoutRef.current);
      }
    };
  }, []);

  // 获取性能状态颜色
  const getStatusColor = (status: PerformanceStatus): string => {
    switch (status) {
      case "excellent": return "#22c55e";
      case "good": return "#84cc16";
      case "moderate": return "#eab308";
      case "poor": return "#f97316";
      case "critical": return "#ef4444";
      default: return "#6b7280";
    }
  };

  // 渲染调试面板
  const renderDebugPanel = (): React.ReactElement | null => {
    if (!showDebugPanel || !state.performanceMetrics) return null;

    const { performanceMetrics, performanceStatus, optimizationSuggestions } = state;

    return (
      <div className="fixed top-4 right-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-sm z-50 max-w-sm">
        <h3 className="font-bold mb-2">性能监控</h3>
        
        {/* 性能状态 */}
        <div className="mb-2">
          <span>状态: </span>
          <span style={{ color: getStatusColor(performanceStatus) }}>
            {performanceStatus.toUpperCase()}
          </span>
        </div>

        {/* 基础指标 */}
        <div className="grid grid-cols-2 gap-2 mb-2 text-xs">
          <div>FPS: {performanceMetrics.fps.toFixed(1)}</div>
          <div>帧时间: {performanceMetrics.frameTime.toFixed(1)}ms</div>
          <div>三角形: {performanceMetrics.triangleCount.toLocaleString()}</div>
          <div>网格: {performanceMetrics.activeMeshes}</div>
          <div>内存: {performanceMetrics.memoryUsage.percentage.toFixed(1)}%</div>
          <div>纹理: {performanceMetrics.textureMemory.toFixed(1)}MB</div>
        </div>

        {/* LOD控制 */}
        <div className="mb-2">
          <label className="block text-xs mb-1">LOD级别:</label>
          <select
            value={state.lodLevel}
            onChange={(e) => handleLODLevelChange(e.target.value as any)}
            className="w-full bg-gray-700 text-white text-xs p-1 rounded"
          >
            <option value="high">高质量</option>
            <option value="medium">中等质量</option>
            <option value="low">低质量</option>
            <option value="mobile">移动端</option>
          </select>
        </div>

        {/* 自动优化开关 */}
        <div className="mb-2">
          <label className="flex items-center text-xs">
            <input
              type="checkbox"
              checked={state.autoOptimizationEnabled}
              onChange={toggleAutoOptimization}
              className="mr-2"
            />
            自动优化
          </label>
        </div>

        {/* 优化建议 */}
        {optimizationSuggestions.length > 0 && (
          <div className="mb-2">
            <h4 className="text-xs font-semibold mb-1">优化建议:</h4>
            <div className="max-h-20 overflow-y-auto">
              {optimizationSuggestions.slice(0, 3).map((suggestion, index) => (
                <div key={index} className="text-xs mb-1 p-1 bg-gray-700 rounded">
                  <div className="font-medium">{suggestion.title}</div>
                  <div className="text-gray-300">{suggestion.action}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 导出按钮 */}
        <button
          onClick={exportPerformanceReport}
          className="w-full bg-blue-600 text-white text-xs py-1 px-2 rounded hover:bg-blue-700"
        >
          导出性能报告
        </button>
      </div>
    );
  };

  return (
    <>
      {renderDebugPanel()}
    </>
  );
};

export default SceneOptimizer;
export type { SceneOptimizerProps, OptimizerState };