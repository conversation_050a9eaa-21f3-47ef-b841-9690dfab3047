import React, { useState, useEffect, useCallback } from 'react';

// 内容审核状态枚举
interface ModerationStats {
  total_messages: number;
  active_messages: number;
  blocked_messages: number;
  block_rate: number;
  period_days: number;
}

interface Message {
  id: string;
  memorial_space_id: string;
  author_id: string;
  content: string;
  is_anonymous: boolean;
  created_at: string;
  updated_at: string;
  author_name?: string;
  author_avatar?: string;
}

const ContentModerationPanel: React.FC = () => {
  const [stats, setStats] = useState<ModerationStats | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSpaceId, setSelectedSpaceId] = useState<string>('');
  const [testContent, setTestContent] = useState('');
  const [testResult, setTestResult] = useState<any>(null);

  // 获取审核统计
  const fetchModerationStats = useCallback(async () => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/v1/admin/moderation/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('获取审核统计失败:', error);
    }
  }, []);

  // 获取留言列表
  const fetchMessages = async (spaceId: string) => {
    if (!spaceId) return;

    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/v1/memorial-spaces/${spaceId}/messages`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setMessages(data.items || []);
      }
    } catch (error) {
      console.error('获取留言失败:', error);
    }
  };

  // 测试内容审核
  const testContentModeration = async () => {
    if (!testContent.trim()) return;

    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/v1/admin/test-moderation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          content: testContent.trim(),
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setTestResult(result);
      }
    } catch (error) {
      console.error('测试审核失败:', error);
    }
  };

  // 删除留言
  const deleteMessage = async (messageId: string) => {
    if (!selectedSpaceId) return;

    if (!confirm('确定要删除这条留言吗？')) return;

    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/v1/memorial-spaces/${selectedSpaceId}/messages/${messageId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        alert('留言删除成功');
        await fetchMessages(selectedSpaceId);
      } else {
        alert('删除失败');
      }
    } catch (error) {
      console.error('删除留言失败:', error);
      alert('删除失败');
    }
  };

  useEffect(() => {
    setLoading(false);
    // 注意：在实际应用中，这里应该检查用户是否有管理员权限
    fetchModerationStats();
  }, [fetchModerationStats]);

  if (loading) {
    return <div className="p-6">加载中...</div>;
  }

  return (
    <div className="p-6 bg-gray-100 min-h-screen">
      <h1 className="text-2xl font-bold mb-6">内容审核管理面板</h1>

      {/* 统计信息 */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-700">总留言数</h3>
            <p className="text-2xl font-bold text-blue-600">{stats.total_messages}</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-700">活跃留言</h3>
            <p className="text-2xl font-bold text-green-600">{stats.active_messages}</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-700">被屏蔽留言</h3>
            <p className="text-2xl font-bold text-red-600">{stats.blocked_messages}</p>
          </div>
          <div className="bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-gray-700">屏蔽率</h3>
            <p className="text-2xl font-bold text-orange-600">{stats.block_rate.toFixed(1)}%</p>
          </div>
        </div>
      )}

      {/* 内容审核测试 */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-xl font-semibold mb-4">内容审核测试</h2>
        <div className="space-y-4">
          <textarea
            value={testContent}
            onChange={(e) => setTestContent(e.target.value)}
            placeholder="输入要测试的内容..."
            className="w-full p-3 border border-gray-300 rounded-lg resize-none h-24"
          />
          <button
            onClick={testContentModeration}
            disabled={!testContent.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400"
          >
            测试审核
          </button>
          
          {testResult && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <h3 className="font-semibold mb-2">审核结果:</h3>
              <div className="space-y-2">
                <p><strong>动作:</strong> 
                  <span className={`ml-2 px-2 py-1 rounded text-sm ${
                    testResult.action === 'allow' ? 'bg-green-100 text-green-800' :
                    testResult.action === 'review' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {testResult.action === 'allow' ? '允许' :
                     testResult.action === 'review' ? '需要审核' : '拒绝'}
                  </span>
                </p>
                <p><strong>置信度:</strong> {(testResult.confidence * 100).toFixed(1)}%</p>
                {testResult.reasons && testResult.reasons.length > 0 && (
                  <div>
                    <strong>原因:</strong>
                    <ul className="list-disc list-inside ml-4">
                      {testResult.reasons.map((reason: string, index: number) => (
                        <li key={index}>{reason}</li>
                      ))}
                    </ul>
                  </div>
                )}
                {testResult.filtered_content && testResult.filtered_content !== testContent && (
                  <div>
                    <strong>过滤后内容:</strong>
                    <p className="mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded">
                      {testResult.filtered_content}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 留言管理 */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">留言管理</h2>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            纪念空间ID
          </label>
          <div className="flex gap-2">
            <input
              type="text"
              value={selectedSpaceId}
              onChange={(e) => setSelectedSpaceId(e.target.value)}
              placeholder="输入纪念空间ID..."
              className="flex-1 p-2 border border-gray-300 rounded-lg"
            />
            <button
              onClick={() => fetchMessages(selectedSpaceId)}
              disabled={!selectedSpaceId.trim()}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400"
            >
              获取留言
            </button>
          </div>
        </div>

        {messages.length > 0 && (
          <div className="space-y-4">
            <h3 className="font-semibold">留言列表 ({messages.length} 条)</h3>
            <div className="space-y-3">
              {messages.map((message) => (
                <div key={message.id} className="p-4 border border-gray-200 rounded-lg">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium">
                          {message.is_anonymous ? '匿名用户' : (message.author_name || '未知用户')}
                        </span>
                        <span className="text-sm text-gray-500">
                          {new Date(message.created_at).toLocaleString()}
                        </span>
                      </div>
                      <p className="text-gray-800 mb-2">{message.content}</p>
                      <p className="text-xs text-gray-500">ID: {message.id}</p>
                    </div>
                    <button
                      onClick={() => deleteMessage(message.id)}
                      className="px-3 py-1 bg-red-500 text-white text-sm rounded hover:bg-red-600"
                    >
                      删除
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentModerationPanel;