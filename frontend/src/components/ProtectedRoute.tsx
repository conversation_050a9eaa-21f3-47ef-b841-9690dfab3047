// 受保护的路由组件
import { ReactNode } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "../hooks/useAuth";

interface ProtectedRouteProps {
  children: ReactNode;
  requireAuth?: boolean; // 默认为true，是否需要认证
  requireEmailVerification?: boolean; // 是否需要邮箱验证
  redirectTo?: string; // 自定义重定向路径
  roles?: string[]; // 允许的角色列表
}

function ProtectedRoute({
  children,
  requireAuth = true,
  requireEmailVerification = false,
  redirectTo,
  roles = [],
}: ProtectedRouteProps) {
  const { isAuthenticated, user, isLoading } = useAuth();
  const location = useLocation();

  // 加载中显示加载指示器
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-300">加载中...</p>
        </div>
      </div>
    );
  }

  // 需要认证但未登录
  if (requireAuth && !isAuthenticated) {
    const redirectPath = redirectTo || "/login";
    return (
      <Navigate
        to={`${redirectPath}?redirect=${encodeURIComponent(location.pathname + location.search)}`}
        replace
      />
    );
  }

  // 不需要认证但已登录（例如登录页面）
  if (!requireAuth && isAuthenticated) {
    const redirectPath = redirectTo || "/dashboard";
    return <Navigate to={redirectPath} replace />;
  }

  // 检查邮箱验证
  if (requireEmailVerification && user && !user.is_verified) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="max-w-md w-full mx-auto p-6 bg-gray-800 rounded-lg shadow-lg text-center">
          <div className="mb-4">
            <i className="fas fa-envelope text-primary text-4xl mb-4"></i>
            <h2 className="text-xl font-bold mb-2">验证您的邮箱</h2>
            <p className="text-gray-300 mb-4">
              请检查您的邮箱 <span className="font-semibold">{user.email}</span>{" "}
              并点击验证链接。
            </p>
          </div>
          <button
            onClick={() => {
              // TODO: 实现重发验证邮件
              console.log("重发验证邮件");
            }}
            className="bg-primary text-white px-4 py-2 rounded hover:bg-secondary transition-colors"
          >
            重发验证邮件
          </button>
        </div>
      </div>
    );
  }

  // 检查角色权限
  if (roles.length > 0 && user && !roles.includes(user.role)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900 text-white">
        <div className="text-center">
          <i className="fas fa-lock text-red-500 text-6xl mb-4"></i>
          <h2 className="text-2xl font-bold mb-2">访问被拒绝</h2>
          <p className="text-gray-300 mb-4">您没有权限访问此页面。</p>
          <button
            onClick={() => window.history.back()}
            className="bg-primary text-white px-4 py-2 rounded hover:bg-secondary transition-colors"
          >
            返回
          </button>
        </div>
      </div>
    );
  }

  // 所有检查通过，渲染子组件
  return <>{children}</>;
}

export default ProtectedRoute;
