import React, { useEffect, useRef } from "react";
import {
  Vector3,
  Mesh,
  MeshBuilder,
  Scene,
  StandardMaterial,
  Color3,
} from "@babylonjs/core";
import BabylonPhysicsSystem, {
  PhysicsBodyType,
  PhysicsShapeType,
} from "../utils/BabylonPhysicsSystem";

interface BabylonPhysicsObjectProps {
  scene: Scene;
  name: string;
  position?: Vector3;
  size?: Vector3;
  mass?: number;
  restitution?: number;
  friction?: number;
  color?: string;
  bodyType?: PhysicsBodyType;
  shapeType?: PhysicsShapeType;
  onCollision?: (collidedWith: string) => void;
}

/**
 * Babylon.js物理对象组件
 * 用于创建具有物理特性的3D对象
 */
const BabylonPhysicsObject: React.FC<BabylonPhysicsObjectProps> = ({
  scene,
  name,
  position = new Vector3(0, 0, 0),
  size = new Vector3(1, 1, 1),
  mass = 1,
  restitution = 0.2,
  friction = 0.5,
  color = "var(--color-surface)",
  bodyType = PhysicsBodyType.DYNAMIC,
  shapeType = PhysicsShapeType.BOX,
  onCollision,
}) => {
  const meshRef = useRef<Mesh | null>(null);
  const physicsSystem = BabylonPhysicsSystem;

  useEffect(() => {
    // 确保物理系统已初始化
    if (!physicsSystem.isInitialized()) {
      physicsSystem.init(scene);
    }

    // 创建网格
    let mesh: Mesh;

    let cylinder: Mesh;
    let topSphere: Mesh;
    let bottomSphere: Mesh;

    switch (shapeType) {
      case PhysicsShapeType.SPHERE:
        mesh = MeshBuilder.CreateSphere(
          name,
          { diameter: size.x, segments: 16 },
          scene,
        );
        break;
      case PhysicsShapeType.CYLINDER:
        mesh = MeshBuilder.CreateCylinder(
          name,
          { height: size.y, diameter: size.x, tessellation: 24 },
          scene,
        );
        break;
      case PhysicsShapeType.CAPSULE:
        // 创建胶囊体（使用圆柱体和两个半球）
        cylinder = MeshBuilder.CreateCylinder(
          `${name}_cylinder`,
          { height: size.y - size.x, diameter: size.x, tessellation: 24 },
          scene,
        );

        topSphere = MeshBuilder.CreateSphere(
          `${name}_topSphere`,
          { diameter: size.x, segments: 16 },
          scene,
        );
        topSphere.position.y = (size.y - size.x) / 2;

        bottomSphere = MeshBuilder.CreateSphere(
          `${name}_bottomSphere`,
          { diameter: size.x, segments: 16 },
          scene,
        );
        bottomSphere.position.y = -(size.y - size.x) / 2;

        // 合并网格
        mesh = Mesh.MergeMeshes(
          [cylinder, topSphere, bottomSphere],
          true,
          true,
          undefined,
          false,
          true,
        )!;
        mesh.name = name;
        break;
      case PhysicsShapeType.BOX:
      default:
        mesh = MeshBuilder.CreateBox(
          name,
          { width: size.x, height: size.y, depth: size.z },
          scene,
        );
    }

    // 设置位置
    mesh.position = position;

    // 创建材质
    const material = new StandardMaterial(`${name}_material`, scene);
    material.diffuseColor = Color3.FromHexString(color);
    mesh.material = material;

    // 添加物理特性
    physicsSystem.addBody(name, mesh, bodyType, shapeType, {
      mass: bodyType === PhysicsBodyType.STATIC ? 0 : mass,
      restitution,
      friction,
    });

    // 添加碰撞事件监听
    if (onCollision) {
      physicsSystem.on("collision_start", (...args: unknown[]) => {
        const event = args[0] as {
          bodyA?: { id: string };
          bodyB?: { id: string };
        };
        if (event && event.bodyA && event.bodyB && event.bodyA.id === name) {
          onCollision(event.bodyB.id);
        } else if (
          event &&
          event.bodyA &&
          event.bodyB &&
          event.bodyB.id === name
        ) {
          onCollision(event.bodyA.id);
        }
      });
    }

    // 保存引用
    meshRef.current = mesh;

    // 清理函数
    return () => {
      if (meshRef.current) {
        // 移除物理特性
        physicsSystem.removeBody(name);

        // 移除网格
        meshRef.current.dispose();
      }
    };
  }, [
    scene,
    physicsSystem,
    name,
    position,
    size,
    mass,
    restitution,
    friction,
    color,
    bodyType,
    shapeType,
    onCollision,
  ]);

  // 这个组件不渲染任何React DOM元素
  return null;
};

export default BabylonPhysicsObject;
