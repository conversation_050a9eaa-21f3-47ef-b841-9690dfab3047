/* Memorial 商业化数据分析Dashboard样式 */

.analyticsDashboard {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-primary, #ffffff);
}

/* 加载和错误状态 */
.loading, .error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: var(--text-secondary, #666);
}

.spinner {
  width: 50px;
  height: 50px;
  border: 5px solid var(--border-light, #e0e0e0);
  border-top: 5px solid var(--primary, #007AFF);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 头部区域 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--border-light, #e8e8e8);
}

.headerTitle h1 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary, #333);
}

.headerTitle p {
  margin: 0;
  font-size: 16px;
  color: var(--text-secondary, #666);
}

.controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.timeRangeSelector {
  padding: 8px 16px;
  border: 2px solid var(--border-light, #e8e8e8);
  border-radius: 8px;
  background: white;
  font-size: 14px;
  color: var(--text-primary, #333);
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.timeRangeSelector:focus {
  outline: none;
  border-color: var(--primary, #007AFF);
}

/* 标签导航 */
.tabNavigation {
  display: flex;
  gap: 4px;
  margin-bottom: 32px;
  background: var(--bg-secondary, #f8f9fa);
  padding: 4px;
  border-radius: 12px;
}

.tabButton {
  flex: 1;
  padding: 12px 20px;
  background: transparent;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary, #666);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.tabButton:hover {
  background: var(--bg-hover, #e8e8e8);
  color: var(--text-primary, #333);
}

.tabButton.active {
  background: white;
  color: var(--primary, #007AFF);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-weight: 600;
}

/* 内容区域 */
.content {
  min-height: 600px;
}

/* 概览标签页 */
.overviewTab {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.kpiCards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.kpiCard {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-light, #f0f0f0);
  transition: all 0.3s ease;
}

.kpiCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.kpiHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.kpiHeader h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary, #666);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.changeIndicator {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.changeIndicator.positive {
  background: rgba(40, 167, 69, 0.1);
  color: var(--success, #28a745);
}

.changeIndicator.negative {
  background: rgba(220, 53, 69, 0.1);
  color: var(--error, #dc3545);
}

.kpiValue {
  font-size: 36px;
  font-weight: 700;
  color: var(--text-primary, #333);
  margin-top: 8px;
}

.chartSection {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.chartSection h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.chartContainer {
  width: 100%;
  height: 300px;
  position: relative;
}

.chartPlaceholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: var(--bg-secondary, #f8f9fa);
  border-radius: 12px;
  border: 2px dashed var(--border-light, #e8e8e8);
}

.chartPlaceholder p {
  margin: 4px 0;
  color: var(--text-secondary, #666);
}

/* 收入分析标签页 */
.revenueTab {
  width: 100%;
}

.revenueGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.revenueOverview, .revenueBySource, .revenueByPlan {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.revenueOverview h3, .revenueBySource h3, .revenueByPlan h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.revenueStats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.revenueStat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-light, #f0f0f0);
}

.revenueStat:last-child {
  border-bottom: none;
}

.revenueStat .label {
  font-size: 14px;
  color: var(--text-secondary, #666);
}

.revenueStat .value {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.sourceList, .planList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.sourceItem, .planItem {
  padding: 16px 0;
  border-bottom: 1px solid var(--border-light, #f0f0f0);
}

.sourceItem:last-child, .planItem:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.sourceInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.sourceName {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #333);
}

.sourceGrowth {
  font-size: 12px;
  font-weight: 600;
  color: var(--success, #28a745);
}

.sourceValue {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.sourceValue span:first-child {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.percentage {
  font-size: 12px;
  color: var(--text-secondary, #666);
}

.progressBar {
  height: 6px;
  background: var(--bg-tertiary, #f0f0f0);
  border-radius: 3px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: linear-gradient(90deg, var(--primary, #007AFF), var(--primary-light, #66b3ff));
  border-radius: 3px;
  transition: width 0.3s ease;
}

.planHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.planName {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.planRevenue {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary, #007AFF);
}

.planStats {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: var(--text-secondary, #666);
}

/* 用户分析标签页 */
.usersTab {
  width: 100%;
}

.userMetricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.userOverview, .activeUsers, .keyMetrics {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.userOverview h3, .activeUsers h3, .keyMetrics h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.userStats, .activeUserStats {
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.userStat, .activeUserStat {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.userStat .value, .activeUserStat .value {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary, #007AFF);
}

.userStat .label, .activeUserStat .label {
  font-size: 12px;
  color: var(--text-secondary, #666);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metricsList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-light, #f0f0f0);
}

.metric:last-child {
  border-bottom: none;
}

.metric .label {
  font-size: 14px;
  color: var(--text-secondary, #666);
}

.metric .value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

/* 转化分析标签页 */
.conversionTab {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.conversionFunnel, .conversionBySource {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.conversionFunnel h3, .conversionBySource h3 {
  margin: 0 0 24px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.funnelSteps {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.funnelStep {
  position: relative;
  padding: 20px;
  background: var(--bg-secondary, #f8f9fa);
  border-radius: 12px;
  border-left: 4px solid var(--primary, #007AFF);
}

.stepInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stepName {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.stepUsers {
  font-size: 14px;
  color: var(--text-secondary, #666);
}

.stepMetrics {
  display: flex;
  gap: 20px;
  margin-bottom: 12px;
}

.conversionRate, .dropoff {
  font-size: 12px;
  font-weight: 500;
}

.conversionRate {
  color: var(--success, #28a745);
}

.dropoff {
  color: var(--error, #dc3545);
}

.funnelBar {
  height: 6px;
  background: linear-gradient(90deg, var(--primary, #007AFF), var(--primary-light, #66b3ff));
  border-radius: 3px;
  transition: width 0.3s ease;
}

.sourceConversions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.sourceConversion {
  padding: 16px;
  background: var(--bg-secondary, #f8f9fa);
  border-radius: 12px;
  text-align: center;
}

.sourceInfo {
  margin-bottom: 12px;
}

.sourceName {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #333);
  margin-bottom: 4px;
}

.sourceRate {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary, #007AFF);
}

.sourceNumbers {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-secondary, #666);
}

/* 产品分析标签页 */
.productsTab {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.virtualProducts, .subscriptionPlans {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.virtualProducts h3, .subscriptionPlans h3 {
  margin: 0 0 24px 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.productList, .plansList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.productItem {
  padding: 20px;
  background: var(--bg-secondary, #f8f9fa);
  border-radius: 12px;
  border: 1px solid var(--border-light, #e8e8e8);
}

.productInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.productName {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.productRating {
  font-size: 14px;
  color: var(--text-secondary, #666);
}

.productMetrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.productMetrics .metric {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: none;
}

.productMetrics .metric .label {
  display: block;
  font-size: 12px;
  color: var(--text-secondary, #666);
  margin-bottom: 4px;
}

.productMetrics .metric .value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.planItem {
  padding: 20px;
  background: var(--bg-secondary, #f8f9fa);
  border-radius: 12px;
  border: 1px solid var(--border-light, #e8e8e8);
}

.planInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.planInfo .planName {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

.subscribers {
  font-size: 14px;
  color: var(--primary, #007AFF);
  font-weight: 500;
}

.planMetrics {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.planMetrics .metric {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: none;
}

.planMetrics .metric .label {
  display: block;
  font-size: 12px;
  color: var(--text-secondary, #666);
  margin-bottom: 4px;
}

.planMetrics .metric .value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #333);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .analyticsDashboard {
    padding: 16px;
  }

  .kpiCards {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .revenueGrid {
    grid-template-columns: 1fr;
  }

  .userMetricsGrid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .tabNavigation {
    flex-direction: column;
  }

  .tabButton {
    text-align: left;
  }

  .kpiCards {
    grid-template-columns: 1fr;
  }

  .userStats, .activeUserStats {
    flex-direction: column;
    gap: 16px;
  }

  .productMetrics, .planMetrics {
    grid-template-columns: 1fr;
  }

  .sourceConversions {
    grid-template-columns: 1fr;
  }

  .sourceNumbers {
    flex-direction: column;
    gap: 4px;
  }

  .kpiValue {
    font-size: 28px;
  }

  .chartContainer {
    height: 200px;
  }
}