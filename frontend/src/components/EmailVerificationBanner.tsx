import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface EmailVerificationBannerProps {
  email?: string;
  onResend?: () => void;
  onDismiss?: () => void;
}

const EmailVerificationBanner: React.FC<EmailVerificationBannerProps> = ({
  email,
  onResend,
  onDismiss
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isResending, setIsResending] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);

  const handleResend = async () => {
    if (!email || isResending) return;

    setIsResending(true);
    try {
      const API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL;
      const response = await fetch(`${API_BASE_URL}/auth/resend-verification`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (response.ok) {
        setResendSuccess(true);
        setTimeout(() => setResendSuccess(false), 3000);
        if (onResend) onResend();
      } else {
        throw new Error('重发失败');
      }
    } catch (error) {
      alert('重发验证邮件失败，请稍后再试');
    } finally {
      setIsResending(false);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    if (onDismiss) onDismiss();
  };

  if (!isVisible) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        className="bg-blue-50 border-l-4 border-blue-400 p-4 relative"
      >
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
            </svg>
          </div>
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium text-blue-800">
              请验证您的邮箱地址
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                我们已向 <span className="font-medium">{email}</span> 发送了验证邮件。
                请检查您的邮箱并点击验证链接来激活您的账户。
              </p>
              {resendSuccess && (
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="mt-2 text-green-600 font-medium"
                >
                  ✓ 验证邮件已重新发送
                </motion.p>
              )}
            </div>
            <div className="mt-4 flex space-x-3">
              <button
                onClick={handleResend}
                disabled={isResending}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isResending ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-3 w-3 text-blue-700" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    重发中...
                  </>
                ) : (
                  '重新发送验证邮件'
                )}
              </button>
              <button
                onClick={() => window.open('https://mail.google.com', '_blank')}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                打开邮箱
              </button>
            </div>
          </div>
          <div className="flex-shrink-0">
            <button
              onClick={handleDismiss}
              className="inline-flex rounded-md text-blue-400 hover:text-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <span className="sr-only">关闭</span>
              <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
        
        <div className="mt-3 text-xs text-blue-600">
          <details>
            <summary className="cursor-pointer hover:text-blue-800">
              没有收到邮件？点击查看帮助
            </summary>
            <div className="mt-2 space-y-1 text-blue-700">
              <p>• 检查垃圾邮件/促销邮件文件夹</p>
              <p>• 确认邮箱地址是否正确</p>
              <p>• 等待几分钟后再检查（邮件可能需要一些时间才能送达）</p>
              <p>• 检查邮箱是否已满</p>
            </div>
          </details>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default EmailVerificationBanner;