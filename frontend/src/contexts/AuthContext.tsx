// 认证上下文
import {
  createContext,
  useReducer,
  useEffect,
  useRef,
  ReactNode,
  useCallback,
} from "react";
import {
  AuthState,
  AuthContextType,
  User,
  LoginCredentials,
  RegisterData,
  AuthError,
} from "../types/auth";
import { authService } from "../services/authService";
import { REFRESH_THRESHOLD, CHECK_INTERVAL } from "./authConstants";

// 初始状态
const initialState: AuthState = {
  user: null,
  token: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// Action 类型
type AuthAction =
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_ERROR"; payload: AuthError | null }
  | {
      type: "LOGIN_SUCCESS";
      payload: { user: User; token: string; refreshToken?: string };
    }
  | { type: "LOGOUT" }
  | { type: "UPDATE_USER"; payload: Partial<User> }
  | { type: "CLEAR_ERROR" }
  | { type: "SET_TOKENS"; payload: { token: string; refreshToken?: string } };

// Reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case "SET_LOADING":
      return { ...state, isLoading: action.payload };

    case "SET_ERROR":
      return { ...state, error: action.payload, isLoading: false };

    case "LOGIN_SUCCESS":
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken || null,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };

    case "LOGOUT":
      return {
        ...initialState,
      };

    case "UPDATE_USER":
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };

    case "CLEAR_ERROR":
      return { ...state, error: null };

    case "SET_TOKENS":
      return {
        ...state,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken || state.refreshToken,
      };

    default:
      return state;
  }
}

// 创建 Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider 组件
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isRefreshingRef = useRef<boolean>(false);

  // 登出函数
  const logout = useCallback(async (): Promise<void> => {
    try {
      await authService.logout();
    } catch (error) {
      console.error("登出请求失败:", error);
    } finally {
      dispatch({ type: "LOGOUT" });
    }
  }, []);

  // 刷新认证
  const refreshAuth = useCallback(async (): Promise<boolean> => {
    try {
      const tokenResponse = await authService.refreshToken();
      dispatch({
        type: "SET_TOKENS",
        payload: {
          token: tokenResponse.access_token,
          refreshToken: tokenResponse.refresh_token,
        },
      });
      return true;
    } catch (error) {
      console.error("刷新token失败:", error);
      logout();
      return false;
    }
  }, [logout]);

  // 初始化认证状态
  useEffect(() => {
    const initializeAuth = async () => {
      dispatch({ type: "SET_LOADING", payload: true });

      try {
        const token = localStorage.getItem("authToken");
        const refreshToken = localStorage.getItem("refreshToken");

        if (token && refreshToken) {
          try {
            const user = await authService.getCurrentUser();
            dispatch({
              type: "LOGIN_SUCCESS",
              payload: { user, token, refreshToken },
            });
          } catch (error) {
            console.error("获取用户信息失败，可能token已过期:", error);
            // 尝试刷新token
            try {
              await refreshAuth();
            } catch (refreshError) {
              console.error("刷新token失败:", refreshError);
              logout();
            }
          }
        }
      } catch (error) {
        console.error("初始化认证状态失败:", error);
        logout();
      } finally {
        dispatch({ type: "SET_LOADING", payload: false });
      }
    };

    initializeAuth();
  }, [refreshAuth, logout]);

  // Token自动刷新机制
  useEffect(() => {
    if (!state.isAuthenticated) {
      // 清理定时器
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    // 检查token是否需要刷新
    const checkTokenExpiry = async () => {
      if (isRefreshingRef.current) {
        return; // 防止并发刷新
      }

      const expiresAtStr = localStorage.getItem("tokenExpiresAt");
      if (!expiresAtStr) {
        return;
      }

      const expiresAt = parseInt(expiresAtStr, 10);
      const now = Date.now();
      const timeUntilExpiry = expiresAt - now;

      // 如果token即将在5分钟内过期，尝试刷新
      if (timeUntilExpiry <= REFRESH_THRESHOLD && timeUntilExpiry > 0) {
        console.log("Token即将过期，尝试刷新...");
        isRefreshingRef.current = true;

        try {
          await refreshAuth();
          console.log("Token刷新成功");
        } catch (error) {
          console.error("Token刷新失败:", error);
          logout();
        } finally {
          isRefreshingRef.current = false;
        }
      } else if (timeUntilExpiry <= 0) {
        // Token已过期
        console.log("Token已过期，登出用户");
        logout();
      }
    };

    // 立即检查一次
    checkTokenExpiry();

    // 设置定时检查
    intervalRef.current = setInterval(checkTokenExpiry, CHECK_INTERVAL);

    // 清理函数
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [state.isAuthenticated, refreshAuth, logout]);

  // 监听页面可见性变化
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && state.isAuthenticated) {
        const expiresAtStr = localStorage.getItem("tokenExpiresAt");
        if (expiresAtStr) {
          const expiresAt = parseInt(expiresAtStr, 10);
          const now = Date.now();

          if (now >= expiresAt) {
            console.log("页面激活时发现token已过期，登出用户");
            logout();
          }
        }
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    window.addEventListener("focus", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("focus", handleVisibilityChange);
    };
  }, [state.isAuthenticated, logout]);

  // 登录
  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    dispatch({ type: "SET_LOADING", payload: true });
    dispatch({ type: "CLEAR_ERROR" });

    try {
      const tokenResponse = await authService.login(credentials) as any;
      const user = await authService.getCurrentUser();

      dispatch({
        type: "LOGIN_SUCCESS",
        payload: {
          user,
          token: tokenResponse.access_token,
          refreshToken: tokenResponse.refresh_token,
        },
      });

      return true;
    } catch (error) {
      const authError: AuthError = {
        message: error instanceof Error ? error.message : "登录失败",
      };
      dispatch({ type: "SET_ERROR", payload: authError });
      return false;
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  };

  // 注册
  const register = async (registerData: RegisterData): Promise<boolean> => {
    dispatch({ type: "SET_LOADING", payload: true });
    dispatch({ type: "CLEAR_ERROR" });

    try {
      const response = await authService.register(registerData) as any;
      const user = await authService.getCurrentUser();

      dispatch({
        type: "LOGIN_SUCCESS",
        payload: {
          user,
          token: response.access_token,
          refreshToken: response.refresh_token,
        },
      });

      return true;
    } catch (error) {
      const authError: AuthError = {
        message: error instanceof Error ? error.message : "注册失败",
      };
      dispatch({ type: "SET_ERROR", payload: authError });
      return false;
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  };

  // 更新用户信息
  const updateUser = async (userData: Partial<User>): Promise<boolean> => {
    dispatch({ type: "SET_LOADING", payload: true });
    dispatch({ type: "CLEAR_ERROR" });

    try {
      // TODO: Implement updateProfile method in authService
      console.log('Profile update requested:', userData);
      dispatch({ type: "UPDATE_USER", payload: userData });
      return true;
    } catch (error) {
      const authError: AuthError = {
        message: error instanceof Error ? error.message : "更新用户信息失败",
      };
      dispatch({ type: "SET_ERROR", payload: authError });
      return false;
    } finally {
      dispatch({ type: "SET_LOADING", payload: false });
    }
  };

  // 清除错误
  const clearError = (): void => {
    dispatch({ type: "CLEAR_ERROR" });
  };

  // 设置加载状态
  const setLoading = (loading: boolean): void => {
    dispatch({ type: "SET_LOADING", payload: loading });
  };

  // Context 值
  const value: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    refreshAuth,
    updateUser,
    clearError,
    setLoading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export default AuthContext;