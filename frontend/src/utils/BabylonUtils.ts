import {
  Scene,
  Engine,
  Vector3,
  Color3,
  Color4,
  ArcRotateCamera,
  HemisphericLight,
  DirectionalLight,
  ShadowGenerator,
  MeshBuilder,
  StandardMaterial,
  Texture,
  CubeTexture,
  SceneLoader,
  AbstractMesh,
  Mesh,
  ParticleSystem,
  Sound,
  EnvironmentHelper,
  PhysicsAggregate,
  PhysicsShapeType,
} from "@babylonjs/core";
import "@babylonjs/loaders";

/**
 * Babylon.js工具类
 * 提供常用的Babylon.js操作方法
 */
export class BabylonUtils {
  /**
   * 创建默认场景
   * @param engine Babylon.js引擎实例
   * @param canvas 画布元素
   * @returns 创建的场景
   */
  static createDefaultScene(engine: Engine, canvas: HTMLCanvasElement): Scene {
    // 创建场景
    const scene = new Scene(engine);

    // 设置场景背景色
    scene.clearColor = new Color4(0.2, 0.2, 0.3, 1);

    // 创建相机
    const camera = new ArcRotateCamera(
      "camera",
      -Math.PI / 2,
      Math.PI / 3,
      10,
      Vector3.Zero(),
      scene,
    );
    camera.attachControl(canvas, true);
    camera.wheelPrecision = 50;
    camera.lowerRadiusLimit = 3;
    camera.upperRadiusLimit = 20;

    // 创建光源
    const hemisphericLight = new HemisphericLight(
      "hemisphericLight",
      new Vector3(0, 1, 0),
      scene,
    );
    hemisphericLight.intensity = 0.7;

    const directionalLight = new DirectionalLight(
      "directionalLight",
      new Vector3(-1, -2, -1),
      scene,
    );
    directionalLight.position = new Vector3(5, 10, 5);
    directionalLight.intensity = 0.8;

    // 创建阴影生成器
    const shadowGenerator = new ShadowGenerator(1024, directionalLight);
    shadowGenerator.useBlurExponentialShadowMap = true;
    shadowGenerator.blurKernel = 32;

    // 创建地面
    const ground = MeshBuilder.CreateGround(
      "ground",
      { width: 20, height: 20, subdivisions: 2 },
      scene,
    );
    ground.receiveShadows = true;

    const groundMaterial = new StandardMaterial("groundMaterial", scene);
    groundMaterial.diffuseColor = new Color3(0.8, 0.8, 0.8);
    groundMaterial.specularColor = new Color3(0.1, 0.1, 0.1);
    ground.material = groundMaterial;

    return scene;
  }

  /**
   * 加载GLTF模型
   * @param scene 场景实例
   * @param rootUrl 模型根URL
   * @param fileName 模型文件名
   * @param onSuccess 加载成功回调
   * @param onError 加载失败回调
   */
  static loadGLTFModel(
    scene: Scene,
    rootUrl: string,
    fileName: string,
    onSuccess?: (meshes: AbstractMesh[]) => void,
    onError?: (error: { message: string; exception: Error }) => void,
  ): void {
    SceneLoader.ImportMesh(
      "",
      rootUrl,
      fileName,
      scene,
      (meshes) => {
        if (onSuccess) {
          onSuccess(meshes);
        }
      },
      null,
      (_scene, message, exception) => {
        // scene parameter is unused
        if (onError) {
          onError({ message, exception: exception as Error });
        }
      },
    );
  }

  /**
   * 创建天空盒
   * @param scene 场景实例
   * @param texturePath 纹理路径
   * @returns 创建的天空盒网格
   */
  static createSkybox(scene: Scene, texturePath: string): Mesh {
    const skybox = MeshBuilder.CreateBox("skyBox", { size: 1000 }, scene);
    const skyboxMaterial = new StandardMaterial("skyBoxMaterial", scene);
    skyboxMaterial.backFaceCulling = false;
    skyboxMaterial.reflectionTexture = new CubeTexture(texturePath, scene);
    skyboxMaterial.reflectionTexture.coordinatesMode = Texture.SKYBOX_MODE;
    skyboxMaterial.diffuseColor = new Color3(0, 0, 0);
    skyboxMaterial.specularColor = new Color3(0, 0, 0);
    skybox.material = skyboxMaterial;

    return skybox;
  }

  /**
   * 创建环境
   * @param scene 场景实例
   * @param options 环境选项
   * @returns 环境助手实例
   */
  static createEnvironment(
    scene: Scene,
    options?: {
      skyboxSize?: number;
      skyboxTexture?: string;
      groundColor?: Color3;
      enableGroundShadow?: boolean;
      groundSize?: number;
    },
  ): EnvironmentHelper | null {
    const helper = scene.createDefaultEnvironment({
      skyboxSize: options?.skyboxSize || 100,
      skyboxTexture: options?.skyboxTexture,
      groundColor: options?.groundColor || new Color3(0.8, 0.8, 0.8),
      enableGroundShadow:
        options?.enableGroundShadow !== undefined
          ? options.enableGroundShadow
          : true,
      groundSize: options?.groundSize || 100,
    });

    return helper;
  }

  /**
   * 创建粒子系统
   * @param scene 场景实例
   * @param emitter 发射器网格
   * @param options 粒子系统选项
   * @returns 创建的粒子系统
   */
  static createParticleSystem(
    scene: Scene,
    emitter: AbstractMesh | Vector3,
    options?: {
      name?: string;
      capacity?: number;
      texturePath?: string;
      minSize?: number;
      maxSize?: number;
      minLifeTime?: number;
      maxLifeTime?: number;
      emitRate?: number;
      color1?: Color4;
      color2?: Color4;
      colorDead?: Color4;
    },
  ): ParticleSystem {
    const name = options?.name || "particles";
    const capacity = options?.capacity || 2000;

    const particleSystem = new ParticleSystem(name, capacity, scene);

    // 设置粒子纹理
    if (options?.texturePath) {
      particleSystem.particleTexture = new Texture(options.texturePath, scene);
    }

    // 设置发射器
    particleSystem.emitter = emitter;

    // 设置粒子属性
    particleSystem.minSize = options?.minSize || 0.1;
    particleSystem.maxSize = options?.maxSize || 0.5;
    particleSystem.minLifeTime = options?.minLifeTime || 0.5;
    particleSystem.maxLifeTime = options?.maxLifeTime || 2.0;
    particleSystem.emitRate = options?.emitRate || 100;

    // 设置粒子颜色
    particleSystem.color1 = options?.color1 || new Color4(1, 1, 1, 1);
    particleSystem.color2 = options?.color2 || new Color4(1, 1, 1, 1);
    particleSystem.colorDead = options?.colorDead || new Color4(0, 0, 0, 0);

    // 启动粒子系统
    particleSystem.start();

    return particleSystem;
  }

  /**
   * 为网格添加物理属性
   * @param mesh 网格
   * @param scene 场景实例
   * @param options 物理选项
   * @returns 物理聚合体
   */
  static addPhysicsToMesh(
    mesh: Mesh,
    scene: Scene,
    options?: {
      mass?: number;
      restitution?: number;
      friction?: number;
      shapeType?: PhysicsShapeType;
    },
  ): PhysicsAggregate {
    const mass = options?.mass || 1;
    const restitution = options?.restitution || 0.2;
    const friction = options?.friction || 0.2;
    const shapeType = options?.shapeType || PhysicsShapeType.MESH;

    // 创建物理聚合体
    const aggregate = new PhysicsAggregate(
      mesh,
      shapeType,
      { mass, restitution, friction },
      scene,
    );

    return aggregate;
  }

  /**
   * 播放音效
   * @param scene 场景实例
   * @param soundUrl 音效URL
   * @param options 音效选项
   * @returns 创建的音效
   */
  static playSound(
    scene: Scene,
    soundUrl: string,
    options?: {
      name?: string;
      loop?: boolean;
      autoplay?: boolean;
      volume?: number;
      spatialSound?: boolean;
      position?: Vector3;
    },
  ): Sound {
    const name = options?.name || "sound";
    const loop = options?.loop !== undefined ? options.loop : false;
    const autoplay = options?.autoplay !== undefined ? options.autoplay : true;
    const volume = options?.volume || 1.0;

    const sound = new Sound(name, soundUrl, scene, null, {
      loop,
      autoplay,
      volume,
    });

    // 设置为空间音效
    if (options?.spatialSound && options?.position) {
      sound.setPosition(options.position);
      sound.setDirectionalCone(90, 180, 0);
      sound.setLocalDirectionToMesh(new Vector3(1, 0, 0));
      sound.attachToMesh(
        MeshBuilder.CreateSphere("soundEmitter", { diameter: 0.1 }, scene),
      );
    }

    return sound;
  }
}

export default BabylonUtils;
