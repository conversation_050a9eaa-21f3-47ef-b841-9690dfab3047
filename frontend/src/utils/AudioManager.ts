/**
 * 音频管理器类，用于控制应用程序中的所有音频
 */

export enum AudioType {
  MUSIC = "music", // 背景音乐
  SOUND_EFFECT = "sfx", // 音效
  AMBIENT = "ambient", // 环境音
  VOICE = "voice", // 语音
}

class AudioManagerClass {
  private masterVolume: number = 1.0;
  private muted: boolean = false;
  private volumeByType: Map<AudioType, number> = new Map();
  private mutedByType: Map<AudioType, boolean> = new Map();
  private activeAudio: Map<string, HTMLAudioElement> = new Map();

  constructor() {
    // 初始化各类型音量
    this.volumeByType.set(AudioType.MUSIC, 0.7);
    this.volumeByType.set(AudioType.SOUND_EFFECT, 1.0);
    this.volumeByType.set(AudioType.AMBIENT, 0.5);
    this.volumeByType.set(AudioType.VOICE, 1.0);

    // 初始化各类型静音状态
    this.mutedByType.set(AudioType.MUSIC, false);
    this.mutedByType.set(AudioType.SOUND_EFFECT, false);
    this.mutedByType.set(AudioType.AMBIENT, false);
    this.mutedByType.set(AudioType.VOICE, false);
  }

  // 获取主音量
  getMasterVolume(): number {
    return this.masterVolume;
  }

  // 设置主音量
  setMasterVolume(volume: number): void {
    this.masterVolume = Math.max(0, Math.min(1, volume));
    this.updateAllAudioVolumes();
  }

  // 获取是否静音
  isMuted(): boolean {
    return this.muted;
  }

  // 设置静音状态
  setMuted(muted: boolean): void {
    this.muted = muted;
    this.updateAllAudioVolumes();
  }

  // 获取指定类型的音量
  getVolumeByType(type: AudioType): number {
    return this.volumeByType.get(type) || 1.0;
  }

  // 设置指定类型的音量
  setVolumeByType(type: AudioType, volume: number): void {
    this.volumeByType.set(type, Math.max(0, Math.min(1, volume)));
    this.updateAudioVolumesByType(type);
  }

  // 获取指定类型是否静音
  isMutedByType(type: AudioType): boolean {
    return this.mutedByType.get(type) || false;
  }

  // 设置指定类型的静音状态
  setMutedByType(type: AudioType, muted: boolean): void {
    this.mutedByType.set(type, muted);
    this.updateAudioVolumesByType(type);
  }

  // 加载音频资源但不立即播放
  loadAudio(
    id: string,
    src: string,
    type: AudioType,
    loop: boolean = false,
    volume: number = 1.0,
  ): HTMLAudioElement {
    // 如果已经存在相同ID的音频，先停止它
    if (this.activeAudio.has(id)) {
      this.stop(id);
    }

    const audio = new Audio(src);
    audio.loop = loop;

    // 计算实际音量
    const typeVolume = this.getVolumeByType(type);
    const typeMuted = this.isMutedByType(type);
    const effectiveVolume =
      this.muted || typeMuted ? 0 : this.masterVolume * typeVolume * volume;

    audio.volume = effectiveVolume;

    // 存储音频引用
    this.activeAudio.set(id, audio);

    // 非循环音频播放完成后自动清理
    if (!loop) {
      audio.onended = () => {
        this.activeAudio.delete(id);
      };
    }

    return audio;
  }

  // 播放音频
  play(
    id: string,
    src: string,
    type: AudioType,
    loop: boolean = false,
    volume: number = 1.0,
  ): void {
    // 如果已经存在相同ID的音频，先停止它
    if (this.activeAudio.has(id)) {
      this.stop(id);
    }

    const audio = new Audio(src);
    audio.loop = loop;

    // 计算实际音量
    const typeVolume = this.getVolumeByType(type);
    const typeMuted = this.isMutedByType(type);
    const effectiveVolume =
      this.muted || typeMuted ? 0 : this.masterVolume * typeVolume * volume;

    audio.volume = effectiveVolume;
    audio
      .play()
      .catch((error) => console.error(`Failed to play audio ${id}:`, error));

    // 存储音频引用
    this.activeAudio.set(id, audio);

    // 非循环音频播放完成后自动清理
    if (!loop) {
      audio.onended = () => {
        this.activeAudio.delete(id);
      };
    }
  }

  // 停止播放指定ID的音频
  stop(id: string): void {
    const audio = this.activeAudio.get(id);
    if (audio) {
      audio.pause();
      audio.currentTime = 0;
      this.activeAudio.delete(id);
    }
  }

  // 暂停指定ID的音频
  pause(id: string): void {
    const audio = this.activeAudio.get(id);
    if (audio) {
      audio.pause();
    }
  }

  // 恢复播放指定ID的音频
  resume(id: string): void {
    const audio = this.activeAudio.get(id);
    if (audio) {
      audio
        .play()
        .catch((error) =>
          console.error(`Failed to resume audio ${id}:`, error),
        );
    }
  }

  // 停止所有音频
  stopAll(): void {
    this.activeAudio.forEach((audio) => {
      audio.pause();
      audio.currentTime = 0;
    });
    this.activeAudio.clear();
  }

  // 暂停所有音频
  pauseAll(): void {
    this.activeAudio.forEach((audio) => {
      audio.pause();
    });
  }

  // 恢复所有音频
  resumeAll(): void {
    this.activeAudio.forEach((audio, id) => {
      audio
        .play()
        .catch((error) =>
          console.error(`Failed to resume audio ${id}:`, error),
        );
    });
  }

  // 更新所有音频的音量
  private updateAllAudioVolumes(): void {
    this.activeAudio.forEach((audio, id) => {
      // 查找该音频的类型
      const type = this.findAudioType(id);
      if (type) {
        const typeVolume = this.getVolumeByType(type);
        const typeMuted = this.isMutedByType(type);
        audio.volume =
          this.muted || typeMuted ? 0 : this.masterVolume * typeVolume;
      } else {
        audio.volume = this.muted ? 0 : this.masterVolume;
      }
    });
  }

  // 更新指定类型的所有音频音量
  private updateAudioVolumesByType(type: AudioType): void {
    this.activeAudio.forEach((audio, id) => {
      const audioType = this.findAudioType(id);
      if (audioType === type) {
        const typeVolume = this.getVolumeByType(type);
        const typeMuted = this.isMutedByType(type);
        audio.volume =
          this.muted || typeMuted ? 0 : this.masterVolume * typeVolume;
      }
    });
  }

  // 查找音频的类型（这里简化实现，实际应用中可能需要更复杂的映射）
  private findAudioType(id: string): AudioType | null {
    // 这里假设ID格式为 "type_name"，例如 "music_background"
    const prefix = id.split("_")[0];

    switch (prefix) {
      case "music":
        return AudioType.MUSIC;
      case "sfx":
        return AudioType.SOUND_EFFECT;
      case "ambient":
        return AudioType.AMBIENT;
      case "voice":
        return AudioType.VOICE;
      default:
        return null;
    }
  }
}

// 创建单例实例
export const AudioManager = new AudioManagerClass();
