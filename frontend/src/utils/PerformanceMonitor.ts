// 性能监控器
import { Scene, Engine } from "@babylonjs/core";

// 性能指标接口
interface PerformanceMetrics {
  fps: number;
  frameTime: number;
  drawCalls: number;
  triangleCount: number;
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  textureMemory: number;
  activeMeshes: number;
  activeLights: number;
  activeParticles: number;
  shaderCompilationTime: number;
  loadingTime: number;
}

// 性能阈值配置
interface PerformanceThresholds {
  fps: {
    good: number;
    moderate: number;
    poor: number;
  };
  frameTime: {
    good: number;
    moderate: number;
    poor: number;
  };
  memoryUsage: {
    good: number;
    moderate: number;
    poor: number;
  };
}

// 性能状态
type PerformanceStatus = "excellent" | "good" | "moderate" | "poor" | "critical";

// 性能优化建议
interface OptimizationSuggestion {
  type: "mesh" | "texture" | "lighting" | "particles" | "postprocessing" | "general";
  priority: "high" | "medium" | "low";
  title: string;
  description: string;
  action: string;
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private scene: Scene | null = null;
  private engine: Engine | null = null;
  private metrics: PerformanceMetrics;
  private isMonitoring: boolean = false;
  private monitoringInterval: number | null = null;
  private callbacks: ((metrics: PerformanceMetrics) => void)[] = [];
  private performanceHistory: PerformanceMetrics[] = [];
  private maxHistorySize: number = 60; // 保存60个样本 (约1分钟的数据)
  private startTime: number = 0;

  // 性能阈值
  private thresholds: PerformanceThresholds = {
    fps: { good: 45, moderate: 30, poor: 20 },
    frameTime: { good: 16.67, moderate: 33.33, poor: 50 }, // ms
    memoryUsage: { good: 50, moderate: 70, poor: 85 }, // %
  };

  private constructor() {
    this.metrics = this.getDefaultMetrics();
  }

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // 初始化监控器
  public initialize(scene: Scene, engine: Engine): void {
    this.scene = scene;
    this.engine = engine;
    this.startTime = performance.now();
    
    // 设置Babylon.js性能监控
    this.setupBabylonMonitoring();
  }

  // 设置Babylon.js性能监控
  private setupBabylonMonitoring(): void {
    if (!this.scene || !this.engine) return;

    // 监听场景渲染事件
    this.scene.onBeforeRenderObservable.add(() => {
      if (this.isMonitoring) {
        this.updateMetrics();
      }
    });
  }

  // 开始监控
  public startMonitoring(intervalMs: number = 1000): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.monitoringInterval = window.setInterval(() => {
      this.updateMetrics();
      this.notifyCallbacks();
      this.addToHistory();
    }, intervalMs);

    console.log("[PerformanceMonitor] Started monitoring");
  }

  // 停止监控
  public stopMonitoring(): void {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    console.log("[PerformanceMonitor] Stopped monitoring");
  }

  // 更新性能指标
  private updateMetrics(): void {
    if (!this.scene || !this.engine) return;

    // 基础性能指标
    this.metrics.fps = this.engine.getFps();
    this.metrics.frameTime = 1000 / this.metrics.fps;

    // 渲染统计  
    // Note: getGlInfo is not available on AbstractEngine - would need Engine cast
    // const renderInfo = (this.scene.getEngine() as Engine).getGlInfo();
    this.metrics.drawCalls = this.scene.getActiveMeshes().length;
    this.metrics.triangleCount = this.calculateTriangleCount();

    // 内存使用情况
    this.metrics.memoryUsage = this.getMemoryUsage();
    this.metrics.textureMemory = this.calculateTextureMemory();

    // 场景对象统计
    this.metrics.activeMeshes = this.scene.getActiveMeshes().length;
    this.metrics.activeLights = this.scene.lights.length;
    this.metrics.activeParticles = this.calculateActiveParticles();

    // 加载时间
    this.metrics.loadingTime = performance.now() - this.startTime;
  }

  // 计算三角形数量
  private calculateTriangleCount(): number {
    if (!this.scene) return 0;

    let totalTriangles = 0;
    this.scene.meshes.forEach(mesh => {
      if (mesh.getTotalIndices) {
        totalTriangles += mesh.getTotalIndices() / 3;
      }
    });

    return Math.floor(totalTriangles);
  }

  // 获取内存使用情况
  private getMemoryUsage(): { used: number; total: number; percentage: number } {
    // Web环境中无法直接获取准确的内存信息
    // 这里使用估算值
    const estimatedUsed = this.estimateMemoryUsage();
    const estimatedTotal = this.estimateTotalMemory();
    
    return {
      used: estimatedUsed,
      total: estimatedTotal,
      percentage: (estimatedUsed / estimatedTotal) * 100,
    };
  }

  // 估算内存使用量
  private estimateMemoryUsage(): number {
    if (!this.scene) return 0;

    let estimated = 0;
    
    // 基于网格数量和复杂度估算
    estimated += this.scene.meshes.length * 0.5; // MB per mesh (rough estimate)
    
    // 基于纹理估算
    estimated += this.calculateTextureMemory();
    
    // 基于活跃对象估算
    estimated += this.scene.getActiveMeshes().length * 0.1;
    
    return estimated;
  }

  // 估算总内存
  private estimateTotalMemory(): number {
    // 估算设备总内存（基于navigator信息）
    const connection = (navigator as any).connection;
    if (connection && connection.effectiveType) {
      switch (connection.effectiveType) {
        case "4g": return 4000; // 4GB
        case "3g": return 2000; // 2GB
        case "2g": return 1000; // 1GB
        default: return 2000;
      }
    }
    
    return 2000; // 默认2GB
  }

  // 计算纹理内存使用
  private calculateTextureMemory(): number {
    if (!this.scene) return 0;

    let textureMemory = 0;
    this.scene.textures.forEach(texture => {
      if (texture.getSize) {
        const size = texture.getSize();
        // 估算纹理内存使用 (width * height * 4 bytes for RGBA)
        textureMemory += (size.width * size.height * 4) / (1024 * 1024); // Convert to MB
      }
    });

    return textureMemory;
  }

  // 计算活跃粒子数量
  private calculateActiveParticles(): number {
    if (!this.scene) return 0;

    let particleCount = 0;
    this.scene.particleSystems.forEach(system => {
      if (system.isStarted()) {
        particleCount += system.getCapacity();
      }
    });

    return particleCount;
  }

  // 获取性能状态
  public getPerformanceStatus(): PerformanceStatus {
    const metrics = this.metrics;
    let score = 0;

    // FPS评分
    if (metrics.fps >= this.thresholds.fps.good) score += 3;
    else if (metrics.fps >= this.thresholds.fps.moderate) score += 2;
    else if (metrics.fps >= this.thresholds.fps.poor) score += 1;

    // 帧时间评分
    if (metrics.frameTime <= this.thresholds.frameTime.good) score += 3;
    else if (metrics.frameTime <= this.thresholds.frameTime.moderate) score += 2;
    else if (metrics.frameTime <= this.thresholds.frameTime.poor) score += 1;

    // 内存使用评分
    if (metrics.memoryUsage.percentage <= this.thresholds.memoryUsage.good) score += 3;
    else if (metrics.memoryUsage.percentage <= this.thresholds.memoryUsage.moderate) score += 2;
    else if (metrics.memoryUsage.percentage <= this.thresholds.memoryUsage.poor) score += 1;

    // 根据总分确定状态
    if (score >= 8) return "excellent";
    if (score >= 6) return "good";
    if (score >= 4) return "moderate";
    if (score >= 2) return "poor";
    return "critical";
  }

  // 获取优化建议
  public getOptimizationSuggestions(): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];
    const metrics = this.metrics;

    // FPS相关建议
    if (metrics.fps < this.thresholds.fps.moderate) {
      suggestions.push({
        type: "general",
        priority: "high",
        title: "帧率过低",
        description: `当前帧率${metrics.fps.toFixed(1)}fps，低于推荐值`,
        action: "考虑降低渲染质量或简化场景复杂度",
      });
    }

    // 三角形数量建议
    if (metrics.triangleCount > 100000) {
      suggestions.push({
        type: "mesh",
        priority: "medium",
        title: "几何复杂度过高",
        description: `场景包含${metrics.triangleCount}个三角形`,
        action: "使用LOD系统或简化模型",
      });
    }

    // 内存使用建议
    if (metrics.memoryUsage.percentage > this.thresholds.memoryUsage.moderate) {
      suggestions.push({
        type: "general",
        priority: "high",
        title: "内存使用过高",
        description: `内存使用率${metrics.memoryUsage.percentage.toFixed(1)}%`,
        action: "释放未使用的资源或降低纹理质量",
      });
    }

    // 纹理内存建议
    if (metrics.textureMemory > 500) {
      suggestions.push({
        type: "texture",
        priority: "medium",
        title: "纹理内存占用过高",
        description: `纹理占用${metrics.textureMemory.toFixed(1)}MB内存`,
        action: "压缩纹理或降低纹理分辨率",
      });
    }

    // 光源数量建议
    if (metrics.activeLights > 8) {
      suggestions.push({
        type: "lighting",
        priority: "medium",
        title: "光源数量过多",
        description: `场景包含${metrics.activeLights}个光源`,
        action: "减少实时光源数量或使用烘焙光照",
      });
    }

    // 粒子系统建议
    if (metrics.activeParticles > 5000) {
      suggestions.push({
        type: "particles",
        priority: "low",
        title: "粒子数量过多",
        description: `活跃粒子数量${metrics.activeParticles}`,
        action: "减少粒子数量或优化粒子系统",
      });
    }

    return suggestions;
  }

  // 添加性能监控回调
  public addCallback(callback: (metrics: PerformanceMetrics) => void): void {
    this.callbacks.push(callback);
  }

  // 移除性能监控回调
  public removeCallback(callback: (metrics: PerformanceMetrics) => void): void {
    const index = this.callbacks.indexOf(callback);
    if (index > -1) {
      this.callbacks.splice(index, 1);
    }
  }

  // 通知所有回调
  private notifyCallbacks(): void {
    this.callbacks.forEach(callback => {
      try {
        callback(this.metrics);
      } catch (error) {
        console.error("[PerformanceMonitor] Callback error:", error);
      }
    });
  }

  // 添加到历史记录
  private addToHistory(): void {
    this.performanceHistory.push({ ...this.metrics });
    
    if (this.performanceHistory.length > this.maxHistorySize) {
      this.performanceHistory.shift();
    }
  }

  // 获取性能历史
  public getPerformanceHistory(): PerformanceMetrics[] {
    return [...this.performanceHistory];
  }

  // 获取当前指标
  public getCurrentMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // 获取默认指标
  private getDefaultMetrics(): PerformanceMetrics {
    return {
      fps: 0,
      frameTime: 0,
      drawCalls: 0,
      triangleCount: 0,
      memoryUsage: { used: 0, total: 0, percentage: 0 },
      textureMemory: 0,
      activeMeshes: 0,
      activeLights: 0,
      activeParticles: 0,
      shaderCompilationTime: 0,
      loadingTime: 0,
    };
  }

  // 重置监控器
  public reset(): void {
    this.stopMonitoring();
    this.metrics = this.getDefaultMetrics();
    this.performanceHistory = [];
    this.callbacks = [];
    this.startTime = performance.now();
  }

  // 导出性能报告
  public exportPerformanceReport(): object {
    const history = this.getPerformanceHistory();
    const current = this.getCurrentMetrics();
    const status = this.getPerformanceStatus();
    const suggestions = this.getOptimizationSuggestions();

    return {
      timestamp: new Date().toISOString(),
      currentMetrics: current,
      performanceStatus: status,
      optimizationSuggestions: suggestions,
      averageMetrics: this.calculateAverageMetrics(history),
      sessionDuration: performance.now() - this.startTime,
      totalSamples: history.length,
    };
  }

  // 计算平均指标
  private calculateAverageMetrics(history: PerformanceMetrics[]): Partial<PerformanceMetrics> {
    if (history.length === 0) return {};

    const sum = history.reduce((acc, metrics) => ({
      fps: acc.fps + metrics.fps,
      frameTime: acc.frameTime + metrics.frameTime,
      memoryUsage: acc.memoryUsage + metrics.memoryUsage.percentage,
    }), { fps: 0, frameTime: 0, memoryUsage: 0 });

    return {
      fps: sum.fps / history.length,
      frameTime: sum.frameTime / history.length,
      memoryUsage: {
        used: 0,
        total: 0,
        percentage: sum.memoryUsage / history.length,
      },
    };
  }

  // 清理资源
  public dispose(): void {
    this.stopMonitoring();
    this.scene = null;
    this.engine = null;
    this.callbacks = [];
    this.performanceHistory = [];
  }
}

export default PerformanceMonitor;
export type { PerformanceMetrics, PerformanceStatus, OptimizationSuggestion };