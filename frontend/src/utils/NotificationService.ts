// 通知类型枚举
export enum NotificationType {
  SUCCESS = 'success',
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info'
}

// 通知接口
export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  duration?: number;
  autoClose?: boolean;
  actions?: NotificationAction[];
  timestamp: Date;
}

// 通知操作接口
export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary';
}

// 通知管理器类
class NotificationManager {
  private static instance: NotificationManager;
  private notifications: Notification[] = [];
  private listeners: ((notifications: Notification[]) => void)[] = [];
  private nextId = 1;

  private constructor() {}

  public static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  // 添加通知
  public addNotification(notification: Omit<Notification, 'id' | 'timestamp'>): string {
    const id = `notification-${this.nextId++}`;
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: new Date(),
      duration: notification.duration ?? 5000,
      autoClose: notification.autoClose ?? true,
    };

    this.notifications.push(newNotification);
    this.notifyListeners();

    // 自动移除通知
    if (newNotification.autoClose && newNotification.duration && newNotification.duration > 0) {
      setTimeout(() => {
        this.removeNotification(id);
      }, newNotification.duration);
    }

    return id;
  }

  // 移除通知
  public removeNotification(id: string): void {
    this.notifications = this.notifications.filter(n => n.id !== id);
    this.notifyListeners();
  }

  // 清除所有通知
  public clearAll(): void {
    this.notifications = [];
    this.notifyListeners();
  }

  // 获取所有通知
  public getNotifications(): Notification[] {
    return [...this.notifications];
  }

  // 添加监听器
  public addListener(listener: (notifications: Notification[]) => void): () => void {
    this.listeners.push(listener);
    
    // 返回移除监听器的函数
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  // 通知所有监听器
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      listener([...this.notifications]);
    });
  }

  // 便捷方法：成功通知
  public success(title: string, message: string, options?: Partial<Notification>): string {
    return this.addNotification({
      type: NotificationType.SUCCESS,
      title,
      message,
      ...options,
    });
  }

  // 便捷方法：错误通知
  public error(title: string, message: string, options?: Partial<Notification>): string {
    return this.addNotification({
      type: NotificationType.ERROR,
      title,
      message,
      autoClose: false, // 错误通知默认不自动关闭
      ...options,
    });
  }

  // 便捷方法：警告通知
  public warning(title: string, message: string, options?: Partial<Notification>): string {
    return this.addNotification({
      type: NotificationType.WARNING,
      title,
      message,
      ...options,
    });
  }

  // 便捷方法：信息通知
  public info(title: string, message: string, options?: Partial<Notification>): string {
    return this.addNotification({
      type: NotificationType.INFO,
      title,
      message,
      ...options,
    });
  }

  // 邮件相关的专用方法
  public emailVerificationSent(email: string): string {
    return this.success(
      '验证邮件已发送',
      `我们已向 ${email} 发送了验证邮件，请查收并点击验证链接。`,
      {
        duration: 0,
        autoClose: false,
        actions: [
          {
            label: '重新发送',
            action: () => this.resendVerificationEmail(email),
            style: 'primary'
          },
          {
            label: '打开邮箱',
            action: () => window.open('https://mail.google.com', '_blank'),
            style: 'secondary'
          }
        ]
      }
    );
  }

  public passwordResetSent(email: string): string {
    return this.success(
      '密码重置邮件已发送',
      `我们已向 ${email} 发送了密码重置邮件，请查收并按照邮件中的指引重置密码。`,
      {
        duration: 0,
        autoClose: false,
        actions: [
          {
            label: '打开邮箱',
            action: () => window.open('https://mail.google.com', '_blank'),
            style: 'primary'
          }
        ]
      }
    );
  }

  public emailVerified(): string {
    return this.success(
      '邮箱验证成功',
      '您的邮箱已成功验证，现在可以正常使用归处的所有功能了！',
      {
        duration: 5000
      }
    );
  }

  public passwordResetSuccess(): string {
    return this.success(
      '密码重置成功',
      '您的密码已成功重置，请使用新密码登录。',
      {
        duration: 5000
      }
    );
  }

  // 重新发送验证邮件
  private async resendVerificationEmail(email: string): Promise<void> {
    try {
      const API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL;
      const response = await fetch(`${API_BASE_URL}/auth/resend-verification`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      if (response.ok) {
        this.success('验证邮件已重新发送', '请检查您的邮箱');
      } else {
        throw new Error('重发失败');
      }
    } catch (error) {
      this.error('重发失败', '无法重新发送验证邮件，请稍后再试');
    }
  }
}

// 导出单例实例
export default NotificationManager.getInstance();
export { NotificationManager };