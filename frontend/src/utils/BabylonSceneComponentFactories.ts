import {
  Mesh,
  <PERSON>sh<PERSON>uild<PERSON>,
  StandardMaterial,
  Color3,
  Vector3,
  TransformNode,
  PointLight,
  SpotLight,
  DirectionalLight,
  HemisphericLight,
  SceneLoader,
  Sound,
  ParticleSystem,
  Texture,
  Scene,
} from "@babylonjs/core";
import "@babylonjs/loaders";
import {
  BabylonSceneComponentFactory,
  BabylonSceneComponent,
} from "./BabylonSceneSystem";
// import ResourceManager, { ResourceType } from "./ResourceManager";

// 基础网格组件工厂
export class BasicMeshFactory implements BabylonSceneComponentFactory {
  type = "basic_mesh";

  async create(
    id: string,
    name: string,
    properties: Record<string, unknown>,
    scene: Scene,
  ): Promise<BabylonSceneComponent> {
    const {
      geometryType = "box",
      width = 1,
      height = 1,
      depth = 1,
      radius = 1,
      color = "var(--color-surface)",
    } = properties as {
      geometryType?: string;
      width?: number;
      height?: number;
      depth?: number;
      radius?: number;
      color?: string;
    };

    let mesh: Mesh;
    switch (geometryType) {
      case "box":
        mesh = MeshBuilder.CreateBox(name, { width, height, depth }, scene);
        break;
      case "sphere":
        mesh = MeshBuilder.CreateSphere(
          name,
          { diameter: (radius as number) * 2, segments: 32 },
          scene,
        );
        break;
      case "plane":
        mesh = MeshBuilder.CreateGround(name, { width, height }, scene);
        break;
      default:
        mesh = MeshBuilder.CreateBox(name, { width, height, depth }, scene);
    }

    // 创建材质
    const material = new StandardMaterial(`${name}_material`, scene);
    material.diffuseColor = Color3.FromHexString(color as string);
    mesh.material = material;

    // 设置阴影
    mesh.receiveShadows =
      properties.receiveShadows !== undefined
        ? (properties.receiveShadows as boolean)
        : true;

    return {
      id,
      name,
      type: this.type,
      node: mesh,
      position: new Vector3(0, 0, 0),
      rotation: new Vector3(0, 0, 0),
      scaling: new Vector3(1, 1, 1),
      visible: true,
      interactive: false,
      properties,
      children: [],
    };
  }
}

// 模型组件工厂
export class ModelFactory implements BabylonSceneComponentFactory {
  type = "model";

  async create(
    id: string,
    name: string,
    properties: Record<string, unknown>,
    scene: Scene,
  ): Promise<BabylonSceneComponent> {
    const { modelPath } = properties as { modelPath?: string };

    if (!modelPath) {
      throw new Error("模型路径未指定");
    }

    // 解析路径获取rootUrl和文件名
    const lastSlashIndex = (modelPath as string).lastIndexOf("/");
    const rootUrl = (modelPath as string).substring(0, lastSlashIndex + 1);
    const fileName = (modelPath as string).substring(lastSlashIndex + 1);

    // 创建一个根节点
    const rootNode = new TransformNode(name, scene);

    try {
      // 加载模型
      const result = await SceneLoader.ImportMeshAsync(
        "",
        rootUrl,
        fileName,
        scene,
      );

      // 将所有网格添加为根节点的子节点
      result.meshes.forEach((mesh) => {
        if (mesh !== result.meshes[0]) {
          // 跳过根网格
          mesh.parent = rootNode;
        }
      });

      // 设置阴影
      result.meshes.forEach((mesh) => {
        if (mesh instanceof Mesh) {
          mesh.receiveShadows =
            properties.receiveShadows !== undefined
              ? (properties.receiveShadows as boolean)
              : true;
        }
      });

      // 将模型添加到资源管理器
      // ResourceManager.addResource(modelPath as string, ResourceType.MODEL);

      return {
        id,
        name,
        type: this.type,
        node: rootNode,
        position: new Vector3(0, 0, 0),
        rotation: new Vector3(0, 0, 0),
        scaling: new Vector3(1, 1, 1),
        visible: true,
        interactive: false,
        properties: {
          ...properties,
          meshes: result.meshes,
        },
        children: [],
      };
    } catch (error) {
      console.error(`加载模型失败: ${modelPath}`, error);
      throw error;
    }
  }
}

// 光源组件工厂
export class LightFactory implements BabylonSceneComponentFactory {
  type = "light";

  async create(
    id: string,
    name: string,
    properties: Record<string, unknown>,
    scene: Scene,
  ): Promise<BabylonSceneComponent> {
    const {
      lightType = "point",
      color = "var(--color-surface)",
      intensity = 1,
      angle,
    } = properties as {
      lightType?: string;
      color?: string;
      intensity?: number;
      angle?: number;
    };

    let light: PointLight | SpotLight | DirectionalLight | HemisphericLight;

    switch (lightType) {
      case "hemispheric":
        light = new HemisphericLight(name, new Vector3(0, 1, 0), scene);
        break;
      case "directional":
        light = new DirectionalLight(name, new Vector3(0, -1, 0), scene);
        break;
      case "spot":
        light = new SpotLight(
          name,
          new Vector3(0, 5, 0),
          new Vector3(0, -1, 0),
          (angle as number) || Math.PI / 4,
          0.5,
          scene,
        );
        break;
      case "point":
      default:
        light = new PointLight(name, new Vector3(0, 0, 0), scene);
    }

    // 设置光源属性
    light.intensity = intensity as number;
    light.diffuse = Color3.FromHexString(color as string);

    // Lights are TransformNodes themselves
    const lightNode = light as unknown as TransformNode;

    let position = new Vector3(0, 0, 0);
    if ("position" in light) {
      position = light.position;
    }

    return {
      id,
      name,
      type: this.type,
      node: lightNode, // Lights are TransformNodes
      position: position,
      rotation: new Vector3(0, 0, 0), // Lights don't typically have rotation in the same way meshes do
      scaling: new Vector3(1, 1, 1),
      visible: true,
      interactive: false,
      properties,
      children: [],
    };
  }
}

// 音频组件工厂
export class AudioFactory implements BabylonSceneComponentFactory {
  type = "audio";

  async create(
    id: string,
    name: string,
    properties: Record<string, unknown>,
    scene: Scene,
  ): Promise<BabylonSceneComponent> {
    const {
      audioPath,
      loop = false,
      volume = 1,
      autoplay = false,
      spatialSound = false,
      maxDistance = 100,
    } = properties as {
      audioPath?: string;
      loop?: boolean;
      volume?: number;
      autoplay?: boolean;
      spatialSound?: boolean;
      maxDistance?: number;
    };

    if (!audioPath) {
      throw new Error("音频路径未指定");
    }

    // 创建音频对象
    const sound = new Sound(name, audioPath as string, scene, null, {
      loop: loop as boolean | undefined,
      autoplay: autoplay as boolean | undefined,
      volume: volume as number | undefined,
      spatialSound: spatialSound as boolean | undefined,
    });

    if (spatialSound) {
      sound.maxDistance = maxDistance as number;
    }

    // 创建一个节点来包含音频
    const node = new TransformNode(name, scene);

    return {
      id,
      name,
      type: this.type,
      node,
      position: new Vector3(0, 0, 0),
      rotation: new Vector3(0, 0, 0),
      scaling: new Vector3(1, 1, 1),
      visible: true,
      interactive: false,
      properties: {
        ...properties,
        sound,
      },
      children: [],
    };
  }
}

// 粒子系统组件工厂
export class ParticleSystemFactory implements BabylonSceneComponentFactory {
  type = "particle_system";

  async create(
    id: string,
    name: string,
    properties: Record<string, unknown>,
    scene: Scene,
  ): Promise<BabylonSceneComponent> {
    const {
      capacity = 2000,
      texturePath,
      emitRate = 500,
      minLifeTime = 0.3,
      maxLifeTime = 1.5,
      minSize = 0.1,
      maxSize = 0.5,
      minEmitPower = 1,
      maxEmitPower = 3,
      direction1 = new Vector3(-1, 1, -1),
      direction2 = new Vector3(1, 1, 1),
      color1 = "var(--color-surface)",
      color2 = "var(--color-surface)",
    } = properties as {
      capacity?: number;
      texturePath?: string;
      emitter?: Vector3 | Mesh;
      emitRate?: number;
      minLifeTime?: number;
      maxLifeTime?: number;
      minSize?: number;
      maxSize?: number;
      minEmitPower?: number;
      maxEmitPower?: number;
      direction1?: Vector3;
      direction2?: Vector3;
      color1?: string;
      color2?: string;
    };

    // 创建粒子系统
    const particleSystem = new ParticleSystem(name, capacity as number, scene);

    // 设置粒子纹理
    if (texturePath) {
      particleSystem.particleTexture = new Texture(
        texturePath as string,
        scene,
      );
    }

    // 设置粒子发射器
    const emitterNode = new TransformNode(name + "_emitter", scene);
    particleSystem.emitter = new Vector3(0, 0, 0); // Use Vector3 as emitter position

    // 设置粒子属性
    particleSystem.emitRate = emitRate as number;
    particleSystem.minLifeTime = minLifeTime as number;
    particleSystem.maxLifeTime = maxLifeTime as number;
    particleSystem.minSize = minSize as number;
    particleSystem.maxSize = maxSize as number;
    particleSystem.minEmitPower = minEmitPower as number;
    particleSystem.maxEmitPower = maxEmitPower as number;
    particleSystem.direction1 = direction1 as Vector3;
    particleSystem.direction2 = direction2 as Vector3;
    particleSystem.color1 = Color3.FromHexString(color1 as string).toColor4();
    particleSystem.color2 = Color3.FromHexString(color2 as string).toColor4();

    // 启动粒子系统
    if (properties.autoStart !== false) {
      particleSystem.start();
    }

    return {
      id,
      name,
      type: this.type,
      node: emitterNode, // Corrected from emitter to emitterNode
      position: new Vector3(0, 0, 0),
      rotation: new Vector3(0, 0, 0),
      scaling: new Vector3(1, 1, 1),
      visible: true,
      interactive: false,
      properties: {
        ...properties,
        particleSystem,
      },
      children: [],
    };
  }
}

// 导出所有工厂
export const factories = {
  BasicMeshFactory,
  ModelFactory,
  LightFactory,
  AudioFactory,
  ParticleSystemFactory,
};
