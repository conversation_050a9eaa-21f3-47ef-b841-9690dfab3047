import { Scene, Engine } from "@babylonjs/core";
import PerformanceMonitor, { PerformanceMetrics } from "./PerformanceMonitor";
import AdaptiveLODManager from "./AdaptiveLODManager";
import ScenePreloader from "./ScenePreloader";
import DeviceDetector from "./DeviceDetector";

// 性能管理器状态
interface PerformanceManagerState {
  isInitialized: boolean;
  autoOptimizationEnabled: boolean;
  currentQualityLevel: "low" | "medium" | "high";
  isOptimizing: boolean;
  lastOptimization: number;
}

// 质量设置配置
interface QualityConfig {
  targetFPS: number;
  shadowQuality: "low" | "medium" | "high" | "disabled";
  textureQuality: number; // 0.25 to 1.0
  particleCount: number;
  postProcessing: boolean;
  antialiasing: boolean;
  lodDistance: number;
}

// 质量级别配置
const qualityConfigs: Record<string, QualityConfig> = {
  low: {
    targetFPS: 30,
    shadowQuality: "disabled",
    textureQuality: 0.5,
    particleCount: 50,
    postProcessing: false,
    antialiasing: false,
    lodDistance: 15,
  },
  medium: {
    targetFPS: 45,
    shadowQuality: "low",
    textureQuality: 0.75,
    particleCount: 100,
    postProcessing: false,
    antialiasing: true,
    lodDistance: 25,
  },
  high: {
    targetFPS: 60,
    shadowQuality: "high",
    textureQuality: 1.0,
    particleCount: 200,
    postProcessing: true,
    antialiasing: true,
    lodDistance: 50,
  },
};

// 场景性能管理器
class ScenePerformanceManager {
  private static instance: ScenePerformanceManager;
  private scene: Scene | null = null;
  private engine: Engine | null = null;
  private performanceMonitor: PerformanceMonitor | null = null;
  private lodManager: AdaptiveLODManager | null = null;
  private state: PerformanceManagerState;
  private optimizationInterval: number | null = null;
  private preloadManager: typeof ScenePreloader;

  // 事件回调
  private onQualityChanged?: (level: string) => void;
  private onPerformanceUpdate?: (metrics: PerformanceMetrics) => void;

  private constructor() {
    this.state = {
      isInitialized: false,
      autoOptimizationEnabled: true,
      currentQualityLevel: "medium",
      isOptimizing: false,
      lastOptimization: 0,
    };
    this.preloadManager = ScenePreloader;
  }

  // 获取单例实例
  public static getInstance(): ScenePerformanceManager {
    if (!ScenePerformanceManager.instance) {
      ScenePerformanceManager.instance = new ScenePerformanceManager();
    }
    return ScenePerformanceManager.instance;
  }

  // 初始化性能管理器
  public initialize(scene: Scene, engine: Engine): void {
    if (this.state.isInitialized) {
      this.dispose();
    }

    this.scene = scene;
    this.engine = engine;

    // 初始化性能监控
    this.performanceMonitor = PerformanceMonitor.getInstance();
    this.performanceMonitor.initialize(scene, engine);

    // 初始化LOD管理器
    this.lodManager = AdaptiveLODManager.getInstance();

    // 检测设备并设置初始质量级别
    this.detectAndSetInitialQuality();

    // 设置性能监控回调 - 将在定期检查中获取指标

    // 设置定期优化检查
    this.startOptimizationLoop();

    this.state.isInitialized = true;
    console.log("[PerformanceManager] Initialized with quality level:", this.state.currentQualityLevel);
  }

  // 检测设备并设置初始质量级别
  private detectAndSetInitialQuality(): void {
    const deviceInfo = DeviceDetector.getDeviceInfo();
    
    if (deviceInfo.isMobile) {
      this.setQualityLevel("low");
    } else if (deviceInfo.performanceLevel === "low") {
      this.setQualityLevel("low");
    } else if (deviceInfo.performanceLevel === "medium") {
      this.setQualityLevel("medium");
    } else {
      this.setQualityLevel("high");
    }
  }

  // 设置质量级别
  public setQualityLevel(level: "low" | "medium" | "high"): void {
    if (!this.scene || !this.engine) return;

    const oldLevel = this.state.currentQualityLevel;
    this.state.currentQualityLevel = level;
    const config = qualityConfigs[level];

    console.log(`[PerformanceManager] Setting quality level: ${oldLevel} -> ${level}`);

    // 应用质量设置
    this.applyQualityConfig(config);

    // 通知质量级别变更
    if (this.onQualityChanged && oldLevel !== level) {
      this.onQualityChanged(level);
    }
  }

  // 应用质量配置
  private applyQualityConfig(config: QualityConfig): void {
    if (!this.scene || !this.engine) return;

    // 设置引擎目标FPS
    this.engine.setHardwareScalingLevel(
      config.textureQuality < 0.75 ? 1 / config.textureQuality : 1
    );

    // 设置阴影质量
    this.configureShadows(config.shadowQuality);

    // 配置纹理质量
    this.configureTextures(config.textureQuality);

    // 配置粒子系统
    this.configureParticles(config.particleCount);

    // 配置后处理
    this.configurePostProcessing(config.postProcessing);

    // 配置抗锯齿
    this.configureAntialiasing(config.antialiasing);

    // 配置LOD距离 - AdaptiveLODManager会自动管理距离
  }

  // 配置阴影
  private configureShadows(quality: "low" | "medium" | "high" | "disabled"): void {
    if (!this.scene) return;

    const lights = this.scene.lights;
    
    lights.forEach(light => {
      if ((light as any).setShadowMapSize) {
        switch (quality) {
          case "disabled":
            (light as any).shadowEnabled = false;
            break;
          case "low":
            (light as any).shadowEnabled = true;
            (light as any).setShadowMapSize(512);
            break;
          case "medium":
            (light as any).shadowEnabled = true;
            (light as any).setShadowMapSize(1024);
            break;
          case "high":
            (light as any).shadowEnabled = true;
            (light as any).setShadowMapSize(2048);
            break;
        }
      }
    });
  }

  // 配置纹理质量
  private configureTextures(quality: number): void {
    if (!this.scene) return;

    this.scene.textures.forEach(texture => {
      if ((texture as any).updateSamplingMode) {
        // 降低纹理采样质量以提升性能
        const samplingMode = quality >= 0.8 ? 2 : quality >= 0.6 ? 1 : 0;
        (texture as any).updateSamplingMode(samplingMode);
      }
    });
  }

  // 配置粒子系统
  private configureParticles(maxCount: number): void {
    if (!this.scene) return;

    this.scene.particleSystems.forEach(system => {
      const currentCapacity = system.getCapacity();
      if (currentCapacity > maxCount) {
        system.manualEmitCount = Math.min(maxCount, currentCapacity);
      }
    });
  }

  // 配置后处理
  private configurePostProcessing(enabled: boolean): void {
    if (!this.scene) return;

    if (!enabled) {
      // 禁用后处理效果
      this.scene.postProcesses.forEach(postProcess => {
        if (!postProcess.name.includes("final")) {
          (postProcess as any).setEnabled(false);
        }
      });
    } else {
      // 启用后处理效果
      this.scene.postProcesses.forEach(postProcess => {
        (postProcess as any).setEnabled(true);
      });
    }
  }

  // 配置抗锯齿
  private configureAntialiasing(enabled: boolean): void {
    if (!this.engine) return;

    // 设置MSAA抗锯齿
    try {
      if (enabled) {
        this.engine.setHardwareScalingLevel(1.0);
      } else {
        // 轻微降低渲染分辨率以提升性能
        this.engine.setHardwareScalingLevel(0.9);
      }
    } catch (error) {
      console.warn("[PerformanceManager] Failed to configure antialiasing:", error);
    }
  }

  // 检查并应用优化
  private checkAndApplyOptimizations(metrics: PerformanceMetrics): void {
    const now = Date.now();
    
    // 防止过于频繁的优化
    if (now - this.state.lastOptimization < 5000 || this.state.isOptimizing) {
      return;
    }

    const currentConfig = qualityConfigs[this.state.currentQualityLevel];
    
    // 如果FPS持续低于目标，降低质量
    if (metrics.fps < currentConfig.targetFPS * 0.8) {
      this.autoOptimizeDown();
    }
    // 如果FPS稳定高于目标，可以尝试提升质量
    else if (metrics.fps > currentConfig.targetFPS * 1.2 && 
             this.state.currentQualityLevel !== "high") {
      this.autoOptimizeUp();
    }
  }

  // 自动降低质量
  private autoOptimizeDown(): void {
    console.log("[PerformanceManager] Auto-optimizing down due to low FPS");
    
    if (this.state.currentQualityLevel === "high") {
      this.setQualityLevel("medium");
    } else if (this.state.currentQualityLevel === "medium") {
      this.setQualityLevel("low");
    }
    
    this.state.lastOptimization = Date.now();
  }

  // 自动提升质量
  private autoOptimizeUp(): void {
    console.log("[PerformanceManager] Auto-optimizing up due to good performance");
    
    if (this.state.currentQualityLevel === "low") {
      this.setQualityLevel("medium");
    } else if (this.state.currentQualityLevel === "medium") {
      this.setQualityLevel("high");
    }
    
    this.state.lastOptimization = Date.now();
  }

  // 开始优化循环
  private startOptimizationLoop(): void {
    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
    }

    this.optimizationInterval = window.setInterval(() => {
      if (this.performanceMonitor && this.onPerformanceUpdate) {
        // 获取当前性能指标并触发回调
        const metrics = this.getCurrentMetrics();
        if (metrics) {
          this.onPerformanceUpdate(metrics);
          
          // 如果启用自动优化，检查是否需要优化
          if (this.state.autoOptimizationEnabled) {
            this.checkAndApplyOptimizations(metrics);
          }
        }
      }
    }, 1000); // 每秒更新一次性能指标
  }

  // 预加载场景资源
  public async preloadScene(environmentId: string): Promise<void> {
    console.log("[PerformanceManager] Preloading scene:", environmentId);
    
    try {
      await this.preloadManager.preloadEnvironment(environmentId);
      console.log("[PerformanceManager] Scene preloading completed");
    } catch (error) {
      console.error("[PerformanceManager] Scene preloading failed:", error);
      throw error;
    }
  }

  // 设置事件回调
  public setCallbacks(callbacks: {
    onQualityChanged?: (level: string) => void;
    onPerformanceUpdate?: (metrics: PerformanceMetrics) => void;
  }): void {
    this.onQualityChanged = callbacks.onQualityChanged;
    this.onPerformanceUpdate = callbacks.onPerformanceUpdate;
  }

  // 获取当前性能指标
  public getCurrentMetrics(): PerformanceMetrics | null {
    if (!this.performanceMonitor || !this.scene || !this.engine) return null;
    
    // 简单的性能指标获取
    return {
      fps: this.engine.getFps(),
      frameTime: 1000 / this.engine.getFps(),
      drawCalls: this.scene.getActiveMeshes().length,
      triangleCount: this.scene.meshes.reduce((total: number, mesh: any) => {
        return total + (mesh.getTotalIndices() / 3);
      }, 0),
      memoryUsage: { used: 0, total: 0, percentage: 0 }, // 简化的内存信息
      textureMemory: 0,
      activeMeshes: this.scene.meshes.length,
      activeLights: this.scene.lights.length,
      activeParticles: this.scene.particleSystems.length,
      shaderCompilationTime: 0,
      loadingTime: 0,
    };
  }

  // 获取当前质量级别
  public getCurrentQualityLevel(): string {
    return this.state.currentQualityLevel;
  }

  // 启用/禁用自动优化
  public setAutoOptimization(enabled: boolean): void {
    this.state.autoOptimizationEnabled = enabled;
    console.log("[PerformanceManager] Auto-optimization:", enabled ? "enabled" : "disabled");
  }

  // 手动触发优化
  public manualOptimize(): void {
    const metrics = this.getCurrentMetrics();
    if (metrics) {
      this.checkAndApplyOptimizations(metrics);
    }
  }

  // 释放资源
  public dispose(): void {
    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
      this.optimizationInterval = null;
    }

    if (this.performanceMonitor) {
      this.performanceMonitor.dispose();
      this.performanceMonitor = null;
    }

    if (this.lodManager) {
      this.lodManager.dispose();
      this.lodManager = null;
    }

    this.scene = null;
    this.engine = null;
    this.state.isInitialized = false;

    console.log("[PerformanceManager] Disposed");
  }
}

// 导出单例实例
export default ScenePerformanceManager;
export { ScenePerformanceManager, type QualityConfig, type PerformanceManagerState };