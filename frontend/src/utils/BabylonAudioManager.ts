import { Scene, Sound, SoundTrack, Vector3, Mesh } from "@babylonjs/core";
import BabylonResourceManager, { ResourceType } from "./BabylonResourceManager";

// 音频类型
export enum AudioType {
  MUSIC = "music",
  SOUND_EFFECT = "sound_effect",
  AMBIENT = "ambient",
  VOICE = "voice",
}

// 音频项接口
export interface AudioItem {
  id: string;
  type: AudioType;
  path: string;
  sound?: Sound;
  loop?: boolean;
  volume?: number;
  autoplay?: boolean;
  spatialSound?: boolean;
  position?: Vector3;
  maxDistance?: number;
  playing?: boolean;
  attachedMesh?: Mesh;
}

// 音频管理器类
export default class BabylonAudioManager {
  private static instance: BabylonAudioManager;
  private scene: Scene | null = null;
  private audios: Map<string, AudioItem> = new Map();
  private masterVolume: number = 1.0;
  private volumeByType: Map<AudioType, number> = new Map();
  private muted: boolean = false;
  private mutedByType: Map<AudioType, boolean> = new Map();
  private soundTracks: Map<AudioType, SoundTrack> = new Map();
  private initialized: boolean = false;

  // 私有构造函数
  private constructor() {
    // 初始化各类型音量
    this.volumeByType.set(AudioType.MUSIC, 0.7);
    this.volumeByType.set(AudioType.SOUND_EFFECT, 1.0);
    this.volumeByType.set(AudioType.AMBIENT, 0.5);
    this.volumeByType.set(AudioType.VOICE, 1.0);

    // 初始化各类型静音状态
    this.mutedByType.set(AudioType.MUSIC, false);
    this.mutedByType.set(AudioType.SOUND_EFFECT, false);
    this.mutedByType.set(AudioType.AMBIENT, false);
    this.mutedByType.set(AudioType.VOICE, false);

    // 从本地存储加载音频设置
    this.loadSettings();
  }

  // 获取单例实例
  public static getInstance(): BabylonAudioManager {
    if (!BabylonAudioManager.instance) {
      BabylonAudioManager.instance = new BabylonAudioManager();
    }
    return BabylonAudioManager.instance;
  }

  // 初始化音频系统
  public init(scene: Scene): void {
    this.scene = scene;

    // 创建音轨
    Object.values(AudioType).forEach((type) => {
      const soundTrack = new SoundTrack(scene, {
        volume: this.volumeByType.get(type) || 1.0,
      });
      this.soundTracks.set(type, soundTrack);
    });
    this.initialized = true;
  }

  // 检查音频系统是否已初始化
  public isInitialized(): boolean {
    return this.initialized;
  }

  // 加载音频
  public async loadAudio(
    id: string,
    path: string,
    type: AudioType = AudioType.SOUND_EFFECT,
    options: Partial<AudioItem> = {},
  ): Promise<AudioItem | null> {
    if (!this.scene) {
      console.error("音频系统未初始化");
      return null;
    }

    // 检查是否已加载
    if (this.audios.has(id)) {
      return this.audios.get(id) || null;
    }

    try {
      // 创建音频对象
      const isSpatial = options.spatialSound || false;
      const sound = new Sound(
        id,
        path,
        this.scene,
        () => {
          console.log(`音频加载完成: ${id}`);

          // 将音频添加到对应的音轨
          const soundTrack = this.soundTracks.get(type);
          if (soundTrack) {
            soundTrack.addSound(sound);
          }

          // 如果设置了自动播放，则播放音频
          if (options.autoplay && !this.muted && !this.mutedByType.get(type)) {
            sound.play();
            audioItem.playing = true;
          }
        },
        {
          loop: options.loop || false,
          autoplay: false, // 我们会在加载完成后手动控制播放
          volume: options.volume || 1.0,
          spatialSound: isSpatial,
        },
      );

      // 如果是空间音效，设置位置和参数
      if (isSpatial && options.position) {
        sound.setPosition(options.position);

        if (options.maxDistance) {
          sound.maxDistance = options.maxDistance;
        }

        // 如果指定了附加网格，则将声音附加到网格
        if (options.attachedMesh) {
          sound.attachToMesh(options.attachedMesh);
        }
      }

      // 计算实际音量
      const typeVolume = this.volumeByType.get(type) || 1.0;
      const itemVolume = options.volume || 1.0;
      const actualVolume = this.masterVolume * typeVolume * itemVolume;

      sound.setVolume(actualVolume);

      // 创建音频项
      const audioItem: AudioItem = {
        id,
        type,
        path,
        sound,
        loop: options.loop || false,
        volume: options.volume || 1.0,
        autoplay: options.autoplay || false,
        spatialSound: isSpatial,
        position: options.position,
        maxDistance: options.maxDistance,
        playing: false,
        attachedMesh: options.attachedMesh,
      };

      // 存储音频项
      this.audios.set(id, audioItem);

      // 将音频添加到资源管理器
      BabylonResourceManager.getInstance().addResource(
        path,
        ResourceType.AUDIO,
      );

      return audioItem;
    } catch (error) {
      console.error(`加载音频失败: ${path}`, error);
      return null;
    }
  }

  // 播放音频
  public play(id: string): boolean {
    const audioItem = this.audios.get(id);

    if (!audioItem || !audioItem.sound) {
      console.warn(`未找到音频: ${id}`);
      return false;
    }

    // 检查是否已经在播放
    if (audioItem.playing) {
      return true;
    }

    // 检查是否静音
    if (this.muted || this.mutedByType.get(audioItem.type)) {
      audioItem.playing = true;
      return true;
    }

    try {
      audioItem.sound.play();
      audioItem.playing = true;
      return true;
    } catch (error) {
      console.error(`播放音频失败: ${id}`, error);
      return false;
    }
  }

  // 停止音频
  public stop(id: string): boolean {
    const audioItem = this.audios.get(id);

    if (!audioItem || !audioItem.sound) {
      console.warn(`未找到音频: ${id}`);
      return false;
    }

    try {
      audioItem.sound.stop();
      audioItem.playing = false;
      return true;
    } catch (error) {
      console.error(`停止音频失败: ${id}`, error);
      return false;
    }
  }

  // 暂停音频
  public pause(id: string): boolean {
    const audioItem = this.audios.get(id);

    if (!audioItem || !audioItem.sound) {
      console.warn(`未找到音频: ${id}`);
      return false;
    }

    try {
      audioItem.sound.pause();
      audioItem.playing = false;
      return true;
    } catch (error) {
      console.error(`暂停音频失败: ${id}`, error);
      return false;
    }
  }

  // 设置音频音量
  public setVolume(id: string, volume: number): boolean {
    const audioItem = this.audios.get(id);

    if (!audioItem || !audioItem.sound) {
      console.warn(`未找到音频: ${id}`);
      return false;
    }

    try {
      // 更新音频项的音量
      audioItem.volume = Math.max(0, Math.min(1, volume));

      // 计算实际音量
      const typeVolume = this.volumeByType.get(audioItem.type) || 1.0;
      const actualVolume = this.masterVolume * typeVolume * audioItem.volume;

      audioItem.sound.setVolume(actualVolume);
      return true;
    } catch (error) {
      console.error(`设置音频音量失败: ${id}`, error);
      return false;
    }
  }

  // 设置主音量
  public setMasterVolume(volume: number): void {
    this.masterVolume = Math.max(0, Math.min(1, volume));

    // 更新所有音频的音量
    this.audios.forEach((audioItem) => {
      if (audioItem.sound) {
        const typeVolume = this.volumeByType.get(audioItem.type) || 1.0;
        const itemVolume = audioItem.volume || 1.0;
        const actualVolume = this.masterVolume * typeVolume * itemVolume;

        audioItem.sound.setVolume(actualVolume);
      }
    });

    // 保存设置
    this.saveSettings();
  }

  // 设置特定类型的音量
  public setVolumeByType(type: AudioType, volume: number): void {
    this.volumeByType.set(type, Math.max(0, Math.min(1, volume)));

    // 更新该类型所有音频的音量
    this.audios.forEach((audioItem) => {
      if (audioItem.sound && audioItem.type === type) {
        const typeVolume = this.volumeByType.get(type) || 1.0;
        const itemVolume = audioItem.volume || 1.0;
        const actualVolume = this.masterVolume * typeVolume * itemVolume;

        audioItem.sound.setVolume(actualVolume);
      }
    });

    // 更新音轨音量
    const soundTrack = this.soundTracks.get(type);
    if (soundTrack) {
      soundTrack.setVolume(volume);
    }

    // 保存设置
    this.saveSettings();
  }

  // 获取主音量
  public getMasterVolume(): number {
    return this.masterVolume;
  }

  // 获取特定类型的音量
  public getVolumeByType(type: AudioType): number {
    return this.volumeByType.get(type) || 1.0;
  }

  // 设置静音
  public setMuted(muted: boolean): void {
    this.muted = muted;

    // 更新所有音频的播放状态
    this.audios.forEach((audioItem) => {
      if (audioItem.sound) {
        if (muted) {
          // 如果正在播放，则暂停
          if (audioItem.playing) {
            audioItem.sound.pause();
          }
        } else if (!this.mutedByType.get(audioItem.type)) {
          // 如果类型未静音且音频应该播放，则恢复播放
          if (audioItem.playing) {
            audioItem.sound.play();
          }
        }
      }
    });

    // 保存设置
    this.saveSettings();
  }

  // 设置特定类型的静音
  public setMutedByType(type: AudioType, muted: boolean): void {
    this.mutedByType.set(type, muted);

    // 更新该类型所有音频的播放状态
    this.audios.forEach((audioItem) => {
      if (audioItem.sound && audioItem.type === type) {
        if (muted || this.muted) {
          // 如果正在播放，则暂停
          if (audioItem.playing) {
            audioItem.sound.pause();
          }
        } else {
          // 如果应该播放，则恢复播放
          if (audioItem.playing) {
            audioItem.sound.play();
          }
        }
      }
    });

    // 保存设置
    this.saveSettings();
  }

  // 获取静音状态
  public isMuted(): boolean {
    return this.muted;
  }

  // 获取特定类型的静音状态
  public isMutedByType(type: AudioType): boolean {
    return this.mutedByType.get(type) || false;
  }

  // 卸载音频
  public unloadAudio(id: string): boolean {
    const audioItem = this.audios.get(id);

    if (!audioItem) {
      return false;
    }

    // 停止播放
    if (audioItem.sound) {
      audioItem.sound.dispose();
    }

    // 移除音频
    this.audios.delete(id);

    return true;
  }

  // 卸载所有音频
  public unloadAll(): void {
    // 停止所有音频
    this.audios.forEach((audioItem) => {
      if (audioItem.sound) {
        audioItem.sound.dispose();
      }
    });

    // 清空音频列表
    this.audios.clear();

    // 清空音轨
    this.soundTracks.forEach((soundTrack) => {
      soundTrack.dispose();
    });
    this.soundTracks.clear();
  }

  // 保存设置到本地存储
  private saveSettings(): void {
    try {
      const settings = {
        masterVolume: this.masterVolume,
        volumeByType: Object.fromEntries(this.volumeByType),
        muted: this.muted,
        mutedByType: Object.fromEntries(this.mutedByType),
      };

      localStorage.setItem("audioSettings", JSON.stringify(settings));
    } catch (error) {
      console.error("保存音频设置失败:", error);
    }
  }

  // 从本地存储加载设置
  private loadSettings(): void {
    try {
      const settingsJson = localStorage.getItem("audioSettings");

      if (settingsJson) {
        const settings = JSON.parse(settingsJson);

        // 加载主音量
        if (settings.masterVolume !== undefined) {
          this.masterVolume = settings.masterVolume;
        }

        // 加载各类型音量
        if (settings.volumeByType) {
          Object.entries(settings.volumeByType).forEach(([type, volume]) => {
            this.volumeByType.set(type as AudioType, volume as number);
          });
        }

        // 加载静音状态
        if (settings.muted !== undefined) {
          this.muted = settings.muted;
        }

        // 加载各类型静音状态
        if (settings.mutedByType) {
          Object.entries(settings.mutedByType).forEach(([type, muted]) => {
            this.mutedByType.set(type as AudioType, muted as boolean);
          });
        }
      }
    } catch (error) {
      console.error("加载音频设置失败:", error);
    }
  }
}

// 导出单例实例
