import BabylonResourceManager, {
  ResourceType,
  ResourcePriority,
} from "./BabylonResourceManager";

// 场景资源配置
interface SceneResources {
  models: string[];
  textures: string[];
  audio: string[];
  other: string[];
}

// 环境资源映射
const environmentResources: Record<string, SceneResources> = {
  "chinese-temple": {
    models: ["/models/chinese-temple.glb", "/models/chinese-temple-low.glb"],
    textures: [
      "/textures/chinese-temple/floor.jpg",
      "/textures/chinese-temple/wall.jpg",
      "/textures/chinese-temple/roof.jpg",
    ],
    audio: [
      "/audio/chinese-temple/ambient.mp3",
      "/audio/chinese-temple/bell.mp3",
    ],
    other: [],
  },
  "buddhist-temple": {
    models: ["/models/buddhist-temple.glb", "/models/buddhist-temple-low.glb"],
    textures: [
      "/textures/buddhist-temple/floor.jpg",
      "/textures/buddhist-temple/wall.jpg",
      "/textures/buddhist-temple/statue.jpg",
    ],
    audio: [
      "/audio/buddhist-temple/ambient.mp3",
      "/audio/buddhist-temple/chant.mp3",
    ],
    other: [],
  },
  "taoist-shrine": {
    models: ["/models/taoist-shrine.glb", "/models/taoist-shrine-low.glb"],
    textures: [
      "/textures/taoist-shrine/floor.jpg",
      "/textures/taoist-shrine/wall.jpg",
    ],
    audio: ["/audio/taoist-shrine/ambient.mp3"],
    other: [],
  },
  "christian-church": {
    models: [
      "/models/christian-church.glb",
      "/models/christian-church-low.glb",
    ],
    textures: [
      "/textures/christian-church/floor.jpg",
      "/textures/christian-church/wall.jpg",
      "/textures/christian-church/window.jpg",
    ],
    audio: [
      "/audio/christian-church/ambient.mp3",
      "/audio/christian-church/organ.mp3",
    ],
    other: [],
  },
  "jewish-memorial": {
    models: ["/models/jewish-memorial.glb", "/models/jewish-memorial-low.glb"],
    textures: [
      "/textures/jewish-memorial/floor.jpg",
      "/textures/jewish-memorial/wall.jpg",
    ],
    audio: ["/audio/jewish-memorial/ambient.mp3"],
    other: [],
  },
  "modern-space": {
    models: ["/models/modern-space.glb", "/models/modern-space-low.glb"],
    textures: [
      "/textures/modern-space/floor.jpg",
      "/textures/modern-space/wall.jpg",
    ],
    audio: ["/audio/modern-space/ambient.mp3"],
    other: [],
  },
  nature: {
    models: ["/models/nature.glb", "/models/nature-low.glb"],
    textures: [
      "/textures/nature/ground.jpg",
      "/textures/nature/tree.jpg",
      "/textures/nature/water.jpg",
    ],
    audio: [
      "/audio/nature/ambient.mp3",
      "/audio/nature/birds.mp3",
      "/audio/nature/water.mp3",
    ],
    other: [],
  },
  cosmos: {
    models: ["/models/cosmos.glb", "/models/cosmos-low.glb"],
    textures: ["/textures/cosmos/stars.jpg", "/textures/cosmos/nebula.jpg"],
    audio: ["/audio/cosmos/ambient.mp3"],
    other: [],
  },
};

// 通用资源
const commonResources: SceneResources = {
  models: [
    "/models/offerings/incense.glb",
    "/models/offerings/flower.glb",
    "/models/offerings/food.glb",
    "/models/offerings/candle.glb",
  ],
  textures: ["/textures/common/smoke.png", "/textures/common/flame.png"],
  audio: ["/audio/common/click.mp3", "/audio/common/place.mp3"],
  other: ["/data/offerings.json"],
};

// 场景预加载器类
class ScenePreloader {
  private static instance: ScenePreloader;
  private currentEnvironment: string | null = null;
  private isLoading: boolean = false;
  private progressCallback: ((progress: number) => void) | null = null;
  private completeCallback: (() => void) | null = null;

  // 私有构造函数
  private constructor() {}

  // 获取单例实例
  public static getInstance(): ScenePreloader {
    if (!ScenePreloader.instance) {
      ScenePreloader.instance = new ScenePreloader();
    }
    return ScenePreloader.instance;
  }

  // 设置进度回调
  public setProgressCallback(callback: (progress: number) => void): void {
    this.progressCallback = callback;

    // 设置资源管理器的进度回调
    BabylonResourceManager.getInstance().setProgressCallback((progressData) => {
      // Renamed for clarity
      if (this.progressCallback && typeof progressData.progress === "number") {
        this.progressCallback(progressData.progress);
      }
    });
  }

  // 设置完成回调
  public setCompleteCallback(callback: () => void): void {
    this.completeCallback = callback;

    // 设置资源管理器的完成回调
    BabylonResourceManager.getInstance().setCompleteCallback(() => {
      if (this.completeCallback) {
        this.completeCallback();
      }
    });
  }

  // 预加载场景资源
  public preloadEnvironment(environmentId: string): Promise<void> {
    if (this.isLoading) {
      return Promise.reject(new Error("已有加载任务正在进行"));
    }

    this.isLoading = true;
    this.currentEnvironment = environmentId;

    // 获取环境资源配置
    const resources = environmentResources[environmentId];
    if (!resources) {
      this.isLoading = false;
      return Promise.reject(new Error(`未找到环境资源配置: ${environmentId}`));
    }

    // 添加环境特定资源
    this.addEnvironmentResources(resources);

    // 添加通用资源
    this.addCommonResources();

    // 开始加载
    return BabylonResourceManager.getInstance()
      .startLoading()
      .then(() => {
        this.isLoading = false;
      })
      .catch((error) => {
        this.isLoading = false;
        throw error;
      });
  }

  // 添加环境特定资源
  private addEnvironmentResources(resources: SceneResources): void {
    // 添加模型资源
    resources.models.forEach((url, index) => {
      // 主模型优先级最高
      const priority =
        index === 0 ? ResourcePriority.CRITICAL : ResourcePriority.HIGH;
      BabylonResourceManager.getInstance().addResource(
        url,
        ResourceType.MODEL,
        priority,
      );
    });

    // 添加纹理资源
    resources.textures.forEach((url) => {
      BabylonResourceManager.getInstance().addResource(
        url,
        ResourceType.TEXTURE,
        ResourcePriority.HIGH,
      );
    });

    // 添加音频资源
    resources.audio.forEach((url) => {
      BabylonResourceManager.getInstance().addResource(
        url,
        ResourceType.AUDIO,
        ResourcePriority.MEDIUM,
      );
    });

    // 添加其他资源
    resources.other.forEach((url) => {
      const type = url.endsWith(".json")
        ? ResourceType.JSON
        : ResourceType.IMAGE;
      BabylonResourceManager.getInstance().addResource(
        url,
        type,
        ResourcePriority.MEDIUM,
      );
    });
  }

  // 添加通用资源
  private addCommonResources(): void {
    // 添加模型资源
    commonResources.models.forEach((url) => {
      BabylonResourceManager.getInstance().addResource(
        url,
        ResourceType.MODEL,
        ResourcePriority.MEDIUM,
      );
    });

    // 添加纹理资源
    commonResources.textures.forEach((url) => {
      BabylonResourceManager.getInstance().addResource(
        url,
        ResourceType.TEXTURE,
        ResourcePriority.MEDIUM,
      );
    });

    // 添加音频资源
    commonResources.audio.forEach((url) => {
      BabylonResourceManager.getInstance().addResource(
        url,
        ResourceType.AUDIO,
        ResourcePriority.LOW,
      );
    });

    // 添加其他资源
    commonResources.other.forEach((url) => {
      const type = url.endsWith(".json")
        ? ResourceType.JSON
        : ResourceType.IMAGE;
      BabylonResourceManager.getInstance().addResource(
        url,
        type,
        ResourcePriority.LOW,
      );
    });
  }

  // 获取当前加载进度
  public getProgress(): number {
    return BabylonResourceManager.getInstance().getProgress();
  }

  // 检查资源是否已加载
  public isResourceLoaded(url: string): boolean {
    return BabylonResourceManager.getInstance().isLoaded(url);
  }

  // 获取资源
  public getResource<T>(url: string): T | null {
    return BabylonResourceManager.getInstance().getResource<T>(url);
  }

  // 释放当前环境资源
  public releaseCurrentEnvironment(): void {
    if (!this.currentEnvironment) {
      return;
    }

    const resources = environmentResources[this.currentEnvironment];
    if (!resources) {
      return;
    }

    // 释放模型资源
    resources.models.forEach((url) => {
      BabylonResourceManager.getInstance().releaseResource(url);
    });

    // 释放纹理资源
    resources.textures.forEach((url) => {
      BabylonResourceManager.getInstance().releaseResource(url);
    });

    // 释放音频资源
    resources.audio.forEach((url) => {
      BabylonResourceManager.getInstance().releaseResource(url);
    });

    // 释放其他资源
    resources.other.forEach((url) => {
      BabylonResourceManager.getInstance().releaseResource(url);
    });

    this.currentEnvironment = null;
  }

  // 释放所有资源
  public releaseAll(): void {
    BabylonResourceManager.getInstance().releaseAll();
    this.currentEnvironment = null;
  }
}

// 导出单例实例
export default ScenePreloader.getInstance();
