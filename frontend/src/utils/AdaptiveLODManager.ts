// 自适应LOD (Level of Detail) 管理器
import { Scene, Mesh } from "@babylonjs/core";
import DeviceDetector from "./DeviceDetector";

// LOD配置接口
interface LODConfig {
  high: {
    distance: number;
    meshComplexity: number;
    textureSize: number;
  };
  medium: {
    distance: number;
    meshComplexity: number;
    textureSize: number;
  };
  low: {
    distance: number;
    meshComplexity: number;
    textureSize: number;
  };
}

// 设备性能级别配置
const deviceLODConfigs: Record<string, LODConfig> = {
  high: {
    high: { distance: 5, meshComplexity: 1.0, textureSize: 2048 },
    medium: { distance: 15, meshComplexity: 0.7, textureSize: 1024 },
    low: { distance: 50, meshComplexity: 0.4, textureSize: 512 },
  },
  medium: {
    high: { distance: 3, meshComplexity: 0.8, textureSize: 1024 },
    medium: { distance: 10, meshComplexity: 0.5, textureSize: 512 },
    low: { distance: 30, meshComplexity: 0.3, textureSize: 256 },
  },
  low: {
    high: { distance: 2, meshComplexity: 0.5, textureSize: 512 },
    medium: { distance: 8, meshComplexity: 0.3, textureSize: 256 },
    low: { distance: 20, meshComplexity: 0.2, textureSize: 128 },
  },
  mobile: {
    high: { distance: 1.5, meshComplexity: 0.4, textureSize: 256 },
    medium: { distance: 5, meshComplexity: 0.25, textureSize: 128 },
    low: { distance: 15, meshComplexity: 0.15, textureSize: 64 },
  },
};

// 模型LOD变体
interface ModelLODVariants {
  high: string; // 高质量模型路径
  medium: string; // 中等质量模型路径
  low: string; // 低质量模型路径
}

// 自适应LOD管理器
class AdaptiveLODManager {
  private static instance: AdaptiveLODManager;
  private scene: Scene | null = null;
  private devicePerformanceLevel: string = "medium";
  private currentLODConfig: LODConfig;
  private registeredMeshes: Map<string, Mesh[]> = new Map();
  private frameRate: number = 60;
  private targetFrameRate: number = 30;
  private frameRateHistory: number[] = [];
  private frameRateCheckInterval: number = 1000; // 1秒检查一次
  private lastFrameRateCheck: number = 0;

  private constructor() {
    this.detectDevicePerformance();
    this.currentLODConfig = deviceLODConfigs[this.devicePerformanceLevel];
  }

  public static getInstance(): AdaptiveLODManager {
    if (!AdaptiveLODManager.instance) {
      AdaptiveLODManager.instance = new AdaptiveLODManager();
    }
    return AdaptiveLODManager.instance;
  }

  // 初始化场景
  public initializeScene(scene: Scene): void {
    this.scene = scene;
    this.setupFrameRateMonitoring();
  }

  // 检测设备性能
  private detectDevicePerformance(): void {
    const detector = DeviceDetector;
    const deviceInfo = detector.getDeviceInfo();

    // 移动设备
    if (deviceInfo.isMobile) {
      this.devicePerformanceLevel = "mobile";
      this.targetFrameRate = 30;
      return;
    }

    // 基于硬件信息估算性能等级
    const cpuCores = navigator.hardwareConcurrency || 4;
    const devicePixelRatio = window.devicePixelRatio || 1;
    
    // 简单的性能估算（基于CPU核心数和设备像素比）
    if (cpuCores >= 8 && devicePixelRatio <= 2) {
      this.devicePerformanceLevel = "high";
      this.targetFrameRate = 60;
    } else if (cpuCores >= 4 && devicePixelRatio <= 2) {
      this.devicePerformanceLevel = "medium";
      this.targetFrameRate = 45;
    } else {
      this.devicePerformanceLevel = "low";
      this.targetFrameRate = 30;
    }

    console.log(`[LOD] Device performance level: ${this.devicePerformanceLevel}`);
  }

  // 设置帧率监控
  private setupFrameRateMonitoring(): void {
    if (!this.scene) return;

    this.scene.onBeforeRenderObservable.add(() => {
      const now = performance.now();
      
      if (now - this.lastFrameRateCheck > this.frameRateCheckInterval) {
        this.frameRate = this.scene!.getEngine().getFps();
        this.frameRateHistory.push(this.frameRate);
        
        // 保持历史记录在合理范围内
        if (this.frameRateHistory.length > 10) {
          this.frameRateHistory.shift();
        }

        // 检查是否需要调整LOD
        this.checkAndAdjustLOD();
        this.lastFrameRateCheck = now;
      }
    });
  }

  // 检查并调整LOD
  private checkAndAdjustLOD(): void {
    if (this.frameRateHistory.length < 3) return;

    const averageFrameRate = this.frameRateHistory.reduce((a, b) => a + b, 0) / this.frameRateHistory.length;
    
    // 如果帧率低于目标帧率的80%，降低质量
    if (averageFrameRate < this.targetFrameRate * 0.8) {
      this.adjustQualityDown();
    }
    // 如果帧率高于目标帧率的120%，可以提高质量
    else if (averageFrameRate > this.targetFrameRate * 1.2 && this.devicePerformanceLevel !== "high") {
      this.adjustQualityUp();
    }
  }

  // 降低渲染质量
  private adjustQualityDown(): void {
    console.log("[LOD] Adjusting quality down due to low frame rate");
    
    if (this.devicePerformanceLevel === "medium") {
      this.devicePerformanceLevel = "low";
    } else if (this.devicePerformanceLevel === "high") {
      this.devicePerformanceLevel = "medium";
    }
    
    this.currentLODConfig = deviceLODConfigs[this.devicePerformanceLevel];
    this.updateAllMeshLODs();
  }

  // 提高渲染质量
  private adjustQualityUp(): void {
    console.log("[LOD] Adjusting quality up due to high frame rate");
    
    if (this.devicePerformanceLevel === "low") {
      this.devicePerformanceLevel = "medium";
    } else if (this.devicePerformanceLevel === "medium") {
      this.devicePerformanceLevel = "high";
    }
    
    this.currentLODConfig = deviceLODConfigs[this.devicePerformanceLevel];
    this.updateAllMeshLODs();
  }

  // 注册网格进行LOD管理
  public registerMeshForLOD(
    meshId: string,
    meshes: Mesh[],
    variants: ModelLODVariants
  ): void {
    this.registeredMeshes.set(meshId, meshes);
    this.setupMeshLOD(meshes, variants);
  }

  // 设置网格LOD
  private setupMeshLOD(meshes: Mesh[], variants: ModelLODVariants): void {
    meshes.forEach((mesh) => {
      // 清除现有LOD
      mesh.removeLODLevel(null);

      // 设置LOD级别
      const config = this.currentLODConfig;

      // 高质量LOD
      mesh.addLODLevel(config.high.distance, mesh);

      // 中等质量LOD (如果有中等质量模型)
      if (variants.medium && variants.medium !== variants.high) {
        // 这里应该加载中等质量的网格
        // 实际实现中需要异步加载模型
        mesh.addLODLevel(config.medium.distance, null); // 临时设置为null
      }

      // 低质量LOD
      if (variants.low && variants.low !== variants.medium) {
        // 这里应该加载低质量的网格
        mesh.addLODLevel(config.low.distance, null); // 临时设置为null
      }
    });
  }

  // 更新所有网格的LOD
  private updateAllMeshLODs(): void {
    this.registeredMeshes.forEach((meshes) => {
      meshes.forEach((mesh) => {
        // 更新材质质量
        this.updateMeshMaterial(mesh);
        
        // 更新几何复杂度
        this.updateMeshComplexity(mesh);
      });
    });
  }

  // 更新网格材质质量
  private updateMeshMaterial(mesh: Mesh): void {
    if (!mesh.material) return;

    const config = this.currentLODConfig.high; // 使用当前配置的高质量设置
    
    // 调整纹理尺寸
    if ((mesh.material as any).diffuseTexture) {
      const texture = (mesh.material as any).diffuseTexture;
      if (texture.getSize) {
        const currentSize = texture.getSize();
        const targetSize = Math.min(currentSize.width, config.textureSize);
        
        if (currentSize.width > targetSize) {
          // 这里应该实现纹理大小调整逻辑
          console.log(`[LOD] Should resize texture from ${currentSize.width} to ${targetSize}`);
        }
      }
    }
  }

  // 更新网格复杂度
  private updateMeshComplexity(mesh: Mesh): void {
    const config = this.currentLODConfig.high;
    
    // 根据复杂度系数调整网格细节
    if (config.meshComplexity < 1.0) {
      // 这里可以实现网格简化逻辑
      mesh.setEnabled(config.meshComplexity > 0.5);
    }
  }

  // 获取当前性能统计
  public getPerformanceStats() {
    return {
      devicePerformanceLevel: this.devicePerformanceLevel,
      currentFrameRate: this.frameRate,
      targetFrameRate: this.targetFrameRate,
      averageFrameRate: this.frameRateHistory.length > 0 
        ? this.frameRateHistory.reduce((a, b) => a + b, 0) / this.frameRateHistory.length 
        : 0,
      registeredMeshCount: this.registeredMeshes.size,
      currentLODConfig: this.currentLODConfig,
    };
  }

  // 强制设置性能级别
  public setPerformanceLevel(level: "high" | "medium" | "low" | "mobile"): void {
    this.devicePerformanceLevel = level;
    this.currentLODConfig = deviceLODConfigs[level];
    this.updateAllMeshLODs();
    
    console.log(`[LOD] Manually set performance level to: ${level}`);
  }

  // 获取推荐的场景设置
  public getRecommendedSceneSettings() {
    const level = this.devicePerformanceLevel;
    
    const settings = {
      high: {
        shadowsEnabled: true,
        shadowMapSize: 1024,
        particleCount: 1000,
        postProcessingEnabled: true,
        antialiasing: true,
        reflectionsEnabled: true,
      },
      medium: {
        shadowsEnabled: true,
        shadowMapSize: 512,
        particleCount: 500,
        postProcessingEnabled: true,
        antialiasing: false,
        reflectionsEnabled: false,
      },
      low: {
        shadowsEnabled: false,
        shadowMapSize: 256,
        particleCount: 200,
        postProcessingEnabled: false,
        antialiasing: false,
        reflectionsEnabled: false,
      },
      mobile: {
        shadowsEnabled: false,
        shadowMapSize: 128,
        particleCount: 100,
        postProcessingEnabled: false,
        antialiasing: false,
        reflectionsEnabled: false,
      },
    };

    return settings[level as keyof typeof settings] || settings.medium;
  }

  // 清理资源
  public dispose(): void {
    this.registeredMeshes.clear();
    this.frameRateHistory = [];
    this.scene = null;
  }
}

export default AdaptiveLODManager;