import {
  Scene,
  Vector3,
  TransformNode,
  AbstractMesh,
  Color3,
  Color4,
  CubeTexture,
  HemisphericLight,
  DirectionalLight,
  ShadowGenerator,
} from "@babylonjs/core";
// 定义事件监听器类型
type EventListener = (...args: unknown[]) => void;

// 浏览器兼容的EventEmitter实现
class EventEmitter {
  private events: { [key: string]: EventListener[] } = {};

  on(event: string, listener: EventListener): this {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);
    return this;
  }

  emit(event: string, ...args: unknown[]): boolean {
    if (!this.events[event]) {
      return false;
    }
    this.events[event].forEach((listener) => listener(...args));
    return true;
  }

  off(event: string, listener: EventListener): this {
    if (!this.events[event]) {
      return this;
    }
    this.events[event] = this.events[event].filter((l) => l !== listener);
    return this;
  }

  removeAllListeners(event?: string): this {
    if (event) {
      delete this.events[event];
    } else {
      this.events = {};
    }
    return this;
  }
}

// 场景组件接口
export interface BabylonSceneComponent {
  id: string;
  name: string;
  type: string;
  node: TransformNode;
  position: Vector3;
  rotation: Vector3;
  scaling: Vector3;
  visible: boolean;
  interactive: boolean;
  properties: Record<string, unknown>;
  parent?: string;
  children: string[];
}

// 场景组件工厂接口
export interface BabylonSceneComponentFactory {
  type: string;
  create: (
    id: string,
    name: string,
    properties: Record<string, unknown>,
    scene: Scene,
  ) => Promise<BabylonSceneComponent>;
}

// 场景事件类型
export enum BabylonSceneEventType {
  COMPONENT_ADDED = "component_added",
  COMPONENT_REMOVED = "component_removed",
  COMPONENT_UPDATED = "component_updated",
  SCENE_LOADED = "scene_loaded",
  SCENE_UNLOADED = "scene_unloaded",
  INTERACTION = "interaction",
}

// 场景事件接口
export interface BabylonSceneEvent {
  type: BabylonSceneEventType;
  componentId?: string;
  data?: Record<string, unknown>;
}

// 场景配置接口
export interface BabylonSceneConfig {
  id: string;
  name: string;
  components: {
    id: string;
    name: string;
    type: string;
    position?: [number, number, number];
    rotation?: [number, number, number];
    scaling?: [number, number, number];
    visible?: boolean;
    interactive?: boolean;
    properties?: Record<string, unknown>;
    parent?: string;
  }[];
  environment?: {
    skybox?: string;
    clearColor?: string;
    ambientLight?: {
      color: string;
      intensity: number;
    };
    directionalLight?: {
      color: string;
      intensity: number;
      position: [number, number, number];
      castShadow: boolean;
    };
  };
}

// 场景管理器类
export class BabylonSceneManager extends EventEmitter {
  private static instance: BabylonSceneManager;
  private scene: Scene | null = null;
  private components: Map<string, BabylonSceneComponent> = new Map();
  private factories: Map<string, BabylonSceneComponentFactory> = new Map();
  private currentSceneId: string | null = null;
  private sceneConfigs: Map<string, BabylonSceneConfig> = new Map();
  private isLoading: boolean = false;
  private shadowGenerator: ShadowGenerator | null = null;

  // 私有构造函数
  private constructor() {
    super();
  }

  // 获取单例实例
  public static getInstance(): BabylonSceneManager {
    if (!BabylonSceneManager.instance) {
      BabylonSceneManager.instance = new BabylonSceneManager();
    }
    return BabylonSceneManager.instance;
  }

  // 设置Babylon.js场景
  public setScene(scene: Scene): void {
    this.scene = scene;
  }

  // 获取Babylon.js场景
  public getScene(): Scene | null {
    return this.scene;
  }

  // 注册组件工厂
  public registerComponentFactory(factory: BabylonSceneComponentFactory): void {
    this.factories.set(factory.type, factory);
  }

  // 注册场景配置
  public registerSceneConfig(config: BabylonSceneConfig): void {
    this.sceneConfigs.set(config.id, config);
  }

  // 加载场景
  public async loadScene(sceneId: string): Promise<boolean> {
    if (this.isLoading) {
      console.warn("场景正在加载中，请等待...");
      return false;
    }

    if (!this.scene) {
      console.error("Babylon.js场景未设置");
      return false;
    }

    const config = this.sceneConfigs.get(sceneId);
    if (!config) {
      console.error(`未找到场景配置: ${sceneId}`);
      return false;
    }

    this.isLoading = true;

    // 卸载当前场景
    if (this.currentSceneId) {
      await this.unloadScene();
    }

    try {
      // 创建场景组件
      for (const compConfig of config.components) {
        await this.createComponent(
          compConfig.id,
          compConfig.name,
          compConfig.type,
          compConfig.properties || {},
          compConfig.position,
          compConfig.rotation,
          compConfig.scaling,
          compConfig.visible,
          compConfig.interactive,
          compConfig.parent,
        );
      }

      // 设置环境
      if (config.environment) {
        this.setupEnvironment(config.environment);
      }

      this.currentSceneId = sceneId;
      this.isLoading = false;

      // 触发场景加载完成事件
      this.emit(BabylonSceneEventType.SCENE_LOADED, {
        type: BabylonSceneEventType.SCENE_LOADED,
        data: { sceneId },
      });

      return true;
    } catch (error) {
      console.error("加载场景失败:", error);
      this.isLoading = false;
      return false;
    }
  }

  // 卸载场景
  public async unloadScene(): Promise<void> {
    if (!this.currentSceneId || !this.scene) {
      return;
    }

    // 移除所有组件
    const componentIds = Array.from(this.components.keys());
    for (const id of componentIds) {
      await this.removeComponent(id);
    }

    // 清除阴影生成器
    if (this.shadowGenerator) {
      this.shadowGenerator.dispose();
      this.shadowGenerator = null;
    }

    // 触发场景卸载事件
    this.emit(BabylonSceneEventType.SCENE_UNLOADED, {
      type: BabylonSceneEventType.SCENE_UNLOADED,
      data: { sceneId: this.currentSceneId },
    });

    this.currentSceneId = null;
  }

  // 创建组件
  public async createComponent(
    id: string,
    name: string,
    type: string,
    properties: Record<string, unknown> = {},
    position?: [number, number, number],
    rotation?: [number, number, number],
    scaling?: [number, number, number],
    visible: boolean = true,
    interactive: boolean = false,
    parentId?: string,
  ): Promise<BabylonSceneComponent | null> {
    if (!this.scene) {
      console.error("Babylon.js场景未设置");
      return null;
    }

    // 检查组件是否已存在
    if (this.components.has(id)) {
      console.warn(`组件已存在: ${id}`);
      return null;
    }

    // 获取组件工厂
    const factory = this.factories.get(type);
    if (!factory) {
      console.error(`未找到组件工厂: ${type}`);
      return null;
    }

    try {
      // 创建组件
      const component = await factory.create(id, name, properties, this.scene);

      // 设置位置、旋转和缩放
      if (position) {
        component.position.set(position[0], position[1], position[2]);
        component.node.position.set(position[0], position[1], position[2]);
      }

      if (rotation) {
        component.rotation.set(rotation[0], rotation[1], rotation[2]);
        component.node.rotation.set(rotation[0], rotation[1], rotation[2]);
      }

      if (scaling) {
        component.scaling.set(scaling[0], scaling[1], scaling[2]);
        component.node.scaling.set(scaling[0], scaling[1], scaling[2]);
      }

      component.visible = visible;
      if (component.node instanceof TransformNode) {
        component.node.setEnabled(visible);
      }
      component.interactive = interactive;
      component.children = [];

      // 添加到场景或父组件
      if (parentId) {
        const parent = this.components.get(parentId);
        if (parent) {
          component.node.parent = parent.node;
          parent.children.push(id);
          component.parent = parentId;
        } else {
          console.warn(`未找到父组件: ${parentId}，将添加到场景根节点`);
        }
      }

      // 存储组件
      this.components.set(id, component);

      // 触发组件添加事件
      this.emit(BabylonSceneEventType.COMPONENT_ADDED, {
        type: BabylonSceneEventType.COMPONENT_ADDED,
        componentId: id,
      });

      return component;
    } catch (error) {
      console.error(`创建组件失败: ${id}`, error);
      return null;
    }
  }

  // 移除组件
  public async removeComponent(id: string): Promise<boolean> {
    const component = this.components.get(id);
    if (!component) {
      return false;
    }

    // 递归移除子组件
    for (const childId of [...component.children]) {
      await this.removeComponent(childId);
    }

    // 从父组件中移除
    if (component.parent) {
      const parent = this.components.get(component.parent);
      if (parent) {
        parent.children = parent.children.filter((childId) => childId !== id);
      }
    }

    // 处理特殊组件类型的清理
    if (
      component.type === "particle_system" &&
      component.properties.particleSystem &&
      typeof (component.properties.particleSystem as { dispose?: () => void })
        .dispose === "function"
    ) {
      (
        component.properties.particleSystem as { dispose: () => void }
      ).dispose();
    } else if (
      component.type === "audio" &&
      component.properties.sound &&
      typeof (component.properties.sound as { dispose?: () => void })
        .dispose === "function"
    ) {
      (component.properties.sound as { dispose: () => void }).dispose();
    }

    // 从场景中移除
    component.node.dispose();

    // 移除组件
    this.components.delete(id);

    // 触发组件移除事件
    this.emit(BabylonSceneEventType.COMPONENT_REMOVED, {
      type: BabylonSceneEventType.COMPONENT_REMOVED,
      componentId: id,
    });

    return true;
  }

  // 获取组件
  public getComponent(id: string): BabylonSceneComponent | undefined {
    return this.components.get(id);
  }

  // 获取所有组件
  public getAllComponents(): BabylonSceneComponent[] {
    return Array.from(this.components.values());
  }

  // 获取特定类型的组件
  public getComponentsByType(type: string): BabylonSceneComponent[] {
    return Array.from(this.components.values()).filter(
      (comp) => comp.type === type,
    );
  }

  // 更新组件属性
  public updateComponent(
    id: string,
    properties: Partial<BabylonSceneComponent>,
  ): boolean {
    const component = this.components.get(id);
    if (!component) {
      return false;
    }

    // 更新位置
    if (properties.position) {
      component.position.copyFrom(properties.position);
      component.node.position.copyFrom(properties.position);
    }

    // 更新旋转
    if (properties.rotation) {
      component.rotation.copyFrom(properties.rotation);
      component.node.rotation.copyFrom(properties.rotation);
    }

    // 更新缩放
    if (properties.scaling) {
      component.scaling.copyFrom(properties.scaling);
      component.node.scaling.copyFrom(properties.scaling);
    }

    // 更新可见性
    if (properties.visible !== undefined) {
      component.visible = properties.visible;
      if (component.node instanceof TransformNode) {
        component.node.setEnabled(properties.visible);
      }
    }

    // 更新交互性
    if (properties.interactive !== undefined) {
      component.interactive = properties.interactive;
    }

    // 更新自定义属性
    if (properties.properties) {
      component.properties = {
        ...component.properties,
        ...properties.properties,
      };
    }

    // 触发组件更新事件
    this.emit(BabylonSceneEventType.COMPONENT_UPDATED, {
      type: BabylonSceneEventType.COMPONENT_UPDATED,
      componentId: id,
      data: properties,
    });

    return true;
  }

  // 设置环境
  private setupEnvironment(environment: {
    clearColor?: string;
    skybox?: string;
    ambientLight?: {
      intensity: number;
      color: string;
    };
    directionalLight?: {
      position: [number, number, number];
      intensity: number;
      color: string;
      castShadow?: boolean;
    };
  }): void {
    if (!this.scene) return;

    // 设置背景色
    if (environment.clearColor) {
      this.scene.clearColor = Color4.FromHexString(environment.clearColor);
    }

    // 设置天空盒
    if (environment.skybox) {
      const skybox = CubeTexture.CreateFromPrefilteredData(
        environment.skybox,
        this.scene,
      );
      this.scene.environmentTexture = skybox;
      this.scene.createDefaultSkybox(skybox, true);
    }

    // 设置环境光
    if (environment.ambientLight) {
      const hemiLight = new HemisphericLight(
        "ambientLight",
        new Vector3(0, 1, 0),
        this.scene,
      );
      hemiLight.intensity = environment.ambientLight.intensity;
      hemiLight.diffuse = Color3.FromHexString(environment.ambientLight.color);
    }

    // 设置平行光和阴影
    if (environment.directionalLight) {
      const dirLight = new DirectionalLight(
        "directionalLight",
        new Vector3(
          environment.directionalLight.position[0],
          environment.directionalLight.position[1],
          environment.directionalLight.position[2],
        ),
        this.scene,
      );
      dirLight.intensity = environment.directionalLight.intensity;
      dirLight.diffuse = Color3.FromHexString(
        environment.directionalLight.color,
      );

      // 设置阴影
      if (environment.directionalLight.castShadow) {
        this.shadowGenerator = new ShadowGenerator(1024, dirLight);
        this.shadowGenerator.useBlurExponentialShadowMap = true;
        this.shadowGenerator.blurKernel = 32;

        // 为所有可接收阴影的网格添加阴影
        this.components.forEach((component) => {
          if (component.properties.receiveShadows && this.shadowGenerator) {
            // 检查节点是否为AbstractMesh类型
            if (component.node instanceof AbstractMesh) {
              this.shadowGenerator.addShadowCaster(component.node);
            }
            // 如果是TransformNode，则遍历其子节点
            else {
              component.node.getChildMeshes().forEach((mesh) => {
                if (mesh instanceof AbstractMesh && this.shadowGenerator) {
                  this.shadowGenerator.addShadowCaster(mesh);
                }
              });
            }
          }
        });
      }
    }
  }

  // 保存场景状态
  public saveSceneState(): Record<string, unknown> {
    if (!this.currentSceneId) {
      return {};
    }

    const componentStates: Record<string, unknown> = {};

    // 保存每个组件的状态
    this.components.forEach((component, id) => {
      componentStates[id] = {
        position: [
          component.position.x,
          component.position.y,
          component.position.z,
        ],
        rotation: [
          component.rotation.x,
          component.rotation.y,
          component.rotation.z,
        ],
        scaling: [
          component.scaling.x,
          component.scaling.y,
          component.scaling.z,
        ],
        visible: component.visible,
        interactive: component.interactive,
        properties: { ...component.properties },
      };
    });

    return {
      sceneId: this.currentSceneId,
      timestamp: Date.now(),
      components: componentStates,
    };
  }

  // 恢复场景状态
  public restoreSceneState(state: Record<string, unknown>): boolean {
    if (!state.sceneId || state.sceneId !== this.currentSceneId) {
      console.warn("无法恢复场景状态：场景ID不匹配");
      return false;
    }

    const componentStates = state.components || {};

    // 恢复每个组件的状态
    Object.entries(componentStates).forEach(
      ([id, compStateUnknown]: [string, unknown]) => {
        // 类型断言
        const compState = compStateUnknown as {
          position?: [number, number, number];
          rotation?: [number, number, number];
          scaling?: [number, number, number];
          visible?: boolean;
          interactive?: boolean;
          properties?: Record<string, unknown>;
        };
        const component = this.components.get(id);
        if (component) {
          // 更新组件状态
          this.updateComponent(id, {
            position: compState.position
              ? new Vector3(
                  compState.position[0],
                  compState.position[1],
                  compState.position[2],
                )
              : undefined,
            rotation: compState.rotation
              ? new Vector3(
                  compState.rotation[0],
                  compState.rotation[1],
                  compState.rotation[2],
                )
              : undefined,
            scaling: compState.scaling
              ? new Vector3(
                  compState.scaling[0],
                  compState.scaling[1],
                  compState.scaling[2],
                )
              : undefined,
            visible: compState.visible,
            interactive: compState.interactive,
            properties: compState.properties,
          });
        }
      },
    );

    return true;
  }
}

// 导出单例实例
export default BabylonSceneManager.getInstance();
