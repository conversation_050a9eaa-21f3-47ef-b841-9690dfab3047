import {
  Scene,
  Vector3,
  PhysicsImpostor,
  PhysicsAggregate,
  AbstractMesh,
  Quaternion,
  HavokPlugin,
  AmmoJSPlugin,
  CannonJSPlugin,
  OimoJSPlugin,
} from "@babylonjs/core";
import { EventEmitter } from "./EventEmitter";

// 物理事件类型
export enum PhysicsEventType {
  COLLISION_START = "collision_start",
  COLLISION_END = "collision_end",
  PHYSICS_STEP = "physics_step",
  BODY_ADDED = "body_added",
  BODY_REMOVED = "body_removed",
}

// 物理事件接口
export interface PhysicsEvent {
  type: PhysicsEventType;
  data?: Record<string, unknown>;
}

// 物理碰撞事件接口
export interface CollisionEvent extends PhysicsEvent {
  bodyA: PhysicsBody;
  bodyB: PhysicsBody;
  point?: Vector3;
  normal?: Vector3;
  impulse?: number;
}

// 物理对象类型
export enum PhysicsBodyType {
  STATIC = "static",
  DYNAMIC = "dynamic",
  KINEMATIC = "kinematic",
}

// 物理引擎类型
export enum PhysicsEngineType {
  HAVOK = "havok",
  AMMO = "ammo",
  CANNON = "cannon",
  OIMO = "oimo",
}

// 物理形状类型
export enum PhysicsShapeType {
  BOX = "box",
  SPHERE = "sphere",
  CAPSULE = "capsule",
  CYLINDER = "cylinder",
  MESH = "mesh",
  HEIGHTMAP = "heightmap",
  PLANE = "plane",
  CONVEX_HULL = "convex_hull",
}

// 物理对象接口
export interface PhysicsBody {
  id: string;
  mesh: AbstractMesh;
  impostor: PhysicsImpostor;
  aggregate?: PhysicsAggregate;
  type: PhysicsBodyType;
  mass: number;
  restitution: number;
  friction: number;
  linearDamping: number;
  angularDamping: number;
  isSensor: boolean;
  userData: Record<string, unknown>;
}

// 物理系统类
export class BabylonPhysicsSystem extends EventEmitter {
  private static instance: BabylonPhysicsSystem;
  private scene: Scene | null = null;
  private bodies: Map<string, PhysicsBody> = new Map();
  private initialized: boolean = false;
  // 物理系统运行状态，用于暂停/恢复物理模拟
  private _running: boolean = false;
  private gravity: Vector3 = new Vector3(0, -9.81, 0);
  // 当前使用的物理引擎类型
  private _engineType: PhysicsEngineType = PhysicsEngineType.AMMO;

  // 私有构造函数
  private constructor() {
    super();
  }

  // 获取单例实例
  public static getInstance(): BabylonPhysicsSystem {
    if (!BabylonPhysicsSystem.instance) {
      BabylonPhysicsSystem.instance = new BabylonPhysicsSystem();
    }
    return BabylonPhysicsSystem.instance;
  }

  // 初始化物理系统
  public async init(
    scene: Scene,
    engineType: PhysicsEngineType = PhysicsEngineType.AMMO,
    gravity?: Vector3,
  ): Promise<boolean> {
    if (this.initialized) {
      return true;
    }

    try {
      this.scene = scene;
      this._engineType = engineType;

      if (gravity) {
        this.gravity = gravity;
      }

      // 创建物理引擎
      let plugin;
      switch (engineType) {
        case PhysicsEngineType.HAVOK:
          plugin = new HavokPlugin();
          break;
        case PhysicsEngineType.AMMO:
          plugin = new AmmoJSPlugin();
          break;
        case PhysicsEngineType.CANNON:
          plugin = new CannonJSPlugin();
          break;
        case PhysicsEngineType.OIMO:
          plugin = new OimoJSPlugin();
          break;
        default:
          plugin = new AmmoJSPlugin();
      }

      // 启用物理引擎
      scene.enablePhysics(this.gravity, plugin);

      // 设置碰撞回调
      scene.onBeforePhysicsObservable.add(() => {
        if (!this._running) {
          return;
        }
        this.emit(PhysicsEventType.PHYSICS_STEP, {
          type: PhysicsEventType.PHYSICS_STEP,
          data: { deltaTime: scene.getEngine().getDeltaTime() / 1000 },
        });
      });

      this.initialized = true;
      this._running = true;
      console.log(`物理系统初始化成功，使用引擎: ${engineType}`);
      console.log(`当前引擎类型: ${this._engineType}`); // Ensure _engineType is read
      return true;
    } catch (error) {
      console.error("初始化物理系统失败:", error);
      return false;
    }
  }

  // 检查是否已初始化
  public isInitialized(): boolean {
    return this.initialized;
  }

  // 启动物理系统
  public start(): void {
    if (!this.initialized) {
      console.error("物理系统尚未初始化");
      return;
    }

    this._running = true;
  }

  // 停止物理系统
  public stop(): void {
    this._running = false;
  }

  // 添加物理对象
  public addBody(
    id: string,
    mesh: AbstractMesh,
    type: PhysicsBodyType = PhysicsBodyType.DYNAMIC,
    shapeType: PhysicsShapeType = PhysicsShapeType.BOX,
    options: {
      mass?: number;
      restitution?: number;
      friction?: number;
      linearDamping?: number;
      angularDamping?: number;
      isSensor?: boolean;
      userData?: Record<string, unknown>;
    } = {},
  ): PhysicsBody | null {
    if (!this.initialized || !this.scene) {
      console.error("物理系统尚未初始化");
      return null;
    }

    if (this.bodies.has(id)) {
      console.warn(`物理对象已存在: ${id}`);
      return this.bodies.get(id) || null;
    }

    // 设置默认选项
    const {
      mass = 1,
      restitution = 0.2,
      friction = 0.5,
      linearDamping = 0.1,
      angularDamping = 0.1,
      isSensor = false,
      userData = {},
    } = options;

    // 根据类型设置物理对象类型
    let impostorType;
    switch (type) {
      case PhysicsBodyType.STATIC:
        impostorType = PhysicsImpostor.BoxImpostor;
        break;
      case PhysicsBodyType.DYNAMIC:
        impostorType = PhysicsImpostor.BoxImpostor;
        break;
      case PhysicsBodyType.KINEMATIC:
        impostorType = PhysicsImpostor.BoxImpostor;
        break;
      default:
        impostorType = PhysicsImpostor.BoxImpostor;
    }

    // 根据形状设置物理对象形状
    switch (shapeType) {
      case PhysicsShapeType.BOX:
        impostorType = PhysicsImpostor.BoxImpostor;
        break;
      case PhysicsShapeType.SPHERE:
        impostorType = PhysicsImpostor.SphereImpostor;
        break;
      case PhysicsShapeType.CAPSULE:
        impostorType = PhysicsImpostor.CapsuleImpostor;
        break;
      case PhysicsShapeType.CYLINDER:
        impostorType = PhysicsImpostor.CylinderImpostor;
        break;
      case PhysicsShapeType.MESH:
        impostorType = PhysicsImpostor.MeshImpostor;
        break;
      case PhysicsShapeType.HEIGHTMAP:
        impostorType = PhysicsImpostor.HeightmapImpostor;
        break;
      case PhysicsShapeType.PLANE:
        impostorType = PhysicsImpostor.PlaneImpostor;
        break;
      case PhysicsShapeType.CONVEX_HULL:
        impostorType = PhysicsImpostor.ConvexHullImpostor;
        break;
      default:
        impostorType = PhysicsImpostor.BoxImpostor;
    }

    // 创建物理对象
    const impostor = new PhysicsImpostor(
      mesh,
      impostorType,
      {
        mass: type === PhysicsBodyType.STATIC ? 0 : mass,
        restitution,
        friction,
      },
      this.scene,
    );

    // 设置阻尼 (通过物理引擎参数设置)
    // 注意：不同物理引擎对阻尼的支持可能不同

    // 设置碰撞回调
    impostor.onCollideEvent = (collider, collidedWith) => {
      const colliderId = this.getBodyIdByImpostor(collider);
      const collidedWithId = this.getBodyIdByImpostor(collidedWith);

      if (colliderId && collidedWithId) {
        const bodyA = this.bodies.get(colliderId);
        const bodyB = this.bodies.get(collidedWithId);

        if (bodyA && bodyB) {
          const event: CollisionEvent = {
            type: PhysicsEventType.COLLISION_START,
            bodyA,
            bodyB,
          };

          this.emit(PhysicsEventType.COLLISION_START, event);
        }
      }
    };

    // 创建物理对象
    const physicsBody: PhysicsBody = {
      id,
      mesh,
      impostor,
      type,
      mass: type === PhysicsBodyType.STATIC ? 0 : mass,
      restitution,
      friction,
      linearDamping,
      angularDamping,
      isSensor,
      userData,
    };

    // 存储物理对象
    this.bodies.set(id, physicsBody);

    // 触发物理对象添加事件
    this.emit(PhysicsEventType.BODY_ADDED, {
      type: PhysicsEventType.BODY_ADDED,
      data: physicsBody,
    });

    return physicsBody;
  }

  // 根据物理对象获取ID
  private getBodyIdByImpostor(impostor: PhysicsImpostor): string | null {
    for (const [id, body] of this.bodies.entries()) {
      if (body.impostor === impostor) {
        return id;
      }
    }
    return null;
  }

  // 移除物理对象
  public removeBody(id: string): boolean {
    if (!this.initialized) {
      console.error("物理系统尚未初始化");
      return false;
    }

    const body = this.bodies.get(id);
    if (!body) {
      console.warn(`物理对象不存在: ${id}`);
      return false;
    }

    // 释放物理对象
    body.impostor.dispose();

    // 从映射中移除
    this.bodies.delete(id);

    // 触发物理对象移除事件
    this.emit(PhysicsEventType.BODY_REMOVED, {
      type: PhysicsEventType.BODY_REMOVED,
      data: body,
    });

    return true;
  }
  // 应用力
  public applyForce(id: string, force: Vector3, point?: Vector3): boolean {
    if (!this.initialized) {
      console.error("物理系统尚未初始化");
      return false;
    }

    const body = this.bodies.get(id);
    if (!body || body.type === PhysicsBodyType.STATIC) {
      return false;
    }

    if (point) {
      body.impostor.applyForce(
        force,
        body.mesh.getAbsolutePosition().add(point),
      );
    } else {
      body.impostor.applyForce(force, body.mesh.getAbsolutePosition());
    }

    return true;
  }

  // 应用冲量
  public applyImpulse(id: string, impulse: Vector3, point?: Vector3): boolean {
    if (!this.initialized) {
      console.error("物理系统尚未初始化");
      return false;
    }

    const body = this.bodies.get(id);
    if (!body || body.type === PhysicsBodyType.STATIC) {
      return false;
    }

    if (point) {
      body.impostor.applyImpulse(
        impulse,
        body.mesh.getAbsolutePosition().add(point),
      );
    } else {
      body.impostor.applyImpulse(impulse, body.mesh.getAbsolutePosition());
    }

    return true;
  }

  // 设置位置
  public setPosition(id: string, position: Vector3): boolean {
    if (!this.initialized) {
      console.error("物理系统尚未初始化");
      return false;
    }

    const body = this.bodies.get(id);
    if (!body) {
      return false;
    }

    body.mesh.position.copyFrom(position);
    body.impostor.forceUpdate();

    return true;
  }

  // 设置旋转
  public setRotation(id: string, rotation: Vector3): boolean {
    if (!this.initialized) {
      console.error("物理系统尚未初始化");
      return false;
    }

    const body = this.bodies.get(id);
    if (!body) {
      return false;
    }

    body.mesh.rotation.copyFrom(rotation);
    body.impostor.forceUpdate();

    return true;
  }

  // 设置四元数旋转
  public setRotationQuaternion(id: string, quaternion: Quaternion): boolean {
    if (!this.initialized) {
      console.error("物理系统尚未初始化");
      return false;
    }

    const body = this.bodies.get(id);
    if (!body) {
      return false;
    }

    if (!body.mesh.rotationQuaternion) {
      body.mesh.rotationQuaternion = new Quaternion();
    }

    body.mesh.rotationQuaternion.copyFrom(quaternion);
    body.impostor.forceUpdate();

    return true;
  }

  // 设置线性速度
  public setLinearVelocity(id: string, velocity: Vector3): boolean {
    if (!this.initialized) {
      console.error("物理系统尚未初始化");
      return false;
    }

    const body = this.bodies.get(id);
    if (!body) {
      return false;
    }

    body.impostor.setLinearVelocity(velocity);

    return true;
  }

  // 设置角速度
  public setAngularVelocity(id: string, velocity: Vector3): boolean {
    if (!this.initialized) {
      console.error("物理系统尚未初始化");
      return false;
    }

    const body = this.bodies.get(id);
    if (!body) {
      return false;
    }

    body.impostor.setAngularVelocity(velocity);

    return true;
  }

  // 获取线性速度
  public getLinearVelocity(id: string): Vector3 | null {
    if (!this.initialized) {
      console.error("物理系统尚未初始化");
      return null;
    }

    const body = this.bodies.get(id);
    if (!body) {
      return null;
    }

    return body.impostor.getLinearVelocity();
  }

  // 获取角速度
  public getAngularVelocity(id: string): Vector3 | null {
    if (!this.initialized) {
      console.error("物理系统尚未初始化");
      return null;
    }

    const body = this.bodies.get(id);
    if (!body) {
      return null;
    }

    return body.impostor.getAngularVelocity();
  }

  // 获取所有物理对象
  public getAllBodies(): PhysicsBody[] {
    return Array.from(this.bodies.values());
  }

  // 获取物理对象
  public getBody(id: string): PhysicsBody | null {
    return this.bodies.get(id) || null;
  }

  // 设置重力
  public setGravity(gravity: Vector3): void {
    if (!this.initialized || !this.scene) {
      console.error("物理系统尚未初始化");
      return;
    }

    this.gravity = gravity;
    this.scene.getPhysicsEngine()?.setGravity(gravity);
  }

  // 获取重力
  public getGravity(): Vector3 {
    return this.gravity;
  }

  // 清理物理系统
  public dispose(): void {
    if (!this.initialized) {
      return;
    }

    // 移除所有物理对象
    const bodyIds = Array.from(this.bodies.keys());
    bodyIds.forEach((id) => {
      this.removeBody(id);
    });

    this.bodies.clear();
    this.initialized = false;
    this._running = false;

    // 移除所有事件监听器
    this.clear();
  }
}

// 导出单例实例
export default BabylonPhysicsSystem.getInstance();
