import { BabylonSceneManager } from "./BabylonSceneSystem"; // Updated import

// 场景状态接口
export interface SceneState {
  id: string;
  sceneId: string;
  name: string;
  timestamp: number;
  data: Record<string, unknown>;
}

// 场景状态管理器类
class SceneStateManager {
  private static instance: SceneStateManager;
  private states: Map<string, SceneState> = new Map();
  private currentStateId: string | null = null;
  // private autoSaveEnabled: boolean = false; // Declared but its value is never read.
  private autoSaveInterval: number = 60000; // 默认1分钟
  private autoSaveTimer: NodeJS.Timeout | null = null;
  private maxStates: number = 10; // 最大保存状态数

  // 私有构造函数
  private constructor() {}

  // 获取单例实例
  public static getInstance(): SceneStateManager {
    if (!SceneStateManager.instance) {
      SceneStateManager.instance = new SceneStateManager();
    }
    return SceneStateManager.instance;
  }

  // 保存当前场景状态
  public saveState(name: string = ""): string | null {
    const sceneManager = BabylonSceneManager.getInstance(); // Get instance
    const sceneState = sceneManager.saveSceneState(); // Use instance

    if (!sceneState.sceneId) {
      console.warn("无法保存场景状态：当前没有加载场景");
      return null;
    }

    const stateId = `state_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const state: SceneState = {
      id: stateId,
      sceneId: sceneState.sceneId as string,
      name: name || `状态 ${new Date().toLocaleString()}`,
      timestamp: Date.now(),
      data: sceneState,
    };

    // 保存状态
    this.states.set(stateId, state);
    this.currentStateId = stateId;

    // 如果超过最大状态数，删除最旧的状态
    if (this.states.size > this.maxStates) {
      const oldestState = Array.from(this.states.values()).sort(
        (a, b) => a.timestamp - b.timestamp,
      )[0];

      if (oldestState) {
        this.states.delete(oldestState.id);
      }
    }

    // 保存到本地存储
    this.saveToLocalStorage();

    return stateId;
  }

  // 加载场景状态
  public loadState(stateId: string): boolean {
    const state = this.states.get(stateId);

    if (!state) {
      console.warn(`未找到场景状态: ${stateId}`);
      return false;
    }

    // 检查当前场景是否与状态匹配
    const sceneManager = BabylonSceneManager.getInstance(); // Get instance
    const currentSceneId = sceneManager.saveSceneState().sceneId; // Use instance
    if (currentSceneId !== state.sceneId) {
      console.warn(
        `无法加载状态：当前场景 (${currentSceneId}) 与状态场景 (${state.sceneId}) 不匹配`,
      );
      return false;
    }

    // 恢复场景状态
    // const sceneManager = BabylonSceneManager.getInstance(); // Get instance - This was a redeclaration, use the one above
    const result = sceneManager.restoreSceneState(state.data); // Use instance

    if (result) {
      this.currentStateId = stateId;
    }

    return result;
  }

  // 获取所有保存的状态
  public getAllStates(): SceneState[] {
    return Array.from(this.states.values()).sort(
      (a, b) => b.timestamp - a.timestamp,
    ); // 按时间降序排序
  }

  // 获取特定场景的所有状态
  public getStatesByScene(sceneId: string): SceneState[] {
    return Array.from(this.states.values())
      .filter((state) => state.sceneId === sceneId)
      .sort((a, b) => b.timestamp - a.timestamp); // 按时间降序排序
  }

  // 删除状态
  public deleteState(stateId: string): boolean {
    if (!this.states.has(stateId)) {
      return false;
    }

    this.states.delete(stateId);

    if (this.currentStateId === stateId) {
      this.currentStateId = null;
    }

    // 更新本地存储
    this.saveToLocalStorage();

    return true;
  }

  // 清除所有状态
  public clearAllStates(): void {
    this.states.clear();
    this.currentStateId = null;

    // 清除本地存储
    localStorage.removeItem("sceneStates");
  }

  // 启用自动保存
  public enableAutoSave(interval: number = 60000): void {
    // this.autoSaveEnabled = true; // Temporarily commented out to avoid unused assignment if not used elsewhere

    this.autoSaveInterval = interval;

    // 清除现有定时器
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }

    // 设置新定时器
    this.autoSaveTimer = setInterval(() => {
      this.saveState(`自动保存 ${new Date().toLocaleString()}`);
    }, this.autoSaveInterval);
  }

  // 禁用自动保存
  public disableAutoSave(): void {
    // this.autoSaveEnabled = false; // Temporarily commented out to avoid unused assignment if not used elsewhere

    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
      this.autoSaveTimer = null;
    }
  }

  // 设置最大状态数
  public setMaxStates(max: number): void {
    this.maxStates = max;

    // 如果当前状态数超过新的最大值，删除最旧的状态
    if (this.states.size > this.maxStates) {
      const statesToDelete = this.states.size - this.maxStates;

      const sortedStates = Array.from(this.states.values()).sort(
        (a, b) => a.timestamp - b.timestamp,
      );

      for (let i = 0; i < statesToDelete; i++) {
        this.states.delete(sortedStates[i].id);
      }

      // 更新本地存储
      this.saveToLocalStorage();
    }
  }

  // 保存到本地存储
  private saveToLocalStorage(): void {
    try {
      const statesArray = Array.from(this.states.values());
      localStorage.setItem("sceneStates", JSON.stringify(statesArray));
    } catch (error) {
      console.error("保存场景状态到本地存储失败:", error);
    }
  }

  // 从本地存储加载
  public loadFromLocalStorage(): void {
    try {
      const statesJson = localStorage.getItem("sceneStates");

      if (statesJson) {
        const statesArray = JSON.parse(statesJson) as SceneState[];

        // 清除现有状态
        this.states.clear();

        // 加载状态
        statesArray.forEach((state) => {
          this.states.set(state.id, state);
        });

        console.log(`从本地存储加载了 ${statesArray.length} 个场景状态`);
      }
    } catch (error) {
      console.error("从本地存储加载场景状态失败:", error);
    }
  }
}

// 导出单例实例
export default SceneStateManager.getInstance();
