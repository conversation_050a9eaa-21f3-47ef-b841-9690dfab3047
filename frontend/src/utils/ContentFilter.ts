// 内容过滤工具类
class ContentFilter {
  private static instance: ContentFilter;
  
  // 敏感词库 - 在实际项目中应该从服务器获取更完整的词库
  private sensitiveWords: string[] = [
    // 政治敏感词
    '政治', '政府', '党派', '选举', '革命', '政变',
    
    // 暴力相关
    '杀害', '谋杀', '暴力', '殴打', '自杀', '伤害',
    
    // 色情内容
    '色情', '性爱', '裸体', '成人', '情色',
    
    // 赌博相关
    '赌博', '赌场', '彩票', '博彩', '赌注',
    
    // 毒品相关
    '毒品', '吸毒', '贩毒', '大麻', '可卡因',
    
    // 仇恨言论
    '仇恨', '歧视', '种族', '宗教冲突', '仇视',
    
    // 不当言论
    '诅咒', '诽谤', '恶毒', '咒骂', '辱骂',
    
    // 欺诈相关
    '诈骗', '欺诈', '假冒', '虚假', '诱骗'
  ];
  
  // 禁用词汇（完全禁止）
  private bannedWords: string[] = [
    'fuck', 'shit', 'damn', 'bitch', 'asshole',
    '妈的', '操你', '去死', '滚蛋', '白痴', '脑残'
  ];
  
  // 替换词汇映射
  private replacements: Record<string, string> = {
    '死': '*',
    '杀': '*',
    '恨': '*',
    '操': '*',
    '滚': '*',
  };

  private constructor() {}

  public static getInstance(): ContentFilter {
    if (!ContentFilter.instance) {
      ContentFilter.instance = new ContentFilter();
    }
    return ContentFilter.instance;
  }

  // 检查内容是否包含敏感词
  public checkSensitiveContent(content: string): {
    isSafe: boolean;
    level: 'safe' | 'warning' | 'blocked';
    detectedWords: string[];
    filteredContent: string;
    reason?: string;
  } {
    // 检查是否为空或只有空白字符
    if (!content.trim()) {
      return {
        isSafe: false,
        level: 'blocked',
        detectedWords: [],
        filteredContent: '',
        reason: '内容不能为空'
      };
    }

    // 检查内容长度
    if (content.length > 500) {
      return {
        isSafe: false,
        level: 'blocked',
        detectedWords: [],
        filteredContent: '',
        reason: '内容过长，请控制在500字以内'
      };
    }

    const normalizedContent = content.toLowerCase().trim();
    const detectedWords: string[] = [];
    let filteredContent = content;

    // 检查禁用词汇
    for (const word of this.bannedWords) {
      if (normalizedContent.includes(word.toLowerCase())) {
        detectedWords.push(word);
        return {
          isSafe: false,
          level: 'blocked',
          detectedWords,
          filteredContent: '',
          reason: '内容包含严重违规词汇，无法发布'
        };
      }
    }

    // 检查敏感词
    let hasWarning = false;
    for (const word of this.sensitiveWords) {
      if (normalizedContent.includes(word.toLowerCase())) {
        detectedWords.push(word);
        hasWarning = true;
      }
    }

    // 应用替换规则
    for (const [original, replacement] of Object.entries(this.replacements)) {
      const regex = new RegExp(original, 'gi');
      filteredContent = filteredContent.replace(regex, replacement);
    }

    if (hasWarning) {
      return {
        isSafe: true,
        level: 'warning',
        detectedWords,
        filteredContent,
        reason: '内容包含敏感词汇，已进行处理'
      };
    }

    return {
      isSafe: true,
      level: 'safe',
      detectedWords,
      filteredContent,
      reason: undefined
    };
  }

  // 检查昵称是否合适
  public checkNickname(nickname: string): {
    isValid: boolean;
    reason?: string;
    filteredNickname: string;
  } {
    if (!nickname.trim()) {
      return {
        isValid: false,
        reason: '昵称不能为空',
        filteredNickname: ''
      };
    }

    if (nickname.length > 20) {
      return {
        isValid: false,
        reason: '昵称过长，请控制在20字以内',
        filteredNickname: nickname.substring(0, 20)
      };
    }

    const contentCheck = this.checkSensitiveContent(nickname);
    
    return {
      isValid: contentCheck.level !== 'blocked',
      reason: contentCheck.reason,
      filteredNickname: contentCheck.filteredContent
    };
  }

  // 获取内容安全建议
  public getContentSuggestions(content: string): string[] {
    const suggestions: string[] = [];
    const check = this.checkSensitiveContent(content);

    if (check.level === 'blocked') {
      suggestions.push('请移除违规内容');
      suggestions.push('使用文明用语');
      suggestions.push('遵守社区规范');
    } else if (check.level === 'warning') {
      suggestions.push('建议使用更友善的表达方式');
      suggestions.push('避免使用可能引起争议的词汇');
    }

    if (content.length > 400) {
      suggestions.push('建议精简内容，控制在合适长度');
    }

    if (suggestions.length === 0) {
      suggestions.push('内容符合规范，可以发布');
    }

    return suggestions;
  }

  // 智能内容建议
  public suggestAlternatives(word: string): string[] {
    const alternatives: Record<string, string[]> = {
      '死': ['去世', '离世', '逝世'],
      '杀': ['消除', '去除', '移除'],
      '恨': ['不喜欢', '反感', '讨厌'],
      '操': ['管理', '处理', '操作'],
      '滚': ['离开', '走开', '请走'],
    };

    return alternatives[word] || [];
  }

  // 更新敏感词库（管理员功能）
  public updateSensitiveWords(newWords: string[]): void {
    this.sensitiveWords = [...new Set([...this.sensitiveWords, ...newWords])];
  }

  // 获取统计信息
  public getFilterStats(): {
    totalSensitiveWords: number;
    totalBannedWords: number;
    totalReplacements: number;
  } {
    return {
      totalSensitiveWords: this.sensitiveWords.length,
      totalBannedWords: this.bannedWords.length,
      totalReplacements: Object.keys(this.replacements).length
    };
  }
}

// 导出单例
export default ContentFilter.getInstance();
export { ContentFilter };