/**
 * 设备类型枚举
 */
export enum DeviceType {
  DESKTOP = "desktop",
  TABLET = "tablet",
  MOBILE = "mobile",
}

/**
 * 设备性能级别枚举
 */
export enum PerformanceLevel {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
}

/**
 * 设备信息接口
 */
export interface DeviceInfo {
  type: DeviceType;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isTouchDevice: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  browser: string;
  os: string;
  screenWidth: number;
  screenHeight: number;
  pixelRatio: number;
  performanceLevel: PerformanceLevel;
  gpuInfo?: {
    renderer: string;
    vendor: string;
  };
  connection?: {
    type: string;
    downlink: number;
    rtt: number;
    saveData: boolean;
  };
  batteryLevel?: number;
  batteryCharging?: boolean;
}

/**
 * 设备检测器类
 * 用于检测设备类型和性能
 */
class DeviceDetector {
  private static instance: DeviceDetector;
  private deviceInfo: DeviceInfo;

  // 私有构造函数
  private constructor() {
    this.deviceInfo = this.detectDevice();
  }

  // 获取单例实例
  public static getInstance(): DeviceDetector {
    if (!DeviceDetector.instance) {
      DeviceDetector.instance = new DeviceDetector();
    }
    return DeviceDetector.instance;
  }

  // 获取设备信息
  public getDeviceInfo(): DeviceInfo {
    return this.deviceInfo;
  }

  // 刷新设备信息
  public refreshDeviceInfo(): DeviceInfo {
    this.deviceInfo = this.detectDevice();
    return this.deviceInfo;
  }

  // 检测设备类型和性能
  private detectDevice(): DeviceInfo {
    const ua = navigator.userAgent;
    const pixelRatio = window.devicePixelRatio || 1;
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;

    // 检测设备类型
    const isMobile = /iPhone|iPod|Android|BlackBerry|IEMobile|Opera Mini/i.test(
      ua,
    );
    const isTablet = /iPad|Android/i.test(ua) && !/Mobile/i.test(ua);
    const isDesktop = !isMobile && !isTablet;
    const isTouchDevice =
      "ontouchstart" in window || navigator.maxTouchPoints > 0;

    // 检测操作系统
    const isIOS = /iPad|iPhone|iPod/i.test(ua);
    const isAndroid = /Android/i.test(ua);

    // 检测浏览器
    let browser = "Unknown";
    if (/Chrome/i.test(ua)) browser = "Chrome";
    else if (/Firefox/i.test(ua)) browser = "Firefox";
    else if (/Safari/i.test(ua)) browser = "Safari";
    else if (/Edge/i.test(ua)) browser = "Edge";
    else if (/MSIE|Trident/i.test(ua)) browser = "IE";

    // 检测操作系统
    let os = "Unknown";
    if (isIOS) os = "iOS";
    else if (isAndroid) os = "Android";
    else if (/Windows/i.test(ua)) os = "Windows";
    else if (/Mac OS X/i.test(ua)) os = "macOS";
    else if (/Linux/i.test(ua)) os = "Linux";

    // 确定设备类型
    let type: DeviceType;
    if (isDesktop) type = DeviceType.DESKTOP;
    else if (isTablet) type = DeviceType.TABLET;
    else type = DeviceType.MOBILE;

    // 检测GPU信息
    let gpuInfo;
    const canvas = document.createElement("canvas");
    const gl =
      canvas.getContext("webgl") || canvas.getContext("experimental-webgl");
    if (gl && gl instanceof WebGLRenderingContext) {
      // Added type check for gl
      const debugInfo = gl.getExtension("WEBGL_debug_renderer_info");
      if (debugInfo) {
        gpuInfo = {
          renderer: gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL),
          vendor: gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL),
        };
      }
    }

    // 检测网络连接信息
    let connection;
    if ("connection" in navigator) {
      const conn = (
        navigator as unknown as {
          connection: {
            effectiveType?: string;
            downlink?: number;
            rtt?: number;
            saveData?: boolean;
          };
        }
      ).connection;
      connection = {
        type: conn.effectiveType || "unknown",
        downlink: conn.downlink || 0,
        rtt: conn.rtt || 0,
        saveData: conn.saveData || false,
      };
    }

    // 检测电池信息
    let batteryLevel;
    let batteryCharging;
    if ("getBattery" in navigator) {
      (
        navigator as unknown as {
          getBattery: () => Promise<{ level: number; charging: boolean }>;
        }
      )
        .getBattery()
        .then((battery) => {
          batteryLevel = battery.level;
          batteryCharging = battery.charging;
        });
    }

    // 确定性能级别
    let performanceLevel: PerformanceLevel;

    // 基于设备类型、像素比和GPU信息确定性能级别
    if (
      isDesktop &&
      pixelRatio >= 1.5 &&
      gpuInfo?.renderer.includes("NVIDIA")
    ) {
      performanceLevel = PerformanceLevel.HIGH;
    } else if (isDesktop || (isTablet && pixelRatio >= 2)) {
      performanceLevel = PerformanceLevel.MEDIUM;
    } else {
      performanceLevel = PerformanceLevel.LOW;
    }

    // 如果是低端Android设备，降低性能级别
    if (
      isAndroid &&
      (/Android 4/i.test(ua) ||
        /Android 5/i.test(ua) ||
        screenWidth * pixelRatio < 1280)
    ) {
      performanceLevel = PerformanceLevel.LOW;
    }

    // 如果是旧款iOS设备，降低性能级别
    if (
      isIOS &&
      (/iPhone OS 9/i.test(ua) ||
        /iPhone OS 10/i.test(ua) ||
        /iPhone OS 11/i.test(ua))
    ) {
      performanceLevel = PerformanceLevel.LOW;
    }

    return {
      type,
      isMobile,
      isTablet,
      isDesktop,
      isTouchDevice,
      isIOS,
      isAndroid,
      browser,
      os,
      screenWidth,
      screenHeight,
      pixelRatio,
      performanceLevel,
      gpuInfo,
      connection,
      batteryLevel,
      batteryCharging,
    };
  }

  // 检测是否为低端设备
  public isLowEndDevice(): boolean {
    return this.deviceInfo.performanceLevel === PerformanceLevel.LOW;
  }

  // 检测是否为中端设备
  public isMidEndDevice(): boolean {
    return this.deviceInfo.performanceLevel === PerformanceLevel.MEDIUM;
  }

  // 检测是否为高端设备
  public isHighEndDevice(): boolean {
    return this.deviceInfo.performanceLevel === PerformanceLevel.HIGH;
  }

  // 获取推荐的渲染质量
  public getRecommendedQuality(): "low" | "medium" | "high" {
    switch (this.deviceInfo.performanceLevel) {
      case PerformanceLevel.LOW:
        return "low";
      case PerformanceLevel.MEDIUM:
        return "medium";
      case PerformanceLevel.HIGH:
        return "high";
      default:
        return "medium";
    }
  }

  // 获取推荐的像素比例
  public getRecommendedPixelRatio(): number {
    switch (this.deviceInfo.performanceLevel) {
      case PerformanceLevel.LOW:
        return Math.min(1, this.deviceInfo.pixelRatio);
      case PerformanceLevel.MEDIUM:
        return Math.min(1.5, this.deviceInfo.pixelRatio);
      case PerformanceLevel.HIGH:
        return this.deviceInfo.pixelRatio;
      default:
        return Math.min(1.5, this.deviceInfo.pixelRatio);
    }
  }

  // 获取推荐的阴影质量
  public getRecommendedShadowMapSize(): number {
    switch (this.deviceInfo.performanceLevel) {
      case PerformanceLevel.LOW:
        return 512;
      case PerformanceLevel.MEDIUM:
        return 1024;
      case PerformanceLevel.HIGH:
        return 2048;
      default:
        return 1024;
    }
  }

  // 获取推荐的最大绘制调用数
  public getRecommendedMaxDrawCalls(): number {
    switch (this.deviceInfo.performanceLevel) {
      case PerformanceLevel.LOW:
        return 100;
      case PerformanceLevel.MEDIUM:
        return 500;
      case PerformanceLevel.HIGH:
        return 2000;
      default:
        return 500;
    }
  }

  // 检测是否应该使用简化着色器
  public shouldUseSimplifiedShaders(): boolean {
    return this.deviceInfo.performanceLevel === PerformanceLevel.LOW;
  }

  // 检测是否应该禁用后处理效果
  public shouldDisablePostProcessing(): boolean {
    return this.deviceInfo.performanceLevel === PerformanceLevel.LOW;
  }

  // 检测是否应该使用低质量纹理
  public shouldUseLowQualityTextures(): boolean {
    return this.deviceInfo.performanceLevel === PerformanceLevel.LOW;
  }

  // 检测是否应该限制视距
  public shouldLimitViewDistance(): boolean {
    return this.deviceInfo.performanceLevel !== PerformanceLevel.HIGH;
  }

  // 获取推荐的视距
  public getRecommendedViewDistance(): number {
    switch (this.deviceInfo.performanceLevel) {
      case PerformanceLevel.LOW:
        return 100;
      case PerformanceLevel.MEDIUM:
        return 300;
      case PerformanceLevel.HIGH:
        return 1000;
      default:
        return 300;
    }
  }

  // 检测是否应该使用简化物理
  public shouldUseSimplifiedPhysics(): boolean {
    return this.deviceInfo.performanceLevel !== PerformanceLevel.HIGH;
  }

  // 检测是否应该限制粒子数量
  public shouldLimitParticles(): boolean {
    return this.deviceInfo.performanceLevel !== PerformanceLevel.HIGH;
  }

  // 获取推荐的粒子数量
  public getRecommendedParticleCount(): number {
    switch (this.deviceInfo.performanceLevel) {
      case PerformanceLevel.LOW:
        return 100;
      case PerformanceLevel.MEDIUM:
        return 500;
      case PerformanceLevel.HIGH:
        return 2000;
      default:
        return 500;
    }
  }
}

// 导出单例实例
export default DeviceDetector.getInstance();
