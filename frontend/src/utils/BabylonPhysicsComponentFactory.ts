import {
  Scene,
  Vector3,
  Abstract<PERSON><PERSON>,
  TransformNode,
  Quaternion,
} from "@babylonjs/core";
import {
  BabylonSceneComponentFactory,
  BabylonSceneComponent,
} from "./BabylonSceneSystem"; // BabylonSceneComponent will be used now
import {
  BabylonPhysicsSystem,
  PhysicsBodyType,
  PhysicsShapeType,
} from "./BabylonPhysicsSystem";

// Babylon物理组件工厂
export class BabylonPhysicsComponentFactory
  implements BabylonSceneComponentFactory
{
  public type = "physics";
  private physicsSystem: BabylonPhysicsSystem; // Store an instance of the class
  private scene: Scene | null = null; // Store scene for deferred initialization

  constructor(scene?: Scene) {
    this.physicsSystem = BabylonPhysicsSystem.getInstance();
    if (scene) {
      this.scene = scene;
    }
  }

  // 设置场景
  setScene(scene: Scene): void {
    this.scene = scene;
  }

  async create(
    id: string,
    name: string,
    properties: Record<string, unknown>,
    scene: Scene, // Added scene parameter to match interface
  ): Promise<BabylonSceneComponent> {
    // Changed SceneComponent to BabylonSceneComponent
    // 确保物理系统已初始化
    if (!this.physicsSystem.isInitialized()) {
      // Use the passed scene argument
      await this.physicsSystem.init(scene);
    }

    // 创建一个空的变换节点作为容器
    const transformNode = new TransformNode(name, scene); // Use the passed scene argument

    // 创建场景组件
    const component: BabylonSceneComponent = {
      // Changed SceneComponent to BabylonSceneComponent
      id,
      name,
      type: this.type,
      node: transformNode,
      position: new Vector3(0, 0, 0),
      rotation: new Vector3(0, 0, 0),
      scaling: new Vector3(1, 1, 1),
      visible: true,
      interactive: (properties.interactive as boolean) || false, // Type assertion for interactive
      properties,
      children: [],
    };

    return component;
  }

  // 为现有对象添加物理属性
  async addPhysicsToObject(
    objectId: string,
    mesh: AbstractMesh,
    bodyType: PhysicsBodyType = PhysicsBodyType.DYNAMIC,
    shapeType: PhysicsShapeType = PhysicsShapeType.BOX,
    options: {
      mass?: number;
      restitution?: number;
      friction?: number;
      linearDamping?: number;
      angularDamping?: number;
      isSensor?: boolean;
      userData?: Record<string, unknown>;
    } = {},
  ): Promise<boolean> {
    // 确保物理系统已初始化
    if (!this.physicsSystem.isInitialized() && this.scene) {
      await this.physicsSystem.init(this.scene);
    } else if (!this.physicsSystem.isInitialized()) {
      console.error("物理系统未初始化，且未提供场景");
      return false;
    }

    // 添加物理对象
    const physicsBody = this.physicsSystem.addBody(
      objectId,
      mesh,
      bodyType,
      shapeType,
      options,
    );

    return !!physicsBody;
  }

  // 应用力
  applyForce(objectId: string, force: Vector3, point?: Vector3): boolean {
    return this.physicsSystem.applyForce(objectId, force, point);
  }

  // 应用冲量
  applyImpulse(objectId: string, impulse: Vector3, point?: Vector3): boolean {
    return this.physicsSystem.applyImpulse(objectId, impulse, point);
  }

  // 设置位置
  setPosition(objectId: string, position: Vector3): boolean {
    return this.physicsSystem.setPosition(objectId, position);
  }

  // 设置旋转
  setRotation(objectId: string, rotation: Vector3): boolean {
    return this.physicsSystem.setRotation(objectId, rotation);
  }

  // 设置四元数旋转
  setRotationQuaternion(objectId: string, quaternion: Quaternion): boolean {
    return this.physicsSystem.setRotationQuaternion(objectId, quaternion);
  }

  // 设置线性速度
  setLinearVelocity(objectId: string, velocity: Vector3): boolean {
    return this.physicsSystem.setLinearVelocity(objectId, velocity);
  }

  // 设置角速度
  setAngularVelocity(objectId: string, velocity: Vector3): boolean {
    return this.physicsSystem.setAngularVelocity(objectId, velocity);
  }

  // 获取线性速度
  getLinearVelocity(objectId: string): Vector3 | null {
    return this.physicsSystem.getLinearVelocity(objectId);
  }

  // 获取角速度
  getAngularVelocity(objectId: string): Vector3 | null {
    return this.physicsSystem.getAngularVelocity(objectId);
  }

  // 移除物理对象
  removePhysicsFromObject(objectId: string): boolean {
    return this.physicsSystem.removeBody(objectId);
  }
}

export default BabylonPhysicsComponentFactory;
