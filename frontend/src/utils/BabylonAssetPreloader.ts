import {
  Scene,
  AssetsManager,
  TextureAssetTask,
  MeshAssetTask,
  BinaryFileAssetTask,
  CubeTextureAssetTask,
  AbstractAssetTask,
} from "@babylonjs/core";
import BabylonResourceManager, { ResourceType } from "./BabylonResourceManager";
import BabylonAudioManager, { AudioType } from "./BabylonAudioManager";

/**
 * 资源预加载状态
 */
export enum PreloadStatus {
  IDLE = "idle",
  LOADING = "loading",
  COMPLETED = "completed",
  ERROR = "error",
}

/**
 * 资源预加载进度
 */
export interface PreloadProgress {
  total: number;
  loaded: number;
  percentage: number;
  status: PreloadStatus;
  errors: string[];
}

/**
 * 资源预加载配置
 */
export interface PreloadConfig {
  models?: {
    id: string;
    path: string;
    rootUrl?: string;
    fileName?: string;
  }[];
  textures?: {
    id: string;
    path: string;
  }[];
  cubeTextures?: {
    id: string;
    rootUrl: string;
    extensions: string[];
  }[];
  audio?: {
    id: string;
    path: string;
    type: AudioType;
    options?: {
      loop?: boolean;
      volume?: number;
      autoplay?: boolean;
      spatialSound?: boolean;
    };
  }[];
  binaries?: {
    id: string;
    path: string;
  }[];
}

/**
 * Babylon.js资源预加载器
 * 用于预加载和缓存3D资源
 */
export class BabylonAssetPreloader {
  private static instance: BabylonAssetPreloader;
  private scene: Scene | null = null;
  private assetsManager: AssetsManager | null = null;
  private progress: PreloadProgress = {
    total: 0,
    loaded: 0,
    percentage: 0,
    status: PreloadStatus.IDLE,
    errors: [],
  };
  private progressCallback: ((progress: PreloadProgress) => void) | null = null;

  // 私有构造函数
  private constructor() {}

  // 获取单例实例
  public static getInstance(): BabylonAssetPreloader {
    if (!BabylonAssetPreloader.instance) {
      BabylonAssetPreloader.instance = new BabylonAssetPreloader();
    }
    return BabylonAssetPreloader.instance;
  }

  /**
   * 初始化预加载器
   * @param scene Babylon.js场景
   */
  public init(scene: Scene): void {
    this.scene = scene;
    this.assetsManager = new AssetsManager(scene);

    // 配置资源管理器
    if (this.assetsManager) {
      this.assetsManager.useDefaultLoadingScreen = false;
      this.assetsManager.autoHideLoadingUI = true;

      // 设置回调
      this.assetsManager.onProgress = (remainingCount, totalCount) => {
        this.progress.loaded = totalCount - remainingCount;
        this.progress.total = totalCount;
        this.progress.percentage = Math.floor(
          (this.progress.loaded / this.progress.total) * 100,
        );

        if (this.progressCallback) {
          this.progressCallback(this.progress);
        }
      };

      this.assetsManager.onTaskError = (task) => {
        this.progress.errors.push(`加载失败: ${task.name}`);

        if (this.progressCallback) {
          this.progressCallback(this.progress);
        }
      };

      this.assetsManager.onTaskSuccess = (task: AbstractAssetTask) => {
        // 将资源添加到资源管理器
        if (task instanceof MeshAssetTask) {
          task.loadedMeshes.forEach((mesh) => {
            BabylonResourceManager.getInstance().addResource(
              task.name,
              ResourceType.MODEL,
              undefined, // Priority will use default
              mesh, // Pass the preloaded mesh
            );
          });
        } else if (task instanceof TextureAssetTask) {
          BabylonResourceManager.getInstance().addResource(
            task.name,
            ResourceType.TEXTURE,
            undefined, // Priority will use default
            task.texture, // Pass the preloaded texture
          );
        } else if (task instanceof CubeTextureAssetTask) {
          BabylonResourceManager.getInstance().addResource(
            task.name,
            ResourceType.CUBETEXTURE,
            undefined, // Priority will use default
            task.texture, // Pass the preloaded cube texture
          );
        } else if (task instanceof BinaryFileAssetTask) {
          BabylonResourceManager.getInstance().addResource(
            task.name,
            ResourceType.BINARY,
            undefined, // Priority will use default
            task.data, // Pass the preloaded binary data
          );
        }
      };

      this.assetsManager.onFinish = () => {
        this.progress.status =
          this.progress.errors.length > 0
            ? PreloadStatus.ERROR
            : PreloadStatus.COMPLETED;

        if (this.progressCallback) {
          this.progressCallback(this.progress);
        }
      };
    }
  }

  /**
   * 预加载资源
   * @param config 预加载配置
   * @param progressCallback 进度回调
   */
  public async preload(
    config: PreloadConfig,
    progressCallback?: (progress: PreloadProgress) => void,
  ): Promise<boolean> {
    if (!this.scene || !this.assetsManager) {
      console.error("预加载器未初始化");
      return false;
    }

    // 重置进度
    this.progress = {
      total: 0,
      loaded: 0,
      percentage: 0,
      status: PreloadStatus.LOADING,
      errors: [],
    };

    // 设置进度回调
    this.progressCallback = progressCallback || null;

    // 添加模型任务
    if (config.models) {
      config.models.forEach((model) => {
        if (model.rootUrl && model.fileName) {
          // 使用rootUrl和fileName
          this.assetsManager!.addMeshTask(
            model.id,
            "",
            model.rootUrl,
            model.fileName,
          );
        } else {
          // 使用完整路径
          const lastSlashIndex = model.path.lastIndexOf("/");
          const rootUrl = model.path.substring(0, lastSlashIndex + 1);
          const fileName = model.path.substring(lastSlashIndex + 1);

          this.assetsManager!.addMeshTask(model.id, "", rootUrl, fileName);
        }
      });
    }

    // 添加纹理任务
    if (config.textures) {
      config.textures.forEach((texture) => {
        this.assetsManager!.addTextureTask(texture.id, texture.path);
      });
    }

    // 添加立方体纹理任务
    if (config.cubeTextures) {
      config.cubeTextures.forEach((cubeTexture) => {
        this.assetsManager!.addCubeTextureTask(
          cubeTexture.id,
          cubeTexture.rootUrl,
          cubeTexture.extensions,
        );
      });
    }

    // 添加二进制文件任务
    if (config.binaries) {
      config.binaries.forEach((binary) => {
        this.assetsManager!.addBinaryFileTask(binary.id, binary.path);
      });
    }

    // 加载音频（不使用AssetsManager，因为它不支持音频）
    if (config.audio) {
      // 确保音频管理器已初始化
      if (!BabylonAudioManager.getInstance().isInitialized()) {
        BabylonAudioManager.getInstance().init(this.scene!);
      }

      // 加载音频
      for (const audio of config.audio) {
        await BabylonAudioManager.getInstance().loadAudio(
          audio.id,
          audio.path,
          audio.type,
          audio.options,
        );
      }
    }

    // 开始加载
    return new Promise((resolve) => {
      this.assetsManager!.load();

      // 监听完成事件
      this.assetsManager!.onFinish = () => {
        this.progress.status =
          this.progress.errors.length > 0
            ? PreloadStatus.ERROR
            : PreloadStatus.COMPLETED;

        if (this.progressCallback) {
          this.progressCallback(this.progress);
        }

        resolve(this.progress.errors.length === 0);
      };
    });
  }

  /**
   * 获取当前进度
   */
  public getProgress(): PreloadProgress {
    return { ...this.progress };
  }

  /**
   * 重置预加载器
   */
  public reset(): void {
    this.progress = {
      total: 0,
      loaded: 0,
      percentage: 0,
      status: PreloadStatus.IDLE,
      errors: [],
    };

    this.progressCallback = null;

    if (this.assetsManager) {
      this.assetsManager.reset();
    }
  }
}

export default BabylonAssetPreloader.getInstance();
