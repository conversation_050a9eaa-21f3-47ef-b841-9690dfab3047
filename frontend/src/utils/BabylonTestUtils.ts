import {
  Scene,
  Engine,
  SceneOptimizer,
  SceneOptimizerOptions,
  MergeMeshesOptimization,
  TextureOptimization,
  HardwareScalingOptimization,
  ShadowsOptimization,
  PostProcessesOptimization,
  LensFlaresOptimization,
  ParticlesOptimization,
  RenderTargetsOptimization,
  EngineInstrumentation,
  SceneInstrumentation,
  Vector3,
  MeshBuilder,
  StandardMaterial,
  Color3,
} from "@babylonjs/core";
import {
  BabylonPhysicsSystem,
  PhysicsEngineType,
  PhysicsBodyType,
  PhysicsShapeType,
} from "./BabylonPhysicsSystem";
import BabylonAudioManager from "./BabylonAudioManager";

/**
 * Babylon.js测试工具类
 * 用于测试和优化Babylon.js组件
 */
export class BabylonTestUtils {
  /**
   * 测试场景性能
   * @param scene Babylon.js场景
   * @param engine Babylon.js引擎
   * @param duration 测试持续时间（毫秒）
   * @returns 性能测试结果
   */
  public static async testScenePerformance(
    scene: Scene,
    engine: Engine,
    duration: number = 5000,
  ): Promise<{
    fps: number;
    drawCalls: number;
    triangles: number;
    renderTime: number;
    frameTime: number;
    meshes: number;
    materials: number;
    textures: number;
  }> {
    // 创建场景检测工具
    const sceneInstrumentation = new SceneInstrumentation(scene);
    sceneInstrumentation.captureFrameTime = true;
    sceneInstrumentation.captureRenderTime = true;
    sceneInstrumentation.captureActiveMeshesEvaluationTime = true;

    // 创建引擎检测工具
    const engineInstrumentation = new EngineInstrumentation(engine);
    engineInstrumentation.captureGPUFrameTime = true;
    engineInstrumentation.captureShaderCompilationTime = true;

    // 收集的数据
    const fpsValues: number[] = [];
    const drawCallsValues: number[] = [];
    const trianglesValues: number[] = [];
    const renderTimeValues: number[] = [];
    const frameTimeValues: number[] = [];

    // 开始测试
    const startTime = performance.now();

    // 创建测试Promise
    return new Promise((resolve) => {
      // 创建测试观察器
      const observer = scene.onAfterRenderObservable.add(() => {
        // 收集数据
        fpsValues.push(engine.getFps());
        drawCallsValues.push(engine._drawCalls.current); // Access the 'current' property for the actual value
        trianglesValues.push(scene.getTotalVertices());
        renderTimeValues.push(sceneInstrumentation.renderTimeCounter.current);
        frameTimeValues.push(sceneInstrumentation.frameTimeCounter.current);

        // 检查是否完成测试
        if (performance.now() - startTime >= duration) {
          // 移除观察器
          scene.onAfterRenderObservable.remove(observer);

          // 计算平均值
          const avgFps =
            fpsValues.reduce((sum, value) => sum + value, 0) / fpsValues.length;
          const avgDrawCalls =
            drawCallsValues.reduce((sum, value) => sum + value, 0) /
            drawCallsValues.length;
          const avgTriangles =
            trianglesValues.reduce((sum, value) => sum + value, 0) /
            trianglesValues.length;
          const avgRenderTime =
            renderTimeValues.reduce((sum, value) => sum + value, 0) /
            renderTimeValues.length;
          const avgFrameTime =
            frameTimeValues.reduce((sum, value) => sum + value, 0) /
            frameTimeValues.length;

          // 返回结果
          resolve({
            fps: avgFps,
            drawCalls: avgDrawCalls,
            triangles: avgTriangles,
            renderTime: avgRenderTime,
            frameTime: avgFrameTime,
            meshes: scene.meshes.length,
            materials: scene.materials.length,
            textures: scene.textures.length,
          });
        }
      });
    });
  }

  /**
   * 优化场景
   * @param scene Babylon.js场景
   * @param targetFps 目标帧率
   */
  public static optimizeScene(scene: Scene, targetFps: number = 60): void {
    // 创建场景优化器选项
    const options = new SceneOptimizerOptions(targetFps);

    // 添加优化步骤
    options.addOptimization(new MergeMeshesOptimization(0));
    options.addOptimization(new TextureOptimization(1, 512));
    options.addOptimization(new HardwareScalingOptimization(2, 4));
    options.addOptimization(new ShadowsOptimization(3));
    options.addOptimization(new PostProcessesOptimization(4));
    options.addOptimization(new LensFlaresOptimization(5));
    options.addOptimization(new ParticlesOptimization(6));
    options.addOptimization(new RenderTargetsOptimization(7));

    // 创建场景优化器
    const optimizer = new SceneOptimizer(scene, options);

    // 启动优化
    optimizer.start();
  }

  /**
   * 创建测试场景
   * @param scene Babylon.js场景
   * @param count 对象数量
   */
  public static createTestScene(scene: Scene, count: number = 100): void {
    // 初始化物理系统
    const physicsSystem = BabylonPhysicsSystem.getInstance();
    physicsSystem.init(scene, PhysicsEngineType.CANNON);

    // 初始化音频系统
    const audioManager = BabylonAudioManager.getInstance(); // Correctly get the singleton instance
    audioManager.init(scene);

    // 创建地面
    const ground = MeshBuilder.CreateGround(
      "ground",
      { width: 50, height: 50 },
      scene,
    );
    const groundMaterial = new StandardMaterial("groundMaterial", scene);
    groundMaterial.diffuseColor = new Color3(0.2, 0.2, 0.2);
    ground.material = groundMaterial;

    // 为地面添加物理特性
    physicsSystem.addBody(
      "ground",
      ground,
      PhysicsBodyType.STATIC,
      PhysicsShapeType.BOX,
      {
        restitution: 0.5,
        friction: 0.1,
      },
    );

    // 创建测试对象
    for (let i = 0; i < count; i++) {
      // 随机位置
      const position = new Vector3(
        (Math.random() - 0.5) * 20,
        10 + i * 0.5,
        (Math.random() - 0.5) * 20,
      );

      // 随机大小
      const size = Math.random() * 0.5 + 0.5;

      // 随机颜色
      const color = new Color3(Math.random(), Math.random(), Math.random());

      // 随机形状
      const shapeTypes = [
        PhysicsShapeType.BOX,
        PhysicsShapeType.SPHERE,
        PhysicsShapeType.CYLINDER,
      ];
      const shapeType =
        shapeTypes[Math.floor(Math.random() * shapeTypes.length)];

      // 创建网格
      let mesh;
      switch (shapeType) {
        case PhysicsShapeType.SPHERE:
          mesh = MeshBuilder.CreateSphere(
            `sphere_${i}`,
            { diameter: size * 2 },
            scene,
          );
          break;
        case PhysicsShapeType.CYLINDER:
          mesh = MeshBuilder.CreateCylinder(
            `cylinder_${i}`,
            { height: size * 2, diameter: size * 2 },
            scene,
          );
          break;
        case PhysicsShapeType.BOX:
        default:
          mesh = MeshBuilder.CreateBox(`box_${i}`, { size: size * 2 }, scene);
      }

      // 设置位置
      mesh.position = position;

      // 创建材质
      const material = new StandardMaterial(`material_${i}`, scene);
      material.diffuseColor = color;
      mesh.material = material;

      // 添加物理特性
      physicsSystem.addBody(
        `object_${i}`,
        mesh,
        PhysicsBodyType.DYNAMIC,
        shapeType,
        {
          mass: 1,
          restitution: 0.5,
          friction: 0.5,
        },
      );
    }
  }
}

export default BabylonTestUtils;
