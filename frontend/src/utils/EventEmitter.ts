/**
 * 事件发射器类
 * 用于实现事件订阅和发布功能
 */
export class EventEmitter {
  private events: Map<string, ((...args: unknown[]) => void)[]> = new Map();

  /**
   * 订阅事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  public on(event: string, callback: (...args: unknown[]) => void): void {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }

    this.events.get(event)!.push(callback);
  }

  /**
   * 取消订阅事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  public off(event: string, callback: (...args: unknown[]) => void): void {
    if (!this.events.has(event)) {
      return;
    }

    const callbacks = this.events.get(event)!;
    const index = callbacks.indexOf(callback);

    if (index !== -1) {
      callbacks.splice(index, 1);
    }

    if (callbacks.length === 0) {
      this.events.delete(event);
    }
  }

  /**
   * 发布事件
   * @param event 事件名称
   * @param args 事件参数
   */
  public emit(event: string, ...args: unknown[]): void {
    if (!this.events.has(event)) {
      return;
    }

    const callbacks = this.events.get(event)!;

    for (const callback of callbacks) {
      callback(...args);
    }
  }

  /**
   * 订阅一次性事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  public once(event: string, callback: (...args: unknown[]) => void): void {
    const onceCallback = (...args: unknown[]) => {
      callback(...args);
      this.off(event, onceCallback);
    };

    this.on(event, onceCallback);
  }

  /**
   * 清除所有事件
   */
  public clear(): void {
    this.events.clear();
  }

  /**
   * 清除特定事件
   * @param event 事件名称
   */
  public clearEvent(event: string): void {
    this.events.delete(event);
  }
}

export default EventEmitter;
