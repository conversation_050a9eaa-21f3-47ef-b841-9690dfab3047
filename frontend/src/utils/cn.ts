import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * 组合和合并 Tailwind CSS 类名的工具函数
 *
 * 这个函数结合了 clsx 和 tailwind-merge 的功能：
 * - clsx: 条件性地组合类名
 * - tailwind-merge: 智能合并冲突的 Tailwind 类名
 *
 * @param inputs - 类名输入，可以是字符串、对象、数组等
 * @returns 合并后的类名字符串
 *
 * @example
 * ```typescript
 * // 基础用法
 * cn('px-4 py-2', 'bg-blue-500') // 'px-4 py-2 bg-blue-500'
 *
 * // 条件类名
 * cn('px-4 py-2', {
 *   'bg-blue-500': isActive,
 *   'bg-gray-300': !isActive
 * })
 *
 * // 智能合并冲突的类名
 * cn('px-4 py-2 bg-blue-500', 'bg-red-500') // 'px-4 py-2 bg-red-500'
 *
 * // 与组件 props 结合
 * cn(
 *   'base-button-classes',
 *   {
 *     'variant-primary': variant === 'primary',
 *     'variant-secondary': variant === 'secondary'
 *   },
 *   className // 来自 props 的自定义类名
 * )
 * ```
 */
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}

/**
 * 创建变体类名的辅助函数
 *
 * @param base - 基础类名
 * @param variants - 变体映射对象
 * @param defaultVariant - 默认变体
 * @returns 返回一个函数，接受变体参数并返回对应的类名
 *
 * @example
 * ```typescript
 * const buttonVariants = createVariants({
 *   base: 'px-4 py-2 rounded-md font-medium transition-colors focus:outline-none focus:ring-2',
 *   variants: {
 *     variant: {
 *       primary: 'bg-amber-500 text-white hover:bg-amber-600 focus:ring-amber-500',
 *       secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
 *       outline: 'border border-gray-300 bg-transparent hover:bg-gray-50 focus:ring-gray-500'
 *     },
 *     size: {
 *       sm: 'px-3 py-1.5 text-sm',
 *       md: 'px-4 py-2 text-base',
 *       lg: 'px-6 py-3 text-lg'
 *     }
 *   },
 *   defaultVariants: {
 *     variant: 'primary',
 *     size: 'md'
 *   }
 * });
 *
 * // 使用
 * const className = buttonVariants({ variant: 'secondary', size: 'lg' });
 * ```
 */
export function createVariants<
  T extends Record<string, Record<string, string>>,
>({
  base,
  variants,
  defaultVariants,
}: {
  base: string;
  variants: T;
  defaultVariants: Partial<{ [K in keyof T]: keyof T[K] }>;
}) {
  return function (
    props: Partial<{ [K in keyof T]: keyof T[K] }> & {
      className?: string;
    } = {},
  ) {
    const { className, ...variantProps } = props;

    const variantClasses = Object.entries(variants).map(([key, variantMap]) => {
      const variantKey =
        variantProps[key as keyof typeof variantProps] ||
        defaultVariants[key as keyof typeof defaultVariants];
      return variantKey ? variantMap[variantKey as string] : "";
    });

    return cn(base, ...variantClasses, className);
  };
}

/**
 * 常用的样式组合
 */
export const commonStyles = {
  // 按钮基础样式
  buttonBase:
    "inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",

  // 输入框基础样式
  inputBase:
    "block w-full rounded-md border-gray-300 shadow-sm focus:border-amber-500 focus:ring-amber-500 sm:text-sm",

  // 卡片基础样式
  cardBase: "bg-white rounded-lg shadow-sm border border-gray-200",

  // 容器样式
  container: "mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",

  // 文本样式
  heading1: "text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl",
  heading2: "text-2xl font-bold tracking-tight text-gray-900 sm:text-3xl",
  heading3: "text-xl font-semibold text-gray-900",
  bodyText: "text-base text-gray-600",
  smallText: "text-sm text-gray-500",

  // 布局样式
  flexCenter: "flex items-center justify-center",
  flexBetween: "flex items-center justify-between",
  gridCols: "grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3",

  // 状态样式
  loading: "animate-pulse",
  disabled: "opacity-50 cursor-not-allowed",
  hidden: "sr-only",

  // 过渡动画
  transition: "transition-all duration-200 ease-in-out",
  fadeIn: "animate-in fade-in duration-200",
  slideIn: "animate-in slide-in-from-bottom duration-300",
} as const;

/**
 * 响应式断点辅助函数
 */
export const breakpoints = {
  sm: "(min-width: 640px)",
  md: "(min-width: 768px)",
  lg: "(min-width: 1024px)",
  xl: "(min-width: 1280px)",
  "2xl": "(min-width: 1536px)",
} as const;

/**
 * 颜色主题辅助函数
 */
export const colors = {
  primary: {
    50: "bg-amber-50 text-amber-900",
    100: "bg-amber-100 text-amber-900",
    500: "bg-amber-500 text-white",
    600: "bg-amber-600 text-white",
    900: "bg-amber-900 text-white",
  },
  gray: {
    50: "bg-gray-50 text-gray-900",
    100: "bg-gray-100 text-gray-900",
    500: "bg-gray-500 text-white",
    600: "bg-gray-600 text-white",
    900: "bg-gray-900 text-white",
  },
  success: {
    50: "bg-green-50 text-green-900",
    500: "bg-green-500 text-white",
    600: "bg-green-600 text-white",
  },
  error: {
    50: "bg-red-50 text-red-900",
    500: "bg-red-500 text-white",
    600: "bg-red-600 text-white",
  },
  warning: {
    50: "bg-yellow-50 text-yellow-900",
    500: "bg-yellow-500 text-white",
    600: "bg-yellow-600 text-white",
  },
} as const;
