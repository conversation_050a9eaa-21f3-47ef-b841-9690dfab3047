// HTTP请求拦截器，自动处理token刷新
import { authService } from "../services/authService";

// 全局请求计数器，防止无限递归
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (token: string) => void;
  reject: (error: Error) => void;
}> = [];

// 处理队列中的请求
function processQueue(error: Error | null, token: string | null = null) {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else if (token) {
      resolve(token);
    }
  });

  failedQueue = [];
}

// 创建带有拦截器的fetch函数
export async function interceptedFetch(
  url: string | URL | Request,
  options: RequestInit = {},
): Promise<Response> {
  const token = authService.getStoredToken();

  // 添加认证头
  if (token && !authService.isTokenExpired()) {
    options.headers = {
      ...options.headers,
      Authorization: `Bearer ${token}`,
    };
  }

  // 发送请求
  let response = await fetch(url, options);

  // 如果是401错误，尝试刷新token
  if (response.status === 401 && token) {
    const originalRequest = { url, options };

    if (isRefreshing) {
      // 如果正在刷新，将请求加入队列
      return new Promise((resolve, reject) => {
        failedQueue.push({
          resolve: (newToken: string) => {
            originalRequest.options.headers = {
              ...originalRequest.options.headers,
              Authorization: `Bearer ${newToken}`,
            };
            resolve(fetch(originalRequest.url, originalRequest.options));
          },
          reject,
        });
      });
    }

    isRefreshing = true;

    try {
      const tokenResponse = await authService.refreshToken();
      const newToken = tokenResponse.access_token;

      // 更新请求头
      originalRequest.options.headers = {
        ...originalRequest.options.headers,
        Authorization: `Bearer ${newToken}`,
      };

      // 处理队列中的请求
      processQueue(null, newToken);

      // 重新发送原始请求
      response = await fetch(originalRequest.url, originalRequest.options);
    } catch (error) {
      // 刷新失败，处理队列并抛出错误
      processQueue(
        error instanceof Error ? error : new Error("Token refresh failed"),
      );

      // 清理登录状态，重定向到登录页
      authService.logout();
      window.location.href = "/login";

      throw error;
    } finally {
      isRefreshing = false;
    }
  }

  return response;
}

// 为常用的HTTP方法创建封装函数
export const httpClient = {
  async get(url: string, options: RequestInit = {}) {
    return interceptedFetch(url, { ...options, method: "GET" });
  },

  async post(url: string, data?: unknown, options: RequestInit = {}) {
    return interceptedFetch(url, {
      ...options,
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      body: data ? JSON.stringify(data) : undefined,
    });
  },

  async put(url: string, data?: unknown, options: RequestInit = {}) {
    return interceptedFetch(url, {
      ...options,
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      body: data ? JSON.stringify(data) : undefined,
    });
  },

  async patch(url: string, data?: unknown, options: RequestInit = {}) {
    return interceptedFetch(url, {
      ...options,
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      body: data ? JSON.stringify(data) : undefined,
    });
  },

  async delete(url: string, options: RequestInit = {}) {
    return interceptedFetch(url, { ...options, method: "DELETE" });
  },
};

export default httpClient;
