import {
  Scene,
  Texture,
  CubeTexture,
  Sound,
  AssetsManager,
  AbstractAssetTask,
} from "@babylonjs/core";
import "@babylonjs/loaders";

// 资源类型
export enum ResourceType {
  MODEL = "model",
  TEXTURE = "texture",
  CUBETEXTURE = "cubetexture",
  AUDIO = "audio",
  JSON = "json",
  IMAGE = "image",
  BINARY = "binary", // Added BINARY type
}

// 资源优先级
export enum ResourcePriority {
  LOW = 0,
  MEDIUM = 1,
  HIGH = 2,
  CRITICAL = 3,
}

// 资源项接口
export interface ResourceItem {
  url: string;
  type: ResourceType;
  priority: ResourcePriority;
  loaded: boolean;
  resource: unknown;
  preloadedResource?: unknown; // For resources added via AssetPreloader
  error?: Error;
  dependencies?: string[];
}

// 加载进度回调
export interface LoadingProgress {
  url: string;
  loaded: number;
  total: number;
  progress: number;
}

// 资源管理器类
export default class BabylonResourceManager {
  private static instance: BabylonResourceManager;
  private resources: Map<string, ResourceItem> = new Map();
  private scene: Scene | null = null;
  private assetsManager: AssetsManager | null = null;
  private onProgressCallback?: (progress: LoadingProgress) => void;
  private onCompleteCallback?: () => void;
  private totalResources: number = 0;
  private loadedResources: number = 0;

  // 私有构造函数
  private constructor() {}

  // 获取单例实例
  public static getInstance(): BabylonResourceManager {
    if (!BabylonResourceManager.instance) {
      BabylonResourceManager.instance = new BabylonResourceManager();
    }
    return BabylonResourceManager.instance;
  }

  // 设置场景
  public setScene(scene: Scene): void {
    this.scene = scene;
    this.assetsManager = new AssetsManager(scene);

    // 设置资产管理器回调
    this.assetsManager.onProgress = (
      remainingCount: number,
      totalCount: number,
    ) => {
      if (this.onProgressCallback) {
        this.onProgressCallback({
          url: "",
          loaded: totalCount - remainingCount,
          total: totalCount,
          progress: (totalCount - remainingCount) / totalCount,
        });
      }
    };

    this.assetsManager.onFinish = () => {
      if (this.onCompleteCallback) {
        this.onCompleteCallback();
      }
    };
  }

  // 设置进度回调
  public setProgressCallback(
    callback: (progress: LoadingProgress) => void,
  ): void {
    this.onProgressCallback = callback;
  }

  // 设置完成回调
  public setCompleteCallback(callback: () => void): void {
    this.onCompleteCallback = callback;
  }

  // 添加资源到队列
  public addResource(
    url: string,
    type: ResourceType,
    priority: ResourcePriority = ResourcePriority.MEDIUM,
    preloadedResource?: unknown, // New optional parameter
    dependencies?: string[],
  ): void {
    if (this.resources.has(url)) {
      return;
    }

    const resource: ResourceItem = {
      url,
      type,
      priority,
      loaded: !!preloadedResource, // Set to true if preloadedResource is provided
      resource: preloadedResource || null, // Store preloadedResource if provided
      preloadedResource,
      dependencies,
    };

    this.resources.set(url, resource);
    if (!preloadedResource) {
      this.totalResources++; // Only increment if not preloaded, as preloaded ones don't go through AssetsManager loading queue here
    }
  }

  // 添加多个资源
  public addResources(
    resources: {
      url: string;
      type: ResourceType;
      priority?: ResourcePriority;
      dependencies?: string[];
    }[],
  ): void {
    resources.forEach((res) => {
      this.addResource(
        res.url,
        res.type,
        res.priority || ResourcePriority.MEDIUM,
        res.dependencies,
      );
    });
  }

  // 开始加载资源
  public startLoading(): Promise<void> {
    if (!this.scene || !this.assetsManager) {
      return Promise.reject(new Error("场景或资产管理器未设置"));
    }

    return new Promise<void>((resolve) => {
      // 创建资产任务
      this.resources.forEach((resource, url) => {
        if (!resource.loaded) {
          this.createAssetTask(url, resource.type);
        }
      });

      // 设置完成回调
      if (this.assetsManager) {
        this.assetsManager.onTaskSuccessObservable.add(
          (eventData: AbstractAssetTask) => {
            const task = { name: eventData.name, task: eventData };
            const resource = this.resources.get(task.name);
            if (resource) {
              resource.loaded = true;
              resource.resource = task.task;
              this.loadedResources++;
            }
          },
        );

        this.assetsManager.onTaskErrorObservable.add(
          (task: { name: string; errorObject?: unknown }) => {
            const resource = this.resources.get(task.name);
            if (resource) {
              resource.error = new Error(`加载资源失败: ${task.name}`);
              console.error(`加载资源失败: ${task.name}`, task.errorObject);
            }
          },
        );

        const originalOnFinish = this.assetsManager.onFinish;
        this.assetsManager.onFinish = (tasks) => {
          if (originalOnFinish) {
            originalOnFinish(tasks);
          }
          resolve();
        };
        // 开始加载
        this.assetsManager.load();
      }
    });
  }

  // 创建资产任务
  private createAssetTask(url: string, type: ResourceType): void {
    if (!this.scene || !this.assetsManager) {
      return;
    }

    // 解析路径获取rootUrl和文件名
    let lastSlashIndex: number;
    let rootUrl: string;
    let fileName: string;

    switch (type) {
      case ResourceType.MODEL:
        // 解析路径获取rootUrl和文件名
        lastSlashIndex = url.lastIndexOf("/");
        rootUrl = url.substring(0, lastSlashIndex + 1);
        fileName = url.substring(lastSlashIndex + 1);

        this.assetsManager.addMeshTask(url, "", rootUrl, fileName);
        break;

      case ResourceType.TEXTURE:
        this.assetsManager.addTextureTask(url, url);
        break;

      case ResourceType.CUBETEXTURE:
        this.assetsManager.addCubeTextureTask(url, url);
        break;

      case ResourceType.AUDIO:
        // 音频资源将在需要时直接加载
        break;

      case ResourceType.JSON:
        this.assetsManager.addBinaryFileTask(url, url);
        break;

      case ResourceType.IMAGE:
        this.assetsManager.addImageTask(url, url);
        break;
    }
  }

  // 获取资源
  public getResource<T>(url: string): T | null {
    const resource = this.resources.get(url);
    if (resource && resource.loaded) {
      return resource.resource as T;
    }
    return null;
  }

  // 检查资源是否已加载
  public isLoaded(url: string): boolean {
    const resource = this.resources.get(url);
    return resource ? resource.loaded : false;
  }

  // 获取加载进度
  public getProgress(): number {
    if (this.totalResources === 0) return 1;
    return this.loadedResources / this.totalResources;
  }

  // 释放资源
  public releaseResource(url: string): void {
    const resource = this.resources.get(url);
    if (resource && resource.loaded) {
      // 根据资源类型执行不同的释放操作
      switch (resource.type) {
        case ResourceType.TEXTURE:
          (resource.resource as Texture).dispose();
          break;
        case ResourceType.CUBETEXTURE:
          (resource.resource as CubeTexture).dispose();
          break;
        case ResourceType.AUDIO:
          (resource.resource as Sound).dispose();
          break;
        case ResourceType.MODEL:
          // 模型资源由场景管理
          break;
      }

      // 从资源映射中移除
      this.resources.delete(url);
      this.loadedResources--;
    }
  }

  // 释放所有资源
  public releaseAll(): void {
    this.resources.forEach((_resource, url) => {
      this.releaseResource(url);
    });

    this.resources.clear();
    this.totalResources = 0;
    this.loadedResources = 0;

    if (this.assetsManager) {
      this.assetsManager.reset();
    }
  }
}

// 导出单例实例
