// 付费订阅系统类型定义

export type PlanType = 'free' | 'premium' | 'family' | 'enterprise';
export type BillingCycle = 'monthly' | 'yearly';
export type SubscriptionStatus = 'active' | 'canceled' | 'expired' | 'trial' | 'past_due';

// 订阅计划功能限制
export interface PlanLimits {
  memorial_spaces: number; // 纪念空间数量，-1表示无限制
  storage_gb: number; // 存储空间(GB)
  ai_minutes_per_month: number; // AI服务分钟数/月
  custom_domains: number; // 自定义域名数量
  api_calls_per_month: number; // API调用次数/月
  priority_support: boolean; // 优先客服支持
  advanced_analytics: boolean; // 高级数据分析
  white_label: boolean; // 白标服务
  custom_branding: boolean; // 自定义品牌
  backup_retention_days: number; // 备份保留天数
}

// 订阅计划功能特性
export interface PlanFeature {
  id: string;
  name: string;
  description: string;
  category: 'basic' | 'advanced' | 'premium' | 'enterprise';
  is_included: boolean;
  limit?: number; // 功能使用限制
  unit?: string; // 限制单位
}

// 订阅计划
export interface SubscriptionPlan {
  id: string;
  name: string;
  display_name: string;
  description: string;
  plan_type: PlanType;
  
  // 价格信息
  monthly_price: number; // 月价格(分)
  yearly_price: number; // 年价格(分)
  currency: string;
  discount_percentage?: number; // 年付折扣
  
  // 功能和限制
  features: PlanFeature[];
  limits: PlanLimits;
  
  // 元数据
  is_popular: boolean; // 是否为热门计划
  is_available: boolean; // 是否可用
  trial_days?: number; // 免费试用天数
  sort_order: number; // 排序权重
  
  // 创建和更新时间
  created_at: string;
  updated_at: string;
}

// 用户订阅信息
export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  plan: SubscriptionPlan;
  
  // 订阅状态
  status: SubscriptionStatus;
  billing_cycle: BillingCycle;
  
  // 时间相关
  start_date: string;
  end_date: string;
  trial_start?: string;
  trial_end?: string;
  next_billing_date?: string;
  canceled_at?: string;
  
  // 支付信息
  auto_renew: boolean;
  payment_method_id?: string;
  last_payment_date?: string;
  next_payment_amount?: number;
  
  // 使用情况
  current_usage: PlanUsage;
  
  // 元数据
  created_at: string;
  updated_at: string;
}

// 计划使用情况
export interface PlanUsage {
  memorial_spaces_used: number;
  storage_used_gb: number;
  ai_minutes_used: number;
  api_calls_used: number;
  last_reset_date: string; // 上次重置日期
  period_start: string; // 当前计费周期开始
  period_end: string; // 当前计费周期结束
}

// 订阅发票
export interface SubscriptionInvoice {
  id: string;
  subscription_id: string;
  invoice_number: string;
  
  // 金额信息
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  currency: string;
  
  // 计费期间
  period_start: string;
  period_end: string;
  
  // 状态和支付
  status: 'draft' | 'open' | 'paid' | 'uncollectible' | 'void';
  paid_at?: string;
  due_date: string;
  
  // 发票项目
  line_items: InvoiceLineItem[];
  
  // 支付信息
  payment_method?: string;
  payment_reference?: string;
  
  // 创建时间
  created_at: string;
  updated_at: string;
}

// 发票项目
export interface InvoiceLineItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  period_start?: string;
  period_end?: string;
}

// 订阅变更
export interface SubscriptionChange {
  id: string;
  subscription_id: string;
  
  // 变更信息
  change_type: 'upgrade' | 'downgrade' | 'cancel' | 'reactivate' | 'billing_cycle_change';
  from_plan_id?: string;
  to_plan_id?: string;
  from_billing_cycle?: BillingCycle;
  to_billing_cycle?: BillingCycle;
  
  // 时间信息
  effective_date: string;
  requested_at: string;
  processed_at?: string;
  
  // 费用调整
  proration_amount?: number; // 按比例调整金额
  credit_amount?: number; // 退款金额
  
  // 状态
  status: 'pending' | 'processed' | 'failed';
  
  // 备注
  reason?: string;
  notes?: string;
}

// 优惠券
export interface Coupon {
  id: string;
  code: string;
  name: string;
  description?: string;
  
  // 折扣信息
  discount_type: 'percentage' | 'fixed_amount';
  discount_value: number; // 折扣值（百分比或固定金额）
  currency?: string; // 固定金额的货币
  
  // 适用范围
  applicable_plans: string[]; // 适用的计划ID，空数组表示全部
  applicable_billing_cycles: BillingCycle[]; // 适用的计费周期
  
  // 使用限制
  max_redemptions?: number; // 最大使用次数
  max_redemptions_per_user?: number; // 每用户最大使用次数
  current_redemptions: number; // 当前使用次数
  
  // 时间限制
  valid_from: string;
  valid_until?: string;
  
  // 状态
  is_active: boolean;
  
  // 创建时间
  created_at: string;
  updated_at: string;
}

// 优惠券使用记录
export interface CouponRedemption {
  id: string;
  coupon_id: string;
  user_id: string;
  subscription_id?: string;
  invoice_id?: string;
  
  // 折扣信息
  discount_amount: number;
  currency: string;
  
  // 时间
  redeemed_at: string;
  
  // 状态
  status: 'active' | 'expired' | 'refunded';
}

// 订阅统计
export interface SubscriptionAnalytics {
  // 订阅概览
  total_subscriptions: number;
  active_subscriptions: number;
  trial_subscriptions: number;
  canceled_subscriptions: number;
  
  // 收入统计
  monthly_recurring_revenue: number; // MRR
  annual_recurring_revenue: number; // ARR
  average_revenue_per_user: number; // ARPU
  customer_lifetime_value: number; // LTV
  
  // 转化统计
  trial_to_paid_rate: number; // 试用转付费率
  churn_rate: number; // 流失率
  upgrade_rate: number; // 升级率
  downgrade_rate: number; // 降级率
  
  // 计划分布
  plan_distribution: Array<{
    plan_id: string;
    plan_name: string;
    subscriber_count: number;
    revenue_percentage: number;
  }>;
  
  // 收入趋势
  revenue_by_month: Array<{
    month: string;
    mrr: number;
    new_revenue: number;
    expansion_revenue: number;
    contraction_revenue: number;
    churn_revenue: number;
  }>;
  
  // 地理分布
  revenue_by_region: Array<{
    region: string;
    revenue: number;
    subscriber_count: number;
  }>;
}

// 订阅预测
export interface SubscriptionForecast {
  // 预测时间范围
  forecast_period: {
    start_date: string;
    end_date: string;
  };
  
  // 收入预测
  projected_mrr: Array<{
    month: string;
    low_estimate: number;
    mid_estimate: number;
    high_estimate: number;
  }>;
  
  // 用户增长预测
  projected_subscribers: Array<{
    month: string;
    new_subscribers: number;
    total_subscribers: number;
    churn_count: number;
  }>;
  
  // 关键指标预测
  projected_metrics: {
    avg_churn_rate: number;
    avg_upgrade_rate: number;
    avg_ltv: number;
    confidence_level: number; // 预测置信度
  };
}

// 订阅服务接口
export interface SubscriptionService {
  // 订阅计划管理
  getPlans(): Promise<SubscriptionPlan[]>;
  getPlan(planId: string): Promise<SubscriptionPlan>;
  
  // 用户订阅管理
  getUserSubscription(): Promise<UserSubscription | null>;
  createSubscription(planId: string, billingCycle: BillingCycle, couponCode?: string): Promise<UserSubscription>;
  updateSubscription(subscriptionId: string, updates: Partial<UserSubscription>): Promise<UserSubscription>;
  cancelSubscription(subscriptionId: string, cancelAt?: string): Promise<boolean>;
  reactivateSubscription(subscriptionId: string): Promise<UserSubscription>;
  
  // 计划变更
  upgradePlan(newPlanId: string): Promise<SubscriptionChange>;
  downgradePlan(newPlanId: string): Promise<SubscriptionChange>;
  changeBillingCycle(newCycle: BillingCycle): Promise<SubscriptionChange>;
  
  // 使用情况
  getUsage(): Promise<PlanUsage>;
  checkFeatureAccess(featureId: string): Promise<boolean>;
  
  // 发票管理
  getInvoices(): Promise<SubscriptionInvoice[]>;
  getInvoice(invoiceId: string): Promise<SubscriptionInvoice>;
  downloadInvoice(invoiceId: string): Promise<Blob>;
  
  // 优惠券
  validateCoupon(couponCode: string, planId?: string): Promise<Coupon>;
  applyCoupon(couponCode: string): Promise<CouponRedemption>;
  
  // 预览和计算
  previewPlanChange(newPlanId: string, billingCycle?: BillingCycle): Promise<{
    immediate_charge: number;
    next_invoice_amount: number;
    proration_amount: number;
  }>;
  
  // 分析和报告（管理员功能）
  getAnalytics(dateRange?: { start: string; end: string }): Promise<SubscriptionAnalytics>;
  getForecast(months: number): Promise<SubscriptionForecast>;
}

// API响应包装
export interface SubscriptionApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// 订阅事件
export type SubscriptionEventType = 
  | 'subscription.created'
  | 'subscription.updated'
  | 'subscription.canceled'
  | 'subscription.trial_ended'
  | 'subscription.payment_succeeded'
  | 'subscription.payment_failed'
  | 'subscription.invoice_created'
  | 'subscription.plan_changed';

export interface SubscriptionEvent {
  id: string;
  type: SubscriptionEventType;
  subscription_id: string;
  user_id: string;
  data: any;
  created_at: string;
  processed_at?: string;
}

// 订阅通知设置
export interface SubscriptionNotificationSettings {
  email_notifications: {
    payment_succeeded: boolean;
    payment_failed: boolean;
    trial_ending: boolean;
    subscription_canceled: boolean;
    invoice_created: boolean;
  };
  webhook_endpoints: Array<{
    url: string;
    events: SubscriptionEventType[];
    is_active: boolean;
  }>;
}