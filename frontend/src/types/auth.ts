// 认证相关类型定义

export interface User {
  id: string;
  username: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  bio?: string;
  is_active: boolean;
  is_verified: boolean;
  is_superuser: boolean;
  role: string;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
}

export interface LoginCredentials {
  login_identifier: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  full_name?: string;
}

export interface TokenResponse {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in: number;
}

export interface AuthError {
  message: string;
  detail?: string;
  code?: number;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: AuthError | null;
}

export interface AuthContextType extends AuthState {
  // 认证操作
  login: (credentials: LoginCredentials) => Promise<boolean>;
  register: (data: RegisterData) => Promise<boolean>;
  logout: () => void;
  refreshAuth: () => Promise<boolean>;

  // 状态操作
  clearError: () => void;
  setLoading: (loading: boolean) => void;

  // 用户信息更新
  updateUser: (user: Partial<User>) => void;
}
