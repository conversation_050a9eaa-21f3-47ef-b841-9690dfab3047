// 会员权益管理类型定义

export type MembershipTier = 'free' | 'premium' | 'family' | 'enterprise';

export interface MembershipSummary {
  user_id: string;
  current_tier: MembershipTier;
  tier_display_name: string;
  limits: MembershipLimits;
  usage: MembershipUsage;
  usage_percentages: Record<string, number | null>;
  upgrade_recommendations: UpgradeRecommendation[];
  features: {
    memorial_spaces: FeatureAccessCheck;
    storage: FeatureAccessCheck;
    ai_minutes: FeatureAccessCheck;
    priority_support: boolean;
    advanced_analytics: boolean;
  };
}

export interface MembershipLimits {
  memorial_spaces: number;
  storage_gb: number;
  ai_minutes: number;
  custom_domains: number;
  api_calls: number;
  priority_support: boolean;
  advanced_analytics: boolean;
  white_label: boolean;
  custom_branding: boolean;
  backup_retention: number;
}

export interface MembershipUsage {
  memorial_spaces_used: number;
  storage_used_gb: number;
  ai_minutes_used: number;
  api_calls_used: number;
  last_reset_date: string;
  period_start: string;
  period_end: string;
}

export interface FeatureAccessCheck {
  allowed: boolean;
  remaining: number | null;
  limit: number | boolean;
  current_usage: number | null;
  tier: string;
}

export interface UpgradeRecommendation {
  feature: string;
  current_usage: number;
  current_limit: number;
  usage_percentage: number;
  recommended_tier: string;
  reason: string;
}

export interface TierInfo {
  tier: MembershipTier;
  name: string;
  description: string;
  limits: MembershipLimits;
}

export interface OperationValidation {
  allowed: boolean;
  remaining?: number | null;
  limit?: number | boolean | null;
  current_usage?: number | null;
  tier?: string;
  error?: string;
}

export interface UsageAlert {
  feature: string;
  usage_percentage: number;
  level: 'warning' | 'critical';
  message: string;
}

export interface MembershipValidationResult {
  valid: boolean;
  user_tier: string;
  required_tier?: string;
  missing_features?: string[];
  insufficient_usage?: Record<string, any>;
  error_message?: string;
  upgrade_suggestion?: string;
}

// 权益验证相关
export interface MembershipRequirement {
  required_tier?: MembershipTier;
  required_features?: string[];
  feature_usage?: Record<string, number>;
}

// 使用量追踪
export interface FeatureUsageHistory {
  feature_type: string;
  date: string;
  usage_amount: number;
  cumulative_usage: number;
}

export interface UsageHistoryResponse {
  user_id: string;
  period_start: string;
  period_end: string;
  history: FeatureUsageHistory[];
}

// 等级比较
export interface TierComparison {
  feature_name: string;
  free: number | boolean | string;
  premium: number | boolean | string;
  family: number | boolean | string;
  enterprise: number | boolean | string;
}

export interface TierComparisonResponse {
  comparisons: TierComparison[];
  recommendations: Record<string, string>;
}

// 会员权益Hook返回类型
export interface UseMembershipReturn {
  summary: MembershipSummary | null;
  loading: boolean;
  error: string | null;
  checkFeatureAccess: (feature: string, amount?: number) => Promise<FeatureAccessCheck>;
  validateOperation: (operation: string, options?: any) => Promise<OperationValidation>;
  consumeUsage: (feature: string, amount?: number) => Promise<boolean>;
  checkUsageAlerts: () => Promise<UsageAlert[]>;
  refresh: () => Promise<void>;
}

// 会员状态上下文类型
export interface MembershipContextType {
  currentTier: MembershipTier;
  tierDisplayName: string;
  limits: MembershipLimits;
  usage: MembershipUsage;
  usagePercentages: Record<string, number | null>;
  upgradeRecommendations: UpgradeRecommendation[];
  
  // 功能检查方法
  canCreateMemorial: () => boolean;
  canUseAI: (minutes?: number) => boolean;
  canUploadFile: (sizeGB?: number) => boolean;
  hasPrioritySupport: () => boolean;
  hasAdvancedAnalytics: () => boolean;
  
  // 使用量管理
  consumeMemorialSpace: () => Promise<boolean>;
  consumeAIMinutes: (minutes: number) => Promise<boolean>;
  consumeStorage: (sizeGB: number) => Promise<boolean>;
  
  // 状态管理
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

// 功能权限装饰器相关
export interface FeatureGuardProps {
  feature: string;
  amount?: number;
  fallback?: React.ReactNode;
  onDenied?: (reason: string) => void;
  children: React.ReactNode;
}

export interface TierGuardProps {
  requiredTier: MembershipTier;
  fallback?: React.ReactNode;
  onDenied?: (currentTier: MembershipTier, requiredTier: MembershipTier) => void;
  children: React.ReactNode;
}

// 使用量监控组件相关
export interface UsageMonitorProps {
  features?: string[];
  warningThreshold?: number;
  criticalThreshold?: number;
  onAlert?: (alerts: UsageAlert[]) => void;
  showRecommendations?: boolean;
}

export interface UsageProgressProps {
  feature: string;
  current: number;
  limit: number;
  unit?: string;
  showPercentage?: boolean;
  colorScheme?: 'default' | 'success' | 'warning' | 'error';
}

// 升级提示相关
export interface UpgradePromptProps {
  trigger: 'usage_limit' | 'feature_access' | 'manual';
  feature?: string;
  currentTier: MembershipTier;
  recommendedTier: MembershipTier;
  reason: string;
  onUpgrade?: (tier: MembershipTier) => void;
  onDismiss?: () => void;
}

// API响应类型
export interface MembershipApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// 会员权益管理器配置
export interface MembershipManagerConfig {
  autoRefreshInterval?: number; // 自动刷新间隔(毫秒)
  usageAlertThresholds?: {
    warning: number;
    critical: number;
  };
  showUpgradePrompts?: boolean;
  enableUsageTracking?: boolean;
  cacheDuration?: number; // 缓存持续时间(毫秒)
}