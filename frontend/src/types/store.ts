// 虚拟商店系统类型定义

export type ReligiousTag = 'buddhist' | 'christian' | 'taoist' | 'islamic' | 'secular' | 'universal';
export type ItemCategory = 'offering' | 'decoration' | 'scene' | 'effect' | 'bundle';
export type ItemType = 'incense' | 'candle' | 'flower' | 'food' | 'paper_money' | 'ornament' | 'background' | 'audio' | 'particle';

// 商品自定义选项
export interface CustomizationOption {
  id: string;
  name: string;
  type: 'text' | 'image' | 'color' | 'size' | 'material';
  required: boolean;
  price_modifier: number; // 价格调整
  max_length?: number; // 文本最大长度
  allowed_formats?: string[]; // 图片格式
  color_palette?: string[]; // 可选颜色
  size_options?: { name: string; multiplier: number }[];
}

// 商店商品
export interface StoreItem {
  id: string;
  name: string;
  description: string;
  category: ItemCategory;
  type: ItemType;
  price: number; // 基础价格（分）
  currency: 'CNY' | 'USD' | 'EUR';
  
  // 3D模型相关
  model_url: string;
  thumbnail_url: string;
  preview_images: string[];
  model_size: number; // 文件大小(KB)
  polygon_count: number; // 多边形数量
  
  // 功能属性
  is_animated: boolean;
  duration?: number; // 动画时长(秒)
  sound_effect?: string; // 音效URL
  particle_effect?: string; // 粒子效果配置
  
  // 兼容性
  religious_tags: ReligiousTag[];
  customization_options: CustomizationOption[];
  
  // 商业信息
  is_featured: boolean;
  is_new: boolean;
  discount_percentage?: number;
  stock_quantity?: number; // 虚拟库存（限量商品）
  
  // 元数据
  created_at: string;
  updated_at: string;
  creator_id?: string; // 创作者ID（用户生成内容）
  download_count: number;
  rating_average: number;
  rating_count: number;
}

// 商品分类
export interface StoreCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  parent_id?: string; // 支持二级分类
  sort_order: number;
  is_active: boolean;
  item_count: number;
  religious_tags?: ReligiousTag[]; // 分类宗教标签
}

// 购物车项目
export interface CartItem {
  id: string;
  store_item: StoreItem;
  quantity: number;
  customizations: { [option_id: string]: any };
  calculated_price: number; // 包含定制的最终价格
  added_at: string;
}

// 购物车
export interface ShoppingCart {
  id: string;
  user_id: string;
  items: CartItem[];
  total_amount: number;
  currency: string;
  created_at: string;
  updated_at: string;
  expires_at?: string; // 购物车过期时间
  memorial_space_id?: string; // 关联的纪念空间ID
}

// 订单状态
export type OrderStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';

// 订单项目
export interface OrderItem {
  id: string;
  store_item_id: string;
  store_item_snapshot: StoreItem; // 订单时的商品快照
  quantity: number;
  customizations: { [option_id: string]: any };
  unit_price: number;
  total_price: number;
  delivery_status: 'pending' | 'delivered' | 'failed';
  delivered_at?: string;
}

// 订单
export interface Order {
  id: string;
  user_id: string;
  order_number: string; // 订单号
  status: OrderStatus;
  
  // 订单项目
  items: OrderItem[];
  
  // 价格信息
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  currency: string;
  
  // 支付信息
  payment_method: string;
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  payment_reference?: string;
  paid_at?: string;
  
  // 时间戳
  created_at: string;
  updated_at: string;
  completed_at?: string;
  
  // 额外信息
  notes?: string;
  memorial_space_id?: string; // 关联的纪念空间
}

// 虚拟商品交付
export interface VirtualDelivery {
  id: string;
  order_id: string;
  order_item_id: string;
  user_id: string;
  memorial_space_id: string;
  
  // 交付的虚拟商品
  delivered_item: {
    store_item_id: string;
    customizations: { [option_id: string]: any };
    model_url: string;
    expires_at?: string; // 虚拟商品过期时间
  };
  
  status: 'pending' | 'delivered' | 'expired' | 'revoked';
  delivered_at?: string;
  expires_at?: string;
}

// 商店配置
export interface StoreConfig {
  is_enabled: boolean;
  featured_categories: string[];
  promotion_banner?: {
    title: string;
    description: string;
    image_url: string;
    action_url: string;
  };
  payment_methods: string[];
  supported_currencies: string[];
  tax_rate: number; // 税率
  free_delivery_threshold?: number; // 免费配送门槛
}

// 商店统计
export interface StoreAnalytics {
  total_revenue: number;
  total_orders: number;
  conversion_rate: number;
  average_order_value: number;
  top_selling_items: Array<{
    item_id: string;
    item_name: string;
    sales_count: number;
    revenue: number;
  }>;
  revenue_by_category: Array<{
    category_id: string;
    category_name: string;
    revenue: number;
    percentage: number;
  }>;
  sales_by_period: Array<{
    date: string;
    revenue: number;
    orders: number;
  }>;
}

// 用户购买历史
export interface UserPurchaseHistory {
  user_id: string;
  total_spent: number;
  order_count: number;
  favorite_categories: string[];
  recent_orders: Order[];
  purchase_patterns: {
    preferred_religious_tags: ReligiousTag[];
    average_order_value: number;
    purchase_frequency: number; // 天
  };
}

// 商品推荐系统
export interface ItemRecommendation {
  user_id: string;
  recommended_items: Array<{
    item: StoreItem;
    reason: 'popular' | 'similar_purchase' | 'category_preference' | 'religious_match';
    confidence_score: number;
  }>;
  generated_at: string;
}

// 虚拟商店API响应
export interface StoreApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    has_more: boolean;
  };
}

// 商店搜索过滤器
export interface StoreFilters {
  category_ids?: string[];
  religious_tags?: ReligiousTag[];
  price_range?: {
    min: number;
    max: number;
  };
  features?: {
    is_animated?: boolean;
    has_sound?: boolean;
    has_particles?: boolean;
    is_customizable?: boolean;
  };
  sort_by?: 'newest' | 'price_low' | 'price_high' | 'popular' | 'rating';
  search_query?: string;
}

// 商店服务接口
export interface VirtualStoreService {
  // 商品管理
  getItems(filters?: StoreFilters): Promise<StoreApiResponse<StoreItem[]>>;
  getItem(itemId: string): Promise<StoreApiResponse<StoreItem>>;
  searchItems(query: string, filters?: StoreFilters): Promise<StoreApiResponse<StoreItem[]>>;
  
  // 分类管理
  getCategories(): Promise<StoreApiResponse<StoreCategory[]>>;
  getCategoryItems(categoryId: string): Promise<StoreApiResponse<StoreItem[]>>;
  
  // 购物车管理
  getCart(): Promise<StoreApiResponse<ShoppingCart>>;
  addToCart(itemId: string, quantity: number, customizations?: any): Promise<StoreApiResponse<CartItem>>;
  updateCartItem(itemId: string, quantity: number, customizations?: any): Promise<StoreApiResponse<CartItem>>;
  removeFromCart(itemId: string): Promise<StoreApiResponse<boolean>>;
  clearCart(): Promise<StoreApiResponse<boolean>>;
  
  // 订单管理
  createOrder(cart: ShoppingCart): Promise<StoreApiResponse<Order>>;
  getOrders(): Promise<StoreApiResponse<Order[]>>;
  getOrder(orderId: string): Promise<StoreApiResponse<Order>>;
  
  // 推荐系统
  getRecommendations(): Promise<StoreApiResponse<ItemRecommendation>>;
  
  // 用户数据
  getPurchaseHistory(): Promise<StoreApiResponse<UserPurchaseHistory>>;
}