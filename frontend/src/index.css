/* 导入统一样式系统 */
@import "./styles/base-styles.css";
@import "./styles/unified-styles.css";
@import "./styles/tailwind-components.css";

/* Tailwind CSS v4.0 */
@import "tailwindcss";

/* 定义主色系统 - 让Tailwind CSS v4生成对应的utility类 */
@theme {
  --color-primary-50: #f0fdfa;
  --color-primary-100: #ccfbf1;
  --color-primary-200: #99f6e4;
  --color-primary-300: #5eead4;
  --color-primary-400: #2dd4bf;
  --color-primary-500: #14b8a6;
  --color-primary-600: #0d9488;
  --color-primary-700: #0f766e;
  --color-primary-800: #115e59;
  --color-primary-900: #134e4a;
  --color-primary-950: #042f2e;

  /* 确保白色可用 */
  --color-white: #ffffff;
}

/* 手动添加text-white类，因为Tailwind CSS v4可能没有自动生成 */
.text-white {
  color: #ffffff !important;
}

/* 链接样式 */
a {
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  text-decoration: inherit;
  transition: all var(--transition-fast);
}

a:hover {
  color: var(--color-primary-hover);
  opacity: 0.8;
}

/* 标题样式 */
h1 {
  font-size: var(--font-size-5xl);
  line-height: var(--line-height-tight);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

h2 {
  font-size: var(--font-size-4xl);
  line-height: var(--line-height-tight);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

h3 {
  font-size: var(--font-size-3xl);
  line-height: var(--line-height-tight);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

h4 {
  font-size: var(--font-size-2xl);
  line-height: var(--line-height-tight);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

h5 {
  font-size: var(--font-size-xl);
  line-height: var(--line-height-tight);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

h6 {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-tight);
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

/* 段落样式 */
p {
  margin-bottom: var(--spacing-md);
  line-height: var(--line-height-relaxed);
  color: var(--color-text-primary);
}

/* 按钮基础样式覆盖 - 使用统一样式系统 */
button:not(.btn) {
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  font-family: inherit;
  background-color: var(--color-primary);
  color: white;
  cursor: pointer;
  transition: all var(--transition-fast);
}

button:not(.btn):hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

button:not(.btn):focus,
button:not(.btn):focus-visible {
  outline: none;
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

button:not(.btn):disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
