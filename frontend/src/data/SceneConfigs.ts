import { BabylonSceneConfig } from "../utils/BabylonSceneSystem";

// 佛教寺庙场景配置
export const buddhistTempleConfig: BabylonSceneConfig = {
  id: "buddhist-temple",
  name: "佛教寺庙",
  components: [
    {
      id: "main-temple",
      name: "主寺庙",
      type: "model",
      properties: {
        modelPath: "/models/buddhist-temple.glb",
        castShadow: true,
        receiveShadow: true,
      },
      position: [0, 0, 0],
      rotation: [0, 0, 0],
      scaling: [1, 1, 1],
      visible: true,
      interactive: true,
    },
    {
      id: "ambient-light",
      name: "环境光",
      type: "light",
      properties: {
        lightType: "ambient",
        color: "var(--color-gray-800)",
        intensity: 0.5,
      },
    },
    {
      id: "main-light",
      name: "主光源",
      type: "light",
      properties: {
        lightType: "directional",
        color: "var(--color-surface)",
        intensity: 1.0,
        castShadow: true,
      },
      position: [5, 10, 5],
      rotation: [0, 0, 0],
    },
    {
      id: "incense-holder",
      name: "香炉",
      type: "model",
      properties: {
        modelPath: "/models/offerings/incense-holder.glb",
        castShadow: true,
        receiveShadow: true,
      },
      position: [0, 0, 2],
      rotation: [0, 0, 0],
      scaling: [1, 1, 1],
      visible: true,
      interactive: true,
    },
    {
      id: "ambient-sound",
      name: "环境音效",
      type: "audio",
      properties: {
        audioPath: "/audio/buddhist-temple/ambient.mp3",
        loop: true,
        volume: 0.5,
        autoplay: true,
        positional: false,
      },
    },
    {
      id: "bell-sound",
      name: "钟声",
      type: "audio",
      properties: {
        audioPath: "/audio/buddhist-temple/bell.mp3",
        loop: false,
        volume: 0.8,
        autoplay: false,
        positional: true,
        refDistance: 5,
        maxDistance: 100,
      },
      position: [3, 2, -5],
    },
    {
      id: "incense-particles",
      name: "香烟粒子",
      type: "particle_system",
      properties: {
        particleType: "smoke",
        // texturePath: "/textures/particles/smoke.png", // Removed texturePath
        count: 100,
        size: 0.1,
        color: "var(--color-gray-500)aaa",
        opacity: 0.5,
        lifetime: 3,
        speed: 0.2,
      },
      position: [0, 1, 2],
      visible: true,
    },
  ],
  environment: {
    skybox: "/textures/skybox/sunset",
    // sky: { type: "cubemap", path: "/textures/skybox/sunset" }, // Removed sky
    // fog: { type: "linear", color: 0xaaaaaa, near: 10, far: 100 }, // Removed fog
    // ground: { enabled: true, texturePath: "/textures/ground/grass.jpg", size: 100 }, // Removed ground
    ambientLight: {
      color: "var(--color-gray-800)",
      intensity: 0.5,
    },
    directionalLight: {
      color: "var(--color-surface)",
      intensity: 1.0,
      position: [5, 10, 5],
      castShadow: true,
    },
  },
};

// 中国寺庙场景配置
export const chineseTempleConfig: BabylonSceneConfig = {
  id: "chinese-temple",
  name: "中国寺庙",
  components: [
    {
      id: "main-temple",
      name: "主寺庙",
      type: "model",
      properties: {
        modelPath: "/models/chinese-temple.glb",
        castShadow: true,
        receiveShadow: true,
      },
      position: [0, 0, 0],
      rotation: [0, 0, 0],
      scaling: [1, 1, 1],
      visible: true,
      interactive: true,
    },
    {
      id: "ambient-light",
      name: "环境光",
      type: "light",
      properties: {
        lightType: "ambient",
        color: "var(--color-gray-800)",
        intensity: 0.5,
      },
    },
    {
      id: "main-light",
      name: "主光源",
      type: "light",
      properties: {
        lightType: "directional",
        color: "var(--color-surface)",
        intensity: 1.0,
        castShadow: true,
      },
      position: [5, 10, 5],
      rotation: [0, 0, 0],
    },
    {
      id: "incense-holder",
      name: "香炉",
      type: "model",
      properties: {
        modelPath: "/models/offerings/incense-holder.glb",
        castShadow: true,
        receiveShadow: true,
      },
      position: [0, 0, 2],
      rotation: [0, 0, 0],
      scaling: [1, 1, 1],
      visible: true,
      interactive: true,
    },
    {
      id: "ambient-sound",
      name: "环境音效",
      type: "audio",
      properties: {
        audioPath: "/audio/chinese-temple/ambient.mp3",
        loop: true,
        volume: 0.5,
        autoplay: true,
        positional: false,
      },
    },
    {
      id: "drum-sound",
      name: "鼓声",
      type: "audio",
      properties: {
        audioPath: "/audio/chinese-temple/drum.mp3",
        loop: false,
        volume: 0.8,
        autoplay: false,
        positional: true,
        refDistance: 5,
        maxDistance: 100,
      },
      position: [-3, 2, -5],
    },
    {
      id: "lantern-light-1",
      name: "灯笼光源1",
      type: "light",
      properties: {
        lightType: "point",
        color: "var(--color-secondary)",
        intensity: 0.8,
        distance: 10,
        decay: 2,
      },
      position: [3, 2, 3],
    },
    {
      id: "lantern-light-2",
      name: "灯笼光源2",
      type: "light",
      properties: {
        lightType: "point",
        color: "var(--color-secondary)",
        intensity: 0.8,
        distance: 10,
        decay: 2,
      },
      position: [-3, 2, 3],
    },
  ],
  environment: {
    skybox: "/textures/skybox/dusk",
    // sky: { type: "cubemap", path: "/textures/skybox/dusk" }, // Removed sky
    // fog: { type: "linear", color: 0xcccccc, near: 15, far: 120 }, // Removed fog
    // ground: { enabled: true, texturePath: "/textures/ground/stone.jpg", size: 120 }, // Removed ground
    ambientLight: {
      color: "var(--color-gray-800)",
      intensity: 0.5,
    },
    directionalLight: {
      color: "var(--color-surface)",
      intensity: 1.0,
      position: [5, 10, 5],
      castShadow: true,
    },
  },
};

// 道教神社场景配置
export const taoistShrineConfig: BabylonSceneConfig = {
  id: "taoist-shrine",
  name: "道教神社",
  components: [
    {
      id: "main-shrine",
      name: "主神社",
      type: "model",
      properties: {
        modelPath: "/models/taoist-shrine.glb",
        castShadow: true,
        receiveShadow: true,
      },
      position: [0, 0, 0],
      rotation: [0, 0, 0],
      scaling: [1, 1, 1],
      visible: true,
      interactive: true,
    },
    {
      id: "ambient-light",
      name: "环境光",
      type: "light",
      properties: {
        lightType: "ambient",
        color: "var(--color-gray-800)",
        intensity: 0.5,
      },
    },
    {
      id: "main-light",
      name: "主光源",
      type: "light",
      properties: {
        lightType: "directional",
        color: "var(--color-surface)",
        intensity: 1.0,
        castShadow: true,
      },
      position: [5, 10, 5],
      rotation: [0, 0, 0],
    },
    {
      id: "ambient-sound",
      name: "环境音效",
      type: "audio",
      properties: {
        audioPath: "/audio/taoist-shrine/ambient.mp3",
        loop: true,
        volume: 0.5,
        autoplay: true,
        positional: false,
      },
    },
    {
      id: "water-particles",
      name: "水粒子",
      type: "particle_system",
      properties: {
        particleType: "water",
        // texturePath: "/textures/particles/water.png", // Removed texturePath
        count: 200,
        size: 0.05,
        color: "var(--color-primary-light)",
        opacity: 0.7,
        lifetime: 2,
        speed: 0.5,
      },
      position: [2, 0.5, 3],
      visible: true,
    },
  ],
  environment: {
    skybox: "/textures/skybox/day",
    // sky: { type: "cubemap", path: "/textures/skybox/day" }, // Removed sky
    // fog: { type: "exponential", color: 0xd0e0f0, density: 0.01 }, // Removed fog
    // ground: { enabled: true, texturePath: "/textures/ground/forest_floor.jpg", size: 150 }, // Removed ground
    ambientLight: {
      color: "var(--color-gray-600)",
      intensity: 0.6,
    },
    directionalLight: {
      color: "var(--color-surface)",
      intensity: 0.8,
      position: [5, 10, 5],
      castShadow: true,
    },
  },
};

// 导出所有场景配置
export const sceneConfigs = {
  "buddhist-temple": buddhistTempleConfig,
  "chinese-temple": chineseTempleConfig,
  "taoist-shrine": taoistShrineConfig,
};
