import React from "react";
import { Link } from "react-router-dom";
import DeviceDetector from "../utils/DeviceDetector";

/**
 * 演示导航页面
 * 提供各个演示页面的导航链接
 */
const DemoNavigator: React.FC = () => {
  const deviceInfo = DeviceDetector.getDeviceInfo();

  // 根据设备类型设置样式
  const getContainerStyle = () => {
    const baseStyle = {
      maxWidth: "800px",
      margin: "0 auto",
      padding: "20px",
      fontFamily: "sans-serif",
      background: "var(--color-gray-50)",
      minHeight: "100vh",
    };

    if (deviceInfo.isMobile) {
      return {
        ...baseStyle,
        padding: "15px",
      };
    }

    return baseStyle;
  };

  // 演示项目数据
  const demos = [
    // Babylon.js 演示
    {
      id: "babylon-physics",
      title: "Babylon.js物理演示",
      description:
        "使用Babylon.js物理引擎实现的物理交互功能，包括碰撞检测、物理属性和力的应用。",
      path: "/demo/babylon-physics",
      icon: "🎲",
    },
    {
      id: "babylon-performance",
      title: "Babylon.js性能监控演示",
      description:
        "使用Babylon.js实现的性能监控和优化演示，包括FPS监控、绘制调用统计和自适应质量调整。",
      path: "/demo/babylon-performance",
      icon: "📊",
    },
    {
      id: "babylon-mobile",
      title: "Babylon.js移动设备优化演示",
      description:
        "使用Babylon.js实现的移动设备优化演示，包括设备检测、自适应渲染和触摸控制。",
      path: "/demo/babylon-mobile",
      icon: "📱",
    },
    {
      id: "babylon-scene",
      title: "Babylon.js基础场景",
      description:
        "展示Babylon.js的基础场景渲染功能，包括相机控制、光照和阴影。",
      path: "/babylon",
      icon: "🌐",
    },
    {
      id: "model-viewer",
      title: "Babylon.js模型查看器",
      description: "使用Babylon.js加载和查看3D模型，支持GLTF格式。",
      path: "/model-viewer",
      icon: "🗿",
    },
    {
      id: "buddhist-temple",
      title: "佛教寺庙场景",
      description:
        "使用Babylon.js渲染的佛教寺庙场景，包括环境光照、粒子效果和音效。",
      path: "/memorial/buddhist-temple",
      icon: "🏯",
    },
  ];

  return (
    <div style={getContainerStyle()}>
      <h1
        style={{
          textAlign: "center",
          marginBottom: "30px",
          color: "var(--color-primary)",
          fontSize: "32px",
          fontWeight: "bold",
          textShadow: "0 1px 2px rgba(var(--color-gray-900-rgb), 0.1)",
        }}
      >
        怀念与孝道网站开发技术演示导航
      </h1>

      <div style={{ marginBottom: "30px", textAlign: "center" }}>
        <Link
          to="/"
          style={{
            display: "inline-block",
            padding: "12px 24px",
            background: "var(--color-primary)",
            color: "white",
            textDecoration: "none",
            borderRadius: "30px",
            fontWeight: "bold",
            boxShadow: "0 4px 8px rgba(var(--color-gray-900-rgb), 0.1)",
            transition: "all 0.2s ease",
          }}
        >
          返回首页
        </Link>
      </div>

      <div
        style={{
          display: "grid",
          gridTemplateColumns: deviceInfo.isMobile
            ? "1fr"
            : "repeat(auto-fill, minmax(300px, 1fr))",
          gap: "20px",
        }}
      >
        {demos.map((demo) => (
          <div
            key={demo.id}
            style={{
              border: "none",
              borderRadius: "8px",
              overflow: "hidden",
              boxShadow: "0 4px 12px rgba(var(--color-gray-900-rgb), 0.15)",
              transition: "transform 0.2s, box-shadow 0.2s",
              background: "white",
              transform: "translateZ(0)",
              // 注意：React内联样式不支持:hover，需要使用CSS或styled-components
            }}
          >
            <div
              style={{
                background: "var(--color-primary)",
                padding: "15px",
                borderBottom: "1px solid var(--color-gray-300)",
                display: "flex",
                alignItems: "center",
              }}
            >
              <span
                style={{
                  fontSize: "24px",
                  marginRight: "10px",
                  color: "white",
                }}
              >
                {demo.icon}
              </span>
              <h2
                style={{
                  margin: 0,
                  fontSize: "18px",
                  color: "white",
                  fontWeight: "bold",
                }}
              >
                {demo.title}
              </h2>
            </div>
            <div style={{ padding: "15px", background: "white" }}>
              <p
                style={{
                  margin: "0 0 15px 0",
                  color: "var(--color-gray-900)",
                  fontSize: "14px",
                }}
              >
                {demo.description}
              </p>
              <Link
                to={demo.path}
                style={{
                  display: "inline-block",
                  padding: "10px 20px",
                  background: "var(--color-primary)",
                  color: "white",
                  textDecoration: "none",
                  borderRadius: "30px",
                  fontWeight: "bold",
                  boxShadow: "0 2px 6px rgba(var(--color-primary-rgb), 0.3)",
                  transition: "all 0.2s ease",
                }}
              >
                查看演示
              </Link>
            </div>
          </div>
        ))}
      </div>

      <div
        style={{
          marginTop: "40px",
          textAlign: "center",
          color: "var(--color-primary)",
          background: "white",
          padding: "15px",
          borderRadius: "8px",
          boxShadow: "0 2px 8px rgba(var(--color-gray-900-rgb), 0.1)",
        }}
      >
        <p style={{ margin: "5px 0", fontWeight: "bold" }}>
          当前设备信息：{deviceInfo.type} | {deviceInfo.os} |{" "}
          {deviceInfo.browser}
        </p>
        <p style={{ margin: "5px 0", fontWeight: "bold" }}>
          性能级别：{deviceInfo.performanceLevel}
        </p>
      </div>
    </div>
  );
};

export default DemoNavigator;
