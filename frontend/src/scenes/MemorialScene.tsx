import React, { useState, useEffect, useRef } from "react";
import { useParams } from "react-router-dom";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { Scene, Engine } from "@babylonjs/core";
import "./MemorialScene.css";
import LanguageSwitcher from "../components/LanguageSwitcher";
import AdaptiveRenderer from "../components/AdaptiveRenderer";
import SceneOptimizer from "../components/SceneOptimizer";
import OfferingSystem from "../components/OfferingSystem";
import LoadingScreen from "../components/LoadingScreen";
import MobilePerformanceOptimizer from "../components/MobilePerformanceOptimizer";
import MessageSystem from "../components/MessageSystem";
import ScenePreloader from "../utils/ScenePreloader";
import ScenePerformanceManager from "../utils/ScenePerformanceManager";

// 主场景组件
const MemorialScene: React.FC = () => {
  const { t } = useTranslation();
  const { style } = useParams<{ style: string }>();
  const [modelPath, setModelPath] = useState("/models/chinese-temple.glb");
  const [environmentName, setEnvironmentName] = useState("");
  const [showOfferings, setShowOfferings] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [babylonScene, setBabylonScene] = useState<Scene | null>(null);
  const [babylonEngine, setBabylonEngine] = useState<Engine | null>(null);
  const [showPerformancePanel, setShowPerformancePanel] = useState(false);
  const rendererRef = useRef<any>(null);

  const handleOfferingPlaced = (offering: {
    id: string;
    type: string;
    position: { x: number; y: number };
    rotation: number;
    scale: number;
    createdAt: number;
    burning?: boolean;
    smoke?: boolean;
  }) => {
    console.log("Offering placed:", offering);
    // 在这里可以添加将供品信息发送到后端的逻辑
  };
  const [isLoading, setIsLoading] = useState(true);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [sceneReady, setSceneReady] = useState(false);
  const [qualityLevel, setQualityLevel] = useState("medium");
  const [showMobileDebug, setShowMobileDebug] = useState(false);
  const [showMessages, setShowMessages] = useState(false);

  // 根据URL参数加载不同的环境模型
  useEffect(() => {
    // 重置状态
    setIsLoading(true);
    setLoadingProgress(0);
    setSceneReady(false);

    // 确定环境类型和模型路径
    let envType = style || "chinese-temple";
    let envName = "environments.chineseTemple";
    let modelPathValue = "/models/chinese-temple.glb";

    switch (envType) {
      case "chinese-temple":
        modelPathValue = "/models/chinese-temple.glb";
        envName = "environments.chineseTemple";
        break;
      case "buddhist-temple":
        modelPathValue = "/models/buddhist-temple.glb";
        envName = "environments.buddhistTemple";
        break;
      case "taoist-shrine":
        modelPathValue = "/models/taoist-shrine.glb";
        envName = "environments.taoistShrine";
        break;
      case "christian-church":
        modelPathValue = "/models/christian-church.glb";
        envName = "environments.christianChurch";
        break;
      case "jewish-memorial":
        modelPathValue = "/models/jewish-memorial.glb";
        envName = "environments.jewishMemorial";
        break;
      case "modern-space":
        modelPathValue = "/models/modern-space.glb";
        envName = "environments.modernSpace";
        break;
      case "nature":
        modelPathValue = "/models/nature.glb";
        envName = "environments.nature";
        break;
      case "cosmos":
        modelPathValue = "/models/cosmos.glb";
        envName = "environments.cosmos";
        break;
      default:
        envType = "chinese-temple";
        modelPathValue = "/models/chinese-temple.glb";
        envName = "environments.chineseTemple";
    }

    setModelPath(modelPathValue);
    setEnvironmentName(envName);

    // 设置进度回调
    ScenePreloader.setProgressCallback((progress) => {
      setLoadingProgress(progress);
    });

    // 设置完成回调
    ScenePreloader.setCompleteCallback(() => {
      setIsLoading(false);
      setSceneReady(true);
    });

    // 开始预加载资源
    ScenePreloader.preloadEnvironment(envType).catch((error) => {
      console.error("加载场景资源失败:", error);
      // 即使加载失败，也允许用户进入场景
      setIsLoading(false);
      setSceneReady(true);
    });

    // 组件卸载时释放资源
    return () => {
      ScenePreloader.releaseCurrentEnvironment();
    };
  }, [style]);

  return (
    <div className="scene-container">
      {/* 加载屏幕 */}
      {isLoading && (
        <LoadingScreen
          progress={loadingProgress}
          message={t("loading.environment", "正在加载环境资源...")}
        />
      )}

      <LanguageSwitcher />

      <motion.div
        className="scene-header"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: sceneReady ? 1 : 0, y: sceneReady ? 0 : -20 }}
        transition={{ duration: 0.5 }}
      >
        <h1>{t(environmentName)}</h1>
        <motion.button
          className="back-button"
          onClick={() => window.history.back()}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {t("controls.back")}
        </motion.button>
      </motion.div>

      <motion.div
        className="canvas-container"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.7 }}
      >
        <AdaptiveRenderer 
          modelPath={modelPath} 
          ref={rendererRef}
          onSceneReady={(scene, engine) => {
            setBabylonScene(scene);
            setBabylonEngine(engine);
            
            // 初始化性能管理器
            const performanceManager = ScenePerformanceManager.getInstance();
            performanceManager.initialize(scene, engine);
            
            // 设置性能管理回调
            performanceManager.setCallbacks({
              onQualityChanged: (level: string) => {
                setQualityLevel(level);
                console.log("Quality level changed to:", level);
              },
              onPerformanceUpdate: (metrics) => {
                // 可以在这里处理性能更新
                if (metrics.fps < 20) {
                  console.warn("Low FPS detected:", metrics.fps);
                }
              }
            });
          }}
        />
      </motion.div>

      <motion.div
        className="scene-controls"
        initial={{ opacity: 0, y: 20 }}
        animate={{
          opacity: sceneReady && showControls ? 1 : 0,
          y: sceneReady && showControls ? 0 : 20,
        }}
        transition={{ duration: 0.5 }}
      >
        <div className="controls-header">
          <h2>{t("controls.title", "祭祀控制")}</h2>
          <motion.button
            className="toggle-controls-btn"
            onClick={() => setShowControls(!showControls)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {showControls ? "−" : "+"}
          </motion.button>
        </div>

        {showControls && (
          <div className="control-buttons">
            <motion.button
              whileHover={{
                scale: 1.05,
                backgroundColor: "var(--color-primary)",
              }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className={showOfferings ? "active" : ""}
              onClick={() => setShowOfferings(!showOfferings)}
            >
              {t("controls.offerings", "供品")}
            </motion.button>
            <motion.button
              whileHover={{
                scale: 1.05,
                backgroundColor: "var(--color-primary)",
              }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className={showMessages ? "active" : ""}
              onClick={() => setShowMessages(!showMessages)}
            >
              {t("controls.messages", "留言")}
            </motion.button>
            <motion.button
              whileHover={{
                scale: 1.05,
                backgroundColor: "var(--color-primary)",
              }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              {t("controls.music", "音乐")}
            </motion.button>
            <motion.button
              whileHover={{
                scale: 1.05,
                backgroundColor: "var(--color-primary)",
              }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              {t("controls.prayer", "祈祷")}
            </motion.button>
            <motion.button
              whileHover={{
                scale: 1.05,
                backgroundColor: "var(--color-primary)",
              }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
            >
              {t("controls.atmosphere", "氛围")}
            </motion.button>
            <motion.button
              whileHover={{
                scale: 1.05,
                backgroundColor: "var(--color-secondary)",
              }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className={showPerformancePanel ? "active" : ""}
              onClick={() => setShowPerformancePanel(!showPerformancePanel)}
            >
              {t("controls.performance", "性能监控")}
            </motion.button>
            <motion.button
              whileHover={{
                scale: 1.05,
                backgroundColor: "var(--color-accent)",
              }}
              whileTap={{ scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className={showMobileDebug ? "active" : ""}
              onClick={() => setShowMobileDebug(!showMobileDebug)}
            >
              移动优化 ({qualityLevel})
            </motion.button>
          </div>
        )}
      </motion.div>

      {/* 供品系统 */}
      {showOfferings && (
        <OfferingSystem onOfferingPlaced={handleOfferingPlaced} />
      )}

      {/* 性能优化器组件 */}
      <SceneOptimizer
        scene={babylonScene}
        engine={babylonEngine}
        showDebugPanel={showPerformancePanel}
        autoOptimization={true}
        onPerformanceUpdate={(metrics) => {
          console.log("[MemorialScene] Performance metrics:", metrics);
        }}
        onOptimizationSuggestions={(suggestions) => {
          console.log("[MemorialScene] Optimization suggestions:", suggestions);
        }}
      />

      {/* 移动端性能优化器 */}
      <MobilePerformanceOptimizer
        scene={babylonScene}
        engine={babylonEngine}
        showDebugInfo={showMobileDebug}
        onOptimizationChange={(level, reason) => {
          console.log(`[MemorialScene] Mobile optimization: ${level} - ${reason}`);
          setQualityLevel(level);
        }}
      />

      {/* 留言系统 */}
      <MessageSystem
        memorialSpaceId={style || "default-space"}
        showMessageBoard={showMessages}
        onToggleMessages={() => setShowMessages(!showMessages)}
        maxMessages={50}
        allowAnonymous={true}
        enableModeration={true}
        enableLikes={true}
      />
    </div>
  );
};

export default MemorialScene;
