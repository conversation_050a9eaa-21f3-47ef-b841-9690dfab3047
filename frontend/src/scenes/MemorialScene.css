.scene-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.scene-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: rgba(var(--color-gray-900-rgb), 0.8);
  color: white;
}

.back-button {
  padding: 0.5rem 1rem;
  background-color: var(--color-gray-800);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.back-button:hover {
  background-color: var(--color-gray-700);
}

.canvas-container {
  flex: 1;
  width: 100%;
}

.scene-controls {
  padding: 1rem 2rem;
  background-color: rgba(var(--color-gray-900-rgb), 0.8);
  color: white;
  position: relative;
  z-index: var(--z-modal);
}

.controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toggle-controls-btn {
  background-color: transparent;
  color: white;
  border: 1px solid var(--color-primary);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.toggle-controls-btn:hover {
  background-color: rgba(var(--color-primary-rgb), 0.2);
}

.control-buttons {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
}

.control-buttons button {
  padding: 0.5rem 1rem;
  background-color: var(--color-gray-800);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  min-width: 100px;
}

.control-buttons button:hover {
  background-color: var(--color-gray-700);
}

.control-buttons button.active {
  background-color: var(--color-primary);
}
