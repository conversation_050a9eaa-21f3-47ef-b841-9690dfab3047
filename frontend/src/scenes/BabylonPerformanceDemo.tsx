import React, { useEffect, useRef, useState } from "react";
import {
  Engine,
  Scene,
  Vector3,
  HemisphericLight,
  MeshBuilder,
  ArcRotateCamera,
  Color3,
  StandardMaterial,
  Texture,
  CubeTexture,
} from "@babylonjs/core";
import "@babylonjs/loaders";
import BabylonPerformanceMonitor from "../components/BabylonPerformanceMonitor";
import BabylonPhysicsSystem, {
  PhysicsBodyType,
  PhysicsShapeType,
} from "../utils/BabylonPhysicsSystem";
import BabylonAudioManager, { AudioType } from "../utils/BabylonAudioManager";

/**
 * Babylon.js性能演示场景
 * 展示Babylon.js的性能监控和物理系统
 */
const BabylonPerformanceDemo: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const engineRef = useRef<Engine | null>(null);
  const sceneRef = useRef<Scene | null>(null);

  const [isReady, setIsReady] = useState(false);
  const [boxCount, setBoxCount] = useState(10);
  const [showInspector, setShowInspector] = useState(false);
  const [enableOptimizer, setEnableOptimizer] = useState(false);

  // 创建场景
  useEffect(() => {
    if (!canvasRef.current) return;

    // 创建引擎
    const engine = new Engine(canvasRef.current, true, {
      preserveDrawingBuffer: true,
      stencil: true,
    });
    engineRef.current = engine;

    // 创建场景
    const scene = new Scene(engine);
    sceneRef.current = scene;

    // 创建相机
    const camera = new ArcRotateCamera(
      "camera",
      -Math.PI / 2,
      Math.PI / 3,
      20,
      Vector3.Zero(),
      scene,
    );
    camera.attachControl(canvasRef.current, true);
    camera.minZ = 0.1;
    camera.maxZ = 1000;
    camera.wheelPrecision = 50;
    camera.lowerRadiusLimit = 5;
    camera.upperRadiusLimit = 50;

    // 创建光源
    const light = new HemisphericLight("light", new Vector3(0, 1, 0), scene);
    light.intensity = 0.7;

    // 创建天空盒
    const skybox = MeshBuilder.CreateBox("skybox", { size: 1000 }, scene);
    const skyboxMaterial = new StandardMaterial("skyboxMaterial", scene);
    skyboxMaterial.backFaceCulling = false;
    skyboxMaterial.reflectionTexture = new CubeTexture(
      "/textures/skybox/skybox",
      scene,
      ["_px.jpg", "_py.jpg", "_pz.jpg", "_nx.jpg", "_ny.jpg", "_nz.jpg"],
    );
    skyboxMaterial.reflectionTexture.coordinatesMode = Texture.SKYBOX_MODE;
    skyboxMaterial.diffuseColor = new Color3(0, 0, 0);
    skyboxMaterial.specularColor = new Color3(0, 0, 0);
    skybox.material = skyboxMaterial;

    // 创建地面
    const ground = MeshBuilder.CreateGround(
      "ground",
      { width: 50, height: 50 },
      scene,
    );
    const groundMaterial = new StandardMaterial("groundMaterial", scene);
    groundMaterial.diffuseColor = new Color3(0.2, 0.2, 0.2);
    ground.material = groundMaterial;

    // 初始化物理系统
    const physicsSystem = BabylonPhysicsSystem;
    physicsSystem.init(scene);

    // 为地面添加物理特性
    physicsSystem.addBody(
      "ground",
      ground,
      PhysicsBodyType.STATIC,
      PhysicsShapeType.BOX,
      {
        restitution: 0.5,
        friction: 0.1,
      },
    );

    // 初始化音频系统
    const audioManager = BabylonAudioManager.getInstance();
    audioManager.init(scene);

    // 加载音频
    audioManager.loadAudio(
      "background",
      "/audio/background.mp3",
      AudioType.MUSIC,
      {
        loop: true,
        volume: 0.5,
        autoplay: true,
      },
    );

    audioManager.loadAudio(
      "collision",
      "/audio/collision.mp3",
      AudioType.SOUND_EFFECT,
      {
        loop: false,
        volume: 0.8,
      },
    );

    // 碰撞音效
    physicsSystem.on("collision_start", (...args: unknown[]) => {
      const event = args[0] as {
        bodyA?: { type: string };
        bodyB?: { type: string };
      };
      // 检查事件对象是否有预期的结构
      if (
        event &&
        event.bodyA &&
        event.bodyB &&
        event.bodyA.type === PhysicsBodyType.DYNAMIC &&
        event.bodyB.type === PhysicsBodyType.DYNAMIC
      ) {
        audioManager.play("collision");
      }
    });

    // 渲染循环
    engine.runRenderLoop(() => {
      scene.render();
    });

    // 窗口大小调整
    window.addEventListener("resize", () => {
      engine.resize();
    });

    // 场景准备就绪
    setIsReady(true);

    // 清理函数
    return () => {
      engine.stopRenderLoop();
      scene.dispose();
      engine.dispose();
      window.removeEventListener("resize", () => {
        engine.resize();
      });
    };
  }, []);

  // 添加盒子
  const addBoxes = () => {
    if (!sceneRef.current) return;

    const scene = sceneRef.current;

    // 清除现有的盒子
    scene.meshes.forEach((mesh) => {
      if (mesh.name.startsWith("box_")) {
        mesh.dispose();
      }
    });

    // 创建新的盒子
    for (let i = 0; i < boxCount; i++) {
      // TODO: 实现盒子创建逻辑
    }
  };

  // 切换检查器
  const toggleInspector = () => {
    setShowInspector(!showInspector);
  };

  // 切换优化器
  const toggleOptimizer = () => {
    setEnableOptimizer(!enableOptimizer);
  };

  return (
    <div style={{ width: "100%", height: "100vh", overflow: "hidden" }}>
      <canvas ref={canvasRef} style={{ width: "100%", height: "100%" }} />

      {isReady && sceneRef.current && engineRef.current && (
        <BabylonPerformanceMonitor
          scene={sceneRef.current}
          engine={engineRef.current}
          showInspector={showInspector}
          enableOptimizer={enableOptimizer}
          position="top-right"
          showFps={true}
          showDrawCalls={true}
          showTriangles={true}
          showActiveMeshes={true}
          showRenderTime={true}
        />
      )}

      <div
        style={{
          position: "absolute",
          top: "10px",
          left: "10px",
          padding: "10px",
          backgroundColor: "rgba(var(--color-gray-900-rgb), 0.7)",
          color: "white",
          borderRadius: "5px",
        }}
      >
        <div>
          <label>
            盒子数量:
            <input
              type="range"
              min="1"
              max="100"
              value={boxCount}
              onChange={(e) => setBoxCount(parseInt(e.target.value))}
              style={{ marginLeft: "10px" }}
            />
            {boxCount}
          </label>
        </div>

        <div style={{ marginTop: "10px" }}>
          <button onClick={addBoxes}>添加盒子</button>
        </div>

        <div style={{ marginTop: "10px" }}>
          <label>
            <input
              type="checkbox"
              checked={showInspector}
              onChange={toggleInspector}
            />
            显示检查器
          </label>
        </div>

        <div style={{ marginTop: "10px" }}>
          <label>
            <input
              type="checkbox"
              checked={enableOptimizer}
              onChange={toggleOptimizer}
            />
            启用优化器
          </label>
        </div>
      </div>
    </div>
  );
};

export default BabylonPerformanceDemo;
