import React, { useState } from "react";
import { Engine, Scene } from "react-babylonjs";
import {
  Vector3,
  Color3,
  Color4,
  MeshBuilder,
  StandardMaterial,
  Texture,
  ParticleSystem,
  Sound,
  HemisphericLight,
  DirectionalLight,
  ShadowGenerator,
  CubeTexture,
  Scene as BabylonScene,
  SceneLoader,
} from "@babylonjs/core";
import "@babylonjs/loaders";

// 佛教寺庙场景组件
const BuddhistTemple: React.FC = () => {
  const [sceneReady, setSceneReady] = useState<boolean>(false);

  // 处理场景加载完成事件
  const handleSceneReady = (scene: BabylonScene) => {
    // 设置环境光和阴影
    const hemiLight = new HemisphericLight(
      "hemiLight",
      new Vector3(0, 1, 0),
      scene,
    );
    hemiLight.intensity = 0.5;

    const dirLight = new DirectionalLight(
      "dirLight",
      new Vector3(-1, -2, -1),
      scene,
    );
    dirLight.position = new Vector3(20, 40, 20);
    dirLight.intensity = 0.7;

    // 创建阴影生成器
    const shadowGenerator = new ShadowGenerator(1024, dirLight);
    shadowGenerator.useBlurExponentialShadowMap = true;
    shadowGenerator.blurKernel = 32;

    // 加载寺庙模型
    SceneLoader.ImportMesh(
      "",
      "/models/",
      "buddhist-temple.glb",
      scene,
      (meshes) => {
        // 为所有网格添加阴影
        meshes.forEach((mesh) => {
          if (mesh.name !== "ground" && mesh.name !== "sky") {
            shadowGenerator.addShadowCaster(mesh);
            mesh.receiveShadows = true;
          }
        });

        // 创建香火粒子效果
        const incenseStand = meshes.find((m) => m.name === "incenseStand");
        if (incenseStand) {
          const particleSystem = new ParticleSystem("incense", 2000, scene);

          // 粒子纹理
          particleSystem.particleTexture = new Texture(
            "/textures/smoke.png",
            scene,
          );

          // 粒子发射器位置
          particleSystem.emitter = new Vector3(
            incenseStand.position.x,
            incenseStand.position.y + 1,
            incenseStand.position.z,
          );
          particleSystem.minEmitBox = new Vector3(-0.2, 0, -0.2);
          particleSystem.maxEmitBox = new Vector3(0.2, 0.2, 0.2);

          // 粒子颜色
          particleSystem.color1 = new Color4(0.8, 0.8, 0.8, 0.1);
          particleSystem.color2 = new Color4(0.9, 0.9, 0.9, 0.15);
          particleSystem.colorDead = new Color4(0.9, 0.9, 0.9, 0);

          // 粒子大小和生命周期
          particleSystem.minSize = 0.3;
          particleSystem.maxSize = 1.5;
          particleSystem.minLifeTime = 2;
          particleSystem.maxLifeTime = 8;

          // 粒子发射率
          particleSystem.emitRate = 50;

          // 粒子方向和速度
          particleSystem.direction1 = new Vector3(-0.2, 1, -0.2);
          particleSystem.direction2 = new Vector3(0.2, 1, 0.2);
          particleSystem.minEmitPower = 0.5;
          particleSystem.maxEmitPower = 1.5;
          particleSystem.updateSpeed = 0.01;

          // 启动粒子系统
          particleSystem.start();
        }

        // 创建环境音效
        new Sound( // ambientSound variable removed as it's unused
          "ambientSound",
          "/audio/buddhist-temple/ambient.mp3",
          scene,
          null,
          {
            loop: true,
            autoplay: true,
            volume: 0.2,
          },
        );
        setSceneReady(true);
      },
    );

    // 创建天空盒
    const skybox = MeshBuilder.CreateBox("skybox", { size: 1000.0 }, scene);
    const skyboxMaterial = new StandardMaterial("skyboxMaterial", scene);
    skyboxMaterial.backFaceCulling = false;
    skyboxMaterial.reflectionTexture = new CubeTexture(
      "/textures/skybox/skybox",
      scene,
    );
    if (skyboxMaterial.reflectionTexture) {
      skyboxMaterial.reflectionTexture.coordinatesMode = Texture.SKYBOX_MODE;
    }
    skyboxMaterial.diffuseColor = new Color3(0, 0, 0);
    skyboxMaterial.specularColor = new Color3(0, 0, 0);
    skybox.material = skyboxMaterial;
  };

  return (
    <div style={{ width: "100%", height: "100vh" }}>
      <Engine antialias adaptToDeviceRatio canvasId="buddhistTemple">
        <Scene
          clearColor={new Color4(0.2, 0.2, 0.3, 1)}
          onSceneMount={({ scene }) => handleSceneReady(scene)}
        >
          {/* 相机 */}
          <arcRotateCamera
            name="camera"
            target={new Vector3(0, 5, 0)}
            alpha={-Math.PI / 2}
            beta={Math.PI / 4}
            radius={20}
            minZ={0.1}
            wheelPrecision={50}
            lowerRadiusLimit={10}
            upperRadiusLimit={50}
          />
        </Scene>
      </Engine>

      {/* 加载状态指示器 */}
      {!sceneReady && (
        <div
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            color: "white",
            background: "rgba(var(--color-gray-900-rgb), 0.5)",
            padding: "10px 20px",
            borderRadius: "5px",
          }}
        >
          正在加载佛教寺庙场景...
        </div>
      )}
    </div>
  );
};

export default BuddhistTemple;
