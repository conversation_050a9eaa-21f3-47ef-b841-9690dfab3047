import React, { useState, useCallback, useRef, useEffect } from "react";
import { Engine, Scene } from "react-babylonjs";
import {
  Vector3,
  Color4,
  Scene as BabylonScene,
  Engine as BabylonEngine,
} from "@babylonjs/core";
import "@babylonjs/loaders";
import "@babylonjs/materials";

// 导入自定义工具
import BuddhistTempleManager, { TempleConfig, InteractiveElement } from "../utils/BuddhistTempleManager";
import DeviceDetector, { PerformanceLevel } from "../utils/DeviceDetector";
import PerformanceMonitor from "../utils/PerformanceMonitor";

// 设备能力适配器
interface DeviceCapability {
  tier: 'high' | 'medium' | 'low';
  maxTextureSize: number;
  supportsShadows: boolean;
  supportsPostProcessing: boolean;
  maxParticles: number;
}

class DeviceCapabilityDetector {
  static detect(): DeviceCapability {
    const deviceInfo = DeviceDetector.getDeviceInfo();

    let tier: 'high' | 'medium' | 'low';
    switch (deviceInfo.performanceLevel) {
      case PerformanceLevel.HIGH:
        tier = 'high';
        break;
      case PerformanceLevel.MEDIUM:
        tier = 'medium';
        break;
      case PerformanceLevel.LOW:
      default:
        tier = 'low';
        break;
    }

    return {
      tier,
      maxTextureSize: tier === 'high' ? 2048 : tier === 'medium' ? 1024 : 512,
      supportsShadows: tier !== 'low',
      supportsPostProcessing: tier === 'high',
      maxParticles: deviceInfo.performanceLevel === PerformanceLevel.HIGH ? 2000 :
                   deviceInfo.performanceLevel === PerformanceLevel.MEDIUM ? 1000 : 500
    };
  }
}

// 组件属性接口
interface BuddhistTempleProps {
  onSceneReady?: () => void;
  onInteraction?: (element: InteractiveElement) => void;
  enablePerformanceMonitoring?: boolean;
}

// 佛教寺庙场景组件
const BuddhistTemple: React.FC<BuddhistTempleProps> = ({
  onSceneReady,
  onInteraction,
  enablePerformanceMonitoring = true
}) => {
  const [sceneReady, setSceneReady] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [loadingProgress, setLoadingProgress] = useState<number>(0);
  const [interactiveElements, setInteractiveElements] = useState<InteractiveElement[]>([]);
  const [performanceInfo, setPerformanceInfo] = useState<string>("");

  const sceneRef = useRef<BabylonScene | null>(null);
  const engineRef = useRef<BabylonEngine | null>(null);
  const templeManagerRef = useRef<BuddhistTempleManager | null>(null);
  const performanceMonitorRef = useRef<PerformanceMonitor | null>(null);

  // 检测设备性能并生成配置
  const deviceCapability = DeviceCapabilityDetector.detect();
  const templeConfig: TempleConfig = {
    enableShadows: deviceCapability.supportsShadows,
    shadowMapSize: deviceCapability.tier === 'high' ? 2048 : deviceCapability.tier === 'medium' ? 1024 : 512,
    particleDensity: deviceCapability.tier === 'high' ? 1.0 : deviceCapability.tier === 'medium' ? 0.7 : 0.4,
    textureQuality: deviceCapability.tier === 'high' ? 1.0 : deviceCapability.tier === 'medium' ? 0.75 : 0.5,
    enablePostProcessing: deviceCapability.supportsPostProcessing,
    lodLevel: deviceCapability.tier,
    enableAudio: true,
    audioVolume: 0.7
  };

  // 处理寺庙管理器的交互事件
  const handleTempleInteraction = useCallback((element: InteractiveElement) => {
    console.log(`Interacted with ${element.type}: ${element.mesh.name}`);

    // 更新本地状态
    setInteractiveElements(prev =>
      prev.map(el =>
        el.mesh === element.mesh
          ? { ...el, isActive: true }
          : el
      )
    );

    // 触发外部回调
    if (onInteraction) {
      onInteraction(element);
    }
  }, [onInteraction]);

  // 处理场景加载完成事件
  const handleSceneReady = useCallback(async (scene: BabylonScene) => {
    sceneRef.current = scene;

    try {
      // 创建寺庙管理器
      const templeManager = new BuddhistTempleManager(scene, engineRef.current!, templeConfig);
      templeManagerRef.current = templeManager;

      // 设置回调
      templeManager.setCallbacks({
        onLoadProgress: (progress) => {
          setLoadingProgress(progress);
        },
        onLoadComplete: () => {
          setIsLoading(false);
          setSceneReady(true);
          setInteractiveElements(templeManager.getInteractiveElements());

          // 触发外部回调
          if (onSceneReady) {
            onSceneReady();
          }
        },
        onInteraction: handleTempleInteraction,
        onError: (error) => {
          console.error("Temple loading error:", error);
          setIsLoading(false);
        }
      });

      // 初始化寺庙场景
      await templeManager.initialize();

      // 设置性能监控
      if (enablePerformanceMonitoring && engineRef.current) {
        const performanceMonitor = new PerformanceMonitor(scene, engineRef.current, {
          minFPS: deviceCapability.tier === 'high' ? 45 : deviceCapability.tier === 'medium' ? 30 : 20,
          maxFrameTime: deviceCapability.tier === 'high' ? 22 : deviceCapability.tier === 'medium' ? 33 : 50
        });

        performanceMonitor.setOptimizationCallback((suggestions) => {
          console.log("Performance optimization suggestions:", suggestions);
          // 可以根据建议自动调整设置
        });

        performanceMonitor.startMonitoring(2000);
        performanceMonitorRef.current = performanceMonitor;

        // 定期更新性能信息
        const updatePerformanceInfo = () => {
          if (performanceMonitor) {
            setPerformanceInfo(performanceMonitor.getPerformanceReport());
          }
        };

        const performanceInterval = setInterval(updatePerformanceInfo, 3000);

        // 清理函数
        return () => {
          clearInterval(performanceInterval);
        };
      }

    } catch (error) {
      console.error("Failed to initialize temple scene:", error);
      setIsLoading(false);
    }
  }, [templeConfig, deviceCapability, enablePerformanceMonitoring, onSceneReady, handleTempleInteraction]);
  // 组件清理函数
  useEffect(() => {
    return () => {
      // 清理寺庙管理器
      if (templeManagerRef.current) {
        templeManagerRef.current.dispose();
      }

      // 清理性能监控器
      if (performanceMonitorRef.current) {
        performanceMonitorRef.current.dispose();
      }

      // 清理场景资源
      if (sceneRef.current) {
        sceneRef.current.dispose();
      }
    };
  }, []);

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      if (engineRef.current) {
        engineRef.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);



  return (
    <div style={{ width: "100%", height: "100vh", position: "relative" }}>
      <Engine
        antialias
        adaptToDeviceRatio
        canvasId="buddhistTemple"
        engineOptions={{
          preserveDrawingBuffer: true,
          stencil: true,
          disableWebGL2Support: deviceCapability.tier === 'low'
        }}
      >
        <Scene
          clearColor={new Color4(0.15, 0.2, 0.35, 1)}
          onSceneMount={({ scene, engine }) => {
            engineRef.current = engine;
            handleSceneReady(scene);
          }}
        >
          {/* 自适应相机系统 */}
          {deviceCapability.tier === 'low' ? (
            // 移动端使用简化的相机控制
            <universalCamera
              name="camera"
              position={new Vector3(0, 8, 15)}
              setTarget={[new Vector3(0, 5, 0)]}
            />
          ) : (
            // 桌面端使用完整的弧形旋转相机
            <arcRotateCamera
              name="camera"
              target={new Vector3(0, 5, 0)}
              alpha={-Math.PI / 2}
              beta={Math.PI / 4}
              radius={20}
              minZ={0.1}
              maxZ={1000}
              wheelPrecision={50}
              lowerRadiusLimit={8}
              upperRadiusLimit={100}
              lowerBetaLimit={0.1}
              upperBetaLimit={Math.PI / 2}
              panningAxis={new Vector3(1, 1, 0)}
              panningInertia={0.9}
            />
          )}
        </Scene>
      </Engine>

      {/* 增强的加载界面 */}
      {isLoading && (
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            background: "linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            color: "white",
            fontFamily: "Arial, sans-serif",
            zIndex: 1000
          }}
        >
          <div style={{ textAlign: "center", maxWidth: "400px" }}>
            <h2 style={{
              fontSize: "24px",
              marginBottom: "20px",
              color: "#f0d000",
              textShadow: "0 2px 4px rgba(0,0,0,0.5)"
            }}>
              🏛️ 佛教寺庙
            </h2>

            <div style={{
              width: "300px",
              height: "6px",
              background: "rgba(255,255,255,0.2)",
              borderRadius: "3px",
              overflow: "hidden",
              marginBottom: "15px"
            }}>
              <div style={{
                width: `${loadingProgress}%`,
                height: "100%",
                background: "linear-gradient(90deg, #f0d000, #ffed4e)",
                borderRadius: "3px",
                transition: "width 0.3s ease"
              }} />
            </div>

            <p style={{
              fontSize: "16px",
              opacity: 0.9,
              marginBottom: "10px"
            }}>
              正在加载场景... {Math.round(loadingProgress)}%
            </p>

            <p style={{
              fontSize: "12px",
              opacity: 0.7,
              color: "#ccc"
            }}>
              设备性能等级: {deviceCapability.tier.toUpperCase()} |
              质量设置: {templeConfig.lodLevel.toUpperCase()}
            </p>
          </div>
        </div>
      )}

      {/* 交互提示界面 */}
      {sceneReady && (
        <div style={{
          position: "absolute",
          top: "20px",
          left: "20px",
          background: "rgba(0,0,0,0.7)",
          color: "white",
          padding: "15px",
          borderRadius: "8px",
          fontSize: "14px",
          maxWidth: "300px",
          zIndex: 100
        }}>
          <h3 style={{ margin: "0 0 10px 0", color: "#f0d000" }}>🙏 交互指南</h3>
          <ul style={{ margin: 0, paddingLeft: "20px", lineHeight: "1.6" }}>
            <li>点击香炉点燃香火</li>
            <li>点击蜡烛点亮火焰</li>
            <li>点击钟铃敲响梵音</li>
            <li>点击佛像进行祈祷</li>
            <li>拖拽鼠标旋转视角</li>
            <li>滚轮缩放场景</li>
          </ul>

          {interactiveElements.length > 0 && (
            <div style={{ marginTop: "10px", fontSize: "12px", opacity: 0.8 }}>
              发现 {interactiveElements.length} 个可交互对象
            </div>
          )}
        </div>
      )}

      {/* 性能信息（开发模式） */}
      {process.env.NODE_ENV === 'development' && sceneReady && (
        <div style={{
          position: "absolute",
          bottom: "20px",
          right: "20px",
          background: "rgba(0,0,0,0.8)",
          color: "white",
          padding: "10px",
          borderRadius: "5px",
          fontSize: "12px",
          fontFamily: "monospace"
        }}>
          <div>设备: {deviceCapability.tier}</div>
          <div>阴影: {templeConfig.enableShadows ? '开启' : '关闭'}</div>
          <div>粒子密度: {Math.round(templeConfig.particleDensity * 100)}%</div>
          <div>纹理质量: {Math.round(templeConfig.textureQuality * 100)}%</div>
          {enablePerformanceMonitoring && performanceInfo && (
            <div style={{ marginTop: "5px", fontSize: "10px", opacity: 0.8 }}>
              {performanceInfo.split('\n').slice(0, 2).map((line, index) => (
                <div key={index}>{line}</div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BuddhistTemple;
