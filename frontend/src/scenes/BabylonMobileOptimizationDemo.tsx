import { useState, useRef, useEffect } from "react";
import { Engine, Scene, useScene } from "react-babylonjs";
import {
  Vector3,
  Color3,
  Color4,
  ArcRotateCamera,
  HemisphericLight,
  DirectionalLight,
  ShadowGenerator,
  MeshBuilder,
  StandardMaterial,
  SceneOptimizer,
  SceneOptimizerOptions,
  TextureOptimization,
  HardwareScalingOptimization,
  ShadowsOptimization,
  PostProcessesOptimization,
  ParticlesOptimization,
  RenderTargetsOptimization,
} from "@babylonjs/core";
import DeviceDetector, {
  DeviceType,
  PerformanceLevel,
} from "../utils/DeviceDetector";
import AdaptiveLOD from "../components/AdaptiveLOD";

// 设备信息显示组件
const DeviceInfoDisplay = () => {
  const deviceInfo = DeviceDetector.getDeviceInfo();

  return (
    <div
      style={{
        position: "absolute",
        top: "10px",
        right: "10px",
        background: "rgba(var(--color-gray-900-rgb), 0.7)",
        color: "white",
        padding: "10px",
        borderRadius: "5px",
        fontFamily: "monospace",
        fontSize: "12px",
        zIndex: 100,
        maxWidth: "300px",
      }}
    >
      <div>
        <strong>设备类型:</strong> {deviceInfo.type}
      </div>
      <div>
        <strong>操作系统:</strong> {deviceInfo.os}
      </div>
      <div>
        <strong>浏览器:</strong> {deviceInfo.browser}
      </div>
      <div>
        <strong>屏幕尺寸:</strong> {deviceInfo.screenWidth}x
        {deviceInfo.screenHeight}
      </div>
      <div>
        <strong>像素比:</strong> {deviceInfo.pixelRatio.toFixed(2)}
      </div>
      <div>
        <strong>性能级别:</strong> {deviceInfo.performanceLevel}
      </div>
      <div>
        <strong>触摸设备:</strong> {deviceInfo.isTouchDevice ? "是" : "否"}
      </div>
      {deviceInfo.connection && (
        <div>
          <strong>网络类型:</strong> {deviceInfo.connection.type}
        </div>
      )}
      {deviceInfo.gpuInfo && (
        <div>
          <strong>GPU:</strong> {deviceInfo.gpuInfo.renderer.split(" ")[0]}
        </div>
      )}
    </div>
  );
};

// 自适应相机组件
const AdaptiveCamera = () => {
  const scene = useScene();
  const deviceInfo = DeviceDetector.getDeviceInfo();

  useEffect(() => {
    if (!scene) return;

    // 获取相机
    const camera = scene.activeCamera as ArcRotateCamera;
    if (!camera) return;

    // 根据设备类型调整相机参数
    if (deviceInfo.type === DeviceType.MOBILE) {
      camera.fov = (70 * Math.PI) / 180; // 移动设备使用更大的FOV
      camera.wheelPrecision = 20; // 更敏感的缩放
    } else if (deviceInfo.type === DeviceType.TABLET) {
      camera.fov = (60 * Math.PI) / 180;
      camera.wheelPrecision = 30;
    } else {
      camera.fov = (50 * Math.PI) / 180;
      camera.wheelPrecision = 50;
    }

    // 更新相机
    camera.minZ = 0.1;
    camera.maxZ = 1000;
    // ArcRotateCamera不需要显式调用updateProjectionMatrix
    // 设置属性后会自动更新
  }, [scene, deviceInfo.type]);

  return null;
};

// 自适应渲染设置组件
const AdaptiveRenderSettings = () => {
  const scene = useScene();
  const deviceInfo = DeviceDetector.getDeviceInfo();

  useEffect(() => {
    if (!scene || !scene.getEngine()) return;

    const engine = scene.getEngine();

    // 根据设备性能级别设置渲染参数
    switch (deviceInfo.performanceLevel) {
      case PerformanceLevel.LOW:
        engine.setHardwareScalingLevel(2); // 降低分辨率
        scene.shadowsEnabled = false;
        scene.postProcessesEnabled = false;
        scene.particlesEnabled = false;
        break;
      case PerformanceLevel.MEDIUM:
        engine.setHardwareScalingLevel(1.5);
        scene.shadowsEnabled = true;
        scene.postProcessesEnabled = true;
        scene.particlesEnabled = true;
        break;
      case PerformanceLevel.HIGH:
        engine.setHardwareScalingLevel(1);
        scene.shadowsEnabled = true;
        scene.postProcessesEnabled = true;
        scene.particlesEnabled = true;
        break;
    }

    // 设置场景优化器
    const options = new SceneOptimizerOptions(60, 2000);

    if (deviceInfo.performanceLevel === PerformanceLevel.LOW) {
      options.addOptimization(new HardwareScalingOptimization(0, 2));
      options.addOptimization(new ShadowsOptimization(1));
      options.addOptimization(new PostProcessesOptimization(2));
      options.addOptimization(new ParticlesOptimization(3));
      options.addOptimization(new TextureOptimization(4, 256));
      options.addOptimization(new RenderTargetsOptimization(5));
    } else if (deviceInfo.performanceLevel === PerformanceLevel.MEDIUM) {
      options.addOptimization(new HardwareScalingOptimization(0, 1.5));
      options.addOptimization(new ShadowsOptimization(1));
      options.addOptimization(new ParticlesOptimization(2));
      options.addOptimization(new TextureOptimization(3, 512));
    }

    // 创建并启动场景优化器
    const optimizer = new SceneOptimizer(scene, options);
    optimizer.start();

    return () => {
      optimizer.stop();
    };
  }, [scene, deviceInfo.performanceLevel]);

  return null;
};

// 地面组件
const Ground = () => {
  const scene = useScene();
  const deviceInfo = DeviceDetector.getDeviceInfo();

  useEffect(() => {
    if (!scene) return;

    // 创建地面
    const ground = MeshBuilder.CreateGround(
      "ground",
      { width: 100, height: 100 },
      scene,
    );

    // 创建材质
    const groundMaterial = new StandardMaterial("groundMaterial", scene);
    groundMaterial.diffuseColor = new Color3(0.2, 0.2, 0.2);
    groundMaterial.specularColor = new Color3(0.1, 0.1, 0.1);
    ground.material = groundMaterial;

    // 根据设备性能级别设置阴影
    ground.receiveShadows =
      deviceInfo.performanceLevel !== PerformanceLevel.LOW;

    // 设置位置
    ground.position.y = -2;

    return () => {
      ground.dispose();
      groundMaterial.dispose();
    };
  }, [scene, deviceInfo.performanceLevel]);

  return null;
};

// 自适应光照组件
const AdaptiveLighting = () => {
  const scene = useScene();
  const deviceInfo = DeviceDetector.getDeviceInfo();
  const shadowGeneratorRef = useRef<ShadowGenerator | null>(null);

  useEffect(() => {
    if (!scene) return;

    // 创建环境光
    const hemiLight = new HemisphericLight(
      "hemiLight",
      new Vector3(0, 1, 0),
      scene,
    );
    hemiLight.intensity = 0.5;

    // 创建平行光
    const dirLight = new DirectionalLight(
      "dirLight",
      new Vector3(-1, -2, -1),
      scene,
    );
    dirLight.position = new Vector3(20, 40, 20);
    dirLight.intensity = 0.7;

    // 根据设备性能级别设置阴影
    if (deviceInfo.performanceLevel !== PerformanceLevel.LOW) {
      // 获取推荐的阴影贴图大小
      const shadowMapSize = DeviceDetector.getRecommendedShadowMapSize();

      // 创建阴影生成器
      const shadowGenerator = new ShadowGenerator(shadowMapSize, dirLight);
      shadowGenerator.useBlurExponentialShadowMap = true;
      shadowGenerator.blurKernel =
        deviceInfo.performanceLevel === PerformanceLevel.HIGH ? 32 : 16;

      // 保存引用
      shadowGeneratorRef.current = shadowGenerator;
    }

    return () => {
      if (shadowGeneratorRef.current) {
        shadowGeneratorRef.current.dispose();
      }
      hemiLight.dispose();
      dirLight.dispose();
    };
  }, [scene, deviceInfo.performanceLevel]);

  return null;
};

// 移动设备优化演示场景
const BabylonMobileOptimizationDemo = () => {
  const [showInstructions, setShowInstructions] = useState(true);
  const deviceInfo = DeviceDetector.getDeviceInfo();

  // 隐藏说明
  const hideInstructions = () => {
    setShowInstructions(false);
  };

  // 根据设备类型设置样式
  const getInstructionsStyle = () => {
    if (deviceInfo.type === DeviceType.MOBILE) {
      return {
        fontSize: "14px",
        padding: "15px",
      };
    }
    return {
      fontSize: "16px",
      padding: "20px",
    };
  };

  return (
    <div style={{ width: "100%", height: "100vh", position: "relative" }}>
      {showInstructions && (
        <div
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            background: "rgba(var(--color-gray-900-rgb), 0.8)",
            color: "white",
            zIndex: 1000,
            textAlign: "center",
            ...getInstructionsStyle(),
          }}
        >
          <h2
            style={{
              margin: "0 0 10px 0",
              fontSize: deviceInfo.isMobile ? "20px" : "24px",
            }}
          >
            移动设备优化演示 (Babylon.js)
          </h2>
          <p>本演示展示了针对移动设备的优化技术：</p>
          <ul
            style={{
              textAlign: "left",
              maxWidth: deviceInfo.isMobile ? "100%" : "600px",
              margin: "0 auto",
              paddingLeft: deviceInfo.isMobile ? "20px" : "20px",
            }}
          >
            <li>自动检测设备类型和性能级别</li>
            <li>根据设备性能调整渲染质量</li>
            <li>针对触摸设备的控制优化</li>
            <li>自适应UI布局</li>
            <li>电池和网络优化</li>
          </ul>
          <button
            style={{
              padding: "8px 16px",
              background: "var(--color-secondary)",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              marginTop: "10px",
              fontSize: deviceInfo.isMobile ? "14px" : "16px",
            }}
            onClick={hideInstructions}
          >
            开始体验
          </button>
        </div>
      )}

      <DeviceInfoDisplay />

      <Engine antialias adaptToDeviceRatio canvasId="mobileOptimizationDemo">
        <Scene clearColor={new Color4(0.1, 0.1, 0.1, 1)}>
          {/* 自适应相机 */}
          <arcRotateCamera
            name="camera"
            target={Vector3.Zero()}
            alpha={-Math.PI / 2}
            beta={Math.PI / 3}
            radius={15}
          />
          <AdaptiveCamera />

          {/* 自适应渲染设置 */}
          <AdaptiveRenderSettings />

          {/* 地面 */}
          <Ground />

          {/* 模型 */}
          {deviceInfo.performanceLevel === PerformanceLevel.LOW ? (
            <AdaptiveLOD
              modelPath="/models/buddhist-temple-low.glb"
              scale={1}
            />
          ) : (
            <AdaptiveLOD
              modelPath="/models/buddhist-temple.glb"
              levels={[
                { distance: 10, path: "/models/buddhist-temple-medium.glb" },
                { distance: 20, path: "/models/buddhist-temple-low.glb" },
              ]}
            />
          )}

          {/* 自适应光照 */}
          <AdaptiveLighting />
        </Scene>
      </Engine>

      {/* 移动设备特定UI */}
      {deviceInfo.isTouchDevice && !showInstructions && (
        <div
          style={{
            position: "absolute",
            bottom: "80px",
            left: "50%",
            transform: "translateX(-50%)",
            background: "rgba(var(--color-gray-900-rgb), 0.5)",
            color: "white",
            padding: "10px",
            borderRadius: "20px",
            fontSize: "14px",
            textAlign: "center",
          }}
        >
          使用触摸手势控制场景
        </div>
      )}
    </div>
  );
};

export default BabylonMobileOptimizationDemo;
