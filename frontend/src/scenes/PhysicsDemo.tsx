import React, { useState, useRef } from "react"; // Added React, useState, useRef
import {
  Engine,
  Scene as BabylonSceneComponent, // react-babylonjs Scene is a component, not a type
  // MeshBuilder, // Removed from here
} from "react-babylonjs";
import {
  Vector3 as BabylonVector3,
  Color3 as BabylonColor3,
  MeshBuilder, // Added MeshBuilder here
  PhysicsImpostor,
  StandardMaterial,
  AbstractMesh, // Added AbstractMesh
  Scene as BabylonCoreScene, // Added Scene type from @babylonjs/core
} from "@babylonjs/core";
import "@babylonjs/loaders";

/**
 * 物理交互演示组件
 * 展示Babylon.js的物理引擎功能
 */
const PhysicsDemo: React.FC = () => {
  const [sceneReady, setSceneReady] = useState(false);
  const [boxCount, setBoxCount] = useState(0);
  const sceneRef = useRef<BabylonCoreScene | null>(null); // Scene type is now correctly imported from @babylonjs/core

  // 处理场景加载完成事件
  const handleSceneReady = ({ scene }: { scene: BabylonCoreScene }) => {
    // Scene type is now correctly imported from @babylonjs/core
    sceneRef.current = scene;

    // 启用物理引擎
    scene.enablePhysics(new BabylonVector3(0, -9.81, 0));

    // 创建地面
    const ground = MeshBuilder.CreateGround(
      "ground",
      { width: 20, height: 20, subdivisions: 2 },
      scene,
    );

    // 为地面添加物理属性
    ground.physicsImpostor = new PhysicsImpostor(
      ground,
      PhysicsImpostor.BoxImpostor,
      { mass: 0, restitution: 0.9, friction: 0.5 },
      scene,
    );

    // 为地面添加材质
    const groundMaterial = new StandardMaterial("groundMaterial", scene);
    groundMaterial.diffuseColor = new BabylonColor3(0.8, 0.8, 0.8);
    groundMaterial.specularColor = new BabylonColor3(0.1, 0.1, 0.1);
    ground.material = groundMaterial;

    // 创建墙壁
    createWalls(scene);

    setSceneReady(true);
  };

  // 创建墙壁
  const createWalls = (scene: BabylonCoreScene) => {
    // Scene type is now correctly imported from @babylonjs/core
    // 创建四面墙
    const wallMaterial = new StandardMaterial("wallMaterial", scene);
    wallMaterial.diffuseColor = new BabylonColor3(0.5, 0.5, 0.5);
    wallMaterial.alpha = 0.5;

    // 前墙
    const frontWall = MeshBuilder.CreateBox(
      "frontWall",
      { width: 20, height: 10, depth: 0.2 },
      scene,
    );
    frontWall.position = new BabylonVector3(0, 5, -10);
    frontWall.physicsImpostor = new PhysicsImpostor(
      frontWall,
      PhysicsImpostor.BoxImpostor,
      { mass: 0, restitution: 0.9 },
      scene,
    );
    frontWall.material = wallMaterial;

    // 后墙
    const backWall = MeshBuilder.CreateBox(
      "backWall",
      { width: 20, height: 10, depth: 0.2 },
      scene,
    );
    backWall.position = new BabylonVector3(0, 5, 10);
    backWall.physicsImpostor = new PhysicsImpostor(
      backWall,
      PhysicsImpostor.BoxImpostor,
      { mass: 0, restitution: 0.9 },
      scene,
    );
    backWall.material = wallMaterial;

    // 左墙
    const leftWall = MeshBuilder.CreateBox(
      "leftWall",
      { width: 0.2, height: 10, depth: 20 },
      scene,
    );
    leftWall.position = new BabylonVector3(-10, 5, 0);
    leftWall.physicsImpostor = new PhysicsImpostor(
      leftWall,
      PhysicsImpostor.BoxImpostor,
      { mass: 0, restitution: 0.9 },
      scene,
    );
    leftWall.material = wallMaterial;

    // 右墙
    const rightWall = MeshBuilder.CreateBox(
      "rightWall",
      { width: 0.2, height: 10, depth: 20 },
      scene,
    );
    rightWall.position = new BabylonVector3(10, 5, 0);
    rightWall.physicsImpostor = new PhysicsImpostor(
      rightWall,
      PhysicsImpostor.BoxImpostor,
      { mass: 0, restitution: 0.9 },
      scene,
    );
    rightWall.material = wallMaterial;
  };

  // 添加物理盒子
  const addPhysicsBox = () => {
    if (!sceneRef.current) return;

    const scene = sceneRef.current;

    // 创建随机颜色
    const randomColor = new BabylonColor3(
      Math.random(),
      Math.random(),
      Math.random(),
    );

    // 创建材质
    const boxMaterial = new StandardMaterial(`boxMaterial${boxCount}`, scene);
    boxMaterial.diffuseColor = randomColor;

    // 创建盒子
    const box = MeshBuilder.CreateBox(`box${boxCount}`, { size: 1 }, scene);

    // 设置随机位置
    box.position = new BabylonVector3(
      Math.random() * 10 - 5,
      10 + Math.random() * 5,
      Math.random() * 10 - 5,
    );

    // 设置随机旋转
    box.rotation = new BabylonVector3(
      Math.random() * Math.PI,
      Math.random() * Math.PI,
      Math.random() * Math.PI,
    );

    // 应用材质
    box.material = boxMaterial;

    // 添加物理属性
    box.physicsImpostor = new PhysicsImpostor(
      box,
      PhysicsImpostor.BoxImpostor,
      {
        mass: 1,
        restitution: 0.7,
        friction: 0.5,
      },
      scene,
    );

    // 更新盒子计数
    setBoxCount(boxCount + 1);
  };

  // 添加物理球体
  const addPhysicsSphere = () => {
    if (!sceneRef.current) return;

    const scene = sceneRef.current;

    // 创建随机颜色
    const randomColor = new BabylonColor3(
      Math.random(),
      Math.random(),
      Math.random(),
    );

    // 创建材质
    const sphereMaterial = new StandardMaterial(
      `sphereMaterial${boxCount}`,
      scene,
    );
    sphereMaterial.diffuseColor = randomColor;

    // 创建球体
    const sphere = MeshBuilder.CreateSphere(
      `sphere${boxCount}`,
      { diameter: 1, segments: 16 },
      scene,
    );

    // 设置随机位置
    sphere.position = new BabylonVector3(
      Math.random() * 10 - 5,
      10 + Math.random() * 5,
      Math.random() * 10 - 5,
    );

    // 应用材质
    sphere.material = sphereMaterial;

    // 添加物理属性
    sphere.physicsImpostor = new PhysicsImpostor(
      sphere,
      PhysicsImpostor.SphereImpostor,
      {
        mass: 1,
        restitution: 0.9,
        friction: 0.1,
      },
      scene,
    );

    // 更新盒子计数
    setBoxCount(boxCount + 1);
  };

  // 清除所有物理对象
  const clearPhysicsObjects = () => {
    if (!sceneRef.current) return;

    const scene = sceneRef.current;

    // 获取所有网格
    const meshes = scene.meshes.slice();

    // 移除所有盒子和球体
    meshes.forEach((mesh: AbstractMesh) => {
      // AbstractMesh type is now correctly imported
      if (mesh.name.startsWith("box") || mesh.name.startsWith("sphere")) {
        mesh.dispose();
      }
    });

    // 重置计数
    setBoxCount(0);
  };

  return (
    <div style={{ width: "100%", height: "100vh", position: "relative" }}>
      <Engine antialias adaptToDeviceRatio canvasId="physicsDemo">
        {/* 使用react-babylonjs的Scene组件，而不是@babylonjs/core的Scene类型 */}
        <BabylonSceneComponent onSceneMount={handleSceneReady}>
          {/* 相机 */}
          <freeCamera
            name="camera"
            position={new BabylonVector3(0, 10, -15)}
            target={new BabylonVector3(0, 5, 0)}
            minZ={0.1}
          />

          {/* 光源 */}
          <hemisphericLight
            key="light"
            name="light"
            intensity={0.7}
            direction={new BabylonVector3(0, 1, 0)}
          />
        </BabylonSceneComponent>
      </Engine>

      {/* 控制面板 */}
      <div
        style={{
          position: "absolute",
          top: "20px",
          left: "20px",
          background: "rgba(var(--color-gray-900-rgb), 0.7)",
          padding: "15px",
          borderRadius: "5px",
          color: "white",
          display: "flex",
          flexDirection: "column",
          gap: "10px",
        }}
      >
        <h2 style={{ margin: "0 0 10px 0" }}>物理交互演示</h2>
        <button
          onClick={addPhysicsBox}
          style={{
            padding: "8px 15px",
            background: "var(--color-secondary)",
            border: "none",
            borderRadius: "4px",
            color: "white",
            cursor: "pointer",
          }}
        >
          添加盒子
        </button>
        <button
          onClick={addPhysicsSphere}
          style={{
            padding: "8px 15px",
            background: "var(--color-primary)",
            border: "none",
            borderRadius: "4px",
            color: "white",
            cursor: "pointer",
          }}
        >
          添加球体
        </button>
        <button
          onClick={clearPhysicsObjects}
          style={{
            padding: "8px 15px",
            background: "var(--color-primary-dark)",
            border: "none",
            borderRadius: "4px",
            color: "white",
            cursor: "pointer",
          }}
        >
          清除所有
        </button>
        <div style={{ marginTop: "10px" }}>物体数量: {boxCount}</div>
      </div>

      {/* 加载状态指示器 */}
      {!sceneReady && (
        <div
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            color: "white",
            background: "rgba(var(--color-gray-900-rgb), 0.5)",
            padding: "10px 20px",
            borderRadius: "5px",
          }}
        >
          正在加载物理引擎...
        </div>
      )}
    </div>
  );
};

export default PhysicsDemo;
