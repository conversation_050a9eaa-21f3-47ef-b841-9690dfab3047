import { loadStripe, Stripe, StripeElements, StripeCardElement } from '@stripe/stripe-js';

// PayPal SDK 类型定义
declare global {
  interface Window {
    paypal?: any;
  }
}

export interface PaymentMethod {
  id: string;
  type: 'stripe' | 'paypal' | 'alipay' | 'wechat';
  name: string;
  icon: string;
  currencies: string[];
  regions: string[];
  isActive: boolean;
}

export interface PaymentRequest {
  amount: number;
  currency: string;
  description: string;
  orderId: string;
  userId: string;
  productId?: string;
  subscriptionId?: string;
  metadata?: Record<string, any>;
}

export interface PaymentResult {
  success: boolean;
  paymentId?: string;
  transactionId?: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  message?: string;
  errorCode?: string;
  redirectUrl?: string;
}

export interface StripeConfig {
  publicKey: string;
  secretKey: string;
  webhookSecret: string;
  apiVersion: string;
}

export interface PayPalConfig {
  clientId: string;
  clientSecret: string;
  sandbox: boolean;
  webhookId: string;
}

export interface InternationalPaymentConfig {
  stripe: StripeConfig;
  paypal: PayPalConfig;
  supportedCurrencies: string[];
  defaultCurrency: string;
  exchangeRateProvider: string;
}

export class InternationalPaymentService {
  private stripe: Stripe | null = null;
  private stripeElements: StripeElements | null = null;
  private paypalReady = false;
  private config: InternationalPaymentConfig;

  constructor(config: InternationalPaymentConfig) {
    this.config = config;
    this.initializeProviders();
  }

  /// 初始化支付提供商
  private async initializeProviders(): Promise<void> {
    await Promise.all([
      this.initializeStripe(),
      this.initializePayPal(),
    ]);
  }

  /// 初始化Stripe
  private async initializeStripe(): Promise<void> {
    try {
      this.stripe = await loadStripe(this.config.stripe.publicKey);
      if (this.stripe) {
        this.stripeElements = this.stripe.elements();
        console.log('Stripe initialized successfully');
      }
    } catch (error) {
      console.error('Failed to initialize Stripe:', error);
    }
  }

  /// 初始化PayPal
  private async initializePayPal(): Promise<void> {
    try {
      // 动态加载PayPal SDK
      await this.loadPayPalSDK();
      this.paypalReady = true;
      console.log('PayPal initialized successfully');
    } catch (error) {
      console.error('Failed to initialize PayPal:', error);
    }
  }

  /// 加载PayPal SDK
  private loadPayPalSDK(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (window.paypal) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = `https://www.paypal.com/sdk/js?client-id=${this.config.paypal.clientId}&currency=${this.config.defaultCurrency}&intent=capture&components=buttons,funding-eligibility`;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load PayPal SDK'));
      document.head.appendChild(script);
    });
  }

  /// 获取支持的支付方式
  getSupportedPaymentMethods(currency: string, region: string): PaymentMethod[] {
    const methods: PaymentMethod[] = [
      {
        id: 'stripe-card',
        type: 'stripe',
        name: 'Credit/Debit Card',
        icon: '💳',
        currencies: ['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'HKD', 'TWD'],
        regions: ['US', 'EU', 'UK', 'JP', 'CN', 'HK', 'TW'],
        isActive: !!this.stripe,
      },
      {
        id: 'paypal',
        type: 'paypal',
        name: 'PayPal',
        icon: '🅿️',
        currencies: ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD'],
        regions: ['US', 'EU', 'UK', 'JP', 'CA', 'AU'],
        isActive: this.paypalReady,
      },
      {
        id: 'alipay',
        type: 'alipay',
        name: '支付宝',
        icon: '💙',
        currencies: ['CNY', 'USD', 'EUR'],
        regions: ['CN', 'HK', 'TW'],
        isActive: true,
      },
      {
        id: 'wechat',
        type: 'wechat',
        name: '微信支付',
        icon: '💚',
        currencies: ['CNY'],
        regions: ['CN'],
        isActive: true,
      },
    ];

    return methods.filter(method => 
      method.currencies.includes(currency) &&
      (method.regions.includes(region) || method.regions.includes('GLOBAL'))
    );
  }

  /// 创建Stripe支付意图
  async createStripePaymentIntent(request: PaymentRequest): Promise<{
    clientSecret: string;
    paymentIntentId: string;
  }> {
    const response = await fetch('/api/v1/payments/stripe/create-payment-intent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.getAuthToken()}`,
      },
      body: JSON.stringify({
        amount: Math.round(request.amount * 100), // Stripe uses cents
        currency: request.currency.toLowerCase(),
        description: request.description,
        orderId: request.orderId,
        userId: request.userId,
        metadata: {
          ...request.metadata,
          productId: request.productId,
          subscriptionId: request.subscriptionId,
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to create payment intent: ${response.statusText}`);
    }

    const data = await response.json();
    return {
      clientSecret: data.client_secret,
      paymentIntentId: data.id,
    };
  }

  /// 处理Stripe支付
  async processStripePayment(
    request: PaymentRequest,
    cardElement: StripeCardElement,
    billingDetails?: any
  ): Promise<PaymentResult> {
    if (!this.stripe) {
      throw new Error('Stripe not initialized');
    }

    try {
      // 创建支付意图
      const { clientSecret } = await this.createStripePaymentIntent(request);

      // 确认支付
      const { error, paymentIntent } = await this.stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: cardElement,
          billing_details: billingDetails,
        },
      });

      if (error) {
        return {
          success: false,
          status: 'failed',
          message: error.message,
          errorCode: error.code,
        };
      }

      if (paymentIntent?.status === 'succeeded') {
        return {
          success: true,
          paymentId: paymentIntent.id,
          transactionId: paymentIntent.id,
          status: 'completed',
          message: 'Payment successful',
        };
      }

      return {
        success: false,
        status: 'pending',
        message: 'Payment requires additional action',
      };

    } catch (error) {
      console.error('Stripe payment error:', error);
      return {
        success: false,
        status: 'failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /// 创建PayPal订单
  async createPayPalOrder(request: PaymentRequest): Promise<string> {
    const response = await fetch('/api/v1/payments/paypal/create-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.getAuthToken()}`,
      },
      body: JSON.stringify({
        amount: request.amount,
        currency: request.currency,
        description: request.description,
        orderId: request.orderId,
        userId: request.userId,
        productId: request.productId,
        subscriptionId: request.subscriptionId,
        metadata: request.metadata,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to create PayPal order: ${response.statusText}`);
    }

    const data = await response.json();
    return data.orderId;
  }

  /// 处理PayPal支付
  async processPayPalPayment(request: PaymentRequest): Promise<PaymentResult> {
    if (!this.paypalReady || !window.paypal) {
      throw new Error('PayPal not initialized');
    }

    return new Promise((resolve) => {
      const paypalContainer = document.getElementById('paypal-button-container');
      if (!paypalContainer) {
        resolve({
          success: false,
          status: 'failed',
          message: 'PayPal container not found',
        });
        return;
      }

      window.paypal.Buttons({
        style: {
          layout: 'vertical',
          color: 'blue',
          shape: 'rect',
          label: 'paypal',
        },

        createOrder: async () => {
          try {
            return await this.createPayPalOrder(request);
          } catch (error) {
            console.error('PayPal create order error:', error);
            resolve({
              success: false,
              status: 'failed',
              message: error instanceof Error ? error.message : 'Unknown error',
            });
            return '';
          }
        },

        onApprove: async (data: any) => {
          try {
            const response = await fetch('/api/v1/payments/paypal/capture-order', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.getAuthToken()}`,
              },
              body: JSON.stringify({
                orderId: data.orderID,
                payerId: data.payerID,
              }),
            });

            if (!response.ok) {
              throw new Error(`Failed to capture PayPal order: ${response.statusText}`);
            }

            const result = await response.json();
            resolve({
              success: true,
              paymentId: result.paymentId,
              transactionId: result.transactionId,
              status: 'completed',
              message: 'Payment successful',
            });

          } catch (error) {
            console.error('PayPal capture error:', error);
            resolve({
              success: false,
              status: 'failed',
              message: error instanceof Error ? error.message : 'Unknown error',
            });
          }
        },

        onCancel: () => {
          resolve({
            success: false,
            status: 'cancelled',
            message: 'Payment cancelled by user',
          });
        },

        onError: (error: any) => {
          console.error('PayPal error:', error);
          resolve({
            success: false,
            status: 'failed',
            message: 'PayPal payment failed',
          });
        },

      }).render('#paypal-button-container');
    });
  }

  /// 处理支付宝支付
  async processAlipayPayment(request: PaymentRequest): Promise<PaymentResult> {
    try {
      const response = await fetch('/api/v1/payments/alipay/create-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
        body: JSON.stringify({
          amount: request.amount,
          currency: request.currency,
          description: request.description,
          orderId: request.orderId,
          userId: request.userId,
          productId: request.productId,
          subscriptionId: request.subscriptionId,
          metadata: request.metadata,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create Alipay payment: ${response.statusText}`);
      }

      const data = await response.json();
      
      // 重定向到支付宝支付页面
      if (data.redirectUrl) {
        window.location.href = data.redirectUrl;
        return {
          success: true,
          paymentId: data.paymentId,
          status: 'pending',
          redirectUrl: data.redirectUrl,
          message: 'Redirecting to Alipay',
        };
      }

      return {
        success: false,
        status: 'failed',
        message: 'Failed to get Alipay redirect URL',
      };

    } catch (error) {
      console.error('Alipay payment error:', error);
      return {
        success: false,
        status: 'failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /// 处理微信支付
  async processWeChatPayment(request: PaymentRequest): Promise<PaymentResult> {
    try {
      const response = await fetch('/api/v1/payments/wechat/create-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
        body: JSON.stringify({
          amount: request.amount,
          currency: request.currency,
          description: request.description,
          orderId: request.orderId,
          userId: request.userId,
          productId: request.productId,
          subscriptionId: request.subscriptionId,
          metadata: request.metadata,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create WeChat payment: ${response.statusText}`);
      }

      const data = await response.json();

      // 生成二维码或跳转微信支付
      if (data.qrCode) {
        return {
          success: true,
          paymentId: data.paymentId,
          status: 'pending',
          message: 'Please scan QR code to pay',
        };
      }

      return {
        success: false,
        status: 'failed',
        message: 'Failed to generate WeChat payment QR code',
      };

    } catch (error) {
      console.error('WeChat payment error:', error);
      return {
        success: false,
        status: 'failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /// 获取汇率
  async getExchangeRate(from: string, to: string): Promise<number> {
    try {
      const response = await fetch(`/api/v1/payments/exchange-rate?from=${from}&to=${to}`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get exchange rate: ${response.statusText}`);
      }

      const data = await response.json();
      return data.rate;

    } catch (error) {
      console.error('Exchange rate error:', error);
      return 1; // 默认汇率
    }
  }

  /// 转换货币
  async convertCurrency(amount: number, from: string, to: string): Promise<number> {
    if (from === to) {
      return amount;
    }

    const rate = await this.getExchangeRate(from, to);
    return amount * rate;
  }

  /// 查询支付状态
  async getPaymentStatus(paymentId: string, provider: string): Promise<PaymentResult> {
    try {
      const response = await fetch(`/api/v1/payments/${provider}/status/${paymentId}`, {
        headers: {
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to get payment status: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        success: data.status === 'completed',
        paymentId: data.paymentId,
        transactionId: data.transactionId,
        status: data.status,
        message: data.message,
      };

    } catch (error) {
      console.error('Payment status error:', error);
      return {
        success: false,
        status: 'failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /// 退款
  async refundPayment(
    paymentId: string,
    amount: number,
    reason: string,
    provider: string
  ): Promise<PaymentResult> {
    try {
      const response = await fetch(`/api/v1/payments/${provider}/refund`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAuthToken()}`,
        },
        body: JSON.stringify({
          paymentId,
          amount,
          reason,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to process refund: ${response.statusText}`);
      }

      const data = await response.json();
      return {
        success: true,
        paymentId: data.refundId,
        transactionId: data.transactionId,
        status: 'completed',
        message: 'Refund processed successfully',
      };

    } catch (error) {
      console.error('Refund error:', error);
      return {
        success: false,
        status: 'failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /// 获取支持的货币列表
  getSupportedCurrencies(): string[] {
    return this.config.supportedCurrencies;
  }

  /// 格式化货币显示
  formatCurrency(amount: number, currency: string): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }

  /// 检测用户地区
  getUserRegion(): string {
    // 简单的地区检测，实际应用中可以使用更复杂的方法
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    if (timezone.includes('Shanghai') || timezone.includes('Beijing')) return 'CN';
    if (timezone.includes('Hong_Kong')) return 'HK';
    if (timezone.includes('Taipei')) return 'TW';
    if (timezone.includes('Tokyo')) return 'JP';
    if (timezone.includes('London')) return 'UK';
    if (timezone.includes('Europe')) return 'EU';
    return 'US';
  }

  /// 获取推荐的支付方式
  getRecommendedPaymentMethod(currency: string, region: string): PaymentMethod | null {
    const methods = this.getSupportedPaymentMethods(currency, region);
    
    // 根据地区推荐最佳支付方式
    if (region === 'CN') {
      return methods.find(m => m.type === 'alipay') || 
             methods.find(m => m.type === 'wechat') ||
             methods.find(m => m.type === 'stripe') ||
             null;
    }
    
    if (['US', 'EU', 'UK'].includes(region)) {
      return methods.find(m => m.type === 'stripe') ||
             methods.find(m => m.type === 'paypal') ||
             null;
    }

    return methods[0] || null;
  }

  /// 验证支付配置
  validateConfig(): boolean {
    return !!(
      this.config.stripe.publicKey &&
      this.config.paypal.clientId &&
      this.config.supportedCurrencies.length > 0
    );
  }

  /// 获取认证令牌
  private getAuthToken(): string {
    return localStorage.getItem('auth_token') || '';
  }

  /// 创建Stripe卡片元素
  createStripeCardElement(containerId: string): StripeCardElement | null {
    if (!this.stripeElements) {
      return null;
    }

    const cardElement = this.stripeElements.create('card', {
      style: {
        base: {
          fontSize: '16px',
          color: '#424770',
          '::placeholder': {
            color: '#aab7c4',
          },
        },
        invalid: {
          color: '#9e2146',
        },
      },
    });

    const container = document.getElementById(containerId);
    if (container) {
      cardElement.mount(`#${containerId}`);
    }

    return cardElement;
  }

  /// 销毁支付服务
  destroy(): void {
    this.stripe = null;
    this.stripeElements = null;
    this.paypalReady = false;
  }
}

// 创建全局支付服务实例
export const createInternationalPaymentService = (config: InternationalPaymentConfig) => {
  return new InternationalPaymentService(config);
};

export default InternationalPaymentService;