// 订阅服务实现

import {
  SubscriptionPlan,
  UserSubscription,
  PlanUsage,
  SubscriptionInvoice,
  SubscriptionChange,
  Coupon,
  CouponRedemption,
  SubscriptionAnalytics,
  SubscriptionForecast,
  SubscriptionService,
  SubscriptionApiResponse,
  BillingCycle
} from '../types/subscription';

class SubscriptionServiceImpl implements SubscriptionService {
  private baseUrl = '/api/v1/subscription';

  private async apiCall<T>(endpoint: string, options?: RequestInit): Promise<SubscriptionApiResponse<T>> {
    const token = localStorage.getItem('authToken');
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `API调用失败: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // 订阅计划管理
  async getPlans(): Promise<SubscriptionPlan[]> {
    const response = await this.apiCall<SubscriptionPlan[]>('/plans');
    return response.data;
  }

  async getPlan(planId: string): Promise<SubscriptionPlan> {
    const response = await this.apiCall<SubscriptionPlan>(`/plans/${planId}`);
    return response.data;
  }

  // 用户订阅管理
  async getUserSubscription(): Promise<UserSubscription | null> {
    try {
      const response = await this.apiCall<UserSubscription>('/user/subscription');
      return response.data;
    } catch (error) {
      // 如果用户没有订阅，返回null而不是抛出错误
      if (error instanceof Error && error.message.includes('404')) {
        return null;
      }
      throw error;
    }
  }

  async createSubscription(
    planId: string,
    billingCycle: BillingCycle,
    couponCode?: string
  ): Promise<UserSubscription> {
    const response = await this.apiCall<UserSubscription>('/user/subscription', {
      method: 'POST',
      body: JSON.stringify({
        plan_id: planId,
        billing_cycle: billingCycle,
        coupon_code: couponCode
      })
    });
    return response.data;
  }

  async updateSubscription(
    subscriptionId: string,
    updates: Partial<UserSubscription>
  ): Promise<UserSubscription> {
    const response = await this.apiCall<UserSubscription>(`/user/subscription/${subscriptionId}`, {
      method: 'PUT',
      body: JSON.stringify(updates)
    });
    return response.data;
  }

  async cancelSubscription(subscriptionId: string, cancelAt?: string): Promise<boolean> {
    const response = await this.apiCall<boolean>(`/user/subscription/${subscriptionId}/cancel`, {
      method: 'POST',
      body: JSON.stringify({
        cancel_at: cancelAt
      })
    });
    return response.data;
  }

  async reactivateSubscription(subscriptionId: string): Promise<UserSubscription> {
    const response = await this.apiCall<UserSubscription>(`/user/subscription/${subscriptionId}/reactivate`, {
      method: 'POST'
    });
    return response.data;
  }

  // 计划变更
  async upgradePlan(newPlanId: string): Promise<SubscriptionChange> {
    const response = await this.apiCall<SubscriptionChange>('/user/subscription/upgrade', {
      method: 'POST',
      body: JSON.stringify({
        new_plan_id: newPlanId
      })
    });
    return response.data;
  }

  async downgradePlan(newPlanId: string): Promise<SubscriptionChange> {
    const response = await this.apiCall<SubscriptionChange>('/user/subscription/downgrade', {
      method: 'POST',
      body: JSON.stringify({
        new_plan_id: newPlanId
      })
    });
    return response.data;
  }

  async changeBillingCycle(newCycle: BillingCycle): Promise<SubscriptionChange> {
    const response = await this.apiCall<SubscriptionChange>('/user/subscription/billing-cycle', {
      method: 'PUT',
      body: JSON.stringify({
        billing_cycle: newCycle
      })
    });
    return response.data;
  }

  // 使用情况
  async getUsage(): Promise<PlanUsage> {
    const response = await this.apiCall<PlanUsage>('/user/usage');
    return response.data;
  }

  async checkFeatureAccess(featureId: string): Promise<boolean> {
    const response = await this.apiCall<boolean>(`/user/features/${featureId}/access`);
    return response.data;
  }

  async incrementUsage(metric: string, amount: number = 1): Promise<PlanUsage> {
    const response = await this.apiCall<PlanUsage>('/user/usage/increment', {
      method: 'POST',
      body: JSON.stringify({
        metric,
        amount
      })
    });
    return response.data;
  }

  // 发票管理
  async getInvoices(): Promise<SubscriptionInvoice[]> {
    const response = await this.apiCall<SubscriptionInvoice[]>('/user/invoices');
    return response.data;
  }

  async getInvoice(invoiceId: string): Promise<SubscriptionInvoice> {
    const response = await this.apiCall<SubscriptionInvoice>(`/user/invoices/${invoiceId}`);
    return response.data;
  }

  async downloadInvoice(invoiceId: string): Promise<Blob> {
    const token = localStorage.getItem('authToken');
    const response = await fetch(`${this.baseUrl}/user/invoices/${invoiceId}/download`, {
      headers: {
        'Authorization': token ? `Bearer ${token}` : '',
      },
    });

    if (!response.ok) {
      throw new Error(`下载发票失败: ${response.status} ${response.statusText}`);
    }

    return response.blob();
  }

  async payInvoice(invoiceId: string, paymentMethodId: string): Promise<SubscriptionInvoice> {
    const response = await this.apiCall<SubscriptionInvoice>(`/user/invoices/${invoiceId}/pay`, {
      method: 'POST',
      body: JSON.stringify({
        payment_method_id: paymentMethodId
      })
    });
    return response.data;
  }

  // 优惠券
  async validateCoupon(couponCode: string, planId?: string): Promise<Coupon> {
    const params = new URLSearchParams({ code: couponCode });
    if (planId) {
      params.append('plan_id', planId);
    }

    const response = await this.apiCall<Coupon>(`/coupons/validate?${params.toString()}`);
    return response.data;
  }

  async applyCoupon(couponCode: string): Promise<CouponRedemption> {
    const response = await this.apiCall<CouponRedemption>('/user/coupons/apply', {
      method: 'POST',
      body: JSON.stringify({
        coupon_code: couponCode
      })
    });
    return response.data;
  }

  async getUserCoupons(): Promise<CouponRedemption[]> {
    const response = await this.apiCall<CouponRedemption[]>('/user/coupons');
    return response.data;
  }

  // 预览和计算
  async previewPlanChange(newPlanId: string, billingCycle?: BillingCycle): Promise<{
    immediate_charge: number;
    next_invoice_amount: number;
    proration_amount: number;
  }> {
    const response = await this.apiCall<{
      immediate_charge: number;
      next_invoice_amount: number;
      proration_amount: number;
    }>('/preview/plan-change', {
      method: 'POST',
      body: JSON.stringify({
        new_plan_id: newPlanId,
        billing_cycle: billingCycle
      })
    });
    return response.data;
  }

  async calculateProration(
    currentPlanId: string,
    newPlanId: string,
    changeDate?: string
  ): Promise<{
    credit_amount: number;
    charge_amount: number;
    net_amount: number;
  }> {
    const response = await this.apiCall<{
      credit_amount: number;
      charge_amount: number;
      net_amount: number;
    }>('/calculate/proration', {
      method: 'POST',
      body: JSON.stringify({
        current_plan_id: currentPlanId,
        new_plan_id: newPlanId,
        change_date: changeDate
      })
    });
    return response.data;
  }

  // 支付方法管理
  async getPaymentMethods(): Promise<any[]> {
    const response = await this.apiCall<any[]>('/user/payment-methods');
    return response.data;
  }

  async addPaymentMethod(paymentMethodData: any): Promise<any> {
    const response = await this.apiCall<any>('/user/payment-methods', {
      method: 'POST',
      body: JSON.stringify(paymentMethodData)
    });
    return response.data;
  }

  async removePaymentMethod(paymentMethodId: string): Promise<boolean> {
    const response = await this.apiCall<boolean>(`/user/payment-methods/${paymentMethodId}`, {
      method: 'DELETE'
    });
    return response.data;
  }

  async setDefaultPaymentMethod(paymentMethodId: string): Promise<boolean> {
    const response = await this.apiCall<boolean>(`/user/payment-methods/${paymentMethodId}/default`, {
      method: 'PUT'
    });
    return response.data;
  }

  // 订阅历史和变更
  async getSubscriptionHistory(): Promise<SubscriptionChange[]> {
    const response = await this.apiCall<SubscriptionChange[]>('/user/subscription/history');
    return response.data;
  }

  async getSubscriptionChange(changeId: string): Promise<SubscriptionChange> {
    const response = await this.apiCall<SubscriptionChange>(`/user/subscription/changes/${changeId}`);
    return response.data;
  }

  // 试用管理
  async startTrial(planId: string): Promise<UserSubscription> {
    const response = await this.apiCall<UserSubscription>('/user/trial/start', {
      method: 'POST',
      body: JSON.stringify({
        plan_id: planId
      })
    });
    return response.data;
  }

  async extendTrial(days: number): Promise<UserSubscription> {
    const response = await this.apiCall<UserSubscription>('/user/trial/extend', {
      method: 'POST',
      body: JSON.stringify({
        extension_days: days
      })
    });
    return response.data;
  }

  // 通知设置
  async getNotificationSettings(): Promise<any> {
    const response = await this.apiCall<any>('/user/notification-settings');
    return response.data;
  }

  async updateNotificationSettings(settings: any): Promise<any> {
    const response = await this.apiCall<any>('/user/notification-settings', {
      method: 'PUT',
      body: JSON.stringify(settings)
    });
    return response.data;
  }

  // 分析和报告（管理员功能）
  async getAnalytics(dateRange?: { start: string; end: string }): Promise<SubscriptionAnalytics> {
    const params = new URLSearchParams();
    if (dateRange) {
      params.append('start_date', dateRange.start);
      params.append('end_date', dateRange.end);
    }

    const query = params.toString();
    const response = await this.apiCall<SubscriptionAnalytics>(`/admin/analytics${query ? `?${query}` : ''}`);
    return response.data;
  }

  async getForecast(months: number): Promise<SubscriptionForecast> {
    const response = await this.apiCall<SubscriptionForecast>(`/admin/forecast?months=${months}`);
    return response.data;
  }

  async getChurnAnalysis(): Promise<any> {
    const response = await this.apiCall<any>('/admin/churn-analysis');
    return response.data;
  }

  async getRevenueAnalysis(): Promise<any> {
    const response = await this.apiCall<any>('/admin/revenue-analysis');
    return response.data;
  }

  // 批量操作（管理员功能）
  async bulkUpdateSubscriptions(updates: Array<{
    subscription_id: string;
    updates: Partial<UserSubscription>;
  }>): Promise<boolean> {
    const response = await this.apiCall<boolean>('/admin/subscriptions/bulk-update', {
      method: 'PUT',
      body: JSON.stringify({ updates })
    });
    return response.data;
  }

  async exportSubscriptions(filters?: any): Promise<Blob> {
    const token = localStorage.getItem('authToken');
    const response = await fetch(`${this.baseUrl}/admin/subscriptions/export`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
      },
      body: JSON.stringify(filters || {})
    });

    if (!response.ok) {
      throw new Error(`导出失败: ${response.status} ${response.statusText}`);
    }

    return response.blob();
  }

  // 价格计算助手
  async calculateAnnualSavings(planId: string): Promise<number> {
    const plan = await this.getPlan(planId);
    const monthlyTotal = plan.monthly_price * 12;
    const yearlySavings = monthlyTotal - plan.yearly_price;
    return yearlySavings;
  }

  formatPrice(amount: number, currency: string = 'CNY'): string {
    if (amount === 0) return '免费';
    
    const formatter = new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    });
    
    return formatter.format(amount / 100);
  }

  // 本地缓存管理
  private cacheKey = 'subscription_cache';
  private cacheExpiry = 5 * 60 * 1000; // 5分钟

  private setCache(key: string, data: any): void {
    const cacheData = {
      data,
      timestamp: Date.now()
    };
    localStorage.setItem(`${this.cacheKey}_${key}`, JSON.stringify(cacheData));
  }

  private getCache(key: string): any | null {
    try {
      const cached = localStorage.getItem(`${this.cacheKey}_${key}`);
      if (!cached) return null;

      const { data, timestamp } = JSON.parse(cached);
      if (Date.now() - timestamp > this.cacheExpiry) {
        localStorage.removeItem(`${this.cacheKey}_${key}`);
        return null;
      }

      return data;
    } catch {
      return null;
    }
  }

  private clearCache(): void {
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith(this.cacheKey)) {
        localStorage.removeItem(key);
      }
    });
  }

  // 带缓存的计划获取
  async getPlansWithCache(): Promise<SubscriptionPlan[]> {
    const cached = this.getCache('plans');
    if (cached) return cached;

    const plans = await this.getPlans();
    this.setCache('plans', plans);
    return plans;
  }

  // 清除所有缓存
  clearAllCache(): void {
    this.clearCache();
  }
}

// 创建单例实例
export const subscriptionService = new SubscriptionServiceImpl();

// 默认导出服务实例
export default subscriptionService;