// 认证服务API
import {
  LoginCredentials,
  RegisterData,
  TokenResponse,
  User,
} from "../types/auth";
import { httpClient } from "../utils/httpInterceptor";
import secureStorage from "./secureStorageService";

const API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL;

class AuthService {
  constructor() {
    // 执行数据迁移和安全性检查
    this.initializeSecureStorage();
  }

  private initializeSecureStorage(): void {
    // 执行安全性检查
    if (!secureStorage.performSecurityCheck()) {
      console.warn("安全存储初始化失败，将使用降级模式");
    }

    // 从localStorage迁移数据到安全存储
    secureStorage.migrateFromLocalStorage();
  }
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const config: RequestInit = {
      headers: {
        "Content-Type": "application/json",
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ detail: "请求失败" }));
        throw new Error(
          errorData.detail || `HTTP ${response.status}: ${response.statusText}`,
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("网络请求失败");
    }
  }

  private getAuthHeaders(token?: string): Record<string, string> {
    const authToken = token || this.getStoredToken();
    return authToken ? { Authorization: `Bearer ${authToken}` } : {};
  }

  public getStoredToken(): string | null {
    return secureStorage.getAuthToken();
  }

  public getStoredRefreshToken(): string | null {
    return secureStorage.getRefreshToken();
  }

  private setTokens(tokenResponse: TokenResponse): void {
    secureStorage.setAuthToken(tokenResponse.access_token);
    if (tokenResponse.refresh_token) {
      secureStorage.setRefreshToken(tokenResponse.refresh_token);
    }
    // 设置过期时间戳
    const expiresAt = Date.now() + tokenResponse.expires_in * 1000;
    secureStorage.setTokenExpiresAt(expiresAt.toString());
  }

  private clearTokens(): void {
    secureStorage.clearAuthData();
  }

  public isTokenExpired(): boolean {
    const expiresAt = secureStorage.getTokenExpiresAt();
    if (!expiresAt) return true;
    return Date.now() >= parseInt(expiresAt, 10);
  }

  // 登录
  async login(credentials: LoginCredentials): Promise<TokenResponse> {
    const tokenResponse = await this.request<TokenResponse>("/auth/login", {
      method: "POST",
      body: JSON.stringify(credentials),
    });

    this.setTokens(tokenResponse);
    return tokenResponse;
  }

  // 注册
  async register(data: RegisterData): Promise<User> {
    return this.request<User>("/auth/register", {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  // 登出
  async logout(): Promise<void> {
    try {
      await this.request("/auth/logout", {
        method: "POST",
        headers: this.getAuthHeaders(),
      });
    } catch (error) {
      // 即使服务器登出失败，也要清理本地token
      console.warn("服务器登出失败:", error);
    } finally {
      this.clearTokens();
    }
  }

  // 刷新token
  async refreshToken(): Promise<TokenResponse> {
    const refreshToken = this.getStoredRefreshToken();
    if (!refreshToken) {
      throw new Error("没有可用的刷新token");
    }

    const tokenResponse = await this.request<TokenResponse>(
      "/auth/login/refresh-token",
      {
        method: "POST",
        headers: { Authorization: `Bearer ${refreshToken}` },
      },
    );

    this.setTokens(tokenResponse);
    return tokenResponse;
  }

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    const response = await httpClient.get(`${API_BASE_URL}/users/me`);
    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ detail: "获取用户信息失败" }));
      throw new Error(
        errorData.detail || `HTTP ${response.status}: ${response.statusText}`,
      );
    }
    return await response.json();
  }

  // 密码重置
  async forgotPassword(email: string): Promise<{ message: string }> {
    return this.request("/auth/forgot-password", {
      method: "POST",
      body: JSON.stringify({ email }),
    });
  }

  // 重置密码
  async resetPassword(
    token: string,
    newPassword: string,
  ): Promise<{ message: string }> {
    return this.request("/auth/reset-password", {
      method: "POST",
      body: JSON.stringify({ token, new_password: newPassword }),
    });
  }

  // 邮箱验证
  async verifyEmail(token: string): Promise<{ message: string }> {
    return this.request("/auth/verify-email", {
      method: "POST",
      body: JSON.stringify({ token }),
    });
  }

  // 重发验证邮件
  async resendVerification(email: string): Promise<{ message: string }> {
    return this.request("/auth/resend-verification", {
      method: "POST",
      body: JSON.stringify({ email }),
    });
  }

  // 检查是否已登录
  isAuthenticated(): boolean {
    const token = this.getStoredToken();
    return !!(token && !this.isTokenExpired());
  }

  // 获取存储的用户信息
  getStoredUser(): User | null {
    const userStr = secureStorage.getUser();
    if (!userStr) return null;

    try {
      return JSON.parse(userStr);
    } catch {
      return null;
    }
  }

  // 存储用户信息
  setStoredUser(user: User): void {
    secureStorage.setUser(JSON.stringify(user));
  }
}

export const authService = new AuthService();
export default authService;
