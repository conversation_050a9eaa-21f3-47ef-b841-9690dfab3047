// 虚拟商店服务

import {
  StoreItem,
  StoreCategory,
  ShoppingCart,
  CartItem,
  Order,
  StoreFilters,
  StoreApiResponse,
  VirtualStoreService,
  ItemRecommendation,
  UserPurchaseHistory,
  StoreAnalytics
} from '../types/store';

class VirtualStoreServiceImpl implements VirtualStoreService {
  private baseUrl = '/api/v1/store';

  private async apiCall<T>(endpoint: string, options?: RequestInit): Promise<StoreApiResponse<T>> {
    const token = localStorage.getItem('authToken');
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // 商品管理
  async getItems(filters?: StoreFilters): Promise<StoreApiResponse<StoreItem[]>> {
    const queryParams = new URLSearchParams();
    
    if (filters) {
      if (filters.category_ids?.length) {
        queryParams.append('categories', filters.category_ids.join(','));
      }
      if (filters.religious_tags?.length) {
        queryParams.append('religious_tags', filters.religious_tags.join(','));
      }
      if (filters.price_range) {
        queryParams.append('min_price', filters.price_range.min.toString());
        queryParams.append('max_price', filters.price_range.max.toString());
      }
      if (filters.sort_by) {
        queryParams.append('sort', filters.sort_by);
      }
      if (filters.search_query) {
        queryParams.append('q', filters.search_query);
      }
      if (filters.features) {
        Object.entries(filters.features).forEach(([key, value]) => {
          if (value !== undefined) {
            queryParams.append(key, value.toString());
          }
        });
      }
    }

    const query = queryParams.toString();
    return this.apiCall<StoreItem[]>(`/items${query ? `?${query}` : ''}`);
  }

  async getItem(itemId: string): Promise<StoreApiResponse<StoreItem>> {
    return this.apiCall<StoreItem>(`/items/${itemId}`);
  }

  async searchItems(query: string, filters?: StoreFilters): Promise<StoreApiResponse<StoreItem[]>> {
    const searchFilters = { ...filters, search_query: query };
    return this.getItems(searchFilters);
  }

  // 分类管理
  async getCategories(): Promise<StoreApiResponse<StoreCategory[]>> {
    return this.apiCall<StoreCategory[]>('/categories');
  }

  async getCategoryItems(categoryId: string): Promise<StoreApiResponse<StoreItem[]>> {
    return this.apiCall<StoreItem[]>(`/categories/${categoryId}/items`);
  }

  // 购物车管理
  async getCart(): Promise<StoreApiResponse<ShoppingCart>> {
    return this.apiCall<ShoppingCart>('/cart');
  }

  async addToCart(
    itemId: string,
    quantity: number,
    customizations?: any
  ): Promise<StoreApiResponse<CartItem>> {
    return this.apiCall<CartItem>('/cart/items', {
      method: 'POST',
      body: JSON.stringify({
        item_id: itemId,
        quantity,
        customizations: customizations || {}
      })
    });
  }

  async updateCartItem(
    itemId: string,
    quantity: number,
    customizations?: any
  ): Promise<StoreApiResponse<CartItem>> {
    return this.apiCall<CartItem>(`/cart/items/${itemId}`, {
      method: 'PUT',
      body: JSON.stringify({
        quantity,
        customizations: customizations || {}
      })
    });
  }

  async removeFromCart(itemId: string): Promise<StoreApiResponse<boolean>> {
    return this.apiCall<boolean>(`/cart/items/${itemId}`, {
      method: 'DELETE'
    });
  }

  async clearCart(): Promise<StoreApiResponse<boolean>> {
    return this.apiCall<boolean>('/cart', {
      method: 'DELETE'
    });
  }

  // 订单管理
  async createOrder(cart: ShoppingCart): Promise<StoreApiResponse<Order>> {
    return this.apiCall<Order>('/orders', {
      method: 'POST',
      body: JSON.stringify({
        cart_id: cart.id,
        memorial_space_id: cart.memorial_space_id
      })
    });
  }

  async getOrders(): Promise<StoreApiResponse<Order[]>> {
    return this.apiCall<Order[]>('/orders');
  }

  async getOrder(orderId: string): Promise<StoreApiResponse<Order>> {
    return this.apiCall<Order>(`/orders/${orderId}`);
  }

  // 支付相关
  async initiatePayment(orderId: string, paymentMethod: string): Promise<StoreApiResponse<any>> {
    return this.apiCall<any>(`/orders/${orderId}/payment`, {
      method: 'POST',
      body: JSON.stringify({
        payment_method: paymentMethod
      })
    });
  }

  async confirmPayment(orderId: string, paymentReference: string): Promise<StoreApiResponse<Order>> {
    return this.apiCall<Order>(`/orders/${orderId}/payment/confirm`, {
      method: 'POST',
      body: JSON.stringify({
        payment_reference: paymentReference
      })
    });
  }

  // 推荐系统
  async getRecommendations(): Promise<StoreApiResponse<ItemRecommendation>> {
    return this.apiCall<ItemRecommendation>('/recommendations');
  }

  async getPersonalizedItems(memorialSpaceId?: string): Promise<StoreApiResponse<StoreItem[]>> {
    const endpoint = memorialSpaceId 
      ? `/recommendations/memorial/${memorialSpaceId}`
      : '/recommendations/personal';
    return this.apiCall<StoreItem[]>(endpoint);
  }

  // 用户数据
  async getPurchaseHistory(): Promise<StoreApiResponse<UserPurchaseHistory>> {
    return this.apiCall<UserPurchaseHistory>('/user/purchase-history');
  }

  // 商品评价
  async rateItem(itemId: string, rating: number, review?: string): Promise<StoreApiResponse<boolean>> {
    return this.apiCall<boolean>(`/items/${itemId}/rating`, {
      method: 'POST',
      body: JSON.stringify({
        rating,
        review
      })
    });
  }

  async getItemReviews(itemId: string): Promise<StoreApiResponse<any[]>> {
    return this.apiCall<any[]>(`/items/${itemId}/reviews`);
  }

  // 收藏/心愿单
  async addToWishlist(itemId: string): Promise<StoreApiResponse<boolean>> {
    return this.apiCall<boolean>('/wishlist', {
      method: 'POST',
      body: JSON.stringify({ item_id: itemId })
    });
  }

  async removeFromWishlist(itemId: string): Promise<StoreApiResponse<boolean>> {
    return this.apiCall<boolean>(`/wishlist/${itemId}`, {
      method: 'DELETE'
    });
  }

  async getWishlist(): Promise<StoreApiResponse<StoreItem[]>> {
    return this.apiCall<StoreItem[]>('/wishlist');
  }

  // 促销和优惠券
  async getActivePromotions(): Promise<StoreApiResponse<any[]>> {
    return this.apiCall<any[]>('/promotions');
  }

  async applyCoupon(cartId: string, couponCode: string): Promise<StoreApiResponse<ShoppingCart>> {
    return this.apiCall<ShoppingCart>(`/cart/${cartId}/coupon`, {
      method: 'POST',
      body: JSON.stringify({ coupon_code: couponCode })
    });
  }

  // 统计和分析（管理员功能）
  async getStoreAnalytics(dateRange?: { start: string; end: string }): Promise<StoreApiResponse<StoreAnalytics>> {
    const queryParams = new URLSearchParams();
    if (dateRange) {
      queryParams.append('start_date', dateRange.start);
      queryParams.append('end_date', dateRange.end);
    }
    
    const query = queryParams.toString();
    return this.apiCall<StoreAnalytics>(`/analytics${query ? `?${query}` : ''}`);
  }

  // 商品库存管理（管理员功能）
  async updateItemStock(itemId: string, quantity: number): Promise<StoreApiResponse<boolean>> {
    return this.apiCall<boolean>(`/admin/items/${itemId}/stock`, {
      method: 'PUT',
      body: JSON.stringify({ quantity })
    });
  }

  async createItem(item: Partial<StoreItem>): Promise<StoreApiResponse<StoreItem>> {
    return this.apiCall<StoreItem>('/admin/items', {
      method: 'POST',
      body: JSON.stringify(item)
    });
  }

  async updateItem(itemId: string, updates: Partial<StoreItem>): Promise<StoreApiResponse<StoreItem>> {
    return this.apiCall<StoreItem>(`/admin/items/${itemId}`, {
      method: 'PUT',
      body: JSON.stringify(updates)
    });
  }

  async deleteItem(itemId: string): Promise<StoreApiResponse<boolean>> {
    return this.apiCall<boolean>(`/admin/items/${itemId}`, {
      method: 'DELETE'
    });
  }

  // 用户生成内容
  async uploadCustomModel(file: File, metadata: any): Promise<StoreApiResponse<string>> {
    const formData = new FormData();
    formData.append('model', file);
    formData.append('metadata', JSON.stringify(metadata));

    const token = localStorage.getItem('authToken');
    const response = await fetch(`${this.baseUrl}/user-content/upload`, {
      method: 'POST',
      headers: {
        'Authorization': token ? `Bearer ${token}` : '',
      },
      body: formData
    });

    if (!response.ok) {
      throw new Error(`上传失败: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async submitUserGeneratedItem(item: Partial<StoreItem>): Promise<StoreApiResponse<StoreItem>> {
    return this.apiCall<StoreItem>('/user-content/submit', {
      method: 'POST',
      body: JSON.stringify(item)
    });
  }

  // 虚拟商品配送
  async deliverVirtualItems(orderId: string, memorialSpaceId: string): Promise<StoreApiResponse<boolean>> {
    return this.apiCall<boolean>(`/orders/${orderId}/deliver`, {
      method: 'POST',
      body: JSON.stringify({
        memorial_space_id: memorialSpaceId
      })
    });
  }

  async getUserVirtualItems(memorialSpaceId?: string): Promise<StoreApiResponse<any[]>> {
    const endpoint = memorialSpaceId 
      ? `/user/virtual-items?memorial_space_id=${memorialSpaceId}`
      : '/user/virtual-items';
    return this.apiCall<any[]>(endpoint);
  }
}

// 创建单例实例
export const virtualStoreService = new VirtualStoreServiceImpl();

// 默认导出服务实例
export default virtualStoreService;