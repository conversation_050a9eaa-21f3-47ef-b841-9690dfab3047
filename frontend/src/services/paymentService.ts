import httpInterceptor from '../utils/httpInterceptor';

/// Memorial 支付服务 - Web端统一支付接口
/// 支持微信支付、支付宝、国际支付等多种支付方式
export interface PaymentProvider {
  id: string;
  name: string;
  type: 'domestic' | 'international';
  supportedMethods: PaymentMethod[];
  enabled: boolean;
}

export interface PaymentMethod {
  id: string;
  name: string;
  icon: string;
  description: string;
  minimumAmount: number;
  maximumAmount: number;
  currency: string[];
}

export interface PaymentRequest {
  orderId: string;
  amount: number;
  currency: string;
  description: string;
  providerId: string;
  methodId: string;
  returnUrl?: string;
  cancelUrl?: string;
  metadata?: Record<string, any>;
}

export interface PaymentResult {
  success: boolean;
  paymentId?: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  message?: string;
  redirectUrl?: string;
  errorCode?: string;
  transactionId?: string;
}

export interface PaymentHistory {
  id: string;
  orderId: string;
  amount: number;
  currency: string;
  status: string;
  paymentMethod: string;
  createdAt: string;
  completedAt?: string;
  description: string;
  metadata?: Record<string, any>;
}

class PaymentService {
  private readonly baseUrl = '/api/v1/payments';

  /// 获取可用的支付提供商
  async getAvailableProviders(): Promise<PaymentProvider[]> {
    try {
      const response = await httpInterceptor.get(`${this.baseUrl}/providers`);
      const data = await response.json();
      return data.providers || [];
    } catch (error) {
      console.error('获取支付提供商失败:', error);
      throw new Error('无法获取支付方式列表');
    }
  }

  /// 获取特定提供商的支付方式
  async getPaymentMethods(providerId: string): Promise<PaymentMethod[]> {
    try {
      const response = await httpInterceptor.get(
        `${this.baseUrl}/providers/${providerId}/methods`
      );
      const data = await response.json();
      return data.methods || [];
    } catch (error) {
      console.error('获取支付方式失败:', error);
      throw new Error('无法获取支付方式详情');
    }
  }

  /// 创建支付订单
  async createPayment(request: PaymentRequest): Promise<PaymentResult> {
    try {
      const response = await httpInterceptor.post(`${this.baseUrl}/create`, {
        order_id: request.orderId,
        amount: request.amount,
        currency: request.currency,
        description: request.description,
        provider_id: request.providerId,
        method_id: request.methodId,
        return_url: request.returnUrl,
        cancel_url: request.cancelUrl,
        metadata: request.metadata,
      });

      const data = await response.json();
      return {
        success: true,
        paymentId: data.payment_id,
        status: data.status,
        redirectUrl: data.redirect_url,
        transactionId: data.transaction_id,
      };
    } catch (error: any) {
      console.error('创建支付失败:', error);
      return {
        success: false,
        status: 'failed',
        message: error.message || '支付创建失败',
        errorCode: error.code,
      };
    }
  }

  /// 查询支付状态
  async getPaymentStatus(paymentId: string): Promise<PaymentResult> {
    try {
      const response = await httpInterceptor.get(`${this.baseUrl}/${paymentId}/status`);
      const data = await response.json();
      
      return {
        success: true,
        paymentId: data.payment_id,
        status: data.status,
        message: data.message,
        transactionId: data.transaction_id,
      };
    } catch (error: any) {
      console.error('查询支付状态失败:', error);
      return {
        success: false,
        status: 'failed',
        message: error.message || '查询支付状态失败',
      };
    }
  }

  /// 取消支付
  async cancelPayment(paymentId: string): Promise<PaymentResult> {
    try {
      const response = await httpInterceptor.post(`${this.baseUrl}/${paymentId}/cancel`);
      const data = await response.json();
      
      return {
        success: true,
        paymentId: data.payment_id,
        status: 'cancelled',
        message: '支付已取消',
      };
    } catch (error: any) {
      console.error('取消支付失败:', error);
      return {
        success: false,
        status: 'failed',
        message: error.message || '取消支付失败',
      };
    }
  }

  /// 获取支付历史记录
  async getPaymentHistory(
    page: number = 1,
    limit: number = 20,
    filters?: {
      status?: string;
      method?: string;
      startDate?: string;
      endDate?: string;
    }
  ): Promise<{
    payments: PaymentHistory[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...filters,
      });

      const response = await httpInterceptor.get(
        `${this.baseUrl}/history?${params.toString()}`
      );

      const data = await response.json();
      return {
        payments: data.payments || [],
        total: data.total || 0,
        page: data.page || 1,
        limit: data.limit || 20,
      };
    } catch (error) {
      console.error('获取支付历史失败:', error);
      throw new Error('无法获取支付历史记录');
    }
  }

  /// 微信支付特定方法
  async createWeChatPayment(request: PaymentRequest): Promise<PaymentResult> {
    const wechatRequest = {
      ...request,
      providerId: 'wechat',
      methodId: 'wechat_h5', // H5支付方式
    };
    
    return this.createPayment(wechatRequest);
  }

  /// 支付宝特定方法
  async createAlipayPayment(request: PaymentRequest): Promise<PaymentResult> {
    const alipayRequest = {
      ...request,
      providerId: 'alipay',
      methodId: 'alipay_web', // 网页支付方式
    };
    
    return this.createPayment(alipayRequest);
  }

  /// 处理支付回调
  async handlePaymentCallback(
    paymentId: string,
    callbackData: Record<string, any>
  ): Promise<PaymentResult> {
    try {
      const response = await httpInterceptor.post(
        `${this.baseUrl}/${paymentId}/callback`,
        callbackData
      );

      const data = await response.json();
      return {
        success: true,
        paymentId: data.payment_id,
        status: data.status,
        message: data.message,
        transactionId: data.transaction_id,
      };
    } catch (error: any) {
      console.error('处理支付回调失败:', error);
      return {
        success: false,
        status: 'failed',
        message: error.message || '支付回调处理失败',
      };
    }
  }

  /// 验证支付金额
  validatePaymentAmount(amount: number, method: PaymentMethod): boolean {
    return amount >= method.minimumAmount && amount <= method.maximumAmount;
  }

  /// 格式化支付金额显示
  formatPaymentAmount(amount: number, currency: string = 'CNY'): string {
    const formatter = new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    });
    
    return formatter.format(amount);
  }

  /// 获取支付方式图标
  getPaymentMethodIcon(methodId: string): string {
    const icons: Record<string, string> = {
      'wechat_h5': '💚',
      'wechat_app': '💚',
      'alipay_web': '🔵',
      'alipay_app': '🔵',
      'unionpay': '💳',
      'paypal': '🟦',
      'stripe': '🟣',
      'apple_pay': '🍎',
      'google_pay': '🔴',
    };
    
    return icons[methodId] || '💳';
  }

  /// 监听支付状态变化
  async pollPaymentStatus(
    paymentId: string,
    onStatusChange: (status: PaymentResult) => void,
    maxAttempts: number = 30,
    interval: number = 2000
  ): Promise<void> {
    let attempts = 0;
    
    const poll = async () => {
      if (attempts >= maxAttempts) {
        onStatusChange({
          success: false,
          status: 'failed',
          message: '支付状态查询超时',
        });
        return;
      }

      try {
        const result = await this.getPaymentStatus(paymentId);
        onStatusChange(result);

        // 如果支付已完成或失败，停止轮询
        if (result.status === 'completed' || result.status === 'failed' || result.status === 'cancelled') {
          return;
        }

        // 继续轮询
        attempts++;
        setTimeout(poll, interval);
      } catch (error) {
        console.error('轮询支付状态失败:', error);
        onStatusChange({
          success: false,
          status: 'failed',
          message: '支付状态查询出错',
        });
      }
    };

    poll();
  }

  /// 创建退款请求
  async createRefund(
    paymentId: string,
    amount: number,
    reason: string
  ): Promise<{
    success: boolean;
    refundId?: string;
    status?: string;
    amount?: number;
    reason?: string;
    message?: string;
  }> {
    try {
      const response = await httpInterceptor.post(`${this.baseUrl}/${paymentId}/refund`, {
        amount,
        reason,
      });

      const data = await response.json();
      return {
        success: true,
        refundId: data.refund_id,
        status: data.status,
        amount: data.amount,
        reason: data.reason,
      };
    } catch (error: any) {
      console.error('创建退款失败:', error);
      return {
        success: false,
        message: error.message || '退款创建失败',
      };
    }
  }
}

export const paymentService = new PaymentService();
export default paymentService;