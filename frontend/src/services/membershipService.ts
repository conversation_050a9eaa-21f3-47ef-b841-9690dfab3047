import { API_BASE_URL } from '../config';
import { 
  MembershipSummary as MembershipSummaryType,
  FeatureAccessCheck as FeatureAccessCheckType,
  UpgradeRecommendation as UpgradeRecommendationType,
  TierInfo as TierInfoType
} from '../types/membership';

// Re-export types for backward compatibility
export type MembershipSummary = MembershipSummaryType;
export type FeatureAccessCheck = FeatureAccessCheckType;
export type UpgradeRecommendation = UpgradeRecommendationType;
export type TierInfo = TierInfoType;

export interface OperationValidation {
  allowed: boolean;
  remaining?: number | null;
  limit?: number | boolean | null;
  current_usage?: number | null;
  tier?: string;
  error?: string;
}

export interface UsageUpdateRequest {
  feature_type: string;
  amount: number;
}

class MembershipService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/membership`;
  }

  private async fetchWithAuth(url: string, options: RequestInit = {}): Promise<Response> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response;
  }

  /**
   * 获取会员权益概览
   */
  async getMembershipSummary(): Promise<MembershipSummary> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/summary`);
    return response.json();
  }

  /**
   * 检查特定功能的访问权限
   */
  async checkFeatureAccess(featureType: string, amount: number = 1): Promise<FeatureAccessCheck> {
    const response = await this.fetchWithAuth(
      `${this.baseUrl}/check-feature/${featureType}?amount=${amount}`
    );
    return response.json();
  }

  /**
   * 验证操作权限
   */
  async validateOperation(
    operationType: string,
    options: { fileSizeGb?: number; minutes?: number } = {}
  ): Promise<OperationValidation> {
    const params = new URLSearchParams({ operation_type: operationType });
    
    if (options.fileSizeGb !== undefined) {
      params.append('file_size_gb', options.fileSizeGb.toString());
    }
    if (options.minutes !== undefined) {
      params.append('minutes', options.minutes.toString());
    }

    const response = await this.fetchWithAuth(
      `${this.baseUrl}/validate-operation?${params.toString()}`,
      { method: 'POST' }
    );
    return response.json();
  }

  /**
   * 消费功能使用量
   */
  async consumeFeatureUsage(featureType: string, amount: number = 1): Promise<{ success: boolean; message: string }> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/consume-usage`, {
      method: 'POST',
      body: JSON.stringify({ feature_type: featureType, amount }),
    });
    return response.json();
  }

  /**
   * 获取当前使用情况
   */
  async getCurrentUsage(): Promise<{ usage: Record<string, any> }> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/usage`);
    return response.json();
  }

  /**
   * 获取升级建议
   */
  async getUpgradeRecommendations(): Promise<{ recommendations: UpgradeRecommendation[] }> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/upgrade-recommendations`);
    return response.json();
  }

  /**
   * 获取所有会员等级信息
   */
  async getAllTiers(): Promise<TierInfo[]> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/tiers`);
    const data = await response.json();
    return data.tiers;
  }

  /**
   * 检查是否可以创建纪念空间
   */
  async canCreateMemorial(): Promise<FeatureAccessCheck> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/can-create-memorial`);
    return response.json();
  }

  /**
   * 检查是否可以使用AI服务
   */
  async canUseAI(minutes: number = 1): Promise<FeatureAccessCheck> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/can-use-ai?minutes=${minutes}`);
    return response.json();
  }

  /**
   * 检查是否有优先支持
   */
  async hasPrioritySupport(): Promise<{ has_priority_support: boolean }> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/has-priority-support`);
    return response.json();
  }

  /**
   * 检查是否有高级分析功能
   */
  async hasAdvancedAnalytics(): Promise<{ has_advanced_analytics: boolean }> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/has-advanced-analytics`);
    return response.json();
  }

  // 便捷方法：操作前检查权限
  async validateAndExecute<T>(
    operationType: string,
    operation: () => Promise<T>,
    options: { fileSizeGb?: number; minutes?: number } = {}
  ): Promise<T> {
    const validation = await this.validateOperation(operationType, options);
    
    if (!validation.allowed) {
      throw new Error(validation.error || '权限不足，无法执行此操作');
    }

    // 执行操作
    const result = await operation();

    // 根据操作类型自动消费使用量
    try {
      switch (operationType) {
        case 'create_memorial':
          await this.consumeFeatureUsage('memorial_spaces', 1);
          break;
        case 'upload_file':
          if (options.fileSizeGb) {
            await this.consumeFeatureUsage('storage_gb', options.fileSizeGb);
          }
          break;
        case 'use_ai':
          if (options.minutes) {
            await this.consumeFeatureUsage('ai_minutes', options.minutes);
          }
          break;
        case 'api_call':
          await this.consumeFeatureUsage('api_calls', 1);
          break;
      }
    } catch (error) {
      console.warn('更新使用量失败:', error);
      // 不抛出错误，因为主要操作已经成功
    }

    return result;
  }

  // 使用量警告检查
  async checkUsageAlerts(): Promise<{
    alerts: Array<{
      feature: string;
      usage_percentage: number;
      level: 'warning' | 'critical';
      message: string;
    }>;
  }> {
    try {
      const summary = await this.getMembershipSummary();
      const alerts = [];

      for (const [feature, percentage] of Object.entries(summary.usage_percentages)) {
        if (percentage === null) continue;
        
        if (percentage >= 90) {
          alerts.push({
            feature,
            usage_percentage: percentage,
            level: 'critical' as const,
            message: `${feature} 使用量已达到 ${percentage.toFixed(1)}%，即将达到限制`
          });
        } else if (percentage >= 70) {
          alerts.push({
            feature,
            usage_percentage: percentage,
            level: 'warning' as const,
            message: `${feature} 使用量已达到 ${percentage.toFixed(1)}%`
          });
        }
      }

      return { alerts };
    } catch (error) {
      console.error('检查使用量警告失败:', error);
      return { alerts: [] };
    }
  }

  // 功能特定的验证方法
  async validateMemorialCreation(): Promise<{ canCreate: boolean; message?: string }> {
    try {
      const check = await this.canCreateMemorial();
      return {
        canCreate: check.allowed,
        message: check.allowed 
          ? undefined 
          : `已达到纪念空间创建限制 (${check.current_usage}/${check.limit})`
      };
    } catch (error) {
      return {
        canCreate: false,
        message: '检查权限失败'
      };
    }
  }

  async validateFileUpload(fileSizeGB: number): Promise<{ canUpload: boolean; message?: string }> {
    try {
      const validation = await this.validateOperation('upload_file', { fileSizeGb: fileSizeGB });
      return {
        canUpload: validation.allowed,
        message: validation.allowed 
          ? undefined 
          : validation.error || '存储空间不足'
      };
    } catch (error) {
      return {
        canUpload: false,
        message: '检查权限失败'
      };
    }
  }

  async validateAIUsage(minutes: number): Promise<{ canUse: boolean; message?: string }> {
    try {
      const check = await this.canUseAI(minutes);
      return {
        canUse: check.allowed,
        message: check.allowed 
          ? undefined 
          : `AI服务使用时长不足 (剩余: ${check.remaining} 分钟)`
      };
    } catch (error) {
      return {
        canUse: false,
        message: '检查权限失败'
      };
    }
  }
}

export const membershipService = new MembershipService();