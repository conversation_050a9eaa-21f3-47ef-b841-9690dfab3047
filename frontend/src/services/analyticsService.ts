import { API_BASE_URL } from '../config';

export interface AnalyticsData {
  revenue: RevenueMetrics;
  users: UserMetrics;
  conversion: ConversionMetrics;
  retention: RetentionMetrics;
  products: ProductMetrics;
  growth: GrowthMetrics;
  membership: MembershipAnalytics;
}

export interface RevenueMetrics {
  mrr: number;
  arr: number;
  total_revenue: number;
  revenue_growth: number;
  avg_revenue_per_user: number;
  by_source: RevenueBySource[];
  by_plan: RevenueByPlan[];
  trends: RevenueTrend[];
}

export interface RevenueBySource {
  source: 'subscription' | 'virtual_products' | 'premium_services' | 'customization';
  amount: number;
  percentage: number;
  growth: number;
}

export interface RevenueByPlan {
  plan: 'free' | 'premium' | 'family' | 'enterprise';
  amount: number;
  users: number;
  arpu: number;
}

export interface RevenueTrend {
  date: string;
  amount: number;
  subscriptions: number;
  virtual_products: number;
  services: number;
}

export interface UserMetrics {
  total_users: number;
  active_users: {
    daily: number;
    weekly: number;
    monthly: number;
  };
  new_users: number;
  user_growth_rate: number;
  churn_rate: number;
  ltv: number;
  cac: number;
  paying_users: number;
  conversion_rate: number;
}

export interface ConversionMetrics {
  funnel_steps: FunnelStep[];
  by_source: ConversionBySource[];
  time_to_convert: number;
  dropoff_points: DropoffPoint[];
}

export interface FunnelStep {
  step: string;
  users: number;
  conversion_rate: number;
  dropoff: number;
}

export interface ConversionBySource {
  source: string;
  visitors: number;
  conversions: number;
  rate: number;
}

export interface DropoffPoint {
  point: string;
  users: number;
  reason: string;
}

export interface RetentionMetrics {
  cohort_analysis: CohortData[];
  retention_rates: {
    day1: number;
    day7: number;
    day30: number;
    day90: number;
  };
  engagement_score: number;
  stickiness: number;
}

export interface CohortData {
  month: string;
  users: number;
  retention: number[];
}

export interface ProductMetrics {
  virtual_products: VirtualProductStats[];
  subscription_plans: SubscriptionStats[];
  premium_features: FeatureUsageStats[];
  customer_satisfaction: number;
}

export interface VirtualProductStats {
  product_id: string;
  name: string;
  sales: number;
  revenue: number;
  popularity: number;
  rating: number;
}

export interface SubscriptionStats {
  plan: string;
  subscribers: number;
  churn_rate: number;
  avg_lifetime: number;
  upgrade_rate: number;
}

export interface FeatureUsageStats {
  feature: string;
  usage: number;
  conversion_impact: number;
  satisfaction: number;
}

export interface GrowthMetrics {
  nps: number;
  referral_rate: number;
  viral_coefficient: number;
  organic_growth: number;
  paid_growth: number;
  marketing_roi: number;
}

export interface MembershipAnalytics {
  tier_distribution: Record<string, {
    user_count: number;
    percentage: number;
  }>;
  conversion_rates: Record<string, number>;
  feature_usage: Record<string, {
    avg_usage_by_tier: Record<string, number>;
    limit_hit_rate: Record<string, number>;
  }>;
  upgrade_paths: UpgradePath[];
  churn_analysis: ChurnAnalysis;
  revenue_contribution: Record<string, {
    monthly_revenue: number;
    user_count: number;
    arpu: number;
    ltv: number;
  }>;
  membership_health_score: number;
}

export interface UpgradePath {
  from_tier: string;
  to_tier: string;
  conversion_rate: number;
  avg_time_to_upgrade: number;
  primary_trigger: string;
}

export interface ChurnAnalysis {
  churn_rate_by_tier: Record<string, number>;
  churn_reasons: Array<{
    reason: string;
    percentage: number;
    mainly_affects: string[];
  }>;
}

export interface KPICard {
  title: string;
  value: string;
  change: number;
  format: 'currency' | 'number' | 'percentage' | 'score';
}

export interface DashboardSummary {
  kpi_cards: KPICard[];
  alerts: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
  }>;
  quick_stats: {
    total_revenue: number;
    total_users: number;
    paying_users: number;
    churn_rate: number;
  };
}

export interface MembershipRecommendation {
  type: 'urgent' | 'opportunity' | 'optimization';
  title: string;
  description: string;
  actions: string[];
}

class AnalyticsService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${API_BASE_URL}/analytics`;
  }

  private async fetchWithAuth(url: string, options: RequestInit = {}): Promise<Response> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
    }

    return response;
  }

  /**
   * 获取完整的商业分析数据
   */
  async getBusinessAnalytics(dateRange: string = '30d'): Promise<AnalyticsData> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/business?date_range=${dateRange}`);
    const data = await response.json();
    return data.data;
  }

  /**
   * 获取收入分析数据
   */
  async getRevenueAnalytics(dateRange: string = '30d'): Promise<RevenueMetrics> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/revenue?date_range=${dateRange}`);
    const data = await response.json();
    return data.data;
  }

  /**
   * 获取用户分析数据
   */
  async getUserAnalytics(dateRange: string = '30d'): Promise<UserMetrics> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/users?date_range=${dateRange}`);
    const data = await response.json();
    return data.data;
  }

  /**
   * 获取转化分析数据
   */
  async getConversionAnalytics(dateRange: string = '30d'): Promise<ConversionMetrics> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/conversion?date_range=${dateRange}`);
    const data = await response.json();
    return data.data;
  }

  /**
   * 获取留存分析数据
   */
  async getRetentionAnalytics(dateRange: string = '30d'): Promise<RetentionMetrics> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/retention?date_range=${dateRange}`);
    const data = await response.json();
    return data.data;
  }

  /**
   * 获取产品分析数据
   */
  async getProductAnalytics(dateRange: string = '30d'): Promise<ProductMetrics> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/products?date_range=${dateRange}`);
    const data = await response.json();
    return data.data;
  }

  /**
   * 获取增长分析数据
   */
  async getGrowthAnalytics(dateRange: string = '30d'): Promise<GrowthMetrics> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/growth?date_range=${dateRange}`);
    const data = await response.json();
    return data.data;
  }

  /**
   * 获取会员权益分析数据
   */
  async getMembershipAnalytics(dateRange: string = '30d'): Promise<MembershipAnalytics> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/membership?date_range=${dateRange}`);
    const data = await response.json();
    return data.data;
  }

  /**
   * 获取会员系统优化建议
   */
  async getMembershipRecommendations(): Promise<MembershipRecommendation[]> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/membership/recommendations`);
    const data = await response.json();
    return data.data;
  }

  /**
   * 获取仪表板概览数据
   */
  async getDashboardSummary(): Promise<DashboardSummary> {
    const response = await this.fetchWithAuth(`${this.baseUrl}/dashboard/summary`);
    const data = await response.json();
    return data.data;
  }

  /**
   * 导出分析数据
   */
  async exportAnalyticsData(
    dateRange: string = '30d',
    format: 'json' | 'csv' | 'excel' = 'json'
  ): Promise<any> {
    const response = await this.fetchWithAuth(
      `${this.baseUrl}/export?date_range=${dateRange}&format=${format}`
    );
    return response.json();
  }

  /**
   * 获取实时指标 (模拟)
   */
  async getRealTimeMetrics(): Promise<{
    active_users_now: number;
    revenue_today: number;
    new_registrations_today: number;
    ai_requests_today: number;
    server_status: 'healthy' | 'warning' | 'error';
  }> {
    // 模拟实时数据
    return {
      active_users_now: Math.floor(Math.random() * 500) + 200,
      revenue_today: Math.floor(Math.random() * 5000) + 2000,
      new_registrations_today: Math.floor(Math.random() * 50) + 10,
      ai_requests_today: Math.floor(Math.random() * 1000) + 500,
      server_status: 'healthy'
    };
  }

  /**
   * 获取趋势预测数据 (模拟)
   */
  async getForecastData(_metric: string, periods: number = 12): Promise<Array<{
    period: string;
    actual?: number;
    predicted: number;
    confidence_lower: number;
    confidence_upper: number;
  }>> {
    // 模拟预测数据
    const data = [];
    const currentDate = new Date();
    
    for (let i = 0; i < periods; i++) {
      const date = new Date(currentDate);
      date.setMonth(date.getMonth() + i);
      
      const baseValue = 100000 + i * 5000;
      const variance = baseValue * 0.1;
      
      data.push({
        period: date.toISOString().slice(0, 7), // YYYY-MM format
        predicted: baseValue + Math.random() * variance - variance/2,
        confidence_lower: baseValue - variance,
        confidence_upper: baseValue + variance
      });
    }
    
    return data;
  }

  /**
   * 比较不同时间段的指标
   */
  async compareMetrics(
    _metric: string,
    _currentPeriod: string,
    _comparisonPeriod: string
  ): Promise<{
    current: number;
    comparison: number;
    change: number;
    change_percentage: number;
  }> {
    // 模拟对比数据
    const current = Math.floor(Math.random() * 10000) + 5000;
    const comparison = Math.floor(Math.random() * 8000) + 4000;
    const change = current - comparison;
    const change_percentage = (change / comparison) * 100;
    
    return {
      current,
      comparison,
      change,
      change_percentage
    };
  }
}

export const analyticsService = new AnalyticsService();