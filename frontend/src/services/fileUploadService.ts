// 文件上传服务
export interface UploadResponse {
  id: string;
  asset_type: string;
  title?: string;
  description?: string;
  file_url: string;
  thumbnail_url?: string;
  original_filename: string;
  file_size: number;
  upload_date: string;
  is_ai_enhanced: boolean;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface FileUploadOptions {
  asset_type: 'life_photo' | 'cover_image' | 'video' | 'audio' | 'document';
  title?: string;
  description?: string;
  display_order?: number;
  onProgress?: (progress: UploadProgress) => void;
}

export class FileUploadService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_APP_API_BASE_URL || '/api/v1';
  }

  /**
   * 上传文件到纪念空间
   */
  async uploadToMemorialSpace(
    spaceId: string,
    file: File,
    options: FileUploadOptions
  ): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('asset_type', options.asset_type);
    
    if (options.title) {
      formData.append('title', options.title);
    }
    
    if (options.description) {
      formData.append('description', options.description);
    }
    
    if (options.display_order !== undefined) {
      formData.append('display_order', options.display_order.toString());
    }

    const token = localStorage.getItem('authToken');
    if (!token) {
      throw new Error('认证令牌未找到');
    }

    const xhr = new XMLHttpRequest();
    
    return new Promise((resolve, reject) => {
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (error) {
            reject(new Error('服务器响应格式错误'));
          }
        } else {
          try {
            const errorData = JSON.parse(xhr.responseText);
            reject(new Error(errorData.detail || `上传失败 (${xhr.status})`));
          } catch {
            reject(new Error(`上传失败 (${xhr.status})`));
          }
        }
      };

      xhr.onerror = () => {
        reject(new Error('网络错误'));
      };

      xhr.onabort = () => {
        reject(new Error('上传被取消'));
      };

      // 上传进度监听
      if (options.onProgress) {
        xhr.upload.onprogress = (event) => {
          if (event.lengthComputable) {
            const progress: UploadProgress = {
              loaded: event.loaded,
              total: event.total,
              percentage: Math.round((event.loaded / event.total) * 100)
            };
            options.onProgress!(progress);
          }
        };
      }

      xhr.open('POST', `${this.baseUrl}/memorial-spaces/${spaceId}/assets/`);
      xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      xhr.send(formData);
    });
  }

  /**
   * 获取纪念空间的资产列表
   */
  async getMemorialAssets(
    spaceId: string,
    options?: {
      asset_type?: string;
      skip?: number;
      limit?: number;
    }
  ): Promise<{ assets: UploadResponse[]; total_count: number }> {
    const token = localStorage.getItem('authToken');
    if (!token) {
      throw new Error('认证令牌未找到');
    }

    const params = new URLSearchParams();
    if (options?.asset_type) {
      params.append('asset_type', options.asset_type);
    }
    if (options?.skip !== undefined) {
      params.append('skip', options.skip.toString());
    }
    if (options?.limit !== undefined) {
      params.append('limit', options.limit.toString());
    }

    const queryString = params.toString();
    const url = `${this.baseUrl}/memorial-spaces/${spaceId}/assets/${queryString ? '?' + queryString : ''}`;

    const response = await fetch(url, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `获取资产列表失败 (${response.status})`);
    }

    return response.json();
  }

  /**
   * 删除纪念空间资产
   */
  async deleteMemorialAsset(spaceId: string, assetId: string): Promise<void> {
    const token = localStorage.getItem('authToken');
    if (!token) {
      throw new Error('认证令牌未找到');
    }

    const response = await fetch(`${this.baseUrl}/memorial-spaces/${spaceId}/assets/${assetId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `删除资产失败 (${response.status})`);
    }
  }

  /**
   * 验证文件类型和大小
   */
  validateFile(file: File, options: { 
    maxSize?: number; 
    allowedTypes?: string[];
    allowedExtensions?: string[];
  } = {}): { valid: boolean; error?: string } {
    const {
      maxSize = 10 * 1024 * 1024, // 默认10MB
      allowedTypes = ['image/*', 'video/*', 'audio/*'],
      allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.mp4', '.mov', '.mp3', '.wav', '.pdf']
    } = options;

    // 检查文件大小
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `文件大小超过限制 (${(maxSize / 1024 / 1024).toFixed(1)}MB)`
      };
    }

    // 检查文件类型
    const fileType = file.type;
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

    const typeMatches = allowedTypes.some(type => {
      if (type.endsWith('/*')) {
        return fileType.startsWith(type.slice(0, -1));
      }
      return fileType === type;
    });

    const extensionMatches = allowedExtensions.includes(fileExtension);

    if (!typeMatches && !extensionMatches) {
      return {
        valid: false,
        error: `不支持的文件类型。支持的格式：${allowedExtensions.join(', ')}`
      };
    }

    return { valid: true };
  }

  /**
   * 生成缩略图（仅用于图片）
   */
  async generateThumbnail(file: File, maxWidth: number = 200, maxHeight: number = 200): Promise<Blob> {
    return new Promise((resolve, reject) => {
      if (!file.type.startsWith('image/')) {
        reject(new Error('只能为图片文件生成缩略图'));
        return;
      }

      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // 计算缩略图尺寸
        let { width, height } = img;
        const aspectRatio = width / height;

        if (width > height) {
          width = Math.min(width, maxWidth);
          height = width / aspectRatio;
        } else {
          height = Math.min(height, maxHeight);
          width = height * aspectRatio;
        }

        canvas.width = width;
        canvas.height = height;

        // 绘制缩略图
        ctx?.drawImage(img, 0, 0, width, height);

        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('生成缩略图失败'));
          }
        }, 'image/jpeg', 0.8);
      };

      img.onerror = () => {
        reject(new Error('无法加载图片'));
      };

      img.src = URL.createObjectURL(file);
    });
  }
}

// 单例导出
export const fileUploadService = new FileUploadService();