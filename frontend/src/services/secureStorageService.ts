// 前端安全存储服务
import CryptoJS from "crypto-js";

interface StorageData {
  encrypted: string;
  iv: string;
  timestamp: number;
}

class SecureStorageService {
  private static instance: SecureStorageService;
  private encryptionKey: string;
  private readonly storagePrefix = "memorial_secure_";
  private readonly keyStoreName = "memorial_encryption_key";

  private constructor() {
    this.encryptionKey = this.getOrCreateEncryptionKey();
  }

  public static getInstance(): SecureStorageService {
    if (!SecureStorageService.instance) {
      SecureStorageService.instance = new SecureStorageService();
    }
    return SecureStorageService.instance;
  }

  // 获取或创建加密密钥
  private getOrCreateEncryptionKey(): string {
    let key = localStorage.getItem(this.keyStoreName);

    if (!key) {
      // 生成新的加密密钥
      key = CryptoJS.lib.WordArray.random(256 / 8).toString();
      localStorage.setItem(this.keyStoreName, key);
    }

    return key;
  }

  // 加密数据
  private encrypt(data: string): StorageData {
    const iv = CryptoJS.lib.WordArray.random(128 / 8);
    const encrypted = CryptoJS.AES.encrypt(data, this.encryptionKey, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    });

    return {
      encrypted: encrypted.toString(),
      iv: iv.toString(),
      timestamp: Date.now(),
    };
  }

  // 解密数据
  private decrypt(storageData: StorageData): string | null {
    try {
      const iv = CryptoJS.enc.Hex.parse(storageData.iv);
      const decrypted = CryptoJS.AES.decrypt(
        storageData.encrypted,
        this.encryptionKey,
        {
          iv: iv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Pkcs7,
        },
      );

      const decryptedString = decrypted.toString(CryptoJS.enc.Utf8);

      if (!decryptedString) {
        console.warn("解密失败：空字符串");
        return null;
      }

      return decryptedString;
    } catch (error) {
      console.error("解密失败:", error);
      return null;
    }
  }

  // 生成数据完整性校验码
  private generateChecksum(data: string): string {
    return CryptoJS.SHA256(data).toString();
  }

  // 验证数据完整性
  private verifyIntegrity(key: string, data: string): boolean {
    const checksumKey = `${key}_checksum`;
    const storedChecksum = localStorage.getItem(
      this.storagePrefix + checksumKey,
    );
    const currentChecksum = this.generateChecksum(data);

    return storedChecksum === currentChecksum;
  }

  // 安全存储数据
  public setItem(key: string, value: string): void {
    try {
      const encryptedData = this.encrypt(value);
      const storageKey = this.storagePrefix + key;

      localStorage.setItem(storageKey, JSON.stringify(encryptedData));

      // 存储校验码
      const checksum = this.generateChecksum(value);
      localStorage.setItem(`${storageKey}_checksum`, checksum);
    } catch (error) {
      console.error("存储数据失败:", error);
      throw new Error("存储数据失败");
    }
  }

  // 安全读取数据
  public getItem(key: string): string | null {
    try {
      const storageKey = this.storagePrefix + key;
      const encryptedDataStr = localStorage.getItem(storageKey);

      if (!encryptedDataStr) {
        return null;
      }

      const encryptedData: StorageData = JSON.parse(encryptedDataStr);
      const decryptedValue = this.decrypt(encryptedData);

      if (!decryptedValue) {
        // 解密失败，清除可能损坏的数据
        this.removeItem(key);
        return null;
      }

      // 验证数据完整性
      if (!this.verifyIntegrity(key, decryptedValue)) {
        console.warn(`数据完整性验证失败，清除数据: ${key}`);
        this.removeItem(key);
        return null;
      }

      // 检查数据是否过期（可选功能）
      const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
      if (Date.now() - encryptedData.timestamp > maxAge) {
        console.info(`数据已过期，清除数据: ${key}`);
        this.removeItem(key);
        return null;
      }

      return decryptedValue;
    } catch (error) {
      console.error("读取数据失败:", error);
      this.removeItem(key);
      return null;
    }
  }

  // 删除数据
  public removeItem(key: string): void {
    const storageKey = this.storagePrefix + key;
    localStorage.removeItem(storageKey);
    localStorage.removeItem(`${storageKey}_checksum`);
  }

  // 检查key是否存在
  public hasItem(key: string): boolean {
    const storageKey = this.storagePrefix + key;
    return localStorage.getItem(storageKey) !== null;
  }

  // 清除所有安全存储的数据
  public clear(): void {
    const keys = Object.keys(localStorage);
    keys.forEach((key) => {
      if (key.startsWith(this.storagePrefix)) {
        localStorage.removeItem(key);
      }
    });
  }

  // Token相关的便捷方法
  public setAuthToken(token: string): void {
    this.setItem("authToken", token);
  }

  public getAuthToken(): string | null {
    return this.getItem("authToken");
  }

  public setRefreshToken(token: string): void {
    this.setItem("refreshToken", token);
  }

  public getRefreshToken(): string | null {
    return this.getItem("refreshToken");
  }

  public setTokenExpiresAt(expiresAt: string): void {
    this.setItem("tokenExpiresAt", expiresAt);
  }

  public getTokenExpiresAt(): string | null {
    return this.getItem("tokenExpiresAt");
  }

  public setUser(user: string): void {
    this.setItem("user", user);
  }

  public getUser(): string | null {
    return this.getItem("user");
  }

  // 清除所有认证数据
  public clearAuthData(): void {
    this.removeItem("authToken");
    this.removeItem("refreshToken");
    this.removeItem("tokenExpiresAt");
    this.removeItem("user");
  }

  // 数据迁移：从localStorage迁移到安全存储
  public migrateFromLocalStorage(): void {
    const keysToMigrate = [
      "authToken",
      "refreshToken",
      "tokenExpiresAt",
      "user",
    ];

    keysToMigrate.forEach((key) => {
      const oldValue = localStorage.getItem(key);
      if (oldValue) {
        this.setItem(key, oldValue);
        localStorage.removeItem(key);
      }
    });
  }

  // 安全性检查
  public performSecurityCheck(): boolean {
    try {
      // 检查localStorage是否可用
      if (typeof Storage === "undefined") {
        console.warn("浏览器不支持localStorage");
        return false;
      }

      // 检查加密功能是否正常
      const testData = "security_test";
      const encrypted = this.encrypt(testData);
      const decrypted = this.decrypt(encrypted);

      if (decrypted !== testData) {
        console.error("加密/解密功能异常");
        return false;
      }

      return true;
    } catch (error) {
      console.error("安全性检查失败:", error);
      return false;
    }
  }

  // 获取存储统计信息
  public getStorageStats(): {
    totalItems: number;
    totalSize: number;
    oldestTimestamp: number;
    newestTimestamp: number;
  } {
    const keys = Object.keys(localStorage);
    const secureKeys = keys.filter(
      (key) => key.startsWith(this.storagePrefix) && !key.endsWith("_checksum"),
    );

    let totalSize = 0;
    let oldestTimestamp = Date.now();
    let newestTimestamp = 0;

    secureKeys.forEach((key) => {
      const data = localStorage.getItem(key);
      if (data) {
        totalSize += data.length;
        try {
          const storageData: StorageData = JSON.parse(data);
          oldestTimestamp = Math.min(oldestTimestamp, storageData.timestamp);
          newestTimestamp = Math.max(newestTimestamp, storageData.timestamp);
        } catch {
          // 忽略解析错误的项目
        }
      }
    });

    return {
      totalItems: secureKeys.length,
      totalSize,
      oldestTimestamp,
      newestTimestamp,
    };
  }
}

// 导出单例实例
export const secureStorage = SecureStorageService.getInstance();
export default secureStorage;
