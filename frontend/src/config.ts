// Configuration module for Memorial frontend

// API configuration
export const API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL || 'http://localhost:5001/api/v1';

// Payment configuration
export const STRIPE_PUBLIC_KEY = import.meta.env.VITE_STRIPE_PUBLIC_KEY || '';

// Environment
export const IS_PRODUCTION = import.meta.env.PROD;
export const IS_DEVELOPMENT = import.meta.env.DEV;

// Analytics configuration
export const ANALYTICS_ENABLED = import.meta.env.VITE_ANALYTICS_ENABLED === 'true';

// Feature flags
export const FEATURES = {
  AI_SERVICES: true,
  PAYMENT_GATEWAY: true,
  MEMBERSHIP_SYSTEM: true,
  ANALYTICS_DASHBOARD: true,
  AR_EXPERIENCE: true,
};

// Default export
export default {
  API_BASE_URL,
  STRIPE_PUBLIC_KEY,
  IS_PRODUCTION,
  IS_DEVELOPMENT,
  ANALYTICS_ENABLED,
  FEATURES,
};