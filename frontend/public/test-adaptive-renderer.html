<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自适应渲染测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .info-box {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button-group {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }
        button {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            background-color: #4CAF50;
            color: white;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #45a049;
        }
        .device-info {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border-left: 4px solid #4CAF50;
        }
        .test-frame {
            width: 100%;
            height: 500px;
            border: none;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>自适应渲染测试</h1>
        
        <div class="info-box">
            <h2>关于此测试</h2>
            <p>此页面用于测试自适应渲染系统，该系统会根据设备性能自动选择最合适的渲染方式：</p>
            <ul>
                <li><strong>高性能设备</strong>：使用完整3D渲染</li>
                <li><strong>中等性能设备</strong>：使用简化3D模型</li>
                <li><strong>低性能设备</strong>：使用预渲染图像或服务器渲染</li>
            </ul>
            <p>您可以使用下面的按钮访问不同的测试页面：</p>
        </div>
        
        <div class="button-group">
            <button onclick="window.location.href='/memorial/buddhist-temple'">访问佛教寺庙页面</button>
            <button onclick="window.location.href='/models/test-buddhist-temple.html'">访问模型测试页面</button>
        </div>
        
        <div class="device-info" id="deviceInfo">
            <h3>您的设备信息</h3>
            <div id="deviceDetails">正在检测设备信息...</div>
        </div>
        
        <h2>预览</h2>
        <iframe src="/memorial/buddhist-temple" class="test-frame" id="testFrame"></iframe>
    </div>

    <script>
        // 检测设备信息
        document.addEventListener('DOMContentLoaded', function() {
            const deviceDetails = document.getElementById('deviceDetails');
            
            // 收集基本设备信息
            const deviceInfo = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                screenWidth: window.screen.width,
                screenHeight: window.screen.height,
                devicePixelRatio: window.devicePixelRatio,
                touchSupport: 'ontouchstart' in window,
                memoryInfo: navigator.deviceMemory || 'Unknown',
                cpuCores: navigator.hardwareConcurrency || 'Unknown',
                connectionType: navigator.connection ? navigator.connection.effectiveType : 'Unknown'
            };
            
            // 检测WebGL支持
            function checkWebGLSupport() {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                
                if (!gl) {
                    return {
                        supported: false,
                        info: 'Not supported'
                    };
                }
                
                const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                if (debugInfo) {
                    return {
                        supported: true,
                        vendor: gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL),
                        renderer: gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL),
                        version: gl.getParameter(gl.VERSION)
                    };
                } else {
                    return {
                        supported: true,
                        info: 'Supported, but detailed info not available'
                    };
                }
            }
            
            const webGLInfo = checkWebGLSupport();
            deviceInfo.webGL = webGLInfo;
            
            // 估计设备性能级别
            function estimatePerformanceTier() {
                let score = 0;
                
                // 基于设备内存
                if (typeof deviceInfo.memoryInfo === 'number') {
                    if (deviceInfo.memoryInfo >= 4) score += 2;
                    else if (deviceInfo.memoryInfo >= 2) score += 1;
                }
                
                // 基于CPU核心数
                if (typeof deviceInfo.cpuCores === 'number') {
                    if (deviceInfo.cpuCores >= 8) score += 2;
                    else if (deviceInfo.cpuCores >= 4) score += 1;
                }
                
                // 基于WebGL支持
                if (webGLInfo.supported) {
                    score += 1;
                    
                    // 检查是否是高性能GPU
                    if (webGLInfo.renderer) {
                        const rendererLower = webGLInfo.renderer.toLowerCase();
                        if (rendererLower.includes('nvidia') || 
                            rendererLower.includes('amd') || 
                            rendererLower.includes('radeon') ||
                            rendererLower.includes('intel iris') ||
                            rendererLower.includes('apple m')) {
                            score += 2;
                        }
                    }
                }
                
                // 基于设备像素比
                if (deviceInfo.devicePixelRatio >= 2) score += 1;
                
                // 基于是否是移动设备
                if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(deviceInfo.userAgent)) {
                    score -= 1;
                }
                
                // 确定性能级别
                if (score >= 5) return 'High';
                if (score >= 3) return 'Medium';
                return 'Low';
            }
            
            deviceInfo.estimatedPerformanceTier = estimatePerformanceTier();
            
            // 显示设备信息
            let html = `
                <p><strong>浏览器:</strong> ${deviceInfo.userAgent}</p>
                <p><strong>平台:</strong> ${deviceInfo.platform}</p>
                <p><strong>屏幕分辨率:</strong> ${deviceInfo.screenWidth} x ${deviceInfo.screenHeight} (像素比: ${deviceInfo.devicePixelRatio})</p>
                <p><strong>触摸支持:</strong> ${deviceInfo.touchSupport ? '是' : '否'}</p>
                <p><strong>设备内存:</strong> ${deviceInfo.memoryInfo} GB</p>
                <p><strong>CPU核心数:</strong> ${deviceInfo.cpuCores}</p>
                <p><strong>网络连接:</strong> ${deviceInfo.connectionType}</p>
                <p><strong>WebGL支持:</strong> ${webGLInfo.supported ? '是' : '否'}</p>
            `;
            
            if (webGLInfo.supported && webGLInfo.renderer) {
                html += `
                    <p><strong>GPU厂商:</strong> ${webGLInfo.vendor}</p>
                    <p><strong>GPU型号:</strong> ${webGLInfo.renderer}</p>
                    <p><strong>WebGL版本:</strong> ${webGLInfo.version}</p>
                `;
            }
            
            html += `<p><strong>估计性能级别:</strong> <span style="font-weight: bold; color: ${
                deviceInfo.estimatedPerformanceTier === 'High' ? 'green' : 
                deviceInfo.estimatedPerformanceTier === 'Medium' ? 'orange' : 'red'
            };">${deviceInfo.estimatedPerformanceTier}</span></p>`;
            
            deviceDetails.innerHTML = html;
        });
    </script>
</body>
</html>
