<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI服务功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .service-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .service-card h3 {
            margin: 0 0 15px 0;
            font-size: 1.5em;
            color: #ffd700;
        }
        
        .service-card p {
            margin-bottom: 20px;
            line-height: 1.6;
            opacity: 0.9;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        
        .feature-list li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }
        
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4ade80;
            font-weight: bold;
        }
        
        .test-button {
            background: linear-gradient(45deg, #4ade80, #22c55e);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 15px;
        }
        
        .test-button:hover {
            background: linear-gradient(45deg, #22c55e, #16a34a);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(34, 197, 94, 0.4);
        }
        
        .test-button:disabled {
            background: #6b7280;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .status-panel {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .status-panel h3 {
            margin: 0 0 15px 0;
            color: #ffd700;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-healthy {
            background: #22c55e;
            color: white;
        }
        
        .status-warning {
            background: #f59e0b;
            color: white;
        }
        
        .status-error {
            background: #ef4444;
            color: white;
        }
        
        .api-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .api-info h4 {
            margin: 0 0 10px 0;
            color: #ffd700;
        }
        
        .api-endpoint {
            font-family: 'Courier New', monospace;
            background: rgba(0, 0, 0, 0.3);
            padding: 8px 12px;
            border-radius: 5px;
            margin: 5px 0;
            font-size: 14px;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4ade80, #22c55e);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .demo-section {
            margin-top: 40px;
            text-align: center;
        }
        
        .demo-section h2 {
            margin-bottom: 20px;
            color: #ffd700;
        }
        
        .file-upload {
            background: rgba(255, 255, 255, 0.1);
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 40px;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .file-upload:hover {
            border-color: #4ade80;
            background: rgba(74, 222, 128, 0.1);
        }
        
        .file-upload input {
            display: none;
        }
        
        .upload-text {
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .upload-hint {
            font-size: 14px;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI服务功能测试</h1>
            <p>Memorial项目 - Phase 1.2 AI服务增强版本</p>
        </div>

        <div class="services-grid">
            <!-- 图像处理服务 -->
            <div class="service-card">
                <h3>📸 图像处理服务</h3>
                <p>支持照片修复、增强、上色、背景移除等功能</p>
                <ul class="feature-list">
                    <li>老照片修复和去噪</li>
                    <li>分辨率增强 (2-8倍)</li>
                    <li>黑白照片智能上色</li>
                    <li>背景移除和替换</li>
                    <li>批量处理支持</li>
                </ul>
                <button class="test-button" onclick="testImageServices()">测试图像服务</button>
            </div>

            <!-- 语音处理服务 -->
            <div class="service-card">
                <h3>🎤 语音处理服务</h3>
                <p>多语言语音克隆和音质增强功能</p>
                <ul class="feature-list">
                    <li>6种语言支持</li>
                    <li>语音质量增强</li>
                    <li>语速和音调调整</li>
                    <li>批量语音生成</li>
                    <li>语言自动检测</li>
                </ul>
                <button class="test-button" onclick="testVoiceServices()">测试语音服务</button>
            </div>

            <!-- 任务管理系统 -->
            <div class="service-card">
                <h3>⚙️ 任务管理系统</h3>
                <p>异步任务处理和进度跟踪</p>
                <ul class="feature-list">
                    <li>实时进度跟踪</li>
                    <li>任务队列管理</li>
                    <li>错误处理和重试</li>
                    <li>任务取消功能</li>
                    <li>批量任务支持</li>
                </ul>
                <button class="test-button" onclick="testTaskManager()">测试任务管理</button>
            </div>

            <!-- 性能监控 -->
            <div class="service-card">
                <h3>📊 性能监控</h3>
                <p>服务健康状态和性能指标监控</p>
                <ul class="feature-list">
                    <li>服务健康检查</li>
                    <li>API响应时间监控</li>
                    <li>错误率统计</li>
                    <li>资源使用监控</li>
                    <li>自动优化建议</li>
                </ul>
                <button class="test-button" onclick="checkHealth()">检查服务状态</button>
            </div>
        </div>

        <!-- 服务状态面板 -->
        <div class="status-panel">
            <h3>🔍 服务状态监控</h3>
            <div id="statusContainer">
                <div class="status-item">
                    <span>正在检查服务状态...</span>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API信息 -->
        <div class="api-info">
            <h4>🔗 API端点信息</h4>
            <div class="api-endpoint">GET /api/v1/ai-enhanced/health - 健康检查</div>
            <div class="api-endpoint">GET /api/v1/ai-enhanced/models - 获取可用模型</div>
            <div class="api-endpoint">POST /api/v1/ai-enhanced/batch/photo-process - 批量图像处理</div>
            <div class="api-endpoint">POST /api/v1/ai-enhanced/voice/clone-enhanced - 增强语音克隆</div>
            <div class="api-endpoint">GET /api/v1/ai-enhanced/task/{id}/status - 任务状态查询</div>
        </div>

        <!-- 演示区域 -->
        <div class="demo-section">
            <h2>🧪 功能演示</h2>
            <div class="file-upload" onclick="document.getElementById('fileInput').click()">
                <input type="file" id="fileInput" accept="image/*,audio/*" onchange="handleFileUpload(event)">
                <div class="upload-text">点击上传文件进行测试</div>
                <div class="upload-hint">支持图片和音频文件</div>
            </div>
            <div id="demoResult"></div>
        </div>
    </div>

    <script>
        // API基础URL
        const API_BASE = 'http://localhost:8000/api/v1/ai-enhanced';
        
        // 检查服务健康状态
        async function checkHealth() {
            const statusContainer = document.getElementById('statusContainer');
            statusContainer.innerHTML = '<div class="status-item"><span>正在检查服务状态...</span></div>';
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                let statusHTML = '';
                
                // 总体状态
                const overallStatus = data.status === 'healthy' ? 'healthy' : 'error';
                statusHTML += `
                    <div class="status-item">
                        <span>总体状态</span>
                        <span class="status-indicator status-${overallStatus}">${data.status}</span>
                    </div>
                `;
                
                // 各服务状态
                if (data.services) {
                    Object.entries(data.services).forEach(([service, info]) => {
                        const status = info.status === 'healthy' ? 'healthy' : 
                                     info.status === 'degraded' ? 'warning' : 'error';
                        statusHTML += `
                            <div class="status-item">
                                <span>${service}</span>
                                <span class="status-indicator status-${status}">${info.status}</span>
                            </div>
                        `;
                    });
                }
                
                statusContainer.innerHTML = statusHTML;
                
            } catch (error) {
                statusContainer.innerHTML = `
                    <div class="status-item">
                        <span>服务连接失败</span>
                        <span class="status-indicator status-error">ERROR</span>
                    </div>
                `;
                console.error('Health check failed:', error);
            }
        }
        
        // 测试图像服务
        async function testImageServices() {
            try {
                const response = await fetch(`${API_BASE}/models`);
                const data = await response.json();
                
                alert(`图像服务测试成功！\n支持的模型: ${Object.keys(data.models).length}个\n批量处理: ${data.features.batch_processing ? '支持' : '不支持'}`);
            } catch (error) {
                alert('图像服务测试失败: ' + error.message);
            }
        }
        
        // 测试语音服务
        async function testVoiceServices() {
            try {
                const response = await fetch(`${API_BASE}/voice/languages`);
                const data = await response.json();
                
                alert(`语音服务测试成功！\n支持的语言: ${data.total_languages}种\n语言列表: ${Object.keys(data.languages).join(', ')}`);
            } catch (error) {
                alert('语音服务测试失败: ' + error.message);
            }
        }
        
        // 测试任务管理
        async function testTaskManager() {
            try {
                // 这里需要认证，简化演示
                alert('任务管理系统功能:\n✓ 异步任务处理\n✓ 实时进度跟踪\n✓ 任务队列管理\n✓ 错误处理和重试\n\n注意: 完整测试需要用户认证');
            } catch (error) {
                alert('任务管理测试失败: ' + error.message);
            }
        }
        
        // 处理文件上传
        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const resultDiv = document.getElementById('demoResult');
            const fileType = file.type.startsWith('image/') ? '图片' : 
                           file.type.startsWith('audio/') ? '音频' : '未知';
            
            resultDiv.innerHTML = `
                <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; margin-top: 20px;">
                    <h4>📁 文件信息</h4>
                    <p><strong>文件名:</strong> ${file.name}</p>
                    <p><strong>文件类型:</strong> ${fileType}</p>
                    <p><strong>文件大小:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                    <p><strong>建议操作:</strong> ${getSuggestedOperation(file.type)}</p>
                    <p style="color: #ffd700; margin-top: 15px;">
                        💡 提示: 完整的文件处理功能需要用户登录和API认证
                    </p>
                </div>
            `;
        }
        
        // 获取建议的操作
        function getSuggestedOperation(fileType) {
            if (fileType.startsWith('image/')) {
                return '图片修复、增强、上色或背景移除';
            } else if (fileType.startsWith('audio/')) {
                return '语音克隆、音质增强或语言检测';
            } else {
                return '不支持的文件类型';
            }
        }
        
        // 页面加载时自动检查健康状态
        window.addEventListener('DOMContentLoaded', () => {
            checkHealth();
            
            // 每30秒自动刷新状态
            setInterval(checkHealth, 30000);
        });
    </script>
</body>
</html>
