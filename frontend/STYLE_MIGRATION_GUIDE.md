# 样式系统迁移指南

## 概述

为了解决CSS样式冲突和提供更精准的样式控制，我们引入了统一的样式系统。这个系统基于CSS变量，提供了一致的设计令牌和工具类。

## 🎯 解决的问题

1. **样式冲突**：多个文件定义相同的全局样式
2. **颜色不一致**：硬编码颜色值导致主题不统一
3. **维护困难**：样式分散在多个文件中
4. **设计不自由**：缺乏系统性的设计令牌

## 📁 新的文件结构

```
src/
├── styles/
│   └── unified-styles.css    # 统一样式系统
├── index.css                 # 全局样式入口
├── App.css                   # 应用级样式
└── components/
    ├── Home.css              # 组件样式（已清理）
    ├── Navbar.css            # 组件样式（已更新）
    └── ...
```

## 🎨 设计令牌系统

### 颜色系统

```css
/* 主色调 */
--color-primary: #4A90E2;           /* 宁静蓝 */
--color-primary-hover: #357ABD;     /* 宁静蓝悬停 */
--color-primary-light: #E3F2FD;     /* 宁静蓝浅色 */

/* 语义化颜色 */
--color-background: var(--color-gray-50);
--color-surface: #FFFFFF;
--color-text-primary: var(--color-gray-900);
--color-text-secondary: var(--color-gray-600);
```

### 间距系统

```css
--spacing-xs: 0.25rem;    /* 4px */
--spacing-sm: 0.5rem;     /* 8px */
--spacing-md: 1rem;       /* 16px */
--spacing-lg: 1.5rem;     /* 24px */
--spacing-xl: 2rem;       /* 32px */
```

### 字体系统

```css
--font-size-xs: 0.75rem;     /* 12px */
--font-size-sm: 0.875rem;    /* 14px */
--font-size-base: 1rem;      /* 16px */
--font-size-lg: 1.125rem;    /* 18px */
--font-size-xl: 1.25rem;     /* 20px */
```

## 🔧 迁移步骤

### 1. 替换硬编码颜色

**之前：**
```css
.my-component {
  background-color: #ffffff;
  color: #333333;
  border: 1px solid #eaeaea;
}
```

**之后：**
```css
.my-component {
  background-color: var(--color-surface);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border-light);
}
```

### 2. 使用统一的间距

**之前：**
```css
.my-component {
  padding: 16px;
  margin: 24px 0;
}
```

**之后：**
```css
.my-component {
  padding: var(--spacing-md);
  margin: var(--spacing-lg) 0;
}
```

### 3. 使用工具类

**之前：**
```css
.my-button {
  background-color: #4A90E2;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 500;
}
```

**之后：**
```html
<button class="btn btn-primary">
  按钮文本
</button>
```

### 4. 使用预定义组件样式

**卡片组件：**
```html
<div class="card">
  <div class="card-header">
    <h3>标题</h3>
  </div>
  <div class="card-body">
    <p>内容</p>
  </div>
</div>
```

**表单组件：**
```html
<div class="form-group">
  <label class="form-label">标签</label>
  <input class="form-input" type="text" placeholder="请输入...">
</div>
```

## 🎯 Z-index 管理

使用预定义的z-index层级：

```css
--z-dropdown: 1000;
--z-sticky: 1020;
--z-fixed: 1030;
--z-modal-backdrop: 1040;
--z-modal: 1050;
--z-popover: 1060;
--z-tooltip: 1070;
--z-toast: 1080;
```

**使用示例：**
```css
.my-modal {
  z-index: var(--z-modal);
}

.my-tooltip {
  z-index: var(--z-tooltip);
}
```

## 🌙 深色主题支持

系统自动支持深色主题，只需在HTML元素上添加`dark`类：

```html
<html class="dark">
```

所有使用CSS变量的组件都会自动适配深色主题。

## 📱 响应式设计

使用预定义的响应式工具类：

```html
<div class="text-center md:text-left lg:text-right">
  响应式文本对齐
</div>
```

## 🚀 最佳实践

### 1. 优先使用CSS变量
- ✅ `color: var(--color-primary);`
- ❌ `color: #4A90E2;`

### 2. 使用语义化的变量名
- ✅ `var(--color-text-primary)`
- ❌ `var(--color-gray-900)`

### 3. 利用工具类减少自定义CSS
- ✅ `class="p-4 mb-3 text-center"`
- ❌ 为每个组件写自定义padding、margin样式

### 4. 保持组件样式的独立性
- 使用类名前缀避免样式冲突
- 避免修改全局元素样式

### 5. 使用预定义的过渡动画
- ✅ `transition: all var(--transition-fast);`
- ❌ `transition: all 0.2s ease;`

## 🔍 调试技巧

### 1. 检查CSS变量值
在浏览器开发者工具中：
```javascript
getComputedStyle(document.documentElement).getPropertyValue('--color-primary')
```

### 2. 验证样式优先级
使用浏览器开发者工具的"Computed"面板查看最终应用的样式。

### 3. 检查深色主题
```javascript
// 切换深色主题
document.documentElement.classList.toggle('dark');
```

## 📋 迁移检查清单

- [ ] 移除重复的全局样式定义
- [ ] 替换硬编码颜色为CSS变量
- [ ] 更新z-index使用预定义值
- [ ] 使用统一的间距系统
- [ ] 采用预定义的字体大小
- [ ] 测试深色主题兼容性
- [ ] 验证响应式布局
- [ ] 检查样式优先级冲突

## 🆘 常见问题

### Q: 如何添加新的颜色？
A: 在`unified-styles.css`的`:root`部分添加新的CSS变量，并在深色主题中提供对应的值。

### Q: 工具类不够用怎么办？
A: 可以在`unified-styles.css`中添加新的工具类，遵循现有的命名规范。

### Q: 如何处理第三方组件的样式？
A: 使用CSS变量覆盖第三方组件的样式，或者创建包装器组件。

### Q: 样式还是不生效怎么办？
A: 检查CSS导入顺序，确保`unified-styles.css`在其他样式文件之前导入。

## 📚 参考资源

- [CSS自定义属性 (MDN)](https://developer.mozilla.org/zh-CN/docs/Web/CSS/--*)
- [设计系统最佳实践](https://designsystemsrepo.com/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)

---

通过遵循这个迁移指南，你可以：
- 🎯 获得更精准的样式控制
- 🔧 避免样式冲突
- 🎨 保持设计一致性
- 🚀 提高开发效率
- 🌙 轻松支持深色主题