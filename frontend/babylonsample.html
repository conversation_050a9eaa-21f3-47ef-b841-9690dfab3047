<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>道教仙境模拟</title>
    <style>
        /* HTML和Body的基本样式，确保画布能填充整个视口 */
        html, body {
            overflow: hidden; /* 防止滚动条 */
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            font-family: sans-serif; /* 默认字体 */
            background-color: #f0f0f0; /* 背景颜色 */
        }
        /* Babylon.js渲染画布的样式 */
        #renderCanvas {
            width: 100%;
            height: 100%;
            display: block; /* 移除默认内联元素的间隙 */
            touch-action: none; /* 禁用浏览器对触摸手势的默认处理，允许Babylon.js处理 */
        }
        /* 信息面板样式，提供用户交互提示 */
        #infoPanel {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.5); /* 半透明背景 */
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 0.9em;
            z-index: 10; /* 确保在画布之上 */
        }
    </style>
    <!-- Babylon.js 库文件 CDN 引用，通常指向最新稳定版本 (例如 5.x 或更高版本) -->
    <script src="https://cdn.babylonjs.com/babylon.js"></script>
    <!-- Babylon.js 附加库：用于加载模型，尽管本例主要使用内置几何体，但为了完整性可引用 -->
    <script src="https://cdn.babylonjs.com/loaders/babylonjs.loaders.min.js"></script>
    <!-- Babylon.js 附加库：用于生成程序纹理，如本例中的水面噪声纹理 -->
    <script src="https://cdn.babylonjs.com/proceduralTexturesLibrary/babylonjs.proceduralTextures.min.js"></script>
</head>
<body>
    <!-- Babylon.js 渲染画布 -->
    <canvas id="renderCanvas"></canvas>
    <!-- 信息面板，提供用户交互提示 -->
    <div id="infoPanel">
        <p>道教仙境模拟场景</p>
        <p>使用鼠标或触摸屏旋转、缩放视角</p>
    </div>
    <script>
        // 创建Babylon.js场景的主函数
        const createScene = async (canvas, engine) => {
            // 创建一个新的场景对象
            const scene = new BABYLON.Scene(engine);
            // 设置场景的初始清除颜色（背景色），在有雾和环境光的情况下最终可能不明显
            scene.clearColor = new BABYLON.Color3(0.0, 0.0, 0.0);

            // --- 摄像机设置 ---
            // ArcRotateCamera: 允许围绕目标旋转，适合探索固定场景
            // 参数: name, alpha (水平旋转), beta (垂直旋转), radius (距离目标), target, scene
            const camera = new BABYLON.ArcRotateCamera("camera", BABYLON.Tools.ToRadians(120), BABYLON.Tools.ToRadians(80), 30, BABYLON.Vector3.Zero(), scene);
            camera.attachControl(canvas, true); // 将摄像机控制附加到画布
            camera.lowerRadiusLimit = 10; // 限制最小缩放距离
            camera.upperRadiusLimit = 100; // 限制最大缩放距离
            camera.wheelPrecision = 0.5; // 调整滚轮缩放速度
            camera.target = new BABYLON.Vector3(0, 5, 0); // 焦点略高于地面，指向庙宇区域

            // --- 灯光设置 ---
            // 半球光 (HemisphericLight): 代表来自上方 (天空) 和下方 (地面) 的环境光
            // 模拟环境中普遍的漫射光，适合森林等环境的氛围
            const hemiLight = new BABYLON.HemisphericLight("hemiLight", new BABYLON.Vector3(0, 1, 0), scene);
            hemiLight.intensity = 0.8; // 整体亮度
            hemiLight.diffuse = new BABYLON.Color3(0.6, 0.7, 0.6); // 森林般的绿色环境光
            hemiLight.specular = new BABYLON.Color3(0.1, 0.1, 0.1); // 减少镜面高光
            hemiLight.groundColor = new BABYLON.Color3(0.3, 0.4, 0.3); // 地面反射颜色

            // 定向光 (DirectionalLight): 代表太阳光，来自特定方向
            // 用于投射阴影，营造阳光照射感
            const dirLight = new BABYLON.DirectionalLight("dirLight", new BABYLON.Vector3(-1, -1, 1), scene);
            dirLight.intensity = 0.7; // 直射光亮度
            dirLight.position = new BABYLON.Vector3(20, 30, -20); // 光源位置
            dirLight.diffuse = new BABYLON.Color3(0.9, 0.8, 0.7); // 温暖的阳光颜色
            dirLight.specular = new BABYLON.Color3(0.5, 0.5, 0.5); // 镜面高光颜色

            // --- 阴影生成器 ---
            // 从定向光生成阴影
            const shadowGenerator = new BABYLON.ShadowGenerator(1024, dirLight);
            shadowGenerator.useBlurExponentialShadowMap = true; // 柔化阴影边缘
            shadowGenerator.blurKernel = 32; // 模糊核大小

            // --- 场景雾效，营造迷蒙氛围 ---
            // 模拟雾气或薄雾，使远处的物体逐渐模糊
            scene.fogMode = BABYLON.Scene.FOGMODE_EXP; // 指数雾模式
            scene.fogDensity = 0.02; // 雾的密度
            scene.fogColor = new BABYLON.Color3(0.6, 0.7, 0.6); // 偏绿灰色调的雾颜色

            // --- 材质定义 ---
            // 可复用的场景元素材质
            const mossMaterial = new BABYLON.StandardMaterial("mossMat", scene);
            mossMaterial.diffuseColor = new BABYLON.Color3(0.2, 0.4, 0.2); // 深绿色
            mossMaterial.specularColor = new BABYLON.Color3(0, 0, 0); // 无光泽

            const stoneMaterial = new BABYLON.StandardMaterial("stoneMat", scene);
            stoneMaterial.diffuseColor = new BABYLON.Color3(0.4, 0.4, 0.4); // 灰色石头
            stoneMaterial.specularColor = new BABYLON.Color3(0.1, 0.1, 0.1);

            const woodMaterial = new BABYLON.StandardMaterial("woodMat", scene);
            woodMaterial.diffuseColor = new BABYLON.Color3(0.3, 0.2, 0.1); // 深棕色木材
            woodMaterial.specularColor = new BABYLON.Color3(0, 0, 0);

            const bambooMaterial = new BABYLON.StandardMaterial("bambooMat", scene);
            bambooMaterial.diffuseColor = new BABYLON.Color3(0.3, 0.5, 0.3); // 竹子绿色
            bambooMaterial.specularColor = new BABYLON.Color3(0.1, 0.1, 0.1);

            // 水面材质 (简化版)
            const waterMaterial = new BABYLON.StandardMaterial("waterMat", scene);
            waterMaterial.diffuseColor = new BABYLON.Color3(0.1, 0.3, 0.4); // 深绿蓝色
            waterMaterial.alpha = 0.8; // 透明度
            waterMaterial.specularColor = new BABYLON.Color3(0.8, 0.8, 0.8); // 镜面反射

            // 使用程序噪声纹理模拟水面波纹
            const noiseTexture = new BABYLON.NoiseProceduralTexture("noise", 256, scene);
            noiseTexture.animationSpeedFactor = 10; // 动画速度
            noiseTexture.brightness = 0.5; // 亮度
            noiseTexture.octaves = 4; // 细节层次
            waterMaterial.diffuseTexture = noiseTexture; // 将噪声纹理作为漫射纹理
            waterMaterial.bumpTexture = noiseTexture; // 将噪声纹理作为凹凸贴图，增加波纹感
            waterMaterial.freeze(); // 优化材质，提高性能

            // --- 地面 ---
            // 创建一个大地平面
            const ground = BABYLON.MeshBuilder.CreateGround("ground", { width: 100, height: 100, subdivisions: 2 }, scene);
            ground.material = mossMaterial; // 地面使用苔藓材质
            ground.receiveShadows = true; // 地面接收阴影

            // --- 小溪 ---
            // 通过多个点创建一条弯曲的路径，然后用Ribbon Mesh生成小溪
            const streamPath = [
                new BABYLON.Vector3(-30, 0.05, -10),
                new BABYLON.Vector3(-20, 0.05, -5),
                new BABYLON.Vector3(-10, 0.05, 0),
                new BABYLON.Vector3(0, 0.05, -5),
                new BABYLON.Vector3(10, 0.05, -10),
                new BABYLON.Vector3(15, 0.05, -5),
                new BABYLON.Vector3(20, 0.05, 0)
            ];
            const streamWidth = 3; // 小溪宽度
            // 使用Ribbon Mesh创建小溪的表面，通过两条平行的路径形成曲面
            const stream = BABYLON.MeshBuilder.CreateRibbon("stream", { pathArray: [streamPath.map(p => new BABYLON.Vector3(p.x, p.y, p.z - streamWidth/2)), streamPath.map(p => new BABYLON.Vector3(p.x, p.y, p.z + streamWidth/2))], closeArray: false, closePath: false }, scene);
            stream.material = waterMaterial; // 为小溪分配水面材质
            stream.position.y = 0.1; // 略高于地面
            stream.receiveShadows = true; // 小溪接收阴影

            // 动画水流效果 (通过动画纹理的偏移量)
            scene.registerBeforeRender(() => {
                if (noiseTexture) {
                    noiseTexture.vOffset += 0.0005 * scene.getAnimationRatio(); // 垂直方向偏移
                    noiseTexture.uOffset += 0.0002 * scene.getAnimationRatio(); // 水平方向偏移
                }
            });

            // --- 小溪中的石头 ---
            // 定义一些石头的放置位置
            const rockPositions = [
                new BABYLON.Vector3(-25, 0.02, -8),
                new BABYLON.Vector3(-15, 0.02, -2),
                new BABYLON.Vector3(5, 0.02, -8),
                new BABYLON.Vector3(12, 0.02, -2),
                new BABYLON.Vector3(0, 0.02, 3), // 靠近庙宇岸边
                new BABYLON.Vector3(-5, 0.02, -8), // 靠近庙宇岸边
            ];
            rockPositions.forEach(pos => {
                // 创建球体作为石头，直径随机变化
                const rock = BABYLON.MeshBuilder.CreateSphere("rock", { diameter: Math.random() * 0.8 + 0.5, segments: 8 }, scene);
                rock.position = pos;
                rock.material = stoneMaterial; // 石头使用石头材质
                rock.receiveShadows = true; // 石头接收阴影
                shadowGenerator.addShadowCaster(rock); // 石头投射阴影
            });

            // --- 庙宇建模 (简化版) ---
            // 主体结构
            const templeBase = BABYLON.MeshBuilder.CreateBox("templeBase", { width: 10, height: 8, depth: 10 }, scene);
            templeBase.position = new BABYLON.Vector3(0, 4, 0); // 居中并抬高
            templeBase.material = stoneMaterial; // 基础使用石头材质
            shadowGenerator.addShadowCaster(templeBase); // 主体投射阴影

            // 苔藓外层 (简单覆盖)
            const mossLayer = BABYLON.MeshBuilder.CreateBox("mossLayer", { width: 10.2, height: 8.2, depth: 10.2 }, scene); // 略大于基础，形成覆盖感
            mossLayer.position = new BABYLON.Vector3(0, 4, 0);
            mossLayer.material = mossMaterial; // 使用苔藓材质
            mossLayer.scaling.y = 0.9; // 略微压扁，露出底部边缘
            mossLayer.parent = templeBase; // 设为主体的子对象，随主体移动

            // 台阶创建函数
            const createStep = (name, width, height, depth, x, y, z) => {
                const step = BABYLON.MeshBuilder.CreateBox(name, { width: width, height: height, depth: depth }, scene);
                step.position = new BABYLON.Vector3(x, y, z);
                step.material = mossMaterial; // 台阶使用苔藓材质
                shadowGenerator.addShadowCaster(step); // 台阶投射阴影
                return step;
            };

            // 正面台阶
            let currentY = 0;
            let currentZ = templeBase.position.z + templeBase.depth / 2 + 0.5; // 从庙宇正面稍远处开始
            for (let i = 0; i < 4; i++) {
                currentY += 0.8; // 每级台阶高度
                currentZ += 1.5; // 每级台阶深度
                const step = createStep(`step${i}`, 8 - i * 0.8, 0.7, 1.5, templeBase.position.x, currentY - templeBase.height / 2 + 0.1, currentZ);
                step.receiveShadows = true; // 台阶接收阴影
            }

            // 屋顶 (简化分层)
            const createRoofLayer = (name, width, depth, height, yPos, parent) => {
                const roof = BABYLON.MeshBuilder.CreateBox(name, { width: width, height: height, depth: depth }, scene);
                roof.position.y = yPos;
                roof.material = woodMaterial; // 屋顶使用木材材质
                shadowGenerator.addShadowCaster(roof); // 屋顶投射阴影
                roof.parent = parent; // 设为主体的子对象
                return roof;
            };

            const roofBase = createRoofLayer("roofBase", 12, 12, 1, templeBase.height / 2, templeBase);
            const roofMid = createRoofLayer("roofMid", 10, 10, 1, roofBase.position.y + 0.8, templeBase);
            const roofTop = createRoofLayer("roofTop", 8, 8, 1, roofMid.position.y + 0.8, templeBase);
            // 宝塔状顶部
            const pagodaPeak = BABYLON.MeshBuilder.CreateCylinder("pagodaPeak", { height: 2, diameterTop: 0.5, diameterBottom: 1.5, tessellation: 4 }, scene); // 四棱锥效果
            pagodaPeak.position.y = roofTop.position.y + 1.5;
            pagodaPeak.material = woodMaterial;
            shadowGenerator.addShadowCaster(pagodaPeak);
            pagodaPeak.parent = templeBase;


            // --- 竹林 ---
            // 创建一个竹子模型
            const createBamboo = (x, z) => {
                const height = Math.random() * 15 + 10; // 随机高度
                const diameter = Math.random() * 0.4 + 0.2; // 随机粗细
                const bamboo = BABYLON.MeshBuilder.CreateCylinder("bamboo", { height: height, diameter: diameter, tessellation: 8 }, scene);
                bamboo.position = new BABYLON.Vector3(x, height / 2, z); // 调整位置使底部在地面上
                bamboo.material = bambooMaterial; // 竹子使用竹子材质
                bamboo.rotation.y = Math.random() * Math.PI * 2; // 随机Y轴旋转
                shadowGenerator.addShadowCaster(bamboo); // 竹子投射阴影
                bamboo.receiveShadows = true; // 竹子接收阴影
                return bamboo;
            };

            // 大量生成竹子以形成密集的竹林
            const forestRadius = 40; // 竹林分布半径
            const bambooCount = 500; // 竹子数量，数量越多越密集
            for (let i = 0; i < bambooCount; i++) {
                let x, z;
                // 确保竹子不会放置在庙宇或小溪太近的地方，避免穿透
                do {
                    x = (Math.random() * 2 - 1) * forestRadius;
                    z = (Math.random() * 2 - 1) * forestRadius;
                } while (BABYLON.Vector3.Distance(new BABYLON.Vector3(x, 0, z), templeBase.position) < 15 && BABYLON.Vector3.Distance(new BABYLON.Vector3(x, 0, z), stream.position) < 10);
                createBamboo(x, z);
            }

            // --- 经幡 (简化版) ---
            const createFlag = (name, color, x, y, z, rotationY) => {
                const flag = BABYLON.MeshBuilder.CreatePlane(name, { width: 3, height: 1.5 }, scene); // 平面作为旗帜
                flag.position = new BABYLON.Vector3(x, y, z);
                flag.rotation.y = rotationY; // 旋转方向
                const flagMat = new BABYLON.StandardMaterial(`${name}Mat`, scene);
                flagMat.diffuseColor = color; // 旗帜颜色
                flag.material = flagMat;
                shadowGenerator.addShadowCaster(flag); // 旗帜投射阴影
                return flag;
            };

            // 旗帜颜色数组
            const flagColors = [
                new BABYLON.Color3(1, 0.2, 0.2), // 红色
                new BABYLON.Color3(0.2, 0.2, 1), // 蓝色
                new BABYLON.Color3(0.2, 1, 0.2), // 绿色
                new BABYLON.Color3(1, 1, 0.2), // 黄色
                new BABYLON.Color3(0.8, 0.2, 1)  // 紫色
            ];

            // 旗帜放置位置
            const flagPositions = [
                { x: -5, y: 15, z: 8, ry: Math.PI / 4 },
                { x: -8, y: 14, z: 5, ry: Math.PI / 2 },
                { x: -10, y: 16, z: 0, ry: Math.PI / 3 },
                { x: 0, y: 17, z: -10, ry: -Math.PI / 4 },
                { x: 5, y: 15, z: -12, ry: -Math.PI / 6 },
                { x: 10, y: 14, z: -5, ry: -Math.PI / 2 }
            ];

            flagPositions.forEach((pos, index) => {
                createFlag(`flag${index}`, flagColors[index % flagColors.length], pos.x, pos.y, pos.z, pos.ry);
            });


            // --- 香炉/烛台 (简化版) ---
            const createBurner = (x, y, z) => {
                // 底座和碗状结构
                const base = BABYLON.MeshBuilder.CreateCylinder("burnerBase", { height: 1.5, diameterBottom: 1.2, diameterTop: 0.8, tessellation: 16 }, scene);
                const bowl = BABYLON.MeshBuilder.CreateCylinder("burnerBowl", { height: 0.5, diameterTop: 1.5, diameterBottom: 1.2, tessellation: 16 }, scene);
                bowl.parent = base; // 碗作为底座的子对象
                bowl.position.y = 0.75; // 碗放置在底座顶部

                base.position = new BABYLON.Vector3(x, y, z);
                base.material = stoneMaterial; // 香炉使用石头材质
                shadowGenerator.addShadowCaster(base); // 香炉投射阴影
                shadowGenerator.addShadowCaster(bowl);

                // 添加火焰/发光点 (发光球体)
                const flame = BABYLON.MeshBuilder.CreateSphere("flame", { diameter: 0.3 }, scene);
                flame.position.y = y + 1.7; // 放置在香炉上方
                flame.position.x = x;
                flame.position.z = z;
                const flameMat = new BABYLON.StandardMaterial("flameMat", scene);
                flameMat.emissiveColor = new BABYLON.Color3(1, 0.5, 0); // 橙色自发光，模拟火焰光效
                flameMat.diffuseColor = new BABYLON.Color3(1, 0.5, 0);
                flame.material = flameMat;
                return base;
            };

            // 在庙宇台阶附近放置香炉
            createBurner(3, 0.75, 4);
            createBurner(-3, 0.75, 4);
            createBurner(5, 0.75, 2);
            createBurner(-5, 0.75, 2);

            return scene; // 返回创建好的场景
        };

        // --- 主执行块 ---
        // 获取渲染画布元素
        const canvas = document.getElementById("renderCanvas");
        // 创建Babylon.js引擎实例，第二个参数true表示使用抗锯齿
        const engine = new BABYLON.Engine(canvas, true, { preserveDrawingBuffer: true, stencil: true });

        // 调用createScene函数来构建场景
        let scene;
        // 确保窗口加载完成后才创建场景并开始渲染循环
        window.onload = async () => {
            scene = await createScene(canvas, engine);

            // 注册一个渲染循环，反复渲染场景
            engine.runRenderLoop(() => {
                if (scene) {
                    scene.render();
                }
            });
        };

        // 监听浏览器/画布尺寸变化事件，并通知引擎调整渲染尺寸
        window.addEventListener("resize", () => {
            engine.resize();
        });
    </script>
</body>
</html>