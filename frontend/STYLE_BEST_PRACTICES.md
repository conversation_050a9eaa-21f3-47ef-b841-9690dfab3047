# 样式最佳实践指南

本指南旨在帮助开发团队建立统一的样式规范，确保代码的可维护性和一致性。

## 📋 目录

- [核心原则](#核心原则)
- [CSS变量使用](#css变量使用)
- [组件样式规范](#组件样式规范)
- [响应式设计](#响应式设计)
- [性能优化](#性能优化)
- [可访问性](#可访问性)
- [调试技巧](#调试技巧)
- [常见问题](#常见问题)

## 🎯 核心原则

### 1. 统一样式系统

**✅ 推荐做法：**
```css
/* 使用CSS变量 */
.button {
  background-color: var(--color-primary);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
}
```

**❌ 避免做法：**
```css
/* 硬编码值 */
.button {
  background-color: #3b82f6;
  padding: 16px;
  border-radius: 8px;
}
```

### 2. 组件级样式隔离

**✅ 推荐做法：**
```css
/* 使用组件前缀 */
.navbar {
  /* 组件根样式 */
}

.navbar__logo {
  /* 子元素样式 */
}

.navbar__link {
  /* 子元素样式 */
}

.navbar__link--active {
  /* 修饰符样式 */
}
```

**❌ 避免做法：**
```css
/* 全局选择器污染 */
body {
  font-family: 'Custom Font';
}

a {
  color: blue;
}
```

### 3. 语义化类名

**✅ 推荐做法：**
```css
.hero-section { }
.product-card { }
.user-avatar { }
.navigation-menu { }
```

**❌ 避免做法：**
```css
.red-text { }
.big-box { }
.left-thing { }
```

## 🎨 CSS变量使用

### 颜色系统

```css
/* 主色调 */
.primary-button {
  background-color: var(--color-primary);
  color: var(--color-on-primary);
}

.primary-button:hover {
  background-color: var(--color-primary-dark);
}

/* 语义化颜色 */
.error-message {
  color: var(--color-error);
  background-color: var(--color-error-light);
}
```

### 间距系统

```css
/* 统一间距 */
.card {
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  gap: var(--spacing-sm);
}

/* 响应式间距 */
.container {
  padding: var(--spacing-md);
}

@media (min-width: 768px) {
  .container {
    padding: var(--spacing-lg);
  }
}
```

### 字体系统

```css
/* 标题层级 */
.heading-1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
}

.heading-2 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-snug);
}

/* 正文文本 */
.body-text {
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
}
```

## 🧩 组件样式规范

### BEM命名规范

```css
/* Block（块） */
.card { }

/* Element（元素） */
.card__header { }
.card__body { }
.card__footer { }

/* Modifier（修饰符） */
.card--featured { }
.card--compact { }
.card__header--dark { }
```

### 组件状态管理

```css
/* 基础状态 */
.button {
  /* 默认样式 */
}

/* 交互状态 */
.button:hover {
  /* 悬停样式 */
}

.button:focus {
  /* 焦点样式 */
}

.button:active {
  /* 激活样式 */
}

/* 功能状态 */
.button:disabled {
  /* 禁用样式 */
}

.button--loading {
  /* 加载样式 */
}
```

### 组件变体

```css
/* 基础按钮 */
.btn {
  /* 共同样式 */
}

/* 样式变体 */
.btn--primary { }
.btn--secondary { }
.btn--outline { }
.btn--ghost { }

/* 尺寸变体 */
.btn--sm { }
.btn--md { }
.btn--lg { }
```

## 📱 响应式设计

### 移动优先

```css
/* 移动端样式（默认） */
.navigation {
  flex-direction: column;
  padding: var(--spacing-sm);
}

/* 平板端 */
@media (min-width: 768px) {
  .navigation {
    flex-direction: row;
    padding: var(--spacing-md);
  }
}

/* 桌面端 */
@media (min-width: 1024px) {
  .navigation {
    padding: var(--spacing-lg);
  }
}
```

### 断点管理

```css
/* 使用CSS变量定义断点 */
:root {
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
}

/* 容器查询（现代浏览器） */
@container (min-width: 300px) {
  .card {
    display: grid;
    grid-template-columns: 1fr 2fr;
  }
}
```

### 流体布局

```css
/* 流体字体大小 */
.heading {
  font-size: clamp(1.5rem, 4vw, 3rem);
}

/* 流体间距 */
.section {
  padding: clamp(2rem, 5vw, 4rem) 0;
}

/* 流体容器 */
.container {
  width: min(100% - 2rem, 1200px);
  margin-inline: auto;
}
```

## ⚡ 性能优化

### CSS优化

```css
/* 避免昂贵的属性 */
/* ❌ 避免 */
.element {
  box-shadow: 0 0 10px rgba(0,0,0,0.5);
  filter: blur(5px);
  transform: rotate(45deg) scale(1.2);
}

/* ✅ 优化后 */
.element {
  box-shadow: var(--shadow-md); /* 预定义阴影 */
  will-change: transform; /* 提示浏览器优化 */
}

.element:hover {
  transform: translateY(-2px); /* 简单变换 */
}
```

### 动画优化

```css
/* 使用transform和opacity进行动画 */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-in.active {
  opacity: 1;
  transform: translateY(0);
}

/* 使用will-change提示 */
.animated-element {
  will-change: transform;
}

.animated-element.animation-complete {
  will-change: auto; /* 动画完成后移除 */
}
```

### 关键CSS

```css
/* 关键路径CSS - 内联在HTML中 */
.hero {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 非关键CSS - 异步加载 */
.footer {
  /* 页脚样式 */
}
```

## ♿ 可访问性

### 颜色对比度

```css
/* 确保足够的对比度 */
.text-primary {
  color: var(--color-text); /* 4.5:1 对比度 */
}

.text-secondary {
  color: var(--color-text-muted); /* 至少 3:1 对比度 */
}

/* 不仅依赖颜色传达信息 */
.error-field {
  border-color: var(--color-error);
  border-width: 2px; /* 视觉指示 */
}

.error-field::after {
  content: "❌"; /* 图标指示 */
}
```

### 焦点管理

```css
/* 清晰的焦点指示 */
.interactive-element:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 跳过链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary);
  color: var(--color-on-primary);
  padding: 8px;
  text-decoration: none;
  z-index: var(--z-skip-link);
}

.skip-link:focus {
  top: 6px;
}
```

### 减少动画

```css
/* 尊重用户的动画偏好 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 提供静态替代方案 */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }
  
  .loading-spinner::after {
    content: "加载中...";
  }
}
```

## 🐛 调试技巧

### 使用调试工具

```css
/* 开发环境调试 */
@import './debug-styles.css';

/* 调试类已在 debug-styles.css 中定义 */
```

### 样式冲突检测

```bash
# 运行样式检测脚本
npm run style:check

# 分析样式问题
npm run style:analyze
```

### 浏览器开发工具

```css
/* 使用CSS注释标记重要样式 */
.critical-component {
  /* CRITICAL: 这个样式影响首屏渲染 */
  display: flex;
}

.performance-sensitive {
  /* PERFORMANCE: 这个动画可能影响性能 */
  transform: translateX(0);
}
```

## ❓ 常见问题

### Q: 如何处理样式优先级冲突？

**A:** 使用CSS特异性而不是 `!important`

```css
/* ❌ 避免 */
.button {
  color: red !important;
}

/* ✅ 推荐 */
.navbar .button {
  color: var(--color-primary);
}

/* 或使用CSS层级 */
@layer components {
  .button {
    color: var(--color-primary);
  }
}
```

### Q: 如何组织大型项目的CSS？

**A:** 使用模块化结构

```
src/styles/
├── base/
│   ├── reset.css
│   ├── typography.css
│   └── variables.css
├── components/
│   ├── button.css
│   ├── card.css
│   └── modal.css
├── layouts/
│   ├── grid.css
│   └── containers.css
├── utilities/
│   ├── spacing.css
│   └── display.css
└── themes/
    ├── light.css
    └── dark.css
```

### Q: 如何确保样式的一致性？

**A:** 建立设计系统和代码审查流程

1. 使用统一的CSS变量
2. 建立组件库
3. 使用样式检测工具
4. 定期进行代码审查
5. 编写样式文档

### Q: 如何处理暗色模式？

**A:** 使用CSS变量和媒体查询

```css
:root {
  --color-background: #ffffff;
  --color-text: #000000;
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: #000000;
    --color-text: #ffffff;
  }
}

/* 手动切换 */
[data-theme="dark"] {
  --color-background: #000000;
  --color-text: #ffffff;
}
```

## 📚 相关资源

- [统一样式系统文档](./STYLE_MIGRATION_GUIDE.md)
- [CSS变量参考](./src/styles/unified-styles.css)
- [调试工具使用](./src/styles/debug-styles.css)
- [样式检测脚本](./scripts/check-style-conflicts.js)

## 🔄 更新日志

- **v1.0.0** - 初始版本，建立基础规范
- **v1.1.0** - 添加调试工具和性能优化指南
- **v1.2.0** - 增加可访问性和响应式设计最佳实践

---

**记住：好的CSS不仅仅是让页面看起来正确，更要确保代码的可维护性、性能和可访问性。**