# CSS Modules 样式作用域隔离指南

本指南介绍如何在项目中使用 CSS Modules 实现真正的样式作用域隔离，结合 Tailwind CSS 的最佳实践。

## 📋 目录

- [什么是 CSS Modules](#什么是-css-modules)
- [配置说明](#配置说明)
- [使用方法](#使用方法)
- [最佳实践](#最佳实践)
- [迁移指南](#迁移指南)
- [常见问题](#常见问题)

## 🎯 什么是 CSS Modules

CSS Modules 是一种 CSS 文件的编写方式，它能够：

- **作用域隔离**：每个组件的样式都有独立的作用域
- **避免命名冲突**：自动生成唯一的类名
- **可预测性**：样式只影响当前组件
- **可维护性**：组件样式与组件代码紧密关联

### 传统 CSS vs CSS Modules

```css
/* 传统 CSS - 全局作用域 */
.button {
  background: blue;
}

/* CSS Modules - 局部作用域 */
.button {
  background: blue; /* 编译后变成 .Button__button___2x3kl */
}
```

## ⚙️ 配置说明

### Vite 配置

项目已配置 CSS Modules 支持：

```typescript
// vite.config.ts
export default defineConfig({
  css: {
    modules: {
      localsConvention: 'camelCaseOnly', // 支持驼峰命名
      generateScopedName: '[name]__[local]___[hash:base64:5]', // 生成唯一类名
      hashPrefix: 'memorial', // 哈希前缀
    }
  }
})
```

### 文件命名约定

- **CSS Modules 文件**：`*.module.css`
- **普通 CSS 文件**：`*.css`
- **全局样式文件**：`global.css` 或在文件中使用 `:global()`

## 🚀 使用方法

### 1. 创建 CSS Modules 文件

```css
/* Button.module.css */
.button {
  @apply inline-flex items-center justify-center;
  @apply px-4 py-2 text-sm font-medium;
  @apply border border-transparent rounded-md;
  @apply transition-colors duration-200;
}

.primary {
  @apply bg-primary-500 text-white;
  @apply hover:bg-primary-600;
}

.secondary {
  @apply bg-secondary-500 text-white;
  @apply hover:bg-secondary-600;
}
```

### 2. 在组件中使用

```tsx
// Button.tsx
import React from 'react';
import styles from './Button.module.css';
import { clsx } from 'clsx';

interface ButtonProps {
  variant?: 'primary' | 'secondary';
  children: React.ReactNode;
  className?: string;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  children,
  className,
  ...props
}) => {
  return (
    <button
      className={clsx(
        styles.button,
        styles[variant],
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
};
```

### 3. 组合多个类名

```tsx
// 使用 clsx 组合类名
const buttonClasses = clsx(
  styles.button, // 基础样式
  styles[variant], // 变体样式
  {
    [styles.loading]: loading, // 条件样式
    [styles.disabled]: disabled,
  },
  className // 外部传入的类名
);
```

## 📁 项目结构

推荐的组件文件结构：

```
src/components/
├── Button/
│   ├── Button.tsx          # 组件逻辑
│   ├── Button.module.css   # 组件样式
│   ├── Button.test.tsx     # 组件测试
│   └── index.ts           # 导出文件
├── Card/
│   ├── Card.tsx
│   ├── Card.module.css
│   └── index.ts
└── ...
```

## 🎨 结合 Tailwind CSS

### 使用 @apply 指令

```css
/* 推荐：使用 @apply 复用 Tailwind 类 */
.button {
  @apply inline-flex items-center justify-center;
  @apply px-4 py-2 text-sm font-medium rounded-md;
  @apply transition-colors duration-200;
}

/* 避免：直接写 CSS 属性 */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  /* ... */
}
```

### 响应式设计

```css
.container {
  @apply w-full px-4;
  @apply sm:px-6 md:px-8;
  @apply max-w-sm sm:max-w-md lg:max-w-lg;
}
```

### 暗色模式支持

```css
.card {
  @apply bg-white text-gray-900;
}

/* 全局暗色模式 */
:global(.dark) .card {
  @apply bg-gray-800 text-gray-100;
}
```

## 🏆 最佳实践

### 1. 命名约定

```css
/* 使用语义化的类名 */
.button { } /* ✅ 好 */
.btn { }    /* ❌ 避免缩写 */

.primary { }   /* ✅ 好 */
.blue { }      /* ❌ 避免颜色名 */

.loading { }   /* ✅ 好 */
.isLoading { } /* ❌ 避免 is 前缀 */
```

### 2. 组件变体

```css
/* 基础样式 */
.button {
  @apply inline-flex items-center justify-center;
  /* 共同样式 */
}

/* 变体样式 */
.primary {
  @apply bg-primary-500 text-white;
}

.secondary {
  @apply bg-gray-500 text-white;
}

.outline {
  @apply bg-transparent border-2 border-primary-500;
}
```

### 3. 状态管理

```css
/* 状态样式 */
.button:hover {
  @apply transform scale-105;
}

.button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.loading {
  @apply relative;
}

.loading::after {
  content: '';
  @apply absolute inset-0 bg-current opacity-20;
}
```

### 4. 组合使用

```tsx
// 组件内部
const classes = clsx(
  styles.button,
  styles[variant],
  {
    [styles.loading]: loading,
    [styles.fullWidth]: fullWidth,
  },
  className
);

// 使用组件
<Button 
  variant="primary" 
  loading={isSubmitting}
  className="mt-4" // 外部样式
>
  提交
</Button>
```

## 🔄 迁移指南

### 从普通 CSS 迁移

1. **重命名文件**：`Component.css` → `Component.module.css`
2. **更新导入**：
   ```tsx
   // 之前
   import './Component.css';
   
   // 之后
   import styles from './Component.module.css';
   ```
3. **更新类名使用**：
   ```tsx
   // 之前
   <div className="button primary">
   
   // 之后
   <div className={clsx(styles.button, styles.primary)}>
   ```

### 渐进式迁移策略

1. **新组件**：直接使用 CSS Modules
2. **现有组件**：逐步迁移，优先迁移复杂组件
3. **全局样式**：保持现有的全局样式文件

## 🔧 工具支持

### TypeScript 支持

创建类型声明文件：

```typescript
// src/types/css-modules.d.ts
declare module '*.module.css' {
  const classes: { [key: string]: string };
  export default classes;
}
```

### VS Code 插件

推荐安装：
- **CSS Modules**: 提供智能提示
- **Tailwind CSS IntelliSense**: Tailwind 类名提示

## ❓ 常见问题

### Q: 如何使用全局样式？

```css
/* 在 CSS Modules 文件中使用全局样式 */
:global(.global-class) {
  /* 全局样式 */
}

/* 或者在普通 CSS 文件中定义全局样式 */
```

### Q: 如何调试生成的类名？

在开发环境中，生成的类名包含原始类名，便于调试：
```
.Button__primary___2x3kl
```

### Q: 如何与第三方组件库结合？

```tsx
// 使用 className 属性传递样式
<ThirdPartyComponent className={styles.customStyle} />

// 或者使用 CSS 变量
<ThirdPartyComponent style={{ '--custom-color': 'red' }} />
```

### Q: 性能影响如何？

CSS Modules 在构建时处理，运行时性能影响微乎其微。实际上由于避免了样式冲突，可能会提升性能。

## 📚 参考资源

- [CSS Modules 官方文档](https://github.com/css-modules/css-modules)
- [Vite CSS Modules 配置](https://vitejs.dev/config/shared-options.html#css-modules)
- [clsx 文档](https://github.com/lukeed/clsx)
- [Tailwind CSS @apply 指令](https://tailwindcss.com/docs/functions-and-directives#apply)

---

通过 CSS Modules，我们实现了真正的样式作用域隔离，同时保持了 Tailwind CSS 的便利性和一致性。这种方案既解决了样式冲突问题，又提供了良好的开发体验。