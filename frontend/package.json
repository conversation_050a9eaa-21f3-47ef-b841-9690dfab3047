{"name": "frontend", "private": true, "version": "0.0.0", "description": "基于3D技术的多宗教兼容祭祀网站", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,css,html}\"", "typecheck": "tsc --noEmit > typecheck_errors.log 2>&1", "style:check": "node scripts/check-style-conflicts.js", "style:fix": "node scripts/fix-hardcoded-colors.js", "style:analyze": "npm run style:check", "dev:full": "npm run style:check && npm run dev", "tailwind:watch": "vite build --watch --mode development", "tailwind:build": "vite build --mode production", "css:purge": "purgecss --css dist/**/*.css --content src/**/*.{html,js,ts,jsx,tsx} --output dist/", "css:modules:migrate": "node scripts/migrate-to-css-modules.js", "css:modules:demo": "echo 'CSS Modules 演示页面: http://localhost:4001/component-demo'", "dev:debug": "TAILWIND_MODE=watch npm run dev", "build:analyze": "npm run build && npm run css:analyze", "css:analyze": "echo 'CSS文件大小分析:' && find dist -name '*.css' -exec wc -c {} + | sort -n"}, "dependencies": {"@babylonjs/core": "^8.11.0", "@babylonjs/gui": "^8.11.0", "@babylonjs/inspector": "^8.11.0", "@babylonjs/loaders": "^8.11.0", "@babylonjs/materials": "^8.11.0", "@dimforge/rapier3d-compat": "^0.16.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.2.3", "@heroicons/react": "^2.2.0", "@stripe/stripe-js": "^7.3.1", "@types/crypto-js": "^4.2.2", "axios": "^1.9.0", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "framer-motion": "^11.18.2", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "react": "^19.1.0", "react-babylonjs": "3.2.5-beta.2", "react-bootstrap": "^2.10.10", "react-dom": "^19.1.0", "react-i18next": "^14.1.3", "react-router-dom": "^7.6.0", "tailwind-merge": "^3.3.0", "web-vitals": "^3.5.2", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.17.0", "@tailwindcss/postcss": "^4.0.0", "@types/node": "^22.15.29", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "glob": "^11.0.0", "globals": "^15.14.0", "postcss": "^8.5.0", "prettier": "^3.4.2", "purgecss": "^6.0.0", "tailwindcss": "^4.0.0", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}