# Tailwind CSS 使用指南

本指南将帮助你充分利用项目中配置的 Tailwind CSS 功能。

## 📋 目录

- [配置概览](#配置概览)
- [色彩系统](#色彩系统)
- [组件库](#组件库)
- [响应式设计](#响应式设计)
- [暗色模式](#暗色模式)
- [动画系统](#动画系统)
- [最佳实践](#最佳实践)
- [性能优化](#性能优化)

## 🎨 配置概览

### 扩展的设计系统

我们的 Tailwind 配置包含了完整的设计系统：

- **色彩系统**: 语义化的主题色彩
- **字体系统**: 中英文字体优化
- **间距系统**: 扩展的间距选项
- **动画系统**: 丰富的动画效果
- **阴影系统**: 多层次阴影效果
- **响应式**: 包含 xs 和 3xl 断点

### 文件结构

```
src/styles/
├── unified-styles.css          # 主样式文件
├── tailwind-components.css     # Tailwind 组件库
├── style-utils.css            # 工具类
└── debug-styles.css           # 调试样式
```

## 🎨 色彩系统

### 主题色彩

```html
<!-- 主色调 (Primary) -->
<div class="bg-primary-500 text-white">主色调</div>
<div class="bg-primary-100 text-primary-900">浅色背景</div>

<!-- 次要色 (Secondary) -->
<div class="bg-secondary-500 text-white">次要色</div>

<!-- 强调色 (Accent) -->
<div class="bg-accent-500 text-white">强调色</div>
```

### 语义化颜色

```html
<!-- 状态颜色 -->
<div class="text-success-500">成功状态</div>
<div class="text-warning-500">警告状态</div>
<div class="text-error-500">错误状态</div>

<!-- 中性色 -->
<div class="bg-neutral-100 text-neutral-800">中性色背景</div>
```

### 暗色模式专用色彩

```html
<!-- 暗色模式背景 -->
<div class="bg-dark-app-bg text-dark-text-primary">
  暗色模式应用背景
</div>

<div class="bg-dark-card-bg text-dark-text-primary">
  暗色模式卡片背景
</div>
```

## 🧩 组件库

### 按钮组件

```html
<!-- 基础按钮 -->
<button class="btn btn-primary">主要按钮</button>
<button class="btn btn-secondary">次要按钮</button>
<button class="btn btn-outline">轮廓按钮</button>
<button class="btn btn-ghost">幽灵按钮</button>

<!-- 不同尺寸 -->
<button class="btn btn-primary btn-xs">超小</button>
<button class="btn btn-primary btn-sm">小</button>
<button class="btn btn-primary">默认</button>
<button class="btn btn-primary btn-lg">大</button>
<button class="btn btn-primary btn-xl">超大</button>

<!-- 特殊状态 -->
<button class="btn btn-primary btn-loading">加载中</button>
<button class="btn btn-circle btn-primary">圆</button>
```

### 卡片组件

```html
<!-- 基础卡片 -->
<div class="card">
  <div class="card-header">
    <h3 class="modal-title">卡片标题</h3>
  </div>
  <div class="card-body">
    <p>卡片内容</p>
  </div>
  <div class="card-footer">
    <button class="btn btn-primary">操作</button>
  </div>
</div>

<!-- 交互式卡片 -->
<div class="card-interactive">
  <div class="card-body">
    <p>可点击的卡片</p>
  </div>
</div>

<!-- 玻璃效果卡片 -->
<div class="card-glass">
  <div class="card-body">
    <p>玻璃效果卡片</p>
  </div>
</div>
```

### 表单组件

```html
<div class="form-group">
  <label class="form-label">用户名</label>
  <input type="text" class="form-input" placeholder="请输入用户名">
  <div class="form-help">用户名长度应为 3-20 个字符</div>
</div>

<div class="form-group">
  <label class="form-label">密码</label>
  <input type="password" class="form-input form-input-error" placeholder="请输入密码">
  <div class="form-error">密码不能为空</div>
</div>

<div class="form-group">
  <label class="form-label">描述</label>
  <textarea class="form-textarea" placeholder="请输入描述"></textarea>
</div>
```

### 导航组件

```html
<!-- 水平导航 -->
<nav class="nav">
  <a href="#" class="nav-item-active">首页</a>
  <a href="#" class="nav-item-inactive">关于</a>
  <a href="#" class="nav-item-inactive">联系</a>
</nav>

<!-- 垂直导航 -->
<nav class="nav nav-vertical">
  <a href="#" class="nav-item-active">仪表板</a>
  <a href="#" class="nav-item-inactive">用户管理</a>
  <a href="#" class="nav-item-inactive">设置</a>
</nav>

<!-- 面包屑导航 -->
<nav class="breadcrumb">
  <a href="#" class="breadcrumb-item">首页</a>
  <span class="breadcrumb-separator">/</span>
  <a href="#" class="breadcrumb-item">产品</a>
  <span class="breadcrumb-separator">/</span>
  <span class="text-neutral-900">详情</span>
</nav>
```

### 徽章和标签

```html
<!-- 徽章 -->
<span class="badge badge-primary">新</span>
<span class="badge badge-success">已完成</span>
<span class="badge badge-warning">待处理</span>
<span class="badge badge-error">错误</span>

<!-- 标签 -->
<span class="tag badge-primary">React</span>
<span class="tag badge-secondary">TypeScript</span>
<span class="tag badge-accent">Tailwind</span>
```

## 📱 响应式设计

### 断点系统

```css
/* 可用断点 */
xs: 475px   /* 超小屏幕 */
sm: 640px   /* 小屏幕 */
md: 768px   /* 中等屏幕 */
lg: 1024px  /* 大屏幕 */
xl: 1280px  /* 超大屏幕 */
2xl: 1536px /* 2倍超大屏幕 */
3xl: 1600px /* 3倍超大屏幕 */
```

### 响应式示例

```html
<!-- 响应式网格 -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
  <div class="card">项目 1</div>
  <div class="card">项目 2</div>
  <div class="card">项目 3</div>
  <div class="card">项目 4</div>
</div>

<!-- 响应式文字 -->
<h1 class="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold">
  响应式标题
</h1>

<!-- 响应式间距 -->
<div class="p-4 sm:p-6 lg:p-8 xl:p-12">
  响应式内边距
</div>

<!-- 响应式显示/隐藏 -->
<div class="block sm:hidden">仅在小屏幕显示</div>
<div class="hidden sm:block lg:hidden">仅在中等屏幕显示</div>
<div class="hidden lg:block">仅在大屏幕显示</div>
```

### 响应式工具类

```html
<!-- 使用预定义的响应式类 -->
<p class="text-responsive">响应式文字大小</p>
<h1 class="heading-responsive">响应式标题大小</h1>
<div class="padding-responsive">响应式内边距</div>
<div class="gap-responsive flex">响应式间距</div>
```

## 🌙 暗色模式

### 启用暗色模式

```html
<!-- 方式1: 使用 class -->
<html class="dark">

<!-- 方式2: 使用 data 属性 -->
<html data-theme="dark">
```

### 暗色模式样式

```html
<!-- 自动适应暗色模式 -->
<div class="bg-white dark:bg-dark-app-bg text-neutral-900 dark:text-dark-text-primary">
  自动适应的内容
</div>

<!-- 使用预定义的暗色模式颜色 -->
<div class="bg-dark-card-bg text-dark-text-primary">
  暗色模式卡片
</div>

<!-- 条件性显示 -->
<div class="block dark:hidden">仅在亮色模式显示</div>
<div class="hidden dark:block">仅在暗色模式显示</div>
```

## ✨ 动画系统

### 预定义动画

```html
<!-- 淡入动画 -->
<div class="animate-fade-in">淡入效果</div>
<div class="animate-fade-in-up">从下方淡入</div>
<div class="animate-fade-in-down">从上方淡入</div>
<div class="animate-fade-in-left">从左侧淡入</div>
<div class="animate-fade-in-right">从右侧淡入</div>

<!-- 缩放动画 -->
<div class="animate-scale-in">缩放进入</div>
<div class="animate-zoom-in">放大进入</div>

<!-- 滑动动画 -->
<div class="animate-slide-up">向上滑动</div>
<div class="animate-slide-down">向下滑动</div>
<div class="animate-slide-in-bottom">从底部滑入</div>

<!-- 特殊动画 -->
<div class="animate-bounce-gentle">轻柔弹跳</div>
<div class="animate-float">浮动效果</div>
<div class="animate-pulse-slow">慢速脉冲</div>
<div class="animate-spin-slow">慢速旋转</div>
```

### 交互动画

```html
<!-- 悬停效果 -->
<div class="hover-lift">悬停上升</div>
<div class="hover-glow">悬停发光</div>
<div class="hover-scale">悬停缩放</div>

<!-- 组合使用 -->
<button class="btn btn-primary hover-lift hover-glow transition-all duration-300">
  多重悬停效果
</button>
```

### 自定义动画时长

```html
<div class="transition-all duration-150">快速过渡</div>
<div class="transition-all duration-300">标准过渡</div>
<div class="transition-all duration-500">慢速过渡</div>
<div class="transition-all duration-700">超慢过渡</div>
```

## 🎯 最佳实践

### 1. 组件优先

```html
<!-- ✅ 推荐：使用预定义组件 -->
<button class="btn btn-primary">提交</button>

<!-- ❌ 不推荐：重复定义样式 -->
<button class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
  提交
</button>
```

### 2. 语义化颜色

```html
<!-- ✅ 推荐：使用语义化颜色 -->
<div class="text-success-500">操作成功</div>
<div class="text-error-500">操作失败</div>

<!-- ❌ 不推荐：使用具体颜色 -->
<div class="text-green-500">操作成功</div>
<div class="text-red-500">操作失败</div>
```

### 3. 响应式设计

```html
<!-- ✅ 推荐：移动优先 -->
<div class="text-sm sm:text-base lg:text-lg">
  响应式文字
</div>

<!-- ✅ 推荐：使用响应式工具类 -->
<div class="text-responsive">
  响应式文字
</div>
```

### 4. 暗色模式支持

```html
<!-- ✅ 推荐：同时考虑亮色和暗色模式 -->
<div class="bg-white dark:bg-[--color-dark-card-bg] text-neutral-900 dark:text-[--color-dark-text-primary]">
  内容
</div>

<!-- ✅ 推荐：使用预定义的暗色模式颜色 -->
<div class="bg-dark-card-bg text-dark-text-primary">
  暗色模式内容
</div>
```

### 5. 性能优化

```html
<!-- ✅ 推荐：使用 transform 而不是改变 position -->
<div class="hover:-translate-y-1 transition-transform">
  悬停上升
</div>

<!-- ❌ 不推荐：改变 position -->
<div class="hover:-top-1 transition-all">
  悬停上升
</div>
```

## ⚡ 性能优化

### 1. CSS 压缩

生产环境自动启用 PurgeCSS 和 cssnano：

```bash
# 构建生产版本
pnpm run build

# 分析 CSS 大小
pnpm run css:analyze
```

### 2. 按需加载

```javascript
// 只导入需要的组件样式
import './styles/tailwind-components.css';

// 或者在组件中按需导入
// import './Button.module.css';
```

### 3. 避免不必要的类

```html
<!-- ✅ 推荐：合并相似的类 -->
<div class="flex items-center justify-between p-4">
  内容
</div>

<!-- ❌ 不推荐：重复的类 -->
<div class="flex flex-row items-center justify-between px-4 py-4">
  内容
</div>
```

### 4. 使用 CSS 变量

```css
/* 在自定义 CSS 中使用 Tailwind 的 CSS 变量 */
.custom-component {
  background-color: rgb(var(--color-primary-500));
  color: rgb(var(--color-neutral-50));
}
```

## 🛠️ 开发工具

### 可用的 npm 脚本

```bash
# Tailwind 相关
pnpm run tailwind:watch    # 监听模式构建
pnpm run tailwind:build    # 构建 Tailwind CSS

# 样式检查
pnpm run style:check       # 检查样式冲突
pnpm run style:analyze     # 分析样式使用情况

# CSS 优化
pnpm run css:purge         # 清理未使用的 CSS
pnpm run css:analyze       # 分析 CSS 文件大小

# 开发模式
pnpm run dev:debug         # 带调试样式的开发模式
pnpm run dev:full          # 完整功能开发模式

# 构建分析
pnpm run build:analyze     # 构建并分析包大小
```

### 调试工具

```html
<!-- 启用调试样式 -->
<link rel="stylesheet" href="./src/styles/debug-styles.css">

<!-- 添加调试类 -->
<div class="debug-outline">显示元素边界</div>
<div class="debug-flex">显示 Flex 布局</div>
<div class="debug-grid">显示 Grid 布局</div>
```

## 📚 扩展资源

- [Tailwind CSS 官方文档](https://tailwindcss.com/docs)
- [Tailwind UI 组件](https://tailwindui.com/)
- [Headless UI](https://headlessui.com/)
- [Heroicons](https://heroicons.com/)

---

通过遵循这个指南，你可以充分利用项目中配置的 Tailwind CSS 功能，创建一致、美观且高性能的用户界面。

# Tailwind 相关
pnpm run tailwind:watch    # 监听模式
pnpm run tailwind:build    # 构建优化

# 样式工具
pnpm run style:check       # 检查冲突
pnpm run css:purge         # 清理 CSS

# 开发模式
pnpm run dev:debug         # 调试模式
pnpm run dev:full          # 完整功能