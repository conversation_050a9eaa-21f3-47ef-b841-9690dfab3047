import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  css: {
    postcss: './postcss.config.js',
    modules: {
      // CSS Modules 配置
      localsConvention: 'camelCaseOnly', // 支持驼峰命名
      generateScopedName: '[name]__[local]___[hash:base64:5]', // 生成唯一类名
      hashPrefix: 'memorial', // 哈希前缀
    }
  },
  server: {
    port: 4001,
    proxy: {
      '/api/v1': {
        target: 'http://localhost:8008',
        changeOrigin: true,
        secure: false,
      }
    }
  },
  optimizeDeps: {
    exclude: [
      // Exclude Babylon.js shader files from optimization
      '@babylonjs/core/Shaders/default.vertex',
      '@babylonjs/core/Shaders/default.fragment',
      '@babylonjs/core/ShadersWGSL/default.vertex',
      '@babylonjs/core/ShadersWGSL/default.fragment',
      // Exclude all Babylon.js shader files
      '@babylonjs/core/Shaders',
      '@babylonjs/core/ShadersWGSL',
      '@babylonjs/materials'
    ]
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes('node_modules')) {
            if (id.includes('react')) return 'react-vendor';
            if (id.includes('three')) return 'three-vendor';
            if (id.includes('babylonjs')) return 'babylon-vendor';
            return 'vendor';
          }
        }
      }
    }
  }
})
