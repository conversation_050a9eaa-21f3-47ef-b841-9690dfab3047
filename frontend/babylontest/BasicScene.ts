import { Scene, Engine, FreeCamera, HemisphericLight, MeshBuilder, Vector3, PBRMaterial, CubeTexture } from '@babylonjs/core';
import { GridMaterial } from '@babylonjs/materials';

export const createScene = (engine: Engine, canvas: HTMLCanvasElement): Scene => {
  const scene = new Scene(engine);

  const camera = new FreeCamera('camera1', new Vector3(0, 5, -10), scene);
  camera.setTarget(Vector3.Zero());
  camera.attachControl(canvas, true);

  const light = new HemisphericLight('light1', new Vector3(0, 1, 0), scene);
  light.intensity = 0.7;

  // 创建球体并应用PBR材质
  const sphere = MeshBuilder.CreateSphere('sphere', { diameter: 2, segments: 32 }, scene);
  sphere.position.y = 2;

  const pbr = new PBRMaterial("pbr", scene);
  pbr.metallic = 1.0;
  pbr.roughness = 0.5;
  sphere.material = pbr;

  // 创建地面并应用网格材质
  const ground = MeshBuilder.CreateGround('ground', { width: 10, height: 10 }, scene);
  const gridMaterial = new GridMaterial("gridMaterial", scene);
  gridMaterial.mainColor = new Vector3(0.8, 0.8, 0.8);
  gridMaterial.lineColor = new Vector3(0, 1, 0);
  ground.material = gridMaterial;

  // 添加天空盒以提供反射
  const skybox = MeshBuilder.CreateBox("skyBox", { size: 1000.0 }, scene);
  const skyboxMaterial = new PBRMaterial("skyBox", scene);
  skyboxMaterial.backFaceCulling = false;
  skyboxMaterial.reflectionTexture = new CubeTexture("https://www.babylonjs-playground.com/textures/skybox", scene);
  skyboxMaterial.reflectionTexture.coordinatesMode = CubeTexture.SKYBOX_MODE;
  skyboxMaterial.disableLighting = true;
  skybox.material = skyboxMaterial;


  return scene;
};