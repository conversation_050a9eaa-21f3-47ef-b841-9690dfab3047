<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Babylon.js 示例引导页</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 2rem;
            background-color: #f8f9fa;
            color: #343a40;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        h1 {
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }
        .scene-entry {
            margin-bottom: 1.5rem;
            padding: 1rem;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            transition: box-shadow 0.3s ease;
        }
        .scene-entry:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .scene-entry h2 {
            margin-top: 0;
        }
        .scene-entry a {
            font-weight: bold;
            text-decoration: none;
            color: #007bff;
        }
        .scene-entry a:hover {
            text-decoration: underline;
        }
        .scene-entry p {
            margin-bottom: 0;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Babylon.js 示例</h1>
        
        <div class="scene-entry">
            <h2><a href="./scenes/taoist/index.html" target="_blank">道教仙境 (Taoist Fairyland)</a></h2>
            <p>一个基于“道教仙境”概念的抽象3D场景。此示例展示了如何使用Babylon.js创建程序化地形、粒子效果（云海）以及基本的3D模型来构建一个富有氛围的奇幻世界。</p>
        </div>

        <div class="scene-entry">
            <h2><a href="./scenes/material-explorer/index.html" target="_blank">材质浏览器 (Material Explorer)</a></h2>
            <p>一个交互式教学示例，用于演示Babylon.js中不同材质的效果。您可以通过UI控件动态切换和调整标准材质、PBR材质、木纹理和水材质，直观地学习材质的用法。</p>
        </div>

        <!-- 未来可以添加更多示例 -->

    </div>
</body>
</html>