import { Scene, Engine, FreeCamera, Vector3, HemisphericLight, MeshBuilder, CubeTexture, Vector2, Texture, PBRMaterial } from '@babylonjs/core';
import { WaterMaterial } from '@babylonjs/materials';

export const createWaterScene = (engine: Engine, canvas: HTMLCanvasElement): Scene => {
  const scene = new Scene(engine);

  const camera = new FreeCamera('camera1', new Vector3(0, 5, -20), scene);
  camera.setTarget(new Vector3(0, 0, 0));
  camera.attachControl(canvas, true);

  const light = new HemisphericLight('light1', new Vector3(0, 1, 0), scene);
  light.intensity = 0.9;

  // 创建天空盒
  const skybox = MeshBuilder.CreateBox("skyBox", { size: 1000.0 }, scene);
  const skyboxMaterial = new PBRMaterial("skyBox", scene);
  skyboxMaterial.backFaceCulling = false;
  skyboxMaterial.reflectionTexture = new CubeTexture("https://www.babylonjs-playground.com/textures/skybox", scene);
  skyboxMaterial.reflectionTexture.coordinatesMode = Texture.SKYBOX_MODE;
  skyboxMaterial.disableLighting = true;
  skybox.material = skyboxMaterial;

  // 创建水面
  const waterMesh = MeshBuilder.CreateGround("waterMesh", { width: 100, height: 100 }, scene);
  const waterMaterial = new WaterMaterial("water", scene, new Vector2(512, 512));
  waterMaterial.backFaceCulling = true;
  waterMaterial.bumpTexture = new Texture("https://www.babylonjs-playground.com/textures/waterbump.png", scene);
  waterMaterial.windForce = -5;
  waterMaterial.waveHeight = 0.5;
  waterMaterial.bumpHeight = 0.1;
  waterMaterial.waveLength = 0.2;
  waterMaterial.colorBlendFactor = 0;
  waterMaterial.addToRenderList(skybox);
  waterMesh.material = waterMaterial;

  return scene;
};