import React, { useEffect, useRef } from 'react';
import { Engine, Scene } from '@babylonjs/core';
import { createWaterScene } from './WaterScene';

const BabylonScene: React.FC = () => {
  const reactCanvas = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (reactCanvas.current) {
      const engine = new Engine(reactCanvas.current, true);
      const scene = createWaterScene(engine, reactCanvas.current);

      engine.runRenderLoop(() => {
        scene.render();
      });

      const resize = () => {
        engine.resize();
      };

      window.addEventListener('resize', resize);

      return () => {
        window.removeEventListener('resize', resize);
        engine.dispose();
      };
    }
  }, [reactCanvas]);

  return <canvas ref={reactCanvas} style={{ width: '100%', height: '100%' }} />;
};

export default BabylonScene;