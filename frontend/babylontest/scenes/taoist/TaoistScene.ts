import {
    Scene,
    Engine,
    ArcRotateCamera,
    Vector3,
    HemisphericLight,
    DirectionalLight,
    Color3,
    Color4,
    MeshBuilder,
    StandardMaterial,
    Texture,
    ParticleSystem,
    SceneLoader
} from '@babylonjs/core';
import '@babylonjs/core/Meshes/meshBuilder';
import '@babylonjs/loaders';

export class TaoistScene {

    public static createScene(engine: Engine, canvas: HTMLCanvasElement): Scene {
        const scene = new Scene(engine);

        // 1. 相机和光照
        const camera = new ArcRotateCamera("camera", -Math.PI / 2.2, Math.PI / 3.5, 50, new Vector3(0, 5, 0), scene);
        camera.attachControl(canvas, true);
        camera.lowerRadiusLimit = 10;
        camera.upperRadiusLimit = 100;

        const light = new HemisphericLight("light", new Vector3(0, 1, 0), scene);
        light.intensity = 0.7;
        light.diffuse = new Color3(0.8, 0.8, 1); // Cool ambient light

        const dirLight = new DirectionalLight("dirLight", new Vector3(-1, -2, -1), scene);
        dirLight.position = new Vector3(20, 40, 20);
        dirLight.intensity = 0.5;

        // 2. 背景和雾效
        scene.clearColor = new Color4(0.6, 0.8, 1.0, 1.0); // Sky blue
        scene.fogMode = Scene.FOGMODE_EXP;
        scene.fogDensity = 0.02;
        scene.fogColor = new Color3(0.7, 0.8, 1.0);

        // 3. 创建山脉 (使用高度图)
        const ground = MeshBuilder.CreateGroundFromHeightMap("ground", "https://www.babylonjs-playground.com/textures/heightMap.png", {
            width: 100,
            height: 100,
            subdivisions: 100,
            minHeight: 0,
            maxHeight: 15
        }, scene);
        const groundMaterial = new StandardMaterial("groundMat", scene);
        groundMaterial.diffuseTexture = new Texture("https://www.babylonjs-playground.com/textures/ground.jpg", scene);
        groundMaterial.specularColor = new Color3(0.1, 0.1, 0.1);
        ground.material = groundMaterial;
        ground.position.y = -5;

        // 4. 创建漂浮的岛屿和宝塔
        const floatingIsland = MeshBuilder.CreateSphere("island", { diameter: 10, segments: 24 }, scene);
        floatingIsland.position = new Vector3(0, 10, 0);
        const islandMaterial = new StandardMaterial("islandMat", scene);
        islandMaterial.diffuseColor = new Color3(0.5, 0.7, 0.4); // Grassy green
        floatingIsland.material = islandMaterial;

        // 创建一个抽象的宝塔
        this.createPagoda(new Vector3(0, 12.5, 0), scene);

        // 5. 创建云海效果
        const particleSystem = new ParticleSystem("particles", 4000, scene);
        particleSystem.particleTexture = new Texture("https://www.babylonjs-playground.com/textures/flare.png", scene);
        particleSystem.emitter = new Vector3(0, 0, 0);
        particleSystem.minEmitBox = new Vector3(-50, -2, -50);
        particleSystem.maxEmitBox = new Vector3(50, 0, 50);
        particleSystem.color1 = new Color4(0.9, 0.9, 1.0, 0.5);
        particleSystem.color2 = new Color4(1.0, 1.0, 1.0, 0.6);
        particleSystem.colorDead = new Color4(1, 1, 1, 0.05);
        particleSystem.minSize = 2.0;
        particleSystem.maxSize = 5.0;
        particleSystem.minLifeTime = 10.0;
        particleSystem.maxLifeTime = 20.0;
        particleSystem.emitRate = 200;
        particleSystem.blendMode = ParticleSystem.BLENDMODE_STANDARD;
        particleSystem.gravity = new Vector3(0, 0, 0);
        particleSystem.direction1 = new Vector3(-0.1, 0.05, -0.1);
        particleSystem.direction2 = new Vector3(0.1, 0.05, 0.1);
        particleSystem.minAngularSpeed = -0.5;
        particleSystem.maxAngularSpeed = 0.5;
        particleSystem.start();

        return scene;
    }

    private static createPagoda(position: Vector3, scene: Scene): void {
        const pagodaMat = new StandardMaterial("pagodaMat", scene);
        pagodaMat.diffuseColor = new Color3(0.8, 0.2, 0.2); // Red
        pagodaMat.specularColor = new Color3(0.2, 0.2, 0.2);

        const roofMat = new StandardMaterial("roofMat", scene);
        roofMat.diffuseColor = new Color3(0.2, 0.2, 0.2); // Dark grey/black

        let currentY = position.y;

        // Base
        const base = MeshBuilder.CreateCylinder("base", { height: 1, diameter: 6 }, scene);
        base.position = new Vector3(position.x, currentY, position.z);
        base.material = pagodaMat;
        currentY += 0.5;

        // Tiers
        for (let i = 0; i < 4; i++) {
            let diameter = 5 - i * 1;
            // Story
            const story = MeshBuilder.CreateCylinder(`story${i}`, { height: 2, diameter: diameter * 0.8 }, scene);
            story.position = new Vector3(position.x, currentY + 1, position.z);
            story.material = pagodaMat;
            currentY += 2;

            // Roof
            const roof = MeshBuilder.CreateCylinder(`roof${i}`, { height: 1.5, diameterTop: 0, diameterBottom: diameter * 1.2 }, scene);
            roof.position = new Vector3(position.x, currentY + 0.75, position.z);
            roof.material = roofMat;
            currentY += 1.5;
        }

        // Spire
        const spire = MeshBuilder.CreateCylinder("spire", { height: 2, diameterTop: 0, diameterBottom: 0.5 }, scene);
        spire.position = new Vector3(position.x, currentY + 1, position.z);
        spire.material = roofMat;
    }
}