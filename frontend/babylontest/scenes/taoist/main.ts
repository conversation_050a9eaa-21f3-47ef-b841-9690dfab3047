import { Engine } from '@babylonjs/core';
import { TaoistScene } from './TaoistScene';

class App {
    constructor() {
        // 创建 canvas 元素
        const canvas = document.createElement("canvas");
        canvas.style.width = "100%";
        canvas.style.height = "100%";
        canvas.id = "gameCanvas";
        document.body.appendChild(canvas);

        // 初始化 Babylon engine
        const engine = new Engine(canvas, true);
        const scene = TaoistScene.createScene(engine, canvas);

        // 渲染循环
        engine.runRenderLoop(() => {
            scene.render();
        });

        // 窗口大小调整
        window.addEventListener("resize", () => {
            engine.resize();
        });
    }
}

new App();