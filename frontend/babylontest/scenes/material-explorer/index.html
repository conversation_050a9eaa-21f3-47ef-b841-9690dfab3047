<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Babylon.js 材质浏览器</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            height: 100vh;
            font-family: sans-serif;
        }
        #renderCanvas {
            flex-grow: 1;
            width: 100%;
            height: 100%;
            touch-action: none;
        }
        #ui-container {
            position: absolute;
            top: 15px;
            left: 15px;
            background-color: rgba(255, 255, 255, 0.8);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        #ui-container h3 {
            margin: 0 0 10px 0;
            padding-bottom: 5px;
            border-bottom: 1px solid #ccc;
        }
        .control-group {
            display: flex;
            flex-direction: column;
        }
        .control-group label {
            margin-bottom: 5px;
            font-size: 14px;
        }
        .control-group button, .control-group input, .control-group select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <canvas id="renderCanvas"></canvas>
    <div id="ui-container">
        <h3>材质控制器</h3>
        <div class="control-group">
            <label for="material-type">选择材质类型:</label>
            <select id="material-type">
                <option value="standard">标准材质 (Standard)</option>
                <option value="pbr">物理渲染 (PBR)</option>
                <option value="wood">木纹材质 (Wood)</option>
                <option value="water">水面材质 (Water)</option>
            </select>
        </div>
        <div class="control-group">
            <label for="color-picker">主颜色:</label>
            <input type="color" id="color-picker" value="#808080">
        </div>
        <div class="control-group">
            <label for="alpha-slider">透明度:</label>
            <input type="range" id="alpha-slider" min="0" max="1" step="0.01" value="1">
        </div>
    </div>
    <script type="module" src="./main.ts"></script>
</body>
</html>