import { Engine, Scene, ArcRotateCamera, Vector3, Vector2, He<PERSON><PERSON>L<PERSON>, MeshBuilder, StandardMaterial, PBRMaterial, Texture, Color3, CubeTexture } from '@babylonjs/core';
import { WaterMaterial } from '@babylonjs/materials';
import '@babylonjs/core/Meshes/meshBuilder';
// Inspector相关的代码已移除，以确保场景能正常渲染

class MaterialExplorer {
    public scene: Scene; // 公开 scene 以便在控制台访问
    private mainObject: any;

    constructor(engine: Engine, canvas: HTMLCanvasElement) {
        this.scene = this.createScene(engine, canvas);
        this.applyMaterial('standard'); // 在 scene 创建并赋值后调用
        this.setupUI();
    }

    createScene(engine: Engine, canvas: HTMLCanvasElement): Scene {
        const scene = new Scene(engine);

        const camera = new ArcRotateCamera("camera", -Math.PI / 2, Math.PI / 2.5, 10, Vector3.Zero(), scene);
        camera.attachControl(canvas, true);

        const light = new HemisphericLight("light", new Vector3(0, 1, 0), scene);
        light.intensity = 0.7;
        
        const skybox = MeshBuilder.CreateBox("skyBox", { size: 1000.0 }, scene);
        const skyboxMaterial = new StandardMaterial("skyBox", scene);
        skyboxMaterial.backFaceCulling = false;
        skyboxMaterial.reflectionTexture = new CubeTexture("https://www.babylonjs-playground.com/textures/skybox", scene);
        skyboxMaterial.reflectionTexture.coordinatesMode = Texture.SKYBOX_MODE;
        skyboxMaterial.diffuseColor = new Color3(0, 0, 0);
        skyboxMaterial.specularColor = new Color3(0, 0, 0);
        skybox.material = skyboxMaterial;

        this.mainObject = MeshBuilder.CreateSphere("sphere", { diameter: 2, segments: 32 }, scene);
        this.mainObject.position.y = 1;

        const ground = MeshBuilder.CreateGround("ground", {width: 100, height: 100}, scene);
        ground.isVisible = false;

        return scene;
    }

    applyMaterial(type: string) {
        const colorPicker = document.getElementById('color-picker') as HTMLInputElement;
        const alphaSlider = document.getElementById('alpha-slider') as HTMLInputElement;
        const ground = this.scene.getMeshByName("ground");

        if (!this.mainObject || !ground) return;

        ground.isVisible = (type === 'water');
        this.mainObject.isVisible = (type !== 'water');

        let material: StandardMaterial | PBRMaterial | WaterMaterial;

        switch (type) {
            case 'pbr':
                material = new PBRMaterial("pbr", this.scene);
                material.albedoColor = Color3.FromHexString(colorPicker.value);
                material.metallic = 0.8;
                material.roughness = 0.2;
                break;
            case 'wood':
                material = new StandardMaterial("wood", this.scene);
                material.diffuseTexture = new Texture("https://www.babylonjs-playground.com/textures/wood.jpg", this.scene);
                break;
            case 'water':
                material = new WaterMaterial("water", this.scene, new Vector2(512, 512));
                material.backFaceCulling = true;
                const skybox = this.scene.getMeshByName("skyBox");
                if(skybox) material.addToRenderList(skybox);
                ground.material = material;
                return;
            case 'standard':
            default:
                material = new StandardMaterial("standard", this.scene);
                material.diffuseColor = Color3.FromHexString(colorPicker.value);
                material.specularColor = new Color3(0.2, 0.2, 0.2);
                break;
        }
        
        material.alpha = parseFloat(alphaSlider.value);
        this.mainObject.material = material;
    }

    setupUI() {
        const materialTypeSelect = document.getElementById('material-type') as HTMLSelectElement;
        const colorPicker = document.getElementById('color-picker') as HTMLInputElement;
        const alphaSlider = document.getElementById('alpha-slider') as HTMLInputElement;

        const updateMaterial = () => {
            this.applyMaterial(materialTypeSelect.value);
        };

        materialTypeSelect.addEventListener('change', updateMaterial);
        colorPicker.addEventListener('input', () => {
            if (this.mainObject.material) {
                const material = this.mainObject.material;
                if (material instanceof StandardMaterial) {
                    material.diffuseColor = Color3.FromHexString(colorPicker.value);
                } else if (material instanceof PBRMaterial) {
                    material.albedoColor = Color3.FromHexString(colorPicker.value);
                }
            }
        });
        alphaSlider.addEventListener('input', () => {
            if (this.mainObject.material) {
                this.mainObject.material.alpha = parseFloat(alphaSlider.value);
            }
        });
    }
}

// --- App Initialization ---
window.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('renderCanvas') as HTMLCanvasElement;
    const engine = new Engine(canvas, true);
    const app = new MaterialExplorer(engine, canvas);
    const sceneToRender = app.scene;

    // 暴露 app 和 scene 到 window 对象，以便在控制台访问
    (window as any).app = app;
    (window as any).scene = sceneToRender;

    engine.runRenderLoop(() => {
        sceneToRender.render();
    });

    window.addEventListener('resize', () => {
        engine.resize();
    });
});