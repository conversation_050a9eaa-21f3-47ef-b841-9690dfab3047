#!/usr/bin/env node

/**
 * CSS Modules 迁移脚本
 * 帮助将现有的普通CSS文件迁移到CSS Modules
 */

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

class CSSModulesMigrator {
  constructor() {
    this.srcDir = path.join(process.cwd(), 'src');
    this.componentsDir = path.join(this.srcDir, 'components');
    this.migratedFiles = [];
    this.errors = [];
  }

  /**
   * 开始迁移过程
   */
  async migrate() {
    console.log('🚀 开始 CSS Modules 迁移...');
    console.log(`📁 扫描目录: ${this.componentsDir}`);

    try {
      // 查找所有组件CSS文件
      const cssFiles = await this.findComponentCSSFiles();
      console.log(`📄 找到 ${cssFiles.length} 个CSS文件`);

      if (cssFiles.length === 0) {
        console.log('✅ 没有需要迁移的CSS文件');
        return;
      }

      // 迁移每个文件
      for (const cssFile of cssFiles) {
        await this.migrateFile(cssFile);
      }

      this.generateReport();
    } catch (error) {
      console.error('❌ 迁移过程中发生错误:', error.message);
    }
  }

  /**
   * 查找组件目录下的CSS文件
   */
  async findComponentCSSFiles() {
    const pattern = path.join(this.componentsDir, '**/*.css').replace(/\\/g, '/');
    const files = await glob(pattern);
    
    // 过滤掉已经是 .module.css 的文件
    return files.filter(file => !file.includes('.module.css'));
  }

  /**
   * 迁移单个CSS文件
   */
  async migrateFile(cssFilePath) {
    try {
      const fileName = path.basename(cssFilePath, '.css');
      const dirName = path.dirname(cssFilePath);
      const newCSSPath = path.join(dirName, `${fileName}.module.css`);
      const tsxFilePath = path.join(dirName, `${fileName}.tsx`);

      console.log(`\n🔄 迁移: ${path.relative(this.srcDir, cssFilePath)}`);

      // 检查对应的TSX文件是否存在
      if (!fs.existsSync(tsxFilePath)) {
        console.log(`⚠️  警告: 未找到对应的TSX文件 ${fileName}.tsx`);
      }

      // 读取CSS内容
      const cssContent = fs.readFileSync(cssFilePath, 'utf8');
      
      // 转换CSS内容
      const convertedCSS = this.convertCSSContent(cssContent);
      
      // 写入新的CSS Modules文件
      fs.writeFileSync(newCSSPath, convertedCSS);
      console.log(`✅ 创建: ${path.relative(this.srcDir, newCSSPath)}`);

      // 更新TSX文件中的导入
      if (fs.existsSync(tsxFilePath)) {
        await this.updateTSXImports(tsxFilePath, fileName);
      }

      // 备份原文件
      const backupPath = `${cssFilePath}.backup`;
      fs.renameSync(cssFilePath, backupPath);
      console.log(`📦 备份: ${path.relative(this.srcDir, backupPath)}`);

      this.migratedFiles.push({
        original: cssFilePath,
        new: newCSSPath,
        backup: backupPath,
        component: tsxFilePath
      });

    } catch (error) {
      this.errors.push({
        file: cssFilePath,
        error: error.message
      });
      console.error(`❌ 迁移失败: ${cssFilePath} - ${error.message}`);
    }
  }

  /**
   * 转换CSS内容，添加CSS Modules相关的注释和优化
   */
  convertCSSContent(content) {
    let converted = content;

    // 添加CSS Modules注释
    const header = `/* CSS Modules 样式文件 */\n/* 使用 .module.css 后缀启用 CSS Modules */\n\n`;
    converted = header + converted;

    // 检查是否使用了全局样式，添加提示注释
    if (converted.includes('body') || converted.includes('html') || converted.includes('*')) {
      const globalComment = `\n/* 注意: 检测到可能的全局样式，请考虑使用 :global() 包装或移至全局样式文件 */\n`;
      converted = header + globalComment + content;
    }

    return converted;
  }

  /**
   * 更新TSX文件中的CSS导入
   */
  async updateTSXImports(tsxFilePath, fileName) {
    try {
      let content = fs.readFileSync(tsxFilePath, 'utf8');
      
      // 查找CSS导入语句
      const cssImportRegex = new RegExp(`import\\s+['"]\\.\\/.*${fileName}\\.css['"];?`, 'g');
      const hasImport = cssImportRegex.test(content);
      
      if (hasImport) {
        // 替换为CSS Modules导入
        content = content.replace(
          cssImportRegex,
          `import styles from './${fileName}.module.css';`
        );
        
        // 添加clsx导入（如果还没有）
        if (!content.includes('clsx') && !content.includes('classnames')) {
          const reactImportMatch = content.match(/import React.*from ['"']react['"'];/);
          if (reactImportMatch) {
            const insertIndex = content.indexOf(reactImportMatch[0]) + reactImportMatch[0].length;
            content = content.slice(0, insertIndex) + 
                     "\nimport { clsx } from 'clsx';" + 
                     content.slice(insertIndex);
          }
        }
        
        // 添加迁移提示注释
        const migrationComment = `\n// TODO: 更新类名使用方式\n// 从: className="button primary"\n// 到: className={clsx(styles.button, styles.primary)}\n`;
        
        const importEndIndex = content.lastIndexOf('import');
        const nextLineIndex = content.indexOf('\n', importEndIndex);
        content = content.slice(0, nextLineIndex) + migrationComment + content.slice(nextLineIndex);
        
        fs.writeFileSync(tsxFilePath, content);
        console.log(`✅ 更新: ${path.relative(this.srcDir, tsxFilePath)}`);
      }
    } catch (error) {
      console.error(`❌ 更新TSX文件失败: ${tsxFilePath} - ${error.message}`);
    }
  }

  /**
   * 生成迁移报告
   */
  generateReport() {
    console.log('\n📊 迁移报告');
    console.log('='.repeat(50));
    
    console.log(`✅ 成功迁移: ${this.migratedFiles.length} 个文件`);
    console.log(`❌ 迁移失败: ${this.errors.length} 个文件`);
    
    if (this.migratedFiles.length > 0) {
      console.log('\n📁 迁移的文件:');
      this.migratedFiles.forEach(file => {
        console.log(`  • ${path.relative(this.srcDir, file.original)} → ${path.relative(this.srcDir, file.new)}`);
      });
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ 失败的文件:');
      this.errors.forEach(error => {
        console.log(`  • ${path.relative(this.srcDir, error.file)}: ${error.error}`);
      });
    }
    
    console.log('\n📝 后续步骤:');
    console.log('1. 检查生成的 .module.css 文件');
    console.log('2. 更新组件中的类名使用方式');
    console.log('3. 测试组件功能是否正常');
    console.log('4. 删除备份文件（.backup）');
    console.log('\n📚 参考文档: CSS_MODULES_GUIDE.md');
  }
}

// 运行迁移
if (import.meta.url === `file://${process.argv[1]}`) {
  const migrator = new CSSModulesMigrator();
  migrator.migrate();
}

export default CSSModulesMigrator;