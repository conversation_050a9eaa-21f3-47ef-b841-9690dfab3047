import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

class StyleConflictChecker {
  constructor() {
    this.issues = [];
    this.duplicateSelectors = new Map();
    this.zIndexValues = new Map();
    this.hardcodedColors = new Set();
    this.globalSelectors = new Set();
  }

  // 分析单个文件
  analyzeFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      this.checkDuplicateSelectors(content, filePath);
      this.checkZIndexValues(content, filePath);
      this.checkHardcodedColors(content, filePath);
      this.checkGlobalSelectors(content, filePath);
      this.checkContainerConflicts(content, filePath);
    } catch (error) {
      console.error(`读取文件失败: ${filePath}`, error.message);
    }
  }

  // 检测重复选择器
  checkDuplicateSelectors(content, filePath) {
    // 移除注释和字符串
    const cleanContent = content
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
      .replace(/\/\/.*$/gm, '') // 移除行注释
      .replace(/["'][^"']*["']/g, ''); // 移除字符串
    
    // 匹配CSS选择器（类和ID）
    const selectorRegex = /^\s*([.#][a-zA-Z][a-zA-Z0-9_-]*(?:\s*,\s*[.#][a-zA-Z][a-zA-Z0-9_-]*)*)\s*\{/gm;
    let match;
    const fileSelectors = new Set(); // 用于跟踪当前文件中已处理的选择器
    
    while ((match = selectorRegex.exec(cleanContent)) !== null) {
      const selector = match[1].trim();
      
      // 跳过伪类、伪元素和媒体查询
      if (selector.includes(':') || selector.includes('@') || selector.includes('keyframes')) {
        continue;
      }
      
      // 如果当前文件中已经处理过这个选择器，跳过
      if (fileSelectors.has(selector)) {
        continue;
      }
      
      fileSelectors.add(selector);
      
      if (!this.duplicateSelectors.has(selector)) {
        this.duplicateSelectors.set(selector, []);
      }
      
      // 检查文件是否已经在列表中，避免重复添加
      const files = this.duplicateSelectors.get(selector);
      if (!files.includes(filePath)) {
        files.push(filePath);
      }
    }
  }

  // 检测z-index值
  checkZIndexValues(content, filePath) {
    // 跳过调试样式文件
    if (filePath.includes('debug-styles.css')) {
      return;
    }
    
    const zIndexRegex = /z-index:\s*(\d+)/g;
    let match;
    
    while ((match = zIndexRegex.exec(content)) !== null) {
      const value = parseInt(match[1]);
      
      if (!this.zIndexValues.has(value)) {
        this.zIndexValues.set(value, []);
      }
      
      this.zIndexValues.get(value).push(filePath);
      
      // 检查是否使用了推荐的z-index值
      const recommendedValues = [1000, 1020, 1030, 1040, 1050, 1060, 1070, 1080];
      if (!recommendedValues.includes(value)) {
        this.issues.push({
          type: 'z-index',
          file: filePath,
          issue: `使用了非推荐的z-index值: ${value}`,
          suggestion: `建议使用推荐的z-index值: ${recommendedValues.join(', ')}`
        });
      }
    }
  }

  // 检测硬编码颜色
  checkHardcodedColors(content, filePath) {
    const colorRegex = /#[0-9a-fA-F]{3,6}|rgb\([^)]+\)|rgba\([^)]+\)|hsl\([^)]+\)|hsla\([^)]+\)/g;
    let match;
    
    while ((match = colorRegex.exec(content)) !== null) {
      // 跳过CSS变量定义
      const beforeMatch = content.substring(0, match.index);
      if (beforeMatch.includes('--') && beforeMatch.lastIndexOf('--') > beforeMatch.lastIndexOf(';')) {
        continue;
      }
      
      this.hardcodedColors.add(match[0]);
      this.issues.push({
        type: 'hardcoded-color',
        file: filePath,
        issue: `发现硬编码颜色: ${match[0]}`,
        suggestion: '建议使用CSS变量替代硬编码颜色'
      });
    }
  }

  // 检测全局选择器
  checkGlobalSelectors(content, filePath) {
    // 只检查组件文件中的全局选择器
    if (!filePath.includes('/components/')) {
      return;
    }
    
    const globalSelectors = ['body', 'html', '*', 'div', 'span', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
    
    globalSelectors.forEach(selector => {
      // 转义特殊正则表达式字符
      const escapedSelector = selector.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(`^\\s*${escapedSelector}\\s*\\{`, 'gm');
      if (regex.test(content)) {
        this.globalSelectors.add(selector);
        this.issues.push({
          type: 'global-selector',
          file: filePath,
          issue: `在组件文件中使用了全局选择器: ${selector}`,
          suggestion: '建议使用类选择器或CSS模块化'
        });
      }
    });
  }

  // 检测容器类冲突
  checkContainerConflicts(content, filePath) {
    const containerRegex = /\.container\s*\{/g;
    if (containerRegex.test(content)) {
      this.issues.push({
        type: 'container-conflict',
        file: filePath,
        issue: '发现.container类定义',
        suggestion: '确保.container类定义不与框架冲突'
      });
    }
  }

  // 生成报告
  generateReport(isDetailMode = false) {
    console.log('\n🔍 样式冲突检测报告\n');
    console.log('='.repeat(50));
    
    if (isDetailMode && this.issues.length > 0) {
      // 按类型分组显示问题
      const issuesByType = {};
      this.issues.forEach(issue => {
        if (!issuesByType[issue.type]) {
          issuesByType[issue.type] = [];
        }
        issuesByType[issue.type].push(issue);
      });
      
      Object.keys(issuesByType).forEach(type => {
        const typeNames = {
          'hardcoded-color': '🎨 硬编码颜色',
          'z-index': '📏 Z-index问题',
          'global-selector': '🌐 全局选择器',
          'css-variables': '🔧 CSS变量使用',
          'container-conflict': '📦 容器类冲突'
        };
        
        console.log(`\n${typeNames[type] || type} (${issuesByType[type].length}个):`);
        console.log('-'.repeat(30));
        
        issuesByType[type].forEach((issue, index) => {
          console.log(`${index + 1}. 文件: ${issue.file}`);
          if (issue.line) {
            console.log(`   行号: ${issue.line}`);
          }
          console.log(`   问题: ${issue.message || issue.issue}`);
          console.log(`   建议: ${issue.suggestion}\n`);
        });
      });
    }
    
    // 显示重复选择器
    console.log('\n🔄 重复选择器分析:');
    console.log('-'.repeat(30));
    
    let duplicateCount = 0;
    this.duplicateSelectors.forEach((files, selector) => {
      if (files.length > 1) {
        duplicateCount++;
        console.log(`选择器 "${selector}" 在以下文件中重复定义:`);
        files.forEach(file => console.log(`  - ${file}`));
        console.log('');
      }
    });
    
    if (duplicateCount === 0) {
      console.log('未发现重复选择器');
    }
    
    // 显示z-index冲突
    console.log('\n📊 Z-index使用分析:');
    console.log('-'.repeat(30));
    
    let zIndexConflicts = 0;
    this.zIndexValues.forEach((files, value) => {
      if (files.length > 1) {
        zIndexConflicts++;
        console.log(`z-index值 ${value} 在以下文件中使用:`);
        files.forEach(file => console.log(`  - ${file}`));
        console.log('');
      }
    });
    
    if (zIndexConflicts === 0) {
      console.log('未发现z-index冲突');
    }
    
    // 总结
    console.log('\n📈 总结:');
    console.log('-'.repeat(30));
    console.log(`总共发现 ${this.issues.length + duplicateCount} 个问题`);
    console.log(`重复选择器: ${duplicateCount} 个`);
    console.log(`硬编码颜色: ${this.hardcodedColors.size} 个`);
    console.log(`全局选择器使用: ${this.globalSelectors.size} 个`);
    
    if (this.issues.length + duplicateCount > 0) {
      console.log('\n💡 建议优先处理:');
      console.log('1. 替换硬编码颜色为CSS变量');
      console.log('2. 移除重复的全局样式定义');
      console.log('3. 统一z-index管理');
      console.log('4. 使用组件级样式避免全局污染');
    } else {
      console.log('\n✅ 未发现样式冲突问题！');
    }
  }

  // 运行检测
  async run(isDetailMode = false) {
    console.log('🚀 开始检测样式冲突...');
    
    // 查找所有CSS文件
    const cssFiles = await glob('src/**/*.css', {
      cwd: process.cwd(),
      absolute: true
    }) || [];
    
    console.log(`找到 ${cssFiles.length} 个CSS文件`);
    
    cssFiles.forEach(file => {
      console.log(`检测: ${path.relative(process.cwd(), file)}`);
      this.analyzeFile(file);
    });
    
    this.generateReport(isDetailMode);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const checker = new StyleConflictChecker();
  const isDetailMode = process.argv.includes('--detail');
  checker.run(isDetailMode).catch(console.error);
}

export default StyleConflictChecker;