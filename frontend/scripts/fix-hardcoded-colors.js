#!/usr/bin/env node

/**
 * 硬编码颜色修复脚本
 * 自动将CSS文件中的硬编码颜色替换为CSS变量
 */

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

class ColorFixer {
  constructor() {
    this.colorMappings = {
      // 主色调映射
      '#4A90E2': 'var(--color-primary)',
      '#4a90e2': 'var(--color-primary)',
      '#357ABD': 'var(--color-primary-hover)',
      '#357abd': 'var(--color-primary-hover)',
      '#3a7bc8': 'var(--color-primary-hover)',
      '#E3F2FD': 'var(--color-primary-light)',
      '#e3f2fd': 'var(--color-primary-light)',
      
      // 次要颜色
      '#F5A623': 'var(--color-secondary)',
      '#f5a623': 'var(--color-secondary)',
      '#E09612': 'var(--color-secondary-hover)',
      '#e09612': 'var(--color-secondary-hover)',
      
      // 状态颜色
      '#2ECC71': 'var(--color-accent)',
      '#2ecc71': 'var(--color-accent)',
      '#F39C12': 'var(--color-warning)',
      '#f39c12': 'var(--color-warning)',
      '#E74C3C': 'var(--color-error)',
      '#e74c3c': 'var(--color-error)',
      
      // 中性色映射
      '#F8F8F8': 'var(--color-gray-50)',
      '#f8f8f8': 'var(--color-gray-50)',
      '#F5F5F5': 'var(--color-gray-100)',
      '#f5f5f5': 'var(--color-gray-100)',
      '#EEEEEE': 'var(--color-gray-200)',
      '#eeeeee': 'var(--color-gray-200)',
      '#E0E0E0': 'var(--color-gray-300)',
      '#e0e0e0': 'var(--color-gray-300)',
      '#BDBDBD': 'var(--color-gray-400)',
      '#bdbdbd': 'var(--color-gray-400)',
      '#9E9E9E': 'var(--color-gray-500)',
      '#9e9e9e': 'var(--color-gray-500)',
      '#757575': 'var(--color-gray-600)',
      '#616161': 'var(--color-gray-700)',
      '#424242': 'var(--color-gray-800)',
      '#333333': 'var(--color-gray-900)',
      '#333': 'var(--color-gray-900)',
      
      // 常见颜色映射
      '#FFFFFF': 'var(--color-surface)',
      '#ffffff': 'var(--color-surface)',
      '#fff': 'var(--color-surface)',
      '#CCCCCC': 'var(--color-gray-400)',
      '#cccccc': 'var(--color-gray-400)',
      '#ccc': 'var(--color-gray-400)',
      '#999999': 'var(--color-gray-500)',
      '#999': 'var(--color-gray-500)',
      '#666666': 'var(--color-gray-600)',
      '#666': 'var(--color-gray-600)',
      '#404040': 'var(--color-gray-800)',
      '#000000': 'var(--color-gray-900)',
      '#000': 'var(--color-gray-900)',
      
      // 特殊颜色
      '#646cff': 'var(--color-primary)',
      '#535bf2': 'var(--color-primary-hover)',
      '#ff4500': 'var(--color-error)',
      '#ff4d4d': 'var(--color-error)',
      '#4CAF50': 'var(--color-accent)',
      '#4caf50': 'var(--color-accent)',
      '#2196F3': 'var(--color-primary)',
      '#2196f3': 'var(--color-primary)',
      '#F44336': 'var(--color-error)',
      '#f44336': 'var(--color-error)',
      '#3f51b5': 'var(--color-primary)',
      '#ddd': 'var(--color-gray-300)',
      '#DDD': 'var(--color-gray-300)',
      '#aaa': 'var(--color-gray-500)',
      '#AAA': 'var(--color-gray-500)',
      
      // 额外的灰色调
      '#1a1a1a': 'var(--color-gray-900)',
      '#222': 'var(--color-gray-900)',
      '#444': 'var(--color-gray-800)',
      '#555': 'var(--color-gray-700)',
      '#4a4a4a': 'var(--color-gray-800)',
      '#5a5a5a': 'var(--color-gray-700)',
      '#606060': 'var(--color-gray-600)',
      
      // 错误和警告相关颜色
      '#ffebee': 'var(--color-error-light)',
      '#d32f2f': 'var(--color-error)',
      '#c0392b': 'var(--color-error)',
      '#357ab8': 'var(--color-primary-hover)',
      '#a0c4e8': 'var(--color-primary-light)',
      
      // 其他特殊颜色
      '#f5f7fa': 'var(--color-gray-50)',
      '#f9f9f9': 'var(--color-gray-100)',
      '#ff5500': 'var(--color-error)',
      '#88ccff': 'var(--color-primary-light)',
      '#00ff00': 'var(--color-accent)',
      
      // 深色主题相关
      '#111827': 'var(--color-gray-900)',
      '#1f2937': 'var(--color-gray-800)',
      '#374151': 'var(--color-gray-700)',
      '#9ca3af': 'var(--color-gray-500)',
      '#3b82f6': 'var(--color-primary)',
      
      // 供奉系统装饰颜色
      '#8b4513': 'var(--color-brown)',
      '#a0522d': 'var(--color-brown-light)',
      '#cd853f': 'var(--color-brown-lighter)',
      '#ff69b4': 'var(--color-pink)',
      '#ff1493': 'var(--color-pink-dark)',
      '#228b22': 'var(--color-green)',
      '#ffd700': 'var(--color-gold)',
      '#ffa500': 'var(--color-orange)',
      
      // 调试样式颜色
      '#00D8FF': 'var(--color-cyan)',
      
      // 文档示例颜色
      '#eaeaea': 'var(--color-gray-300)',
      '#FFA631': 'var(--color-orange)',
    };
    
    this.rgbaPatterns = {
      // 常见的 rgba 模式
      'rgba(0, 0, 0, 0.5)': 'rgba(var(--color-gray-900-rgb), 0.5)',
      'rgba(0, 0, 0, 0.7)': 'rgba(var(--color-gray-900-rgb), 0.7)',
      'rgba(0, 0, 0, 0.8)': 'rgba(var(--color-gray-900-rgb), 0.8)',
      'rgba(0, 0, 0, 0.9)': 'rgba(var(--color-gray-900-rgb), 0.9)',
      'rgba(255, 255, 255, 0.1)': 'rgba(var(--color-surface-rgb), 0.1)',
      'rgba(255, 255, 255, 0.3)': 'rgba(var(--color-surface-rgb), 0.3)',
      'rgba(255, 255, 255, 0.5)': 'rgba(var(--color-surface-rgb), 0.5)',
      'rgba(74, 144, 226, 0.1)': 'var(--color-primary-light)',
      'rgba(74, 144, 226, 0.3)': 'rgba(var(--color-primary-rgb), 0.3)',
      
      // 更多rgba颜色映射
      'rgba(0,0,0,0.5)': 'rgba(var(--color-gray-900-rgb), 0.5)',
      'rgba(0,0,0,0.7)': 'rgba(var(--color-gray-900-rgb), 0.7)',
      'rgba(30, 41, 59, 0.6)': 'rgba(var(--color-gray-800-rgb), 0.6)',
      'rgba(30, 41, 59, 0.8)': 'rgba(var(--color-gray-800-rgb), 0.8)',
      'rgba(100, 108, 255, 0.2)': 'rgba(var(--color-primary-rgb), 0.2)',
      'rgba(63, 81, 181, 0.3)': 'rgba(var(--color-primary-rgb), 0.3)',
      'rgba(0, 0, 0, 0.1)': 'rgba(var(--color-gray-900-rgb), 0.1)',
      'rgba(0, 0, 0, 0.15)': 'rgba(var(--color-gray-900-rgb), 0.15)',
      'rgba(0, 0, 0, 0.2)': 'rgba(var(--color-gray-900-rgb), 0.2)',
      'rgba(0, 0, 0, 0.3)': 'rgba(var(--color-gray-900-rgb), 0.3)',
      'rgba(0, 0, 0, 0.4)': 'rgba(var(--color-gray-900-rgb), 0.4)',
      'rgba(0, 0, 0, 0.6)': 'rgba(var(--color-gray-900-rgb), 0.6)',
      'rgba(0,0,0,0.1)': 'rgba(var(--color-gray-900-rgb), 0.1)',
      'rgba(0,0,0,0.2)': 'rgba(var(--color-gray-900-rgb), 0.2)',
      'rgba(0,0,0,0.3)': 'rgba(var(--color-gray-900-rgb), 0.3)',
      'rgba(0,0,0,0.4)': 'rgba(var(--color-gray-900-rgb), 0.4)',
      'rgba(0,0,0,0.6)': 'rgba(var(--color-gray-900-rgb), 0.6)',
      'rgba(0,0,0,0.8)': 'rgba(var(--color-gray-900-rgb), 0.8)',
      'rgba(0,0,0,0.9)': 'rgba(var(--color-gray-900-rgb), 0.9)',
      
      // 更多rgba颜色映射
      'rgba(0, 0, 0, 0.04)': 'rgba(var(--color-gray-900-rgb), 0.04)',
      'rgba(0, 0, 0, 0.05)': 'rgba(var(--color-gray-900-rgb), 0.05)',
      'rgba(0, 0, 0, 0.06)': 'rgba(var(--color-gray-900-rgb), 0.06)',
      'rgba(0, 0, 0, 0.07)': 'rgba(var(--color-gray-900-rgb), 0.07)',
      'rgba(255, 255, 255, 0.2)': 'rgba(var(--color-surface-rgb), 0.2)',
      'rgba(200, 200, 200, 0.8)': 'rgba(var(--color-gray-400-rgb), 0.8)',
      'rgba(200, 200, 200, 0)': 'rgba(var(--color-gray-400-rgb), 0)',
      
      // 调试样式rgba颜色
      'rgba(255, 0, 0, 0.1)': 'rgba(var(--color-red-rgb), 0.1)',
      'rgba(255, 0, 0, 0.3)': 'rgba(var(--color-red-rgb), 0.3)',
      'rgba(255, 0, 0, 0.8)': 'rgba(var(--color-red-rgb), 0.8)',
      'rgba(255, 0, 0, 0.9)': 'rgba(var(--color-red-rgb), 0.9)',
      'rgba(0, 255, 0, 0.1)': 'rgba(var(--color-green-rgb), 0.1)',
      'rgba(0, 255, 0, 0.3)': 'rgba(var(--color-green-rgb), 0.3)',
      'rgba(0, 0, 255, 0.1)': 'rgba(var(--color-blue-rgb), 0.1)',
      'rgba(0, 0, 255, 0.5)': 'rgba(var(--color-blue-rgb), 0.5)',
      'rgba(0, 0, 255, 0.9)': 'rgba(var(--color-blue-rgb), 0.9)',
      'rgba(255, 165, 0, 0.1)': 'rgba(var(--color-orange-rgb), 0.1)',
      'rgba(255, 165, 0, 0.7)': 'rgba(var(--color-orange-rgb), 0.7)',
      'rgba(255, 165, 0, 0.9)': 'rgba(var(--color-orange-rgb), 0.9)',
      'rgba(255, 255, 0, 0.3)': 'rgba(var(--color-yellow-rgb), 0.3)',
      'rgba(255, 255, 0, 0.9)': 'rgba(var(--color-yellow-rgb), 0.9)',
      'rgba(128, 0, 128, 0.9)': 'rgba(var(--color-purple-rgb), 0.9)',
      'rgba(0, 128, 128, 0.9)': 'rgba(var(--color-teal-rgb), 0.9)',
      'rgba(255, 0, 255, 0.8)': 'rgba(var(--color-magenta-rgb), 0.8)',
      'rgba(255, 0, 255, 0.9)': 'rgba(var(--color-magenta-rgb), 0.9)',
      'rgba(34, 197, 94, 0.8)': 'rgba(var(--color-green-rgb), 0.8)',
      'rgba(34, 197, 94, 0.9)': 'rgba(var(--color-green-rgb), 0.9)',
      'rgba(59, 130, 246, 0.8)': 'rgba(var(--color-blue-rgb), 0.8)',
      'rgba(147, 51, 234, 0.8)': 'rgba(var(--color-purple-rgb), 0.8)',
      'rgba(239, 68, 68, 0.8)': 'rgba(var(--color-red-rgb), 0.8)',
      'rgba(245, 101, 101, 0.8)': 'rgba(var(--color-red-rgb), 0.8)',
      
      // 文档和配置文件中的颜色
      'rgb(var(--color-primary-500))': 'rgb(var(--color-primary-rgb))',
      'rgb(var(--color-neutral-50))': 'rgb(var(--color-gray-50-rgb))',
      
      // Tailwind配置中的颜色
      '#eff6ff': 'var(--color-blue-50)',
      '#dbeafe': 'var(--color-blue-100)',
      '#bfdbfe': 'var(--color-blue-200)',
      '#93c5fd': 'var(--color-blue-300)',
      '#60a5fa': 'var(--color-blue-400)',
      '#2563eb': 'var(--color-blue-600)',
      '#1d4ed8': 'var(--color-blue-700)',
      '#1e40af': 'var(--color-blue-800)',
      '#1e3a8a': 'var(--color-blue-900)',
      '#172554': 'var(--color-blue-950)',
      
      // 阴影相关的rgba值
      'rgba(0, 0, 0, 0.07)': 'rgba(var(--color-gray-900-rgb), 0.07)',
      'rgba(0, 0, 0, 0.04)': 'rgba(var(--color-gray-900-rgb), 0.04)',
      
      // SVG中的颜色
      '#00D8FF': 'var(--color-cyan)',
    };
    
    this.fixedFiles = [];
    this.totalReplacements = 0;
  }
  
  // 修复单个文件
  fixFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let originalContent = content;
      let replacements = 0;
      
      // 替换十六进制颜色
      Object.entries(this.colorMappings).forEach(([hexColor, cssVar]) => {
        const regex = new RegExp(hexColor.replace('#', '#'), 'gi');
        const matches = content.match(regex);
        if (matches) {
          content = content.replace(regex, cssVar);
          replacements += matches.length;
        }
      });
      
      // 替换 rgba 颜色
      Object.entries(this.rgbaPatterns).forEach(([rgbaColor, cssVar]) => {
        const regex = new RegExp(rgbaColor.replace(/[()]/g, '\\$&'), 'gi');
        const matches = content.match(regex);
        if (matches) {
          content = content.replace(regex, cssVar);
          replacements += matches.length;
        }
      });
      
      // 如果有修改，写入文件
      if (content !== originalContent) {
        fs.writeFileSync(filePath, content, 'utf8');
        this.fixedFiles.push({
          path: filePath,
          replacements: replacements
        });
        this.totalReplacements += replacements;
        console.log(`✅ 修复: ${path.relative(process.cwd(), filePath)} (${replacements} 处替换)`);
      }
      
    } catch (error) {
      console.error(`❌ 修复失败: ${filePath}`, error.message);
    }
  }
  
  // 运行修复
  async run() {
    console.log('🚀 开始修复硬编码颜色...');
    
    // 查找所有CSS文件
    const cssFiles = await glob('src/**/*.css', {
      cwd: process.cwd(),
      absolute: true
    }) || [];
    
    // 查找所有TSX文件中的内联样式
    const tsxFiles = await glob('src/**/*.{tsx,ts}', {
      cwd: process.cwd(),
      absolute: true
    }) || [];
    
    const allFiles = [...cssFiles, ...tsxFiles];
    
    console.log(`找到 ${allFiles.length} 个文件需要检查`);
    
    allFiles.forEach(file => {
      this.fixFile(file);
    });
    
    this.generateReport();
  }
  
  // 生成修复报告
  generateReport() {
    console.log('\n📊 修复报告');
    console.log('='.repeat(50));
    
    if (this.fixedFiles.length === 0) {
      console.log('✅ 没有发现需要修复的硬编码颜色！');
      return;
    }
    
    console.log(`总共修复了 ${this.fixedFiles.length} 个文件`);
    console.log(`总共替换了 ${this.totalReplacements} 处硬编码颜色`);
    
    console.log('\n📝 修复详情:');
    console.log('-'.repeat(30));
    
    this.fixedFiles.forEach(file => {
      console.log(`${file.path}: ${file.replacements} 处替换`);
    });
    
    console.log('\n💡 建议:');
    console.log('1. 检查修复后的文件，确保颜色显示正确');
    console.log('2. 运行 pnpm run style:check 验证修复效果');
    console.log('3. 测试应用程序确保功能正常');
    console.log('4. 考虑为剩余的特殊颜色添加新的CSS变量');
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const fixer = new ColorFixer();
  fixer.run();
}

export default ColorFixer;