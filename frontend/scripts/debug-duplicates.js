#!/usr/bin/env node

import { glob } from 'glob';
import fs from 'fs';
import path from 'path';

class DuplicateChecker {
  constructor() {
    this.duplicateSelectors = new Map();
  }

  checkDuplicateSelectors(content, filePath) {
    // 更精确的选择器正则，排除伪类和伪元素
    const selectorRegex = /^\s*([.#][a-zA-Z][a-zA-Z0-9_-]*(?:\s*[,\s>+~]\s*[.#a-zA-Z][a-zA-Z0-9_-]*)*)\s*\{/gm;
    let match;
    const fileSelectors = new Set();
    
    while ((match = selectorRegex.exec(content)) !== null) {
      const selector = match[1].trim();
      // 排除包含伪类和伪元素的选择器
      if (!selector.includes(':') && !selector.includes('::')) {
        fileSelectors.add(selector);
      }
    }
    
    fileSelectors.forEach(selector => {
      if (!this.duplicateSelectors.has(selector)) {
        this.duplicateSelectors.set(selector, []);
      }
      
      if (!this.duplicateSelectors.get(selector).includes(filePath)) {
        this.duplicateSelectors.get(selector).push(filePath);
      }
    });
  }

  analyzeFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      this.checkDuplicateSelectors(content, filePath);
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error.message);
    }
  }

  async run() {
    console.log('🔍 查找重复选择器...');
    
    const cssFiles = await glob('src/**/*.css', {
      cwd: process.cwd(),
      absolute: true
    }) || [];
    
    // 去重文件路径
    const uniqueFiles = [...new Set(cssFiles)];
    
    console.log(`找到 ${uniqueFiles.length} 个CSS文件\n`);
    
    uniqueFiles.forEach(file => {
      console.log(`处理: ${path.relative(process.cwd(), file)}`);
      this.analyzeFile(file);
    });
    
    console.log('🔄 重复选择器详情:');
    console.log('='.repeat(50));
    
    let duplicateCount = 0;
    this.duplicateSelectors.forEach((files, selector) => {
      if (files.length > 1) {
        duplicateCount++;
        console.log(`\n${duplicateCount}. 选择器: "${selector}"`);
        console.log(`   出现在 ${files.length} 个文件中:`);
        const uniqueFiles = [...new Set(files)];
        uniqueFiles.forEach(file => {
          console.log(`   - ${path.relative(process.cwd(), file)}`);
        });
        if (files.length !== uniqueFiles.length) {
          console.log(`   注意: 原始数组有 ${files.length} 个条目，去重后有 ${uniqueFiles.length} 个`);
        }
      }
    });
    
    console.log(`\n📊 总计: ${duplicateCount} 个重复选择器`);
  }
}

const checker = new DuplicateChecker();
checker.run().catch(console.error);