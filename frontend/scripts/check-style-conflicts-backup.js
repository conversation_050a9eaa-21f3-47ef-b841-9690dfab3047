#!/usr/bin/env node

/**
 * 样式冲突检测脚本
 * 用于检测CSS文件中的潜在冲突和问题
 */

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

class StyleConflictChecker {
  constructor() {
    this.issues = [];
    this.hardcodedColors = new Set();
    this.duplicateSelectors = new Map();
    this.zIndexValues = new Map();
    this.globalSelectors = new Set();
  }

  // 检测硬编码颜色
  checkHardcodedColors(content, filePath) {
    // 跳过文档文件、配置文件和修复脚本
    if (filePath.includes('.md') || 
        filePath.includes('tailwind.config.js') || 
        filePath.includes('fix-hardcoded-colors.js') ||
        filePath.includes('react.svg') ||
        filePath.includes('base-styles.css')) {
      return;
    }
    
    const colorRegex = /#[0-9a-fA-F]{3,6}|rgba?\([0-9,\s.]+\)/g;
    const lines = content.split('\n');
    
    lines.forEach((line, index) => {
      // 跳过已经在CSS变量中使用的颜色或注释行
      if (line.includes('var(--') || line.trim().startsWith('/*') || line.includes('/* ')) {
        return;
      }
      
      const matches = line.match(colorRegex);
      if (matches) {
        matches.forEach(color => {
          this.hardcodedColors.add(color);
          this.issues.push({
            type: 'hardcoded-color',
            file: filePath,
            line: index + 1,
            issue: `发现硬编码颜色: ${color}`,
            suggestion: '建议使用CSS变量，如 var(--color-primary)',
            color: color
          });
        });
      }
    });
  }

  // 检测重复选择器
  checkDuplicateSelectors(content, filePath) {
    // 移除注释和字符串
    const cleanContent = content
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
      .replace(/\/\/.*$/gm, ''); // 移除单行注释
    
    // 更精确的选择器正则，排除伪类、伪元素、关键帧内容和媒体查询
    const lines = cleanContent.split('\n');
    const fileSelectors = new Set();
    let inKeyframes = false;
    let inMediaQuery = false;
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      // 检测关键帧开始和结束
      if (line.includes('@keyframes')) {
        inKeyframes = true;
        continue;
      }
      
      // 检测媒体查询开始
      if (line.includes('@media')) {
        inMediaQuery = true;
        continue;
      }
      
      // 检测块结束
      if (line === '}') {
        if (inKeyframes) inKeyframes = false;
        if (inMediaQuery) inMediaQuery = false;
        continue;
      }
      
      // 跳过关键帧和媒体查询内的内容
      if (inKeyframes || inMediaQuery) {
        continue;
      }
      
      // 匹配CSS选择器（以{结尾的行）
      if (line.includes('{') && !line.includes(':') && !line.includes('::')) {
        const selector = line.replace('{', '').trim();
        // 只记录类选择器和ID选择器
        if (selector.match(/^[.#][a-zA-Z][a-zA-Z0-9_-]*/) || 
            selector.match(/^\.[a-zA-Z][a-zA-Z0-9_-]*\s+[a-zA-Z]/) ||
            selector.match(/^\.[a-zA-Z][a-zA-Z0-9_-]*\s*[>+~]/) ||
            selector.match(/^[a-zA-Z][a-zA-Z0-9]*\s*\.[a-zA-Z]/)) {
          fileSelectors.add(selector);
        }
      }
    }
    
    // 为每个唯一选择器记录文件路径（每个文件只记录一次）
    fileSelectors.forEach(selector => {
      if (!this.duplicateSelectors.has(selector)) {
        this.duplicateSelectors.set(selector, []);
      }
      
      if (!this.duplicateSelectors.get(selector).includes(filePath)) {
        this.duplicateSelectors.get(selector).push(filePath);
      }
    });
  }

  // 检测z-index值
  checkZIndexValues(content, filePath) {
    // 跳过调试样式文件
    if (filePath.includes('debug-styles.css')) {
      return;
    }
    
    const zIndexRegex = /z-index:\s*(\d+)/g;
    let match;
    
    while ((match = zIndexRegex.exec(content)) !== null) {
      const value = parseInt(match[1]);
      
      if (!this.zIndexValues.has(value)) {
        this.zIndexValues.set(value, []);
      }
      
      this.zIndexValues.get(value).push(filePath);
      
      // 检查是否使用了推荐的z-index值
      const recommendedValues = [1000, 1020, 1030, 1040, 1050, 1060, 1070, 1080];
      if (!recommendedValues.includes(value)) {
        this.issues.push({
          type: 'z-index',
          file: filePath,
          issue: `使用了非标准z-index值: ${value}`,
          suggestion: '建议使用预定义的z-index变量，如 var(--z-modal)'
        });
      }
    }
  }

  // 检测全局选择器
  checkGlobalSelectors(content, filePath) {
    const globalSelectors = ['body', 'html', '*', ':root', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'a', 'button', 'input'];
    
    globalSelectors.forEach(selector => {
      // 转义特殊字符，特别是星号
      const escapedSelector = selector.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(`^\\s*${escapedSelector}\\s*\\{`, 'gm');
      if (regex.test(content)) {
        // 只有在非全局样式文件中才报告问题和计数
        if (!filePath.includes('index.css') && !filePath.includes('unified-styles.css') && !filePath.includes('base-styles.css') && !filePath.includes('STYLE_BEST_PRACTICES.md')) {
          this.globalSelectors.add(selector);
          this.issues.push({
            type: 'global-selector',
            file: filePath,
            issue: `在组件文件中使用了全局选择器: ${selector}`,
            suggestion: '建议使用类选择器或将全局样式移至 index.css'
          });
        }
      }
    });
  }

  // 检测未使用CSS变量的情况
  checkCSSVariableUsage(content, filePath) {
    const hasColors = /#[0-9a-fA-F]{3,6}|rgb\([^)]+\)|rgba\([^)]+\)/.test(content);
    const usesCSSVars = /var\(--[^)]+\)/.test(content);
    
    if (hasColors && !usesCSSVars) {
      this.issues.push({
        type: 'css-variables',
        file: filePath,
        issue: '文件包含颜色值但未使用CSS变量',
        suggestion: '建议使用统一样式系统中的CSS变量'
      });
    }
  }

  // 检测容器类冲突
  checkContainerConflicts(content, filePath) {
    if (content.includes('.container') && !filePath.includes('unified-styles.css')) {
      this.issues.push({
        type: 'container-conflict',
        file: filePath,
        issue: '重复定义了 .container 类',
        suggestion: '建议移除重复定义，使用统一样式系统中的容器类'
      });
    }
  }

  // 分析单个文件
  analyzeFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      this.checkHardcodedColors(content, filePath);
      this.checkDuplicateSelectors(content, filePath);
      this.checkZIndexValues(content, filePath);
      this.checkGlobalSelectors(content, filePath);
      this.checkCSSVariableUsage(content, filePath);
      this.checkContainerConflicts(content, filePath);
      
    } catch (error) {
      console.error(`读取文件失败: ${filePath}`, error.message);
    }
  }

  // 生成报告
  generateReport(isDetailMode = false) {
    console.log('\n🔍 样式冲突检测报告\n');
    console.log('='.repeat(50));
    
    if (isDetailMode && this.issues.length > 0) {
      // 按类型分组显示问题
      const issuesByType = {};
      this.issues.forEach(issue => {
        if (!issuesByType[issue.type]) {
          issuesByType[issue.type] = [];
        }
        issuesByType[issue.type].push(issue);
      });
      
      Object.keys(issuesByType).forEach(type => {
        const typeNames = {
          'hardcoded-color': '🎨 硬编码颜色',
          'z-index': '📏 Z-index问题',
          'global-selector': '🌐 全局选择器',
          'css-variables': '🔧 CSS变量使用',
          'container-conflict': '📦 容器类冲突'
        };
        
        console.log(`\n${typeNames[type] || type} (${issuesByType[type].length}个):`);
        console.log('-'.repeat(30));
        
        issuesByType[type].forEach((issue, index) => {
          console.log(`${index + 1}. 文件: ${issue.file}`);
          if (issue.line) {
            console.log(`   行号: ${issue.line}`);
          }
          console.log(`   问题: ${issue.message || issue.issue}`);
          console.log(`   建议: ${issue.suggestion}\n`);
        });
      });
    }
    
    // 显示重复选择器
    console.log('\n🔄 重复选择器分析:');
    console.log('-'.repeat(30));
    
    this.duplicateSelectors.forEach((files, selector) => {
      if (files.length > 1) {
        console.log(`选择器 "${selector}" 在以下文件中重复定义:`);
        files.forEach(file => console.log(`  - ${file}`));
        console.log('');
      }
    });
    
    // 显示z-index冲突
    console.log('\n📊 Z-index使用分析:');
    console.log('-'.repeat(30));
    
    this.zIndexValues.forEach((files, value) => {
      if (files.length > 1) {
        console.log(`z-index值 ${value} 在以下文件中使用:`);
        files.forEach(file => console.log(`  - ${file}`));
        console.log('');
      }
    });
    
    // 计算重复选择器数量
    let duplicateCount = 0;
    this.duplicateSelectors.forEach((files, selector) => {
      if (files.length > 1) {
        duplicateCount++;
      }
    });
    
    // 总结
    console.log('\n📈 总结:');
    console.log('-'.repeat(30));
    console.log(`总共发现 ${this.issues.length + duplicateCount} 个问题`);
    console.log(`重复选择器: ${duplicateCount} 个`);
    console.log(`硬编码颜色: ${this.hardcodedColors.size} 个`);
    console.log(`全局选择器使用: ${this.globalSelectors.size} 个`);
    
    console.log('\n💡 建议优先处理:');
    console.log('1. 替换硬编码颜色为CSS变量');
    console.log('2. 移除重复的全局样式定义');
    console.log('3. 统一z-index管理');
    console.log('4. 使用组件级样式避免全局污染');
  }

  // 运行检测
  async run(isDetailMode = false) {
    console.log('🚀 开始检测样式冲突...');
    
    // 查找所有CSS文件
    const cssFiles = await glob('src/**/*.css', {
      cwd: process.cwd(),
      absolute: true
    }) || [];
    
    console.log(`找到 ${cssFiles.length} 个CSS文件`);
    
    cssFiles.forEach(file => {
      console.log(`检测: ${path.relative(process.cwd(), file)}`);
      this.analyzeFile(file);
    });
    
    this.generateReport(isDetailMode);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const checker = new StyleConflictChecker();
  const isDetailMode = process.argv.includes('--detail');
  checker.run(isDetailMode).catch(console.error);
}

export default StyleConflictChecker;