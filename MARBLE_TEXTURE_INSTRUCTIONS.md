# 创建Blender大理石材质背景

## 步骤

1. **运行Blender脚本**
   - 打开Blender
   - 切换到"Scripting"工作区
   - 点击"Open"按钮，打开`create_marble_texture.py`脚本
   - 点击"Run Script"按钮
   - 脚本将渲染大理石背景并保存到您的主目录，文件名为`blender-marble-bg.jpg`

2. **移动渲染图像到项目目录**
   - 将生成的`blender-marble-bg.jpg`文件移动到项目的`frontend/public/images/`目录下
   ```
   mv ~/blender-marble-bg.jpg frontend/public/images/
   ```

3. **自定义大理石材质（可选）**
   - 如果您想调整大理石的外观，可以修改脚本中的以下参数：
     - 颜色：修改`color_ramp`节点中的颜色值
     - 纹理密度：修改`noise_texture`节点中的`Scale`值
     - 纹理细节：修改`noise_texture`节点中的`Detail`值
     - 凹凸程度：修改`bump_node`节点中的`Strength`值

## 高级定制

如果您想要更复杂的大理石材质，可以在Blender中手动创建材质：

1. 打开Blender，创建一个新场景
2. 添加一个平面
3. 切换到"Shading"工作区
4. 使用节点编辑器创建大理石材质
   - 使用多个噪波纹理和颜色渐变
   - 添加凹凸贴图和次表面散射以增加真实感
5. 设置相机和灯光
6. 渲染图像并保存

## 其他大理石类型

您可以通过调整颜色和纹理参数创建不同类型的大理石：

- **卡拉拉白大理石**：主要为白色，带有细微的灰色纹理
- **黑色大理石**：深灰色到黑色，带有白色纹理
- **绿色大理石**：带有绿色和白色纹理
- **棕色大理石**：棕色和米色的混合，带有深色纹理

只需修改脚本中的颜色值即可创建这些不同类型的大理石。
