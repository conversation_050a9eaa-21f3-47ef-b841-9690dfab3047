# Memorial项目依赖管理指南

## 📋 概述

本文档提供了Memorial项目依赖环境的管理指南，确保开发环境的整洁和一致性。

## 🏗️ 环境架构

### Conda环境 (memorial)
- **用途**: 主要开发环境，管理Python版本和核心依赖
- **配置文件**: `environment_clean.yml`
- **包含**: Python 3.11、核心科学计算库、开发工具

### Pip依赖
- **用途**: 项目特定依赖，通过conda的pip安装
- **配置文件**: `backend/requirements.txt`
- **包含**: FastAPI、数据库驱动、专用库

## 📁 依赖文件说明

### environment_clean.yml
```yaml
# 清理后的conda环境配置
# 包含核心依赖和通过pip安装的专用包
```

### backend/requirements.txt
```txt
# 后端项目特定依赖
# 版本锁定，确保生产环境一致性
```

### frontend/package.json
```json
# 前端依赖管理
# 使用pnpm作为包管理器
```

## 🔧 环境管理命令

### 检查依赖冲突
```bash
# 运行依赖分析脚本
python cleanup_dependencies.py
```

### 重建环境
```bash
# 完全重建conda环境
./rebuild_environment.sh
```

### 启动服务
```bash
# 启动所有服务器
./start_server.sh
```

## 📦 依赖分类

### Conda管理的包
- **系统级**: python, pip
- **科学计算**: numpy, pandas, matplotlib
- **开发工具**: jupyter, pytest, black, ruff
- **Web框架**: fastapi, uvicorn, sqlalchemy

### Pip管理的包
- **项目特定**: python-dotenv, gunicorn
- **3D渲染**: moderngl, trimesh, PyGLM
- **图像处理**: Pillow
- **认证**: python-jose, passlib
- **API工具**: pydantic-settings, replicate

## ⚠️ 常见问题

### 重复依赖
**问题**: 同一个包通过conda和pip都安装了
**解决**: 使用`cleanup_dependencies.py`检测并重建环境

### 版本冲突
**问题**: 不同包要求同一依赖的不同版本
**解决**: 在`environment_clean.yml`中明确指定版本

### 环境污染
**问题**: 环境中安装了过多不必要的包
**解决**: 定期使用`rebuild_environment.sh`重建环境

## 🎯 最佳实践

### 1. 环境隔离
- 使用conda环境隔离项目依赖
- 不在base环境中安装项目依赖

### 2. 版本锁定
- 在`requirements.txt`中锁定版本号
- 定期更新依赖并测试兼容性

### 3. 依赖分层
- Conda管理系统级和科学计算依赖
- Pip管理项目特定和专用依赖

### 4. 定期清理
- 每月运行依赖检查脚本
- 移除不再使用的依赖

### 5. 文档更新
- 添加新依赖时更新相应配置文件
- 记录依赖变更的原因

## 🚀 快速开始

### 新环境设置
```bash
# 1. 克隆项目
git clone <repository>
cd memorial

# 2. 创建环境
conda env create -f environment_clean.yml

# 3. 激活环境
conda activate memorial

# 4. 安装后端依赖
cd backend && pip install -r requirements.txt

# 5. 安装前端依赖
cd ../frontend && pnpm install

# 6. 启动服务
cd .. && ./start_server.sh
```

### 环境维护
```bash
# 检查依赖状态
python cleanup_dependencies.py

# 重建环境（如有问题）
./rebuild_environment.sh
```

## 📊 依赖统计

当前环境状态:
- **Conda包**: ~25个核心包
- **Pip包**: ~15个专用包
- **前端包**: 管理在frontend/package.json中
- **重复依赖**: 已清理

## 🔄 更新流程

### 添加新依赖
1. 确定依赖类型（conda vs pip）
2. 更新相应配置文件
3. 运行依赖检查脚本
4. 测试环境兼容性
5. 提交配置文件变更

### 移除依赖
1. 从配置文件中移除
2. 重建环境验证
3. 运行测试确保功能正常
4. 提交变更

## 📞 支持

如遇到依赖问题:
1. 运行`python cleanup_dependencies.py`诊断
2. 查看本文档的常见问题部分
3. 尝试重建环境`./rebuild_environment.sh`
4. 联系开发团队

---

**最后更新**: $(date)
**维护者**: Memorial开发团队