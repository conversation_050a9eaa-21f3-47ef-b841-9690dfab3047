#!/bin/bash

# 停止 Memorial 项目服务器脚本

echo "🛑 停止 Memorial 项目服务器..."

# 停止占用端口的进程
BACKEND_PORT=8008
FRONTEND_PORT=4001

echo "🔍 检查运行中的服务..."

# 停止后端服务 (端口 8008)
BACKEND_PID=$(lsof -ti :$BACKEND_PORT)
if [ ! -z "$BACKEND_PID" ]; then
    echo "📍 发现后端服务 (PID: $BACKEND_PID)，正在停止..."
    kill -TERM $BACKEND_PID 2>/dev/null
    sleep 2
    
    # 如果还在运行，强制停止
    if kill -0 $BACKEND_PID 2>/dev/null; then
        echo "⚡ 强制停止后端服务..."
        kill -KILL $BACKEND_PID 2>/dev/null
    fi
    echo "✅ 后端服务已停止"
else
    echo "ℹ️  未发现运行中的后端服务"
fi

# 停止前端服务 (端口 4001)
FRONTEND_PID=$(lsof -ti :$FRONTEND_PORT)
if [ ! -z "$FRONTEND_PID" ]; then
    echo "📍 发现前端服务 (PID: $FRONTEND_PID)，正在停止..."
    kill -TERM $FRONTEND_PID 2>/dev/null
    sleep 2
    
    # 如果还在运行，强制停止
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "⚡ 强制停止前端服务..."
        kill -KILL $FRONTEND_PID 2>/dev/null
    fi
    echo "✅ 前端服务已停止"
else
    echo "ℹ️  未发现运行中的前端服务"
fi

# 停止可能的Python进程
echo "🐍 检查 Python/FastAPI 进程..."
PYTHON_PIDS=$(pgrep -f "uvicorn.*memorial" || true)
if [ ! -z "$PYTHON_PIDS" ]; then
    echo "📍 发现 Memorial Python 进程，正在停止..."
    echo $PYTHON_PIDS | xargs kill -TERM 2>/dev/null || true
    echo "✅ Python 进程已停止"
fi

# 停止可能的Node.js进程
echo "🟢 检查 Node.js/Vite 进程..."
NODE_PIDS=$(pgrep -f "vite.*memorial" || pgrep -f "node.*memorial" || true)
if [ ! -z "$NODE_PIDS" ]; then
    echo "📍 发现 Memorial Node.js 进程，正在停止..."
    echo $NODE_PIDS | xargs kill -TERM 2>/dev/null || true
    echo "✅ Node.js 进程已停止"
fi

echo ""
echo "🎯 服务停止完成！"
echo "📍 端口 $BACKEND_PORT 和 $FRONTEND_PORT 已释放"
echo "🚀 现在可以重新运行 ./start_server.sh 启动服务"
echo ""