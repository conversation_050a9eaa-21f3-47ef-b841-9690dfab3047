"""
将现有的JPG图片转换为WebP格式
"""
import os
from PIL import Image
import glob

def convert_to_webp(source_dir, quality=80):
    """
    将指定目录下的所有JPG/JPEG/PNG图片转换为WebP格式
    
    参数:
    source_dir: 源图片目录
    quality: WebP图片质量，范围0-100，默认80
    """
    # 确保目录存在
    if not os.path.exists(source_dir):
        print(f"目录不存在: {source_dir}")
        return
    
    # 获取所有图片文件
    image_files = []
    for ext in ['*.jpg', '*.jpeg', '*.png']:
        image_files.extend(glob.glob(os.path.join(source_dir, ext)))
        image_files.extend(glob.glob(os.path.join(source_dir, ext.upper())))
    
    if not image_files:
        print(f"目录中没有找到JPG/JPEG/PNG图片: {source_dir}")
        return
    
    # 转换每个图片
    for img_path in image_files:
        try:
            # 打开图片
            with Image.open(img_path) as img:
                # 构建WebP文件路径
                webp_path = os.path.splitext(img_path)[0] + '.webp'
                
                # 转换并保存为WebP
                img.save(webp_path, 'WEBP', quality=quality)
                
                # 获取文件大小
                original_size = os.path.getsize(img_path) / 1024  # KB
                webp_size = os.path.getsize(webp_path) / 1024  # KB
                
                # 计算节省的空间
                saved = original_size - webp_size
                saved_percent = (saved / original_size) * 100 if original_size > 0 else 0
                
                print(f"已转换: {os.path.basename(img_path)} -> {os.path.basename(webp_path)}")
                print(f"  原始大小: {original_size:.2f} KB")
                print(f"  WebP大小: {webp_size:.2f} KB")
                print(f"  节省空间: {saved:.2f} KB ({saved_percent:.2f}%)")
                
        except Exception as e:
            print(f"转换 {img_path} 时出错: {e}")

if __name__ == "__main__":
    # 转换前端公共图片目录下的图片
    image_dir = "frontend/public/images"
    print(f"开始转换 {image_dir} 中的图片...")
    convert_to_webp(image_dir)
    print("转换完成!")
