# 依赖
node_modules/
.pnp
.pnp.js

# 构建输出
dist/
build/
out/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 编辑器和IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# 缓存
.cache/
.parcel-cache/
.eslintcache
.npm

# 测试覆盖率
coverage/

# Flutter SDK (嵌入的git仓库)
mobile/flutter/

# Python 缓存
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so

# 其他
.DS_Store
Thumbs.db
