# 🏛️ Memorial - AI驱动的数字纪念馆平台
# 海南长小养智能科技有限公司
[![项目状态](https://img.shields.io/badge/状态-开发中-green.svg)](https://github.com/memorial/memorial)
[![技术栈](https://img.shields.io/badge/技术栈-React%2BPython%2BFlutter-blue.svg)](#技术栈)
[![代码行数](https://img.shields.io/badge/代码行数-120K+-orange.svg)](#项目统计)
[![团队规模](https://img.shields.io/badge/团队规模-13--19人-purple.svg)](#团队规模)

## 🎯 项目概述

Memorial是一个创新的AI驱动数字纪念馆平台，融合了先进的3D渲染技术、人工智能和情感计算，为用户提供沉浸式的数字纪念体验。平台支持多宗教兼容，致力于传承记忆、连接情感、构建永恒的数字传承空间。

### ✨ 核心特性
- 🤖 **AI智能服务**: 图像修复、语音克隆、3D重建
- 🌐 **3D沉浸体验**: 基于Babylon.js的实时3D渲染
- 📱 **多平台支持**: Web端、iOS、Android统一体验
- 🎨 **个性化定制**: 多样化纪念空间主题
- 🔒 **隐私保护**: 企业级安全和隐私保护
- 🌍 **多语言支持**: 中英文国际化

## 🏗️ 技术栈

### 前端技术栈
- **框架**: React 18 + TypeScript 5.x
- **3D渲染**: Babylon.js 8.0 + WebGL/WebGPU
- **状态管理**: React Hooks + Context API
- **UI框架**: Tailwind CSS + 自定义组件
- **构建工具**: Vite + ESBuild
- **性能优化**: 自适应渲染 + 设备检测

### 后端技术栈
- **框架**: FastAPI + Python 3.11
- **数据库**: PostgreSQL + SQLAlchemy
- **AI/ML**: PyTorch + Transformers + OpenCV
- **3D渲染**: ModernGL + 自定义着色器
- **认证**: JWT + OAuth2
- **部署**: Docker + Kubernetes

### 移动端技术栈
- **框架**: Flutter 3.x + Dart
- **架构**: 功能模块化 (Clean Architecture)
- **UI设计**: Cupertino Design + Material Design
- **状态管理**: Provider + Bloc Pattern
- **平台**: iOS + Android + Web PWA

## 📁 项目结构

```
memorial/
├── 📱 frontend/              # Web前端 (React + TypeScript)
│   ├── src/
│   │   ├── components/       # 可复用组件
│   │   ├── pages/           # 页面组件
│   │   ├── scenes/          # 3D场景
│   │   ├── utils/           # 工具函数
│   │   └── types/           # TypeScript类型定义
├── 📲 mobile/               # 移动端 (Flutter)
│   ├── lib/
│   │   ├── features/        # 功能模块
│   │   │   ├── auth/        # 认证功能
│   │   │   ├── dashboard/   # 仪表板
│   │   │   ├── memorial/    # 纪念空间
│   │   │   └── settings/    # 设置
│   │   └── core/           # 核心功能
├── 🐍 backend/              # 后端服务 (Python)
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   └── ai_services/    # AI服务
├── 🎨 3d-assets/           # 3D资源和模型
├── 📚 docs/                # 项目文档
├── 📊 reports/             # 项目统计报告
├── 🛠️ scripts/             # 部署和工具脚本
└── 🎯 design/              # UI设计和原型
```

## 📊 项目统计

### 代码规模
- **总文件数**: 855个
- **总代码行数**: 120,307行
- **核心业务代码**: 19,022行
- **前端代码**: 11,819行 (TypeScript)
- **后端代码**: 4,662行 (Python)
- **移动端代码**: 2,541行 (Dart)

### 团队规模
- **推荐团队**: 13-19人
- **开发周期**: 12-18个月
- **项目评级**: ⭐⭐⭐⭐⭐ (企业级)

## 💻 开发环境

### 系统要求
- **操作系统**: macOS (Apple Silicon优化) / Linux / Windows
- **Node.js**: 18.x+
- **Python**: 3.11+
- **Flutter**: 3.x+
- **内存**: 16GB+ 推荐
- **存储**: 50GB+ 可用空间

### 推荐工具
- **IDE**: VS Code + 扩展包
- **3D建模**: Blender 3.x+
- **API测试**: Postman / Insomnia
- **版本控制**: Git + GitHub
- **容器**: Docker Desktop

## 🚀 快速开始

### 📋 环境准备

#### 1. 克隆项目
```bash
git clone https://github.com/memorial/memorial.git
cd memorial
```

#### 2. 自动环境配置 (推荐)
项目支持自动环境激活，安装direnv后会自动激活conda环境：

```bash
# 安装direnv (macOS)
brew install direnv

# 配置shell (添加到 ~/.zshrc 或 ~/.bashrc)
eval "$(direnv hook zsh)"

# 进入项目目录会自动激活环境
cd memorial
```

#### 3. 手动环境配置
```bash
# 创建并激活conda环境
conda env create -f environment.yml
conda activate memorial

# 或使用手动激活脚本
./activate_env.sh
```

### 📦 安装依赖

```bash
# 后端依赖
pip install -r backend/requirements.txt

# Web前端依赖
npm install -g pnpm
cd frontend && pnpm install

# 移动端依赖
cd mobile && flutter pub get
```

### 🔧 启动服务

#### 后端服务
```bash
# 统一服务模式 (推荐)
./start_unified_service.sh

# 或分别启动
./start_backend.sh
./start_render_service.sh
```

#### 前端服务
```bash
# Web前端开发服务器
cd frontend && pnpm dev

# 移动端应用
cd mobile && flutter run
```

### 🌐 访问应用

| 服务 | 地址 | 说明 |
|------|------|------|
| **Web前端** | http://localhost:5173 | React应用主界面 |
| **移动端** | 模拟器/真机 | Flutter应用 |
| **后端API** | http://localhost:5001 | FastAPI服务 |
| **API文档** | http://localhost:5001/docs | Swagger文档 |
| **管理后台** | http://localhost:5001/admin | 管理界面 |

## 🏗️ 核心架构

### 统一服务架构
项目采用统一服务架构，将渲染服务整合到主应用中，所有API都通过同一个端口访问，简化了部署和维护。

### 自适应渲染系统
实现了智能的自适应渲染系统，可以根据用户设备性能自动选择最合适的渲染方式：
- **高性能设备**: WebGPU + 高质量渲染
- **中等设备**: WebGL + 优化渲染
- **低性能设备**: 图像模式 + 预渲染

### 功能模块化
- **前端**: 组件化架构，支持热重载和按需加载
- **后端**: 微服务架构，API版本化管理
- **移动端**: Clean Architecture，功能模块独立

## 🤖 AI功能特性

### 图像处理
- **照片修复**: 老照片去噪、修复、上色
- **人像增强**: 面部修复、清晰度提升
- **风格转换**: 艺术风格化处理

### 语音技术
- **语音克隆**: 基于少量样本的声音复制
- **语音合成**: 文本转语音，多语言支持
- **情感识别**: 语音情感分析

### 3D重建
- **照片转3D**: 单张照片生成3D模型
- **场景重建**: 多视角3D场景构建
- **动画生成**: 自动生成纪念动画

## 📱 多平台支持

### Web端特性
- 🌐 响应式设计，支持所有现代浏览器
- ⚡ PWA支持，可离线使用
- 🎮 WebXR支持，VR/AR体验

### 移动端特性
- 📱 原生性能，流畅体验
- 🔔 推送通知，纪念日提醒
- 📷 相机集成，实时拍照上传
- 🗺️ 地理位置，纪念地标记

## 🔒 安全与隐私

### 数据安全
- 🔐 端到端加密
- 🛡️ HTTPS/TLS 1.3
- 🔑 JWT + OAuth2认证
- 🚫 零日志政策

### 隐私保护
- 👤 匿名化处理
- 🏠 本地数据存储选项
- 🗑️ 数据删除权
- 📋 透明的隐私政策

## 📈 性能优化

### 前端优化
- ⚡ Vite构建，秒级热重载
- 🗜️ 代码分割，按需加载
- 🖼️ 图像懒加载，WebP格式
- 💾 智能缓存策略

### 后端优化
- 🚀 异步处理，高并发支持
- 📊 数据库连接池
- 🔄 Redis缓存
- 📈 性能监控

## 📚 文档与资源

### 开发文档
- 📖 [环境配置指南](docs/环境配置指南.md)
- 🏗️ [架构设计文档](mobile/ARCHITECTURE.md)
- 🔧 [开发建议](docs/开发建议.md)
- 📊 [项目统计报告](reports/README.md)

### API文档
- 🔗 [API接口文档](http://localhost:5001/docs)
- 📝 [数据模型说明](backend/app/models/)
- 🧪 [测试用例](test/)

## 🤝 贡献指南

### 开发流程
1. Fork项目到个人仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 代码规范
- **TypeScript**: ESLint + Prettier
- **Python**: Black + Ruff + MyPy
- **Dart**: Flutter官方规范
- **提交信息**: 使用Conventional Commits

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

## 🙏 致谢

感谢所有为Memorial项目做出贡献的开发者和用户。

---

<div align="center">

**🏛️ Memorial - 传承记忆，连接永恒 🏛️**

[![GitHub stars](https://img.shields.io/github/stars/memorial/memorial.svg?style=social&label=Star)](https://github.com/memorial/memorial)
[![GitHub forks](https://img.shields.io/github/forks/memorial/memorial.svg?style=social&label=Fork)](https://github.com/memorial/memorial)

</div>
