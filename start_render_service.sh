#!/bin/bash
# 启动3D渲染服务

# 确保脚本在正确的目录中执行
cd "$(dirname "$0")"

# 检查是否已安装所需的依赖
echo "检查依赖..."
python3 -c "import moderngl" 2>/dev/null || { echo "安装 moderngl..."; pip install moderngl; }
python3 -c "import trimesh" 2>/dev/null || { echo "安装 trimesh..."; pip install trimesh; }
python3 -c "import pyrr" 2>/dev/null || { echo "安装 pyrr..."; pip install pyrr; }
python3 -c "import numpy" 2>/dev/null || { echo "安装 numpy..."; pip install numpy; }
python3 -c "import PIL" 2>/dev/null || { echo "安装 Pillow..."; pip install Pillow; }
python3 -c "import flask" 2>/dev/null || { echo "安装 Flask..."; pip install flask flask-cors; }
python3 -c "import glm" 2>/dev/null || { echo "安装 PyGLM..."; pip install PyGLM; }

# 创建日志目录
mkdir -p logs

# 启动渲染服务
echo "启动3D渲染服务..."
cd backend
python3 render_service.py > ../logs/render_service.log 2>&1 &

# 保存进程ID
echo $! > ../render_service.pid
echo "渲染服务已启动，进程ID: $(cat ../render_service.pid)"
echo "日志文件: $(pwd)/../logs/render_service.log"

# 等待服务启动
sleep 2

# 检查服务是否正常运行
if curl -s http://localhost:5001/api/render-service/status > /dev/null; then
    echo "渲染服务运行正常，可以通过 http://localhost:5001 访问"
    echo "GPU渲染状态: $(curl -s http://localhost:5001/api/render-service/status | grep gpuRendering)"
else
    echo "警告: 渲染服务可能未正常启动，请检查日志"
fi

echo "使用 ./stop_render_service.sh 停止服务"
