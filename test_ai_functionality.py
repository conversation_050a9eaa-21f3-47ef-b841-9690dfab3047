#!/usr/bin/env python3
"""
AI 功能测试脚本
测试 Replicate API 连接和基本功能
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# 添加后端路径到系统路径
backend_path = Path(__file__).parent / "backend"
sys.path.insert(0, str(backend_path))

try:
    from app.ai_services.replicate_service import ReplicateService
    from app.core.config import settings
    print("✅ 成功导入 AI 服务模块")
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    sys.exit(1)

async def test_replicate_connection():
    """测试 Replicate 连接"""
    try:
        # 创建服务实例
        service = ReplicateService(api_key=settings.REPLICATE_API_KEY)
        print(f"✅ Replicate 服务初始化成功")
        print(f"📁 上传目录: {service.upload_dir}")
        
        # 测试 API 连接（通过检查环境变量）
        import os
        if os.environ.get("REPLICATE_API_TOKEN"):
            print(f"✅ API Token 已设置: {os.environ.get('REPLICATE_API_TOKEN')[:10]}...")
            
            # 简单测试：检查 replicate 模块是否可用
            import replicate
            print("✅ Replicate 模块导入成功")
        else:
            print("❌ API Token 未设置")
            
        return True
        
    except Exception as e:
        print(f"❌ Replicate 连接测试失败: {e}")
        return False

async def test_ai_endpoints():
    """测试 AI API 端点"""
    import requests
    
    try:
        # 测试 API 端点是否可访问
        response = requests.get("http://localhost:5001/api/v1/openapi.json", timeout=5)
        if response.status_code == 200:
            print("✅ 后端 API 可访问")
            
            # 检查 AI 端点是否在 OpenAPI 规范中
            openapi_spec = response.json()
            paths = openapi_spec.get("paths", {})
            
            ai_endpoints = [path for path in paths.keys() if "/ai/" in path]
            if ai_endpoints:
                print(f"✅ 发现 AI 端点: {ai_endpoints}")
            else:
                print("⚠️  未发现 AI 端点")
                
        else:
            print(f"❌ 后端 API 不可访问，状态码: {response.status_code}")
            return False
            
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到后端 API: {e}")
        return False

def test_frontend_ai_components():
    """测试前端 AI 组件"""
    try:
        # 检查 AI 组件文件是否存在
        frontend_ai_path = Path(__file__).parent / "frontend" / "src" / "components" / "ai"
        
        if not frontend_ai_path.exists():
            print("❌ AI 组件目录不存在")
            return False
            
        ai_components = list(frontend_ai_path.glob("*.tsx"))
        if ai_components:
            print(f"✅ 发现 AI 组件: {[c.name for c in ai_components]}")
            
            # 检查组件是否使用了正确的 API 端点
            for component in ai_components:
                content = component.read_text()
                if "/api/ai/" in content or "/api/v1/ai/" in content:
                    print(f"✅ {component.name} 使用了 AI API 端点")
                else:
                    print(f"⚠️  {component.name} 可能没有使用 AI API 端点")
                    
        else:
            print("❌ 未发现 AI 组件文件")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ 检查前端 AI 组件失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始 AI 功能测试...")
    print("=" * 50)
    
    tests = [
        ("Replicate 连接测试", test_replicate_connection()),
        ("AI API 端点测试", test_ai_endpoints()),
        ("前端 AI 组件测试", test_frontend_ai_components()),
    ]
    
    results = []
    for test_name, test_coro in tests:
        print(f"\n🔍 {test_name}...")
        if asyncio.iscoroutine(test_coro):
            result = await test_coro
        else:
            result = test_coro
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有 AI 功能测试通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置")
        return False

if __name__ == "__main__":
    # 设置日志级别
    logging.basicConfig(level=logging.INFO)
    
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 测试过程中发生意外错误: {e}")
        sys.exit(1)