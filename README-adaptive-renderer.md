# 自适应渲染系统

本项目实现了一个自适应渲染系统，可以根据用户设备性能自动选择最合适的渲染方式，以降低对用户浏览器的要求，同时充分利用后端资源。

## 功能特点

- **自动检测设备性能**：使用WebGL检测和设备信息分析，自动确定设备性能级别
- **多级渲染方式**：
  - 高性能设备：完整3D渲染
  - 中等性能设备：简化3D模型
  - 低性能设备：预渲染图像或服务器渲染
- **服务器端渲染**：对于网络条件良好的低端设备，提供服务器渲染选项
- **预渲染图像**：对于最低端设备，提供预渲染图像作为备选方案
- **手动质量控制**：允许用户手动选择渲染质量

## 系统架构

系统由以下几个主要部分组成：

1. **前端自适应渲染组件**：`AdaptiveRenderer.tsx`
2. **后端渲染服务**：`render_service.py`
3. **预渲染图像系统**：存储在`/frontend/public/images/buddhist-temple`目录中
4. **多质量级别模型**：存储在`/frontend/public/models`目录中

## 使用方法

### 启动服务

1. 启动统一服务（包含渲染服务）：

```bash
./start_unified_service.sh
```

2. 启动前端开发服务器：

```bash
cd frontend
npm run dev
```

> 注意：不再需要单独启动渲染服务，所有功能都已整合到统一服务中。

### 访问测试页面

访问以下URL测试自适应渲染系统：

- 主应用页面：http://localhost:5173/memorial/buddhist-temple
- 测试页面：http://localhost:5173/test-adaptive-renderer.html

### 集成到现有组件

要将自适应渲染系统集成到现有组件中，只需替换原有的3D渲染代码：

```jsx
// 原有代码
<Canvas>
  <EnvironmentModel modelPath={modelPath} />
  <OrbitControls />
  <Environment preset="sunset" />
</Canvas>

// 替换为
<AdaptiveRenderer modelPath={modelPath} />
```

## 性能优化建议

1. **模型优化**：
   - 使用低多边形模型作为低质量版本
   - 使用纹理烘焙将复杂材质效果烘焙到简单纹理中
   - 减少纹理大小和复杂度

2. **渲染优化**：
   - 限制最大DPR（设备像素比）
   - 减少阴影和后处理效果
   - 使用LOD（细节级别）技术

3. **加载优化**：
   - 实现渐进式加载
   - 使用资源压缩
   - 实现资源预加载

## 故障排除

如果遇到渲染问题，请尝试以下步骤：

1. **检查设备兼容性**：访问测试页面查看设备信息
2. **手动切换渲染模式**：使用质量选择器手动切换渲染模式
3. **检查网络连接**：服务器渲染需要稳定的网络连接
4. **检查服务器状态**：确保渲染服务正在运行

## 技术细节

### 设备性能检测

系统使用以下指标来评估设备性能：

- WebGL支持和GPU信息
- 设备内存
- CPU核心数
- 设备像素比
- 是否为移动设备

### 渲染模式

系统支持以下渲染模式：

- **高质量**：完整3D模型，高分辨率纹理，完整光照效果
- **中等质量**：简化3D模型，中等分辨率纹理，基本光照效果
- **低质量**：极简3D模型，低分辨率纹理，最小光照效果
- **服务器渲染**：在服务器上渲染3D场景，将结果作为图像流发送到客户端
- **图像模式**：使用预渲染的多角度图像，实现简单的交互式查看

## 未来改进

- 实现更高级的服务器端渲染，支持实时交互
- 添加更多的预渲染视角和动画
- 实现更精确的设备性能检测
- 添加更多的渲染质量级别
- 优化网络传输，减少延迟
