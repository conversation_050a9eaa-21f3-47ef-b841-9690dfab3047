#!/bin/bash

# Memorial 项目启动测试脚本
# 测试启动脚本的各个组件是否正常工作

echo "🧪 测试 Memorial 项目启动环境..."

# 切换到项目根目录
cd "$(dirname "$0")"

# 测试环境变量
echo "🔧 检查环境配置..."
if [ ! -f ".env" ]; then
    echo "⚠️  未找到 .env 文件，创建示例配置..."
    cp .env.example .env
    echo "✅ 已创建 .env 文件，请根据需要修改配置"
fi

# 测试Python环境
echo "🐍 检查 Python 环境..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version 2>&1)
    echo "  ✅ Python: $PYTHON_VERSION"
else
    echo "  ❌ 未找到 Python3"
    exit 1
fi

# 测试Node.js环境
echo "🟢 检查 Node.js 环境..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version 2>&1)
    echo "  ✅ Node.js: $NODE_VERSION"
else
    echo "  ❌ 未找到 Node.js"
    exit 1
fi

if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version 2>&1)
    echo "  ✅ npm: $NPM_VERSION"
else
    echo "  ❌ 未找到 npm"
    exit 1
fi

# 测试PostgreSQL连接
echo "🗄️  检查 PostgreSQL..."
if command -v psql &> /dev/null; then
    echo "  ✅ PostgreSQL 客户端已安装"
    # 尝试连接数据库
    if psql "postgresql://memorial_user:memorial_pass@localhost/memorial_db" -c "SELECT 1;" &> /dev/null; then
        echo "  ✅ 数据库连接成功"
    else
        echo "  ⚠️  数据库连接失败，请检查 PostgreSQL 是否运行"
    fi
else
    echo "  ⚠️  未找到 PostgreSQL 客户端"
fi

# 检查后端依赖
echo "📦 检查后端依赖..."
cd backend
if [ -f "requirements.txt" ]; then
    echo "  ✅ 找到 requirements.txt"
    # 检查关键依赖
    if python3 -c "import fastapi" &> /dev/null; then
        echo "  ✅ FastAPI 已安装"
    else
        echo "  ⚠️  FastAPI 未安装，需要运行: pip install -r requirements.txt"
    fi
else
    echo "  ❌ 未找到 requirements.txt"
fi

# 检查前端依赖
echo "📦 检查前端依赖..."
cd ../frontend
if [ -f "package.json" ]; then
    echo "  ✅ 找到 package.json"
    if [ -d "node_modules" ]; then
        echo "  ✅ node_modules 存在"
    else
        echo "  ⚠️  node_modules 不存在，需要运行: npm install"
    fi
else
    echo "  ❌ 未找到 package.json"
fi

# 检查Vite配置
if [ -f "vite.config.ts" ]; then
    echo "  ✅ 找到 vite.config.ts"
    # 检查代理配置
    if grep -q "target.*8008" vite.config.ts; then
        echo "  ✅ Vite 代理配置正确 (指向端口 8008)"
    else
        echo "  ⚠️  Vite 代理配置可能需要检查"
    fi
else
    echo "  ❌ 未找到 vite.config.ts"
fi

# 测试端口可用性
echo "🔌 检查端口可用性..."
if lsof -i :8008 &> /dev/null; then
    echo "  ⚠️  端口 8008 已被占用"
else
    echo "  ✅ 端口 8008 可用"
fi

if lsof -i :4001 &> /dev/null; then
    echo "  ⚠️  端口 4001 已被占用"
else
    echo "  ✅ 端口 4001 可用"
fi

# 检查启动脚本权限
cd ..
if [ -x "start_server.sh" ]; then
    echo "  ✅ start_server.sh 有执行权限"
else
    echo "  ⚠️  start_server.sh 无执行权限，正在修复..."
    chmod +x start_server.sh
    echo "  ✅ 已添加执行权限"
fi

echo ""
echo "🎯 测试总结:"
echo "📍 如果所有检查都通过，可以运行: ./start_server.sh"
echo "📍 如果有⚠️警告，请先解决相关问题"
echo "📍 详细配置请查看 .env 文件"
echo ""

# 显示快速启动指南
echo "🚀 快速启动指南:"
echo "1. 确保 PostgreSQL 运行: brew services start postgresql"
echo "2. 安装后端依赖: cd backend && pip install -r requirements.txt"
echo "3. 安装前端依赖: cd frontend && npm install"
echo "4. 启动服务: ./start_server.sh"
echo ""