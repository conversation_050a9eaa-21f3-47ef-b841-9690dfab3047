#!/bin/bash
# 自动激活conda环境（静默模式）
if command -v conda &> /dev/null; then
    # 使用conda run方式避免conda init错误
    if conda info --envs | grep -q "memorial" &> /dev/null; then
        export CONDA_DEFAULT_ENV="memorial"
        export CONDA_PREFIX="$(conda info --base)/envs/memorial"
        export PATH="$CONDA_PREFIX/bin:$PATH"
    fi
fi

# 设置项目相关的环境变量
export PROJECT_ROOT=$(pwd)
export PYTHONPATH="${PROJECT_ROOT}/backend:${PYTHONPATH}"
export MEMORIAL_ENV="development"