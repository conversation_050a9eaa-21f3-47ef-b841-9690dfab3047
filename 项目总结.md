# 祭祀网站项目总结

*最后更新日期：2025年5月8日*
## 项目概述

这是一个基于3D技术的多宗教兼容祭祀网站，旨在为用户提供沉浸式的祭祀体验。项目支持多种宗教和文化背景的祭祀环境，包括东方传统环境（中国传统祠堂、佛教寺庙、道教仙境等）、西方宗教环境（基督教教堂、犹太教纪念空间等）以及现代/中性环境（现代简约空间、自然生态环境、星空宇宙环境等）。

## 技术栈

- **前端**：React + Babylon.js 8.0 + TypeScript *(从Three.js迁移)*
- **后端**：Python (Flask) + PostgreSQL
- **3D开发**：Blender + glTF
- **渲染**：Metal API (针对Apple Silicon M4芯片优化) + WebGPU
## 项目结构

- **/docs** - 项目文档
- **/frontend** - React前端
- **/backend** - Python Flask后端
- **/database** - 数据库脚本
- **/3d-assets** - Blender模型和资源
- **/design** - UI设计和原型
- **/scripts** - 部署和工具脚本
- **/metal-shaders** - 自定义Metal着色器
- **/babylon-shaders** - Babylon.js自定义着色器
## 前端部分

前端使用React + TypeScript开发，集成了Babylon.js 8.0进行3D渲染（已从Three.js迁移）。主要功能包括：

- 首页展示不同宗教/文化的祭祀环境选项
- 3D祭祀场景渲染和交互
- 用户可以在3D环境中进行祭祀活动（点燃香烛、献上祭品、播放音乐、祈福祷告等）
- 利用Babylon.js 8.0的IBL阴影和区域光源提供更真实的光照效果
- 使用Babylon.js的物理引擎实现自然的物品交互
## 后端部分

后端使用Flask框架开发，主要功能包括：

- 用户认证系统（注册、登录）
- 环境数据管理（不同宗教/文化的祭祀环境）
- 先人信息管理（用户可以添加和管理先人信息）
- 宗教/文化设置管理（仪式元素、特殊节日、祷告文等）
- 3D资产管理和优化
## 数据库设计

数据库使用PostgreSQL，主要包含以下表：

- **用户表(users)**：存储用户信息
- **先人表(ancestors)**：存储用户添加的先人信息
- **环境表(environments)**：存储不同的祭祀环境
- **宗教/文化配置表(religious_cultural_settings)**：存储不同宗教/文化的配置
- **3D资产表(assets)**：存储3D模型和资源的元数据
## 3D资产

项目使用Blender创建3D模型，并通过glTF格式导出供前端使用。针对Apple Silicon M4芯片，项目使用Metal API进行渲染优化，同时利用Babylon.js 8.0的WebGPU支持提供跨平台高性能渲染。

## 项目状态

项目目前处于开发阶段，已经完成了基础的项目结构搭建和部分功能实现。前端已经从Three.js迁移到Babylon.js 8.0，并实现了基本的路由和3D场景加载。后端已经实现了基本的API和数据模型。

## 待完成工作

- [x] 从Three.js迁移到Babylon.js 8.0
- [ ] 完善3D模型和资产
- [ ] 实现更多的交互功能
- [ ] 优化渲染性能
- [ ] 完善用户认证和权限管理
- [ ] 实现多语言支持
- [ ] 添加更多宗教/文化选项
- [ ] 实现天气和季节变化系统（使用Babylon.js的环境系统）
- [ ] 添加音效和音乐系统（使用Babylon.js的音频引擎）
- [ ] 实现VR/AR支持（利用Babylon.js的WebXR功能）

这个项目是一个综合性的Web应用，结合了3D技术和多宗教文化元素，旨在为用户提供一个沉浸式的祭祀体验平台。通过迁移到Babylon.js 8.0，我们期望能够提供更高质量的视觉效果和更丰富的交互体验。

## 环境设置

### 后端环境
```bash
conda create -n memorial python=3.11
conda activate memorial
pip install -r backend/requirements.txt
```

### 前端环境
```bash
npm install -g pnpm
cd frontend
pnpm install

# Babylon.js相关包
pnpm add babylonjs babylonjs-loaders babylonjs-materials babylonjs-gui
pnpm add react-babylonjs
```

## 相关文档

- [从Three.js迁移到Babylon.js 8.0指南](./docs/从Three.js迁移到Babylon.js%208.0指南.md)
- [Three.js vs Babylon.js 8.0 对比分析](./docs/Three.js%20vs%20Babylon.js%208.0%20对比分析.md)