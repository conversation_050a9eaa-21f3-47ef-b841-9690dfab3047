import {
    Engine,
    Scene,
    Vector3,
    ArcRotateCamera,
    HemisphericLight,
    MeshBuilder,
    StandardMaterial,
    Color3,
    SceneLoader,
    CubeTexture
} from "@babylonjs/core";

// 导入 Inspector 和加载器，确保它们被打包
import "@babylonjs/core/Debug/debugLayer";
import "@babylonjs/inspector";
import "@babylonjs/loaders/glTF";
import { GridMaterial } from "@babylonjs/materials/grid";

class App {
    private _canvas: HTMLCanvasElement;
    private _engine: Engine;
    private _scene!: Scene;

    constructor() {
        this._canvas = document.getElementById("renderCanvas") as HTMLCanvasElement;
        this._engine = new Engine(this._canvas, true);
        
        this.init();
    }

    private async init(): Promise<void> {
        this._scene = await this.createScene();

        window.addEventListener("keydown", (ev) => {
            if (ev.key === 'i' && ev.ctrlKey) {
                if (this._scene.debugLayer.isVisible()) {
                    this._scene.debugLayer.hide();
                } else {
                    this._scene.debugLayer.show();
                }
            }
        });

        this._engine.runRenderLoop(() => {
            this._scene.render();
        });

        window.addEventListener("resize", () => {
            this._engine.resize();
        });
    }

    private async createScene(): Promise<Scene> {
        const scene = new Scene(this._engine);

        // 注意：环境贴图仍然从外部加载，如果这也失败，也需要本地化
        const environmentTexture = CubeTexture.CreateFromPrefilteredData("https://assets.babylonjs.com/environments/environmentSpecular.env", scene);
        scene.environmentTexture = environmentTexture;

        const camera = new ArcRotateCamera("camera", -Math.PI / 2, Math.PI / 2.5, 20, new Vector3(0, 5, 0), scene);
        camera.attachControl(this._canvas, true);

        const light = new HemisphericLight("light", new Vector3(0, 1, 0), scene);
        light.intensity = 0.7;

        const ground = MeshBuilder.CreateGround("ground", {width: 10, height: 10});
        const gridMaterial = new GridMaterial("gridMat", scene);
        gridMaterial.mainColor = new Color3(0.7, 0.7, 0.7);
        gridMaterial.lineColor = new Color3(0.2, 0.2, 1.0);
        ground.material = gridMaterial;

        // **重要改动**: 从本地路径加载模型
        // 您需要将 flightHelmet.glb 文件下载并放置在 babylon-native-env/public/assets/ 目录下
        try {
            const result = await SceneLoader.ImportMeshAsync(
                "", 
                "/assets/", // Vite 会将 public 目录映射到根URL
                "flightHelmet.glb", 
                scene
            );
            const helmet = result.meshes[0];
            helmet.position.y = 5;
            helmet.scaling.scaleInPlace(5);
        } catch (error) {
            console.error("本地模型加载失败:", error);
        }

        return scene;
    }
}

new App();