{"name": "babylon-native-env", "private": true, "version": "1.0.0", "description": "A clean, native Babylon.js development environment using Vite and TypeScript.", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"typescript": "^5.5.3", "vite": "^5.3.4"}, "dependencies": {"@babylonjs/core": "^8.11.0", "@babylonjs/inspector": "^8.11.0", "@babylonjs/loaders": "^8.11.0", "@babylonjs/materials": "^8.11.0"}}