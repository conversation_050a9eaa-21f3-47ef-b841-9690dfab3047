#!/usr/bin/env python3
"""
依赖环境清理脚本
用于清理conda环境和pip环境中的重复依赖，确保环境整洁
"""

import subprocess
import sys
from typing import Dict, List, Set

def run_command(cmd: List[str]) -> str:
    """执行命令并返回输出"""
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {' '.join(cmd)}")
        print(f"错误: {e.stderr}")
        return ""

def get_conda_packages() -> Dict[str, str]:
    """获取conda安装的包"""
    output = run_command(["conda", "list", "-n", "memorial", "--export"])
    conda_packages = {}
    
    for line in output.split('\n'):
        if '=' in line and not line.startswith('#'):
            parts = line.split('=')
            if len(parts) >= 2:
                name = parts[0].lower()  # 转换为小写以便比较
                version = parts[1]
                # 只记录非pypi来源的包
                if 'pypi_0' not in line:
                    conda_packages[name] = version
    
    return conda_packages

def get_pip_packages() -> Dict[str, str]:
    """获取pip安装的包"""
    output = run_command(["pip", "freeze"])
    pip_packages = {}
    
    for line in output.split('\n'):
        if '==' in line:
            name, version = line.split('==', 1)
            pip_packages[name.lower()] = version
    
    return pip_packages

def analyze_duplicates():
    """分析重复的依赖"""
    print("🔍 分析依赖环境...")
    
    conda_packages = get_conda_packages()
    pip_packages = get_pip_packages()
    
    print(f"\n📦 Conda包数量: {len(conda_packages)}")
    print(f"📦 Pip包数量: {len(pip_packages)}")
    
    # 调试输出
    print("\n🔍 调试信息:")
    print(f"Conda包前10个: {list(conda_packages.keys())[:10]}")
    print(f"Pip包前10个: {list(pip_packages.keys())[:10]}")
    
    # 查找重复的包
    duplicates = []
    for pip_name in pip_packages:
        for conda_name in conda_packages:
            if pip_name.lower() == conda_name.lower() or pip_name.replace('-', '_') == conda_name.replace('-', '_'):
                duplicates.append({
                    'name': pip_name,
                    'pip_version': pip_packages[pip_name],
                    'conda_name': conda_name,
                    'conda_version': conda_packages[conda_name]
                })
    
    if duplicates:
        print(f"\n⚠️  发现 {len(duplicates)} 个重复依赖:")
        for dup in duplicates:
            print(f"  - {dup['name']}: pip={dup['pip_version']}, conda={dup['conda_version']}")
    else:
        print("\n✅ 未发现重复依赖")
    
    return duplicates

def check_requirements_conflicts():
    """检查requirements.txt中的冲突"""
    print("\n🔍 检查requirements.txt冲突...")
    
    try:
        with open('backend/requirements.txt', 'r') as f:
            content = f.read()
        
        lines = content.strip().split('\n')
        seen_packages = set()
        duplicates = []
        
        for line in lines:
            if line.strip() and not line.startswith('#'):
                package_name = line.split('==')[0].split('>=')[0].split('<=')[0].strip()
                if package_name in seen_packages:
                    duplicates.append(package_name)
                seen_packages.add(package_name)
        
        if duplicates:
            print(f"⚠️  requirements.txt中发现重复包: {duplicates}")
            return True
        else:
            print("✅ requirements.txt无重复包")
            return False
            
    except FileNotFoundError:
        print("❌ 未找到backend/requirements.txt文件")
        return False

def clean_requirements_txt():
    """清理requirements.txt中的重复项"""
    print("\n🧹 清理requirements.txt...")
    
    try:
        with open('backend/requirements.txt', 'r') as f:
            lines = f.readlines()
        
        seen_packages = set()
        clean_lines = []
        
        for line in lines:
            if line.strip() and not line.startswith('#'):
                package_name = line.split('==')[0].split('>=')[0].split('<=')[0].strip()
                if package_name not in seen_packages:
                    clean_lines.append(line)
                    seen_packages.add(package_name)
                else:
                    print(f"  移除重复项: {line.strip()}")
            elif line.strip():
                clean_lines.append(line)
        
        # 写回文件
        with open('backend/requirements.txt', 'w') as f:
            f.writelines(clean_lines)
        
        print("✅ requirements.txt清理完成")
        
    except Exception as e:
        print(f"❌ 清理requirements.txt失败: {e}")

def generate_clean_environment_yml():
    """生成清理后的environment.yml"""
    print("\n📝 生成清理后的environment.yml...")
    
    clean_env_content = '''name: memorial
channels:
  - conda-forge
  - defaults
dependencies:
  - python=3.11
  - pip
  # 核心开发工具
  - numpy
  - pandas
  - matplotlib
  - jupyter
  - ipykernel
  
  # Web框架和API
  - fastapi
  - uvicorn
  - pydantic
  - httpx
  - websockets
  - sqlalchemy
  - alembic
  - psycopg2
  
  # 开发工具
  - pytest
  - pytest-asyncio
  - black
  - flake8
  - mypy
  - ruff
  
  # 后端依赖
  - python-dotenv
  - gunicorn
  - trimesh
  - pyrr
  
  # 通过pip安装的专用包
  - pip:
    - moderngl
    - Pillow
    - pydantic-settings
    - replicate
    - PyGLM
    - gevent
    - "python-jose[cryptography]"
    - "passlib[bcrypt]"
    - python-multipart
    - "uvicorn[standard]==0.27.1"
    - loguru
    - rich
    - typer
'''
    
    with open('environment_clean.yml', 'w') as f:
        f.write(clean_env_content)
    
    print("✅ 已生成 environment_clean.yml")

def main():
    """主函数"""
    print("🧹 Memorial项目依赖环境清理工具")
    print("=" * 50)
    
    # 分析重复依赖
    duplicates = analyze_duplicates()
    
    # 检查requirements.txt冲突
    has_conflicts = check_requirements_conflicts()
    
    # 清理requirements.txt
    if has_conflicts:
        clean_requirements_txt()
    
    # 生成清理后的环境文件
    generate_clean_environment_yml()
    
    print("\n📋 清理建议:")
    print("1. 使用 environment_clean.yml 重新创建conda环境")
    print("2. 删除当前环境: conda env remove -n memorial")
    print("3. 创建新环境: conda env create -f environment_clean.yml")
    print("4. 激活环境: conda activate memorial")
    print("5. 安装后端依赖: cd backend && pip install -r requirements.txt")
    
    if duplicates:
        print("\n⚠️  注意: 发现重复依赖，建议重新创建环境以确保整洁")
    else:
        print("\n✅ 环境相对整洁，可选择性重新创建")

if __name__ == "__main__":
    main()