#!/bin/bash
# 停止统一服务

# 确保脚本在正确的目录中执行
cd "$(dirname "$0")"

# 停止 Gunicorn 进程
echo "停止服务..."
pkill -f gunicorn || true

# 检查端口是否仍被占用
PORT_USAGE=$(lsof -i:5001 | grep LISTEN)
if [ -n "$PORT_USAGE" ]; then
    echo "警告: 端口5001仍被占用:"
    echo "$PORT_USAGE"
    echo "尝试强制终止这些进程..."
    
    # 获取占用端口的进程ID
    PIDS=$(lsof -i:5001 -t)
    if [ -n "$PIDS" ]; then
        for pid in $PIDS; do
            echo "终止进程 $pid..."
            kill -9 $pid
        done
    fi
    
    # 再次检查端口
    sleep 1
    if [ -n "$(lsof -i:5001 | grep LISTEN)" ]; then
        echo "警告: 无法释放端口5001，可能需要手动终止进程"
    else
        echo "端口5001已释放"
    fi
else
    echo "端口5001已释放"
fi

echo "服务已停止"
