#!/bin/bash

# Memorial 项目简化启动脚本

set -e

# 切换到项目根目录
cd "$(dirname "$0")"

echo "🚀 启动 Memorial 项目服务器..."

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)/backend"
export DATABASE_URL="postgresql://memorial:memorial@localhost/memorial"
export SECRET_KEY="memorial-dev-secret-key-2025"
export JWT_SECRET_KEY="memorial-jwt-secret-key-2025"
export REPLICATE_API_TOKEN="${REPLICATE_API_TOKEN:-demo-token}"

# 端口配置
BACKEND_PORT=8008
FRONTEND_PORT=4001

echo "🔧 配置信息:"
echo "  - 后端端口: ${BACKEND_PORT}"
echo "  - 前端端口: ${FRONTEND_PORT}"

# 检查环境
echo "🔍 检查环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到 Python3"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ 未找到 npm"
    exit 1
fi

# 启动后端
echo "🔧 启动后端服务 (端口: ${BACKEND_PORT})..."
cd backend

# 检查并安装依赖
echo "📥 检查后端依赖..."
if ! python3 -c "import fastapi, aiohttp, replicate" 2>/dev/null; then
    echo "📥 安装后端依赖..."
    pip install -r requirements.txt
    echo "✅ 后端依赖安装完成"
else
    echo "✅ 后端依赖已就绪"
fi

# 启动后端服务
echo "✅ 启动 FastAPI 服务器..."
uvicorn app.main:app --host 0.0.0.0 --port ${BACKEND_PORT} --reload &
BACKEND_PID=$!

# 等待后端启动
echo "⏳ 等待后端服务启动..."
sleep 5

# 检查后端是否启动成功
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    echo "❌ 后端服务启动失败"
    exit 1
fi

# 启动前端
echo "🎨 启动前端服务 (端口: ${FRONTEND_PORT})..."
cd ../frontend

# 检查并安装依赖
if [ ! -d "node_modules" ]; then
    echo "📥 安装前端依赖..."
    npm install
fi

# 设置前端环境变量
export VITE_APP_API_BASE_URL="http://localhost:${BACKEND_PORT}/api/v1"
export VITE_APP_BACKEND_URL="http://localhost:${BACKEND_PORT}"

# 启动前端服务
echo "✅ 启动 Vite 服务器..."
npm run dev -- --port ${FRONTEND_PORT} &
FRONTEND_PID=$!

# 等待前端启动
echo "⏳ 等待前端服务启动..."
sleep 5

# 检查前端是否启动成功
if ! kill -0 $FRONTEND_PID 2>/dev/null; then
    echo "❌ 前端服务启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo ""
echo "🎉 Memorial 项目启动成功！"
echo ""
echo "📍 访问地址:"
echo "  🌐 前端应用: http://localhost:${FRONTEND_PORT}"
echo "  🔧 后端 API: http://localhost:${BACKEND_PORT}"
echo "  📚 API 文档: http://localhost:${BACKEND_PORT}/docs"
echo ""
echo "💡 按 Ctrl+C 停止服务"

# 优雅关闭处理
cleanup() {
    echo ""
    echo "🛑 正在停止服务..."
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null && echo "  ✅ 后端服务已停止"
    fi
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null && echo "  ✅ 前端服务已停止"
    fi
    echo "🎯 服务已停止"
    exit 0
}

trap cleanup INT TERM

# 保持脚本运行
while true; do
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        echo "❌ 后端服务停止"
        kill $FRONTEND_PID 2>/dev/null
        exit 1
    fi
    
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "❌ 前端服务停止"
        kill $BACKEND_PID 2>/dev/null
        exit 1
    fi
    
    sleep 5
done