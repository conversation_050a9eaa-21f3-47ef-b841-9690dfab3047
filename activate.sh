#!/bin/bash
# 激活memorial conda环境的脚本
echo "🔄 正在激活memorial环境..."

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "❌ 未找到conda命令，请确保已安装Anaconda或Miniconda"
    exit 1
fi

# 检查memorial环境是否存在
if ! conda env list | grep -q "memorial"; then
    echo "❌ memorial环境不存在，请先创建环境"
    echo "💡 可以使用命令: conda create -n memorial python=3.9"
    exit 1
fi

# 激活环境
eval "$(conda shell.bash hook)"
conda activate memorial

# 设置项目相关的环境变量
PROJECT_ROOT=$(pwd)
export PROJECT_ROOT
export PYTHONPATH="${PROJECT_ROOT}/backend:${PYTHONPATH}"
export MEMORIAL_ENV="development"

echo "✅ memorial环境已激活！"
echo "📁 项目根目录: ${PROJECT_ROOT}"
echo "🚀 Memorial项目环境已准备就绪"

# 显示当前环境信息
python --version
echo "当前工作目录: $(pwd)"
