[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.ruff]
# 与 Black 兼容的行长度
line-length = 88
target-version = "py311"

[tool.ruff.lint]
# 启用的规则集
select = [
    "E",  # pycodestyle 错误
    "F",  # pyflakes
    "I",  # isort
    "C",  # flake8-comprehensions
    "B",  # flake8-bugbear
    "UP", # pyupgrade
]
ignore = [
    "E203",  # 与 Black 冲突的空格规则
    "E501",  # 行太长（由 Black 处理）
    "B008",  # 在函数调用中使用默认参数（Flask 路由装饰器需要）
]

[tool.ruff.lint.isort]
known-first-party = ["app"]
known-third-party = ["flask", "flask_cors", "flask_sqlalchemy", "flask_migrate", "flask_jwt_extended", "sqlalchemy", "moderngl", "trimesh", "pyrr", "numpy", "PIL", "glm", "gevent"]

[tool.bandit]
exclude_dirs = [".venv", "tests", "migrations"]
skips = ["B101", "B104"]  # 跳过某些检查
