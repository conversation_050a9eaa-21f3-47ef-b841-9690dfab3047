[mypy]
python_version = 3.11
warn_return_any = False
warn_unused_configs = True
disallow_untyped_defs = False
disallow_incomplete_defs = False
check_untyped_defs = False
disallow_untyped_decorators = False
no_implicit_optional = True
strict_optional = False
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True

# 忽略缺失的导入
ignore_missing_imports = True

# 禁用 SQLAlchemy 相关的属性定义错误
disable_error_code = attr-defined

# 忽略SQLAlchemy的Base类型问题
disallow_subclassing_any = False

# SQLAlchemy 2.0 plugin
plugins = sqlalchemy.ext.mypy.plugin

# 移除不识别的选项
# warn_unused_calls = True
# warn_untyped_calls = True

[mypy.app.api.namespaces.auth]
exclude = True

# 暂时忽略 crud.py 文件中的类型错误
[mypy.app.crud]
disallow_untyped_defs = False
check_untyped_defs = False
warn_return_any = False
