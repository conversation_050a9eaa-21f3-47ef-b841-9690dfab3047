# 🔄 Memorial 跨平台开发同步策略

## 📊 当前状态分析

### Web端完成度: 90%
- ✅ 3D场景渲染 (Babylon.js)
- ✅ 认证系统 + 状态管理  
- ✅ 虚拟商店 + 订阅系统
- ✅ AI服务集成
- ✅ 纪念空间管理

### Flutter移动端完成度: 70%
- ✅ UI架构 (Cupertino + Riverpod)
- ✅ 认证界面结构
- ⚠️ 缺少API集成
- ⚠️ 缺少3D渲染引擎
- ⚠️ 缺少商业化功能

## 🎯 同步开发策略

### 策略1: API-First 同步开发 (推荐 ⭐)

#### 核心原则
```
Backend API → Web实现 → Flutter实现 → 功能对齐
```

#### 实施步骤

##### Phase 1: API契约统一 (1周)
1. **统一数据模型**
   ```typescript
   // 共享接口定义
   interface MemorialSpace {
     id: string;
     title: string;
     description: string;
     privacy_level: 'public' | 'private' | 'family';
     scene_config: SceneConfiguration;
     created_at: string;
   }
   ```

2. **API文档生成**
   ```bash
   # 使用OpenAPI生成跨平台模型
   cd backend/
   python generate_api_docs.py
   # 生成 TypeScript 和 Dart 模型
   ```

##### Phase 2: 移动端API集成 (2周)
1. **Flutter HTTP服务层**
   ```dart
   // lib/core/services/api_service.dart
   class ApiService {
     static const String baseUrl = 'https://api.memorial.com';
     
     Future<List<MemorialSpace>> getMemorialSpaces() async {
       final response = await http.get('$baseUrl/memorial-spaces');
       return (response.data as List)
         .map((json) => MemorialSpace.fromJson(json))
         .toList();
     }
   }
   ```

2. **Riverpod状态管理集成**
   ```dart
   // lib/providers/memorial_provider.dart
   final memorialProvider = StateNotifierProvider<MemorialNotifier, MemorialState>(
     (ref) => MemorialNotifier(ref.read(apiServiceProvider)),
   );
   ```

##### Phase 3: 功能同步开发 (持续)
1. **同步开发流程**
   ```mermaid
   graph LR
   A[需求定义] --> B[API设计]
   B --> C[Web实现]
   C --> D[Flutter实现]
   D --> E[测试对齐]
   ```

2. **功能对齐检查表**
   - [ ] 认证流程 (登录/注册/密码重置)
   - [ ] 纪念空间 CRUD
   - [ ] 3D场景渲染 (Web: Babylon.js, Flutter: 待选择)
   - [ ] 虚拟商店系统
   - [ ] 订阅管理
   - [ ] AI服务调用

### 策略2: 移动端3D渲染解决方案

#### 选项A: Flutter + Unity集成 (推荐)
```yaml
# pubspec.yaml
dependencies:
  unity_widget: ^0.1.0  # Unity 3D渲染
```

#### 选项B: Flutter + WebView (Babylon.js)
```dart
// 在Flutter中嵌入Web 3D场景
WebView(
  initialUrl: 'https://memorial.com/3d-scene',
  javascriptMode: JavaScriptMode.unrestricted,
)
```

#### 选项C: Flutter + OpenGL原生渲染
```dart
// 使用 flutter_gl 进行原生3D渲染
dependencies:
  flutter_gl: ^0.0.20
```

### 策略3: 代码共享架构

#### 共享组件库
```
shared/
├── api_models/           # 跨平台数据模型
│   ├── typescript/       # Web TypeScript定义
│   └── dart/            # Flutter Dart定义
├── business_logic/       # 业务逻辑规则
└── constants/           # 共享常量
```

#### 自动代码生成
```bash
# 从OpenAPI生成跨平台模型
npm run generate-models:web
flutter packages pub run build_runner build
```

## 🔧 开发工具链同步

### 1. 统一开发环境
```json
// .vscode/settings.json
{
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "dart.flutterHotReloadOnSave": "always",
  "files.associations": {
    "*.dart": "dart",
    "*.ts": "typescript"
  }
}
```

### 2. 统一代码质量标准
```yaml
# .github/workflows/cross_platform_ci.yml
name: Cross Platform CI
on: [push, pull_request]
jobs:
  web_tests:
    runs-on: ubuntu-latest
    steps:
      - run: npm run test
      - run: npm run typecheck
  
  flutter_tests:
    runs-on: ubuntu-latest
    steps:
      - run: flutter test
      - run: flutter analyze
```

### 3. 统一部署流程
```bash
#!/bin/bash
# scripts/deploy_all_platforms.sh
echo "Building Web..."
cd frontend && npm run build

echo "Building Flutter Web..."
cd ../flutter && flutter build web

echo "Building Flutter APK..."
flutter build apk

echo "Deploying all platforms..."
```

## 📱 移动端优先功能规划

### 高优先级移动端特有功能
1. **推送通知系统**
   ```dart
   // 祭拜提醒、家族活动通知
   class NotificationService {
     static Future<void> scheduleMemorialReminder() async {
       await flutterLocalNotificationsPlugin.schedule(...);
     }
   }
   ```

2. **离线缓存支持**
   ```dart
   // 离线浏览纪念空间
   class OfflineCache {
     static Future<void> cacheMemorialData() async {
       final box = await Hive.openBox('memorial_cache');
       // 缓存重要数据
     }
   }
   ```

3. **相机集成**
   ```dart
   // 直接拍照上传祭品照片
   final ImagePicker _picker = ImagePicker();
   final XFile? photo = await _picker.pickImage(
     source: ImageSource.camera,
   );
   ```

## 🚦 开发优先级路线图

### Week 1-2: API统一 (完成度: 100%)
- [x] 统一Backend API接口
- [x] 生成跨平台数据模型
- [x] 集成测试验证

### Week 3-4: Flutter核心功能同步 (目标: 85%)
- [ ] API服务层集成
- [ ] 纪念空间管理同步
- [ ] 认证流程完善
- [ ] 基础UI组件库

### Week 5-6: 3D渲染解决方案 (目标: 70%)
- [ ] 选择移动端3D引擎
- [ ] 实现基础3D场景
- [ ] 性能优化和适配

### Week 7-8: 商业化功能同步 (目标: 80%)
- [ ] 虚拟商店移动端实现
- [ ] 订阅管理系统
- [ ] 支付集成 (iOS/Android)

## 🎯 成功指标

### 功能对齐指标
- **API兼容性**: 100%接口对齐
- **功能完整性**: ≥90%核心功能同步
- **性能表现**: 移动端与Web端体验一致

### 开发效率指标
- **代码复用率**: ≥60%业务逻辑复用
- **开发同步性**: 新功能24小时内跨平台交付
- **Bug修复速度**: 跨平台Bug48小时内修复

## 🛠️ 工具和资源

### 跨平台开发工具
- **API文档**: Swagger/OpenAPI
- **状态管理**: React Context + Riverpod
- **代码生成**: json_serializable + quicktype
- **测试工具**: Jest + Flutter Test
- **CI/CD**: GitHub Actions双平台

### 监控和分析
- **性能监控**: Firebase Performance
- **错误追踪**: Sentry跨平台
- **用户分析**: Firebase Analytics
- **A/B测试**: Firebase Remote Config

---

## 📈 预期收益

### 开发效率提升
- 减少50%重复开发工作
- 提高30%新功能交付速度
- 降低40%跨平台Bug率

### 用户体验一致性
- 100%功能对齐
- 统一的设计语言
- 一致的性能表现

### 长期维护优势
- 统一的代码架构
- 简化的测试流程
- 降低维护成本

---

*本策略文档将根据开发进展持续更新*