<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI功能集成测试</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #4ade80;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #065f46; border-left: 4px solid #10b981; }
        .error { background: #7f1d1d; border-left: 4px solid #ef4444; }
        .warning { background: #78350f; border-left: 4px solid #f59e0b; }
        .loading { background: #1e3a8a; border-left: 4px solid #3b82f6; }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #2563eb; }
        button:disabled { background: #6b7280; cursor: not-allowed; }
        .endpoint-test {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 10px 0;
            padding: 10px;
            background: #374151;
            border-radius: 4px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-pending { background: #6b7280; }
        .status-testing { background: #3b82f6; animation: pulse 1s infinite; }
        .status-success { background: #10b981; }
        .status-error { background: #ef4444; }
    </style>
</head>
<body>
    <h1>🤖 AI功能集成测试</h1>
    <p>此页面用于测试前端到后端的AI功能整合情况</p>

    <div class="test-section">
        <h2>📡 后端连接测试</h2>
        <div id="backend-status" class="test-result loading">
            <div class="status-indicator status-testing"></div>
            正在检查后端连接...
        </div>
        <button onclick="testBackendConnection()">重新测试后端</button>
    </div>

    <div class="test-section">
        <h2>🔗 AI端点测试</h2>
        <div id="ai-endpoints">
            <!-- AI端点测试结果将在这里显示 -->
        </div>
        <button onclick="testAIEndpoints()">测试AI端点</button>
    </div>

    <div class="test-section">
        <h2>🎨 AI组件响应测试</h2>
        <p>测试AI组件是否能正确处理移动端交互和响应式布局：</p>
        
        <div class="test-result">
            <h3>📱 移动端兼容性测试</h3>
            <div id="mobile-test-results"></div>
            <button onclick="testMobileCompatibility()">测试移动端兼容性</button>
        </div>

        <div class="test-result">
            <h3>🖼️ 文件上传测试</h3>
            <div id="upload-test-results"></div>
            <input type="file" id="test-file-input" accept="image/*" style="margin: 10px 0;">
            <button onclick="testFileUpload()" id="upload-test-btn">测试文件上传</button>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 测试汇总</h2>
        <div id="test-summary">
            <div class="test-result loading">等待测试完成...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001/api/v1';
        
        // 存储测试结果
        const testResults = {
            backend: null,
            endpoints: {},
            mobile: null,
            upload: null
        };

        // AI端点列表
        const AI_ENDPOINTS = [
            { path: '/ai/photo-enhance', name: '照片增强' },
            { path: '/ai/photo-remove-bg', name: '背景移除' },
            { path: '/ai/photo-restore', name: '照片修复' },
            { path: '/ai/photo-colorize', name: '照片上色' },
            { path: '/ai/photo-to-3d', name: '照片转3D' },
            { path: '/ai/clone-voice', name: '语音克隆' }
        ];

        async function testBackendConnection() {
            const statusEl = document.getElementById('backend-status');
            statusEl.className = 'test-result loading';
            statusEl.innerHTML = '<div class="status-indicator status-testing"></div>正在测试后端连接...';

            try {
                const response = await fetch(`${API_BASE}/openapi.json`);
                if (response.ok) {
                    testResults.backend = true;
                    statusEl.className = 'test-result success';
                    statusEl.innerHTML = '<div class="status-indicator status-success"></div>✅ 后端连接成功';
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                testResults.backend = false;
                statusEl.className = 'test-result error';
                statusEl.innerHTML = `<div class="status-indicator status-error"></div>❌ 后端连接失败: ${error.message}`;
            }
            
            updateSummary();
        }

        async function testAIEndpoints() {
            const container = document.getElementById('ai-endpoints');
            container.innerHTML = '';

            for (const endpoint of AI_ENDPOINTS) {
                const endpointEl = document.createElement('div');
                endpointEl.className = 'endpoint-test';
                endpointEl.innerHTML = `
                    <span><div class="status-indicator status-testing"></div>${endpoint.name}</span>
                    <span>测试中...</span>
                `;
                container.appendChild(endpointEl);

                try {
                    // 测试OPTIONS请求（CORS预检）
                    const response = await fetch(`${API_BASE}${endpoint.path}`, {
                        method: 'OPTIONS'
                    });
                    
                    testResults.endpoints[endpoint.path] = true;
                    endpointEl.innerHTML = `
                        <span><div class="status-indicator status-success"></div>${endpoint.name}</span>
                        <span>✅ 可访问</span>
                    `;
                } catch (error) {
                    testResults.endpoints[endpoint.path] = false;
                    endpointEl.innerHTML = `
                        <span><div class="status-indicator status-error"></div>${endpoint.name}</span>
                        <span>❌ 不可访问</span>
                    `;
                }
            }
            
            updateSummary();
        }

        function testMobileCompatibility() {
            const resultsEl = document.getElementById('mobile-test-results');
            const tests = [];

            // 测试视口宽度
            const viewportWidth = window.innerWidth;
            tests.push({
                name: '视口宽度检测',
                passed: viewportWidth > 0,
                details: `当前宽度: ${viewportWidth}px`
            });

            // 测试触摸支持
            const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
            tests.push({
                name: '触摸支持',
                passed: true, // 始终通过，因为桌面也应该工作
                details: touchSupport ? '支持触摸' : '仅鼠标'
            });

            // 测试CSS媒体查询
            const mediaQuery = window.matchMedia('(max-width: 768px)');
            tests.push({
                name: '移动端媒体查询',
                passed: true,
                details: mediaQuery.matches ? '移动端布局' : '桌面布局'
            });

            // 测试响应式元标签
            const viewport = document.querySelector('meta[name="viewport"]');
            tests.push({
                name: '响应式元标签',
                passed: !!viewport,
                details: viewport ? '已设置' : '未设置'
            });

            const allPassed = tests.every(test => test.passed);
            testResults.mobile = allPassed;

            resultsEl.innerHTML = tests.map(test => `
                <div class="test-result ${test.passed ? 'success' : 'error'}">
                    ${test.passed ? '✅' : '❌'} ${test.name}: ${test.details}
                </div>
            `).join('');
            
            updateSummary();
        }

        async function testFileUpload() {
            const fileInput = document.getElementById('test-file-input');
            const resultsEl = document.getElementById('upload-test-results');
            const btn = document.getElementById('upload-test-btn');

            if (!fileInput.files.length) {
                resultsEl.innerHTML = '<div class="test-result warning">⚠️ 请先选择一个图片文件</div>';
                return;
            }

            const file = fileInput.files[0];
            btn.disabled = true;
            resultsEl.innerHTML = '<div class="test-result loading">🔄 正在测试文件上传...</div>';

            try {
                // 模拟文件上传验证（不实际上传）
                const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                const isValidType = validTypes.includes(file.type);
                const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB

                const tests = [
                    { name: '文件类型', passed: isValidType, details: file.type },
                    { name: '文件大小', passed: isValidSize, details: `${(file.size / 1024 / 1024).toFixed(2)} MB` },
                    { name: '文件名', passed: file.name.length > 0, details: file.name }
                ];

                const allPassed = tests.every(test => test.passed);
                testResults.upload = allPassed;

                resultsEl.innerHTML = tests.map(test => `
                    <div class="test-result ${test.passed ? 'success' : 'error'}">
                        ${test.passed ? '✅' : '❌'} ${test.name}: ${test.details}
                    </div>
                `).join('');

            } catch (error) {
                testResults.upload = false;
                resultsEl.innerHTML = `<div class="test-result error">❌ 文件上传测试失败: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
                updateSummary();
            }
        }

        function updateSummary() {
            const summaryEl = document.getElementById('test-summary');
            const results = testResults;
            
            const tests = [
                { name: '后端连接', result: results.backend },
                { name: 'AI端点', result: Object.keys(results.endpoints).length > 0 ? Object.values(results.endpoints).every(r => r) : null },
                { name: '移动端兼容', result: results.mobile },
                { name: '文件上传', result: results.upload }
            ];

            const completed = tests.filter(t => t.result !== null);
            const passed = tests.filter(t => t.result === true);

            summaryEl.innerHTML = `
                <div class="test-result ${passed.length === completed.length && completed.length > 0 ? 'success' : 'warning'}">
                    📊 测试进度: ${completed.length}/${tests.length} 完成
                    <br>✅ 通过: ${passed.length} | ❌ 失败: ${completed.filter(t => t.result === false).length}
                </div>
                ${tests.map(test => `
                    <div class="test-result ${test.result === null ? 'loading' : test.result ? 'success' : 'error'}">
                        ${test.result === null ? '⏳' : test.result ? '✅' : '❌'} ${test.name}
                    </div>
                `).join('')}
            `;
        }

        // 页面加载时自动开始测试
        window.addEventListener('load', () => {
            testBackendConnection();
            testMobileCompatibility();
        });

        // 添加脉搏动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>